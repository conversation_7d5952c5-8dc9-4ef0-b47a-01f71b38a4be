from fastapi import APIRouter, HTTPException, Form, Depends, UploadFile, File, Response
from typing import Optional, Dict, List, Any
import os
import json
import logging
import re
import shutil
import datetime
from .auth import get_current_user
from fastapi.responses import FileResponse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

router = APIRouter()

# 协议类型映射关系
PROTOCOL_TYPE_MAP = {
    "TCP": 2,
    "UDP": 0,
    "ICMP": 1
}

@router.post("/generate_template")
async def generate_clean_template(
    results_file: str = Form(...),
    output_folder: str = Form("/data/output"),
    template_name: str = Form(None),
    current_user: str = Depends(get_current_user)
):
    """
    根据训练或预测结果生成清洗模板
    
    参数:
    - results_file: 结果文件路径，如 "/data/output/xxx_results.txt"
    - output_folder: 输出文件夹路径
    - template_name: 模板名称，如果不提供，将使用结果文件名作为基础
    """
    try:
        # 验证结果文件是否存在
        if not os.path.exists(results_file):
            raise HTTPException(status_code=404, detail=f"结果文件 {results_file} 不存在")
        
        # 读取基础模板
        base_template_path = "cleantemplate.json"
        if not os.path.exists(base_template_path):
            raise HTTPException(status_code=404, detail="基础模板文件不存在")
        
        with open(base_template_path, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        # 读取结果文件
        thresholds = {}
        with open(results_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过空行和注释行（以#开头的行）
                if not line or line.startswith('#'):
                    continue

                parts = line.split()
                if len(parts) >= 3:  # 确保至少有协议、数据类型和阈值三个部分
                    try:
                        protocol = parts[0]
                        datatype = parts[1]
                        threshold = float(parts[2])
                        thresholds[(protocol, datatype)] = int(threshold)  # 转换为整数
                        logging.info(f"解析阈值: {protocol} {datatype} -> {int(threshold)}")
                    except ValueError as e:
                        logging.warning(f"跳过无法解析的行: {line} - 错误: {e}")
                        continue
        
        # 生成输出文件名
        if template_name:
            # 使用提供的模板名称
            base_name = template_name
        else:
            # 从结果文件名提取基础名称 - 第一个下划线前的内容
            file_name = os.path.basename(results_file)
            # 去掉 _results.txt 后缀
            file_name_without_suffix = re.sub(r'_results\.txt$', '', file_name)
            # 取第一个下划线前的内容
            if '_' in file_name_without_suffix:
                base_name = file_name_without_suffix.split('_')[0]
            else:
                base_name = file_name_without_suffix

        # 客户编号和名称映射关系
        customer_mapping = {
            "C20230330-000318": "嵩山实验室",
            "C20240528-000695": "河南行上云网络科技有限公司",
            "C20240530-000713": "长沙高乾国际贸易有限公司",
            "C20240702-000792": "湖南蚨哌网络科技有限公司（常德）-正式",
            "C20240802-000822": "深信服科技股份有限公司",
            "C20240901-000854": "西安云桥联动网络科技有限公司-正式",
            "C20240901-000855": "珠海横琴澳琴云创科技有限公司-正式1",
            "C20241028-000947": "抚顺新航智能科技有限公司",
            "C20241101-000953": "徐州昊驰网络科技有限公司正式",
            "C20241112-000960": "河南中原消费金融股份有限公司",
            "C20241206-001000": "贵州汐云数字科技有限公司",
            "C20241227-001035": "江苏致宁网络科技有限公司"
        }

        # 填充customerNum字段
        if "customerNum" in template_data:
            template_data["customerNum"] = base_name
            logging.info(f"设置customerNum字段: {base_name}")

        # 填充customerName字段（根据customerNum映射）
        if "customerName" in template_data:
            customer_name = customer_mapping.get(base_name, "未知客户")
            template_data["customerName"] = customer_name
            logging.info(f"设置customerName字段: {customer_name}")

        # 填充templateName字段（cleantemplate + 日期）
        current_date = datetime.datetime.now().strftime("%Y%m%d")
        template_name = f"cleantemplate{current_date}"
        if "templateName" in template_data:
            template_data["templateName"] = template_name
            logging.info(f"设置templateName字段: {template_name}")

        # 更新模板中的阈值
        for item in template_data.get("fingerprintProtection", []):
            protocol_type = item.get("fingerprintProtectionType")
            strategy_name = item.get("strategyName")

            # 根据协议类型找到对应的协议名称
            protocol = next((p for p, t in PROTOCOL_TYPE_MAP.items() if t == protocol_type), None)

            if protocol and strategy_name:
                key = (protocol, strategy_name)
                if key in thresholds:
                    item["protectionThreshold"] = thresholds[key]
                    logging.info(f"更新阈值: {protocol} {strategy_name} -> {thresholds[key]}")
        
        # 添加当前时间（精确到天）
        current_time = datetime.datetime.now().strftime("%Y%m%d")
        output_filename = f"{base_name}_{current_time}_cleantemplate.json"
        
        output_path = os.path.join(output_folder, output_filename)
        
        # 保存更新后的模板
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        return {
            "message": "清洗模板生成成功",
            "template_path": output_path,
            "updated_thresholds": len(thresholds)
        }
    
    except Exception as e:
        logging.error(f"生成清洗模板失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成清洗模板失败: {str(e)}")

@router.get("/list_result_files")
async def list_result_files(
    folder_path: str = "/data/output",
    current_user: str = Depends(get_current_user)
):
    """
    列出指定文件夹中的结果文件
    """
    try:
        if not os.path.exists(folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹 {folder_path} 不存在")
        
        result_files = [f for f in os.listdir(folder_path) if f.endswith('_results.txt')]
        result_files.sort()
        
        return {"files": result_files}
    
    except Exception as e:
        logging.error(f"列出结果文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"列出结果文件失败: {str(e)}")

@router.get("/list_templates")
async def list_templates(
    folder_path: str = "/data/output",
    current_user: str = Depends(get_current_user)
):
    """
    列出指定文件夹中的清洗模板文件，包含详细信息
    """
    try:
        if not os.path.exists(folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹 {folder_path} 不存在")

        template_files = [f for f in os.listdir(folder_path) if f.endswith('_cleantemplate.json')]
        template_files.sort()

        # 获取每个模板文件的详细信息
        templates_info = []
        for filename in template_files:
            file_path = os.path.join(folder_path, filename)
            try:
                # 获取文件统计信息
                stat = os.stat(file_path)

                # 尝试读取模板内容获取模板名称
                template_name = filename.replace('_cleantemplate.json', '')
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                        # 如果模板中有templateName字段，使用它作为显示名称
                        if 'templateName' in template_data:
                            template_name = template_data['templateName']
                except:
                    # 如果读取失败，使用文件名
                    pass

                templates_info.append({
                    "template_name": template_name,
                    "filename": filename,
                    "template_path": file_path,
                    "file_size": stat.st_size,
                    "created_time": stat.st_mtime,
                    "modified_time": stat.st_mtime
                })
            except Exception as e:
                logging.warning(f"获取文件 {filename} 信息失败: {e}")
                # 如果获取文件信息失败，仍然添加基本信息
                templates_info.append({
                    "template_name": filename.replace('_cleantemplate.json', ''),
                    "filename": filename,
                    "template_path": os.path.join(folder_path, filename),
                    "file_size": 0,
                    "created_time": 0,
                    "modified_time": 0
                })

        return {"templates": templates_info}

    except Exception as e:
        logging.error(f"列出模板文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"列出模板文件失败: {str(e)}")

@router.get("/download_template")
async def download_template(
    template_path: str,
    current_user: str = Depends(get_current_user)
):
    """
    下载指定的清洗模板文件
    """
    try:
        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件 {template_path} 不存在")
        
        return FileResponse(
            path=template_path,
            filename=os.path.basename(template_path),
            media_type="application/json"
        )
    
    except Exception as e:
        logging.error(f"下载模板文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载模板文件失败: {str(e)}")

@router.delete("/delete_template")
async def delete_template(
    template_path: str,
    current_user: str = Depends(get_current_user)
):
    """
    删除指定的清洗模板文件
    """
    try:
        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件 {template_path} 不存在")
        
        os.remove(template_path)
        
        return {"message": f"模板文件 {os.path.basename(template_path)} 已成功删除"}
    
    except Exception as e:
        logging.error(f"删除模板文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除模板文件失败: {str(e)}")

@router.get("/get_template_content")
async def get_template_content(
    template_path: str,
    current_user: str = Depends(get_current_user)
):
    """
    获取指定清洗模板文件的内容
    """
    try:
        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件 {template_path} 不存在")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        return {"content": template_data}
    
    except Exception as e:
        logging.error(f"获取模板内容失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取模板内容失败: {str(e)}")

@router.post("/update_template")
async def update_template(
    template_path: str = Form(...),
    template_content: str = Form(...),
    current_user: str = Depends(get_current_user)
):
    """
    更新指定清洗模板文件的内容
    """
    try:
        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件 {template_path} 不存在")
        
        # 验证JSON格式
        try:
            template_data = json.loads(template_content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="提供的模板内容不是有效的JSON格式")
        
        # 备份原文件
        backup_path = f"{template_path}.bak"
        shutil.copy2(template_path, backup_path)
        
        # 保存更新后的模板
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        return {"message": "模板内容已成功更新"}
    
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"更新模板内容失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新模板内容失败: {str(e)}")

@router.post("/send_template")
async def send_template(
    template_path: str = Form(...),
    target_host: str = Form(...),
    target_username: str = Form(...),
    target_password: str = Form(...),
    target_port: int = Form(22),
    target_path: str = Form(...),
    current_user: str = Depends(get_current_user)
):
    """
    通过SFTP发送清洗模板到目标服务器
    """
    try:
        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件 {template_path} 不存在")
        
        # 导入paramiko库用于SFTP传输
        try:
            import paramiko
        except ImportError:
            raise HTTPException(status_code=500, detail="服务器未安装paramiko库，无法进行SFTP传输")
        
        # 创建SSH客户端
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # 连接到目标服务器
            ssh_client.connect(
                hostname=target_host,
                port=target_port,
                username=target_username,
                password=target_password,
                timeout=10
            )
            
            # 创建SFTP客户端
            sftp_client = ssh_client.open_sftp()
            
            # 获取模板文件名
            template_filename = os.path.basename(template_path)
            target_file_path = os.path.join(target_path, template_filename)
            
            # 上传文件
            sftp_client.put(template_path, target_file_path)
            
            # 关闭连接
            sftp_client.close()
            ssh_client.close()
            
            return {
                "message": "清洗模板发送成功",
                "target_file": target_file_path
            }
        
        except Exception as e:
            if ssh_client:
                ssh_client.close()
            raise HTTPException(status_code=500, detail=f"SFTP传输失败: {str(e)}")
    
    except Exception as e:
        logging.error(f"发送模板失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"发送模板失败: {str(e)}")
