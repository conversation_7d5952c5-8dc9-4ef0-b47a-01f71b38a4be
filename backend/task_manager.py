"""
异步任务管理模块
提供模型训练和预测的异步任务管理功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
import uuid
import logging
import threading
import time
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# 任务状态枚举
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 任务类型枚举
class TaskType(str, Enum):
    TRAINING = "training"
    PREDICTION = "prediction"
    DATA_CLEANING = "data_cleaning"

# 全局任务存储
class TaskStorage:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.Lock()

        # JSON文件路径
        self.tasks_file = "task_storage.json"
        self.results_file = "task_results.json"

        # 加载现有数据
        self._load_data()

    def _validate_and_fix_json_file(self, filepath):
        """验证并修复JSON文件"""
        if not os.path.exists(filepath):
            return True

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                json.load(f)
            return True
        except json.JSONDecodeError as e:
            logger.error(f"JSON文件损坏: {filepath}, 错误: {e}")

            # 创建备份
            backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            try:
                os.rename(filepath, backup_path)
                logger.info(f"已备份损坏文件: {backup_path}")
            except Exception:
                pass

            # 创建空的有效JSON文件
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
                logger.info(f"已创建新的空JSON文件: {filepath}")
                return True
            except Exception as e:
                logger.error(f"创建新JSON文件失败: {e}")
                return False

    def _load_data(self):
        """从JSON文件加载数据"""
        try:
            # 验证并修复任务文件
            self._validate_and_fix_json_file(self.tasks_file)

            # 加载任务数据
            if os.path.exists(self.tasks_file):
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                logger.info(f"加载了 {len(self.tasks)} 个任务")
            else:
                self.tasks = {}
                logger.info("任务文件不存在，初始化空任务存储")
        except Exception as e:
            logger.error(f"加载任务数据失败: {e}")
            self.tasks = {}

    def _save_tasks(self):
        """保存任务数据到JSON文件（原子写入）"""
        try:
            # 使用临时文件进行原子写入
            temp_file = f"{self.tasks_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)

            # 原子性地替换原文件
            os.replace(temp_file, self.tasks_file)
            logger.debug(f"任务数据已保存到 {self.tasks_file}")
            return True
        except Exception as e:
            logger.error(f"保存任务数据失败: {e}")
            # 清理临时文件
            temp_file = f"{self.tasks_file}.tmp"
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False

    def _load_results(self) -> Dict[str, Any]:
        """从JSON文件加载结果数据"""
        try:
            # 验证并修复结果文件
            self._validate_and_fix_json_file(self.results_file)

            if os.path.exists(self.results_file):
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                logger.debug(f"加载了 {len(results)} 个任务结果")
                return results
            else:
                logger.debug("结果文件不存在，返回空字典")
                return {}
        except Exception as e:
            logger.error(f"加载结果数据失败: {e}")
            return {}

    def _save_results(self, results: Dict[str, Any]):
        """保存结果数据到JSON文件（原子写入）"""
        try:
            # 使用临时文件进行原子写入
            temp_file = f"{self.results_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            # 原子性地替换原文件
            os.replace(temp_file, self.results_file)
            logger.debug(f"结果数据已保存到 {self.results_file}")
            return True
        except Exception as e:
            logger.error(f"保存结果数据失败: {e}")
            # 清理临时文件
            temp_file = f"{self.results_file}.tmp"
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False

    def create_task(self, task_type: TaskType, params: Dict[str, Any], prefix: str = None) -> str:
        """创建新任务"""
        base_id = str(uuid.uuid4())
        if prefix:
            # 移除文件扩展名
            filename_without_ext = prefix.replace('.csv', '').replace('.CSV', '')

            # 提取第一个下划线前的内容
            if '_' in filename_without_ext:
                clean_prefix = filename_without_ext.split('_')[0]
            else:
                clean_prefix = filename_without_ext

            # 只保留字母、数字、下划线和连字符
            clean_prefix = ''.join(c for c in clean_prefix if c.isalnum() or c in '_-')

            # 限制长度并确保不为空
            clean_prefix = clean_prefix[:30]  # 前缀长度限制为30个字符
            if clean_prefix:  # 确保前缀不为空
                task_id = f"{clean_prefix}_{base_id}"
            else:
                task_id = base_id
        else:
            task_id = base_id

        with self.lock:
            self.tasks[task_id] = {
                "task_id": task_id,
                "task_type": task_type,
                "status": TaskStatus.PENDING,
                "progress": 0,
                "created_at": datetime.now().isoformat(),
                "started_at": None,
                "completed_at": None,
                "params": params,
                "error": None,
                "current_step": None,
                "total_steps": None
            }
            # 保存到文件
            self._save_tasks()

        logger.info(f"Created task {task_id} of type {task_type}")
        return task_id
    
    def update_task_status(self, task_id: str, status: TaskStatus, **kwargs):
        """更新任务状态"""
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = status
                
                if status == TaskStatus.RUNNING and self.tasks[task_id]["started_at"] is None:
                    self.tasks[task_id]["started_at"] = datetime.now().isoformat()
                elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    self.tasks[task_id]["completed_at"] = datetime.now().isoformat()
                    self.tasks[task_id]["updated_at"] = datetime.now().isoformat()

                # 更新其他字段
                for key, value in kwargs.items():
                    self.tasks[task_id][key] = value

                # 保存到文件
                self._save_tasks()
                logger.info(f"Task {task_id} status updated to {status}")
    
    def update_task_progress(self, task_id: str, progress: int, current_step: str = None, total_steps: int = None):
        """更新任务进度"""
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["progress"] = min(100, max(0, progress))
                if current_step:
                    self.tasks[task_id]["current_step"] = current_step
                if total_steps:
                    self.tasks[task_id]["total_steps"] = total_steps
                # 保存到文件
                self._save_tasks()
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        with self.lock:
            task_info = self.tasks.get(task_id)
            if task_info:
                # 创建副本避免外部修改
                task_copy = task_info.copy()
                # 添加结果信息 - 从文件加载
                if task_info["status"] == TaskStatus.COMPLETED:
                    results = self._load_results()
                    if task_id in results:
                        task_copy["result"] = results[task_id]
                return task_copy
            return None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务"""
        with self.lock:
            # 从文件加载结果数据
            results = self._load_results()

            tasks = []
            for task_id, task_info in self.tasks.items():
                task_copy = task_info.copy()
                if task_info["status"] == TaskStatus.COMPLETED and task_id in results:
                    task_copy["result"] = results[task_id]
                tasks.append(task_copy)
            return sorted(tasks, key=lambda x: x["created_at"], reverse=True)
    
    def set_task_result(self, task_id: str, result: Any):
        """设置任务结果"""
        with self.lock:
            # 从文件加载现有结果
            results = self._load_results()

            # 添加新结果
            results[task_id] = result

            # 保存到文件
            if self._save_results(results):
                logger.info(f"Task {task_id} result saved to file")
            else:
                logger.error(f"Failed to save task {task_id} result to file")
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            if task_id in self.tasks and self.tasks[task_id]["status"] in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                self.tasks[task_id]["status"] = TaskStatus.CANCELLED
                self.tasks[task_id]["completed_at"] = datetime.now().isoformat()
                self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
                # 保存到文件
                self._save_tasks()
                logger.info(f"Task {task_id} cancelled")
                return True
            return False
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)

        with self.lock:
            tasks_to_remove = []
            for task_id, task_info in self.tasks.items():
                # 处理时间字符串
                created_at = task_info["created_at"]
                if isinstance(created_at, str):
                    created_at = datetime.fromisoformat(created_at).timestamp()
                elif hasattr(created_at, 'timestamp'):
                    created_at = created_at.timestamp()
                else:
                    continue

                if created_at < cutoff_time:
                    if task_info["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                        tasks_to_remove.append(task_id)
            
            # 清理任务和结果
            if tasks_to_remove:
                # 加载现有结果
                results = self._load_results()

                for task_id in tasks_to_remove:
                    del self.tasks[task_id]
                    if task_id in results:
                        del results[task_id]
                    logger.info(f"Cleaned up old task {task_id}")

                # 保存更新后的数据
                self._save_tasks()
                self._save_results(results)

# 全局任务存储实例
task_storage = TaskStorage()

# 定期清理任务的后台线程
def cleanup_worker():
    """后台清理工作线程"""
    while True:
        try:
            time.sleep(3600)  # 每小时清理一次
            task_storage.cleanup_old_tasks()
        except Exception as e:
            logger.error(f"Error in cleanup worker: {e}")

# 延迟启动清理线程，避免在导入时就启动
def start_cleanup_worker():
    """启动清理工作线程"""
    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
    cleanup_thread.start()
    logger.info("Task cleanup worker started")

# 在第一次使用时启动清理线程
_cleanup_started = False

def ensure_cleanup_started():
    """确保清理线程已启动"""
    global _cleanup_started
    if not _cleanup_started:
        start_cleanup_worker()
        _cleanup_started = True

# API路由

@router.get("/tasks")
async def get_all_tasks():
    """获取所有任务列表"""
    try:
        ensure_cleanup_started()  # 确保清理线程已启动
        tasks = task_storage.get_all_tasks()
        return {
            "success": True,
            "tasks": tasks,
            "total": len(tasks)
        }
    except Exception as e:
        logger.error(f"Error getting all tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """获取特定任务的状态"""
    try:
        task_info = task_storage.get_task(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "task": task_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        success = task_storage.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="无法取消该任务")
        
        return {
            "success": True,
            "message": "任务已取消"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/running")
async def get_running_tasks():
    """获取正在运行的任务"""
    try:
        all_tasks = task_storage.get_all_tasks()
        running_tasks = [task for task in all_tasks if task["status"] == TaskStatus.RUNNING]

        return {
            "success": True,
            "tasks": running_tasks,
            "count": len(running_tasks)
        }
    except Exception as e:
        logger.error(f"Error getting running tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/completed")
async def get_completed_tasks():
    """获取已完成的任务"""
    try:
        all_tasks = task_storage.get_all_tasks()
        completed_tasks = [task for task in all_tasks if task["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]]

        return {
            "success": True,
            "tasks": completed_tasks,
            "count": len(completed_tasks)
        }
    except Exception as e:
        logger.error(f"Error getting completed tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/task/{task_id}/delete")
async def delete_single_task(task_id: str):
    """删除单个已完成的任务"""
    try:
        # 检查任务是否存在
        task = task_storage.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        # 检查任务是否已完成
        if task["status"] not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            raise HTTPException(status_code=400, detail=f"只能删除已完成的任务，当前状态: {task['status']}")

        # 删除任务和结果
        with task_storage.lock:
            # 删除任务
            task_storage.tasks.pop(task_id, None)

            # 删除结果
            results = task_storage._load_results()
            results.pop(task_id, None)

            # 保存更新后的数据
            task_storage._save_tasks()
            task_storage._save_results(results)

        logger.info(f"Deleted task {task_id}")
        return {
            "success": True,
            "message": f"已删除任务 {task_id}",
            "deleted_task_id": task_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/completed")
async def clear_completed_tasks():
    """清空所有已完成的任务"""
    try:
        all_tasks = task_storage.get_all_tasks()
        completed_task_ids = [task["task_id"] for task in all_tasks if task["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]]

        # 删除已完成的任务
        if completed_task_ids:
            # 加载现有结果
            results = task_storage._load_results()

            for task_id in completed_task_ids:
                task_storage.tasks.pop(task_id, None)
                results.pop(task_id, None)

            # 保存更新后的数据
            task_storage._save_tasks()
            task_storage._save_results(results)

        return {
            "success": True,
            "message": f"已清空 {len(completed_task_ids)} 个已完成的任务",
            "cleared_count": len(completed_task_ids),
            "cleared_task_ids": completed_task_ids
        }
    except Exception as e:
        logger.error(f"Error clearing completed tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 导出任务存储实例供其他模块使用
__all__ = ["router", "task_storage", "TaskStatus", "TaskType"]
