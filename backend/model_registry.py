from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Optional
import json
import os
import logging
from datetime import datetime
from .auth import get_current_user

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

router = APIRouter()

# 模型仓库文件路径
MODEL_REGISTRY_FILE = "model_registry.json"

def load_model_registry() -> Dict:
    """加载模型仓库数据"""
    try:
        if os.path.exists(MODEL_REGISTRY_FILE):
            with open(MODEL_REGISTRY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {"models": []}
    except Exception as e:
        logging.error(f"加载模型仓库失败: {e}")
        return {"models": []}

def save_model_registry(registry_data: Dict) -> bool:
    """保存模型仓库数据"""
    try:
        with open(MODEL_REGISTRY_FILE, 'w', encoding='utf-8') as f:
            json.dump(registry_data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logging.error(f"保存模型仓库失败: {e}")
        return False

def generate_model_id(csv_filename: str = None) -> str:
    """生成唯一的模型ID"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if csv_filename:
        # 移除文件扩展名
        filename_without_ext = csv_filename.replace('.csv', '').replace('.CSV', '')

        # 提取第一个下划线前的内容
        if '_' in filename_without_ext:
            prefix = filename_without_ext.split('_')[0]
        else:
            prefix = filename_without_ext

        # 只保留字母、数字、下划线和连字符
        prefix = ''.join(c for c in prefix if c.isalnum() or c in '_-')

        # 限制长度并确保不为空
        prefix = prefix[:20]  # 前缀长度限制为20个字符
        if prefix:
            return f"{prefix}_model_{timestamp}"

    return f"model_{timestamp}"

@router.post("/register")
async def register_model(
    model_info: Dict,
    current_user: str = Depends(get_current_user)
):
    """
    注册新模型到仓库
    """
    try:
        # 加载现有仓库数据
        registry = load_model_registry()
        
        # 生成模型ID
        csv_filename = model_info.get("data_file", model_info.get("source_data", ""))
        model_id = generate_model_id(csv_filename)
        
        # 构建模型信息（支持新旧字段名）
        model_entry = {
            "model_id": model_id,
            "created_by": current_user,
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "training_time": model_info.get("training_time", ""),
            "source_data": model_info.get("data_file", model_info.get("source_data", "")),
            "protocol": model_info.get("protocol", ""),
            "datatype": model_info.get("data_type", model_info.get("datatype", "")),
            "r2_score": model_info.get("r2_score", 0.0),
            "cleaning_threshold": model_info.get("cleaning_threshold", 0.0),
            "training_duration": model_info.get("duration_seconds", model_info.get("training_duration", 0)),
            "cpu_usage": model_info.get("cpu_percent", model_info.get("cpu_usage", 0.0)),
            "memory_usage": model_info.get("memory_mb", model_info.get("memory_usage", 0.0)),
            "gpu_memory": model_info.get("gpu_memory_mb", model_info.get("gpu_memory", 0.0)),
            "gpu_utilization": model_info.get("gpu_utilization_percent", model_info.get("gpu_utilization", 0.0)),
            "model_architecture": {
                "type": model_info.get("model_type", "GRU"),
                "hidden_size": model_info.get("hidden_size", 50),
                "num_layers": model_info.get("num_layers", 2),
                "sequence_length": model_info.get("sequence_length", 10),
                "dropout": model_info.get("dropout", 0.2)
            },
            "training_params": {
                "learning_rate": model_info.get("learning_rate", 0.001),
                "batch_size": model_info.get("batch_size", 32),
                "epochs": model_info.get("epochs", 100)
            },
            "file_paths": {
                "model_path": model_info.get("model_path", ""),
                "params_path": model_info.get("params_path", ""),
                "scaler_path": model_info.get("scaler_path", ""),
                "test_data_path": model_info.get("test_data_path", "")
            },
            "train_shape": model_info.get("train_shape", []),
            "test_shape": model_info.get("test_shape", [])
        }
        
        # 添加到仓库
        registry["models"].append(model_entry)
        
        # 保存仓库数据
        if save_model_registry(registry):
            logging.info(f"用户 {current_user} 成功注册模型: {model_id}")
            return {
                "success": True,
                "message": "模型注册成功",
                "model_id": model_id
            }
        else:
            raise HTTPException(status_code=500, detail="保存模型信息失败")
            
    except Exception as e:
        logging.error(f"用户 {current_user} 注册模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"注册模型失败: {str(e)}")

@router.get("/list")
async def list_models(
    current_user: str = Depends(get_current_user)
):
    """
    获取模型列表
    """
    try:
        registry = load_model_registry()
        models = registry.get("models", [])
        
        # 按创建时间倒序排列
        models.sort(key=lambda x: x.get("created_time", ""), reverse=True)
        
        logging.info(f"用户 {current_user} 查询模型列表，共 {len(models)} 个模型")
        return {
            "success": True,
            "models": models,
            "total_count": len(models)
        }
        
    except Exception as e:
        logging.error(f"用户 {current_user} 获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

@router.get("/detail/{model_id}")
async def get_model_detail(
    model_id: str,
    current_user: str = Depends(get_current_user)
):
    """
    获取模型详细信息
    """
    try:
        registry = load_model_registry()
        models = registry.get("models", [])
        
        # 查找指定模型
        model = next((m for m in models if m["model_id"] == model_id), None)
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        logging.info(f"用户 {current_user} 查询模型详情: {model_id}")
        return {
            "success": True,
            "model": model
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"用户 {current_user} 获取模型详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型详情失败: {str(e)}")

@router.delete("/delete/{model_id}")
async def delete_model(
    model_id: str,
    current_user: str = Depends(get_current_user)
):
    """
    删除模型
    """
    try:
        registry = load_model_registry()
        models = registry.get("models", [])
        
        # 查找要删除的模型
        model_to_delete = next((m for m in models if m["model_id"] == model_id), None)
        
        if not model_to_delete:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 删除模型文件 - 增加安全检查
        file_paths = model_to_delete.get("file_paths", {})
        deleted_files = []
        failed_files = []
        skipped_files = []

        # 检查文件是否被其他模型引用
        def is_file_referenced_by_other_models(file_path, current_model_id):
            for model in models:
                if model["model_id"] != current_model_id:
                    other_file_paths = model.get("file_paths", {})
                    for other_file_path in other_file_paths.values():
                        if other_file_path == file_path:
                            return True, model["model_id"]
            return False, None

        for file_type, file_path in file_paths.items():
            if file_path and os.path.exists(file_path):
                # 检查是否被其他模型引用
                is_referenced, referencing_model = is_file_referenced_by_other_models(file_path, model_id)

                if is_referenced:
                    skipped_files.append(f"{file_path}: 被模型 {referencing_model} 引用")
                    logging.warning(f"跳过删除文件 {file_path}: 被模型 {referencing_model} 引用")
                else:
                    try:
                        os.remove(file_path)
                        deleted_files.append(file_path)
                        logging.info(f"删除文件: {file_path}")
                    except Exception as e:
                        failed_files.append(f"{file_path}: {str(e)}")
                        logging.error(f"删除文件失败 {file_path}: {e}")
        
        # 从仓库中移除模型记录
        registry["models"] = [m for m in models if m["model_id"] != model_id]
        
        # 保存更新后的仓库数据
        if save_model_registry(registry):
            logging.info(f"用户 {current_user} 成功删除模型: {model_id}")

            message = "模型删除成功"
            if skipped_files:
                message += f"，但跳过了 {len(skipped_files)} 个被其他模型引用的文件"

            return {
                "success": True,
                "message": message,
                "deleted_files": deleted_files,
                "failed_files": failed_files,
                "skipped_files": skipped_files
            }
        else:
            raise HTTPException(status_code=500, detail="更新模型仓库失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"用户 {current_user} 删除模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除模型失败: {str(e)}")

@router.get("/statistics")
async def get_statistics(
    current_user: str = Depends(get_current_user)
):
    """
    获取模型仓库统计信息
    """
    try:
        registry = load_model_registry()
        models = registry.get("models", [])
        
        if not models:
            return {
                "success": True,
                "statistics": {
                    "total_models": 0,
                    "avg_r2_score": 0,
                    "best_model": None,
                    "protocols": {},
                    "datatypes": {}
                }
            }
        
        # 计算统计信息
        total_models = len(models)
        r2_scores = [m.get("r2_score", 0) for m in models if m.get("r2_score") is not None]
        avg_r2_score = sum(r2_scores) / len(r2_scores) if r2_scores else 0
        
        # 找到最佳模型
        best_model = max(models, key=lambda x: x.get("r2_score", 0)) if models else None
        
        # 统计协议和数据类型分布
        protocols = {}
        datatypes = {}
        
        for model in models:
            protocol = model.get("protocol", "未知")
            datatype = model.get("datatype", "未知")
            
            protocols[protocol] = protocols.get(protocol, 0) + 1
            datatypes[datatype] = datatypes.get(datatype, 0) + 1
        
        statistics = {
            "total_models": total_models,
            "avg_r2_score": round(avg_r2_score, 4),
            "best_model": {
                "model_id": best_model.get("model_id"),
                "r2_score": best_model.get("r2_score"),
                "protocol": best_model.get("protocol"),
                "datatype": best_model.get("datatype")
            } if best_model else None,
            "protocols": protocols,
            "datatypes": datatypes
        }
        
        logging.info(f"用户 {current_user} 查询模型仓库统计信息")
        return {
            "success": True,
            "statistics": statistics
        }
        
    except Exception as e:
        logging.error(f"用户 {current_user} 获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
