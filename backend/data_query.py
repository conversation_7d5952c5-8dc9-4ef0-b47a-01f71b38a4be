from fastapi import APIRouter, HTTPException, Depends # 从 fastapi 库导入 APIRouter, HTTPException, 和 Depends
import os # 导入 os 库，用于与操作系统交互
from fastapi.responses import FileResponse # 从 fastapi.responses 库导入 FileResponse，用于返回文件作为响应
from .auth import get_current_user # 从本地 auth 模块导入 get_current_user 函数，用于用户认证

router = APIRouter() # 创建一个 APIRouter 实例，用于定义路由

@router.get("/list_csv_files") # 定义一个 GET 路由 "/list_csv_files"
async def list_csv_files(csv_dir: str = "/data/output", current_user: str = Depends(get_current_user)): # 定义列出 CSV 文件的异步函数
    if not os.path.exists(csv_dir): # 检查 CSV 目录是否存在
        raise HTTPException(status_code=400, detail="CSV目录路径不存在！") # 如果不存在，则抛出 HTTP 400 异常
    csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv') and not f.endswith('_test.csv') and not f.endswith('_predictions.csv')] # 列出目录下所有符合条件的 CSV 文件
    csv_files.sort() # 对文件列表进行排序
    return {"csv_files": csv_files} # 返回 CSV 文件列表

@router.get("/download_csv") # 定义一个 GET 路由 "/download_csv"
async def download_csv(csv_dir: str, csv_file: str, current_user: str = Depends(get_current_user)): # 定义下载 CSV 文件的异步函数
    file_path = os.path.join(csv_dir, csv_file) # 构建文件的完整路径
    if not os.path.exists(file_path): # 检查文件是否存在
        raise HTTPException(status_code=400, detail=f"文件 {csv_file} 不存在！") # 如果不存在，则抛出 HTTP 400 异常
    return FileResponse(file_path, media_type="text/csv", filename=csv_file) # 返回文件响应，提供下载

@router.get("/list_result_files") # 定义一个 GET 路由 "/list_result_files"
async def list_result_files(result_dir: str = "/data/output", current_user: str = Depends(get_current_user)): # 定义列出结果文件的异步函数
    if not os.path.exists(result_dir): # 检查结果目录是否存在
        raise HTTPException(status_code=400, detail="特征预测值文件目录路径不存在！") # 如果不存在，则抛出 HTTP 400 异常
    result_files = [f for f in os.listdir(result_dir) if f.endswith('_results.txt')] # 列出目录下所有以 '_results.txt' 结尾的文件
    result_files.sort() # 对文件列表进行排序
    return {"result_files": result_files} # 返回结果文件列表

@router.get("/get_result_content") # 定义一个 GET 路由 "/get_result_content"
async def get_result_content(result_dir: str, result_file: str, current_user: str = Depends(get_current_user)): # 定义获取结果文件内容的异步函数
    file_path = os.path.join(result_dir, result_file) # 构建文件的完整路径
    if not os.path.exists(file_path): # 检查文件是否存在
        raise HTTPException(status_code=400, detail=f"文件 {result_file} 不存在！") # 如果不存在，则抛出 HTTP 400 异常
    try: # 尝试读取文件
        with open(file_path, 'r', encoding='utf-8') as f: # 以只读模式打开文件
            content = f.read() # 读取文件所有内容
        return {"content": content} # 返回文件内容
    except Exception as e: # 如果读取失败
        raise HTTPException(status_code=500, detail=f"无法读取文件 {result_file}: {e}") # 抛出 HTTP 500 异常

@router.get("/download_result") # 定义一个 GET 路由 "/download_result"
async def download_result(result_dir: str, result_file: str, current_user: str = Depends(get_current_user)): # 定义下载结果文件的异步函数
    file_path = os.path.join(result_dir, result_file) # 构建文件的完整路径
    if not os.path.exists(file_path): # 检查文件是否存在
        raise HTTPException(status_code=400, detail=f"文件 {result_file} 不存在！") # 如果不存在，则抛出 HTTP 400 异常
    return FileResponse(file_path, media_type="text/plain", filename=result_file) # 返回文件响应，提供下载