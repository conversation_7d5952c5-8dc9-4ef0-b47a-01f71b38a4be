from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Depends, BackgroundTasks # 从 fastapi 库导入相关模块
from typing import List, Optional # 从 typing 库导入 List 和 Optional 用于类型提示
from pydantic import BaseModel # 导入 pydantic 的 BaseModel 用于数据验证
import pandas as pd # 导入 pandas 库，并使用别名 pd，用于数据处理
import os # 导入 os 库，用于与操作系统交互
import logging # 导入 logging 库，用于日志记录
import csv # 导入 csv 库，用于读写 CSV 文件
import re # 导入 re 库，用于正则表达式
import shutil # 导入 shutil 库，用于文件操作（如复制）
import string # 导入 string 库，用于字符串操作
import uuid # 导入 uuid 库，用于生成唯一标识符
import asyncio # 导入 asyncio 库，用于异步操作
from datetime import datetime # 导入 datetime 用于时间处理
from .auth import get_current_user # 从本地 auth 模块导入 get_current_user 函数，用于用户认证
from .task_manager import task_storage, TaskStatus, TaskType # 导入任务管理相关模块

router = APIRouter() # 创建一个 APIRouter 实例，用于定义路由

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s') # 配置日志记录器

field_names = ["srcaddress", "dstaddress", "srcport", "dstport", "tcpflags", "protocol", "prot", "ipVer", "packetssam", "timestamp", "custcode"] # 定义 CSV 文件的字段名

# 批量任务相关的数据模型
class BatchTask(BaseModel):
    customer: str
    input_dir: str
    output_dir: str

class BatchAnalyzeRequest(BaseModel):
    tasks: List[BatchTask]

def get_common_prefix(file_names): # 定义一个函数来获取文件名列表的共同前缀
    if not file_names: # 如果文件名列表为空
        return "output" # 返回默认前缀 "output"
    if len(file_names) == 1: # 如果只有一个文件
        return os.path.splitext(file_names[0])[0] # 返回该文件的基本名（不带扩展名）
    min_len = min(len(name) for name in file_names) # 找到所有文件名中最短的长度
    prefix = "" # 初始化前缀为空字符串
    for i in range(min_len): # 遍历最短长度的范围
        char = file_names[0][i] # 获取第一个文件的第 i 个字符
        if all(name[i] == char for name in file_names): # 检查所有文件的第 i 个字符是否都相同
            prefix += char # 如果相同，则将该字符添加到前缀中
        else: # 如果不同
            break # 停止遍历
    valid_chars = string.ascii_letters + string.digits # 定义有效字符集（字母和数字）
    prefix = prefix.rstrip(''.join(c for c in map(chr, range(128)) if c not in valid_chars)) # 从前缀末尾移除所有无效字符
    return prefix if prefix else "output" # 如果前缀不为空则返回前缀，否则返回 "output"

@router.post("/clean_data") # 定义一个 POST 路由 "/clean_data"
async def clean_data( # 定义数据清洗的异步函数
    files: List[UploadFile] = File(None), # 接收上传的文件列表，默认为 None
    folder_path: Optional[str] = Form(None), # 接收本地文件夹路径，默认为 None
    selected_files: Optional[List[str]] = Form(None), # 接收所选的本地文件列表，默认为 None
    output_dir: str = Form("/data/output"), # 接收输出目录路径
    current_user: str = Depends(get_current_user) # 依赖注入，获取当前登录用户
):
    logging.info(f"用户 {current_user} 请求参数: files={len(files) if files else None}, folder_path={folder_path}, selected_files={selected_files}, output_dir={output_dir}") # 记录请求信息

    if not files and not (folder_path and selected_files): # 检查是否提供了输入文件
        raise HTTPException(status_code=400, detail="请提供文件或选择本地文件！") # 如果没有，则抛出 HTTP 400 异常
    if not output_dir: # 检查是否指定了输出目录
        raise HTTPException(status_code=400, detail="请指定CSV输出目录！") # 如果没有，则抛出 HTTP 400 异常
    
    if not os.path.exists(output_dir): # 检查输出目录是否存在
        try: # 尝试创建目录
            os.makedirs(output_dir, exist_ok=True) # 创建输出目录，如果已存在则不报错
        except Exception as e: # 如果创建失败
            raise HTTPException(status_code=500, detail=f"无法创建输出目录 {output_dir}: {str(e)}") # 抛出 HTTP 500 异常

    file_names = [] # 初始化文件名列表
    if files: # 如果有上传文件
        file_names = [file.filename for file in files if file.filename] # 获取上传文件的文件名列表
        source_type = "uploaded" # 设置源类型为 "uploaded"
    else: # 如果没有上传文件（即使用本地文件）
        source_type = "local" # 设置源类型为 "local"
        if not folder_path or not os.path.exists(folder_path): # 检查本地文件夹路径是否存在
            raise HTTPException(status_code=400, detail=f"目录路径不存在: {folder_path}") # 如果不存在，则抛出 HTTP 400 异常
        file_names = selected_files or [] # 获取选择的本地文件列表

    output_csv_name = get_common_prefix(file_names) + ".csv" # 根据共同前缀生成输出 CSV 文件名
    final_csv_path = os.path.join(output_dir, output_csv_name) # 构建最终的 CSV 文件路径

    # 准备文件路径列表
    file_paths = []
    file_iterator = files if source_type == "uploaded" else file_names # 根据源类型确定要迭代的文件列表
    if not file_iterator: # 如果迭代器为空
        raise HTTPException(status_code=400, detail="未提供有效文件进行处理") # 抛出异常

    # 处理上传文件或本地文件路径
    for idx, file_info in enumerate(file_iterator): # 遍历文件列表
        if source_type == "uploaded": # 如果是上传的文件
            file_obj = file_info # file_info 是 UploadFile 对象
            file_path = f"temp_input_{idx}.txt" # 创建一个临时输入文件路径
            with open(file_path, "wb") as f: # 打开临时文件
                f.write(await file_obj.read()) # 读取上传文件内容并写入临时文件
            file_paths.append(file_path)
        else: # 如果是本地文件
            file_path = os.path.join(folder_path, file_info) # 构建本地文件的完整路径
            file_paths.append(file_path)

    try:
        # 调用核心处理逻辑（与批量分析使用相同的逻辑）
        result = process_files_core(file_paths, final_csv_path)

        # 清理上传的临时文件
        if source_type == "uploaded":
            for file_path in file_paths:
                if os.path.exists(file_path):
                    os.remove(file_path)

        return {"message": f"CSV文件已保存到: {final_csv_path}"} # 返回成功消息

    except Exception as e:
        # 清理上传的临时文件
        if source_type == "uploaded":
            for file_path in file_paths:
                if os.path.exists(file_path):
                    os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}") # 抛出 HTTP 500 异常

@router.get("/list_files") # 定义一个 GET 路由 "/list_files"
async def list_files(folder_path: str, current_user: str = Depends(get_current_user)): # 定义列出文件的异步函数
    if not os.path.exists(folder_path): # 检查文件夹路径是否存在
        raise HTTPException(status_code=400, detail=f"目录路径不存在: {folder_path}") # 如果不存在，则抛出 HTTP 400 异常
    files = [f for f in os.listdir(folder_path) if f.endswith('.txt')] # 列出目录下所有以 .txt 结尾的文件
    files.sort() # 对文件列表进行排序
    logging.info(f"用户 {current_user} 文件列表: folder_path={folder_path}, files={files}") # 记录获取的文件列表信息
    return {"files": files} # 返回文件列表

@router.post("/batch_analyze") # 定义批量分析的 POST 路由
async def batch_analyze(
    request: BatchAnalyzeRequest,
    background_tasks: BackgroundTasks,
    current_user: str = Depends(get_current_user)
):
    """批量流量数据分析"""
    try:
        # 验证所有任务的输入目录
        for task in request.tasks:
            if not os.path.exists(task.input_dir):
                raise HTTPException(status_code=400, detail=f"输入目录不存在: {task.input_dir}")

            # 检查是否有TXT文件
            txt_files = [f for f in os.listdir(task.input_dir) if f.endswith('.txt')]
            if not txt_files:
                raise HTTPException(status_code=400, detail=f"目录 {task.input_dir} 中没有TXT文件")

            # 确保输出目录存在
            os.makedirs(task.output_dir, exist_ok=True)

        # 创建批量任务记录
        batch_id = task_storage.create_task(
            task_type=TaskType.DATA_CLEANING,
            params={
                "batch_tasks": [task.dict() for task in request.tasks],
                "user": current_user
            }
        )

        # 启动后台批量处理
        background_tasks.add_task(process_batch_analysis, batch_id, request.tasks, current_user)

        logging.info(f"用户 {current_user} 启动批量分析任务: batch_id={batch_id}, tasks={len(request.tasks)}")

        return {
            "success": True,
            "message": "批量分析任务已启动",
            "batch_id": batch_id,
            "task_count": len(request.tasks)
        }

    except Exception as e:
        logging.error(f"批量分析启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析启动失败: {str(e)}")

@router.get("/batch_status/{batch_id}") # 定义批量任务状态查询的 GET 路由
async def get_batch_status(
    batch_id: str,
    current_user: str = Depends(get_current_user)
):
    """获取批量任务状态"""
    try:
        task_info = task_storage.get_task(batch_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="批量任务不存在")

        return {
            "success": True,
            "batch_id": batch_id,
            "status": task_info["status"],
            "progress": task_info.get("progress", 0),
            "current_step": task_info.get("current_step", ""),
            "created_at": task_info.get("created_at"),
            "completed_at": task_info.get("completed_at"),
            "error": task_info.get("error")
        }

    except Exception as e:
        logging.error(f"获取批量任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取批量任务状态失败: {str(e)}")

async def process_batch_analysis(batch_id: str, tasks: List[BatchTask], user: str):
    """后台批量处理函数"""
    try:
        task_storage.update_task_status(batch_id, TaskStatus.RUNNING)
        task_storage.update_task_progress(batch_id, 0, "开始批量分析")

        total_tasks = len(tasks)
        completed_tasks = 0
        results = []

        for i, task in enumerate(tasks):
            try:
                # 更新当前处理的任务
                current_step = f"处理客户 {task.customer} ({i+1}/{total_tasks})"
                task_storage.update_task_progress(batch_id, int(completed_tasks/total_tasks*100), current_step)

                # 获取输入目录中的所有TXT文件
                txt_files = [f for f in os.listdir(task.input_dir) if f.endswith('.txt')]

                # 处理每个客户的数据
                customer_result = await process_single_customer(task, txt_files)
                results.append({
                    "customer": task.customer,
                    "status": "success",
                    "result": customer_result
                })

                completed_tasks += 1

            except Exception as e:
                logging.error(f"处理客户 {task.customer} 失败: {e}")
                results.append({
                    "customer": task.customer,
                    "status": "failed",
                    "error": str(e)
                })

        # 完成批量任务
        task_storage.update_task_progress(batch_id, 100, "批量分析完成")
        task_storage.update_task_status(batch_id, TaskStatus.COMPLETED)
        task_storage.set_task_result(batch_id, {
            "results": results,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "success_rate": completed_tasks / total_tasks * 100
        })

        logging.info(f"批量分析任务完成: batch_id={batch_id}, 成功率={completed_tasks}/{total_tasks}")

    except Exception as e:
        logging.error(f"批量分析任务失败: batch_id={batch_id}, error={e}")
        task_storage.update_task_status(batch_id, TaskStatus.FAILED, str(e))

def process_files_core(file_paths: List[str], output_csv_path: str) -> dict:
    """
    核心文件处理逻辑 - 从单个目录分析中提取的通用逻辑
    """
    temp_csv_path = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    processed_files = 0
    total_rows = 0

    try:
        with open(temp_csv_path, "w", encoding="utf-8", newline='') as csv_file:
            writer = csv.writer(csv_file)
            writer.writerow(field_names)  # 写入表头

            for file_path in file_paths:
                try:
                    if not os.path.exists(file_path):
                        logging.error(f"文件不存在: {file_path}")
                        continue

                    current_file_name = os.path.basename(file_path)

                    # 使用与单个目录分析完全相同的pandas处理逻辑
                    # 注意：这些文件没有表头，需要指定header=None
                    for chunk in pd.read_csv(file_path, sep="|", chunksize=10000, encoding="utf-8", engine='c', header=None):
                        # 确保列数匹配
                        chunk = chunk.iloc[:, :len(field_names)]
                        # 确保列名正确
                        chunk.columns = field_names[:len(chunk.columns)]

                        # 不修改custcode字段，保持原始数据不变

                        # 检查数据类型
                        logging.info(f"文件 {current_file_name} 的 packetssam 数据类型: {chunk['packetssam'].dtype}")

                        # 将 packetssam 转换为数值类型
                        try:
                            chunk['packetssam'] = pd.to_numeric(chunk['packetssam'], errors='coerce')
                        except Exception as e:
                            logging.error(f"转换 packetssam 列为数值类型失败: {e}")
                            continue

                        # 应用过滤条件
                        chunk = chunk[chunk['packetssam'] <= 4000000]
                        logging.info(f"文件 {current_file_name} 过滤后行数: {len(chunk)}")

                        if not chunk.empty:
                            chunk.to_csv(csv_file, index=False, header=False, mode='a')
                            total_rows += len(chunk)
                        else:
                            logging.warning(f"文件 {current_file_name} 在过滤后没有数据")

                    processed_files += 1

                except Exception as e:
                    logging.error(f"处理文件 {file_path} 时发生错误: {e}")
                    continue

        # 将临时文件复制到最终位置
        try:
            shutil.copy(temp_csv_path, output_csv_path)
            os.remove(temp_csv_path)
        except Exception as e:
            logging.error(f"无法保存CSV文件到 {output_csv_path}: {str(e)}")
            raise Exception(f"无法保存CSV文件: {str(e)}")

        return {
            "output_file": output_csv_path,
            "processed_files": processed_files,
            "total_rows": total_rows,
            "message": f"成功处理 {processed_files} 个文件，共 {total_rows} 行数据"
        }

    except Exception as e:
        # 清理临时文件
        if os.path.exists(temp_csv_path):
            os.remove(temp_csv_path)
        raise Exception(f"文件处理失败: {str(e)}")

async def process_single_customer(task: BatchTask, txt_files: List[str]) -> dict:
    """处理单个客户的数据 - 直接调用核心处理逻辑"""
    try:
        # 使用与单个目录分析相同的文件名生成逻辑
        output_csv_name = get_common_prefix(txt_files) + ".csv"
        output_path = os.path.join(task.output_dir, output_csv_name)

        # 构建完整的文件路径列表
        file_paths = [os.path.join(task.input_dir, txt_file) for txt_file in txt_files]

        # 调用核心处理逻辑（与单个目录分析完全相同，不修改custcode）
        result = process_files_core(file_paths, output_path)

        return result

    except Exception as e:
        raise Exception(f"处理客户 {task.customer} 数据失败: {str(e)}")
