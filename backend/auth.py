from fastapi import APIRouter, HTTPException, status, Depends # 从 fastapi 库导入 APIRouter, HTTPException, status, 和 Depends
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm # 从 fastapi.security 库导入 OAuth2PasswordBearer 和 OAuth2PasswordRequestForm，用于处理 OAuth2 认证
from pydantic import BaseModel # 从 pydantic 库导入 BaseModel，用于数据模型定义和验证
import bcrypt # 导入 bcrypt 库，用于密码哈希
import json # 导入 json 库，用于处理 JSON 数据
import os # 导入 os 库，用于与操作系统交互，如此处用于获取环境变量和文件路径操作
import re # 导入 re 库，用于正则表达式操作，如此处用于密码复杂度校验
import logging # 导入 logging 库，用于日志记录
from datetime import datetime, timedelta, timezone # 从 datetime 库导入 datetime, timedelta, 和 timezone，用于处理日期和时间
from jose import JWTError, jwt # 从 jose 库导入 JWTError 和 jwt，用于创建和验证 JSON Web Tokens (JWT)

router = APIRouter() # 创建一个 APIRouter 实例，用于定义路由

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s') # 配置日志记录器，设置日志级别为 INFO，并指定日志格式

# JWT 配置
SECRET_KEY = os.getenv("SECRET_KEY", "")  # 从环境变量中获取 SECRET_KEY，用于 JWT 签名，如果不存在则使用空字符串
ALGORITHM = "HS256" # 指定 JWT 签名的算法
ACCESS_TOKEN_EXPIRE_MINUTES = 9999 # 设置访问令牌的过期时间（分钟）

# OAuth2 方案
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login") # 创建一个 OAuth2PasswordBearer 实例，指定获取 token 的 URL

class LoginRequest(BaseModel): # 定义登录请求的数据模型
    username: str # 用户名
    password: str # 密码

class ChangePasswordRequest(BaseModel): # 定义修改密码请求的数据模型
    username: str # 用户名
    old_password: str # 旧密码
    new_password: str # 新密码
    confirm_password: str # 确认新密码

class AddUserRequest(BaseModel): # 定义添加用户请求的数据模型
    username: str # 当前操作的用户名 (管理员)
    new_username: str # 新用户的用户名
    new_user_password: str # 新用户的密码
    confirm_user_password: str # 确认新用户的密码

class Token(BaseModel): # 定义令牌的数据模型
    access_token: str # 访问令牌
    token_type: str # 令牌类型，通常是 "bearer"

def hash_password(password): # 定义一个函数来哈希密码
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8') # 使用 bcrypt 对密码进行哈希处理

def check_password(password, hashed): # 定义一个函数来验证密码
    try: # 尝试验证
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8')) # 使用 bcrypt 检查明文密码和哈希密码是否匹配
    except Exception as e: # 如果发生异常
        logging.error(f"密码验证失败: {e}") # 记录错误日志
        return False # 返回 False

def check_password_complexity(password): # 定义一个函数来检查密码复杂度
    if len(password) < 12: # 检查密码长度是否小于12位
        return False, "密码长度必须至少12位" # 如果是，则返回 False 和错误信息
    if not re.search(r'[A-Z]', password): # 检查密码是否包含大写字母
        return False, "密码必须包含至少一个大写字母" # 如果不包含，则返回 False 和错误信息
    if not re.search(r'[a-z]', password): # 检查密码是否包含小写字母
        return False, "密码必须包含至少一个小写字母" # 如果不包含，则返回 False 和错误信息
    if not re.search(r'\d', password): # 检查密码是否包含数字
        return False, "密码必须包含至少一个数字" # 如果不包含，则返回 False 和错误信息
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password): # 检查密码是否包含特殊字符
        return False, "密码必须包含至少一个特殊字符" # 如果不包含，则返回 False 和错误信息
    return True, "" # 如果所有检查都通过，则返回 True 和空字符串

def load_users(json_path="users.json"): # 定义一个函数来加载用户数据
    if not os.path.exists(json_path): # 检查用户数据文件是否存在
        default_users = { # 如果不存在，则创建一个默认用户 "admin"
            "admin": { # "admin" 用户
                "password": hash_password(""), # 密码为空字符串的哈希值
                "is_default": True # 标记为默认密码
            }
        }
        with open(json_path, 'w', encoding='utf-8') as f: # 创建并写入 users.json 文件
            json.dump(default_users, f, ensure_ascii=False, indent=4) # 将默认用户信息以 JSON 格式写入文件
        return default_users # 返回默认用户信息
    try: # 尝试读取用户文件
        with open(json_path, 'r', encoding='utf-8') as f: # 以只读模式打开用户文件
            return json.load(f) # 加载并返回 JSON 数据
    except Exception as e: # 如果发生异常
        logging.error(f"加载用户文件失败: {e}") # 记录错误日志
        return {} # 返回一个空字典

def save_users(users, json_path="users.json"): # 定义一个函数来保存用户数据
    try: # 尝试保存
        with open(json_path, 'w', encoding='utf-8') as f: # 以写入模式打开用户文件
            json.dump(users, f, ensure_ascii=False, indent=4) # 将用户数据以 JSON 格式写入文件
    except Exception as e: # 如果发生异常
        logging.error(f"保存用户文件失败: {e}") # 记录错误日志

def create_access_token(data: dict): # 定义一个函数来创建访问令牌
    to_encode = data.copy() # 复制要编码的数据
    expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES) # 计算令牌的过期时间
    to_encode.update({"exp": expire}) # 将过期时间添加到要编码的数据中
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM) # 使用密钥和算法对数据进行编码，生成 JWT
    return encoded_jwt # 返回生成的 JWT

async def get_current_user(token: str = Depends(oauth2_scheme)): # 定义一个依赖函数来获取当前用户
    credentials_exception = HTTPException( # 定义一个凭证验证失败的异常
        status_code=status.HTTP_401_UNAUTHORIZED, # HTTP 状态码 401
        detail="无法验证凭证", # 错误详情
        headers={"WWW-Authenticate": "Bearer"}, # 响应头
    )
    try: # 尝试解码 JWT
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM]) # 解码 JWT
        username: str = payload.get("sub") # 从 payload 中获取用户名 ("sub" 是 JWT 的标准字段，代表主题)
        if username is None: # 如果用户名为空
            raise credentials_exception # 抛出凭证验证失败的异常
    except JWTError: # 如果解码过程中发生 JWTError
        raise credentials_exception # 抛出凭证验证失败的异常
    users = load_users() # 加载用户数据
    if username not in users: # 检查用户名是否存在于用户数据中
        raise credentials_exception # 如果不存在，抛出凭证验证失败的异常
    return username # 返回用户名

@router.post("/login", response_model=Token) # 定义一个 POST 路由 "/login"，响应模型为 Token
async def login(form_data: OAuth2PasswordRequestForm = Depends()): # 定义登录处理函数，使用 OAuth2PasswordRequestForm 作为依赖来获取表单数据
    users = load_users() # 加载用户数据
    if form_data.username not in users: # 检查用户名是否存在
        raise HTTPException(status_code=400, detail="用户不存在！") # 如果不存在，则抛出 HTTP 400 异常
    if not check_password(form_data.password, users[form_data.username]["password"]): # 检查密码是否正确
        raise HTTPException(status_code=400, detail="密码错误！") # 如果密码错误，则抛出 HTTP 400 异常
    access_token = create_access_token(data={"sub": form_data.username}) # 为用户创建访问令牌
    return {"access_token": access_token, "token_type": "bearer"} # 返回访问令牌和令牌类型

@router.post("/change_password") # 定义一个 POST 路由 "/change_password"
async def change_password(request: ChangePasswordRequest, current_user: str = Depends(get_current_user)): # 定义修改密码处理函数，依赖于 get_current_user 来获取当前用户
    if request.username != current_user: # 检查请求中的用户名是否与当前登录用户一致
        raise HTTPException(status_code=403, detail="无权更改其他用户密码！") # 如果不一致，则抛出 HTTP 403 异常
    users = load_users() # 加载用户数据
    if not check_password(request.old_password, users[request.username]["password"]): # 验证旧密码是否正确
        raise HTTPException(status_code=400, detail="原密码错误！") # 如果旧密码错误，则抛出 HTTP 400 异常
    if request.new_password != request.confirm_password: # 检查两次输入的新密码是否一致
        raise HTTPException(status_code=400, detail="两次输入的新密码不一致！") # 如果不一致，则抛出 HTTP 400 异常
    is_complex, message = check_password_complexity(request.new_password) # 检查新密码的复杂度
    if not is_complex: # 如果密码不够复杂
        raise HTTPException(status_code=400, detail=message) # 抛出 HTTP 400 异常，并附带具体原因
    users[request.username]["password"] = hash_password(request.new_password) # 哈希新密码并更新用户数据
    users[request.username]["is_default"] = False # 将 is_default 标记为 False
    save_users(users) # 保存更新后的用户数据
    return {"message": "密码修改成功，请重新登录！"} # 返回成功消息

@router.post("/add_user") # 定义一个 POST 路由 "/add_user"
async def add_user(request: AddUserRequest, current_user: str = Depends(get_current_user)): # 定义添加用户处理函数，依赖于 get_current_user 来验证当前用户是否为管理员
    if current_user != "admin": # 检查当前用户是否是 "admin"
        raise HTTPException(status_code=403, detail="仅管理员（admin）用户可以添加新用户！") # 如果不是，则抛出 HTTP 403 异常
    users = load_users() # 加载用户数据
    if request.new_username in users: # 检查新用户名是否已存在
        raise HTTPException(status_code=400, detail="用户名已存在，请选择其他用户名！") # 如果已存在，则抛出 HTTP 400 异常
    if not request.new_username: # 检查新用户名是否为空
        raise HTTPException(status_code=400, detail="用户名不能为空！") # 如果为空，则抛出 HTTP 400 异常
    if request.new_user_password != request.confirm_user_password: # 检查两次输入的密码是否一致
        raise HTTPException(status_code=400, detail="两次输入的密码不一致！") # 如果不一致，则抛出 HTTP 400 异常
    is_complex, message = check_password_complexity(request.new_user_password) # 检查新用户密码的复杂度
    if not is_complex: # 如果密码不够复杂
        raise HTTPException(status_code=400, detail=message) # 抛出 HTTP 400 异常，并附带具体原因
    users[request.new_username] = { # 创建新用户信息
        "password": hash_password(request.new_user_password), # 哈希新密码
        "is_default": False # is_default 标记为 False
    }
    save_users(users) # 保存更新后的用户数据
    return {"message": f"用户 {request.new_username} 添加成功！"} # 返回成功消息

@router.get("/users") # 定义一个 GET 路由 "/users"
async def get_users(current_user: str = Depends(get_current_user)): # 定义获取所有用户的处理函数，依赖于 get_current_user 来确保用户已登录
    users = load_users() # 加载用户数据
    return users # 返回所有用户信息
