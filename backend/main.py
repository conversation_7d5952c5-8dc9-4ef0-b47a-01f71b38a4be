from fastapi import FastAPI, Depends, HTTPException # 从 fastapi 库导入 FastAPI, Depends, 和 HTTPException
from fastapi.middleware.cors import CORSMiddleware # 从 fastapi.middleware.cors 库导入 CORSMiddleware 用于处理跨域资源共享
from fastapi.security import OAuth2PasswordBearer # 从 fastapi.security 库导入 OAuth2PasswordBearer 用于 OAuth2 认证
import logging # 导入 logging 库，用于日志记录
import os # 导入 os 库，用于与操作系统交互
from .auth import router as auth_router # 从本地 auth 模块导入路由
from .data_cleaning import router as data_cleaning_router # 从本地 data_cleaning 模块导入路由
from .data_query import router as data_query_router # 从本地 data_query 模块导入路由
from .model_training import router as model_training_router # 从本地 model_training 模块导入路由
from .model_prediction import router as model_prediction_router # 从本地 model_prediction 模块导入路由
from .clean_template import router as clean_template_router # 从本地 clean_template 模块导入路由
from .model_registry import router as model_registry_router # 从本地 model_registry 模块导入路由
from .task_manager import router as task_manager_router # 从本地 task_manager 模块导入路由

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s') # 配置日志记录器

app = FastAPI(title="AI智能清洗策略系统 API") # 创建一个 FastAPI 应用实例，并设置标题

# 允许跨域请求
app.add_middleware( # 添加中间件
    CORSMiddleware, # 使用 CORSMiddleware
    allow_origins=[
        "http://localhost:8231",  # React前端（新端口）
        "http://127.0.0.1:8231",  # React前端（新端口备用地址）
        "http://localhost:3000",  # React前端（原端口）
        "http://************:3000",  # React演示版本
        "http://127.0.0.1:3000",  # React前端（备用地址）
        "http://127.0.0.1:8080",  # React演示版本（备用地址）
    ],
    allow_credentials=True, # 允许携带凭证（如 cookies）
    allow_methods=["*"], # 允许所有 HTTP 方法
    allow_headers=["*"], # 允许所有 HTTP 请求头
)

# 包含各个模块的路由
app.include_router(auth_router, prefix="/auth") # 包含认证模块的路由，并设置前缀为 /auth
app.include_router(data_cleaning_router, prefix="/data_cleaning") # 包含数据清洗模块的路由，并设置前缀为 /data_cleaning
app.include_router(data_query_router, prefix="/data_query") # 包含数据查询模块的路由，并设置前缀为 /data_query
app.include_router(model_training_router, prefix="/model_training") # 包含模型训练模块的路由，并设置前缀为 /model_training
app.include_router(model_prediction_router, prefix="/model_prediction") # 包含模型预测模块的路由，并设置前缀为 /model_prediction
app.include_router(clean_template_router, prefix="/clean_template") # 包含清洗模板模块的路由，并设置前缀为 /clean_template
app.include_router(model_registry_router, prefix="/model_registry") # 包含模型仓库模块的路由，并设置前缀为 /model_registry
app.include_router(task_manager_router, prefix="/tasks") # 包含任务管理模块的路由，并设置前缀为 /tasks

@app.get("/") # 定义一个 GET 路由 "/"
async def root(): # 定义根路由的处理函数
    return {"message": "AI智能清洗策略系统 API"} # 返回一个欢迎消息