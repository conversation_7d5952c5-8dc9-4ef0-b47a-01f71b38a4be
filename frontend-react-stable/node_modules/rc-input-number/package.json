{"name": "rc-input-number", "version": "7.3.11", "description": "React input-number component", "keywords": ["react", "react-component", "react-input-number", "input-number"], "files": ["lib", "es", "assets/*.css"], "main": "./lib/index", "module": "./es/index", "homepage": "https://github.com/react-component/input-number", "author": "<EMAIL>", "repository": {"type": "git", "url": "**************:react-component/input-number.git"}, "bugs": {"url": "http://github.com/react-component/input-number/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father-build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.23.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.8", "@types/jest": "^26.0.0", "@types/react": "^17.0.37", "@types/react-dom": "^17.0.9", "@types/responselike": "^1.0.0", "@umijs/fabric": "^2.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.0", "eslint": "^7.1.0", "expect.js": "~0.3.1", "father": "^2.22.0", "father-build": "^1.19.1", "glob": "^7.1.6", "less": "^3.12.2", "np": "^7.2.0", "rc-tooltip": "^5.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "regenerator-runtime": "^0.13.7", "typescript": "^4.0.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}