"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _classnames = _interopRequireDefault(require("classnames"));
var React = _interopRequireWildcard(require("react"));
var _configProvider = require("../config-provider");
var __rest = void 0 && (void 0).__rest || function (s, e) {
  var t = {};
  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var Grid = function Grid(_a) {
  var prefixCls = _a.prefixCls,
    className = _a.className,
    _a$hoverable = _a.hoverable,
    hoverable = _a$hoverable === void 0 ? true : _a$hoverable,
    props = __rest(_a, ["prefixCls", "className", "hoverable"]);
  return /*#__PURE__*/React.createElement(_configProvider.ConfigConsumer, null, function (_ref) {
    var getPrefixCls = _ref.getPrefixCls;
    var prefix = getPrefixCls('card', prefixCls);
    var classString = (0, _classnames["default"])("".concat(prefix, "-grid"), className, (0, _defineProperty2["default"])({}, "".concat(prefix, "-grid-hoverable"), hoverable));
    return /*#__PURE__*/React.createElement("div", (0, _extends2["default"])({}, props, {
      className: classString
    }));
  });
};
var _default = Grid;
exports["default"] = _default;