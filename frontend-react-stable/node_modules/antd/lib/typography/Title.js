"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _type = require("../_util/type");
var _warning = _interopRequireDefault(require("../_util/warning"));
var _Base = _interopRequireDefault(require("./Base"));
var __rest = void 0 && (void 0).__rest || function (s, e) {
  var t = {};
  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var TITLE_ELE_LIST = (0, _type.tupleNum)(1, 2, 3, 4, 5);
var Title = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var _props$level = props.level,
    level = _props$level === void 0 ? 1 : _props$level,
    restProps = __rest(props, ["level"]);
  var component;
  if (TITLE_ELE_LIST.includes(level)) {
    component = "h".concat(level);
  } else {
    process.env.NODE_ENV !== "production" ? (0, _warning["default"])(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;
    component = 'h1';
  }
  return /*#__PURE__*/React.createElement(_Base["default"], (0, _extends2["default"])({
    ref: ref
  }, restProps, {
    component: component
  }));
});
var _default = Title;
exports["default"] = _default;