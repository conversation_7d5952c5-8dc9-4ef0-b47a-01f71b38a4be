"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.formatCountdown = formatCountdown;
exports.formatTimeStr = formatTimeStr;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _padStart = _interopRequireDefault(require("lodash/padStart"));
// Countdown
var timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365], ['M', 1000 * 60 * 60 * 24 * 30], ['D', 1000 * 60 * 60 * 24], ['H', 1000 * 60 * 60], ['m', 1000 * 60], ['s', 1000], ['S', 1] // million seconds
];

function formatTimeStr(duration, format) {
  var leftDuration = duration;
  var escapeRegex = /\[[^\]]*]/g;
  var keepList = (format.match(escapeRegex) || []).map(function (str) {
    return str.slice(1, -1);
  });
  var templateText = format.replace(escapeRegex, '[]');
  var replacedText = timeUnits.reduce(function (current, _ref) {
    var _ref2 = (0, _slicedToArray2["default"])(_ref, 2),
      name = _ref2[0],
      unit = _ref2[1];
    if (current.includes(name)) {
      var value = Math.floor(leftDuration / unit);
      leftDuration -= value * unit;
      return current.replace(new RegExp("".concat(name, "+"), 'g'), function (match) {
        var len = match.length;
        return (0, _padStart["default"])(value.toString(), len, '0');
      });
    }
    return current;
  }, templateText);
  var index = 0;
  return replacedText.replace(escapeRegex, function () {
    var match = keepList[index];
    index += 1;
    return match;
  });
}
function formatCountdown(value, config) {
  var _config$format = config.format,
    format = _config$format === void 0 ? '' : _config$format;
  var target = new Date(value).getTime();
  var current = Date.now();
  var diff = Math.max(target - current, 0);
  return formatTimeStr(diff, format);
}