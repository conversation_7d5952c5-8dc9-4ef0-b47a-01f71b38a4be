"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _ga_IE = _interopRequireDefault(require("rc-picker/lib/locale/ga_IE"));
var _ga_IE2 = _interopRequireDefault(require("../../time-picker/locale/ga_IE"));
// Merge into a locale object
var locale = {
  lang: (0, _extends2["default"])({
    placeholder: 'Roghnaigh dáta',
    yearPlaceholder: 'Roghnaigh bliain',
    quarterPlaceholder: 'Roghnaigh ráithe',
    monthPlaceholder: 'Roghnaigh mí',
    weekPlaceholder: 'Roghnaigh seachtain',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> tosaigh', '<PERSON><PERSON><PERSON> deiridh'],
    rangeYearPlaceholder: ['T<PERSON> na bliana', '<PERSON>iread<PERSON> na bliana'],
    rangeMonthPlaceholder: ['Tosaigh mhí', 'Deireadh mhí'],
    rangeWeekPlaceholder: ['Tosaigh an tseachtain', 'Deireadh na seachtaine']
  }, _ga_IE["default"]),
  timePickerLocale: (0, _extends2["default"])({}, _ga_IE2["default"])
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
var _default = locale;
exports["default"] = _default;