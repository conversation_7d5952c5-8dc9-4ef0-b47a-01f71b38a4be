"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _fr_FR = _interopRequireDefault(require("rc-picker/lib/locale/fr_FR"));
var _fr_FR2 = _interopRequireDefault(require("../../time-picker/locale/fr_FR"));
// Merge into a locale object
var locale = {
  lang: (0, _extends2["default"])({
    placeholder: 'Sélectionner une date',
    yearPlaceholder: 'Sélectionner une année',
    quarterPlaceholder: 'Sélectionner un trimestre',
    monthPlaceholder: 'Sélectionner un mois',
    weekPlaceholder: 'Sélectionner une semaine',
    rangePlaceholder: ['Date de début', 'Date de fin'],
    rangeYearPlaceholder: ['<PERSON><PERSON> de début', '<PERSON><PERSON> de fin'],
    rangeMonthPlaceholder: ['<PERSON>is de début', '<PERSON><PERSON> de fin'],
    rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin']
  }, _fr_FR["default"]),
  timePickerLocale: (0, _extends2["default"])({}, _fr_FR2["default"])
};
// All settings at:
// https://github.com/ant-design/ant-design/issues/424
var _default = locale;
exports["default"] = _default;