"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _tr_TR = _interopRequireDefault(require("rc-picker/lib/locale/tr_TR"));
var _tr_TR2 = _interopRequireDefault(require("../../time-picker/locale/tr_TR"));
// Merge into a locale object
var locale = {
  lang: (0, _extends2["default"])({
    placeholder: 'Tarih seç',
    yearPlaceholder: 'Yıl seç',
    quarterPlaceholder: '<PERSON>ey<PERSON> seç',
    monthPlaceholder: 'Ay seç',
    weekPlaceholder: 'Hafta seç',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> tarihi', '<PERSON><PERSON><PERSON> tarihi'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> yılı', '<PERSON><PERSON><PERSON> yılı'],
    rangeMonthPlaceholder: ['<PERSON>şlangıç ayı', 'Bitiş ayı'],
    rangeWeekPlaceholder: ['Başlangıç haftası', 'Bitiş haftası']
  }, _tr_TR["default"]),
  timePickerLocale: (0, _extends2["default"])({}, _tr_TR2["default"])
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
var _default = locale;
exports["default"] = _default;