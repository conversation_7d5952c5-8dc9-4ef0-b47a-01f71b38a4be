"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _fr_BE = _interopRequireDefault(require("rc-pagination/lib/locale/fr_BE"));
var _fr_BE2 = _interopRequireDefault(require("../calendar/locale/fr_BE"));
var _fr_BE3 = _interopRequireDefault(require("../date-picker/locale/fr_BE"));
var _fr_BE4 = _interopRequireDefault(require("../time-picker/locale/fr_BE"));
var localeValues = {
  locale: 'fr',
  Pagination: _fr_BE["default"],
  DatePicker: _fr_BE3["default"],
  TimePicker: _fr_BE4["default"],
  Calendar: _fr_BE2["default"],
  Table: {
    filterTitle: 'Filtrer',
    filterConfirm: 'OK',
    filterReset: 'Réinitialiser',
    filterCheckall: 'Sélectionner la page actuelle'
  },
  Modal: {
    okText: 'OK',
    cancelText: 'Annuler',
    justOkText: 'OK'
  },
  Popconfirm: {
    okText: 'OK',
    cancelText: 'Annuler'
  },
  Transfer: {
    titles: ['', ''],
    searchPlaceholder: 'Recherche',
    itemUnit: 'élément',
    itemsUnit: 'éléments'
  },
  Upload: {
    uploading: 'Téléchargement...',
    removeFile: 'Effacer le fichier',
    uploadError: 'Erreur de téléchargement',
    previewFile: 'Fichier de prévisualisation',
    downloadFile: 'Télécharger un fichier'
  },
  Empty: {
    description: 'Aucune donnée'
  },
  Text: {
    edit: 'éditer',
    copy: 'copier',
    copied: 'copie effectuée',
    expand: 'développer'
  }
};
var _default = localeValues;
exports["default"] = _default;