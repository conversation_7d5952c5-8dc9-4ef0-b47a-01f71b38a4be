import type { FormProviderProps as RcFormProviderProps } from 'rc-field-form/lib/FormContext';
import type { Meta } from 'rc-field-form/lib/interface';
import type { FC, PropsWithChildren, ReactNode } from 'react';
import * as React from 'react';
import type { ColProps } from '../grid/col';
import type { FormInstance, RequiredMark } from './Form';
import type { ValidateStatus } from './FormItem';
import type { FormLabelAlign } from './interface';
/** Form Context. Set top form style and pass to Form Item usage. */
export interface FormContextProps {
    vertical: boolean;
    name?: string;
    colon?: boolean;
    labelAlign?: FormLabelAlign;
    labelWrap?: boolean;
    labelCol?: ColProps;
    wrapperCol?: ColProps;
    requiredMark?: RequiredMark;
    itemRef: (name: (string | number)[]) => (node: React.ReactElement) => void;
    form?: FormInstance;
}
export declare const FormContext: React.Context<FormContextProps>;
/** `noStyle` Form Item Context. Used for error collection */
export declare type ReportMetaChange = (meta: Meta, uniqueKeys: React.Key[]) => void;
export declare const NoStyleItemContext: React.Context<ReportMetaChange | null>;
/** Form Provider */
export interface FormProviderProps extends Omit<RcFormProviderProps, 'validateMessages'> {
    prefixCls?: string;
}
export declare const FormProvider: React.FC<FormProviderProps>;
/** Used for ErrorList only */
export interface FormItemPrefixContextProps {
    prefixCls: string;
    status?: ValidateStatus;
}
export declare const FormItemPrefixContext: React.Context<FormItemPrefixContextProps>;
export interface FormItemStatusContextProps {
    isFormItemInput?: boolean;
    status?: ValidateStatus;
    hasFeedback?: boolean;
    feedbackIcon?: ReactNode;
}
export declare const FormItemInputContext: React.Context<FormItemStatusContextProps>;
export declare type NoFormStyleProps = PropsWithChildren<{
    status?: boolean;
    override?: boolean;
}>;
export declare const NoFormStyle: FC<NoFormStyleProps>;
