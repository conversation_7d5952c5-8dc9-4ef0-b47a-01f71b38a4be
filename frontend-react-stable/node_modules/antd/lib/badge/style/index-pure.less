@import '../../style/themes/index';
@import '../../style/mixins/index';

@badge-prefix-cls: ~'@{ant-prefix}-badge';
@number-prefix-cls: ~'@{ant-prefix}-scroll-number';

.@{badge-prefix-cls} {
  .reset-component();

  position: relative;
  display: inline-block;
  line-height: 1;

  &-count {
    z-index: @zindex-badge;
    min-width: @badge-height;
    height: @badge-height;
    padding: 0 6px;
    color: @badge-text-color;
    font-weight: @badge-font-weight;
    font-size: @badge-font-size;
    line-height: @badge-height;
    white-space: nowrap;
    text-align: center;
    background: @badge-color;
    border-radius: (@badge-height / 2);
    box-shadow: 0 0 0 1px @shadow-color-inverse;

    a,
    a:hover {
      color: @badge-text-color;
    }
  }

  &-count-sm {
    min-width: @badge-height-sm;
    height: @badge-height-sm;
    padding: 0;
    font-size: @badge-font-size-sm;
    line-height: @badge-height-sm;
    border-radius: (@badge-height-sm / 2);
  }

  &-multiple-words {
    padding: 0 8px;
  }

  &-dot {
    z-index: @zindex-badge;
    width: @badge-dot-size;
    min-width: @badge-dot-size;
    height: @badge-dot-size;
    background: @highlight-color;
    border-radius: 100%;
    box-shadow: 0 0 0 1px @shadow-color-inverse;
  }

  // Tricky way to resolve https://github.com/ant-design/ant-design/issues/30088
  &-dot.@{number-prefix-cls} {
    transition: background 1.5s;
  }

  &-count,
  &-dot,
  .@{number-prefix-cls}-custom-component {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
    transform-origin: 100% 0%;

    &.@{iconfont-css-prefix}-spin {
      animation: antBadgeLoadingCircle 1s infinite linear;
    }
  }

  &-status {
    line-height: inherit;
    vertical-align: baseline;

    &-dot {
      position: relative;
      top: -1px;
      display: inline-block;
      width: @badge-status-size;
      height: @badge-status-size;
      vertical-align: middle;
      border-radius: 50%;
    }

    &-success {
      background-color: @success-color;
    }

    &-processing {
      position: relative;
      background-color: @processing-color;

      &::after {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 1px solid @processing-color;
        border-radius: 50%;
        animation: antStatusProcessing 1.2s infinite ease-in-out;
        content: '';
      }
    }

    &-default {
      background-color: @normal-color;
    }

    &-error {
      background-color: @error-color;
    }

    &-warning {
      background-color: @warning-color;
    }

    // mixin to iterate over colors and create CSS class for each one
    .make-color-classes(@i: length(@preset-colors)) when (@i > 0) {
      .make-color-classes(@i - 1);
      @color: extract(@preset-colors, @i);
      @darkColor: '@{color}-6';
      &-@{color} {
        background: @@darkColor;
      }
    }
    .make-color-classes();

    &-text {
      margin-left: 8px;
      color: @text-color;
      font-size: @font-size-base;
    }
  }

  &-zoom-appear,
  &-zoom-enter {
    animation: antZoomBadgeIn @animation-duration-slow @ease-out-back;
    animation-fill-mode: both;
  }

  &-zoom-leave {
    animation: antZoomBadgeOut @animation-duration-slow @ease-in-back;
    animation-fill-mode: both;
  }

  &-not-a-wrapper {
    .@{badge-prefix-cls}-zoom-appear,
    .@{badge-prefix-cls}-zoom-enter {
      animation: antNoWrapperZoomBadgeIn @animation-duration-slow @ease-out-back;
    }

    .@{badge-prefix-cls}-zoom-leave {
      animation: antNoWrapperZoomBadgeOut @animation-duration-slow @ease-in-back;
    }

    &:not(.@{badge-prefix-cls}-status) {
      vertical-align: middle;
    }

    .@{number-prefix-cls}-custom-component,
    .@{badge-prefix-cls}-count {
      transform: none;
    }

    .@{number-prefix-cls}-custom-component,
    .@{number-prefix-cls} {
      position: relative;
      top: auto;
      display: block;
      transform-origin: 50% 50%;
    }
  }
}

@keyframes antStatusProcessing {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

// Safari will blink with transform when inner element has absolute style.
.safari-fix-motion() {
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-transform-style: preserve-3d;
  -webkit-backface-visibility: hidden;
  /* stylelint-enable property-no-vendor-prefix */
}

.@{number-prefix-cls} {
  overflow: hidden;
  direction: ltr;

  &-only {
    position: relative;
    display: inline-block;
    height: @badge-height;
    transition: all @animation-duration-slow @ease-in-out;
    .safari-fix-motion;

    > p.@{number-prefix-cls}-only-unit {
      height: @badge-height;
      margin: 0;
      .safari-fix-motion;
    }
  }

  &-symbol {
    vertical-align: top;
  }
}

@keyframes antZoomBadgeIn {
  0% {
    transform: scale(0) translate(50%, -50%);
    opacity: 0;
  }

  100% {
    transform: scale(1) translate(50%, -50%);
  }
}

@keyframes antZoomBadgeOut {
  0% {
    transform: scale(1) translate(50%, -50%);
  }

  100% {
    transform: scale(0) translate(50%, -50%);
    opacity: 0;
  }
}

@keyframes antNoWrapperZoomBadgeIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  100% {
    transform: scale(1);
  }
}

@keyframes antNoWrapperZoomBadgeOut {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
    opacity: 0;
  }
}

@keyframes antBadgeLoadingCircle {
  0% {
    transform-origin: 50%;
  }

  100% {
    transform: translate(50%, -50%) rotate(360deg);
    transform-origin: 50%;
  }
}

@import './ribbon';
@import './rtl';
