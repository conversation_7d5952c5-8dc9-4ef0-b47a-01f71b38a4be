declare const _default: {
    Affix: {
        global: string[];
        component: {};
    };
    Alert: {
        global: string[];
        component: {};
    };
    Anchor: {
        global: string[];
        component: {};
    };
    Avatar: {
        global: string[];
        component: {};
    };
    BackTop: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Badge: {
        global: string[];
        component: {};
    };
    Breadcrumb: {
        global: string[];
        component: {};
    };
    Button: {
        global: string[];
        component: {};
    };
    Calendar: {
        global: string[];
        component: {
            yearControlWidth: number;
            monthControlWidth: number;
            miniContentHeight: number;
        };
    };
    Card: {
        global: string[];
        component: {};
    };
    Carousel: {
        global: string[];
        component: {
            dotWidth: number;
            dotHeight: number;
            dotWidthActive: number;
        };
    };
    Cascader: {
        global: string[];
        component: {
            controlWidth: number;
            controlItemWidth: number;
            dropdownHeight: number;
        };
    };
    Checkbox: {
        global: string[];
        component: {};
    };
    Collapse: {
        global: string[];
        component: {};
    };
    DatePicker: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Descriptions: {
        global: string[];
        component: {};
    };
    Divider: {
        global: string[];
        component: {
            sizePaddingEdgeHorizontal: number;
        };
    };
    Drawer: {
        global: string[];
        component: {};
    };
    Dropdown: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Empty: {
        global: string[];
        component: {};
    };
    Form: {
        global: string[];
        component: {};
    };
    Grid: {
        global: never[];
        component: {};
    };
    Image: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    InputNumber: {
        global: string[];
        component: {
            controlWidth: number;
            handleWidth: number;
            handleFontSize: number;
        };
    };
    Input: {
        global: string[];
        component: {};
    };
    Layout: {
        global: string[];
        component: {
            colorBgHeader: string;
            colorBgBody: string;
            colorBgTrigger: string;
        };
    };
    List: {
        global: string[];
        component: {
            contentWidth: number;
        };
    };
    Mentions: {
        global: string[];
        component: {
            dropdownHeight: number;
            controlItemWidth: number;
            zIndexPopup: number;
        };
    };
    Menu: {
        global: string[];
        component: {
            dropdownWidth: number;
            zIndexPopup: number;
        };
    };
    Message: {
        global: string[];
        component: {
            height: number;
            zIndexPopup: number;
        };
    };
    Modal: {
        global: string[];
        component: {};
    };
    Notification: {
        global: string[];
        component: {
            zIndexPopup: number;
            width: number;
        };
    };
    Pagination: {
        global: string[];
        component: {};
    };
    Popconfirm: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Popover: {
        global: string[];
        component: {
            zIndexPopup: number;
            width: number;
        };
    };
    Progress: {
        global: string[];
        component: {};
    };
    Radio: {
        global: string[];
        component: {};
    };
    Rate: {
        global: string[];
        component: {};
    };
    Result: {
        global: string[];
        component: {
            imageWidth: number;
            imageHeight: number;
        };
    };
    Segmented: {
        global: string[];
        component: {
            bgColor: string;
            bgColorHover: string;
            bgColorSelected: string;
        };
    };
    Select: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Skeleton: {
        global: string[];
        component: {
            color: string;
            colorGradientEnd: string;
        };
    };
    Slider: {
        global: string[];
        component: {
            controlSize: number;
            railSize: number;
            handleSize: number;
            dotSize: number;
            lineHandleWidth: number;
        };
    };
    Space: {
        global: never[];
        component: {};
    };
    Spin: {
        global: string[];
        component: {
            contentHeight: number;
        };
    };
    Statistic: {
        global: string[];
        component: {};
    };
    Steps: {
        global: string[];
        component: {
            descriptionWidth: number;
        };
    };
    Switch: {
        global: string[];
        component: {};
    };
    Table: {
        global: string[];
        component: {};
    };
    Tabs: {
        global: string[];
        component: {
            zIndexPopup: number;
        };
    };
    Tag: {
        global: string[];
        component: {};
    };
    Timeline: {
        global: string[];
        component: {};
    };
    Tooltip: {
        global: string[];
        component: {
            zIndexPopup: number;
            colorBgDefault: string;
        };
    };
    Transfer: {
        global: string[];
        component: {
            listWidth: number;
            listHeight: number;
            listWidthLG: number;
        };
    };
    TreeSelect: {
        global: string[];
        component: {};
    };
    Tree: {
        global: string[];
        component: {};
    };
    Typography: {
        global: string[];
        component: {
            sizeMarginHeadingVerticalStart: string;
            sizeMarginHeadingVerticalEnd: string;
        };
    };
    Upload: {
        global: string[];
        component: {};
    };
};
export default _default;
