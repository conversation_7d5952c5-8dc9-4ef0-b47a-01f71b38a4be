"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = {
  Affix: {
    global: ['zIndexBase'],
    component: {}
  },
  Alert: {
    global: ['fontSizeHeading3', 'paddingXS', 'padding', 'motionDurationSlow', 'marginXS', 'fontSize', 'fontSizeLG', 'lineHeight', 'controlRadius', 'motionEaseInOutCirc', 'colorText', 'marginXXS', 'colorSuccess', 'colorSuccessBorder', 'colorSuccessBg', 'colorWarning', 'colorWarningBorder', 'colorWarningBg', 'colorError', 'colorErrorBorder', 'colorErrorBg', 'colorInfo', 'colorInfoBorder', 'colorInfoBg', 'controlLineWidth', 'controlLineType', 'fontSizeIcon', 'colorAction', 'colorActionHover'],
    component: {}
  },
  Anchor: {
    global: ['fontSize', 'lineHeight', 'fontSizeLG', 'padding', 'paddingXXS', 'lineWidthBold', 'colorText', 'colorSplit', 'colorBgContainer', 'colorPrimary', 'motionDurationSlow'],
    component: {}
  },
  Avatar: {
    global: ['colorTextLightSolid', 'controlHeight', 'controlHeightLG', 'controlHeightSM', 'fontSize', 'fontSizeLG', 'fontSizeXL', 'fontSizeHeading3', 'marginXS', 'colorBorderBg', 'colorTextPlaceholder', 'radiusBase', 'lineWidth', 'lineType', 'colorText', 'lineHeight'],
    component: {}
  },
  BackTop: {
    global: ['zIndexBase', 'fontSizeHeading3', 'colorTextSecondary', 'colorTextLightSolid', 'colorText', 'controlHeightLG', 'fontSize', 'lineHeight', 'motionDurationSlow', 'screenMD', 'screenXS'],
    component: {
      zIndexPopup: 10
    }
  },
  Badge: {
    global: ['fontSize', 'lineHeight', 'fontSizeSM', 'controlLineWidth', 'marginXS', 'colorBorderBg', 'colorBgContainer', 'colorError', 'motionDurationSlow', 'blue-6', 'purple-6', 'cyan-6', 'green-6', 'magenta-6', 'pink-6', 'red-6', 'orange-6', 'yellow-6', 'volcano-6', 'geekblue-6', 'lime-6', 'gold-6', 'colorText', 'paddingXS', 'motionDurationFast', 'colorSuccess', 'colorPrimary', 'colorTextPlaceholder', 'colorWarning', 'motionEaseOutBack', 'controlRadius', 'colorTextLightSolid'],
    component: {}
  },
  Breadcrumb: {
    global: ['colorTextSecondary', 'fontSizeBase', 'colorText', 'paddingXS', 'fontSize', 'lineHeight', 'motionDurationSlow', 'marginXXS'],
    component: {}
  },
  Button: {
    global: ['buttonColorBgTextHover', 'buttonColorBgTextActive', 'controlTmpOutline', 'controlLineWidth', 'controlLineType', 'motionDurationSlow', 'motionEaseInOut', 'lineHeight', 'colorText', 'marginXS', 'controlHeightSM', 'paddingXS', 'fontSize', 'opacityLoading', 'controlHeight', 'padding', 'controlHeightLG', 'fontSizeLG', 'controlRadius', 'colorBorder', 'colorTextDisabled', 'colorBgContainerDisabled', 'colorBgContainer', 'controlOutlineWidth', 'colorPrimaryHover', 'colorPrimaryActive', 'colorError', 'colorErrorHover', 'colorErrorActive', 'colorTextLightSolid', 'colorPrimary', 'colorPrimaryOutline', 'colorErrorOutline', 'colorLink', 'colorLinkHover', 'colorLinkActive', 'fontSizeBase'],
    component: {}
  },
  Calendar: {
    global: ['paddingXXS', 'controlHeight', 'fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'colorPrimary', 'colorBgContainer', 'controlItemBgActive', 'fontSizeSM', 'lineHeightSM', 'marginXS', 'lineWidth', 'paddingSM', 'paddingXS', 'controlLineType', 'colorBorder', 'radiusBase', 'colorTextHeading', 'colorSplit', 'colorAction', 'motionDurationSlow', 'colorActionHover', 'fontWeightStrong', 'colorText', 'colorTextDisabled', 'controlItemBgHover', 'colorTextLightSolid', 'colorBgContainerDisabled', 'colorLink', 'colorLinkHover', 'colorLinkActive', 'lineWidthBold', 'lineType', 'screenXS'],
    component: {
      yearControlWidth: 80,
      monthControlWidth: 70,
      miniContentHeight: 256
    }
  },
  Card: {
    global: ['padding', 'boxShadowCard', 'fontSizeLG', 'paddingLG', 'lineWidth', 'paddingSM', 'fontSize', 'colorBorderSecondary', 'colorText', 'lineHeight', 'colorBgContainer', 'radiusBase', 'colorTextHeading', 'fontWeightStrong', 'controlLineWidth', 'controlLineType', 'fontSizeBase', 'motionDurationSlow', 'colorTextSecondary', 'colorPrimary', 'marginXXS', 'marginXS', 'colorBgContainerSecondary'],
    component: {}
  },
  Carousel: {
    global: ['controlHeightLG', 'controlHeightSM', 'colorText', 'fontSize', 'lineHeight', 'motionDurationSlow', 'colorBgContainer'],
    component: {
      dotWidth: 16,
      dotHeight: 3,
      dotWidthActive: 24
    }
  },
  Cascader: {
    global: ['controlHeight', 'fontSize', 'lineHeight', 'controlInteractiveSize', 'colorText', 'marginXS', 'colorBgContainer', 'controlLineWidth', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'lineWidthBold', 'motionDurationFast', 'paddingXS', 'fontSizeLG', 'colorPrimary', 'motionEaseOutBack', 'colorBgContainerDisabled', 'colorTextDisabled', 'colorSplit', 'paddingSM', 'controlItemBgHover', 'fontWeightStrong', 'controlItemBgActive', 'paddingXXS', 'colorTextSecondary', 'fontSizeIcon', 'colorHighlight'],
    component: {
      controlWidth: 184,
      controlItemWidth: 111,
      dropdownHeight: 180
    }
  },
  Checkbox: {
    global: ['controlInteractiveSize', 'colorText', 'fontSize', 'lineHeight', 'marginXS', 'colorBgContainer', 'controlLineWidth', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'lineWidthBold', 'motionDurationFast', 'paddingXS', 'fontSizeLG', 'colorPrimary', 'motionEaseOutBack', 'colorBgContainerDisabled', 'colorTextDisabled'],
    component: {}
  },
  Collapse: {
    global: ['colorBgContainer', 'padding', 'colorBgContainerSecondary', 'paddingSM', 'radiusBase', 'controlLineWidth', 'controlLineType', 'colorBorder', 'colorText', 'colorTextHeading', 'colorTextDisabled', 'fontSize', 'lineHeight', 'marginSM', 'motionDurationSlow', 'paddingXXS'],
    component: {}
  },
  DatePicker: {
    global: ['zIndexPopupBase', 'paddingXXS', 'controlHeight', 'fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'colorPrimary', 'colorText', 'colorBgContainer', 'controlLineType', 'colorBorder', 'radiusBase', 'motionDurationSlow', 'controlOutlineWidth', 'colorPrimaryOutline', 'colorBgContainerDisabled', 'colorTextDisabled', 'controlRadius', 'colorTextPlaceholder', 'paddingXS', 'marginXS', 'colorTextSecondary', 'lineWidthBold', 'paddingSM', 'colorTextHeading', 'colorSplit', 'colorAction', 'colorActionHover', 'fontWeightStrong', 'controlItemBgHover', 'controlItemBgActive', 'colorTextLightSolid', 'colorLink', 'colorLinkHover', 'colorLinkActive', 'sizePopupArrow', 'colorPrimaryBorder', 'boxShadowPopoverArrowBottom', 'colorBgElevated', 'boxShadow', 'colorError', 'colorErrorOutline', 'colorWarning', 'colorWarningOutline'],
    component: {
      zIndexPopup: 1050
    }
  },
  Descriptions: {
    global: ['colorBgContainerSecondary', 'fontSizeSM', 'lineHeightSM', 'colorText', 'paddingXS', 'padding', 'paddingLG', 'paddingSM', 'marginXS', 'marginXXS', 'fontSize', 'lineHeight', 'lineWidth', 'lineType', 'colorSplit', 'fontWeightStrong', 'fontSizeLG', 'lineHeightLG', 'radiusBase'],
    component: {}
  },
  Divider: {
    global: ['marginXS', 'margin', 'marginLG', 'colorSplit', 'controlLineWidth', 'colorText', 'fontSize', 'lineHeight', 'colorTextHeading', 'fontSizeLG'],
    component: {
      sizePaddingEdgeHorizontal: 0
    }
  },
  Drawer: {
    global: ['paddingXS', 'padding', 'motionEaseOut', 'motionDurationSlow', 'fontSizeLG', 'paddingLG', 'lineWidth', 'radiusBase', 'fontSize', 'lineHeight', 'zIndexPopupBase', 'colorText', 'lineType', 'colorSplit', 'colorBgElevated', 'fontWeightStrong', 'lineHeightLG', 'marginSM', 'colorAction', 'colorActionHover', 'colorBgMask', 'boxShadowDrawerRight', 'boxShadowDrawerLeft', 'boxShadowDrawerDown', 'boxShadowDrawerUp'],
    component: {}
  },
  Dropdown: {
    global: ['zIndexPopupBase', 'marginXXS', 'sizePopupArrow', 'controlHeight', 'fontSizeBase', 'lineHeight', 'paddingXXS', 'motionDurationMid', 'motionDurationSlow', 'radiusBase', 'colorTextDisabled', 'fontSizeIcon', 'controlPaddingHorizontal', 'colorBgElevated', 'colorText', 'fontSize', 'boxShadowPopoverArrow', 'boxShadowPopoverArrowBottom', 'controlRadius', 'boxShadow', 'colorTextSecondary', 'marginXS', 'fontSizeSM', 'colorPrimary', 'controlItemBgActive', 'controlItemBgHover', 'colorSplit', 'paddingXS', 'motionEaseOutQuint', 'motionEaseInQuint', 'motionEaseOutCirc', 'motionEaseInOutCirc', 'opacityLoading', 'colorError', 'colorTextLightSolid'],
    component: {
      zIndexPopup: 1050
    }
  },
  Empty: {
    global: ['controlHeightLG', 'margin', 'marginXS', 'marginXL', 'fontSizeBase', 'lineHeight', 'opacityImage', 'colorTextDisabled'],
    component: {}
  },
  Form: {
    global: ['colorText', 'fontSize', 'lineHeight', 'marginLG', 'colorTextSecondary', 'fontSizeLG', 'controlLineWidth', 'controlLineType', 'colorBorder', 'controlOutlineWidth', 'colorPrimaryOutline', 'paddingSM', 'controlHeightSM', 'controlHeightLG', 'motionDurationSlow', 'colorError', 'colorWarning', 'controlHeight', 'colorTextHeading', 'marginXXS', 'marginXS', 'motionDurationMid', 'motionEaseOut', 'motionEaseOutBack', 'colorSuccess', 'colorPrimary', 'motionEaseInOut', 'margin', 'paddingXS', 'screenSMMax', 'screenMDMax', 'screenLGMax'],
    component: {}
  },
  Grid: {
    global: [],
    component: {}
  },
  Image: {
    global: ['zIndexPopupBase', 'colorTextLightSolid', 'fontSizeIcon', 'controlHeightLG', 'colorBgContainerDisabled', 'motionDurationSlow', 'paddingXXS', 'marginXXS', 'motionEaseOut', 'paddingSM', 'colorText', 'fontSize', 'lineHeight', 'marginSM', 'colorBgMask', 'motionDurationMid', 'motionEaseOutCirc', 'motionEaseInOutCirc'],
    component: {
      zIndexPopup: 1080
    }
  },
  InputNumber: {
    global: ['controlHeightSM', 'controlLineWidth', 'fontSize', 'paddingXXS', 'controlHeight', 'lineHeight', 'controlHeightLG', 'fontSizeLG', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'controlLineType', 'colorBorder', 'controlRadius', 'colorError', 'colorTextSecondary', 'motionDurationFast', 'colorPrimary', 'motionDurationSlow', 'colorBgContainer', 'motionDurationMid', 'colorTextDisabled', 'colorText', 'colorTextPlaceholder', 'controlOutlineWidth', 'colorPrimaryOutline', 'colorBgContainerDisabled', 'colorWarning', 'colorErrorOutline', 'colorWarningOutline', 'paddingXS', 'colorBgContainerSecondary'],
    component: {
      controlWidth: 90,
      handleWidth: 22,
      handleFontSize: 7
    }
  },
  Input: {
    global: ['paddingXXS', 'controlHeight', 'fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'colorText', 'colorBgContainer', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'colorTextPlaceholder', 'controlOutlineWidth', 'colorPrimaryOutline', 'colorTextDisabled', 'colorBgContainerDisabled', 'colorError', 'colorWarning', 'colorErrorOutline', 'colorWarningOutline', 'paddingLG', 'colorTextSecondary', 'colorAction', 'colorActionHover', 'paddingXS', 'fontSizeIcon', 'colorSuccess', 'colorBgContainerSecondary', 'colorPrimary'],
    component: {}
  },
  Layout: {
    global: ['colorText', 'controlHeightSM', 'controlHeight', 'controlHeightLG', 'marginXXS', 'colorTextLightSolid', 'motionDurationMid', 'motionDurationSlow', 'fontSizeBase', 'radiusBase', 'fontSizeXL', 'colorBgContainer'],
    component: {
      colorBgHeader: '#001529',
      colorBgBody: '#f0f2f5',
      colorBgTrigger: '#002140'
    }
  },
  List: {
    global: ['controlHeightLG', 'paddingSM', 'paddingXS', 'padding', 'paddingLG', 'controlHeight', 'marginLG', 'colorPrimary', 'margin', 'colorText', 'colorTextSecondary', 'motionDurationSlow', 'lineWidth', 'fontSize', 'lineHeight', 'marginXXS', 'marginXXL', 'colorSplit', 'fontSizeSM', 'colorTextDisabled', 'fontSizeLG', 'lineHeightLG', 'controlLineType', 'lineType', 'radiusBase', 'colorBorder', 'screenSM', 'screenMD', 'marginSM'],
    component: {
      contentWidth: 220
    }
  },
  Mentions: {
    global: ['zIndexPopupBase', 'paddingXXS', 'controlHeight', 'fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'colorTextDisabled', 'controlItemBgHover', 'colorText', 'motionDurationSlow', 'colorBgContainer', 'controlRadius', 'boxShadow', 'controlLineType', 'colorBorder', 'colorTextPlaceholder', 'controlOutlineWidth', 'colorPrimaryOutline', 'colorBgContainerDisabled', 'colorError', 'colorWarning', 'colorErrorOutline', 'colorWarningOutline', 'fontWeightStrong'],
    component: {
      dropdownHeight: 250,
      controlItemWidth: 100,
      zIndexPopup: 1050
    }
  },
  Menu: {
    global: ['zIndexPopupBase', 'colorPrimary', 'colorError', 'colorTextDisabled', 'colorErrorHover', 'colorErrorOutline', 'colorText', 'colorTextLightSolid', 'colorTextSecondary', 'colorBgContainer', 'colorBgContainerSecondary', 'controlHeightLG', 'fontSize', 'controlItemBgActive', 'lineWidth', 'lineWidthBold', 'motionDurationSlow', 'motionDurationMid', 'motionEaseInOut', 'motionEaseOut', 'lineHeight', 'paddingXS', 'padding', 'colorBorderSecondary', 'radiusBase', 'controlHeightSM', 'lineType', 'margin', 'motionDurationFast', 'paddingXL', 'fontSizeLG', 'boxShadow', 'marginXS', 'marginXXS', 'controlOutlineWidth', 'colorPrimaryHover'],
    component: {
      dropdownWidth: 160,
      zIndexPopup: 1050
    }
  },
  Message: {
    global: ['zIndexPopupBase', 'controlHeightLG', 'fontSize', 'lineHeight', 'padding', 'boxShadow', 'colorBgElevated', 'colorSuccess', 'colorError', 'colorWarning', 'colorInfo', 'fontSizeLG', 'motionEaseInOutCirc', 'motionDurationSlow', 'marginXS', 'paddingXS', 'radiusBase', 'colorText'],
    component: {
      height: 150,
      zIndexPopup: 1010
    }
  },
  Modal: {
    global: ['padding', 'fontSizeHeading5', 'lineHeightHeading5', 'paddingLG', 'colorBgElevated', 'controlLineWidth', 'controlLineType', 'colorSplit', 'colorTextHeading', 'colorTextSecondary', 'paddingXS', 'fontSizeLG', 'colorActionHover', 'fontSize', 'lineHeight', 'zIndexPopupBase', 'screenSMMax', 'marginXS', 'colorText', 'margin', 'fontWeightStrong', 'controlRadius', 'boxShadow', 'motionDurationSlow', 'fontSizeBase', 'marginLG', 'colorError', 'colorWarning', 'colorInfo', 'colorSuccess', 'colorBgMask', 'motionDurationMid', 'motionEaseOutCirc', 'motionEaseInOutCirc'],
    component: {}
  },
  Notification: {
    global: ['zIndexPopupBase', 'padding', 'paddingLG', 'colorBgElevated', 'margin', 'marginLG', 'boxShadow', 'fontSizeLG', 'radiusBase', 'colorSuccess', 'colorInfo', 'colorWarning', 'colorError', 'colorTextHeading', 'motionDurationMid', 'motionEaseInOut', 'fontSizeBase', 'lineHeight', 'colorText', 'fontSize', 'marginXS', 'lineHeightLG', 'marginXXS', 'marginXXL', 'colorAction', 'colorActionHover'],
    component: {
      zIndexPopup: 1050,
      width: 384
    }
  },
  Pagination: {
    global: ['controlHeight', 'fontFamily', 'colorBgContainer', 'fontWeightStrong', 'controlHeightSM', 'controlItemBgActiveDisabled', 'colorTextDisabled', 'marginXXS', 'controlHeightLG', 'marginSM', 'paddingXXS', 'fontSize', 'lineHeight', 'controlLineWidth', 'fontSizeLG', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'colorText', 'marginXS', 'controlLineType', 'colorBorder', 'radiusBase', 'colorPrimary', 'motionDurationSlow', 'fontSizeSM', 'motionDurationMid', 'margin', 'controlRadius', 'colorTextPlaceholder', 'controlOutlineWidth', 'colorPrimaryOutline', 'colorBgContainerDisabled', 'screenLG', 'screenSM'],
    component: {}
  },
  Popconfirm: {
    global: ['zIndexPopupBase', 'paddingSM', 'padding', 'paddingXXS', 'colorText', 'colorWarning', 'marginXS', 'marginXXS', 'fontSize', 'lineHeight'],
    component: {
      zIndexPopup: 1060
    }
  },
  Popover: {
    global: ['zIndexPopupBase', 'controlHeight', 'fontSize', 'lineHeight', 'lineWidth', 'colorBgElevated', 'colorText', 'padding', 'lineType', 'fontWeightStrong', 'boxShadow', 'colorSplit', 'colorTextHeading', 'radiusBase', 'paddingSM', 'sizePopupArrow', 'marginXXS', 'boxShadowPopoverArrow', 'boxShadowPopoverArrowBottom', 'blue-6', 'purple-6', 'cyan-6', 'green-6', 'magenta-6', 'pink-6', 'red-6', 'orange-6', 'yellow-6', 'volcano-6', 'geekblue-6', 'lime-6', 'gold-6', 'motionDurationMid', 'motionEaseOutCirc', 'motionEaseInOutCirc'],
    component: {
      zIndexPopup: 1030,
      width: 177
    }
  },
  Progress: {
    global: ['marginXXS', 'colorText', 'colorInfo', 'colorBgContent', 'fontSize', 'lineHeight', 'fontSizeBase', 'marginXS', 'paddingXS', 'motionDurationSlow', 'motionEaseInOutCirc', 'colorSuccess', 'colorBgContainer', 'motionEaseOutQuint', 'colorError', 'fontSizeSM'],
    component: {}
  },
  Radio: {
    global: ['padding', 'controlLineWidth', 'colorBgContainerDisabled', 'paddingXXS', 'colorTextDisabled', 'colorBgContainer', 'fontSize', 'lineHeight', 'fontSizeLG', 'colorPrimaryOutline', 'colorPrimaryHover', 'colorPrimaryActive', 'colorText', 'colorPrimary', 'marginXS', 'controlOutlineWidth', 'motionDurationSlow', 'motionEaseInOut', 'motionEaseInOutCirc', 'colorBorder', 'paddingXS', 'controlLineType', 'controlHeight', 'controlHeightLG', 'controlHeightSM', 'controlRadius'],
    component: {}
  },
  Rate: {
    global: ['colorBgFillTmp', 'yellow-6', 'controlHeightLG', 'colorText', 'fontSize', 'lineHeight', 'marginXS', 'motionDurationSlow', 'lineWidth'],
    component: {}
  },
  Result: {
    global: ['paddingLG', 'fontSizeHeading3', 'fontSize', 'colorInfo', 'colorError', 'colorSuccess', 'colorWarning', 'lineHeightHeading3', 'padding', 'paddingXL', 'paddingXS', 'marginXS', 'lineHeight', 'colorTextHeading', 'colorTextSecondary', 'colorBgContainerSecondary'],
    component: {
      imageWidth: 250,
      imageHeight: 295
    }
  },
  Segmented: {
    global: ['segmentedBgColor', 'segmentedBgColorActive', 'segmentedBgColorHover', 'lineWidthBold', 'controlLineWidth', 'colorTextLabel', 'colorText', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'fontSize', 'lineHeight', 'radiusBase', 'motionDurationSlow', 'motionEaseInOut', 'controlRadius', 'boxShadowSegmentedSelectedItem', 'controlHeight', 'marginSM', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'colorTextDisabled', 'paddingXXS'],
    component: {
      bgColor: 'rgba(0, 0, 0, 0.04)',
      bgColorHover: 'rgba(0, 0, 0, 0.06)',
      bgColorSelected: '#ffffff'
    }
  },
  Select: {
    global: ['zIndexPopupBase', 'controlPaddingHorizontal', 'colorText', 'fontSize', 'lineHeight', 'colorBgContainer', 'controlLineWidth', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'motionEaseInOut', 'colorTextDisabled', 'colorBgContainerDisabled', 'colorTextPlaceholder', 'fontSizeIcon', 'colorTextSecondary', 'paddingXXS', 'controlPaddingHorizontalSM', 'controlHeight', 'controlHeightSM', 'controlHeightLG', 'fontSizeLG', 'controlHeightXS', 'colorBgContent', 'colorSplit', 'paddingXS', 'colorAction', 'colorActionHover', 'fontFamily', 'colorBgElevated', 'boxShadow', 'fontSizeSM', 'controlItemBgHover', 'fontWeightStrong', 'controlItemBgActive', 'colorPrimary', 'motionDurationMid', 'motionEaseOutQuint', 'motionEaseInQuint', 'motionEaseOutCirc', 'motionEaseInOutCirc', 'colorPrimaryHover', 'colorPrimaryOutline', 'controlOutlineWidth', 'colorErrorHover', 'colorErrorOutline', 'colorWarningHover', 'colorWarningOutline'],
    component: {
      zIndexPopup: 1050
    }
  },
  Skeleton: {
    global: ['colorBgFillTmp', 'colorTextPlaceholder', 'controlHeight', 'radiusLG', 'marginLG', 'marginXXS', 'controlHeightLG', 'controlHeightSM', 'padding', 'marginSM', 'controlHeightXS', 'radiusBase'],
    component: {
      color: 'rgba(0, 0, 0, 0.06)',
      colorGradientEnd: 'rgba(0, 0, 0, 0.25)'
    }
  },
  Slider: {
    global: ['controlHeightSM', 'lineWidth', 'controlHeight', 'controlHeightLG', 'colorPrimary', 'colorBgContentHover', 'colorText', 'fontSize', 'lineHeight', 'colorBgContent', 'controlRadius', 'motionDurationSlow', 'colorPrimaryBorder', 'colorBgContainer', 'colorPrimaryHover', 'colorPrimaryBorderHover', 'colorTextSecondary', 'colorSplit', 'colorTextDisabled'],
    component: {
      controlSize: 12,
      railSize: 4,
      handleSize: 14,
      dotSize: 8,
      lineHandleWidth: 2
    }
  },
  Space: {
    global: [],
    component: {}
  },
  Spin: {
    global: ['colorTextSecondary', 'controlHeightLG', 'controlHeight', 'colorText', 'fontSize', 'lineHeight', 'colorPrimary', 'motionDurationSlow', 'motionEaseInOutCirc', 'colorBgContainer', 'marginXXS'],
    component: {
      contentHeight: 400
    }
  },
  Statistic: {
    global: ['fontSizeHeading3', 'fontSize', 'fontFamily', 'marginXXS', 'padding', 'colorTextSecondary', 'colorTextHeading', 'colorText', 'lineHeight'],
    component: {}
  },
  Steps: {
    global: ['controlHeight', 'colorSplit', 'colorPrimary', 'colorTextDisabled', 'fontSizeHeading3', 'fontSizeLG', 'controlHeightLG', 'colorText', 'colorTextLightSolid', 'colorTextSecondary', 'colorError', 'fontSize', 'lineHeight', 'motionDurationSlow', 'marginXS', 'fontFamily', 'controlLineWidth', 'controlLineType', 'marginSM', 'lineWidth', 'padding', 'fontSizeBase', 'colorBgContainer', 'fontWeightStrong', 'fontSizeSM', 'paddingSM', 'margin', 'marginXXS', 'paddingXXS', 'paddingLG', 'paddingXS', 'controlHeightSM', 'fontSizeIcon', 'lineType', 'lineWidthBold', 'marginLG'],
    component: {
      descriptionWidth: 140
    }
  },
  Switch: {
    global: ['fontSize', 'lineHeight', 'controlHeight', 'paddingXXS', 'motionDurationMid', 'colorPrimary', 'opacityLoading', 'colorBgContainer', 'fontSizeIcon', 'colorText', 'colorTextDisabled', 'controlOutlineWidth', 'controlTmpOutline', 'colorPrimaryOutline', 'colorTextLightSolid', 'fontSizeSM'],
    component: {}
  },
  Table: {
    global: ['controlItemBgActive', 'controlItemBgActiveHover', 'colorTextPlaceholder', 'colorTextHeading', 'colorSplit', 'fontSize', 'padding', 'paddingXS', 'paddingSM', 'controlHeight', 'colorBgContainerSecondary', 'colorAction', 'colorActionHover', 'opacityLoading', 'colorBgContainer', 'colorBgContent', 'radiusBase', 'colorBgFillTmp', 'controlInteractiveSize', 'fontWeightStrong', 'controlLineWidth', 'controlLineType', 'motionDurationSlow', 'colorText', 'lineHeight', 'margin', 'marginXXS', 'fontSizeIcon', 'colorPrimary', 'paddingXXS', 'lineWidth', 'fontSizeSM', 'colorTextSecondary', 'colorPrimaryActive', 'colorTextDisabled', 'controlItemBgHover', 'boxShadow', 'colorLink', 'colorLinkHover', 'colorLinkActive'],
    component: {}
  },
  Tabs: {
    global: ['zIndexPopupBase', 'controlHeightLG', 'colorPrimaryHover', 'colorPrimaryActive', 'fontSize', 'lineHeight', 'controlLineWidth', 'padding', 'marginXXS', 'marginXL', 'colorBgContainerSecondary', 'paddingXXS', 'paddingXS', 'fontSizeBase', 'fontSizeLG', 'marginSM', 'marginXS', 'margin', 'colorSplit', 'controlLineType', 'lineWidthBold', 'motionDurationSlow', 'controlHeight', 'boxShadowTabsOverflowLeft', 'boxShadowTabsOverflowRight', 'paddingLG', 'boxShadowTabsOverflowTop', 'boxShadowTabsOverflowBottom', 'colorBorder', 'colorText', 'colorBgContainer', 'radiusBase', 'boxShadow', 'paddingSM', 'colorTextSecondary', 'fontSizeSM', 'controlItemBgHover', 'colorTextDisabled', 'motionEaseInOut', 'colorPrimary', 'colorTextHeading'],
    component: {
      zIndexPopup: 1050
    }
  },
  Tag: {
    global: ['fontSize', 'lineHeight', 'controlLineWidth', 'fontSizeIcon', 'fontSizeSM', 'colorBgContainerSecondary', 'colorText', 'paddingXS', 'paddingXXS', 'marginXS', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'colorTextSecondary', 'colorTextHeading', 'colorTextLightSolid', 'colorPrimary', 'colorPrimaryActive', 'blue-1', 'blue-3', 'blue-6', 'blue-7', 'purple-1', 'purple-3', 'purple-6', 'purple-7', 'cyan-1', 'cyan-3', 'cyan-6', 'cyan-7', 'green-1', 'green-3', 'green-6', 'green-7', 'magenta-1', 'magenta-3', 'magenta-6', 'magenta-7', 'pink-1', 'pink-3', 'pink-6', 'pink-7', 'red-1', 'red-3', 'red-6', 'red-7', 'orange-1', 'orange-3', 'orange-6', 'orange-7', 'yellow-1', 'yellow-3', 'yellow-6', 'yellow-7', 'volcano-1', 'volcano-3', 'volcano-6', 'volcano-7', 'geekblue-1', 'geekblue-3', 'geekblue-6', 'geekblue-7', 'lime-1', 'lime-3', 'lime-6', 'lime-7', 'gold-1', 'gold-3', 'gold-6', 'gold-7', 'colorSuccess', 'colorSuccessBg', 'colorSuccessBorder', 'colorInfo', 'colorInfoBg', 'colorInfoBorder', 'colorError', 'colorErrorBg', 'colorErrorBorder', 'colorWarning', 'colorWarningBg', 'colorWarningBorder'],
    component: {}
  },
  Timeline: {
    global: ['padding', 'paddingXXS', 'colorText', 'fontSize', 'lineHeight', 'radiusLG', 'lineWidthBold', 'lineType', 'colorSplit', 'fontSizeSM', 'colorBgContainer', 'radiusBase', 'colorPrimary', 'colorError', 'colorSuccess', 'colorTextDisabled', 'lineWidth', 'margin', 'controlHeightLG', 'marginXXS', 'radiusSM', 'marginSM', 'marginXS', 'fontSizeBase'],
    component: {}
  },
  Tooltip: {
    global: ['zIndexPopupBase', 'colorBgTooltipTmp', 'radiusBase', 'colorTextLightSolid', 'controlHeight', 'boxShadow', 'paddingSM', 'paddingXS', 'colorText', 'fontSize', 'lineHeight', 'blue-6', 'purple-6', 'cyan-6', 'green-6', 'magenta-6', 'pink-6', 'red-6', 'orange-6', 'yellow-6', 'volcano-6', 'geekblue-6', 'lime-6', 'gold-6', 'sizePopupArrow', 'marginXXS', 'boxShadowPopoverArrow', 'boxShadowPopoverArrowBottom', 'motionDurationFast', 'motionEaseOutCirc', 'motionEaseInOutCirc'],
    component: {
      zIndexPopup: 1070,
      colorBgDefault: 'rgba(0, 0, 0, 0.85)'
    }
  },
  Transfer: {
    global: ['fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'controlHeight', 'marginXS', 'marginXXS', 'fontSizeIcon', 'colorText', 'colorBgContainerDisabled', 'colorBorder', 'colorSplit', 'controlItemBgActive', 'controlItemBgActiveHover', 'colorTextDisabled', 'paddingSM', 'controlLineType', 'radiusBase', 'colorBgContainer', 'fontSizeBase', 'motionDurationSlow', 'colorLink', 'colorLinkHover', 'colorLinkActive', 'controlItemBgHover', 'paddingXS', 'margin', 'colorError', 'colorWarning'],
    component: {
      listWidth: 180,
      listHeight: 200,
      listWidthLG: 250
    }
  },
  TreeSelect: {
    global: ['colorBgElevated', 'paddingXS', 'controlHeightSM', 'fontSizeLG', 'colorText', 'fontSize', 'lineHeight', 'controlRadius', 'motionDurationSlow', 'colorPrimaryOutline', 'colorPrimary', 'colorTextDisabled', 'controlItemBgHover', 'colorBorder', 'lineWidthBold', 'colorTextLightSolid', 'controlInteractiveSize', 'marginXS', 'colorBgContainer', 'controlLineWidth', 'controlLineType', 'motionDurationFast', 'motionEaseOutBack', 'colorBgContainerDisabled'],
    component: {}
  },
  Tree: {
    global: ['controlInteractiveSize', 'colorText', 'fontSize', 'lineHeight', 'marginXS', 'colorBgContainer', 'controlLineWidth', 'controlLineType', 'colorBorder', 'controlRadius', 'motionDurationSlow', 'lineWidthBold', 'motionDurationFast', 'paddingXS', 'fontSizeLG', 'colorPrimary', 'motionEaseOutBack', 'colorBgContainerDisabled', 'colorTextDisabled', 'controlHeightSM', 'colorPrimaryOutline', 'controlItemBgHover', 'colorTextLightSolid'],
    component: {}
  },
  Typography: {
    global: ['colorText', 'colorTextSecondary', 'colorSuccess', 'colorWarning', 'colorError', 'colorErrorActive', 'colorErrorHover', 'colorTextDisabled', 'fontSizeHeading1', 'lineHeightHeading1', 'colorTextHeading', 'fontWeightStrong', 'fontSizeHeading2', 'lineHeightHeading2', 'fontSizeHeading3', 'lineHeightHeading3', 'fontSizeHeading4', 'lineHeightHeading4', 'fontSizeHeading5', 'lineHeightHeading5', 'colorLink', 'motionDurationSlow', 'colorLinkHover', 'colorLinkActive', 'linkDecoration', 'linkHoverDecoration', 'marginXXS', 'paddingXXS', 'controlHeight', 'fontSize', 'lineHeight', 'controlLineWidth', 'controlHeightLG', 'fontSizeLG', 'controlHeightSM', 'controlPaddingHorizontal', 'controlPaddingHorizontalSM', 'colorPrimaryHover', 'paddingSM', 'marginXS'],
    component: {
      sizeMarginHeadingVerticalStart: '1.2em',
      sizeMarginHeadingVerticalEnd: '0.5em'
    }
  },
  Upload: {
    global: ['fontSizeHeading3', 'fontSizeBase', 'lineHeight', 'lineWidth', 'controlHeightLG', 'colorText', 'fontSize', 'colorBgContainerSecondary', 'controlLineWidth', 'colorBorder', 'radiusBase', 'motionDurationSlow', 'padding', 'colorPrimaryHover', 'margin', 'colorPrimary', 'marginXXS', 'colorTextHeading', 'fontSizeLG', 'colorTextSecondary', 'colorTextDisabled', 'paddingXS', 'controlLineType', 'paddingSM', 'fontSizeHeading2', 'colorError', 'colorErrorBg', 'colorTextLightSolid', 'marginXS', 'colorBgMask', 'marginXL', 'controlItemBgHover', 'motionEaseInOutCirc'],
    component: {}
  }
};
exports["default"] = _default;