"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.changeConfirmLocale = changeConfirmLocale;
exports.getConfirmLocale = getConfirmLocale;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _default = _interopRequireDefault(require("../locale/default"));
var runtimeLocale = (0, _extends2["default"])({}, _default["default"].Modal);
function changeConfirmLocale(newLocale) {
  if (newLocale) {
    runtimeLocale = (0, _extends2["default"])((0, _extends2["default"])({}, runtimeLocale), newLocale);
  } else {
    runtimeLocale = (0, _extends2["default"])({}, _default["default"].Modal);
  }
}
function getConfirmLocale() {
  return runtimeLocale;
}