"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _classnames = _interopRequireDefault(require("classnames"));
var _rcMenu = require("rc-menu");
var React = _interopRequireWildcard(require("react"));
var _configProvider = require("../config-provider");
var __rest = void 0 && (void 0).__rest || function (s, e) {
  var t = {};
  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var MenuDivider = function MenuDivider(_a) {
  var customizePrefixCls = _a.prefixCls,
    className = _a.className,
    dashed = _a.dashed,
    restProps = __rest(_a, ["prefixCls", "className", "dashed"]);
  var _React$useContext = React.useContext(_configProvider.ConfigContext),
    getPrefixCls = _React$useContext.getPrefixCls;
  var prefixCls = getPrefixCls('menu', customizePrefixCls);
  var classString = (0, _classnames["default"])((0, _defineProperty2["default"])({}, "".concat(prefixCls, "-item-divider-dashed"), !!dashed), className);
  return /*#__PURE__*/React.createElement(_rcMenu.Divider, (0, _extends2["default"])({
    className: classString
  }, restProps));
};
var _default = MenuDivider;
exports["default"] = _default;