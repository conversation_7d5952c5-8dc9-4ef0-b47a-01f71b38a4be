@import '../../style/themes/index';
@import '../../style/mixins/index';

@popover-prefix-cls: ~'@{ant-prefix}-popover';

.@{popover-prefix-cls} {
  &-rtl {
    direction: rtl;
    text-align: right;
  }

  &-message {
    &-icon {
      .@{popover-prefix-cls}-rtl & {
        margin-right: 0;
        margin-left: 8px;
      }
    }

    &-title {
      .@{popover-prefix-cls}-rtl & {
        padding-left: @padding-md;
      }
    }
  }

  &-buttons {
    .@{popover-prefix-cls}-rtl & {
      text-align: left;
    }

    button {
      .@{popover-prefix-cls}-rtl & {
        margin-right: 8px;
        margin-left: 0;
      }
    }
  }
}
