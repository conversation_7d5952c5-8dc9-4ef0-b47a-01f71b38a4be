"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _createSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/createSuper"));
var React = _interopRequireWildcard(require("react"));
var _ = _interopRequireDefault(require("."));
var ErrorBoundary = /*#__PURE__*/function (_React$Component) {
  (0, _inherits2["default"])(ErrorBoundary, _React$Component);
  var _super = (0, _createSuper2["default"])(ErrorBoundary);
  function ErrorBoundary() {
    var _this;
    (0, _classCallCheck2["default"])(this, ErrorBoundary);
    _this = _super.apply(this, arguments);
    _this.state = {
      error: undefined,
      info: {
        componentStack: ''
      }
    };
    return _this;
  }
  (0, _createClass2["default"])(ErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, info) {
      this.setState({
        error: error,
        info: info
      });
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props,
        message = _this$props.message,
        description = _this$props.description,
        children = _this$props.children;
      var _this$state = this.state,
        error = _this$state.error,
        info = _this$state.info;
      var componentStack = info && info.componentStack ? info.componentStack : null;
      var errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;
      var errorDescription = typeof description === 'undefined' ? componentStack : description;
      if (error) {
        return /*#__PURE__*/React.createElement(_["default"], {
          type: "error",
          message: errorMessage,
          description: /*#__PURE__*/React.createElement("pre", null, errorDescription)
        });
      }
      return children;
    }
  }]);
  return ErrorBoundary;
}(React.Component);
exports["default"] = ErrorBoundary;