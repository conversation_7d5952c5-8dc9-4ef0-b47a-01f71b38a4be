"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PresetStatusColorTypes = exports.PresetColorTypes = void 0;
var _type = require("./type");
var PresetStatusColorTypes = (0, _type.tuple)('success', 'processing', 'error', 'default', 'warning');
// eslint-disable-next-line import/prefer-default-export
exports.PresetStatusColorTypes = PresetStatusColorTypes;
var PresetColorTypes = (0, _type.tuple)('pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime');
exports.PresetColorTypes = PresetColorTypes;