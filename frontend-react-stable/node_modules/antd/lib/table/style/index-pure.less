@import '../../style/themes/index';
@import '../../style/mixins/index';
@import './size';
@import './bordered';

@table-prefix-cls: ~'@{ant-prefix}-table';
@tree-prefix-cls: ~'@{ant-prefix}-tree';
@dropdown-prefix-cls: ~'@{ant-prefix}-dropdown';
@descriptions-prefix-cls: ~'@{ant-prefix}-descriptions';
@table-header-icon-color: #bfbfbf;
@table-header-icon-color-hover: darken(@table-header-icon-color, 10%);
@table-sticky-zindex: calc(@zindex-table-fixed + 1);
@table-sticky-scroll-bar-active-bg: fade(@table-sticky-scroll-bar-bg, 80%);
@table-filter-dropdown-max-height: 264px;
@table-expand-column-width: 48px;

.@{table-prefix-cls}-wrapper {
  clear: both;
  max-width: 100%;
  .clearfix();
}

.@{table-prefix-cls} {
  .reset-component();
  position: relative;
  font-size: @table-font-size;
  background: @table-bg;
  border-radius: @table-border-radius-base;

  // https://github.com/ant-design/ant-design/issues/17611
  table {
    width: 100%;
    text-align: left;
    border-radius: @table-border-radius-base @table-border-radius-base 0 0;
    border-collapse: separate;
    border-spacing: 0;
  }

  // ============================= Cell =============================
  &-thead > tr > th,
  &-tbody > tr > td,
  tfoot > tr > th,
  tfoot > tr > td {
    position: relative;
    padding: @table-padding-vertical @table-padding-horizontal;
    overflow-wrap: break-word;
  }

  &-cell-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;

    // Fixed first or last should special process
    &.@{table-prefix-cls}-cell-fix-left-last,
    &.@{table-prefix-cls}-cell-fix-right-first {
      overflow: visible;

      .@{table-prefix-cls}-cell-content {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .@{table-prefix-cls}-column-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;
    }
  }

  // ============================ Title =============================
  &-title {
    padding: @table-padding-vertical @table-padding-horizontal;
  }

  // ============================ Footer ============================
  &-footer {
    padding: @table-padding-vertical @table-padding-horizontal;
    color: @table-footer-color;
    background: @table-footer-bg;
  }

  // ============================ Header ============================
  &-thead {
    > tr {
      > th {
        position: relative;
        color: @table-header-color;
        font-weight: 500;
        text-align: left;
        background: @table-header-bg;
        border-bottom: @border-width-base @border-style-base @table-border-color;
        transition: background 0.3s ease;

        &[colspan]:not([colspan='1']) {
          text-align: center;
        }

        &:not(:last-child):not(.@{table-prefix-cls}-selection-column):not(.@{table-prefix-cls}-row-expand-icon-cell):not([colspan])::before {
          position: absolute;
          top: 50%;
          right: 0;
          width: 1px;
          height: 1.6em;
          background-color: @table-header-cell-split-color;
          transform: translateY(-50%);
          transition: background-color 0.3s;
          content: '';
        }
      }
    }

    > tr:not(:last-child) > th {
      &[colspan] {
        border-bottom: 0;
      }
    }
  }

  // ============================= Body =============================
  &-tbody {
    > tr {
      > td {
        border-bottom: @border-width-base @border-style-base @table-border-color;
        transition: background 0.3s;

        // ========================= Nest Table ===========================
        > .@{table-prefix-cls}-wrapper:only-child,
        > .@{table-prefix-cls}-expanded-row-fixed > .@{table-prefix-cls}-wrapper:only-child {
          .@{table-prefix-cls} {
            margin: -@table-padding-vertical -@table-padding-horizontal -@table-padding-vertical (@table-expand-column-width -
                  @table-padding-horizontal);

            &-tbody > tr:last-child > td {
              border-bottom: 0;

              &:first-child,
              &:last-child {
                border-radius: 0;
              }
            }
          }
        }
      }

      &.@{table-prefix-cls}-row:hover > td,
      > td.@{table-prefix-cls}-cell-row-hover {
        background: @table-row-hover-bg;
      }

      &.@{table-prefix-cls}-row-selected {
        > td {
          background: @table-selected-row-bg;
          border-color: rgba(0, 0, 0, 0.03);
        }

        &:hover {
          > td {
            background: @table-selected-row-hover-bg;
          }
        }
      }
    }
  }

  // =========================== Summary ============================
  &-summary {
    position: relative;
    z-index: @zindex-table-fixed;
    background: @table-bg;

    div& {
      box-shadow: 0 -@border-width-base 0 @table-border-color;
    }

    > tr {
      > th,
      > td {
        border-bottom: @border-width-base @border-style-base @table-border-color;
      }
    }
  }

  // ========================== Pagination ==========================
  &-pagination.@{ant-prefix}-pagination {
    margin: 16px 0;
  }

  &-pagination {
    display: flex;
    flex-wrap: wrap;
    row-gap: @padding-xs;

    > * {
      flex: none;
    }

    &-left {
      justify-content: flex-start;
    }

    &-center {
      justify-content: center;
    }

    &-right {
      justify-content: flex-end;
    }
  }

  // ================================================================
  // =                           Function                           =
  // ================================================================

  // ============================ Sorter ============================
  &-thead th.@{table-prefix-cls}-column-has-sorters {
    outline: none;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: @table-header-sort-active-bg;

      &::before {
        background-color: transparent !important;
      }
    }

    &:focus-visible {
      color: @primary-color;
    }

    // https://github.com/ant-design/ant-design/issues/30969
    &.@{table-prefix-cls}-cell-fix-left:hover,
    &.@{table-prefix-cls}-cell-fix-right:hover {
      background: @table-fixed-header-sort-active-bg;
    }
  }

  &-thead th.@{table-prefix-cls}-column-sort {
    background: @table-header-sort-bg;

    &::before {
      background-color: transparent !important;
    }
  }

  td&-column-sort {
    background: @table-body-sort-bg;
  }

  &-column-title {
    position: relative;
    z-index: 1;
    flex: 1;
  }

  &-column-sorters {
    display: flex;
    flex: auto;
    align-items: center;
    justify-content: space-between;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: '';
    }
  }

  &-column-sorter {
    margin-left: 4px;
    color: @table-header-icon-color;
    font-size: 0;
    transition: color 0.3s;

    &-inner {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
    }

    &-up,
    &-down {
      font-size: 11px;

      &.active {
        color: @primary-color;
      }
    }

    &-up + &-down {
      margin-top: -0.3em;
    }
  }

  &-column-sorters:hover &-column-sorter {
    color: darken(@table-header-icon-color, 10%);
  }

  // ============================ Filter ============================
  &-filter-column {
    display: flex;
    justify-content: space-between;
  }

  &-filter-trigger {
    position: relative;
    display: flex;
    align-items: center;
    margin: -4px (-@table-padding-horizontal / 2) -4px 4px;
    padding: 0 4px;
    color: @table-header-icon-color;
    font-size: @font-size-sm;
    border-radius: @border-radius-base;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: @text-color-secondary;
      background: @table-header-filter-active-bg;
    }

    &.active {
      color: @primary-color;
    }
  }

  // Dropdown
  &-filter-dropdown {
    .reset-component();

    min-width: 120px;
    background-color: @table-filter-dropdown-bg;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;

    // Reset menu
    .@{dropdown-prefix-cls}-menu {
      // https://github.com/ant-design/ant-design/issues/4916
      // https://github.com/ant-design/ant-design/issues/19542
      max-height: @table-filter-dropdown-max-height;
      overflow-x: hidden;
      border: 0;
      box-shadow: none;

      &:empty::after {
        display: block;
        padding: 8px 0;
        color: @disabled-color;
        font-size: @font-size-sm;
        text-align: center;
        content: 'Not Found';
      }
    }

    &-tree {
      padding: 8px 8px 0;

      .@{tree-prefix-cls}-treenode .@{tree-prefix-cls}-node-content-wrapper:hover {
        background-color: @tree-node-hover-bg;
      }

      .@{tree-prefix-cls}-treenode-checkbox-checked .@{tree-prefix-cls}-node-content-wrapper {
        &,
        &:hover {
          background-color: @tree-node-selected-bg;
        }
      }
    }

    &-search {
      padding: 8px;
      border-bottom: @border-width-base @border-color-split @border-style-base;

      &-input {
        input {
          min-width: 140px;
        }
        .@{iconfont-css-prefix} {
          color: @disabled-color;
        }
      }
    }

    &-checkall {
      width: 100%;
      margin-bottom: 4px;
      margin-left: 4px;
    }

    &-submenu > ul {
      max-height: calc(100vh - 130px);
      overflow-x: hidden;
      overflow-y: auto;
    }

    // Checkbox
    &,
    &-submenu {
      .@{ant-prefix}-checkbox-wrapper + span {
        padding-left: 8px;
      }
    }

    // Operation
    &-btns {
      display: flex;
      justify-content: space-between;
      padding: 7px 8px;
      overflow: hidden;
      background-color: @table-filter-btns-bg;
      border-top: @border-width-base @border-style-base @table-border-color;
    }
  }

  // ========================== Selections ==========================
  &-selection-col {
    width: @table-selection-column-width;
  }

  &-bordered &-selection-col {
    width: @table-selection-column-width + 18px;
  }

  table tr th&-selection-column,
  table tr td&-selection-column {
    padding-right: @padding-xs;
    padding-left: @padding-xs;
    text-align: center;

    .@{ant-prefix}-radio-wrapper {
      margin-right: 0;
    }
  }

  table tr th&-selection-column&-cell-fix-left {
    z-index: 3;
  }

  table tr th&-selection-column::after {
    background-color: transparent !important;
  }

  &-selection {
    position: relative;
    display: inline-flex;
    flex-direction: column;

    &-extra {
      position: absolute;
      top: 0;
      z-index: 1;
      cursor: pointer;
      transition: all 0.3s;
      margin-inline-start: 100%;
      padding-inline-start: (@table-padding-horizontal / 4);

      .@{iconfont-css-prefix} {
        color: @table-header-icon-color;
        font-size: 10px;

        &:hover {
          color: @table-header-icon-color-hover;
        }
      }
    }
  }

  // ========================== Expandable ==========================
  &-expand-icon-col {
    width: @table-expand-column-width;
  }

  &-row-expand-icon-cell {
    text-align: center;

    .@{table-prefix-cls}-row-expand-icon {
      display: inline-flex;
      float: none;
      vertical-align: sub;
    }
  }

  &-row-indent {
    float: left;
    height: 1px;
  }

  &-row-expand-icon {
    .operation-unit();
    position: relative;
    float: left;
    box-sizing: border-box;
    width: @expand-icon-size;
    height: @expand-icon-size;
    padding: 0;
    color: inherit;
    line-height: ceil(((@font-size-sm * 1.4 - @border-width-base * 3) / 2)) * 2 + @border-width-base *
      3;
    background: @table-expand-icon-bg;
    border: @border-width-base @border-style-base @table-border-color;
    border-radius: @border-radius-base;
    transform: scale((unit(@checkbox-size) / unit(@expand-icon-size)));
    transition: all 0.3s;
    user-select: none;
    @expand-icon-size: ceil(((@font-size-sm * 1.4 - @border-width-base * 3) / 2)) * 2 +
      @border-width-base * 3;

    &:focus,
    &:hover,
    &:active {
      border-color: currentcolor;
    }

    &::before,
    &::after {
      position: absolute;
      background: currentcolor;
      transition: transform 0.3s ease-out;
      content: '';
    }

    &::before {
      top: ceil(((@font-size-sm * 1.4 - @border-width-base * 3) / 2));
      right: 3px;
      left: 3px;
      height: @border-width-base;
    }

    &::after {
      top: 3px;
      bottom: 3px;
      left: ceil(((@font-size-sm * 1.4 - @border-width-base * 3) / 2));
      width: @border-width-base;
      transform: rotate(90deg);
    }

    // Motion effect
    &-collapsed::before {
      transform: rotate(-180deg);
    }

    &-collapsed::after {
      transform: rotate(0deg);
    }

    &-spaced {
      &::before,
      &::after {
        display: none;
        content: none;
      }
      background: transparent;
      border: 0;
      visibility: hidden;
    }

    .@{table-prefix-cls}-row-indent + & {
      margin-top: ((@font-size-base * @line-height-base - @border-width-base * 3) / 2) -
        ceil(((@font-size-sm * 1.4 - @border-width-base * 3) / 2));
      margin-right: @padding-xs;
    }
  }

  tr&-expanded-row {
    &,
    &:hover {
      > td {
        background: @table-expanded-row-bg;
      }
    }

    // https://github.com/ant-design/ant-design/issues/25573
    .@{descriptions-prefix-cls}-view {
      display: flex;

      table {
        flex: auto;
        width: auto;
      }
    }
  }

  // With fixed
  .@{table-prefix-cls}-expanded-row-fixed {
    position: relative;
    margin: -@table-padding-vertical -@table-padding-horizontal;
    padding: @table-padding-vertical @table-padding-horizontal;
  }

  // ========================= Placeholder ==========================
  &-tbody > tr&-placeholder {
    text-align: center;
    .@{table-prefix-cls}-empty & {
      color: @disabled-color;
    }

    &:hover {
      > td {
        background: @component-background;
      }
    }
  }

  // ============================ Fixed =============================
  &-cell-fix-left,
  &-cell-fix-right {
    position: sticky !important;
    z-index: @zindex-table-fixed;
    background: @table-bg;
  }

  &-cell-fix-left-first::after,
  &-cell-fix-left-last::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: -1px;
    width: 30px;
    transform: translateX(100%);
    transition: box-shadow 0.3s;
    content: '';
    pointer-events: none;
  }

  &-cell-fix-left-all::after {
    display: none;
  }

  &-cell-fix-right-first::after,
  &-cell-fix-right-last::after {
    position: absolute;
    top: 0;
    bottom: -1px;
    left: 0;
    width: 30px;
    transform: translateX(-100%);
    transition: box-shadow 0.3s;
    content: '';
    pointer-events: none;
  }

  .@{table-prefix-cls}-container {
    &::before,
    &::after {
      position: absolute;
      top: 0;
      bottom: 0;
      z-index: calc(@table-sticky-zindex + 1);
      width: 30px;
      transition: box-shadow 0.3s;
      content: '';
      pointer-events: none;
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }

  &-ping-left {
    &:not(.@{table-prefix-cls}-has-fix-left) > .@{table-prefix-cls}-container {
      position: relative;

      &::before {
        box-shadow: inset 10px 0 8px -8px darken(@shadow-color, 5%);
      }
    }

    .@{table-prefix-cls}-cell-fix-left-first::after,
    .@{table-prefix-cls}-cell-fix-left-last::after {
      box-shadow: inset 10px 0 8px -8px darken(@shadow-color, 5%);
    }

    .@{table-prefix-cls}-cell-fix-left-last::before {
      background-color: transparent !important;
    }
  }

  &-ping-right {
    &:not(.@{table-prefix-cls}-has-fix-right) > .@{table-prefix-cls}-container {
      position: relative;

      &::after {
        box-shadow: inset -10px 0 8px -8px darken(@shadow-color, 5%);
      }
    }

    .@{table-prefix-cls}-cell-fix-right-first::after,
    .@{table-prefix-cls}-cell-fix-right-last::after {
      box-shadow: inset -10px 0 8px -8px darken(@shadow-color, 5%);
    }
  }

  &-sticky {
    &-holder {
      position: sticky;
      z-index: @table-sticky-zindex;
      background: @component-background;
    }

    &-scroll {
      position: sticky;
      bottom: 0;
      z-index: @table-sticky-zindex;
      display: flex;
      align-items: center;
      background: lighten(@table-border-color, 80%);
      border-top: 1px solid @table-border-color;
      opacity: 0.6;

      &:hover {
        transform-origin: center bottom;
      }

      &-bar {
        height: 8px;
        background-color: @table-sticky-scroll-bar-bg;
        border-radius: @table-sticky-scroll-bar-radius;

        &:hover {
          background-color: @table-sticky-scroll-bar-active-bg;
        }

        &-active {
          background-color: @table-sticky-scroll-bar-active-bg;
        }
      }
    }
  }
}

@media all and (-ms-high-contrast: none) {
  .@{table-prefix-cls} {
    &-ping-left {
      .@{table-prefix-cls}-cell-fix-left-last::after {
        box-shadow: none !important;
      }
    }

    &-ping-right {
      .@{table-prefix-cls}-cell-fix-right-first::after {
        box-shadow: none !important;
      }
    }
  }
}

@import './radius';
@import './rtl';
