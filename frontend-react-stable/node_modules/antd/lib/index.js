"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Affix", {
  enumerable: true,
  get: function get() {
    return _affix["default"];
  }
});
Object.defineProperty(exports, "Alert", {
  enumerable: true,
  get: function get() {
    return _alert["default"];
  }
});
Object.defineProperty(exports, "Anchor", {
  enumerable: true,
  get: function get() {
    return _anchor["default"];
  }
});
Object.defineProperty(exports, "AutoComplete", {
  enumerable: true,
  get: function get() {
    return _autoComplete["default"];
  }
});
Object.defineProperty(exports, "Avatar", {
  enumerable: true,
  get: function get() {
    return _avatar["default"];
  }
});
Object.defineProperty(exports, "BackTop", {
  enumerable: true,
  get: function get() {
    return _backTop["default"];
  }
});
Object.defineProperty(exports, "Badge", {
  enumerable: true,
  get: function get() {
    return _badge["default"];
  }
});
Object.defineProperty(exports, "Breadcrumb", {
  enumerable: true,
  get: function get() {
    return _breadcrumb["default"];
  }
});
Object.defineProperty(exports, "Button", {
  enumerable: true,
  get: function get() {
    return _button["default"];
  }
});
Object.defineProperty(exports, "Calendar", {
  enumerable: true,
  get: function get() {
    return _calendar["default"];
  }
});
Object.defineProperty(exports, "Card", {
  enumerable: true,
  get: function get() {
    return _card["default"];
  }
});
Object.defineProperty(exports, "Carousel", {
  enumerable: true,
  get: function get() {
    return _carousel["default"];
  }
});
Object.defineProperty(exports, "Cascader", {
  enumerable: true,
  get: function get() {
    return _cascader["default"];
  }
});
Object.defineProperty(exports, "Checkbox", {
  enumerable: true,
  get: function get() {
    return _checkbox["default"];
  }
});
Object.defineProperty(exports, "Col", {
  enumerable: true,
  get: function get() {
    return _col["default"];
  }
});
Object.defineProperty(exports, "Collapse", {
  enumerable: true,
  get: function get() {
    return _collapse["default"];
  }
});
Object.defineProperty(exports, "Comment", {
  enumerable: true,
  get: function get() {
    return _comment["default"];
  }
});
Object.defineProperty(exports, "ConfigProvider", {
  enumerable: true,
  get: function get() {
    return _configProvider["default"];
  }
});
Object.defineProperty(exports, "DatePicker", {
  enumerable: true,
  get: function get() {
    return _datePicker["default"];
  }
});
Object.defineProperty(exports, "Descriptions", {
  enumerable: true,
  get: function get() {
    return _descriptions["default"];
  }
});
Object.defineProperty(exports, "Divider", {
  enumerable: true,
  get: function get() {
    return _divider["default"];
  }
});
Object.defineProperty(exports, "Drawer", {
  enumerable: true,
  get: function get() {
    return _drawer["default"];
  }
});
Object.defineProperty(exports, "Dropdown", {
  enumerable: true,
  get: function get() {
    return _dropdown["default"];
  }
});
Object.defineProperty(exports, "Empty", {
  enumerable: true,
  get: function get() {
    return _empty["default"];
  }
});
Object.defineProperty(exports, "Form", {
  enumerable: true,
  get: function get() {
    return _form["default"];
  }
});
Object.defineProperty(exports, "Grid", {
  enumerable: true,
  get: function get() {
    return _grid["default"];
  }
});
Object.defineProperty(exports, "Image", {
  enumerable: true,
  get: function get() {
    return _image["default"];
  }
});
Object.defineProperty(exports, "Input", {
  enumerable: true,
  get: function get() {
    return _input["default"];
  }
});
Object.defineProperty(exports, "InputNumber", {
  enumerable: true,
  get: function get() {
    return _inputNumber["default"];
  }
});
Object.defineProperty(exports, "Layout", {
  enumerable: true,
  get: function get() {
    return _layout["default"];
  }
});
Object.defineProperty(exports, "List", {
  enumerable: true,
  get: function get() {
    return _list["default"];
  }
});
Object.defineProperty(exports, "Mentions", {
  enumerable: true,
  get: function get() {
    return _mentions["default"];
  }
});
Object.defineProperty(exports, "Menu", {
  enumerable: true,
  get: function get() {
    return _menu["default"];
  }
});
Object.defineProperty(exports, "Modal", {
  enumerable: true,
  get: function get() {
    return _modal["default"];
  }
});
Object.defineProperty(exports, "PageHeader", {
  enumerable: true,
  get: function get() {
    return _pageHeader["default"];
  }
});
Object.defineProperty(exports, "Pagination", {
  enumerable: true,
  get: function get() {
    return _pagination["default"];
  }
});
Object.defineProperty(exports, "Popconfirm", {
  enumerable: true,
  get: function get() {
    return _popconfirm["default"];
  }
});
Object.defineProperty(exports, "Popover", {
  enumerable: true,
  get: function get() {
    return _popover["default"];
  }
});
Object.defineProperty(exports, "Progress", {
  enumerable: true,
  get: function get() {
    return _progress["default"];
  }
});
Object.defineProperty(exports, "Radio", {
  enumerable: true,
  get: function get() {
    return _radio["default"];
  }
});
Object.defineProperty(exports, "Rate", {
  enumerable: true,
  get: function get() {
    return _rate["default"];
  }
});
Object.defineProperty(exports, "Result", {
  enumerable: true,
  get: function get() {
    return _result["default"];
  }
});
Object.defineProperty(exports, "Row", {
  enumerable: true,
  get: function get() {
    return _row["default"];
  }
});
Object.defineProperty(exports, "Segmented", {
  enumerable: true,
  get: function get() {
    return _segmented["default"];
  }
});
Object.defineProperty(exports, "Select", {
  enumerable: true,
  get: function get() {
    return _select["default"];
  }
});
Object.defineProperty(exports, "Skeleton", {
  enumerable: true,
  get: function get() {
    return _skeleton["default"];
  }
});
Object.defineProperty(exports, "Slider", {
  enumerable: true,
  get: function get() {
    return _slider["default"];
  }
});
Object.defineProperty(exports, "Space", {
  enumerable: true,
  get: function get() {
    return _space["default"];
  }
});
Object.defineProperty(exports, "Spin", {
  enumerable: true,
  get: function get() {
    return _spin["default"];
  }
});
Object.defineProperty(exports, "Statistic", {
  enumerable: true,
  get: function get() {
    return _statistic["default"];
  }
});
Object.defineProperty(exports, "Steps", {
  enumerable: true,
  get: function get() {
    return _steps["default"];
  }
});
Object.defineProperty(exports, "Switch", {
  enumerable: true,
  get: function get() {
    return _switch["default"];
  }
});
Object.defineProperty(exports, "Table", {
  enumerable: true,
  get: function get() {
    return _table["default"];
  }
});
Object.defineProperty(exports, "Tabs", {
  enumerable: true,
  get: function get() {
    return _tabs["default"];
  }
});
Object.defineProperty(exports, "Tag", {
  enumerable: true,
  get: function get() {
    return _tag["default"];
  }
});
Object.defineProperty(exports, "TimePicker", {
  enumerable: true,
  get: function get() {
    return _timePicker["default"];
  }
});
Object.defineProperty(exports, "Timeline", {
  enumerable: true,
  get: function get() {
    return _timeline["default"];
  }
});
Object.defineProperty(exports, "Tooltip", {
  enumerable: true,
  get: function get() {
    return _tooltip["default"];
  }
});
Object.defineProperty(exports, "Transfer", {
  enumerable: true,
  get: function get() {
    return _transfer["default"];
  }
});
Object.defineProperty(exports, "Tree", {
  enumerable: true,
  get: function get() {
    return _tree["default"];
  }
});
Object.defineProperty(exports, "TreeSelect", {
  enumerable: true,
  get: function get() {
    return _treeSelect["default"];
  }
});
Object.defineProperty(exports, "Typography", {
  enumerable: true,
  get: function get() {
    return _typography["default"];
  }
});
Object.defineProperty(exports, "Upload", {
  enumerable: true,
  get: function get() {
    return _upload["default"];
  }
});
Object.defineProperty(exports, "message", {
  enumerable: true,
  get: function get() {
    return _message["default"];
  }
});
Object.defineProperty(exports, "notification", {
  enumerable: true,
  get: function get() {
    return _notification["default"];
  }
});
exports.theme = void 0;
Object.defineProperty(exports, "version", {
  enumerable: true,
  get: function get() {
    return _version["default"];
  }
});
var _affix = _interopRequireDefault(require("./affix"));
var _alert = _interopRequireDefault(require("./alert"));
var _anchor = _interopRequireDefault(require("./anchor"));
var _autoComplete = _interopRequireDefault(require("./auto-complete"));
var _avatar = _interopRequireDefault(require("./avatar"));
var _backTop = _interopRequireDefault(require("./back-top"));
var _badge = _interopRequireDefault(require("./badge"));
var _breadcrumb = _interopRequireDefault(require("./breadcrumb"));
var _button = _interopRequireDefault(require("./button"));
var _calendar = _interopRequireDefault(require("./calendar"));
var _card = _interopRequireDefault(require("./card"));
var _carousel = _interopRequireDefault(require("./carousel"));
var _cascader = _interopRequireDefault(require("./cascader"));
var _checkbox = _interopRequireDefault(require("./checkbox"));
var _col = _interopRequireDefault(require("./col"));
var _collapse = _interopRequireDefault(require("./collapse"));
var _comment = _interopRequireDefault(require("./comment"));
var _configProvider = _interopRequireDefault(require("./config-provider"));
var _datePicker = _interopRequireDefault(require("./date-picker"));
var _descriptions = _interopRequireDefault(require("./descriptions"));
var _divider = _interopRequireDefault(require("./divider"));
var _drawer = _interopRequireDefault(require("./drawer"));
var _dropdown = _interopRequireDefault(require("./dropdown"));
var _empty = _interopRequireDefault(require("./empty"));
var _form = _interopRequireDefault(require("./form"));
var _grid = _interopRequireDefault(require("./grid"));
var _image = _interopRequireDefault(require("./image"));
var _input = _interopRequireDefault(require("./input"));
var _inputNumber = _interopRequireDefault(require("./input-number"));
var _layout = _interopRequireDefault(require("./layout"));
var _list = _interopRequireDefault(require("./list"));
var _mentions = _interopRequireDefault(require("./mentions"));
var _menu = _interopRequireDefault(require("./menu"));
var _message = _interopRequireDefault(require("./message"));
var _modal = _interopRequireDefault(require("./modal"));
var _notification = _interopRequireDefault(require("./notification"));
var _pageHeader = _interopRequireDefault(require("./page-header"));
var _pagination = _interopRequireDefault(require("./pagination"));
var _popconfirm = _interopRequireDefault(require("./popconfirm"));
var _popover = _interopRequireDefault(require("./popover"));
var _progress = _interopRequireDefault(require("./progress"));
var _radio = _interopRequireDefault(require("./radio"));
var _rate = _interopRequireDefault(require("./rate"));
var _result = _interopRequireDefault(require("./result"));
var _row = _interopRequireDefault(require("./row"));
var _segmented = _interopRequireDefault(require("./segmented"));
var _select = _interopRequireDefault(require("./select"));
var _skeleton = _interopRequireDefault(require("./skeleton"));
var _slider = _interopRequireDefault(require("./slider"));
var _space = _interopRequireDefault(require("./space"));
var _spin = _interopRequireDefault(require("./spin"));
var _statistic = _interopRequireDefault(require("./statistic"));
var _steps = _interopRequireDefault(require("./steps"));
var _switch = _interopRequireDefault(require("./switch"));
var _table = _interopRequireDefault(require("./table"));
var _tabs = _interopRequireDefault(require("./tabs"));
var _tag = _interopRequireDefault(require("./tag"));
var _timePicker = _interopRequireDefault(require("./time-picker"));
var _timeline = _interopRequireDefault(require("./timeline"));
var _tooltip = _interopRequireDefault(require("./tooltip"));
var _transfer = _interopRequireDefault(require("./transfer"));
var _tree = _interopRequireDefault(require("./tree"));
var _treeSelect = _interopRequireDefault(require("./tree-select"));
var _typography = _interopRequireDefault(require("./typography"));
var _upload = _interopRequireDefault(require("./upload"));
var _version = _interopRequireDefault(require("./version"));
/* @remove-on-es-build-begin */
// this file is not used if use https://github.com/ant-design/babel-plugin-import
var ENV = process.env.NODE_ENV;
if (ENV !== 'production' && ENV !== 'test' && typeof console !== 'undefined' && console.warn &&
// eslint-disable-line no-console
typeof window !== 'undefined') {
  // eslint-disable-next-line no-console
  console.warn('You are using a whole package of antd, ' + 'please use https://www.npmjs.com/package/babel-plugin-import to reduce app bundle size.');
}
/* @remove-on-es-build-end */
// Fix vite build error
var theme = null;
exports.theme = theme;