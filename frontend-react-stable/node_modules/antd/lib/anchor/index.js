"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _Anchor = _interopRequireDefault(require("./Anchor"));
var _AnchorLink = _interopRequireDefault(require("./AnchorLink"));
var Anchor = _Anchor["default"];
Anchor.Link = _AnchorLink["default"];
var _default = Anchor;
exports["default"] = _default;