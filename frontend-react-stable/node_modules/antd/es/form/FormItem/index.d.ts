import type { FormInstance } from 'rc-field-form';
import type { FieldProps } from 'rc-field-form/lib/Field';
import * as React from 'react';
import useFormItemStatus from '../hooks/useFormItemStatus';
import type { FormItemInputProps } from '../FormItemInput';
import type { FormItemLabelProps, LabelTooltipType } from '../FormItemLabel';
declare const ValidateStatuses: ["success", "warning", "error", "validating", ""];
export declare type ValidateStatus = typeof ValidateStatuses[number];
declare type RenderChildren<Values = any> = (form: FormInstance<Values>) => React.ReactNode;
declare type RcFieldProps<Values = any> = Omit<FieldProps<Values>, 'children'>;
declare type ChildrenType<Values = any> = RenderChildren<Values> | React.ReactNode;
export interface FormItemProps<Values = any> extends FormItemLabelProps, FormItemInputProps, RcFieldProps<Values> {
    prefixCls?: string;
    noStyle?: boolean;
    style?: React.CSSProperties;
    className?: string;
    children?: ChildrenType<Values>;
    id?: string;
    hasFeedback?: boolean;
    validateStatus?: ValidateStatus;
    required?: boolean;
    hidden?: boolean;
    initialValue?: any;
    messageVariables?: Record<string, string>;
    tooltip?: LabelTooltipType;
    /** @deprecated No need anymore */
    fieldKey?: React.Key | React.Key[];
}
declare function InternalFormItem<Values = any>(props: FormItemProps<Values>): React.ReactElement;
declare type InternalFormItemType = typeof InternalFormItem;
interface FormItemInterface extends InternalFormItemType {
    useStatus: typeof useFormItemStatus;
}
declare const FormItem: FormItemInterface;
export default FormItem;
