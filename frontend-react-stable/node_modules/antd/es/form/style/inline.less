@import (reference) '../../style/themes/index';

@form-prefix-cls: ~'@{ant-prefix}-form';
@form-item-prefix-cls: ~'@{form-prefix-cls}-item';

.@{form-prefix-cls}-inline {
  display: flex;
  flex-wrap: wrap;

  .@{form-prefix-cls}-item {
    flex: none;
    flex-wrap: nowrap;
    margin-right: 16px;
    margin-bottom: 0;

    &-with-help {
      margin-bottom: @form-item-margin-bottom;
    }

    > .@{form-item-prefix-cls}-label,
    > .@{form-item-prefix-cls}-control {
      display: inline-block;
      vertical-align: top;
    }

    > .@{form-item-prefix-cls}-label {
      flex: none;
    }

    .@{form-prefix-cls}-text {
      display: inline-block;
    }

    .@{form-item-prefix-cls}-has-feedback {
      display: inline-block;
    }
  }
}
