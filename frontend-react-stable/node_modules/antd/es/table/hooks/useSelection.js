import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _toArray from "@babel/runtime/helpers/esm/toArray";
import _extends from "@babel/runtime/helpers/esm/extends";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _typeof from "@babel/runtime/helpers/esm/typeof";
import DownOutlined from "@ant-design/icons/es/icons/DownOutlined";
import { INTERNAL_COL_DEFINE } from 'rc-table';
import { arrAdd, arrDel } from "rc-tree/es/util";
import { conductCheck } from "rc-tree/es/utils/conductUtil";
import { convertDataToEntities } from "rc-tree/es/utils/treeUtil";
import useMergedState from "rc-util/es/hooks/useMergedState";
import * as React from 'react';
import { useCallback, useMemo, useState } from 'react';
import Checkbox from '../../checkbox';
import Dropdown from '../../dropdown';
import Radio from '../../radio';
import warning from '../../_util/warning';
// TODO: warning if use ajax!!!
export var SELECTION_COLUMN = {};
export var SELECTION_ALL = 'SELECT_ALL';
export var SELECTION_INVERT = 'SELECT_INVERT';
export var SELECTION_NONE = 'SELECT_NONE';
var EMPTY_LIST = [];
function flattenData(data, childrenColumnName) {
  var list = [];
  (data || []).forEach(function (record) {
    list.push(record);
    if (record && _typeof(record) === 'object' && childrenColumnName in record) {
      list = [].concat(_toConsumableArray(list), _toConsumableArray(flattenData(record[childrenColumnName], childrenColumnName)));
    }
  });
  return list;
}
export default function useSelection(rowSelection, config) {
  var _ref = rowSelection || {},
    preserveSelectedRowKeys = _ref.preserveSelectedRowKeys,
    selectedRowKeys = _ref.selectedRowKeys,
    defaultSelectedRowKeys = _ref.defaultSelectedRowKeys,
    getCheckboxProps = _ref.getCheckboxProps,
    onSelectionChange = _ref.onChange,
    onSelect = _ref.onSelect,
    onSelectAll = _ref.onSelectAll,
    onSelectInvert = _ref.onSelectInvert,
    onSelectNone = _ref.onSelectNone,
    onSelectMultiple = _ref.onSelectMultiple,
    selectionColWidth = _ref.columnWidth,
    selectionType = _ref.type,
    selections = _ref.selections,
    fixed = _ref.fixed,
    customizeRenderCell = _ref.renderCell,
    hideSelectAll = _ref.hideSelectAll,
    _ref$checkStrictly = _ref.checkStrictly,
    checkStrictly = _ref$checkStrictly === void 0 ? true : _ref$checkStrictly;
  var prefixCls = config.prefixCls,
    data = config.data,
    pageData = config.pageData,
    getRecordByKey = config.getRecordByKey,
    getRowKey = config.getRowKey,
    expandType = config.expandType,
    childrenColumnName = config.childrenColumnName,
    tableLocale = config.locale,
    getPopupContainer = config.getPopupContainer;
  // ========================= Keys =========================
  var _useMergedState = useMergedState(selectedRowKeys || defaultSelectedRowKeys || EMPTY_LIST, {
      value: selectedRowKeys
    }),
    _useMergedState2 = _slicedToArray(_useMergedState, 2),
    mergedSelectedKeys = _useMergedState2[0],
    setMergedSelectedKeys = _useMergedState2[1];
  // ======================== Caches ========================
  var preserveRecordsRef = React.useRef(new Map());
  var updatePreserveRecordsCache = useCallback(function (keys) {
    if (preserveSelectedRowKeys) {
      var newCache = new Map();
      // Keep key if mark as preserveSelectedRowKeys
      keys.forEach(function (key) {
        var record = getRecordByKey(key);
        if (!record && preserveRecordsRef.current.has(key)) {
          record = preserveRecordsRef.current.get(key);
        }
        newCache.set(key, record);
      });
      // Refresh to new cache
      preserveRecordsRef.current = newCache;
    }
  }, [getRecordByKey, preserveSelectedRowKeys]);
  // Update cache with selectedKeys
  React.useEffect(function () {
    updatePreserveRecordsCache(mergedSelectedKeys);
  }, [mergedSelectedKeys]);
  var _useMemo = useMemo(function () {
      return checkStrictly ? {
        keyEntities: null
      } : convertDataToEntities(data, {
        externalGetKey: getRowKey,
        childrenPropName: childrenColumnName
      });
    }, [data, getRowKey, checkStrictly, childrenColumnName]),
    keyEntities = _useMemo.keyEntities;
  // Get flatten data
  var flattedData = useMemo(function () {
    return flattenData(pageData, childrenColumnName);
  }, [pageData, childrenColumnName]);
  // Get all checkbox props
  var checkboxPropsMap = useMemo(function () {
    var map = new Map();
    flattedData.forEach(function (record, index) {
      var key = getRowKey(record, index);
      var checkboxProps = (getCheckboxProps ? getCheckboxProps(record) : null) || {};
      map.set(key, checkboxProps);
      process.env.NODE_ENV !== "production" ? warning(!('checked' in checkboxProps || 'defaultChecked' in checkboxProps), 'Table', 'Do not set `checked` or `defaultChecked` in `getCheckboxProps`. Please use `selectedRowKeys` instead.') : void 0;
    });
    return map;
  }, [flattedData, getRowKey, getCheckboxProps]);
  var isCheckboxDisabled = useCallback(function (r) {
    var _a;
    return !!((_a = checkboxPropsMap.get(getRowKey(r))) === null || _a === void 0 ? void 0 : _a.disabled);
  }, [checkboxPropsMap, getRowKey]);
  var _useMemo2 = useMemo(function () {
      if (checkStrictly) {
        return [mergedSelectedKeys || [], []];
      }
      var _conductCheck = conductCheck(mergedSelectedKeys, true, keyEntities, isCheckboxDisabled),
        checkedKeys = _conductCheck.checkedKeys,
        halfCheckedKeys = _conductCheck.halfCheckedKeys;
      return [checkedKeys || [], halfCheckedKeys];
    }, [mergedSelectedKeys, checkStrictly, keyEntities, isCheckboxDisabled]),
    _useMemo3 = _slicedToArray(_useMemo2, 2),
    derivedSelectedKeys = _useMemo3[0],
    derivedHalfSelectedKeys = _useMemo3[1];
  var derivedSelectedKeySet = useMemo(function () {
    var keys = selectionType === 'radio' ? derivedSelectedKeys.slice(0, 1) : derivedSelectedKeys;
    return new Set(keys);
  }, [derivedSelectedKeys, selectionType]);
  var derivedHalfSelectedKeySet = useMemo(function () {
    return selectionType === 'radio' ? new Set() : new Set(derivedHalfSelectedKeys);
  }, [derivedHalfSelectedKeys, selectionType]);
  // Save last selected key to enable range selection
  var _useState = useState(null),
    _useState2 = _slicedToArray(_useState, 2),
    lastSelectedKey = _useState2[0],
    setLastSelectedKey = _useState2[1];
  // Reset if rowSelection reset
  React.useEffect(function () {
    if (!rowSelection) {
      setMergedSelectedKeys(EMPTY_LIST);
    }
  }, [!!rowSelection]);
  var setSelectedKeys = useCallback(function (keys, method) {
    var availableKeys;
    var records;
    updatePreserveRecordsCache(keys);
    if (preserveSelectedRowKeys) {
      availableKeys = keys;
      records = keys.map(function (key) {
        return preserveRecordsRef.current.get(key);
      });
    } else {
      // Filter key which not exist in the `dataSource`
      availableKeys = [];
      records = [];
      keys.forEach(function (key) {
        var record = getRecordByKey(key);
        if (record !== undefined) {
          availableKeys.push(key);
          records.push(record);
        }
      });
    }
    setMergedSelectedKeys(availableKeys);
    onSelectionChange === null || onSelectionChange === void 0 ? void 0 : onSelectionChange(availableKeys, records, {
      type: method
    });
  }, [setMergedSelectedKeys, getRecordByKey, onSelectionChange, preserveSelectedRowKeys]);
  // ====================== Selections ======================
  // Trigger single `onSelect` event
  var triggerSingleSelection = useCallback(function (key, selected, keys, event) {
    if (onSelect) {
      var rows = keys.map(function (k) {
        return getRecordByKey(k);
      });
      onSelect(getRecordByKey(key), selected, rows, event);
    }
    setSelectedKeys(keys, 'single');
  }, [onSelect, getRecordByKey, setSelectedKeys]);
  var mergedSelections = useMemo(function () {
    if (!selections || hideSelectAll) {
      return null;
    }
    var selectionList = selections === true ? [SELECTION_ALL, SELECTION_INVERT, SELECTION_NONE] : selections;
    return selectionList.map(function (selection) {
      if (selection === SELECTION_ALL) {
        return {
          key: 'all',
          text: tableLocale.selectionAll,
          onSelect: function onSelect() {
            setSelectedKeys(data.map(function (record, index) {
              return getRowKey(record, index);
            }).filter(function (key) {
              var checkProps = checkboxPropsMap.get(key);
              return !(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled) || derivedSelectedKeySet.has(key);
            }), 'all');
          }
        };
      }
      if (selection === SELECTION_INVERT) {
        return {
          key: 'invert',
          text: tableLocale.selectInvert,
          onSelect: function onSelect() {
            var keySet = new Set(derivedSelectedKeySet);
            pageData.forEach(function (record, index) {
              var key = getRowKey(record, index);
              var checkProps = checkboxPropsMap.get(key);
              if (!(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled)) {
                if (keySet.has(key)) {
                  keySet["delete"](key);
                } else {
                  keySet.add(key);
                }
              }
            });
            var keys = Array.from(keySet);
            if (onSelectInvert) {
              process.env.NODE_ENV !== "production" ? warning(false, 'Table', '`onSelectInvert` will be removed in future. Please use `onChange` instead.') : void 0;
              onSelectInvert(keys);
            }
            setSelectedKeys(keys, 'invert');
          }
        };
      }
      if (selection === SELECTION_NONE) {
        return {
          key: 'none',
          text: tableLocale.selectNone,
          onSelect: function onSelect() {
            onSelectNone === null || onSelectNone === void 0 ? void 0 : onSelectNone();
            setSelectedKeys(Array.from(derivedSelectedKeySet).filter(function (key) {
              var checkProps = checkboxPropsMap.get(key);
              return checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled;
            }), 'none');
          }
        };
      }
      return selection;
    }).map(function (selection) {
      return _extends(_extends({}, selection), {
        onSelect: function onSelect() {
          var _a2;
          var _a;
          for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
            rest[_key] = arguments[_key];
          }
          (_a = selection.onSelect) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [selection].concat(rest));
          setLastSelectedKey(null);
        }
      });
    });
  }, [selections, derivedSelectedKeySet, pageData, getRowKey, onSelectInvert, setSelectedKeys]);
  // ======================= Columns ========================
  var transformColumns = useCallback(function (columns) {
    var _a;
    // >>>>>>>>>>> Skip if not exists `rowSelection`
    if (!rowSelection) {
      process.env.NODE_ENV !== "production" ? warning(!columns.includes(SELECTION_COLUMN), 'Table', '`rowSelection` is not config but `SELECTION_COLUMN` exists in the `columns`.') : void 0;
      return columns.filter(function (col) {
        return col !== SELECTION_COLUMN;
      });
    }
    // >>>>>>>>>>> Support selection
    var cloneColumns = _toConsumableArray(columns);
    var keySet = new Set(derivedSelectedKeySet);
    // Record key only need check with enabled
    var recordKeys = flattedData.map(getRowKey).filter(function (key) {
      return !checkboxPropsMap.get(key).disabled;
    });
    var checkedCurrentAll = recordKeys.every(function (key) {
      return keySet.has(key);
    });
    var checkedCurrentSome = recordKeys.some(function (key) {
      return keySet.has(key);
    });
    var onSelectAllChange = function onSelectAllChange() {
      var changeKeys = [];
      if (checkedCurrentAll) {
        recordKeys.forEach(function (key) {
          keySet["delete"](key);
          changeKeys.push(key);
        });
      } else {
        recordKeys.forEach(function (key) {
          if (!keySet.has(key)) {
            keySet.add(key);
            changeKeys.push(key);
          }
        });
      }
      var keys = Array.from(keySet);
      onSelectAll === null || onSelectAll === void 0 ? void 0 : onSelectAll(!checkedCurrentAll, keys.map(function (k) {
        return getRecordByKey(k);
      }), changeKeys.map(function (k) {
        return getRecordByKey(k);
      }));
      setSelectedKeys(keys, 'all');
      setLastSelectedKey(null);
    };
    // ===================== Render =====================
    // Title Cell
    var title;
    if (selectionType !== 'radio') {
      var customizeSelections;
      if (mergedSelections) {
        var menu = {
          getPopupContainer: getPopupContainer,
          items: mergedSelections.map(function (selection, index) {
            var key = selection.key,
              text = selection.text,
              onSelectionClick = selection.onSelect;
            return {
              key: key || index,
              onClick: function onClick() {
                onSelectionClick === null || onSelectionClick === void 0 ? void 0 : onSelectionClick(recordKeys);
              },
              label: text
            };
          })
        };
        customizeSelections = /*#__PURE__*/React.createElement("div", {
          className: "".concat(prefixCls, "-selection-extra")
        }, /*#__PURE__*/React.createElement(Dropdown, {
          menu: menu,
          getPopupContainer: getPopupContainer
        }, /*#__PURE__*/React.createElement("span", null, /*#__PURE__*/React.createElement(DownOutlined, null))));
      }
      var allDisabledData = flattedData.map(function (record, index) {
        var key = getRowKey(record, index);
        var checkboxProps = checkboxPropsMap.get(key) || {};
        return _extends({
          checked: keySet.has(key)
        }, checkboxProps);
      }).filter(function (_ref2) {
        var disabled = _ref2.disabled;
        return disabled;
      });
      var allDisabled = !!allDisabledData.length && allDisabledData.length === flattedData.length;
      var allDisabledAndChecked = allDisabled && allDisabledData.every(function (_ref3) {
        var checked = _ref3.checked;
        return checked;
      });
      var allDisabledSomeChecked = allDisabled && allDisabledData.some(function (_ref4) {
        var checked = _ref4.checked;
        return checked;
      });
      title = !hideSelectAll && /*#__PURE__*/React.createElement("div", {
        className: "".concat(prefixCls, "-selection")
      }, /*#__PURE__*/React.createElement(Checkbox, {
        checked: !allDisabled ? !!flattedData.length && checkedCurrentAll : allDisabledAndChecked,
        indeterminate: !allDisabled ? !checkedCurrentAll && checkedCurrentSome : !allDisabledAndChecked && allDisabledSomeChecked,
        onChange: onSelectAllChange,
        disabled: flattedData.length === 0 || allDisabled,
        skipGroup: true
      }), customizeSelections);
    }
    // Body Cell
    var renderCell;
    if (selectionType === 'radio') {
      renderCell = function renderCell(_, record, index) {
        var key = getRowKey(record, index);
        var checked = keySet.has(key);
        return {
          node: /*#__PURE__*/React.createElement(Radio, _extends({}, checkboxPropsMap.get(key), {
            checked: checked,
            onClick: function onClick(e) {
              return e.stopPropagation();
            },
            onChange: function onChange(event) {
              if (!keySet.has(key)) {
                triggerSingleSelection(key, true, [key], event.nativeEvent);
              }
            }
          })),
          checked: checked
        };
      };
    } else {
      renderCell = function renderCell(_, record, index) {
        var _a;
        var key = getRowKey(record, index);
        var checked = keySet.has(key);
        var indeterminate = derivedHalfSelectedKeySet.has(key);
        var checkboxProps = checkboxPropsMap.get(key);
        var mergedIndeterminate;
        if (expandType === 'nest') {
          mergedIndeterminate = indeterminate;
          process.env.NODE_ENV !== "production" ? warning(typeof (checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== 'boolean', 'Table', 'set `indeterminate` using `rowSelection.getCheckboxProps` is not allowed with tree structured dataSource.') : void 0;
        } else {
          mergedIndeterminate = (_a = checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== null && _a !== void 0 ? _a : indeterminate;
        }
        // Record checked
        return {
          node: /*#__PURE__*/React.createElement(Checkbox, _extends({}, checkboxProps, {
            indeterminate: mergedIndeterminate,
            checked: checked,
            skipGroup: true,
            onClick: function onClick(e) {
              return e.stopPropagation();
            },
            onChange: function onChange(_ref5) {
              var nativeEvent = _ref5.nativeEvent;
              var shiftKey = nativeEvent.shiftKey;
              var startIndex = -1;
              var endIndex = -1;
              // Get range of this
              if (shiftKey && checkStrictly) {
                var pointKeys = new Set([lastSelectedKey, key]);
                recordKeys.some(function (recordKey, recordIndex) {
                  if (pointKeys.has(recordKey)) {
                    if (startIndex === -1) {
                      startIndex = recordIndex;
                    } else {
                      endIndex = recordIndex;
                      return true;
                    }
                  }
                  return false;
                });
              }
              if (endIndex !== -1 && startIndex !== endIndex && checkStrictly) {
                // Batch update selections
                var rangeKeys = recordKeys.slice(startIndex, endIndex + 1);
                var changedKeys = [];
                if (checked) {
                  rangeKeys.forEach(function (recordKey) {
                    if (keySet.has(recordKey)) {
                      changedKeys.push(recordKey);
                      keySet["delete"](recordKey);
                    }
                  });
                } else {
                  rangeKeys.forEach(function (recordKey) {
                    if (!keySet.has(recordKey)) {
                      changedKeys.push(recordKey);
                      keySet.add(recordKey);
                    }
                  });
                }
                var keys = Array.from(keySet);
                onSelectMultiple === null || onSelectMultiple === void 0 ? void 0 : onSelectMultiple(!checked, keys.map(function (recordKey) {
                  return getRecordByKey(recordKey);
                }), changedKeys.map(function (recordKey) {
                  return getRecordByKey(recordKey);
                }));
                setSelectedKeys(keys, 'multiple');
              } else {
                // Single record selected
                var originCheckedKeys = derivedSelectedKeys;
                if (checkStrictly) {
                  var checkedKeys = checked ? arrDel(originCheckedKeys, key) : arrAdd(originCheckedKeys, key);
                  triggerSingleSelection(key, !checked, checkedKeys, nativeEvent);
                } else {
                  // Always fill first
                  var result = conductCheck([].concat(_toConsumableArray(originCheckedKeys), [key]), true, keyEntities, isCheckboxDisabled);
                  var _checkedKeys = result.checkedKeys,
                    halfCheckedKeys = result.halfCheckedKeys;
                  var nextCheckedKeys = _checkedKeys;
                  // If remove, we do it again to correction
                  if (checked) {
                    var tempKeySet = new Set(_checkedKeys);
                    tempKeySet["delete"](key);
                    nextCheckedKeys = conductCheck(Array.from(tempKeySet), {
                      checked: false,
                      halfCheckedKeys: halfCheckedKeys
                    }, keyEntities, isCheckboxDisabled).checkedKeys;
                  }
                  triggerSingleSelection(key, !checked, nextCheckedKeys, nativeEvent);
                }
              }
              if (checked) {
                setLastSelectedKey(null);
              } else {
                setLastSelectedKey(key);
              }
            }
          })),
          checked: checked
        };
      };
    }
    var renderSelectionCell = function renderSelectionCell(_, record, index) {
      var _renderCell = renderCell(_, record, index),
        node = _renderCell.node,
        checked = _renderCell.checked;
      if (customizeRenderCell) {
        return customizeRenderCell(checked, record, index, node);
      }
      return node;
    };
    // Insert selection column if not exist
    if (!cloneColumns.includes(SELECTION_COLUMN)) {
      // Always after expand icon
      if (cloneColumns.findIndex(function (col) {
        var _a;
        return ((_a = col[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN';
      }) === 0) {
        var _cloneColumns = cloneColumns,
          _cloneColumns2 = _toArray(_cloneColumns),
          expandColumn = _cloneColumns2[0],
          restColumns = _cloneColumns2.slice(1);
        cloneColumns = [expandColumn, SELECTION_COLUMN].concat(_toConsumableArray(restColumns));
      } else {
        // Normal insert at first column
        cloneColumns = [SELECTION_COLUMN].concat(_toConsumableArray(cloneColumns));
      }
    }
    // Deduplicate selection column
    var selectionColumnIndex = cloneColumns.indexOf(SELECTION_COLUMN);
    process.env.NODE_ENV !== "production" ? warning(cloneColumns.filter(function (col) {
      return col === SELECTION_COLUMN;
    }).length <= 1, 'Table', 'Multiple `SELECTION_COLUMN` exist in `columns`.') : void 0;
    cloneColumns = cloneColumns.filter(function (column, index) {
      return column !== SELECTION_COLUMN || index === selectionColumnIndex;
    });
    // Fixed column logic
    var prevCol = cloneColumns[selectionColumnIndex - 1];
    var nextCol = cloneColumns[selectionColumnIndex + 1];
    var mergedFixed = fixed;
    if (mergedFixed === undefined) {
      if ((nextCol === null || nextCol === void 0 ? void 0 : nextCol.fixed) !== undefined) {
        mergedFixed = nextCol.fixed;
      } else if ((prevCol === null || prevCol === void 0 ? void 0 : prevCol.fixed) !== undefined) {
        mergedFixed = prevCol.fixed;
      }
    }
    if (mergedFixed && prevCol && ((_a = prevCol[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN' && prevCol.fixed === undefined) {
      prevCol.fixed = mergedFixed;
    }
    // Replace with real selection column
    var selectionColumn = _defineProperty({
      fixed: mergedFixed,
      width: selectionColWidth,
      className: "".concat(prefixCls, "-selection-column"),
      title: rowSelection.columnTitle || title,
      render: renderSelectionCell
    }, INTERNAL_COL_DEFINE, {
      className: "".concat(prefixCls, "-selection-col")
    });
    return cloneColumns.map(function (col) {
      return col === SELECTION_COLUMN ? selectionColumn : col;
    });
  }, [getRowKey, flattedData, rowSelection, derivedSelectedKeys, derivedSelectedKeySet, derivedHalfSelectedKeySet, selectionColWidth, mergedSelections, expandType, lastSelectedKey, checkboxPropsMap, onSelectMultiple, triggerSingleSelection, isCheckboxDisabled]);
  return [transformColumns, derivedSelectedKeySet];
}