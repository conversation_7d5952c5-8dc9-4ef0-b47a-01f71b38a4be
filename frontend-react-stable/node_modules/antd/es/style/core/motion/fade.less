.fade-motion(@className, @keyframeName) {
  @name: ~'@{ant-prefix}-@{className}';
  .make-motion(@name, @keyframeName);
  .@{name}-enter,
  .@{name}-appear {
    opacity: 0;
    animation-timing-function: linear;
  }
  .@{name}-leave {
    animation-timing-function: linear;
  }
}

.fade-motion(fade, antFade);

@keyframes antFadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes antFadeOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
