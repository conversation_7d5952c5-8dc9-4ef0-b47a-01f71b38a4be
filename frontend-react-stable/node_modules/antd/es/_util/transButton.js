import _extends from "@babel/runtime/helpers/esm/extends";
var __rest = this && this.__rest || function (s, e) {
  var t = {};
  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
/**
 * Wrap of sub component which need use as Button capacity (like Icon component).
 *
 * This helps accessibility reader to tread as a interactive button to operation.
 */
import KeyCode from "rc-util/es/KeyCode";
import * as React from 'react';
var inlineStyle = {
  border: 0,
  background: 'transparent',
  padding: 0,
  lineHeight: 'inherit',
  display: 'inline-block'
};
var TransButton = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var onKeyDown = function onKeyDown(event) {
    var keyCode = event.keyCode;
    if (keyCode === KeyCode.ENTER) {
      event.preventDefault();
    }
  };
  var onKeyUp = function onKeyUp(event) {
    var keyCode = event.keyCode;
    var onClick = props.onClick;
    if (keyCode === KeyCode.ENTER && onClick) {
      onClick();
    }
  };
  var style = props.style,
    noStyle = props.noStyle,
    disabled = props.disabled,
    restProps = __rest(props, ["style", "noStyle", "disabled"]);
  var mergedStyle = {};
  if (!noStyle) {
    mergedStyle = _extends({}, inlineStyle);
  }
  if (disabled) {
    mergedStyle.pointerEvents = 'none';
  }
  mergedStyle = _extends(_extends({}, mergedStyle), style);
  return /*#__PURE__*/React.createElement("div", _extends({
    role: "button",
    tabIndex: 0,
    ref: ref
  }, restProps, {
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp,
    style: mergedStyle
  }));
});
export default TransButton;