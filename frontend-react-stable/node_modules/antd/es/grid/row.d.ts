import * as React from 'react';
import type { Breakpoint } from '../_util/responsiveObserve';
declare const RowAligns: ["top", "middle", "bottom", "stretch"];
declare const RowJustify: ["start", "end", "center", "space-around", "space-between", "space-evenly"];
declare type Responsive = 'xxl' | 'xl' | 'lg' | 'md' | 'sm' | 'xs';
declare type ResponsiveLike<T> = {
    [key in Responsive]?: T;
};
export declare type Gutter = number | undefined | Partial<Record<Breakpoint, number>>;
declare type ResponsiveAligns = ResponsiveLike<typeof RowAligns[number]>;
declare type ResponsiveJustify = ResponsiveLike<typeof RowJustify[number]>;
export interface RowProps extends React.HTMLAttributes<HTMLDivElement> {
    gutter?: Gutter | [Gutter, Gutter];
    align?: typeof RowAligns[number] | ResponsiveAligns;
    justify?: typeof RowJustify[number] | ResponsiveJustify;
    prefixCls?: string;
    wrap?: boolean;
}
declare const Row: React.ForwardRefExoticComponent<RowProps & React.RefAttributes<HTMLDivElement>>;
export default Row;
