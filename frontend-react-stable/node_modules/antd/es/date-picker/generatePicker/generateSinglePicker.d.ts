import type { GenerateConfig } from 'rc-picker/lib/generate/index';
import type { PickerDateProps, PickerProps, PickerTimeProps } from '.';
import type { PickerComponentClass } from './interface';
export default function generatePicker<DateType>(generateConfig: GenerateConfig<DateType>): {
    DatePicker: PickerComponentClass<PickerProps<DateType> & {
        status?: "" | "warning" | "error" | undefined;
        /**
         * @deprecated `dropdownClassName` is deprecated which will be removed in next major
         *   version.Please use `popupClassName` instead.
         */
        dropdownClassName?: string | undefined;
        popupClassName?: string | undefined;
    }, unknown>;
    WeekPicker: PickerComponentClass<Omit<PickerDateProps<DateType>, "picker">, unknown>;
    MonthPicker: PickerComponentClass<Omit<PickerDateProps<DateType>, "picker">, unknown>;
    YearPicker: PickerComponentClass<Omit<PickerDateProps<DateType>, "picker">, unknown>;
    TimePicker: PickerComponentClass<Omit<PickerTimeProps<DateType>, "picker">, unknown>;
    QuarterPicker: PickerComponentClass<Omit<PickerTimeProps<DateType>, "picker">, unknown>;
};
