import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/hu_HU";
import TimePickerLocale from '../../time-picker/locale/hu_HU';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON>lass<PERSON> dátumot',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> dátum', '<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>tum<PERSON>']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;