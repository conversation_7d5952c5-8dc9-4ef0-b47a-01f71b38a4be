import _extends from "@babel/runtime/helpers/esm/extends";
import TimePickerLocale from '../../time-picker/locale/sl_SI';
// Merge into a locale object
var locale = {
  lang: {
    locale: 'sl',
    placeholder: 'Izberite datum',
    rangePlaceholder: ['Zač<PERSON><PERSON> datum', 'Konč<PERSON> datum'],
    today: 'Dane<PERSON>',
    now: 'Trenutno',
    backToToday: 'Nazaj na trenutni datum',
    ok: 'OK',
    clear: 'Počisti',
    month: 'Mesec',
    year: 'Leto',
    timeSelect: 'Izberi čas',
    dateSelect: 'Izberi datum',
    monthSelect: 'Izberite mesec',
    yearSelect: 'Izberite leto',
    decadeSelect: 'Izberite desetletje',
    yearFormat: 'YYYY',
    dateFormat: 'D.M.YYYY',
    dayFormat: 'D',
    dateTimeFormat: 'D.M.YYYY HH:mm:ss',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    previousMonth: '<PERSON><PERSON><PERSON><PERSON> mesec (PageUp)',
    nextMonth: 'Na<PERSON><PERSON>ji mesec (PageDown)',
    previousYear: 'Lans<PERSON> leto (Control + left)',
    nextYear: 'Naslednje leto (Control + right)',
    previousDecade: 'Prejšnje desetletje',
    nextDecade: 'Naslednje desetletje',
    previousCentury: 'Zadnje stoletje',
    nextCentury: 'Naslednje stoletje'
  },
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;