import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/is_IS";
import TimePickerLocale from '../../time-picker/locale/is_IS';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON>u dag',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON>gu<PERSON>', 'Lokadagur']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;