import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/sv_SE";
import TimePickerLocale from '../../time-picker/locale/sv_SE';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: 'V<PERSON>lj datum',
    yearPlaceholder: 'Välj år',
    quarterPlaceholder: '<PERSON><PERSON><PERSON><PERSON> kvartal',
    monthPlaceholder: 'Välj månad',
    weekPlaceholder: 'V<PERSON>lj vecka',
    rangePlaceholder: ['Startdatum', 'Slutdatum'],
    rangeYearPlaceholder: ['Startår', 'Slutår'],
    rangeMonthPlaceholder: ['Startmånad', 'Slutmånad'],
    rangeWeekPlaceholder: ['Startvecka', 'Slutvecka']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;