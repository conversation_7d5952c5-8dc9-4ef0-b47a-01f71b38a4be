import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/tr_TR";
import TimePickerLocale from '../../time-picker/locale/tr_TR';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON><PERSON> seç',
    yearPlaceholder: 'Yıl seç',
    quarterPlaceholder: '<PERSON><PERSON><PERSON> seç',
    monthPlaceholder: 'Ay seç',
    weekPlaceholder: 'Hafta seç',
    rangePlaceholder: ['<PERSON>şlangıç tarihi', 'Biti<PERSON> tarihi'],
    rangeYearPlaceholder: ['Başlangıç yılı', 'Bitiş yılı'],
    rangeMonthPlaceholder: ['Başlangıç ayı', 'Bitiş ayı'],
    rangeWeekPlaceholder: ['<PERSON>şlangıç haftası', '<PERSON><PERSON><PERSON> haftası']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;