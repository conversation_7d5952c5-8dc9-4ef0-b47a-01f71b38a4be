import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/id_ID";
import TimePickerLocale from '../../time-picker/locale/id_ID';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON><PERSON> tanggal',
    rangePlaceholder: ['<PERSON><PERSON> tanggal', '<PERSON><PERSON> akhir']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;