import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/tk_TK";
import TimePickerLocale from '../../time-picker/locale/tk_TK';
var locale = {
  lang: _extends({
    placeholder: '<PERSON>agt saýla<PERSON>',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> wagty', '<PERSON><PERSON><PERSON><PERSON><PERSON> wagty'],
    yearPlaceholder: 'Ýyl saýlaň',
    quarterPlaceholder: 'Çärýek saýla<PERSON>',
    monthPlaceholder: '<PERSON>ý saýlaň',
    weekPlaceholder: '<PERSON><PERSON><PERSON> saýlaň',
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON>ý<PERSON> ýyly', '<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly'],
    rangeQuarterPlaceholder: ['Başlanýan çärýegi', '<PERSON><PERSON><PERSON><PERSON><PERSON> çärýegi'],
    rangeMonthPlaceholder: ['<PERSON>şlaný<PERSON> aýy', '<PERSON><PERSON><PERSON><PERSON><PERSON> aýy'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> hepdesi', '<PERSON><PERSON><PERSON><PERSON><PERSON> hep<PERSON>']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
export default locale;