import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/et_EE";
import TimePickerLocale from '../../time-picker/locale/et_EE';
// 统一合并为完整的 Locale
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON> kuup<PERSON>ev',
    rangePlaceholder: ['<PERSON><PERSON> kuupäev', '<PERSON><PERSON><PERSON> kuupäev']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;