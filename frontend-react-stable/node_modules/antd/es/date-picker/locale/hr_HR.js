import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/hr_HR";
import TimePickerLocale from '../../time-picker/locale/hr_HR';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: 'Odaberite datum',
    yearPlaceholder: 'Odaberite godinu',
    quarterPlaceholder: 'Odaberite četvrtinu',
    monthPlaceholder: 'Odaberite mjesec',
    weekPlaceholder: 'Odaberite tjedan',
    rangePlaceholder: ['Početni datum', 'Završni datum'],
    rangeYearPlaceholder: ['Početna godina', '<PERSON><PERSON><PERSON><PERSON><PERSON> godina'],
    rangeMonthPlaceholder: ['Početni mjesec', '<PERSON>av<PERSON><PERSON><PERSON> mjesec'],
    rangeWeekPlaceholder: ['Početni tjedan', 'Završni tjedan']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;