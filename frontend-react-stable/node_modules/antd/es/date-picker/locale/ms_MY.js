import _extends from "@babel/runtime/helpers/esm/extends";
import CalendarLocale from "rc-picker/es/locale/ms_MY";
import TimePickerLocale from '../../time-picker/locale/ms_MY';
// Merge into a locale object
var locale = {
  lang: _extends({
    placeholder: '<PERSON><PERSON><PERSON> tarikh',
    rangePlaceholder: ['<PERSON><PERSON>h mula', '<PERSON><PERSON>h akhir']
  }, CalendarLocale),
  timePickerLocale: _extends({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;