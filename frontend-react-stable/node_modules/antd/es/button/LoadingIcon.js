import LoadingOutlined from "@ant-design/icons/es/icons/LoadingOutlined";
import CSSMotion from 'rc-motion';
import React from 'react';
var getCollapsedWidth = function getCollapsedWidth() {
  return {
    width: 0,
    opacity: 0,
    transform: 'scale(0)'
  };
};
var getRealWidth = function getRealWidth(node) {
  return {
    width: node.scrollWidth,
    opacity: 1,
    transform: 'scale(1)'
  };
};
var LoadingIcon = function LoadingIcon(_ref) {
  var prefixCls = _ref.prefixCls,
    loading = _ref.loading,
    existIcon = _ref.existIcon;
  var visible = !!loading;
  if (existIcon) {
    return /*#__PURE__*/React.createElement("span", {
      className: "".concat(prefixCls, "-loading-icon")
    }, /*#__PURE__*/React.createElement(LoadingOutlined, null));
  }
  return /*#__PURE__*/React.createElement(CSSMotion, {
    visible: visible,
    // We do not really use this motionName
    motionName: "".concat(prefixCls, "-loading-icon-motion"),
    removeOnLeave: true,
    onAppearStart: getCollapsedWidth,
    onAppearActive: getRealWidth,
    onEnterStart: getCollapsedWidth,
    onEnterActive: getRealWidth,
    onLeaveStart: getRealWidth,
    onLeaveActive: getCollapsedWidth
  }, function (_ref2, ref) {
    var className = _ref2.className,
      style = _ref2.style;
    return /*#__PURE__*/React.createElement("span", {
      className: "".concat(prefixCls, "-loading-icon"),
      style: style,
      ref: ref
    }, /*#__PURE__*/React.createElement(LoadingOutlined, {
      className: className
    }));
  });
};
export default LoadingIcon;