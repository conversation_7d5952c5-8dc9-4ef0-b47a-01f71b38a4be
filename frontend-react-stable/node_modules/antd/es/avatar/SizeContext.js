import * as React from 'react';
var SizeContext = /*#__PURE__*/React.createContext('default');
export var SizeContextProvider = function SizeContextProvider(_ref) {
  var children = _ref.children,
    size = _ref.size;
  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {
    return /*#__PURE__*/React.createElement(SizeContext.Provider, {
      value: size || originSize
    }, children);
  });
};
export default SizeContext;