import SearchOutlined from "@ant-design/icons/es/icons/SearchOutlined";
import * as React from 'react';
import Input from '../input';
export default function Search(props) {
  var _props$placeholder = props.placeholder,
    placeholder = _props$placeholder === void 0 ? '' : _props$placeholder,
    value = props.value,
    prefixCls = props.prefixCls,
    disabled = props.disabled,
    onChange = props.onChange,
    handleClear = props.handleClear;
  var handleChange = React.useCallback(function (e) {
    onChange === null || onChange === void 0 ? void 0 : onChange(e);
    if (e.target.value === '') {
      handleClear === null || handleClear === void 0 ? void 0 : handleClear();
    }
  }, [onChange]);
  return /*#__PURE__*/React.createElement(Input, {
    placeholder: placeholder,
    className: prefixCls,
    value: value,
    onChange: handleChange,
    disabled: disabled,
    allowClear: true,
    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)
  });
}