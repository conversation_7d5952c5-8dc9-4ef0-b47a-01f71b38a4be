{"version": 3, "names": ["Plug<PERSON><PERSON><PERSON>", "constructor", "file", "key", "options", "isAsync", "_map", "Map", "opts", "cwd", "filename", "set", "val", "get", "availableHelper", "name", "versionRange", "addHelper", "buildCodeFrameError", "node", "msg", "_Error", "exports", "default", "prototype", "getModuleName", "addImport"], "sources": ["../../src/transformation/plugin-pass.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport type File from \"./file/file.ts\";\n\nexport default class PluginPass<Options = object> {\n  _map: Map<unknown, unknown> = new Map();\n  key: string | undefined | null;\n  file: File;\n  opts: Partial<Options>;\n\n  /**\n   * The working directory that <PERSON><PERSON>'s programmatic options are loaded\n   * relative to.\n   */\n  cwd: string;\n\n  /** The absolute path of the file being compiled. */\n  filename: string | void;\n\n  /**\n   * Is Babel executed in async mode or not.\n   */\n  isAsync: boolean;\n\n  constructor(\n    file: File,\n    key: string | null,\n    options: Options | undefined,\n    isAsync: boolean,\n  ) {\n    this.key = key;\n    this.file = file;\n    this.opts = options || {};\n    this.cwd = file.opts.cwd;\n    this.filename = file.opts.filename;\n    this.isAsync = isAsync;\n  }\n\n  set(key: unknown, val: unknown) {\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  availableHelper(name: string, versionRange?: string | null) {\n    return this.file.availableHelper(name, versionRange);\n  }\n\n  addHelper(name: string) {\n    return this.file.addHelper(name);\n  }\n\n  buildCodeFrameError(\n    node: t.Node | undefined | null,\n    msg: string,\n    _Error?: typeof Error,\n  ) {\n    return this.file.buildCodeFrameError(node, msg, _Error);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  (PluginPass as any).prototype.getModuleName = function getModuleName(\n    this: PluginPass,\n  ): string | undefined {\n    // @ts-expect-error only exists in Babel 7\n    return this.file.getModuleName();\n  };\n  (PluginPass as any).prototype.addImport = function addImport(\n    this: PluginPass,\n  ): void {\n    // @ts-expect-error only exists in Babel 7\n    this.file.addImport();\n  };\n}\n"], "mappings": ";;;;;;AAGe,MAAMA,UAAU,CAAmB;EAoBhDC,WAAWA,CACTC,IAAU,EACVC,GAAkB,EAClBC,OAA4B,EAC5BC,OAAgB,EAChB;IAAA,KAxBFC,IAAI,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCJ,GAAG;IAAA,KACHD,IAAI;IAAA,KACJM,IAAI;IAAA,KAMJC,GAAG;IAAA,KAGHC,QAAQ;IAAA,KAKRL,OAAO;IAQL,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACM,IAAI,GAAGJ,OAAO,IAAI,CAAC,CAAC;IACzB,IAAI,CAACK,GAAG,GAAGP,IAAI,CAACM,IAAI,CAACC,GAAG;IACxB,IAAI,CAACC,QAAQ,GAAGR,IAAI,CAACM,IAAI,CAACE,QAAQ;IAClC,IAAI,CAACL,OAAO,GAAGA,OAAO;EACxB;EAEAM,GAAGA,CAACR,GAAY,EAAES,GAAY,EAAE;IAC9B,IAAI,CAACN,IAAI,CAACK,GAAG,CAACR,GAAG,EAAES,GAAG,CAAC;EACzB;EAEAC,GAAGA,CAACV,GAAY,EAAO;IACrB,OAAO,IAAI,CAACG,IAAI,CAACO,GAAG,CAACV,GAAG,CAAC;EAC3B;EAEAW,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAE;IAC1D,OAAO,IAAI,CAACd,IAAI,CAACY,eAAe,CAACC,IAAI,EAAEC,YAAY,CAAC;EACtD;EAEAC,SAASA,CAACF,IAAY,EAAE;IACtB,OAAO,IAAI,CAACb,IAAI,CAACe,SAAS,CAACF,IAAI,CAAC;EAClC;EAEAG,mBAAmBA,CACjBC,IAA+B,EAC/BC,GAAW,EACXC,MAAqB,EACrB;IACA,OAAO,IAAI,CAACnB,IAAI,CAACgB,mBAAmB,CAACC,IAAI,EAAEC,GAAG,EAAEC,MAAM,CAAC;EACzD;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAvB,UAAA;AAEkC;EAChCA,UAAU,CAASwB,SAAS,CAACC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAE9C;IAEpB,OAAO,IAAI,CAACvB,IAAI,CAACuB,aAAa,CAAC,CAAC;EAClC,CAAC;EACAzB,UAAU,CAASwB,SAAS,CAACE,SAAS,GAAG,SAASA,SAASA,CAAA,EAEpD;IAEN,IAAI,CAACxB,IAAI,CAACwB,SAAS,CAAC,CAAC;EACvB,CAAC;AACH;AAAC", "ignoreList": []}