{"version": 3, "names": [], "sources": ["../../src/config/cache-contexts.ts"], "sourcesContent": ["import type { Targets } from \"@babel/helper-compilation-targets\";\n\nimport type { ConfigContext } from \"./config-chain.ts\";\nimport type { CallerMetadata } from \"./validation/options.ts\";\n\nexport type { ConfigContext as FullConfig };\n\nexport type FullPreset = {\n  targets: Targets;\n} & ConfigContext;\nexport type FullPlugin = {\n  assumptions: { [name: string]: boolean };\n} & FullPreset;\n\n// Context not including filename since it is used in places that cannot\n// process 'ignore'/'only' and other filename-based logic.\nexport type SimpleConfig = {\n  envName: string;\n  caller: CallerMetadata | undefined;\n};\nexport type SimplePreset = {\n  targets: Targets;\n} & SimpleConfig;\nexport type SimplePlugin = {\n  assumptions: {\n    [name: string]: boolean;\n  };\n} & SimplePreset;\n"], "mappings": "", "ignoreList": []}