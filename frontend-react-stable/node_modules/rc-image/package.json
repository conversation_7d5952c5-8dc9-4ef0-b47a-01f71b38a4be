{"name": "rc-image", "version": "5.9.0", "description": "React easy to use image component", "keywords": ["react", "react-component", "react-image", "image"], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "files": ["assets/*.css", "es", "lib"], "homepage": "http://github.com/react-component/image", "repository": {"type": "git", "url": "**************:react-component/image.git"}, "bugs": {"url": "http://github.com/react-component/image/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.11.2", "classnames": "^2.2.6", "rc-dialog": "~9.0.0", "rc-util": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@ant-design/icons": "^4.2.2", "@types/classnames": "^2.2.10", "@types/jest": "^26.0.9", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.2.2", "cross-env": "^7.0.2", "dumi": "^1.1.4", "enzyme": "^3.11.0", "eslint": "^7.6.0", "father": "^2.29.6", "glob": "^7.1.6", "less": "^3.12.2", "np": "^7.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.1.3"}}