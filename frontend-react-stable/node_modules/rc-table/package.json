{"name": "rc-table", "version": "7.26.0", "description": "table ui component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-table", "table", "component", "ui"], "files": ["assets/*.css", "dist", "es", "lib"], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "homepage": "http://github.com/react-component/table", "maintainers": ["<EMAIL>", "<EMAIL>"], "repository": {"type": "git", "url": "**************:react-component/table.git"}, "bugs": {"url": "http://github.com/react-component/table/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "father test", "coverage": "father test --coverage", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-resize-observer": "^1.1.0", "rc-util": "^5.22.5", "shallowequal": "^1.1.0"}, "devDependencies": {"@types/enzyme": "^3.10.5", "@types/jest": "^28.1.2", "@types/react": "^17.0.35", "@types/react-dom": "^18.0.5", "@types/shallowequal": "^1.1.1", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.9", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^2.22.0", "father-build": "^1.18.6", "gh-pages": "^3.1.0", "glob": "^7.1.6", "immutability-helper": "^3.0.0", "less": "^3.10.3", "np": "^7.0.0", "prettier": "^2.0.1", "rc-animate": "^3.0.0", "rc-dropdown": "~4.0.1", "rc-menu": "~9.6.0", "rc-tooltip": "^5.2.1", "react": "^16.0.0", "react-dnd": "^2.5.4", "react-dnd-html5-backend": "^2.5.4", "react-dom": "^16.0.0", "react-resizable": "^1.7.5", "react-test-renderer": "^16.0.0", "react-virtualized": "^9.12.0", "react-window": "^1.8.5", "regenerator-runtime": "^0.13.7", "styled-components": "^5.0.1", "typescript": "^4.0.3"}}