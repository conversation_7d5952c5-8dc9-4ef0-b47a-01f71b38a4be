{"name": "rc-trigger", "version": "5.3.4", "description": "base abstract trigger component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-trigger", "trigger"], "homepage": "https://github.com/react-component/trigger", "author": "", "repository": {"type": "git", "url": "https://github.com/react-component/trigger.git"}, "bugs": {"url": "https://github.com/react-component/trigger/issues"}, "files": ["es", "lib", "assets/**/*.css", "assets/**/*.less"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "coverage": "rc-test --coverage", "now-build": "npm run build"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@types/classnames": "^2.2.10", "@types/jest": "^26.0.15", "@types/react": "^16.8.19", "@types/react-dom": "^16.8.4", "cross-env": "^7.0.1", "dumi": "^1.1.38", "eslint": "^7.0.0", "father": "^4.0.0", "less": "^3.10.3", "np": "^6.2.0", "rc-test": "^7.0.13", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.13.7", "typescript": "^4.0.0", "@rc-component/father-plugin": "^1.0.0"}, "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.6", "rc-align": "^4.0.0", "rc-motion": "^2.0.0", "rc-util": "^5.19.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}