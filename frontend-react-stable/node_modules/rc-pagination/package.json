{"name": "rc-pagination", "version": "3.1.17", "description": "pagination ui component for react", "keywords": ["react", "react-component", "react-pagination", "pagination", "antd", "pager"], "main": "./lib/index", "module": "./es/index", "types": "rc-pagination.d.ts", "files": ["assets/*.css", "assets/*.less", "es", "lib", "dist", "rc-pagination.d.ts"], "homepage": "https://react-component.github.io/pagination", "repository": {"type": "git", "url": "**************:react-component/pagination.git"}, "bugs": {"url": "http://github.com/react-component/pagination/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "gh-pages": "npm run docs:build && npm run docs:deploy", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish && npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "pretty-quick": "pretty-quick", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.1"}, "devDependencies": {"@types/classnames": "^2.2.9", "@types/jest": "^27.4.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.0.3", "coveralls": "^3.0.6", "cross-env": "^7.0.0", "dumi": "^1.1.7", "enzyme": "^3.0.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.4.0", "eslint": "^7.0.0", "father": "^2.13.4", "gh-pages": "^3.1.0", "glob": "^7.1.6", "less": "^3.10.3", "np": "^7.0.0", "prettier": "^2.0.1", "pretty-quick": "^3.0.0", "rc-select": "^14.1.2", "react": "^16.0.0", "react-dom": "^16.0.0", "react-test-renderer": "^16.0.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}}