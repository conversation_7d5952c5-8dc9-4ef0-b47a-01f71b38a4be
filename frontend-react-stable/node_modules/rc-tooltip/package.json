{"name": "rc-tooltip", "version": "5.2.2", "description": "React <PERSON>", "keywords": ["react", "react-component", "react-tooltip", "tooltip"], "homepage": "http://github.com/react-component/tooltip", "bugs": {"url": "http://github.com/react-component/tooltip/issues"}, "repository": {"type": "git", "url": "**************:react-component/tooltip.git"}, "license": "MIT", "files": ["dist", "lib", "es", "assets/*.css", "assets/*.less"], "main": "lib/index", "module": "es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/bootstrap.less assets/bootstrap.css && lessc assets/bootstrap_white.less assets/bootstrap_white.css", "coverage": "father test --coverage", "lint": "eslint src/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "test": "father test"}, "dependencies": {"@babel/runtime": "^7.11.2", "classnames": "^2.3.1", "rc-trigger": "^5.0.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/react": "^17.0.15", "@types/react-dom": "^16.9.2", "@types/warning": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "enzyme-to-json": "^3.4.3", "eslint": "^7.1.0", "father": "^2.23.1", "father-build": "^1.18.6", "gh-pages": "^3.1.0", "less": "^3.11.1", "np": "^7.1.0", "react": "^16.10.2", "react-dom": "^16.10.2", "typescript": "^4.0.5"}, "maintainers": ["<EMAIL>"], "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}