"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Referencer = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */
const referencer_1 = __importDefault(require("eslint-scope/lib/referencer"));
const Referencer = referencer_1.default;
exports.Referencer = Referencer;
//# sourceMappingURL=Referencer.js.map