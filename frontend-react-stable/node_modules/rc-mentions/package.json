{"name": "rc-mentions", "version": "1.10.0", "description": "React Mentions", "keywords": ["react", "react-component", "react-mentions", "mentions"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "es", "dist", "lib"], "homepage": "http://github.com/react-component/mentions", "repository": {"type": "git", "url": "**************:react-component/mentions.git"}, "bugs": {"url": "http://github.com/react-component/mentions/issues"}, "license": "MIT", "scripts": {"start": "father doc dev --storybook", "build": "father doc build --storybook", "compile": "father build", "lint": "eslint src/ --ext .tsx,.ts", "test": "father test", "now-build": "npm run build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@types/classnames": "^2.2.6", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@types/warning": "^3.0.0", "@umijs/fabric": "^2.0.8", "eslint": "^7.0.0", "father": "^2.13.6", "jest": "^28.0.3", "lodash.debounce": "^4.0.8", "np": "^7.0.0", "prettier": "^2.0.5", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.0.3"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.6", "rc-menu": "~9.6.0", "rc-textarea": "^0.4.0", "rc-trigger": "^5.0.4", "rc-util": "^5.22.5"}}