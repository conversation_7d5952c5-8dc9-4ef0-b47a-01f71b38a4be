{"name": "rc-slider", "version": "10.0.1", "description": "Slider UI component for React", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-slider", "slider", "input", "range"], "homepage": "http://github.com/react-component/slider/", "repository": {"type": "git", "url": "**************:react-component/slider.git"}, "bugs": {"url": "http://github.com/react-component/slider/issues"}, "files": ["assets/*.css", "lib", "es", "dist"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "style": "./assets/index.css", "types": "./lib/index.d.ts", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.18.1", "shallowequal": "^1.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.3", "@types/classnames": "^2.2.9", "@types/jest": "^26.0.0", "@types/react": "^16.9.2", "@types/react-dom": "^16.9.0", "@types/shallowequal": "^1.1.1", "@umijs/fabric": "^2.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.7", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^2.22.0", "father-build": "^1.18.6", "gh-pages": "^3.1.0", "glob": "^7.1.6", "less": "^3.10.3", "np": "^7.0.0", "rc-tooltip": "^5.0.1", "rc-trigger": "^5.0.4", "react": "^16.0.0", "react-dom": "^16.0.0", "regenerator-runtime": "^0.13.9", "typescript": "^4.0.5"}}