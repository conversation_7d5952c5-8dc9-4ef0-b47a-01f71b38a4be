{"ast": null, "code": "var arrayEvery = require('./_arrayEvery'),\n  baseEvery = require('./_baseEvery'),\n  baseIteratee = require('./_baseIteratee'),\n  isArray = require('./isArray'),\n  isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Checks if `predicate` returns truthy for **all** elements of `collection`.\n * Iteration is stopped once `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index|key, collection).\n *\n * **Note:** This method returns `true` for\n * [empty collections](https://en.wikipedia.org/wiki/Empty_set) because\n * [everything is true](https://en.wikipedia.org/wiki/Vacuous_truth) of\n * elements of empty collections.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n * @example\n *\n * _.every([true, 1, null, 'yes'], <PERSON><PERSON><PERSON>);\n * // => false\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': false },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * // The `_.matches` iteratee shorthand.\n * _.every(users, { 'user': 'barney', 'active': false });\n * // => false\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.every(users, ['active', false]);\n * // => true\n *\n * // The `_.property` iteratee shorthand.\n * _.every(users, 'active');\n * // => false\n */\nfunction every(collection, predicate, guard) {\n  var func = isArray(collection) ? arrayEvery : baseEvery;\n  if (guard && isIterateeCall(collection, predicate, guard)) {\n    predicate = undefined;\n  }\n  return func(collection, baseIteratee(predicate, 3));\n}\nmodule.exports = every;", "map": {"version": 3, "names": ["arrayEvery", "require", "baseEvery", "baseIteratee", "isArray", "isIterateeCall", "every", "collection", "predicate", "guard", "func", "undefined", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/every.js"], "sourcesContent": ["var arrayEvery = require('./_arrayEvery'),\n    baseEvery = require('./_baseEvery'),\n    baseIteratee = require('./_baseIteratee'),\n    isArray = require('./isArray'),\n    isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Checks if `predicate` returns truthy for **all** elements of `collection`.\n * Iteration is stopped once `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index|key, collection).\n *\n * **Note:** This method returns `true` for\n * [empty collections](https://en.wikipedia.org/wiki/Empty_set) because\n * [everything is true](https://en.wikipedia.org/wiki/Vacuous_truth) of\n * elements of empty collections.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n * @example\n *\n * _.every([true, 1, null, 'yes'], <PERSON><PERSON><PERSON>);\n * // => false\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': false },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * // The `_.matches` iteratee shorthand.\n * _.every(users, { 'user': 'barney', 'active': false });\n * // => false\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.every(users, ['active', false]);\n * // => true\n *\n * // The `_.property` iteratee shorthand.\n * _.every(users, 'active');\n * // => false\n */\nfunction every(collection, predicate, guard) {\n  var func = isArray(collection) ? arrayEvery : baseEvery;\n  if (guard && isIterateeCall(collection, predicate, guard)) {\n    predicate = undefined;\n  }\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nmodule.exports = every;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EACzCG,OAAO,GAAGH,OAAO,CAAC,WAAW,CAAC;EAC9BI,cAAc,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,KAAKA,CAACC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC3C,IAAIC,IAAI,GAAGN,OAAO,CAACG,UAAU,CAAC,GAAGP,UAAU,GAAGE,SAAS;EACvD,IAAIO,KAAK,IAAIJ,cAAc,CAACE,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC,EAAE;IACzDD,SAAS,GAAGG,SAAS;EACvB;EACA,OAAOD,IAAI,CAACH,UAAU,EAAEJ,YAAY,CAACK,SAAS,EAAE,CAAC,CAAC,CAAC;AACrD;AAEAI,MAAM,CAACC,OAAO,GAAGP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}