{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\n// zombieJ: 🚨 Do not pass `defaultRenderEmpty` here since it will case circular dependency.\nexport var ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls\n});\nexport var ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\nexport function withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, _extends({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    if (process.env.NODE_ENV !== 'production') {\n      SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    }\n    return SFC;\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "defaultGetPrefixCls", "suffixCls", "customizePrefixCls", "concat", "ConfigContext", "createContext", "getPrefixCls", "ConfigConsumer", "Consumer", "withConfigConsumer", "config", "withConfigConsumerFunc", "Component", "SFC", "props", "createElement", "configProps", "basicPrefixCls", "prefixCls", "cons", "constructor", "name", "displayName", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/config-provider/context.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\n// zombieJ: 🚨 Do not pass `defaultRenderEmpty` here since it will case circular dependency.\nexport var ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls\n});\nexport var ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\nexport function withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, _extends({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    if (process.env.NODE_ENV !== 'production') {\n      SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    }\n    return SFC;\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,SAAS,EAAEC,kBAAkB,EAAE;EACpF,IAAIA,kBAAkB,EAAE,OAAOA,kBAAkB;EACjD,OAAOD,SAAS,GAAG,MAAM,CAACE,MAAM,CAACF,SAAS,CAAC,GAAG,KAAK;AACrD,CAAC;AACD;AACA,OAAO,IAAIG,aAAa,GAAG,aAAaL,KAAK,CAACM,aAAa,CAAC;EAC1D;EACAC,YAAY,EAAEN;AAChB,CAAC,CAAC;AACF,OAAO,IAAIO,cAAc,GAAGH,aAAa,CAACI,QAAQ;AAClD;AACA,OAAO,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,SAASC,sBAAsBA,CAACC,SAAS,EAAE;IAChD;IACA,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,KAAK,EAAE;MAC5B,OAAO,aAAaf,KAAK,CAACgB,aAAa,CAACR,cAAc,EAAE,IAAI,EAAE,UAAUS,WAAW,EAAE;QACnF,IAAIC,cAAc,GAAGP,MAAM,CAACQ,SAAS;QACrC,IAAIZ,YAAY,GAAGU,WAAW,CAACV,YAAY;QAC3C,IAAIJ,kBAAkB,GAAGY,KAAK,CAACI,SAAS;QACxC,IAAIA,SAAS,GAAGZ,YAAY,CAACW,cAAc,EAAEf,kBAAkB,CAAC;QAChE,OAAO,aAAaH,KAAK,CAACgB,aAAa,CAACH,SAAS,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,EAAEF,KAAK,EAAE;UAClFI,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC;IACD,IAAIC,IAAI,GAAGP,SAAS,CAACQ,WAAW;IAChC,IAAIC,IAAI,GAAGF,IAAI,IAAIA,IAAI,CAACG,WAAW,IAAIV,SAAS,CAACS,IAAI,IAAI,WAAW;IACpE,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCZ,GAAG,CAACS,WAAW,GAAG,qBAAqB,CAACnB,MAAM,CAACkB,IAAI,EAAE,GAAG,CAAC;IAC3D;IACA,OAAOR,GAAG;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}