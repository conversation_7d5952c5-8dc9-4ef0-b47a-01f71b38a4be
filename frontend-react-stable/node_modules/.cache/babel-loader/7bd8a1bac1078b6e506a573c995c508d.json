{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ascending;\nfunction ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ascending", "a", "b", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/ascending.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ascending;\n\nfunction ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,SAASA,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAOD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,GAAGC,GAAG,GAAGF,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAG,CAAC,GAAGC,GAAG;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}