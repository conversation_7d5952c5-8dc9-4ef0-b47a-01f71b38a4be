{"ast": null, "code": "import * as React from 'react';\nimport { ConfigConsumer } from '.';\nimport Empty from '../empty';\nvar defaultRenderEmpty = function defaultRenderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE\n        });\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n      /* istanbul ignore next */\n      default:\n        // Should never hit if we take all the component into consider.\n        return /*#__PURE__*/React.createElement(Empty, null);\n    }\n  });\n};\nexport default defaultRenderEmpty;", "map": {"version": 3, "names": ["React", "ConfigConsumer", "Empty", "defaultRenderEmpty", "componentName", "createElement", "_ref", "getPrefixCls", "prefix", "image", "PRESENTED_IMAGE_SIMPLE", "className", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/config-provider/defaultRenderEmpty.js"], "sourcesContent": ["import * as React from 'react';\nimport { ConfigConsumer } from '.';\nimport Empty from '../empty';\nvar defaultRenderEmpty = function defaultRenderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE\n        });\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n      /* istanbul ignore next */\n      default:\n        // Should never hit if we take all the component into consider.\n        return /*#__PURE__*/React.createElement(Empty, null);\n    }\n  });\n};\nexport default defaultRenderEmpty;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,GAAG;AAClC,OAAOC,KAAK,MAAM,UAAU;AAC5B,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,aAAa,EAAE;EAClE,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAACJ,cAAc,EAAE,IAAI,EAAE,UAAUK,IAAI,EAAE;IAC5E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIC,MAAM,GAAGD,YAAY,CAAC,OAAO,CAAC;IAClC,QAAQH,aAAa;MACnB,KAAK,OAAO;MACZ,KAAK,MAAM;QACT,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAACH,KAAK,EAAE;UAC7CO,KAAK,EAAEP,KAAK,CAACQ;QACf,CAAC,CAAC;MACJ,KAAK,QAAQ;MACb,KAAK,YAAY;MACjB,KAAK,UAAU;MACf,KAAK,UAAU;MACf,KAAK,UAAU;QACb,OAAO,aAAaV,KAAK,CAACK,aAAa,CAACH,KAAK,EAAE;UAC7CO,KAAK,EAAEP,KAAK,CAACQ,sBAAsB;UACnCC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,MAAM,EAAE,QAAQ;QACvC,CAAC,CAAC;MACJ;MACA;QACE;QACA,OAAO,aAAaR,KAAK,CAACK,aAAa,CAACH,KAAK,EAAE,IAAI,CAAC;IACxD;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}