{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { FormProvider as RcFormProvider } from 'rc-field-form';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nexport var FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexport var NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport var FormProvider = function FormProvider(props) {\n  var providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, _extends({}, providerProps));\n};\nexport var FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport var FormItemInputContext = /*#__PURE__*/React.createContext({});\nexport var NoFormStyle = function NoFormStyle(_ref) {\n  var children = _ref.children,\n    status = _ref.status,\n    override = _ref.override;\n  var formItemInputContext = useContext(FormItemInputContext);\n  var newFormItemInputContext = useMemo(function () {\n    var newContext = _extends({}, formItemInputContext);\n    if (override) {\n      delete newContext.isFormItemInput;\n    }\n    if (status) {\n      delete newContext.status;\n      delete newContext.hasFeedback;\n      delete newContext.feedbackIcon;\n    }\n    return newContext;\n  }, [status, override, formItemInputContext]);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: newFormItemInputContext\n  }, children);\n};", "map": {"version": 3, "names": ["_extends", "FormProvider", "RcFormProvider", "omit", "React", "useContext", "useMemo", "FormContext", "createContext", "labelAlign", "vertical", "itemRef", "NoStyleItemContext", "props", "providerProps", "createElement", "FormItemPrefixContext", "prefixCls", "FormItemInputContext", "NoFormStyle", "_ref", "children", "status", "override", "formItemInputContext", "newFormItemInputContext", "newContext", "isFormItemInput", "hasFeedback", "feedbackIcon", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/context.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { FormProvider as RcFormProvider } from 'rc-field-form';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nexport var FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexport var NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport var FormProvider = function FormProvider(props) {\n  var providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, _extends({}, providerProps));\n};\nexport var FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport var FormItemInputContext = /*#__PURE__*/React.createContext({});\nexport var NoFormStyle = function NoFormStyle(_ref) {\n  var children = _ref.children,\n    status = _ref.status,\n    override = _ref.override;\n  var formItemInputContext = useContext(FormItemInputContext);\n  var newFormItemInputContext = useMemo(function () {\n    var newContext = _extends({}, formItemInputContext);\n    if (override) {\n      delete newContext.isFormItemInput;\n    }\n    if (status) {\n      delete newContext.status;\n      delete newContext.hasFeedback;\n      delete newContext.feedbackIcon;\n    }\n    return newContext;\n  }, [status, override, formItemInputContext]);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: newFormItemInputContext\n  }, children);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,YAAY,IAAIC,cAAc,QAAQ,eAAe;AAC9D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAO,IAAIC,WAAW,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC;EACxDC,UAAU,EAAE,OAAO;EACnBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;AAC/B,CAAC,CAAC;AACF,OAAO,IAAIC,kBAAkB,GAAG,aAAaR,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AACtE,OAAO,IAAIP,YAAY,GAAG,SAASA,YAAYA,CAACY,KAAK,EAAE;EACrD,IAAIC,aAAa,GAAGX,IAAI,CAACU,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC9C,OAAO,aAAaT,KAAK,CAACW,aAAa,CAACb,cAAc,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEc,aAAa,CAAC,CAAC;AACtF,CAAC;AACD,OAAO,IAAIE,qBAAqB,GAAG,aAAaZ,KAAK,CAACI,aAAa,CAAC;EAClES,SAAS,EAAE;AACb,CAAC,CAAC;AACF,OAAO,IAAIC,oBAAoB,GAAG,aAAad,KAAK,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC;AACtE,OAAO,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAClD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC1B,IAAIC,oBAAoB,GAAGnB,UAAU,CAACa,oBAAoB,CAAC;EAC3D,IAAIO,uBAAuB,GAAGnB,OAAO,CAAC,YAAY;IAChD,IAAIoB,UAAU,GAAG1B,QAAQ,CAAC,CAAC,CAAC,EAAEwB,oBAAoB,CAAC;IACnD,IAAID,QAAQ,EAAE;MACZ,OAAOG,UAAU,CAACC,eAAe;IACnC;IACA,IAAIL,MAAM,EAAE;MACV,OAAOI,UAAU,CAACJ,MAAM;MACxB,OAAOI,UAAU,CAACE,WAAW;MAC7B,OAAOF,UAAU,CAACG,YAAY;IAChC;IACA,OAAOH,UAAU;EACnB,CAAC,EAAE,CAACJ,MAAM,EAAEC,QAAQ,EAAEC,oBAAoB,CAAC,CAAC;EAC5C,OAAO,aAAapB,KAAK,CAACW,aAAa,CAACG,oBAAoB,CAACY,QAAQ,EAAE;IACrEC,KAAK,EAAEN;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}