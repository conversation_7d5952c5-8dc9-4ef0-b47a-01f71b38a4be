{"ast": null, "code": "import _isEqual from \"lodash/isEqual\";\nimport _sortBy from \"lodash/sortBy\";\nimport _upperFirst from \"lodash/upperFirst\";\nimport _isString from \"lodash/isString\";\nimport _isNaN from \"lodash/isNaN\";\nimport _isArray from \"lodash/isArray\";\nimport _max from \"lodash/max\";\nimport _min from \"lodash/min\";\nimport _flatMap from \"lodash/flatMap\";\nimport _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport { getNiceTickValues, getTickValuesFixedDomain } from 'recharts-scale';\nimport { ErrorBar } from '../cartesian/ErrorBar';\nimport { Legend } from '../component/Legend';\nimport { findEntryInArray, getPercentValue, isNumber, isNumOrStr, mathSign, uniqueId } from './DataUtils';\nimport { filterProps, findAllByType, findChildByType, getDisplayName } from './ReactUtils';\n// TODO: Cause of circular dependency. Needs refactor.\n// import { RadiusAxisProps, AngleAxisProps } from '../polar/types';\n\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (_isNil(obj) || _isNil(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return _get(obj, dataKey, defaultValue);\n  }\n  if (_isFunction(dataKey)) {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\n/**\n * Get domain of data by key\n * @param  {Array}   data      The data displayed in the chart\n * @param  {String}  key       The unique key of a group of data\n * @param  {String}  type      The type of axis\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array} Domain of data\n */\nexport function getDomainOfDataByKey(data, key, type, filterNil) {\n  var flattenData = _flatMap(data, function (entry) {\n    return getValueByDataKey(entry, key);\n  });\n  if (type === 'number') {\n    var domain = flattenData.filter(function (entry) {\n      return isNumber(entry) || parseFloat(entry);\n    });\n    return domain.length ? [_min(domain), _max(domain)] : [Infinity, -Infinity];\n  }\n  var validateData = filterNil ? flattenData.filter(function (entry) {\n    return !_isNil(entry);\n  }) : flattenData;\n\n  // 支持Date类型的x轴\n  return validateData.map(function (entry) {\n    return isNumOrStr(entry) || entry instanceof Date ? entry : '';\n  });\n}\nexport var calculateActiveTickIndex = function calculateActiveTickIndex(coordinate) {\n  var _ticks$length;\n  var ticks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var unsortedTicks = arguments.length > 2 ? arguments[2] : undefined;\n  var axis = arguments.length > 3 ? arguments[3] : undefined;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or less ticks ticks then the active tick is at index 0\n  if (len <= 1) {\n    return 0;\n  }\n  if (axis && axis.axisType === 'angleAxis' && Math.abs(Math.abs(axis.range[1] - axis.range[0]) - 360) <= 1e-6) {\n    var range = axis.range;\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      } else {\n        var min = Math.min(before, after);\n        var max = Math.max(before, after);\n        if (coordinate > (min + cur) / 2 && coordinate <= (max + cur) / 2) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      }\n    }\n  } else {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        index = ticks[_i].index;\n        break;\n      }\n    }\n  }\n  return index;\n};\n\n/**\n * Get the main color of each graphic item\n * @param  {ReactElement} item A graphic item\n * @return {String}            Color\n */\nexport var getMainColorOfGraphicItem = function getMainColorOfGraphicItem(item) {\n  var _ref = item,\n    displayName = _ref.type.displayName; // TODO: check if displayName is valid.\n  var _item$props = item.props,\n    stroke = _item$props.stroke,\n    fill = _item$props.fill;\n  var result;\n  switch (displayName) {\n    case 'Line':\n      result = stroke;\n      break;\n    case 'Area':\n    case 'Radar':\n      result = stroke && stroke !== 'none' ? stroke : fill;\n      break;\n    default:\n      result = fill;\n      break;\n  }\n  return result;\n};\nexport var getLegendProps = function getLegendProps(_ref2) {\n  var children = _ref2.children,\n    formattedGraphicalItems = _ref2.formattedGraphicalItems,\n    legendWidth = _ref2.legendWidth,\n    legendContent = _ref2.legendContent;\n  var legendItem = findChildByType(children, Legend);\n  if (!legendItem) {\n    return null;\n  }\n  var legendData;\n  if (legendItem.props && legendItem.props.payload) {\n    legendData = legendItem.props && legendItem.props.payload;\n  } else if (legendContent === 'children') {\n    legendData = (formattedGraphicalItems || []).reduce(function (result, _ref3) {\n      var item = _ref3.item,\n        props = _ref3.props;\n      var data = props.sectors || props.data || [];\n      return result.concat(data.map(function (entry) {\n        return {\n          type: legendItem.props.iconType || item.props.legendType,\n          value: entry.name,\n          color: entry.fill,\n          payload: entry\n        };\n      }));\n    }, []);\n  } else {\n    legendData = (formattedGraphicalItems || []).map(function (_ref4) {\n      var item = _ref4.item;\n      var _item$props2 = item.props,\n        dataKey = _item$props2.dataKey,\n        name = _item$props2.name,\n        legendType = _item$props2.legendType,\n        hide = _item$props2.hide;\n      return {\n        inactive: hide,\n        dataKey: dataKey,\n        type: legendItem.props.iconType || legendType || 'square',\n        color: getMainColorOfGraphicItem(item),\n        value: name || dataKey,\n        payload: item.props\n      };\n    });\n  }\n  return _objectSpread(_objectSpread(_objectSpread({}, legendItem.props), Legend.getWithHeight(legendItem, legendWidth)), {}, {\n    payload: legendData,\n    item: legendItem\n  });\n};\n/**\n * Calculate the size of all groups for stacked bar graph\n * @param  {Object} stackGroups The items grouped by axisId and stackId\n * @return {Object} The size of all groups\n */\nexport var getBarSizeList = function getBarSizeList(_ref5) {\n  var globalSize = _ref5.barSize,\n    _ref5$stackGroups = _ref5.stackGroups,\n    stackGroups = _ref5$stackGroups === void 0 ? {} : _ref5$stackGroups;\n  if (!stackGroups) {\n    return {};\n  }\n  var result = {};\n  var numericAxisIds = Object.keys(stackGroups);\n  for (var i = 0, len = numericAxisIds.length; i < len; i++) {\n    var sgs = stackGroups[numericAxisIds[i]].stackGroups;\n    var stackIds = Object.keys(sgs);\n    for (var j = 0, sLen = stackIds.length; j < sLen; j++) {\n      var _sgs$stackIds$j = sgs[stackIds[j]],\n        items = _sgs$stackIds$j.items,\n        cateAxisId = _sgs$stackIds$j.cateAxisId;\n      var barItems = items.filter(function (item) {\n        return getDisplayName(item.type).indexOf('Bar') >= 0;\n      });\n      if (barItems && barItems.length) {\n        var selfSize = barItems[0].props.barSize;\n        var cateId = barItems[0].props[cateAxisId];\n        if (!result[cateId]) {\n          result[cateId] = [];\n        }\n        result[cateId].push({\n          item: barItems[0],\n          stackList: barItems.slice(1),\n          barSize: _isNil(selfSize) ? globalSize : selfSize\n        });\n      }\n    }\n  }\n  return result;\n};\n\n/**\n * Calculate the size of each bar and the gap between two bars\n * @param  {Number} bandSize  The size of each category\n * @param  {sizeList} sizeList  The size of all groups\n * @param  {maxBarSize} maxBarSize The maximum size of bar\n * @return {Number} The size of each bar and the gap between two bars\n */\nexport var getBarPosition = function getBarPosition(_ref6) {\n  var barGap = _ref6.barGap,\n    barCategoryGap = _ref6.barCategoryGap,\n    bandSize = _ref6.bandSize,\n    _ref6$sizeList = _ref6.sizeList,\n    sizeList = _ref6$sizeList === void 0 ? [] : _ref6$sizeList,\n    maxBarSize = _ref6.maxBarSize;\n  var len = sizeList.length;\n  if (len < 1) return null;\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n\n  // whether or not is barSize setted by user\n  if (sizeList[0].barSize === +sizeList[0].barSize) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce(function (res, entry) {\n      return res + entry.barSize || 0;\n    }, 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce(function (res, entry) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : entry.barSize\n        }\n      }]);\n      prev = newRes[newRes.length - 1].position;\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: prev\n          });\n        });\n      }\n      return newRes;\n    }, []);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = maxBarSize === +maxBarSize ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce(function (res, entry, i) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n          size: size\n        }\n      }]);\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: newRes[newRes.length - 1].position\n          });\n        });\n      }\n      return newRes;\n    }, []);\n  }\n  return result;\n};\nexport var appendOffsetOfLegend = function appendOffsetOfLegend(offset, items, props, legendBox) {\n  var children = props.children,\n    width = props.width,\n    margin = props.margin;\n  var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n  // const legendHeight = height - (margin.top || 0) - (margin.bottom || 0);\n  var legendProps = getLegendProps({\n    children: children,\n    legendWidth: legendWidth\n  });\n  var newOffset = offset;\n  if (legendProps) {\n    var box = legendBox || {};\n    var align = legendProps.align,\n      verticalAlign = legendProps.verticalAlign,\n      layout = legendProps.layout;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && isNumber(offset[align])) {\n      newOffset = _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, align, newOffset[align] + (box.width || 0)));\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && isNumber(offset[verticalAlign])) {\n      newOffset = _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, verticalAlign, newOffset[verticalAlign] + (box.height || 0)));\n    }\n  }\n  return newOffset;\n};\nvar isErrorBarRelevantForAxis = function isErrorBarRelevantForAxis(layout, axisType, direction) {\n  if (_isNil(axisType)) {\n    return true;\n  }\n  if (layout === 'horizontal') {\n    return axisType === 'yAxis';\n  }\n  if (layout === 'vertical') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'x') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'y') {\n    return axisType === 'yAxis';\n  }\n  return true;\n};\nexport var getDomainOfErrorBars = function getDomainOfErrorBars(data, item, dataKey, layout, axisType) {\n  var children = item.props.children;\n  var errorBars = findAllByType(children, ErrorBar).filter(function (errorBarChild) {\n    return isErrorBarRelevantForAxis(layout, axisType, errorBarChild.props.direction);\n  });\n  if (errorBars && errorBars.length) {\n    var keys = errorBars.map(function (errorBarChild) {\n      return errorBarChild.props.dataKey;\n    });\n    return data.reduce(function (result, entry) {\n      var entryValue = getValueByDataKey(entry, dataKey, 0);\n      var mainValue = _isArray(entryValue) ? [_min(entryValue), _max(entryValue)] : [entryValue, entryValue];\n      var errorDomain = keys.reduce(function (prevErrorArr, k) {\n        var errorValue = getValueByDataKey(entry, k, 0);\n        var lowerValue = mainValue[0] - Math.abs(_isArray(errorValue) ? errorValue[0] : errorValue);\n        var upperValue = mainValue[1] + Math.abs(_isArray(errorValue) ? errorValue[1] : errorValue);\n        return [Math.min(lowerValue, prevErrorArr[0]), Math.max(upperValue, prevErrorArr[1])];\n      }, [Infinity, -Infinity]);\n      return [Math.min(errorDomain[0], result[0]), Math.max(errorDomain[1], result[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\nexport var parseErrorBarsOfAxis = function parseErrorBarsOfAxis(data, items, dataKey, axisType, layout) {\n  var domains = items.map(function (item) {\n    return getDomainOfErrorBars(data, item, dataKey, layout, axisType);\n  }).filter(function (entry) {\n    return !_isNil(entry);\n  });\n  if (domains && domains.length) {\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\n\n/**\n * Get domain of data by the configuration of item element\n * @param  {Array}   data      The data displayed in the chart\n * @param  {Array}   items     The instances of item\n * @param  {String}  type      The type of axis, number - Number Axis, category - Category Axis\n * @param  {LayoutType} layout The type of layout\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array}        Domain\n */\nexport var getDomainOfItemsWithSameAxis = function getDomainOfItemsWithSameAxis(data, items, type, layout, filterNil) {\n  var domains = items.map(function (item) {\n    var dataKey = item.props.dataKey;\n    if (type === 'number' && dataKey) {\n      return getDomainOfErrorBars(data, item, dataKey, layout) || getDomainOfDataByKey(data, dataKey, type, filterNil);\n    }\n    return getDomainOfDataByKey(data, dataKey, type, filterNil);\n  });\n  if (type === 'number') {\n    // Calculate the domain of number axis\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  var tag = {};\n  // Get the union set of category axis\n  return domains.reduce(function (result, entry) {\n    for (var i = 0, len = entry.length; i < len; i++) {\n      if (!tag[entry[i]]) {\n        tag[entry[i]] = true;\n        result.push(entry[i]);\n      }\n    }\n    return result;\n  }, []);\n};\nexport var isCategoricalAxis = function isCategoricalAxis(layout, axisType) {\n  return layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n};\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks The ticks in axis\n * @param {Number} min   The minimun value of axis\n * @param {Number} max   The maximun value of axis\n * @return {Array}       Coordinates\n */\nexport var getCoordinatesOfGrid = function getCoordinatesOfGrid(ticks, min, max) {\n  var hasMin, hasMax;\n  var values = ticks.map(function (entry) {\n    if (entry.coordinate === min) {\n      hasMin = true;\n    }\n    if (entry.coordinate === max) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(min);\n  }\n  if (!hasMax) {\n    values.push(max);\n  }\n  return values;\n};\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = function getTicksOfAxis(axis, isGrid, isAll) {\n  if (!axis) return null;\n  var scale = axis.scale;\n  var duplicateDomain = axis.duplicateDomain,\n    type = axis.type,\n    range = axis.range;\n  var offsetForBand = axis.realScaleType === 'scaleBand' ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axis.axisType === 'angleAxis' && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (axis.ticks || axis.niceTicks)) {\n    var result = (axis.ticks || axis.niceTicks).map(function (entry) {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n    return result.filter(function (row) {\n      return !_isNaN(row.coordinate);\n    });\n  }\n\n  // When axis is a categorial axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (axis.isCategorical && axis.categoricalDomain) {\n    return axis.categoricalDomain.map(function (entry, index) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        index: index,\n        offset: offset\n      };\n    });\n  }\n  if (scale.ticks && !isAll) {\n    return scale.ticks(axis.tickCount).map(function (entry) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map(function (entry, index) {\n    return {\n      coordinate: scale(entry) + offset,\n      value: duplicateDomain ? duplicateDomain[entry] : entry,\n      index: index,\n      offset: offset\n    };\n  });\n};\n\n/**\n * combine the handlers\n * @param  {Function} defaultHandler Internal private handler\n * @param  {Function} parentHandler  Handler function specified in parent component\n * @param  {Function} childHandler   Handler function specified in child component\n * @return {Function}                The combined handler\n */\nexport var combineEventHandlers = function combineEventHandlers(defaultHandler, parentHandler, childHandler) {\n  var customizedHandler;\n  if (_isFunction(childHandler)) {\n    customizedHandler = childHandler;\n  } else if (_isFunction(parentHandler)) {\n    customizedHandler = parentHandler;\n  }\n  if (_isFunction(defaultHandler) || customizedHandler) {\n    return function (arg1, arg2, arg3, arg4) {\n      if (_isFunction(defaultHandler)) {\n        defaultHandler(arg1, arg2, arg3, arg4);\n      }\n      if (_isFunction(customizedHandler)) {\n        customizedHandler(arg1, arg2, arg3, arg4);\n      }\n    };\n  }\n  return null;\n};\n/**\n * Parse the scale function of axis\n * @param  {Object}   axis          The option of axis\n * @param  {String}   chartType     The displayName of chart\n * @param  {Boolean}  hasBar        if it has a bar\n * @return {Function}               The scale function\n */\nexport var parseScale = function parseScale(axis, chartType, hasBar) {\n  var scale = axis.scale,\n    type = axis.type,\n    layout = axis.layout,\n    axisType = axis.axisType;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return {\n        scale: d3Scales.scaleLinear(),\n        realScaleType: 'linear'\n      };\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return {\n        scale: d3Scales.scalePoint(),\n        realScaleType: 'point'\n      };\n    }\n    if (type === 'category') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    return {\n      scale: d3Scales.scaleLinear(),\n      realScaleType: 'linear'\n    };\n  }\n  if (_isString(scale)) {\n    var name = \"scale\".concat(_upperFirst(scale));\n    return {\n      scale: (d3Scales[name] || d3Scales.scalePoint)(),\n      realScaleType: d3Scales[name] ? name : 'point'\n    };\n  }\n  return _isFunction(scale) ? {\n    scale: scale\n  } : {\n    scale: d3Scales.scalePoint(),\n    realScaleType: 'point'\n  };\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = function checkDomainOfScale(scale) {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var min = Math.min(range[0], range[1]) - EPS;\n  var max = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < min || first > max || last < min || last > max) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\nexport var findPositionOfBar = function findPositionOfBar(barPosition, child) {\n  if (!barPosition) {\n    return null;\n  }\n  for (var i = 0, len = barPosition.length; i < len; i++) {\n    if (barPosition[i].item === child) {\n      return barPosition[i].position;\n    }\n  }\n  return null;\n};\nexport var truncateByDomain = function truncateByDomain(value, domain) {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var min = Math.min(domain[0], domain[1]);\n  var max = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < min) {\n    result[0] = min;\n  }\n  if (!isNumber(value[1]) || value[1] > max) {\n    result[1] = max;\n  }\n  if (result[0] > max) {\n    result[0] = max;\n  }\n  if (result[1] < min) {\n    result[1] = min;\n  }\n  return result;\n};\n\n/* eslint no-param-reassign: 0 */\nexport var offsetSign = function offsetSign(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = _isNaN(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring */\n    }\n  }\n};\nexport var offsetPositive = function offsetPositive(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = _isNaN(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring */\n    }\n  }\n};\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  expand: stackOffsetExpand,\n  none: stackOffsetNone,\n  silhouette: stackOffsetSilhouette,\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = function getStackedData(data, stackItems, offsetType) {\n  var dataKeys = stackItems.map(function (item) {\n    return item.props.dataKey;\n  });\n  var stack = shapeStack().keys(dataKeys).value(function (d, key) {\n    return +getValueByDataKey(d, key, 0);\n  }).order(stackOrderNone).offset(STACK_OFFSET_MAP[offsetType]);\n  return stack(data);\n};\nexport var getStackGroupsByAxisId = function getStackGroupsByAxisId(data, _items, numericAxisId, cateAxisId, offsetType, reverseStackOrder) {\n  if (!data) {\n    return null;\n  }\n\n  // reversing items to affect render order (for layering)\n  var items = reverseStackOrder ? _items.reverse() : _items;\n  var stackGroups = items.reduce(function (result, item) {\n    var _item$props3 = item.props,\n      stackId = _item$props3.stackId,\n      hide = _item$props3.hide;\n    if (hide) {\n      return result;\n    }\n    var axisId = item.props[numericAxisId];\n    var parentGroup = result[axisId] || {\n      hasStack: false,\n      stackGroups: {}\n    };\n    if (isNumOrStr(stackId)) {\n      var childGroup = parentGroup.stackGroups[stackId] || {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: []\n      };\n      childGroup.items.push(item);\n      parentGroup.hasStack = true;\n      parentGroup.stackGroups[stackId] = childGroup;\n    } else {\n      parentGroup.stackGroups[uniqueId('_stackId_')] = {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: [item]\n      };\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, parentGroup));\n  }, {});\n  return Object.keys(stackGroups).reduce(function (result, axisId) {\n    var group = stackGroups[axisId];\n    if (group.hasStack) {\n      group.stackGroups = Object.keys(group.stackGroups).reduce(function (res, stackId) {\n        var g = group.stackGroups[stackId];\n        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, stackId, {\n          numericAxisId: numericAxisId,\n          cateAxisId: cateAxisId,\n          items: g.items,\n          stackedData: getStackedData(data, g.items, offsetType)\n        }));\n      }, {});\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, group));\n  }, {});\n};\n\n/**\n * Configure the scale function of axis\n * @param {Object} scale The scale function\n * @param {Object} opts  The configuration of axis\n * @return {Object}      null\n */\nexport var getTicksOfScale = function getTicksOfScale(scale, opts) {\n  var realScaleType = opts.realScaleType,\n    type = opts.type,\n    tickCount = opts.tickCount,\n    originalDomain = opts.originalDomain,\n    allowDecimals = opts.allowDecimals;\n  var scaleType = realScaleType || opts.scale;\n  if (scaleType !== 'auto' && scaleType !== 'linear') {\n    return null;\n  }\n  if (tickCount && type === 'number' && originalDomain && (originalDomain[0] === 'auto' || originalDomain[1] === 'auto')) {\n    // Calculate the ticks by the number of grid when the axis is a number axis\n    var domain = scale.domain();\n    if (!domain.length) {\n      return null;\n    }\n    var tickValues = getNiceTickValues(domain, tickCount, allowDecimals);\n    scale.domain([_min(tickValues), _max(tickValues)]);\n    return {\n      niceTicks: tickValues\n    };\n  }\n  if (tickCount && type === 'number') {\n    var _domain = scale.domain();\n    var _tickValues = getTickValuesFixedDomain(_domain, tickCount, allowDecimals);\n    return {\n      niceTicks: _tickValues\n    };\n  }\n  return null;\n};\nexport var getCateCoordinateOfLine = function getCateCoordinateOfLine(_ref7) {\n  var axis = _ref7.axis,\n    ticks = _ref7.ticks,\n    bandSize = _ref7.bandSize,\n    entry = _ref7.entry,\n    index = _ref7.index,\n    dataKey = _ref7.dataKey;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !_isNil(entry[axis.dataKey])) {\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !_isNil(dataKey) ? dataKey : axis.dataKey);\n  return !_isNil(value) ? axis.scale(value) : null;\n};\nexport var getCateCoordinateOfBar = function getCateCoordinateOfBar(_ref8) {\n  var axis = _ref8.axis,\n    ticks = _ref8.ticks,\n    offset = _ref8.offset,\n    bandSize = _ref8.bandSize,\n    entry = _ref8.entry,\n    index = _ref8.index;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.domain[index]);\n  return !_isNil(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = function getBaseValueOfBar(_ref9) {\n  var numericAxis = _ref9.numericAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var min = Math.min(domain[0], domain[1]);\n    var max = Math.max(domain[0], domain[1]);\n    if (min <= 0 && max >= 0) {\n      return 0;\n    }\n    if (max < 0) {\n      return max;\n    }\n    return min;\n  }\n  return domain[0];\n};\nexport var getStackedDataOfItem = function getStackedDataOfItem(item, stackGroups) {\n  var stackId = item.props.stackId;\n  if (isNumOrStr(stackId)) {\n    var group = stackGroups[stackId];\n    if (group && group.items.length) {\n      var itemIndex = -1;\n      for (var i = 0, len = group.items.length; i < len; i++) {\n        if (group.items[i] === item) {\n          itemIndex = i;\n          break;\n        }\n      }\n      return itemIndex >= 0 ? group.stackedData[itemIndex] : null;\n    }\n  }\n  return null;\n};\nvar getDomainOfSingle = function getDomainOfSingle(data) {\n  return data.reduce(function (result, entry) {\n    return [_min(entry.concat([result[0]]).filter(isNumber)), _max(entry.concat([result[1]]).filter(isNumber))];\n  }, [Infinity, -Infinity]);\n};\nexport var getDomainOfStackGroups = function getDomainOfStackGroups(stackGroups, startIndex, endIndex) {\n  return Object.keys(stackGroups).reduce(function (result, stackId) {\n    var group = stackGroups[stackId];\n    var stackedData = group.stackedData;\n    var domain = stackedData.reduce(function (res, entry) {\n      var s = getDomainOfSingle(entry.slice(startIndex, endIndex + 1));\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]).map(function (result) {\n    return result === Infinity || result === -Infinity ? 0 : result;\n  });\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var parseSpecifiedDomain = function parseSpecifiedDomain(specifiedDomain, dataDomain, allowDataOverflow) {\n  if (_isFunction(specifiedDomain)) {\n    return specifiedDomain(dataDomain, allowDataOverflow);\n  }\n  if (!_isArray(specifiedDomain)) {\n    return dataDomain;\n  }\n  var domain = [];\n\n  /* eslint-disable prefer-destructuring */\n  if (isNumber(specifiedDomain[0])) {\n    domain[0] = allowDataOverflow ? specifiedDomain[0] : Math.min(specifiedDomain[0], dataDomain[0]);\n  } else if (MIN_VALUE_REG.test(specifiedDomain[0])) {\n    var value = +MIN_VALUE_REG.exec(specifiedDomain[0])[1];\n    domain[0] = dataDomain[0] - value;\n  } else if (_isFunction(specifiedDomain[0])) {\n    domain[0] = specifiedDomain[0](dataDomain[0]);\n  } else {\n    domain[0] = dataDomain[0];\n  }\n  if (isNumber(specifiedDomain[1])) {\n    domain[1] = allowDataOverflow ? specifiedDomain[1] : Math.max(specifiedDomain[1], dataDomain[1]);\n  } else if (MAX_VALUE_REG.test(specifiedDomain[1])) {\n    var _value = +MAX_VALUE_REG.exec(specifiedDomain[1])[1];\n    domain[1] = dataDomain[1] + _value;\n  } else if (_isFunction(specifiedDomain[1])) {\n    domain[1] = specifiedDomain[1](dataDomain[1]);\n  } else {\n    domain[1] = dataDomain[1];\n  }\n  /* eslint-enable prefer-destructuring */\n\n  return domain;\n};\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = function getBandSizeOfAxis(axis, ticks, isBar) {\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = _sortBy(ticks, function (o) {\n      return o.coordinate;\n    });\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\n/**\n * parse the domain of a category axis when a domain is specified\n * @param   {Array}        specifiedDomain  The domain specified by users\n * @param   {Array}        calculatedDomain The domain calculated by dateKey\n * @param   {ReactElement} axisChild        The axis element\n * @returns {Array}        domains\n */\nexport var parseDomainOfCategoryAxis = function parseDomainOfCategoryAxis(specifiedDomain, calculatedDomain, axisChild) {\n  if (!specifiedDomain || !specifiedDomain.length) {\n    return calculatedDomain;\n  }\n  if (_isEqual(specifiedDomain, _get(axisChild, 'type.defaultProps.domain'))) {\n    return calculatedDomain;\n  }\n  return specifiedDomain;\n};\nexport var getTooltipItem = function getTooltipItem(graphicalItem, payload) {\n  var _graphicalItem$props = graphicalItem.props,\n    dataKey = _graphicalItem$props.dataKey,\n    name = _graphicalItem$props.name,\n    unit = _graphicalItem$props.unit,\n    formatter = _graphicalItem$props.formatter,\n    tooltipType = _graphicalItem$props.tooltipType,\n    chartType = _graphicalItem$props.chartType;\n  return _objectSpread(_objectSpread({}, filterProps(graphicalItem)), {}, {\n    dataKey: dataKey,\n    unit: unit,\n    formatter: formatter,\n    name: name || dataKey,\n    color: getMainColorOfGraphicItem(graphicalItem),\n    value: getValueByDataKey(payload, dataKey),\n    type: tooltipType,\n    payload: payload,\n    chartType: chartType\n  });\n};", "map": {"version": 3, "names": ["_isEqual", "_sortBy", "_upperFirst", "_isString", "_isNaN", "_isArray", "_max", "_min", "_flatMap", "_isFunction", "_get", "_isNil", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "length", "i", "arr2", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "d3Scales", "stack", "shapeStack", "stackOffsetExpand", "stackOffsetNone", "stackOffsetSilhouette", "stackOffsetWiggle", "stackOrderNone", "getNiceTickValues", "getTickValuesFixedDomain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Legend", "findEntryInArray", "getPercentValue", "isNumber", "isNumOrStr", "mathSign", "uniqueId", "filterProps", "findAllByType", "findChildByType", "getDisplayName", "getValueByDataKey", "dataKey", "defaultValue", "getDomainOfDataByKey", "data", "type", "filterNil", "flattenData", "entry", "domain", "parseFloat", "Infinity", "validateData", "map", "Date", "calculateActiveTickIndex", "coordinate", "_ticks$length", "ticks", "unsortedTicks", "axis", "index", "axisType", "Math", "abs", "range", "before", "cur", "after", "sameDirectionCoord", "diffInterval", "curInRange", "min", "max", "afterInRange", "sameInterval", "_i", "getMainColorOfGraphicItem", "item", "_ref", "displayName", "_item$props", "props", "stroke", "fill", "result", "getLegendProps", "_ref2", "children", "formattedGraphicalItems", "legend<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "legendItem", "legendData", "payload", "reduce", "_ref3", "sectors", "concat", "iconType", "legendType", "color", "_ref4", "_item$props2", "hide", "inactive", "getWithHeight", "getBarSizeList", "_ref5", "globalSize", "barSize", "_ref5$stackGroups", "stackGroups", "numericAxisIds", "sgs", "stackIds", "j", "sLen", "_sgs$stackIds$j", "items", "cateAxisId", "barItems", "indexOf", "selfSize", "cateId", "stackList", "getBarPosition", "_ref6", "barGap", "barCategoryGap", "bandSize", "_ref6$sizeList", "sizeList", "maxBarSize", "realBarGap", "useFull", "fullBarSize", "sum", "offset", "prev", "size", "newRes", "position", "_offset", "originalSize", "appendOffsetOfLegend", "legendBox", "width", "margin", "left", "right", "legendProps", "newOffset", "box", "align", "verticalAlign", "layout", "height", "isErrorBarRelevantForAxis", "direction", "getDomainOfErrorBars", "errorBars", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryValue", "mainValue", "errorDomain", "prevErrorArr", "k", "errorValue", "lowerValue", "upperValue", "parseErrorBarsOfAxis", "domains", "getDomainOfItemsWithSameAxis", "tag", "isCategoricalAxis", "getCoordinatesOfGrid", "has<PERSON>in", "hasMax", "values", "getTicksOfAxis", "isGrid", "isAll", "scale", "duplicateDomain", "offsetForBand", "realScaleType", "bandwidth", "niceTicks", "scaleContent", "row", "isCategorical", "categoricalDomain", "tickCount", "combineEventHandlers", "defaultHandler", "parentHandler", "<PERSON><PERSON><PERSON><PERSON>", "customizedHandler", "arg1", "arg2", "arg3", "arg4", "parseScale", "chartType", "<PERSON><PERSON><PERSON>", "scaleBand", "scaleLinear", "scalePoint", "EPS", "checkDomainOfScale", "first", "last", "findPositionOfBar", "barPosition", "child", "truncateByDomain", "offsetSign", "series", "m", "positive", "negative", "offsetPositive", "STACK_OFFSET_MAP", "sign", "expand", "none", "silhouette", "wiggle", "getStackedData", "stackItems", "offsetType", "dataKeys", "d", "order", "getStackGroupsByAxisId", "_items", "numericAxisId", "reverseStackOrder", "reverse", "_item$props3", "stackId", "axisId", "parentGroup", "hasStack", "childGroup", "group", "g", "stackedData", "getTicksOfScale", "opts", "originalDomain", "allowDecimals", "scaleType", "tickValues", "_domain", "_tickValues", "getCateCoordinateOfLine", "_ref7", "allowDuplicatedCategory", "matchedTick", "getCateCoordinateOfBar", "_ref8", "getBaseValueOfBar", "_ref9", "numericAxis", "getStackedDataOfItem", "itemIndex", "getDomainOfSingle", "getDomainOfStackGroups", "startIndex", "endIndex", "s", "MIN_VALUE_REG", "MAX_VALUE_REG", "parseSpecifiedDomain", "specifiedDomain", "dataDomain", "allowDataOverflow", "exec", "_value", "getBandSizeOfAxis", "isBar", "bandWidth", "orderedTicks", "parseDomainOfCategoryAxis", "calculatedDomain", "axisChild", "getTooltipItem", "graphicalItem", "_graphicalItem$props", "unit", "formatter", "tooltipType"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/util/ChartUtils.js"], "sourcesContent": ["import _isEqual from \"lodash/isEqual\";\nimport _sortBy from \"lodash/sortBy\";\nimport _upperFirst from \"lodash/upperFirst\";\nimport _isString from \"lodash/isString\";\nimport _isNaN from \"lodash/isNaN\";\nimport _isArray from \"lodash/isArray\";\nimport _max from \"lodash/max\";\nimport _min from \"lodash/min\";\nimport _flatMap from \"lodash/flatMap\";\nimport _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport { getNiceTickValues, getTickValuesFixedDomain } from 'recharts-scale';\nimport { ErrorBar } from '../cartesian/ErrorBar';\nimport { Legend } from '../component/Legend';\nimport { findEntryInArray, getPercentValue, isNumber, isNumOrStr, mathSign, uniqueId } from './DataUtils';\nimport { filterProps, findAllByType, findChildByType, getDisplayName } from './ReactUtils';\n// TODO: Cause of circular dependency. Needs refactor.\n// import { RadiusAxisProps, AngleAxisProps } from '../polar/types';\n\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (_isNil(obj) || _isNil(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return _get(obj, dataKey, defaultValue);\n  }\n  if (_isFunction(dataKey)) {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\n/**\n * Get domain of data by key\n * @param  {Array}   data      The data displayed in the chart\n * @param  {String}  key       The unique key of a group of data\n * @param  {String}  type      The type of axis\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array} Domain of data\n */\nexport function getDomainOfDataByKey(data, key, type, filterNil) {\n  var flattenData = _flatMap(data, function (entry) {\n    return getValueByDataKey(entry, key);\n  });\n  if (type === 'number') {\n    var domain = flattenData.filter(function (entry) {\n      return isNumber(entry) || parseFloat(entry);\n    });\n    return domain.length ? [_min(domain), _max(domain)] : [Infinity, -Infinity];\n  }\n  var validateData = filterNil ? flattenData.filter(function (entry) {\n    return !_isNil(entry);\n  }) : flattenData;\n\n  // 支持Date类型的x轴\n  return validateData.map(function (entry) {\n    return isNumOrStr(entry) || entry instanceof Date ? entry : '';\n  });\n}\nexport var calculateActiveTickIndex = function calculateActiveTickIndex(coordinate) {\n  var _ticks$length;\n  var ticks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var unsortedTicks = arguments.length > 2 ? arguments[2] : undefined;\n  var axis = arguments.length > 3 ? arguments[3] : undefined;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or less ticks ticks then the active tick is at index 0\n  if (len <= 1) {\n    return 0;\n  }\n  if (axis && axis.axisType === 'angleAxis' && Math.abs(Math.abs(axis.range[1] - axis.range[0]) - 360) <= 1e-6) {\n    var range = axis.range;\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      } else {\n        var min = Math.min(before, after);\n        var max = Math.max(before, after);\n        if (coordinate > (min + cur) / 2 && coordinate <= (max + cur) / 2) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      }\n    }\n  } else {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        index = ticks[_i].index;\n        break;\n      }\n    }\n  }\n  return index;\n};\n\n/**\n * Get the main color of each graphic item\n * @param  {ReactElement} item A graphic item\n * @return {String}            Color\n */\nexport var getMainColorOfGraphicItem = function getMainColorOfGraphicItem(item) {\n  var _ref = item,\n    displayName = _ref.type.displayName; // TODO: check if displayName is valid.\n  var _item$props = item.props,\n    stroke = _item$props.stroke,\n    fill = _item$props.fill;\n  var result;\n  switch (displayName) {\n    case 'Line':\n      result = stroke;\n      break;\n    case 'Area':\n    case 'Radar':\n      result = stroke && stroke !== 'none' ? stroke : fill;\n      break;\n    default:\n      result = fill;\n      break;\n  }\n  return result;\n};\nexport var getLegendProps = function getLegendProps(_ref2) {\n  var children = _ref2.children,\n    formattedGraphicalItems = _ref2.formattedGraphicalItems,\n    legendWidth = _ref2.legendWidth,\n    legendContent = _ref2.legendContent;\n  var legendItem = findChildByType(children, Legend);\n  if (!legendItem) {\n    return null;\n  }\n  var legendData;\n  if (legendItem.props && legendItem.props.payload) {\n    legendData = legendItem.props && legendItem.props.payload;\n  } else if (legendContent === 'children') {\n    legendData = (formattedGraphicalItems || []).reduce(function (result, _ref3) {\n      var item = _ref3.item,\n        props = _ref3.props;\n      var data = props.sectors || props.data || [];\n      return result.concat(data.map(function (entry) {\n        return {\n          type: legendItem.props.iconType || item.props.legendType,\n          value: entry.name,\n          color: entry.fill,\n          payload: entry\n        };\n      }));\n    }, []);\n  } else {\n    legendData = (formattedGraphicalItems || []).map(function (_ref4) {\n      var item = _ref4.item;\n      var _item$props2 = item.props,\n        dataKey = _item$props2.dataKey,\n        name = _item$props2.name,\n        legendType = _item$props2.legendType,\n        hide = _item$props2.hide;\n      return {\n        inactive: hide,\n        dataKey: dataKey,\n        type: legendItem.props.iconType || legendType || 'square',\n        color: getMainColorOfGraphicItem(item),\n        value: name || dataKey,\n        payload: item.props\n      };\n    });\n  }\n  return _objectSpread(_objectSpread(_objectSpread({}, legendItem.props), Legend.getWithHeight(legendItem, legendWidth)), {}, {\n    payload: legendData,\n    item: legendItem\n  });\n};\n/**\n * Calculate the size of all groups for stacked bar graph\n * @param  {Object} stackGroups The items grouped by axisId and stackId\n * @return {Object} The size of all groups\n */\nexport var getBarSizeList = function getBarSizeList(_ref5) {\n  var globalSize = _ref5.barSize,\n    _ref5$stackGroups = _ref5.stackGroups,\n    stackGroups = _ref5$stackGroups === void 0 ? {} : _ref5$stackGroups;\n  if (!stackGroups) {\n    return {};\n  }\n  var result = {};\n  var numericAxisIds = Object.keys(stackGroups);\n  for (var i = 0, len = numericAxisIds.length; i < len; i++) {\n    var sgs = stackGroups[numericAxisIds[i]].stackGroups;\n    var stackIds = Object.keys(sgs);\n    for (var j = 0, sLen = stackIds.length; j < sLen; j++) {\n      var _sgs$stackIds$j = sgs[stackIds[j]],\n        items = _sgs$stackIds$j.items,\n        cateAxisId = _sgs$stackIds$j.cateAxisId;\n      var barItems = items.filter(function (item) {\n        return getDisplayName(item.type).indexOf('Bar') >= 0;\n      });\n      if (barItems && barItems.length) {\n        var selfSize = barItems[0].props.barSize;\n        var cateId = barItems[0].props[cateAxisId];\n        if (!result[cateId]) {\n          result[cateId] = [];\n        }\n        result[cateId].push({\n          item: barItems[0],\n          stackList: barItems.slice(1),\n          barSize: _isNil(selfSize) ? globalSize : selfSize\n        });\n      }\n    }\n  }\n  return result;\n};\n\n/**\n * Calculate the size of each bar and the gap between two bars\n * @param  {Number} bandSize  The size of each category\n * @param  {sizeList} sizeList  The size of all groups\n * @param  {maxBarSize} maxBarSize The maximum size of bar\n * @return {Number} The size of each bar and the gap between two bars\n */\nexport var getBarPosition = function getBarPosition(_ref6) {\n  var barGap = _ref6.barGap,\n    barCategoryGap = _ref6.barCategoryGap,\n    bandSize = _ref6.bandSize,\n    _ref6$sizeList = _ref6.sizeList,\n    sizeList = _ref6$sizeList === void 0 ? [] : _ref6$sizeList,\n    maxBarSize = _ref6.maxBarSize;\n  var len = sizeList.length;\n  if (len < 1) return null;\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n\n  // whether or not is barSize setted by user\n  if (sizeList[0].barSize === +sizeList[0].barSize) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce(function (res, entry) {\n      return res + entry.barSize || 0;\n    }, 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce(function (res, entry) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : entry.barSize\n        }\n      }]);\n      prev = newRes[newRes.length - 1].position;\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: prev\n          });\n        });\n      }\n      return newRes;\n    }, []);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = maxBarSize === +maxBarSize ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce(function (res, entry, i) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n          size: size\n        }\n      }]);\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: newRes[newRes.length - 1].position\n          });\n        });\n      }\n      return newRes;\n    }, []);\n  }\n  return result;\n};\nexport var appendOffsetOfLegend = function appendOffsetOfLegend(offset, items, props, legendBox) {\n  var children = props.children,\n    width = props.width,\n    margin = props.margin;\n  var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n  // const legendHeight = height - (margin.top || 0) - (margin.bottom || 0);\n  var legendProps = getLegendProps({\n    children: children,\n    legendWidth: legendWidth\n  });\n  var newOffset = offset;\n  if (legendProps) {\n    var box = legendBox || {};\n    var align = legendProps.align,\n      verticalAlign = legendProps.verticalAlign,\n      layout = legendProps.layout;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && isNumber(offset[align])) {\n      newOffset = _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, align, newOffset[align] + (box.width || 0)));\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && isNumber(offset[verticalAlign])) {\n      newOffset = _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, verticalAlign, newOffset[verticalAlign] + (box.height || 0)));\n    }\n  }\n  return newOffset;\n};\nvar isErrorBarRelevantForAxis = function isErrorBarRelevantForAxis(layout, axisType, direction) {\n  if (_isNil(axisType)) {\n    return true;\n  }\n  if (layout === 'horizontal') {\n    return axisType === 'yAxis';\n  }\n  if (layout === 'vertical') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'x') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'y') {\n    return axisType === 'yAxis';\n  }\n  return true;\n};\nexport var getDomainOfErrorBars = function getDomainOfErrorBars(data, item, dataKey, layout, axisType) {\n  var children = item.props.children;\n  var errorBars = findAllByType(children, ErrorBar).filter(function (errorBarChild) {\n    return isErrorBarRelevantForAxis(layout, axisType, errorBarChild.props.direction);\n  });\n  if (errorBars && errorBars.length) {\n    var keys = errorBars.map(function (errorBarChild) {\n      return errorBarChild.props.dataKey;\n    });\n    return data.reduce(function (result, entry) {\n      var entryValue = getValueByDataKey(entry, dataKey, 0);\n      var mainValue = _isArray(entryValue) ? [_min(entryValue), _max(entryValue)] : [entryValue, entryValue];\n      var errorDomain = keys.reduce(function (prevErrorArr, k) {\n        var errorValue = getValueByDataKey(entry, k, 0);\n        var lowerValue = mainValue[0] - Math.abs(_isArray(errorValue) ? errorValue[0] : errorValue);\n        var upperValue = mainValue[1] + Math.abs(_isArray(errorValue) ? errorValue[1] : errorValue);\n        return [Math.min(lowerValue, prevErrorArr[0]), Math.max(upperValue, prevErrorArr[1])];\n      }, [Infinity, -Infinity]);\n      return [Math.min(errorDomain[0], result[0]), Math.max(errorDomain[1], result[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\nexport var parseErrorBarsOfAxis = function parseErrorBarsOfAxis(data, items, dataKey, axisType, layout) {\n  var domains = items.map(function (item) {\n    return getDomainOfErrorBars(data, item, dataKey, layout, axisType);\n  }).filter(function (entry) {\n    return !_isNil(entry);\n  });\n  if (domains && domains.length) {\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\n\n/**\n * Get domain of data by the configuration of item element\n * @param  {Array}   data      The data displayed in the chart\n * @param  {Array}   items     The instances of item\n * @param  {String}  type      The type of axis, number - Number Axis, category - Category Axis\n * @param  {LayoutType} layout The type of layout\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array}        Domain\n */\nexport var getDomainOfItemsWithSameAxis = function getDomainOfItemsWithSameAxis(data, items, type, layout, filterNil) {\n  var domains = items.map(function (item) {\n    var dataKey = item.props.dataKey;\n    if (type === 'number' && dataKey) {\n      return getDomainOfErrorBars(data, item, dataKey, layout) || getDomainOfDataByKey(data, dataKey, type, filterNil);\n    }\n    return getDomainOfDataByKey(data, dataKey, type, filterNil);\n  });\n  if (type === 'number') {\n    // Calculate the domain of number axis\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  var tag = {};\n  // Get the union set of category axis\n  return domains.reduce(function (result, entry) {\n    for (var i = 0, len = entry.length; i < len; i++) {\n      if (!tag[entry[i]]) {\n        tag[entry[i]] = true;\n        result.push(entry[i]);\n      }\n    }\n    return result;\n  }, []);\n};\nexport var isCategoricalAxis = function isCategoricalAxis(layout, axisType) {\n  return layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n};\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks The ticks in axis\n * @param {Number} min   The minimun value of axis\n * @param {Number} max   The maximun value of axis\n * @return {Array}       Coordinates\n */\nexport var getCoordinatesOfGrid = function getCoordinatesOfGrid(ticks, min, max) {\n  var hasMin, hasMax;\n  var values = ticks.map(function (entry) {\n    if (entry.coordinate === min) {\n      hasMin = true;\n    }\n    if (entry.coordinate === max) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(min);\n  }\n  if (!hasMax) {\n    values.push(max);\n  }\n  return values;\n};\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = function getTicksOfAxis(axis, isGrid, isAll) {\n  if (!axis) return null;\n  var scale = axis.scale;\n  var duplicateDomain = axis.duplicateDomain,\n    type = axis.type,\n    range = axis.range;\n  var offsetForBand = axis.realScaleType === 'scaleBand' ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axis.axisType === 'angleAxis' && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (axis.ticks || axis.niceTicks)) {\n    var result = (axis.ticks || axis.niceTicks).map(function (entry) {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n    return result.filter(function (row) {\n      return !_isNaN(row.coordinate);\n    });\n  }\n\n  // When axis is a categorial axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (axis.isCategorical && axis.categoricalDomain) {\n    return axis.categoricalDomain.map(function (entry, index) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        index: index,\n        offset: offset\n      };\n    });\n  }\n  if (scale.ticks && !isAll) {\n    return scale.ticks(axis.tickCount).map(function (entry) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map(function (entry, index) {\n    return {\n      coordinate: scale(entry) + offset,\n      value: duplicateDomain ? duplicateDomain[entry] : entry,\n      index: index,\n      offset: offset\n    };\n  });\n};\n\n/**\n * combine the handlers\n * @param  {Function} defaultHandler Internal private handler\n * @param  {Function} parentHandler  Handler function specified in parent component\n * @param  {Function} childHandler   Handler function specified in child component\n * @return {Function}                The combined handler\n */\nexport var combineEventHandlers = function combineEventHandlers(defaultHandler, parentHandler, childHandler) {\n  var customizedHandler;\n  if (_isFunction(childHandler)) {\n    customizedHandler = childHandler;\n  } else if (_isFunction(parentHandler)) {\n    customizedHandler = parentHandler;\n  }\n  if (_isFunction(defaultHandler) || customizedHandler) {\n    return function (arg1, arg2, arg3, arg4) {\n      if (_isFunction(defaultHandler)) {\n        defaultHandler(arg1, arg2, arg3, arg4);\n      }\n      if (_isFunction(customizedHandler)) {\n        customizedHandler(arg1, arg2, arg3, arg4);\n      }\n    };\n  }\n  return null;\n};\n/**\n * Parse the scale function of axis\n * @param  {Object}   axis          The option of axis\n * @param  {String}   chartType     The displayName of chart\n * @param  {Boolean}  hasBar        if it has a bar\n * @return {Function}               The scale function\n */\nexport var parseScale = function parseScale(axis, chartType, hasBar) {\n  var scale = axis.scale,\n    type = axis.type,\n    layout = axis.layout,\n    axisType = axis.axisType;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return {\n        scale: d3Scales.scaleLinear(),\n        realScaleType: 'linear'\n      };\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return {\n        scale: d3Scales.scalePoint(),\n        realScaleType: 'point'\n      };\n    }\n    if (type === 'category') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    return {\n      scale: d3Scales.scaleLinear(),\n      realScaleType: 'linear'\n    };\n  }\n  if (_isString(scale)) {\n    var name = \"scale\".concat(_upperFirst(scale));\n    return {\n      scale: (d3Scales[name] || d3Scales.scalePoint)(),\n      realScaleType: d3Scales[name] ? name : 'point'\n    };\n  }\n  return _isFunction(scale) ? {\n    scale: scale\n  } : {\n    scale: d3Scales.scalePoint(),\n    realScaleType: 'point'\n  };\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = function checkDomainOfScale(scale) {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var min = Math.min(range[0], range[1]) - EPS;\n  var max = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < min || first > max || last < min || last > max) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\nexport var findPositionOfBar = function findPositionOfBar(barPosition, child) {\n  if (!barPosition) {\n    return null;\n  }\n  for (var i = 0, len = barPosition.length; i < len; i++) {\n    if (barPosition[i].item === child) {\n      return barPosition[i].position;\n    }\n  }\n  return null;\n};\nexport var truncateByDomain = function truncateByDomain(value, domain) {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var min = Math.min(domain[0], domain[1]);\n  var max = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < min) {\n    result[0] = min;\n  }\n  if (!isNumber(value[1]) || value[1] > max) {\n    result[1] = max;\n  }\n  if (result[0] > max) {\n    result[0] = max;\n  }\n  if (result[1] < min) {\n    result[1] = min;\n  }\n  return result;\n};\n\n/* eslint no-param-reassign: 0 */\nexport var offsetSign = function offsetSign(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = _isNaN(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring */\n    }\n  }\n};\n\nexport var offsetPositive = function offsetPositive(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = _isNaN(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring */\n    }\n  }\n};\n\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  expand: stackOffsetExpand,\n  none: stackOffsetNone,\n  silhouette: stackOffsetSilhouette,\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = function getStackedData(data, stackItems, offsetType) {\n  var dataKeys = stackItems.map(function (item) {\n    return item.props.dataKey;\n  });\n  var stack = shapeStack().keys(dataKeys).value(function (d, key) {\n    return +getValueByDataKey(d, key, 0);\n  }).order(stackOrderNone).offset(STACK_OFFSET_MAP[offsetType]);\n  return stack(data);\n};\nexport var getStackGroupsByAxisId = function getStackGroupsByAxisId(data, _items, numericAxisId, cateAxisId, offsetType, reverseStackOrder) {\n  if (!data) {\n    return null;\n  }\n\n  // reversing items to affect render order (for layering)\n  var items = reverseStackOrder ? _items.reverse() : _items;\n  var stackGroups = items.reduce(function (result, item) {\n    var _item$props3 = item.props,\n      stackId = _item$props3.stackId,\n      hide = _item$props3.hide;\n    if (hide) {\n      return result;\n    }\n    var axisId = item.props[numericAxisId];\n    var parentGroup = result[axisId] || {\n      hasStack: false,\n      stackGroups: {}\n    };\n    if (isNumOrStr(stackId)) {\n      var childGroup = parentGroup.stackGroups[stackId] || {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: []\n      };\n      childGroup.items.push(item);\n      parentGroup.hasStack = true;\n      parentGroup.stackGroups[stackId] = childGroup;\n    } else {\n      parentGroup.stackGroups[uniqueId('_stackId_')] = {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: [item]\n      };\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, parentGroup));\n  }, {});\n  return Object.keys(stackGroups).reduce(function (result, axisId) {\n    var group = stackGroups[axisId];\n    if (group.hasStack) {\n      group.stackGroups = Object.keys(group.stackGroups).reduce(function (res, stackId) {\n        var g = group.stackGroups[stackId];\n        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, stackId, {\n          numericAxisId: numericAxisId,\n          cateAxisId: cateAxisId,\n          items: g.items,\n          stackedData: getStackedData(data, g.items, offsetType)\n        }));\n      }, {});\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, group));\n  }, {});\n};\n\n/**\n * Configure the scale function of axis\n * @param {Object} scale The scale function\n * @param {Object} opts  The configuration of axis\n * @return {Object}      null\n */\nexport var getTicksOfScale = function getTicksOfScale(scale, opts) {\n  var realScaleType = opts.realScaleType,\n    type = opts.type,\n    tickCount = opts.tickCount,\n    originalDomain = opts.originalDomain,\n    allowDecimals = opts.allowDecimals;\n  var scaleType = realScaleType || opts.scale;\n  if (scaleType !== 'auto' && scaleType !== 'linear') {\n    return null;\n  }\n  if (tickCount && type === 'number' && originalDomain && (originalDomain[0] === 'auto' || originalDomain[1] === 'auto')) {\n    // Calculate the ticks by the number of grid when the axis is a number axis\n    var domain = scale.domain();\n    if (!domain.length) {\n      return null;\n    }\n    var tickValues = getNiceTickValues(domain, tickCount, allowDecimals);\n    scale.domain([_min(tickValues), _max(tickValues)]);\n    return {\n      niceTicks: tickValues\n    };\n  }\n  if (tickCount && type === 'number') {\n    var _domain = scale.domain();\n    var _tickValues = getTickValuesFixedDomain(_domain, tickCount, allowDecimals);\n    return {\n      niceTicks: _tickValues\n    };\n  }\n  return null;\n};\nexport var getCateCoordinateOfLine = function getCateCoordinateOfLine(_ref7) {\n  var axis = _ref7.axis,\n    ticks = _ref7.ticks,\n    bandSize = _ref7.bandSize,\n    entry = _ref7.entry,\n    index = _ref7.index,\n    dataKey = _ref7.dataKey;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !_isNil(entry[axis.dataKey])) {\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !_isNil(dataKey) ? dataKey : axis.dataKey);\n  return !_isNil(value) ? axis.scale(value) : null;\n};\nexport var getCateCoordinateOfBar = function getCateCoordinateOfBar(_ref8) {\n  var axis = _ref8.axis,\n    ticks = _ref8.ticks,\n    offset = _ref8.offset,\n    bandSize = _ref8.bandSize,\n    entry = _ref8.entry,\n    index = _ref8.index;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.domain[index]);\n  return !_isNil(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = function getBaseValueOfBar(_ref9) {\n  var numericAxis = _ref9.numericAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var min = Math.min(domain[0], domain[1]);\n    var max = Math.max(domain[0], domain[1]);\n    if (min <= 0 && max >= 0) {\n      return 0;\n    }\n    if (max < 0) {\n      return max;\n    }\n    return min;\n  }\n  return domain[0];\n};\nexport var getStackedDataOfItem = function getStackedDataOfItem(item, stackGroups) {\n  var stackId = item.props.stackId;\n  if (isNumOrStr(stackId)) {\n    var group = stackGroups[stackId];\n    if (group && group.items.length) {\n      var itemIndex = -1;\n      for (var i = 0, len = group.items.length; i < len; i++) {\n        if (group.items[i] === item) {\n          itemIndex = i;\n          break;\n        }\n      }\n      return itemIndex >= 0 ? group.stackedData[itemIndex] : null;\n    }\n  }\n  return null;\n};\nvar getDomainOfSingle = function getDomainOfSingle(data) {\n  return data.reduce(function (result, entry) {\n    return [_min(entry.concat([result[0]]).filter(isNumber)), _max(entry.concat([result[1]]).filter(isNumber))];\n  }, [Infinity, -Infinity]);\n};\nexport var getDomainOfStackGroups = function getDomainOfStackGroups(stackGroups, startIndex, endIndex) {\n  return Object.keys(stackGroups).reduce(function (result, stackId) {\n    var group = stackGroups[stackId];\n    var stackedData = group.stackedData;\n    var domain = stackedData.reduce(function (res, entry) {\n      var s = getDomainOfSingle(entry.slice(startIndex, endIndex + 1));\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]).map(function (result) {\n    return result === Infinity || result === -Infinity ? 0 : result;\n  });\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var parseSpecifiedDomain = function parseSpecifiedDomain(specifiedDomain, dataDomain, allowDataOverflow) {\n  if (_isFunction(specifiedDomain)) {\n    return specifiedDomain(dataDomain, allowDataOverflow);\n  }\n  if (!_isArray(specifiedDomain)) {\n    return dataDomain;\n  }\n  var domain = [];\n\n  /* eslint-disable prefer-destructuring */\n  if (isNumber(specifiedDomain[0])) {\n    domain[0] = allowDataOverflow ? specifiedDomain[0] : Math.min(specifiedDomain[0], dataDomain[0]);\n  } else if (MIN_VALUE_REG.test(specifiedDomain[0])) {\n    var value = +MIN_VALUE_REG.exec(specifiedDomain[0])[1];\n    domain[0] = dataDomain[0] - value;\n  } else if (_isFunction(specifiedDomain[0])) {\n    domain[0] = specifiedDomain[0](dataDomain[0]);\n  } else {\n    domain[0] = dataDomain[0];\n  }\n  if (isNumber(specifiedDomain[1])) {\n    domain[1] = allowDataOverflow ? specifiedDomain[1] : Math.max(specifiedDomain[1], dataDomain[1]);\n  } else if (MAX_VALUE_REG.test(specifiedDomain[1])) {\n    var _value = +MAX_VALUE_REG.exec(specifiedDomain[1])[1];\n    domain[1] = dataDomain[1] + _value;\n  } else if (_isFunction(specifiedDomain[1])) {\n    domain[1] = specifiedDomain[1](dataDomain[1]);\n  } else {\n    domain[1] = dataDomain[1];\n  }\n  /* eslint-enable prefer-destructuring */\n\n  return domain;\n};\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = function getBandSizeOfAxis(axis, ticks, isBar) {\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = _sortBy(ticks, function (o) {\n      return o.coordinate;\n    });\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\n/**\n * parse the domain of a category axis when a domain is specified\n * @param   {Array}        specifiedDomain  The domain specified by users\n * @param   {Array}        calculatedDomain The domain calculated by dateKey\n * @param   {ReactElement} axisChild        The axis element\n * @returns {Array}        domains\n */\nexport var parseDomainOfCategoryAxis = function parseDomainOfCategoryAxis(specifiedDomain, calculatedDomain, axisChild) {\n  if (!specifiedDomain || !specifiedDomain.length) {\n    return calculatedDomain;\n  }\n  if (_isEqual(specifiedDomain, _get(axisChild, 'type.defaultProps.domain'))) {\n    return calculatedDomain;\n  }\n  return specifiedDomain;\n};\nexport var getTooltipItem = function getTooltipItem(graphicalItem, payload) {\n  var _graphicalItem$props = graphicalItem.props,\n    dataKey = _graphicalItem$props.dataKey,\n    name = _graphicalItem$props.name,\n    unit = _graphicalItem$props.unit,\n    formatter = _graphicalItem$props.formatter,\n    tooltipType = _graphicalItem$props.tooltipType,\n    chartType = _graphicalItem$props.chartType;\n  return _objectSpread(_objectSpread({}, filterProps(graphicalItem)), {}, {\n    dataKey: dataKey,\n    unit: unit,\n    formatter: formatter,\n    name: name || dataKey,\n    color: getMainColorOfGraphicItem(graphicalItem),\n    value: getValueByDataKey(payload, dataKey),\n    type: tooltipType,\n    payload: payload,\n    chartType: chartType\n  });\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACZ,SAAS,CAACa,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACT,WAAW,EAAEY,CAAC,GAAGH,CAAC,CAACT,WAAW,CAACiB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACV,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASL,gBAAgBA,CAACgB,IAAI,EAAE;EAAE,IAAI,OAAOvB,MAAM,KAAK,WAAW,IAAIuB,IAAI,CAACvB,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIsB,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASjB,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIe,KAAK,CAACI,OAAO,CAACnB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;AAAE;AAC1F,SAASQ,iBAAiBA,CAACR,GAAG,EAAEoB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGpB,GAAG,CAACqB,MAAM,EAAED,GAAG,GAAGpB,GAAG,CAACqB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIR,KAAK,CAACK,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGtB,GAAG,CAACsB,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGjB,MAAM,CAACiB,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIf,MAAM,CAACkB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGnB,MAAM,CAACkB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOrB,MAAM,CAACsB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASS,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,SAAS,CAACjB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIiB,MAAM,GAAG,IAAI,IAAID,SAAS,CAAChB,CAAC,CAAC,GAAGgB,SAAS,CAAChB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGE,OAAO,CAACd,MAAM,CAAC6B,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACL,MAAM,EAAEI,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAG/B,MAAM,CAACiC,yBAAyB,GAAGjC,MAAM,CAACkC,gBAAgB,CAACP,MAAM,EAAE3B,MAAM,CAACiC,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGf,OAAO,CAACd,MAAM,CAAC6B,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE/B,MAAM,CAACmC,cAAc,CAACR,MAAM,EAAEI,GAAG,EAAE/B,MAAM,CAACsB,wBAAwB,CAACO,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AACzf,SAASK,eAAeA,CAAChD,GAAG,EAAE+C,GAAG,EAAEK,KAAK,EAAE;EAAEL,GAAG,GAAGM,cAAc,CAACN,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI/C,GAAG,EAAE;IAAEgB,MAAM,CAACmC,cAAc,CAACnD,GAAG,EAAE+C,GAAG,EAAE;MAAEK,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEvD,GAAG,CAAC+C,GAAG,CAAC,GAAGK,KAAK;EAAE;EAAE,OAAOpD,GAAG;AAAE;AAC3O,SAASqD,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIT,GAAG,GAAGU,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOzD,OAAO,CAACgD,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGW,MAAM,CAACX,GAAG,CAAC;AAAE;AAC5H,SAASU,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI7D,OAAO,CAAC4D,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC1D,MAAM,CAAC6D,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC3C,IAAI,CAACyC,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI7D,OAAO,CAACiE,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIrD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACiD,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,OAAO,KAAKO,QAAQ,MAAM,yBAAyB;AACnD,SAASC,KAAK,IAAIC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,yBAAyB;AAC3J,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,gBAAgB;AAC5E,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,aAAa;AACzG,SAASC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,cAAc,QAAQ,cAAc;AAC1F;AACA;;AAEA,OAAO,SAASC,iBAAiBA,CAACxF,GAAG,EAAEyF,OAAO,EAAEC,YAAY,EAAE;EAC5D,IAAI5F,MAAM,CAACE,GAAG,CAAC,IAAIF,MAAM,CAAC2F,OAAO,CAAC,EAAE;IAClC,OAAOC,YAAY;EACrB;EACA,IAAIT,UAAU,CAACQ,OAAO,CAAC,EAAE;IACvB,OAAO5F,IAAI,CAACG,GAAG,EAAEyF,OAAO,EAAEC,YAAY,CAAC;EACzC;EACA,IAAI9F,WAAW,CAAC6F,OAAO,CAAC,EAAE;IACxB,OAAOA,OAAO,CAACzF,GAAG,CAAC;EACrB;EACA,OAAO0F,YAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE7C,GAAG,EAAE8C,IAAI,EAAEC,SAAS,EAAE;EAC/D,IAAIC,WAAW,GAAGpG,QAAQ,CAACiG,IAAI,EAAE,UAAUI,KAAK,EAAE;IAChD,OAAOR,iBAAiB,CAACQ,KAAK,EAAEjD,GAAG,CAAC;EACtC,CAAC,CAAC;EACF,IAAI8C,IAAI,KAAK,QAAQ,EAAE;IACrB,IAAII,MAAM,GAAGF,WAAW,CAAC3D,MAAM,CAAC,UAAU4D,KAAK,EAAE;MAC/C,OAAOhB,QAAQ,CAACgB,KAAK,CAAC,IAAIE,UAAU,CAACF,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOC,MAAM,CAACtE,MAAM,GAAG,CAACjC,IAAI,CAACuG,MAAM,CAAC,EAAExG,IAAI,CAACwG,MAAM,CAAC,CAAC,GAAG,CAACE,QAAQ,EAAE,CAACA,QAAQ,CAAC;EAC7E;EACA,IAAIC,YAAY,GAAGN,SAAS,GAAGC,WAAW,CAAC3D,MAAM,CAAC,UAAU4D,KAAK,EAAE;IACjE,OAAO,CAAClG,MAAM,CAACkG,KAAK,CAAC;EACvB,CAAC,CAAC,GAAGD,WAAW;;EAEhB;EACA,OAAOK,YAAY,CAACC,GAAG,CAAC,UAAUL,KAAK,EAAE;IACvC,OAAOf,UAAU,CAACe,KAAK,CAAC,IAAIA,KAAK,YAAYM,IAAI,GAAGN,KAAK,GAAG,EAAE;EAChE,CAAC,CAAC;AACJ;AACA,OAAO,IAAIO,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,UAAU,EAAE;EAClF,IAAIC,aAAa;EACjB,IAAIC,KAAK,GAAG9D,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKmB,SAAS,GAAGnB,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAClF,IAAI+D,aAAa,GAAG/D,SAAS,CAACjB,MAAM,GAAG,CAAC,GAAGiB,SAAS,CAAC,CAAC,CAAC,GAAGmB,SAAS;EACnE,IAAI6C,IAAI,GAAGhE,SAAS,CAACjB,MAAM,GAAG,CAAC,GAAGiB,SAAS,CAAC,CAAC,CAAC,GAAGmB,SAAS;EAC1D,IAAI8C,KAAK,GAAG,CAAC,CAAC;EACd,IAAInF,GAAG,GAAG,CAAC+E,aAAa,GAAGC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/E,MAAM,MAAM,IAAI,IAAI8E,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;;EAE/I;EACA,IAAI/E,GAAG,IAAI,CAAC,EAAE;IACZ,OAAO,CAAC;EACV;EACA,IAAIkF,IAAI,IAAIA,IAAI,CAACE,QAAQ,KAAK,WAAW,IAAIC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,GAAGL,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE;IAC5G,IAAIA,KAAK,GAAGL,IAAI,CAACK,KAAK;IACtB;IACA,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAIsF,MAAM,GAAGtF,CAAC,GAAG,CAAC,GAAG+E,aAAa,CAAC/E,CAAC,GAAG,CAAC,CAAC,CAAC4E,UAAU,GAAGG,aAAa,CAACjF,GAAG,GAAG,CAAC,CAAC,CAAC8E,UAAU;MACxF,IAAIW,GAAG,GAAGR,aAAa,CAAC/E,CAAC,CAAC,CAAC4E,UAAU;MACrC,IAAIY,KAAK,GAAGxF,CAAC,IAAIF,GAAG,GAAG,CAAC,GAAGiF,aAAa,CAAC,CAAC,CAAC,CAACH,UAAU,GAAGG,aAAa,CAAC/E,CAAC,GAAG,CAAC,CAAC,CAAC4E,UAAU;MACxF,IAAIa,kBAAkB,GAAG,KAAK,CAAC;MAC/B,IAAInC,QAAQ,CAACiC,GAAG,GAAGD,MAAM,CAAC,KAAKhC,QAAQ,CAACkC,KAAK,GAAGD,GAAG,CAAC,EAAE;QACpD,IAAIG,YAAY,GAAG,EAAE;QACrB,IAAIpC,QAAQ,CAACkC,KAAK,GAAGD,GAAG,CAAC,KAAKjC,QAAQ,CAAC+B,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3DI,kBAAkB,GAAGD,KAAK;UAC1B,IAAIG,UAAU,GAAGJ,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC1CK,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACS,GAAG,CAACD,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;UACjEI,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACU,GAAG,CAACF,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;QACnE,CAAC,MAAM;UACLG,kBAAkB,GAAGH,MAAM;UAC3B,IAAIQ,YAAY,GAAGN,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC9CK,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACS,GAAG,CAACL,GAAG,EAAE,CAACO,YAAY,GAAGP,GAAG,IAAI,CAAC,CAAC;UACzDG,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACU,GAAG,CAACN,GAAG,EAAE,CAACO,YAAY,GAAGP,GAAG,IAAI,CAAC,CAAC;QAC3D;QACA,IAAIQ,YAAY,GAAG,CAACZ,IAAI,CAACS,GAAG,CAACL,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,EAAEJ,IAAI,CAACU,GAAG,CAACN,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,CAAC;QACjH,IAAIX,UAAU,GAAGmB,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,IAAImB,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,IAAIc,YAAY,CAAC,CAAC,CAAC,IAAId,UAAU,IAAIc,YAAY,CAAC,CAAC,CAAC,EAAE;UACnIT,KAAK,GAAGF,aAAa,CAAC/E,CAAC,CAAC,CAACiF,KAAK;UAC9B;QACF;MACF,CAAC,MAAM;QACL,IAAIW,GAAG,GAAGT,IAAI,CAACS,GAAG,CAACN,MAAM,EAAEE,KAAK,CAAC;QACjC,IAAIK,GAAG,GAAGV,IAAI,CAACU,GAAG,CAACP,MAAM,EAAEE,KAAK,CAAC;QACjC,IAAIZ,UAAU,GAAG,CAACgB,GAAG,GAAGL,GAAG,IAAI,CAAC,IAAIX,UAAU,IAAI,CAACiB,GAAG,GAAGN,GAAG,IAAI,CAAC,EAAE;UACjEN,KAAK,GAAGF,aAAa,CAAC/E,CAAC,CAAC,CAACiF,KAAK;UAC9B;QACF;MACF;IACF;EACF,CAAC,MAAM;IACL;IACA,KAAK,IAAIe,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlG,GAAG,EAAEkG,EAAE,EAAE,EAAE;MAC/B,IAAIA,EAAE,KAAK,CAAC,IAAIpB,UAAU,IAAI,CAACE,KAAK,CAACkB,EAAE,CAAC,CAACpB,UAAU,GAAGE,KAAK,CAACkB,EAAE,GAAG,CAAC,CAAC,CAACpB,UAAU,IAAI,CAAC,IAAIoB,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGlG,GAAG,GAAG,CAAC,IAAI8E,UAAU,GAAG,CAACE,KAAK,CAACkB,EAAE,CAAC,CAACpB,UAAU,GAAGE,KAAK,CAACkB,EAAE,GAAG,CAAC,CAAC,CAACpB,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,CAACE,KAAK,CAACkB,EAAE,CAAC,CAACpB,UAAU,GAAGE,KAAK,CAACkB,EAAE,GAAG,CAAC,CAAC,CAACpB,UAAU,IAAI,CAAC,IAAIoB,EAAE,KAAKlG,GAAG,GAAG,CAAC,IAAI8E,UAAU,GAAG,CAACE,KAAK,CAACkB,EAAE,CAAC,CAACpB,UAAU,GAAGE,KAAK,CAACkB,EAAE,GAAG,CAAC,CAAC,CAACpB,UAAU,IAAI,CAAC,EAAE;QAClVK,KAAK,GAAGH,KAAK,CAACkB,EAAE,CAAC,CAACf,KAAK;QACvB;MACF;IACF;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgB,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,IAAI,EAAE;EAC9E,IAAIC,IAAI,GAAGD,IAAI;IACbE,WAAW,GAAGD,IAAI,CAAClC,IAAI,CAACmC,WAAW,CAAC,CAAC;EACvC,IAAIC,WAAW,GAAGH,IAAI,CAACI,KAAK;IAC1BC,MAAM,GAAGF,WAAW,CAACE,MAAM;IAC3BC,IAAI,GAAGH,WAAW,CAACG,IAAI;EACzB,IAAIC,MAAM;EACV,QAAQL,WAAW;IACjB,KAAK,MAAM;MACTK,MAAM,GAAGF,MAAM;MACf;IACF,KAAK,MAAM;IACX,KAAK,OAAO;MACVE,MAAM,GAAGF,MAAM,IAAIA,MAAM,KAAK,MAAM,GAAGA,MAAM,GAAGC,IAAI;MACpD;IACF;MACEC,MAAM,GAAGD,IAAI;MACb;EACJ;EACA,OAAOC,MAAM;AACf,CAAC;AACD,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3BC,uBAAuB,GAAGF,KAAK,CAACE,uBAAuB;IACvDC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,aAAa,GAAGJ,KAAK,CAACI,aAAa;EACrC,IAAIC,UAAU,GAAGtD,eAAe,CAACkD,QAAQ,EAAE3D,MAAM,CAAC;EAClD,IAAI,CAAC+D,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAIC,UAAU;EACd,IAAID,UAAU,CAACV,KAAK,IAAIU,UAAU,CAACV,KAAK,CAACY,OAAO,EAAE;IAChDD,UAAU,GAAGD,UAAU,CAACV,KAAK,IAAIU,UAAU,CAACV,KAAK,CAACY,OAAO;EAC3D,CAAC,MAAM,IAAIH,aAAa,KAAK,UAAU,EAAE;IACvCE,UAAU,GAAG,CAACJ,uBAAuB,IAAI,EAAE,EAAEM,MAAM,CAAC,UAAUV,MAAM,EAAEW,KAAK,EAAE;MAC3E,IAAIlB,IAAI,GAAGkB,KAAK,CAAClB,IAAI;QACnBI,KAAK,GAAGc,KAAK,CAACd,KAAK;MACrB,IAAItC,IAAI,GAAGsC,KAAK,CAACe,OAAO,IAAIf,KAAK,CAACtC,IAAI,IAAI,EAAE;MAC5C,OAAOyC,MAAM,CAACa,MAAM,CAACtD,IAAI,CAACS,GAAG,CAAC,UAAUL,KAAK,EAAE;QAC7C,OAAO;UACLH,IAAI,EAAE+C,UAAU,CAACV,KAAK,CAACiB,QAAQ,IAAIrB,IAAI,CAACI,KAAK,CAACkB,UAAU;UACxDhG,KAAK,EAAE4C,KAAK,CAAC5E,IAAI;UACjBiI,KAAK,EAAErD,KAAK,CAACoC,IAAI;UACjBU,OAAO,EAAE9C;QACX,CAAC;MACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,MAAM;IACL6C,UAAU,GAAG,CAACJ,uBAAuB,IAAI,EAAE,EAAEpC,GAAG,CAAC,UAAUiD,KAAK,EAAE;MAChE,IAAIxB,IAAI,GAAGwB,KAAK,CAACxB,IAAI;MACrB,IAAIyB,YAAY,GAAGzB,IAAI,CAACI,KAAK;QAC3BzC,OAAO,GAAG8D,YAAY,CAAC9D,OAAO;QAC9BrE,IAAI,GAAGmI,YAAY,CAACnI,IAAI;QACxBgI,UAAU,GAAGG,YAAY,CAACH,UAAU;QACpCI,IAAI,GAAGD,YAAY,CAACC,IAAI;MAC1B,OAAO;QACLC,QAAQ,EAAED,IAAI;QACd/D,OAAO,EAAEA,OAAO;QAChBI,IAAI,EAAE+C,UAAU,CAACV,KAAK,CAACiB,QAAQ,IAAIC,UAAU,IAAI,QAAQ;QACzDC,KAAK,EAAExB,yBAAyB,CAACC,IAAI,CAAC;QACtC1E,KAAK,EAAEhC,IAAI,IAAIqE,OAAO;QACtBqD,OAAO,EAAEhB,IAAI,CAACI;MAChB,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOxF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,UAAU,CAACV,KAAK,CAAC,EAAErD,MAAM,CAAC6E,aAAa,CAACd,UAAU,EAAEF,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HI,OAAO,EAAED,UAAU;IACnBf,IAAI,EAAEc;EACR,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,UAAU,GAAGD,KAAK,CAACE,OAAO;IAC5BC,iBAAiB,GAAGH,KAAK,CAACI,WAAW;IACrCA,WAAW,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;EACrE,IAAI,CAACC,WAAW,EAAE;IAChB,OAAO,CAAC,CAAC;EACX;EACA,IAAI3B,MAAM,GAAG,CAAC,CAAC;EACf,IAAI4B,cAAc,GAAGjJ,MAAM,CAACiB,IAAI,CAAC+H,WAAW,CAAC;EAC7C,KAAK,IAAIpI,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGuI,cAAc,CAACtI,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IACzD,IAAIsI,GAAG,GAAGF,WAAW,CAACC,cAAc,CAACrI,CAAC,CAAC,CAAC,CAACoI,WAAW;IACpD,IAAIG,QAAQ,GAAGnJ,MAAM,CAACiB,IAAI,CAACiI,GAAG,CAAC;IAC/B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGF,QAAQ,CAACxI,MAAM,EAAEyI,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;MACrD,IAAIE,eAAe,GAAGJ,GAAG,CAACC,QAAQ,CAACC,CAAC,CAAC,CAAC;QACpCG,KAAK,GAAGD,eAAe,CAACC,KAAK;QAC7BC,UAAU,GAAGF,eAAe,CAACE,UAAU;MACzC,IAAIC,QAAQ,GAAGF,KAAK,CAACnI,MAAM,CAAC,UAAU0F,IAAI,EAAE;QAC1C,OAAOvC,cAAc,CAACuC,IAAI,CAACjC,IAAI,CAAC,CAAC6E,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;MACtD,CAAC,CAAC;MACF,IAAID,QAAQ,IAAIA,QAAQ,CAAC9I,MAAM,EAAE;QAC/B,IAAIgJ,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC,CAACvC,KAAK,CAAC4B,OAAO;QACxC,IAAIc,MAAM,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAACvC,KAAK,CAACsC,UAAU,CAAC;QAC1C,IAAI,CAACnC,MAAM,CAACuC,MAAM,CAAC,EAAE;UACnBvC,MAAM,CAACuC,MAAM,CAAC,GAAG,EAAE;QACrB;QACAvC,MAAM,CAACuC,MAAM,CAAC,CAACpI,IAAI,CAAC;UAClBsF,IAAI,EAAE2C,QAAQ,CAAC,CAAC,CAAC;UACjBI,SAAS,EAAEJ,QAAQ,CAACtJ,KAAK,CAAC,CAAC,CAAC;UAC5B2I,OAAO,EAAEhK,MAAM,CAAC6K,QAAQ,CAAC,GAAGd,UAAU,GAAGc;QAC3C,CAAC,CAAC;MACJ;IACF;EACF;EACA,OAAOtC,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,cAAc,GAAGJ,KAAK,CAACK,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;IAC1DE,UAAU,GAAGN,KAAK,CAACM,UAAU;EAC/B,IAAI3J,GAAG,GAAG0J,QAAQ,CAACzJ,MAAM;EACzB,IAAID,GAAG,GAAG,CAAC,EAAE,OAAO,IAAI;EACxB,IAAI4J,UAAU,GAAGvG,eAAe,CAACiG,MAAM,EAAEE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;EAC3D,IAAI7C,MAAM;;EAEV;EACA,IAAI+C,QAAQ,CAAC,CAAC,CAAC,CAACtB,OAAO,KAAK,CAACsB,QAAQ,CAAC,CAAC,CAAC,CAACtB,OAAO,EAAE;IAChD,IAAIyB,OAAO,GAAG,KAAK;IACnB,IAAIC,WAAW,GAAGN,QAAQ,GAAGxJ,GAAG;IAChC,IAAI+J,GAAG,GAAGL,QAAQ,CAACrC,MAAM,CAAC,UAAU/E,GAAG,EAAEgC,KAAK,EAAE;MAC9C,OAAOhC,GAAG,GAAGgC,KAAK,CAAC8D,OAAO,IAAI,CAAC;IACjC,CAAC,EAAE,CAAC,CAAC;IACL2B,GAAG,IAAI,CAAC/J,GAAG,GAAG,CAAC,IAAI4J,UAAU;IAC7B,IAAIG,GAAG,IAAIP,QAAQ,EAAE;MACnBO,GAAG,IAAI,CAAC/J,GAAG,GAAG,CAAC,IAAI4J,UAAU;MAC7BA,UAAU,GAAG,CAAC;IAChB;IACA,IAAIG,GAAG,IAAIP,QAAQ,IAAIM,WAAW,GAAG,CAAC,EAAE;MACtCD,OAAO,GAAG,IAAI;MACdC,WAAW,IAAI,GAAG;MAClBC,GAAG,GAAG/J,GAAG,GAAG8J,WAAW;IACzB;IACA,IAAIE,MAAM,GAAG,CAACR,QAAQ,GAAGO,GAAG,IAAI,CAAC,IAAI,CAAC;IACtC,IAAIE,IAAI,GAAG;MACTD,MAAM,EAAEA,MAAM,GAAGJ,UAAU;MAC3BM,IAAI,EAAE;IACR,CAAC;IACDvD,MAAM,GAAG+C,QAAQ,CAACrC,MAAM,CAAC,UAAU/E,GAAG,EAAEgC,KAAK,EAAE;MAC7C,IAAI6F,MAAM,GAAG,EAAE,CAAC3C,MAAM,CAAC7I,kBAAkB,CAAC2D,GAAG,CAAC,EAAE,CAAC;QAC/C8D,IAAI,EAAE9B,KAAK,CAAC8B,IAAI;QAChBgE,QAAQ,EAAE;UACRJ,MAAM,EAAEC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI,GAAGN,UAAU;UAC5CM,IAAI,EAAEL,OAAO,GAAGC,WAAW,GAAGxF,KAAK,CAAC8D;QACtC;MACF,CAAC,CAAC,CAAC;MACH6B,IAAI,GAAGE,MAAM,CAACA,MAAM,CAAClK,MAAM,GAAG,CAAC,CAAC,CAACmK,QAAQ;MACzC,IAAI9F,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC6E,SAAS,CAAClJ,MAAM,EAAE;QAC7CqE,KAAK,CAAC6E,SAAS,CAAC/H,OAAO,CAAC,UAAUgF,IAAI,EAAE;UACtC+D,MAAM,CAACrJ,IAAI,CAAC;YACVsF,IAAI,EAAEA,IAAI;YACVgE,QAAQ,EAAEH;UACZ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOE,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,MAAM;IACL,IAAIE,OAAO,GAAGhH,eAAe,CAACkG,cAAc,EAAEC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;IAChE,IAAIA,QAAQ,GAAG,CAAC,GAAGa,OAAO,GAAG,CAACrK,GAAG,GAAG,CAAC,IAAI4J,UAAU,IAAI,CAAC,EAAE;MACxDA,UAAU,GAAG,CAAC;IAChB;IACA,IAAIU,YAAY,GAAG,CAACd,QAAQ,GAAG,CAAC,GAAGa,OAAO,GAAG,CAACrK,GAAG,GAAG,CAAC,IAAI4J,UAAU,IAAI5J,GAAG;IAC1E,IAAIsK,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,KAAK,CAAC;IACpB;IACA,IAAIJ,IAAI,GAAGP,UAAU,KAAK,CAACA,UAAU,GAAGtE,IAAI,CAACS,GAAG,CAACwE,YAAY,EAAEX,UAAU,CAAC,GAAGW,YAAY;IACzF3D,MAAM,GAAG+C,QAAQ,CAACrC,MAAM,CAAC,UAAU/E,GAAG,EAAEgC,KAAK,EAAEpE,CAAC,EAAE;MAChD,IAAIiK,MAAM,GAAG,EAAE,CAAC3C,MAAM,CAAC7I,kBAAkB,CAAC2D,GAAG,CAAC,EAAE,CAAC;QAC/C8D,IAAI,EAAE9B,KAAK,CAAC8B,IAAI;QAChBgE,QAAQ,EAAE;UACRJ,MAAM,EAAEK,OAAO,GAAG,CAACC,YAAY,GAAGV,UAAU,IAAI1J,CAAC,GAAG,CAACoK,YAAY,GAAGJ,IAAI,IAAI,CAAC;UAC7EA,IAAI,EAAEA;QACR;MACF,CAAC,CAAC,CAAC;MACH,IAAI5F,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC6E,SAAS,CAAClJ,MAAM,EAAE;QAC7CqE,KAAK,CAAC6E,SAAS,CAAC/H,OAAO,CAAC,UAAUgF,IAAI,EAAE;UACtC+D,MAAM,CAACrJ,IAAI,CAAC;YACVsF,IAAI,EAAEA,IAAI;YACVgE,QAAQ,EAAED,MAAM,CAACA,MAAM,CAAClK,MAAM,GAAG,CAAC,CAAC,CAACmK;UACtC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOD,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR;EACA,OAAOxD,MAAM;AACf,CAAC;AACD,OAAO,IAAI4D,oBAAoB,GAAG,SAASA,oBAAoBA,CAACP,MAAM,EAAEnB,KAAK,EAAErC,KAAK,EAAEgE,SAAS,EAAE;EAC/F,IAAI1D,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IAC3B2D,KAAK,GAAGjE,KAAK,CAACiE,KAAK;IACnBC,MAAM,GAAGlE,KAAK,CAACkE,MAAM;EACvB,IAAI1D,WAAW,GAAGyD,KAAK,IAAIC,MAAM,CAACC,IAAI,IAAI,CAAC,CAAC,IAAID,MAAM,CAACE,KAAK,IAAI,CAAC,CAAC;EAClE;EACA,IAAIC,WAAW,GAAGjE,cAAc,CAAC;IAC/BE,QAAQ,EAAEA,QAAQ;IAClBE,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAI8D,SAAS,GAAGd,MAAM;EACtB,IAAIa,WAAW,EAAE;IACf,IAAIE,GAAG,GAAGP,SAAS,IAAI,CAAC,CAAC;IACzB,IAAIQ,KAAK,GAAGH,WAAW,CAACG,KAAK;MAC3BC,aAAa,GAAGJ,WAAW,CAACI,aAAa;MACzCC,MAAM,GAAGL,WAAW,CAACK,MAAM;IAC7B,IAAI,CAACA,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,YAAY,IAAID,aAAa,KAAK,QAAQ,KAAK3H,QAAQ,CAAC0G,MAAM,CAACgB,KAAK,CAAC,CAAC,EAAE;MAC/GF,SAAS,GAAG9J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgJ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE1I,eAAe,CAAC,CAAC,CAAC,EAAE0J,KAAK,EAAEF,SAAS,CAACE,KAAK,CAAC,IAAID,GAAG,CAACN,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3H;IACA,IAAI,CAACS,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIF,KAAK,KAAK,QAAQ,KAAK1H,QAAQ,CAAC0G,MAAM,CAACiB,aAAa,CAAC,CAAC,EAAE;MAC/GH,SAAS,GAAG9J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgJ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE1I,eAAe,CAAC,CAAC,CAAC,EAAE2J,aAAa,EAAEH,SAAS,CAACG,aAAa,CAAC,IAAIF,GAAG,CAACI,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5I;EACF;EACA,OAAOL,SAAS;AAClB,CAAC;AACD,IAAIM,yBAAyB,GAAG,SAASA,yBAAyBA,CAACF,MAAM,EAAE9F,QAAQ,EAAEiG,SAAS,EAAE;EAC9F,IAAIjN,MAAM,CAACgH,QAAQ,CAAC,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAI8F,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO9F,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAI8F,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO9F,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAIiG,SAAS,KAAK,GAAG,EAAE;IACrB,OAAOjG,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAIiG,SAAS,KAAK,GAAG,EAAE;IACrB,OAAOjG,QAAQ,KAAK,OAAO;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIkG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACpH,IAAI,EAAEkC,IAAI,EAAErC,OAAO,EAAEmH,MAAM,EAAE9F,QAAQ,EAAE;EACrG,IAAI0B,QAAQ,GAAGV,IAAI,CAACI,KAAK,CAACM,QAAQ;EAClC,IAAIyE,SAAS,GAAG5H,aAAa,CAACmD,QAAQ,EAAE5D,QAAQ,CAAC,CAACxC,MAAM,CAAC,UAAU8K,aAAa,EAAE;IAChF,OAAOJ,yBAAyB,CAACF,MAAM,EAAE9F,QAAQ,EAAEoG,aAAa,CAAChF,KAAK,CAAC6E,SAAS,CAAC;EACnF,CAAC,CAAC;EACF,IAAIE,SAAS,IAAIA,SAAS,CAACtL,MAAM,EAAE;IACjC,IAAIM,IAAI,GAAGgL,SAAS,CAAC5G,GAAG,CAAC,UAAU6G,aAAa,EAAE;MAChD,OAAOA,aAAa,CAAChF,KAAK,CAACzC,OAAO;IACpC,CAAC,CAAC;IACF,OAAOG,IAAI,CAACmD,MAAM,CAAC,UAAUV,MAAM,EAAErC,KAAK,EAAE;MAC1C,IAAImH,UAAU,GAAG3H,iBAAiB,CAACQ,KAAK,EAAEP,OAAO,EAAE,CAAC,CAAC;MACrD,IAAI2H,SAAS,GAAG5N,QAAQ,CAAC2N,UAAU,CAAC,GAAG,CAACzN,IAAI,CAACyN,UAAU,CAAC,EAAE1N,IAAI,CAAC0N,UAAU,CAAC,CAAC,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;MACtG,IAAIE,WAAW,GAAGpL,IAAI,CAAC8G,MAAM,CAAC,UAAUuE,YAAY,EAAEC,CAAC,EAAE;QACvD,IAAIC,UAAU,GAAGhI,iBAAiB,CAACQ,KAAK,EAAEuH,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAIE,UAAU,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAGrG,IAAI,CAACC,GAAG,CAACxH,QAAQ,CAACgO,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC;QAC3F,IAAIE,UAAU,GAAGN,SAAS,CAAC,CAAC,CAAC,GAAGrG,IAAI,CAACC,GAAG,CAACxH,QAAQ,CAACgO,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC;QAC3F,OAAO,CAACzG,IAAI,CAACS,GAAG,CAACiG,UAAU,EAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEvG,IAAI,CAACU,GAAG,CAACiG,UAAU,EAAEJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,EAAE,CAACnH,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;MACzB,OAAO,CAACY,IAAI,CAACS,GAAG,CAAC6F,WAAW,CAAC,CAAC,CAAC,EAAEhF,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEtB,IAAI,CAACU,GAAG,CAAC4F,WAAW,CAAC,CAAC,CAAC,EAAEhF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC,EAAE,CAAClC,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIwH,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC/H,IAAI,EAAE2E,KAAK,EAAE9E,OAAO,EAAEqB,QAAQ,EAAE8F,MAAM,EAAE;EACtG,IAAIgB,OAAO,GAAGrD,KAAK,CAAClE,GAAG,CAAC,UAAUyB,IAAI,EAAE;IACtC,OAAOkF,oBAAoB,CAACpH,IAAI,EAAEkC,IAAI,EAAErC,OAAO,EAAEmH,MAAM,EAAE9F,QAAQ,CAAC;EACpE,CAAC,CAAC,CAAC1E,MAAM,CAAC,UAAU4D,KAAK,EAAE;IACzB,OAAO,CAAClG,MAAM,CAACkG,KAAK,CAAC;EACvB,CAAC,CAAC;EACF,IAAI4H,OAAO,IAAIA,OAAO,CAACjM,MAAM,EAAE;IAC7B,OAAOiM,OAAO,CAAC7E,MAAM,CAAC,UAAUV,MAAM,EAAErC,KAAK,EAAE;MAC7C,OAAO,CAACe,IAAI,CAACS,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEe,IAAI,CAACU,GAAG,CAACY,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE,CAACG,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI0H,4BAA4B,GAAG,SAASA,4BAA4BA,CAACjI,IAAI,EAAE2E,KAAK,EAAE1E,IAAI,EAAE+G,MAAM,EAAE9G,SAAS,EAAE;EACpH,IAAI8H,OAAO,GAAGrD,KAAK,CAAClE,GAAG,CAAC,UAAUyB,IAAI,EAAE;IACtC,IAAIrC,OAAO,GAAGqC,IAAI,CAACI,KAAK,CAACzC,OAAO;IAChC,IAAII,IAAI,KAAK,QAAQ,IAAIJ,OAAO,EAAE;MAChC,OAAOuH,oBAAoB,CAACpH,IAAI,EAAEkC,IAAI,EAAErC,OAAO,EAAEmH,MAAM,CAAC,IAAIjH,oBAAoB,CAACC,IAAI,EAAEH,OAAO,EAAEI,IAAI,EAAEC,SAAS,CAAC;IAClH;IACA,OAAOH,oBAAoB,CAACC,IAAI,EAAEH,OAAO,EAAEI,IAAI,EAAEC,SAAS,CAAC;EAC7D,CAAC,CAAC;EACF,IAAID,IAAI,KAAK,QAAQ,EAAE;IACrB;IACA,OAAO+H,OAAO,CAAC7E,MAAM,CAAC,UAAUV,MAAM,EAAErC,KAAK,EAAE;MAC7C,OAAO,CAACe,IAAI,CAACS,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEe,IAAI,CAACU,GAAG,CAACY,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE,CAACG,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,IAAI2H,GAAG,GAAG,CAAC,CAAC;EACZ;EACA,OAAOF,OAAO,CAAC7E,MAAM,CAAC,UAAUV,MAAM,EAAErC,KAAK,EAAE;IAC7C,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGsE,KAAK,CAACrE,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD,IAAI,CAACkM,GAAG,CAAC9H,KAAK,CAACpE,CAAC,CAAC,CAAC,EAAE;QAClBkM,GAAG,CAAC9H,KAAK,CAACpE,CAAC,CAAC,CAAC,GAAG,IAAI;QACpByG,MAAM,CAAC7F,IAAI,CAACwD,KAAK,CAACpE,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOyG,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,OAAO,IAAI0F,iBAAiB,GAAG,SAASA,iBAAiBA,CAACnB,MAAM,EAAE9F,QAAQ,EAAE;EAC1E,OAAO8F,MAAM,KAAK,YAAY,IAAI9F,QAAQ,KAAK,OAAO,IAAI8F,MAAM,KAAK,UAAU,IAAI9F,QAAQ,KAAK,OAAO,IAAI8F,MAAM,KAAK,SAAS,IAAI9F,QAAQ,KAAK,WAAW,IAAI8F,MAAM,KAAK,QAAQ,IAAI9F,QAAQ,KAAK,YAAY;AACjN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIkH,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtH,KAAK,EAAEc,GAAG,EAAEC,GAAG,EAAE;EAC/E,IAAIwG,MAAM,EAAEC,MAAM;EAClB,IAAIC,MAAM,GAAGzH,KAAK,CAACL,GAAG,CAAC,UAAUL,KAAK,EAAE;IACtC,IAAIA,KAAK,CAACQ,UAAU,KAAKgB,GAAG,EAAE;MAC5ByG,MAAM,GAAG,IAAI;IACf;IACA,IAAIjI,KAAK,CAACQ,UAAU,KAAKiB,GAAG,EAAE;MAC5ByG,MAAM,GAAG,IAAI;IACf;IACA,OAAOlI,KAAK,CAACQ,UAAU;EACzB,CAAC,CAAC;EACF,IAAI,CAACyH,MAAM,EAAE;IACXE,MAAM,CAAC3L,IAAI,CAACgF,GAAG,CAAC;EAClB;EACA,IAAI,CAAC0G,MAAM,EAAE;IACXC,MAAM,CAAC3L,IAAI,CAACiF,GAAG,CAAC;EAClB;EACA,OAAO0G,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACxH,IAAI,EAAEyH,MAAM,EAAEC,KAAK,EAAE;EACvE,IAAI,CAAC1H,IAAI,EAAE,OAAO,IAAI;EACtB,IAAI2H,KAAK,GAAG3H,IAAI,CAAC2H,KAAK;EACtB,IAAIC,eAAe,GAAG5H,IAAI,CAAC4H,eAAe;IACxC3I,IAAI,GAAGe,IAAI,CAACf,IAAI;IAChBoB,KAAK,GAAGL,IAAI,CAACK,KAAK;EACpB,IAAIwH,aAAa,GAAG7H,IAAI,CAAC8H,aAAa,KAAK,WAAW,GAAGH,KAAK,CAACI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAClF,IAAIjD,MAAM,GAAG,CAAC2C,MAAM,IAAIC,KAAK,KAAKzI,IAAI,KAAK,UAAU,IAAI0I,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACI,SAAS,CAAC,CAAC,GAAGF,aAAa,GAAG,CAAC;EAChH/C,MAAM,GAAG9E,IAAI,CAACE,QAAQ,KAAK,WAAW,IAAI,CAACG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtF,MAAM,KAAK,CAAC,GAAGuD,QAAQ,CAAC+B,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGyE,MAAM,GAAGA,MAAM;;EAEjK;EACA,IAAI2C,MAAM,KAAKzH,IAAI,CAACF,KAAK,IAAIE,IAAI,CAACgI,SAAS,CAAC,EAAE;IAC5C,IAAIvG,MAAM,GAAG,CAACzB,IAAI,CAACF,KAAK,IAAIE,IAAI,CAACgI,SAAS,EAAEvI,GAAG,CAAC,UAAUL,KAAK,EAAE;MAC/D,IAAI6I,YAAY,GAAGL,eAAe,GAAGA,eAAe,CAAC9D,OAAO,CAAC1E,KAAK,CAAC,GAAGA,KAAK;MAC3E,OAAO;QACL;QACA;QACAQ,UAAU,EAAE+H,KAAK,CAACM,YAAY,CAAC,GAAGnD,MAAM;QACxCtI,KAAK,EAAE4C,KAAK;QACZ0F,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;IACF,OAAOrD,MAAM,CAACjG,MAAM,CAAC,UAAU0M,GAAG,EAAE;MAClC,OAAO,CAACvP,MAAM,CAACuP,GAAG,CAACtI,UAAU,CAAC;IAChC,CAAC,CAAC;EACJ;;EAEA;EACA,IAAII,IAAI,CAACmI,aAAa,IAAInI,IAAI,CAACoI,iBAAiB,EAAE;IAChD,OAAOpI,IAAI,CAACoI,iBAAiB,CAAC3I,GAAG,CAAC,UAAUL,KAAK,EAAEa,KAAK,EAAE;MACxD,OAAO;QACLL,UAAU,EAAE+H,KAAK,CAACvI,KAAK,CAAC,GAAG0F,MAAM;QACjCtI,KAAK,EAAE4C,KAAK;QACZa,KAAK,EAAEA,KAAK;QACZ6E,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;EACJ;EACA,IAAI6C,KAAK,CAAC7H,KAAK,IAAI,CAAC4H,KAAK,EAAE;IACzB,OAAOC,KAAK,CAAC7H,KAAK,CAACE,IAAI,CAACqI,SAAS,CAAC,CAAC5I,GAAG,CAAC,UAAUL,KAAK,EAAE;MACtD,OAAO;QACLQ,UAAU,EAAE+H,KAAK,CAACvI,KAAK,CAAC,GAAG0F,MAAM;QACjCtI,KAAK,EAAE4C,KAAK;QACZ0F,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,OAAO6C,KAAK,CAACtI,MAAM,CAAC,CAAC,CAACI,GAAG,CAAC,UAAUL,KAAK,EAAEa,KAAK,EAAE;IAChD,OAAO;MACLL,UAAU,EAAE+H,KAAK,CAACvI,KAAK,CAAC,GAAG0F,MAAM;MACjCtI,KAAK,EAAEoL,eAAe,GAAGA,eAAe,CAACxI,KAAK,CAAC,GAAGA,KAAK;MACvDa,KAAK,EAAEA,KAAK;MACZ6E,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIwD,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAE;EAC3G,IAAIC,iBAAiB;EACrB,IAAI1P,WAAW,CAACyP,YAAY,CAAC,EAAE;IAC7BC,iBAAiB,GAAGD,YAAY;EAClC,CAAC,MAAM,IAAIzP,WAAW,CAACwP,aAAa,CAAC,EAAE;IACrCE,iBAAiB,GAAGF,aAAa;EACnC;EACA,IAAIxP,WAAW,CAACuP,cAAc,CAAC,IAAIG,iBAAiB,EAAE;IACpD,OAAO,UAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MACvC,IAAI9P,WAAW,CAACuP,cAAc,CAAC,EAAE;QAC/BA,cAAc,CAACI,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MACxC;MACA,IAAI9P,WAAW,CAAC0P,iBAAiB,CAAC,EAAE;QAClCA,iBAAiB,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAC3C;IACF,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAC/I,IAAI,EAAEgJ,SAAS,EAAEC,MAAM,EAAE;EACnE,IAAItB,KAAK,GAAG3H,IAAI,CAAC2H,KAAK;IACpB1I,IAAI,GAAGe,IAAI,CAACf,IAAI;IAChB+G,MAAM,GAAGhG,IAAI,CAACgG,MAAM;IACpB9F,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,IAAIyH,KAAK,KAAK,MAAM,EAAE;IACpB,IAAI3B,MAAM,KAAK,QAAQ,IAAI9F,QAAQ,KAAK,YAAY,EAAE;MACpD,OAAO;QACLyH,KAAK,EAAErK,QAAQ,CAAC4L,SAAS,CAAC,CAAC;QAC3BpB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAI9B,MAAM,KAAK,QAAQ,IAAI9F,QAAQ,KAAK,WAAW,EAAE;MACnD,OAAO;QACLyH,KAAK,EAAErK,QAAQ,CAAC6L,WAAW,CAAC,CAAC;QAC7BrB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAI7I,IAAI,KAAK,UAAU,IAAI+J,SAAS,KAAKA,SAAS,CAAClF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAIkF,SAAS,CAAClF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAIkF,SAAS,CAAClF,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAACmF,MAAM,CAAC,EAAE;MAC1K,OAAO;QACLtB,KAAK,EAAErK,QAAQ,CAAC8L,UAAU,CAAC,CAAC;QAC5BtB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAI7I,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO;QACL0I,KAAK,EAAErK,QAAQ,CAAC4L,SAAS,CAAC,CAAC;QAC3BpB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,OAAO;MACLH,KAAK,EAAErK,QAAQ,CAAC6L,WAAW,CAAC,CAAC;MAC7BrB,aAAa,EAAE;IACjB,CAAC;EACH;EACA,IAAIpP,SAAS,CAACiP,KAAK,CAAC,EAAE;IACpB,IAAInN,IAAI,GAAG,OAAO,CAAC8H,MAAM,CAAC7J,WAAW,CAACkP,KAAK,CAAC,CAAC;IAC7C,OAAO;MACLA,KAAK,EAAE,CAACrK,QAAQ,CAAC9C,IAAI,CAAC,IAAI8C,QAAQ,CAAC8L,UAAU,EAAE,CAAC;MAChDtB,aAAa,EAAExK,QAAQ,CAAC9C,IAAI,CAAC,GAAGA,IAAI,GAAG;IACzC,CAAC;EACH;EACA,OAAOxB,WAAW,CAAC2O,KAAK,CAAC,GAAG;IAC1BA,KAAK,EAAEA;EACT,CAAC,GAAG;IACFA,KAAK,EAAErK,QAAQ,CAAC8L,UAAU,CAAC,CAAC;IAC5BtB,aAAa,EAAE;EACjB,CAAC;AACH,CAAC;AACD,IAAIuB,GAAG,GAAG,IAAI;AACd,OAAO,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3B,KAAK,EAAE;EACjE,IAAItI,MAAM,GAAGsI,KAAK,CAACtI,MAAM,CAAC,CAAC;EAC3B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACtE,MAAM,IAAI,CAAC,EAAE;IACjC;EACF;EACA,IAAID,GAAG,GAAGuE,MAAM,CAACtE,MAAM;EACvB,IAAIsF,KAAK,GAAGsH,KAAK,CAACtH,KAAK,CAAC,CAAC;EACzB,IAAIO,GAAG,GAAGT,IAAI,CAACS,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGgJ,GAAG;EAC5C,IAAIxI,GAAG,GAAGV,IAAI,CAACU,GAAG,CAACR,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGgJ,GAAG;EAC5C,IAAIE,KAAK,GAAG5B,KAAK,CAACtI,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAImK,IAAI,GAAG7B,KAAK,CAACtI,MAAM,CAACvE,GAAG,GAAG,CAAC,CAAC,CAAC;EACjC,IAAIyO,KAAK,GAAG3I,GAAG,IAAI2I,KAAK,GAAG1I,GAAG,IAAI2I,IAAI,GAAG5I,GAAG,IAAI4I,IAAI,GAAG3I,GAAG,EAAE;IAC1D8G,KAAK,CAACtI,MAAM,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAACvE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C;AACF,CAAC;AACD,OAAO,IAAI2O,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC5E,IAAI,CAACD,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EACA,KAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAG4O,WAAW,CAAC3O,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IACtD,IAAI0O,WAAW,CAAC1O,CAAC,CAAC,CAACkG,IAAI,KAAKyI,KAAK,EAAE;MACjC,OAAOD,WAAW,CAAC1O,CAAC,CAAC,CAACkK,QAAQ;IAChC;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAI0E,gBAAgB,GAAG,SAASA,gBAAgBA,CAACpN,KAAK,EAAE6C,MAAM,EAAE;EACrE,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACtE,MAAM,KAAK,CAAC,IAAI,CAACqD,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAACjB,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAClF,OAAO7C,KAAK;EACd;EACA,IAAIoE,GAAG,GAAGT,IAAI,CAACS,GAAG,CAACvB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,IAAIwB,GAAG,GAAGV,IAAI,CAACU,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,IAAIoC,MAAM,GAAG,CAACjF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,IAAI,CAAC4B,QAAQ,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGoE,GAAG,EAAE;IACzCa,MAAM,CAAC,CAAC,CAAC,GAAGb,GAAG;EACjB;EACA,IAAI,CAACxC,QAAQ,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGqE,GAAG,EAAE;IACzCY,MAAM,CAAC,CAAC,CAAC,GAAGZ,GAAG;EACjB;EACA,IAAIY,MAAM,CAAC,CAAC,CAAC,GAAGZ,GAAG,EAAE;IACnBY,MAAM,CAAC,CAAC,CAAC,GAAGZ,GAAG;EACjB;EACA,IAAIY,MAAM,CAAC,CAAC,CAAC,GAAGb,GAAG,EAAE;IACnBa,MAAM,CAAC,CAAC,CAAC,GAAGb,GAAG;EACjB;EACA,OAAOa,MAAM;AACf,CAAC;;AAED;AACA,OAAO,IAAIoI,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAE;EAClD,IAAI3P,CAAC,GAAG2P,MAAM,CAAC/O,MAAM;EACrB,IAAIZ,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIqJ,CAAC,GAAG,CAAC,EAAEuG,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC/O,MAAM,EAAEyI,CAAC,GAAGuG,CAAC,EAAE,EAAEvG,CAAC,EAAE;IAChD,IAAIwG,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIjP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,EAAE,EAAEa,CAAC,EAAE;MAC1B,IAAIwB,KAAK,GAAG7D,MAAM,CAACmR,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvE;MACA,IAAIhH,KAAK,IAAI,CAAC,EAAE;QACdsN,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwG,QAAQ;QAC1BF,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwG,QAAQ,GAAGxN,KAAK;QAClCwN,QAAQ,GAAGF,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGyG,QAAQ;QAC1BH,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGyG,QAAQ,GAAGzN,KAAK;QAClCyN,QAAQ,GAAGH,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA;IACF;EACF;AACF,CAAC;AAED,OAAO,IAAI0G,cAAc,GAAG,SAASA,cAAcA,CAACJ,MAAM,EAAE;EAC1D,IAAI3P,CAAC,GAAG2P,MAAM,CAAC/O,MAAM;EACrB,IAAIZ,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIqJ,CAAC,GAAG,CAAC,EAAEuG,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC/O,MAAM,EAAEyI,CAAC,GAAGuG,CAAC,EAAE,EAAEvG,CAAC,EAAE;IAChD,IAAIwG,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIhP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,EAAE,EAAEa,CAAC,EAAE;MAC1B,IAAIwB,KAAK,GAAG7D,MAAM,CAACmR,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvE;MACA,IAAIhH,KAAK,IAAI,CAAC,EAAE;QACdsN,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwG,QAAQ;QAC1BF,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwG,QAAQ,GAAGxN,KAAK;QAClCwN,QAAQ,GAAGF,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACnBsG,MAAM,CAAC9O,CAAC,CAAC,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACrB;MACA;IACF;EACF;AACF,CAAC;AAED,IAAI2G,gBAAgB,GAAG;EACrBC,IAAI,EAAEP,UAAU;EAChBQ,MAAM,EAAE5M,iBAAiB;EACzB6M,IAAI,EAAE5M,eAAe;EACrB6M,UAAU,EAAE5M,qBAAqB;EACjC6M,MAAM,EAAE5M,iBAAiB;EACzBoM,QAAQ,EAAEE;AACZ,CAAC;AACD,OAAO,IAAIO,cAAc,GAAG,SAASA,cAAcA,CAACzL,IAAI,EAAE0L,UAAU,EAAEC,UAAU,EAAE;EAChF,IAAIC,QAAQ,GAAGF,UAAU,CAACjL,GAAG,CAAC,UAAUyB,IAAI,EAAE;IAC5C,OAAOA,IAAI,CAACI,KAAK,CAACzC,OAAO;EAC3B,CAAC,CAAC;EACF,IAAItB,KAAK,GAAGC,UAAU,CAAC,CAAC,CAACnC,IAAI,CAACuP,QAAQ,CAAC,CAACpO,KAAK,CAAC,UAAUqO,CAAC,EAAE1O,GAAG,EAAE;IAC9D,OAAO,CAACyC,iBAAiB,CAACiM,CAAC,EAAE1O,GAAG,EAAE,CAAC,CAAC;EACtC,CAAC,CAAC,CAAC2O,KAAK,CAACjN,cAAc,CAAC,CAACiH,MAAM,CAACqF,gBAAgB,CAACQ,UAAU,CAAC,CAAC;EAC7D,OAAOpN,KAAK,CAACyB,IAAI,CAAC;AACpB,CAAC;AACD,OAAO,IAAI+L,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC/L,IAAI,EAAEgM,MAAM,EAAEC,aAAa,EAAErH,UAAU,EAAE+G,UAAU,EAAEO,iBAAiB,EAAE;EAC1I,IAAI,CAAClM,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAI2E,KAAK,GAAGuH,iBAAiB,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,GAAGH,MAAM;EACzD,IAAI5H,WAAW,GAAGO,KAAK,CAACxB,MAAM,CAAC,UAAUV,MAAM,EAAEP,IAAI,EAAE;IACrD,IAAIkK,YAAY,GAAGlK,IAAI,CAACI,KAAK;MAC3B+J,OAAO,GAAGD,YAAY,CAACC,OAAO;MAC9BzI,IAAI,GAAGwI,YAAY,CAACxI,IAAI;IAC1B,IAAIA,IAAI,EAAE;MACR,OAAOnB,MAAM;IACf;IACA,IAAI6J,MAAM,GAAGpK,IAAI,CAACI,KAAK,CAAC2J,aAAa,CAAC;IACtC,IAAIM,WAAW,GAAG9J,MAAM,CAAC6J,MAAM,CAAC,IAAI;MAClCE,QAAQ,EAAE,KAAK;MACfpI,WAAW,EAAE,CAAC;IAChB,CAAC;IACD,IAAI/E,UAAU,CAACgN,OAAO,CAAC,EAAE;MACvB,IAAII,UAAU,GAAGF,WAAW,CAACnI,WAAW,CAACiI,OAAO,CAAC,IAAI;QACnDJ,aAAa,EAAEA,aAAa;QAC5BrH,UAAU,EAAEA,UAAU;QACtBD,KAAK,EAAE;MACT,CAAC;MACD8H,UAAU,CAAC9H,KAAK,CAAC/H,IAAI,CAACsF,IAAI,CAAC;MAC3BqK,WAAW,CAACC,QAAQ,GAAG,IAAI;MAC3BD,WAAW,CAACnI,WAAW,CAACiI,OAAO,CAAC,GAAGI,UAAU;IAC/C,CAAC,MAAM;MACLF,WAAW,CAACnI,WAAW,CAAC7E,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG;QAC/C0M,aAAa,EAAEA,aAAa;QAC5BrH,UAAU,EAAEA,UAAU;QACtBD,KAAK,EAAE,CAACzC,IAAI;MACd,CAAC;IACH;IACA,OAAOpF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,MAAM,CAAC,EAAE,CAAC,CAAC,EAAErF,eAAe,CAAC,CAAC,CAAC,EAAEkP,MAAM,EAAEC,WAAW,CAAC,CAAC;EAC/F,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOnR,MAAM,CAACiB,IAAI,CAAC+H,WAAW,CAAC,CAACjB,MAAM,CAAC,UAAUV,MAAM,EAAE6J,MAAM,EAAE;IAC/D,IAAII,KAAK,GAAGtI,WAAW,CAACkI,MAAM,CAAC;IAC/B,IAAII,KAAK,CAACF,QAAQ,EAAE;MAClBE,KAAK,CAACtI,WAAW,GAAGhJ,MAAM,CAACiB,IAAI,CAACqQ,KAAK,CAACtI,WAAW,CAAC,CAACjB,MAAM,CAAC,UAAU/E,GAAG,EAAEiO,OAAO,EAAE;QAChF,IAAIM,CAAC,GAAGD,KAAK,CAACtI,WAAW,CAACiI,OAAO,CAAC;QAClC,OAAOvP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAEiP,OAAO,EAAE;UAC5EJ,aAAa,EAAEA,aAAa;UAC5BrH,UAAU,EAAEA,UAAU;UACtBD,KAAK,EAAEgI,CAAC,CAAChI,KAAK;UACdiI,WAAW,EAAEnB,cAAc,CAACzL,IAAI,EAAE2M,CAAC,CAAChI,KAAK,EAAEgH,UAAU;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;IACA,OAAO7O,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,MAAM,CAAC,EAAE,CAAC,CAAC,EAAErF,eAAe,CAAC,CAAC,CAAC,EAAEkP,MAAM,EAAEI,KAAK,CAAC,CAAC;EACzF,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAAClE,KAAK,EAAEmE,IAAI,EAAE;EACjE,IAAIhE,aAAa,GAAGgE,IAAI,CAAChE,aAAa;IACpC7I,IAAI,GAAG6M,IAAI,CAAC7M,IAAI;IAChBoJ,SAAS,GAAGyD,IAAI,CAACzD,SAAS;IAC1B0D,cAAc,GAAGD,IAAI,CAACC,cAAc;IACpCC,aAAa,GAAGF,IAAI,CAACE,aAAa;EACpC,IAAIC,SAAS,GAAGnE,aAAa,IAAIgE,IAAI,CAACnE,KAAK;EAC3C,IAAIsE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,QAAQ,EAAE;IAClD,OAAO,IAAI;EACb;EACA,IAAI5D,SAAS,IAAIpJ,IAAI,KAAK,QAAQ,IAAI8M,cAAc,KAAKA,cAAc,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIA,cAAc,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,EAAE;IACtH;IACA,IAAI1M,MAAM,GAAGsI,KAAK,CAACtI,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACA,MAAM,CAACtE,MAAM,EAAE;MAClB,OAAO,IAAI;IACb;IACA,IAAImR,UAAU,GAAGpO,iBAAiB,CAACuB,MAAM,EAAEgJ,SAAS,EAAE2D,aAAa,CAAC;IACpErE,KAAK,CAACtI,MAAM,CAAC,CAACvG,IAAI,CAACoT,UAAU,CAAC,EAAErT,IAAI,CAACqT,UAAU,CAAC,CAAC,CAAC;IAClD,OAAO;MACLlE,SAAS,EAAEkE;IACb,CAAC;EACH;EACA,IAAI7D,SAAS,IAAIpJ,IAAI,KAAK,QAAQ,EAAE;IAClC,IAAIkN,OAAO,GAAGxE,KAAK,CAACtI,MAAM,CAAC,CAAC;IAC5B,IAAI+M,WAAW,GAAGrO,wBAAwB,CAACoO,OAAO,EAAE9D,SAAS,EAAE2D,aAAa,CAAC;IAC7E,OAAO;MACLhE,SAAS,EAAEoE;IACb,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,KAAK,EAAE;EAC3E,IAAItM,IAAI,GAAGsM,KAAK,CAACtM,IAAI;IACnBF,KAAK,GAAGwM,KAAK,CAACxM,KAAK;IACnBwE,QAAQ,GAAGgI,KAAK,CAAChI,QAAQ;IACzBlF,KAAK,GAAGkN,KAAK,CAAClN,KAAK;IACnBa,KAAK,GAAGqM,KAAK,CAACrM,KAAK;IACnBpB,OAAO,GAAGyN,KAAK,CAACzN,OAAO;EACzB,IAAImB,IAAI,CAACf,IAAI,KAAK,UAAU,EAAE;IAC5B;IACA,IAAI,CAACe,IAAI,CAACuM,uBAAuB,IAAIvM,IAAI,CAACnB,OAAO,IAAI,CAAC3F,MAAM,CAACkG,KAAK,CAACY,IAAI,CAACnB,OAAO,CAAC,CAAC,EAAE;MACjF,IAAI2N,WAAW,GAAGtO,gBAAgB,CAAC4B,KAAK,EAAE,OAAO,EAAEV,KAAK,CAACY,IAAI,CAACnB,OAAO,CAAC,CAAC;MACvE,IAAI2N,WAAW,EAAE;QACf,OAAOA,WAAW,CAAC5M,UAAU,GAAG0E,QAAQ,GAAG,CAAC;MAC9C;IACF;IACA,OAAOxE,KAAK,CAACG,KAAK,CAAC,GAAGH,KAAK,CAACG,KAAK,CAAC,CAACL,UAAU,GAAG0E,QAAQ,GAAG,CAAC,GAAG,IAAI;EACrE;EACA,IAAI9H,KAAK,GAAGoC,iBAAiB,CAACQ,KAAK,EAAE,CAAClG,MAAM,CAAC2F,OAAO,CAAC,GAAGA,OAAO,GAAGmB,IAAI,CAACnB,OAAO,CAAC;EAC/E,OAAO,CAAC3F,MAAM,CAACsD,KAAK,CAAC,GAAGwD,IAAI,CAAC2H,KAAK,CAACnL,KAAK,CAAC,GAAG,IAAI;AAClD,CAAC;AACD,OAAO,IAAIiQ,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAE;EACzE,IAAI1M,IAAI,GAAG0M,KAAK,CAAC1M,IAAI;IACnBF,KAAK,GAAG4M,KAAK,CAAC5M,KAAK;IACnBgF,MAAM,GAAG4H,KAAK,CAAC5H,MAAM;IACrBR,QAAQ,GAAGoI,KAAK,CAACpI,QAAQ;IACzBlF,KAAK,GAAGsN,KAAK,CAACtN,KAAK;IACnBa,KAAK,GAAGyM,KAAK,CAACzM,KAAK;EACrB,IAAID,IAAI,CAACf,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAOa,KAAK,CAACG,KAAK,CAAC,GAAGH,KAAK,CAACG,KAAK,CAAC,CAACL,UAAU,GAAGkF,MAAM,GAAG,IAAI;EAC/D;EACA,IAAItI,KAAK,GAAGoC,iBAAiB,CAACQ,KAAK,EAAEY,IAAI,CAACnB,OAAO,EAAEmB,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC,CAAC;EACtE,OAAO,CAAC/G,MAAM,CAACsD,KAAK,CAAC,GAAGwD,IAAI,CAAC2H,KAAK,CAACnL,KAAK,CAAC,GAAG8H,QAAQ,GAAG,CAAC,GAAGQ,MAAM,GAAG,IAAI;AAC1E,CAAC;AACD,OAAO,IAAI6H,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EAC/D,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;EACnC,IAAIxN,MAAM,GAAGwN,WAAW,CAAClF,KAAK,CAACtI,MAAM,CAAC,CAAC;EACvC,IAAIwN,WAAW,CAAC5N,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAI2B,GAAG,GAAGT,IAAI,CAACS,GAAG,CAACvB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIwB,GAAG,GAAGV,IAAI,CAACU,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIuB,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,EAAE;MACxB,OAAO,CAAC;IACV;IACA,IAAIA,GAAG,GAAG,CAAC,EAAE;MACX,OAAOA,GAAG;IACZ;IACA,OAAOD,GAAG;EACZ;EACA,OAAOvB,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,OAAO,IAAIyN,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC5L,IAAI,EAAEkC,WAAW,EAAE;EACjF,IAAIiI,OAAO,GAAGnK,IAAI,CAACI,KAAK,CAAC+J,OAAO;EAChC,IAAIhN,UAAU,CAACgN,OAAO,CAAC,EAAE;IACvB,IAAIK,KAAK,GAAGtI,WAAW,CAACiI,OAAO,CAAC;IAChC,IAAIK,KAAK,IAAIA,KAAK,CAAC/H,KAAK,CAAC5I,MAAM,EAAE;MAC/B,IAAIgS,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,IAAI/R,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAG4Q,KAAK,CAAC/H,KAAK,CAAC5I,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QACtD,IAAI0Q,KAAK,CAAC/H,KAAK,CAAC3I,CAAC,CAAC,KAAKkG,IAAI,EAAE;UAC3B6L,SAAS,GAAG/R,CAAC;UACb;QACF;MACF;MACA,OAAO+R,SAAS,IAAI,CAAC,GAAGrB,KAAK,CAACE,WAAW,CAACmB,SAAS,CAAC,GAAG,IAAI;IAC7D;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAChO,IAAI,EAAE;EACvD,OAAOA,IAAI,CAACmD,MAAM,CAAC,UAAUV,MAAM,EAAErC,KAAK,EAAE;IAC1C,OAAO,CAACtG,IAAI,CAACsG,KAAK,CAACkD,MAAM,CAAC,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAACjG,MAAM,CAAC4C,QAAQ,CAAC,CAAC,EAAEvF,IAAI,CAACuG,KAAK,CAACkD,MAAM,CAAC,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAACjG,MAAM,CAAC4C,QAAQ,CAAC,CAAC,CAAC;EAC7G,CAAC,EAAE,CAACmB,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,IAAI0N,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC7J,WAAW,EAAE8J,UAAU,EAAEC,QAAQ,EAAE;EACrG,OAAO/S,MAAM,CAACiB,IAAI,CAAC+H,WAAW,CAAC,CAACjB,MAAM,CAAC,UAAUV,MAAM,EAAE4J,OAAO,EAAE;IAChE,IAAIK,KAAK,GAAGtI,WAAW,CAACiI,OAAO,CAAC;IAChC,IAAIO,WAAW,GAAGF,KAAK,CAACE,WAAW;IACnC,IAAIvM,MAAM,GAAGuM,WAAW,CAACzJ,MAAM,CAAC,UAAU/E,GAAG,EAAEgC,KAAK,EAAE;MACpD,IAAIgO,CAAC,GAAGJ,iBAAiB,CAAC5N,KAAK,CAAC7E,KAAK,CAAC2S,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChE,OAAO,CAAChN,IAAI,CAACS,GAAG,CAACxD,GAAG,CAAC,CAAC,CAAC,EAAEgQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjN,IAAI,CAACU,GAAG,CAACzD,GAAG,CAAC,CAAC,CAAC,EAAEgQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC7N,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;IACzB,OAAO,CAACY,IAAI,CAACS,GAAG,CAACvB,MAAM,CAAC,CAAC,CAAC,EAAEoC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEtB,IAAI,CAACU,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,EAAEoC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC,EAAE,CAAClC,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUgC,MAAM,EAAE;IAC9C,OAAOA,MAAM,KAAKlC,QAAQ,IAAIkC,MAAM,KAAK,CAAClC,QAAQ,GAAG,CAAC,GAAGkC,MAAM;EACjE,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAI4L,aAAa,GAAG,iDAAiD;AAC5E,OAAO,IAAIC,aAAa,GAAG,kDAAkD;AAC7E,OAAO,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,eAAe,EAAEC,UAAU,EAAEC,iBAAiB,EAAE;EAC9G,IAAI1U,WAAW,CAACwU,eAAe,CAAC,EAAE;IAChC,OAAOA,eAAe,CAACC,UAAU,EAAEC,iBAAiB,CAAC;EACvD;EACA,IAAI,CAAC9U,QAAQ,CAAC4U,eAAe,CAAC,EAAE;IAC9B,OAAOC,UAAU;EACnB;EACA,IAAIpO,MAAM,GAAG,EAAE;;EAEf;EACA,IAAIjB,QAAQ,CAACoP,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAChCnO,MAAM,CAAC,CAAC,CAAC,GAAGqO,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC,GAAGrN,IAAI,CAACS,GAAG,CAAC4M,eAAe,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC,MAAM,IAAIJ,aAAa,CAAC1S,IAAI,CAAC6S,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,IAAIhR,KAAK,GAAG,CAAC6Q,aAAa,CAACM,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtDnO,MAAM,CAAC,CAAC,CAAC,GAAGoO,UAAU,CAAC,CAAC,CAAC,GAAGjR,KAAK;EACnC,CAAC,MAAM,IAAIxD,WAAW,CAACwU,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1CnO,MAAM,CAAC,CAAC,CAAC,GAAGmO,eAAe,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,MAAM;IACLpO,MAAM,CAAC,CAAC,CAAC,GAAGoO,UAAU,CAAC,CAAC,CAAC;EAC3B;EACA,IAAIrP,QAAQ,CAACoP,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAChCnO,MAAM,CAAC,CAAC,CAAC,GAAGqO,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC,GAAGrN,IAAI,CAACU,GAAG,CAAC2M,eAAe,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC,MAAM,IAAIH,aAAa,CAAC3S,IAAI,CAAC6S,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,IAAII,MAAM,GAAG,CAACN,aAAa,CAACK,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDnO,MAAM,CAAC,CAAC,CAAC,GAAGoO,UAAU,CAAC,CAAC,CAAC,GAAGG,MAAM;EACpC,CAAC,MAAM,IAAI5U,WAAW,CAACwU,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1CnO,MAAM,CAAC,CAAC,CAAC,GAAGmO,eAAe,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,MAAM;IACLpO,MAAM,CAAC,CAAC,CAAC,GAAGoO,UAAU,CAAC,CAAC,CAAC;EAC3B;EACA;;EAEA,OAAOpO,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIwO,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC7N,IAAI,EAAEF,KAAK,EAAEgO,KAAK,EAAE;EAC5E,IAAI9N,IAAI,IAAIA,IAAI,CAAC2H,KAAK,IAAI3H,IAAI,CAAC2H,KAAK,CAACI,SAAS,EAAE;IAC9C,IAAIgG,SAAS,GAAG/N,IAAI,CAAC2H,KAAK,CAACI,SAAS,CAAC,CAAC;IACtC,IAAI,CAAC+F,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAE;MAC3B,OAAOA,SAAS;IAClB;EACF;EACA,IAAI/N,IAAI,IAAIF,KAAK,IAAIA,KAAK,CAAC/E,MAAM,IAAI,CAAC,EAAE;IACtC,IAAIiT,YAAY,GAAGxV,OAAO,CAACsH,KAAK,EAAE,UAAU9F,CAAC,EAAE;MAC7C,OAAOA,CAAC,CAAC4F,UAAU;IACrB,CAAC,CAAC;IACF,IAAI0E,QAAQ,GAAG/E,QAAQ;IACvB,KAAK,IAAIvE,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGkT,YAAY,CAACjT,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACvD,IAAIuF,GAAG,GAAGyN,YAAY,CAAChT,CAAC,CAAC;MACzB,IAAI+J,IAAI,GAAGiJ,YAAY,CAAChT,CAAC,GAAG,CAAC,CAAC;MAC9BsJ,QAAQ,GAAGnE,IAAI,CAACS,GAAG,CAAC,CAACL,GAAG,CAACX,UAAU,IAAI,CAAC,KAAKmF,IAAI,CAACnF,UAAU,IAAI,CAAC,CAAC,EAAE0E,QAAQ,CAAC;IAC/E;IACA,OAAOA,QAAQ,KAAK/E,QAAQ,GAAG,CAAC,GAAG+E,QAAQ;EAC7C;EACA,OAAOwJ,KAAK,GAAG3Q,SAAS,GAAG,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI8Q,yBAAyB,GAAG,SAASA,yBAAyBA,CAACT,eAAe,EAAEU,gBAAgB,EAAEC,SAAS,EAAE;EACtH,IAAI,CAACX,eAAe,IAAI,CAACA,eAAe,CAACzS,MAAM,EAAE;IAC/C,OAAOmT,gBAAgB;EACzB;EACA,IAAI3V,QAAQ,CAACiV,eAAe,EAAEvU,IAAI,CAACkV,SAAS,EAAE,0BAA0B,CAAC,CAAC,EAAE;IAC1E,OAAOD,gBAAgB;EACzB;EACA,OAAOV,eAAe;AACxB,CAAC;AACD,OAAO,IAAIY,cAAc,GAAG,SAASA,cAAcA,CAACC,aAAa,EAAEnM,OAAO,EAAE;EAC1E,IAAIoM,oBAAoB,GAAGD,aAAa,CAAC/M,KAAK;IAC5CzC,OAAO,GAAGyP,oBAAoB,CAACzP,OAAO;IACtCrE,IAAI,GAAG8T,oBAAoB,CAAC9T,IAAI;IAChC+T,IAAI,GAAGD,oBAAoB,CAACC,IAAI;IAChCC,SAAS,GAAGF,oBAAoB,CAACE,SAAS;IAC1CC,WAAW,GAAGH,oBAAoB,CAACG,WAAW;IAC9CzF,SAAS,GAAGsF,oBAAoB,CAACtF,SAAS;EAC5C,OAAOlN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,WAAW,CAAC6P,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACtExP,OAAO,EAAEA,OAAO;IAChB0P,IAAI,EAAEA,IAAI;IACVC,SAAS,EAAEA,SAAS;IACpBhU,IAAI,EAAEA,IAAI,IAAIqE,OAAO;IACrB4D,KAAK,EAAExB,yBAAyB,CAACoN,aAAa,CAAC;IAC/C7R,KAAK,EAAEoC,iBAAiB,CAACsD,OAAO,EAAErD,OAAO,CAAC;IAC1CI,IAAI,EAAEwP,WAAW;IACjBvM,OAAO,EAAEA,OAAO;IAChB8G,SAAS,EAAEA;EACb,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}