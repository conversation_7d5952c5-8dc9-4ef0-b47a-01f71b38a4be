{"ast": null, "code": "import CSSMotion from \"./CSSMotion\";\nimport CSSMotionList from \"./CSSMotionList\";\nexport { default as Provider } from \"./context\";\nexport { CSSMotionList };\nexport default CSSMotion;", "map": {"version": 3, "names": ["CSSMotion", "CSSMotionList", "default", "Provider"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/index.js"], "sourcesContent": ["import CSSMotion from \"./CSSMotion\";\nimport CSSMotionList from \"./CSSMotionList\";\nexport { default as Provider } from \"./context\";\nexport { CSSMotionList };\nexport default CSSMotion;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,OAAO,IAAIC,QAAQ,QAAQ,WAAW;AAC/C,SAASF,aAAa;AACtB,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}