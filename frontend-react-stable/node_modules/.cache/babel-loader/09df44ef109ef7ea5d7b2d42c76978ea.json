{"ast": null, "code": "/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n    result = data['delete'](key);\n  this.size = data.size;\n  return result;\n}\nmodule.exports = stackDelete;", "map": {"version": 3, "names": ["stackDelete", "key", "data", "__data__", "result", "size", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_stackDelete.js"], "sourcesContent": ["/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n\n  this.size = data.size;\n  return result;\n}\n\nmodule.exports = stackDelete;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,GAAG,EAAE;EACxB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;IACpBC,MAAM,GAAGF,IAAI,CAAC,QAAQ,CAAC,CAACD,GAAG,CAAC;EAEhC,IAAI,CAACI,IAAI,GAAGH,IAAI,CAACG,IAAI;EACrB,OAAOD,MAAM;AACf;AAEAE,MAAM,CAACC,OAAO,GAAGP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}