{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PauseCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/PauseCircleTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar PauseCircleTwoTone = function PauseCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PauseCircleTwoToneSvg\n  }));\n};\nPauseCircleTwoTone.displayName = 'PauseCircleTwoTone';\nexport default /*#__PURE__*/React.forwardRef(PauseCircleTwoTone);", "map": {"version": 3, "names": ["_objectSpread", "React", "PauseCircleTwoToneSvg", "AntdIcon", "PauseCircleTwoTone", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/PauseCircleTwoTone.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PauseCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/PauseCircleTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar PauseCircleTwoTone = function PauseCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PauseCircleTwoToneSvg\n  }));\n};\nPauseCircleTwoTone.displayName = 'PauseCircleTwoTone';\nexport default /*#__PURE__*/React.forwardRef(PauseCircleTwoTone);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,kBAAkB,CAACK,WAAW,GAAG,oBAAoB;AACrD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}