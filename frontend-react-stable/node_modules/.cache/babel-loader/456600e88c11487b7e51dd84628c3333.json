{"ast": null, "code": "export var STATUS_NONE = 'none';\nexport var STATUS_APPEAR = 'appear';\nexport var STATUS_ENTER = 'enter';\nexport var STATUS_LEAVE = 'leave';\nexport var STEP_NONE = 'none';\nexport var STEP_PREPARE = 'prepare';\nexport var STEP_START = 'start';\nexport var STEP_ACTIVE = 'active';\nexport var STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nexport var STEP_PREPARED = 'prepared';", "map": {"version": 3, "names": ["STATUS_NONE", "STATUS_APPEAR", "STATUS_ENTER", "STATUS_LEAVE", "STEP_NONE", "STEP_PREPARE", "STEP_START", "STEP_ACTIVE", "STEP_ACTIVATED", "STEP_PREPARED"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/interface.js"], "sourcesContent": ["export var STATUS_NONE = 'none';\nexport var STATUS_APPEAR = 'appear';\nexport var STATUS_ENTER = 'enter';\nexport var STATUS_LEAVE = 'leave';\nexport var STEP_NONE = 'none';\nexport var STEP_PREPARE = 'prepare';\nexport var STEP_START = 'start';\nexport var STEP_ACTIVE = 'active';\nexport var STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nexport var STEP_PREPARED = 'prepared';"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG,MAAM;AAC/B,OAAO,IAAIC,aAAa,GAAG,QAAQ;AACnC,OAAO,IAAIC,YAAY,GAAG,OAAO;AACjC,OAAO,IAAIC,YAAY,GAAG,OAAO;AACjC,OAAO,IAAIC,SAAS,GAAG,MAAM;AAC7B,OAAO,IAAIC,YAAY,GAAG,SAAS;AACnC,OAAO,IAAIC,UAAU,GAAG,OAAO;AAC/B,OAAO,IAAIC,WAAW,GAAG,QAAQ;AACjC,OAAO,IAAIC,cAAc,GAAG,KAAK;AACjC;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}