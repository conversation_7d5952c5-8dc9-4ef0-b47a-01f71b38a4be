{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FunnelPlotTwoToneSvg from \"@ant-design/icons-svg/es/asn/FunnelPlotTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar FunnelPlotTwoTone = function FunnelPlotTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FunnelPlotTwoToneSvg\n  }));\n};\nFunnelPlotTwoTone.displayName = 'FunnelPlotTwoTone';\nexport default /*#__PURE__*/React.forwardRef(FunnelPlotTwoTone);", "map": {"version": 3, "names": ["_objectSpread", "React", "FunnelPlotTwoToneSvg", "AntdIcon", "FunnelPlotTwoTone", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/FunnelPlotTwoTone.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FunnelPlotTwoToneSvg from \"@ant-design/icons-svg/es/asn/FunnelPlotTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar FunnelPlotTwoTone = function FunnelPlotTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FunnelPlotTwoToneSvg\n  }));\n};\nFunnelPlotTwoTone.displayName = 'FunnelPlotTwoTone';\nexport default /*#__PURE__*/React.forwardRef(FunnelPlotTwoTone);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACnD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}