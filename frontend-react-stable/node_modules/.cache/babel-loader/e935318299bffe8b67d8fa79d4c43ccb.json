{"ast": null, "code": "function t(t) {\n  return \"object\" == typeof t && null != t && 1 === t.nodeType;\n}\nfunction e(t, e) {\n  return (!e || \"hidden\" !== t) && \"visible\" !== t && \"clip\" !== t;\n}\nfunction n(t, n) {\n  if (t.clientHeight < t.scrollHeight || t.clientWidth < t.scrollWidth) {\n    var r = getComputedStyle(t, null);\n    return e(r.overflowY, n) || e(r.overflowX, n) || function (t) {\n      var e = function (t) {\n        if (!t.ownerDocument || !t.ownerDocument.defaultView) return null;\n        try {\n          return t.ownerDocument.defaultView.frameElement;\n        } catch (t) {\n          return null;\n        }\n      }(t);\n      return !!e && (e.clientHeight < t.scrollHeight || e.clientWidth < t.scrollWidth);\n    }(t);\n  }\n  return !1;\n}\nfunction r(t, e, n, r, i, o, l, d) {\n  return o < t && l > e || o > t && l < e ? 0 : o <= t && d <= n || l >= e && d >= n ? o - t - r : l > e && d < n || o < t && d > n ? l - e + i : 0;\n}\nvar i = function (e, i) {\n  var o = window,\n    l = i.scrollMode,\n    d = i.block,\n    f = i.inline,\n    h = i.boundary,\n    u = i.skipOverflowHiddenElements,\n    s = \"function\" == typeof h ? h : function (t) {\n      return t !== h;\n    };\n  if (!t(e)) throw new TypeError(\"Invalid target\");\n  for (var a, c, g = document.scrollingElement || document.documentElement, p = [], m = e; t(m) && s(m);) {\n    if ((m = null == (c = (a = m).parentElement) ? a.getRootNode().host || null : c) === g) {\n      p.push(m);\n      break;\n    }\n    null != m && m === document.body && n(m) && !n(document.documentElement) || null != m && n(m, u) && p.push(m);\n  }\n  for (var w = o.visualViewport ? o.visualViewport.width : innerWidth, v = o.visualViewport ? o.visualViewport.height : innerHeight, W = window.scrollX || pageXOffset, H = window.scrollY || pageYOffset, b = e.getBoundingClientRect(), y = b.height, E = b.width, M = b.top, V = b.right, x = b.bottom, I = b.left, C = \"start\" === d || \"nearest\" === d ? M : \"end\" === d ? x : M + y / 2, R = \"center\" === f ? I + E / 2 : \"end\" === f ? V : I, T = [], k = 0; k < p.length; k++) {\n    var B = p[k],\n      D = B.getBoundingClientRect(),\n      O = D.height,\n      X = D.width,\n      Y = D.top,\n      L = D.right,\n      S = D.bottom,\n      j = D.left;\n    if (\"if-needed\" === l && M >= 0 && I >= 0 && x <= v && V <= w && M >= Y && x <= S && I >= j && V <= L) return T;\n    var N = getComputedStyle(B),\n      q = parseInt(N.borderLeftWidth, 10),\n      z = parseInt(N.borderTopWidth, 10),\n      A = parseInt(N.borderRightWidth, 10),\n      F = parseInt(N.borderBottomWidth, 10),\n      G = 0,\n      J = 0,\n      K = \"offsetWidth\" in B ? B.offsetWidth - B.clientWidth - q - A : 0,\n      P = \"offsetHeight\" in B ? B.offsetHeight - B.clientHeight - z - F : 0,\n      Q = \"offsetWidth\" in B ? 0 === B.offsetWidth ? 0 : X / B.offsetWidth : 0,\n      U = \"offsetHeight\" in B ? 0 === B.offsetHeight ? 0 : O / B.offsetHeight : 0;\n    if (g === B) G = \"start\" === d ? C : \"end\" === d ? C - v : \"nearest\" === d ? r(H, H + v, v, z, F, H + C, H + C + y, y) : C - v / 2, J = \"start\" === f ? R : \"center\" === f ? R - w / 2 : \"end\" === f ? R - w : r(W, W + w, w, q, A, W + R, W + R + E, E), G = Math.max(0, G + H), J = Math.max(0, J + W);else {\n      G = \"start\" === d ? C - Y - z : \"end\" === d ? C - S + F + P : \"nearest\" === d ? r(Y, S, O, z, F + P, C, C + y, y) : C - (Y + O / 2) + P / 2, J = \"start\" === f ? R - j - q : \"center\" === f ? R - (j + X / 2) + K / 2 : \"end\" === f ? R - L + A + K : r(j, L, X, q, A + K, R, R + E, E);\n      var Z = B.scrollLeft,\n        $ = B.scrollTop;\n      C += $ - (G = Math.max(0, Math.min($ + G / U, B.scrollHeight - O / U + P))), R += Z - (J = Math.max(0, Math.min(Z + J / Q, B.scrollWidth - X / Q + K)));\n    }\n    T.push({\n      el: B,\n      top: G,\n      left: J\n    });\n  }\n  return T;\n};\nexport { i as default };", "map": {"version": 3, "names": ["t", "nodeType", "e", "n", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "r", "getComputedStyle", "overflowY", "overflowX", "ownerDocument", "defaultView", "frameElement", "i", "o", "l", "d", "window", "scrollMode", "block", "f", "inline", "h", "boundary", "u", "skipOverflowHiddenElements", "s", "TypeError", "a", "c", "g", "document", "scrollingElement", "documentElement", "p", "m", "parentElement", "getRootNode", "host", "push", "body", "w", "visualViewport", "width", "innerWidth", "v", "height", "innerHeight", "W", "scrollX", "pageXOffset", "H", "scrollY", "pageYOffset", "b", "getBoundingClientRect", "y", "E", "M", "top", "V", "right", "x", "bottom", "I", "left", "C", "R", "T", "k", "length", "B", "D", "O", "X", "Y", "L", "S", "j", "N", "q", "parseInt", "borderLeftWidth", "z", "borderTopWidth", "A", "borderRightWidth", "F", "borderBottomWidth", "G", "J", "K", "offsetWidth", "P", "offsetHeight", "Q", "U", "Math", "max", "Z", "scrollLeft", "$", "scrollTop", "min", "el", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["// Compute what scrolling needs to be done on required scrolling boxes for target to be in view\n\n// The type names here are named after the spec to make it easier to find more information around what they mean:\n// To reduce churn and reduce things that need be maintained things from the official TS DOM library is used here\n// https://drafts.csswg.org/cssom-view/\n\n// For a definition on what is \"block flow direction\" exactly, check this: https://drafts.csswg.org/css-writing-modes-4/#block-flow-direction\n\n// add support for visualViewport object currently implemented in chrome\ninterface visualViewport {\n  height: number\n  width: number\n}\n\ntype ScrollLogicalPosition = 'start' | 'center' | 'end' | 'nearest'\n// This new option is tracked in this PR, which is the most likely candidate at the time: https://github.com/w3c/csswg-drafts/pull/1805\ntype ScrollMode = 'always' | 'if-needed'\n// New option that skips auto-scrolling all nodes with overflow: hidden set\n// See FF implementation: https://hg.mozilla.org/integration/fx-team/rev/c48c3ec05012#l7.18\ntype SkipOverflowHiddenElements = boolean\n\ninterface Options {\n  block?: ScrollLogicalPosition\n  inline?: ScrollLogicalPosition\n  scrollMode?: ScrollMode\n  boundary?: CustomScrollBoundary\n  skipOverflowHiddenElements?: SkipOverflowHiddenElements\n}\n\n// Custom behavior, not in any spec\ntype CustomScrollBoundaryCallback = (parent: Element) => boolean\ntype CustomScrollBoundary = Element | CustomScrollBoundaryCallback | null\ninterface CustomScrollAction {\n  el: Element\n  top: number\n  left: number\n}\n\n// @TODO better shadowdom test, 11 = document fragment\nfunction isElement(el: any): el is Element {\n  return typeof el === 'object' && el != null && el.nodeType === 1\n}\n\nfunction canOverflow(\n  overflow: string | null,\n  skipOverflowHiddenElements?: boolean\n) {\n  if (skipOverflowHiddenElements && overflow === 'hidden') {\n    return false\n  }\n\n  return overflow !== 'visible' && overflow !== 'clip'\n}\n\nfunction getFrameElement(el: Element) {\n  if (!el.ownerDocument || !el.ownerDocument.defaultView) {\n    return null\n  }\n\n  try {\n    return el.ownerDocument.defaultView.frameElement\n  } catch (e) {\n    return null\n  }\n}\n\nfunction isHiddenByFrame(el: Element): boolean {\n  const frame = getFrameElement(el)\n  if (!frame) {\n    return false\n  }\n\n  return (\n    frame.clientHeight < el.scrollHeight || frame.clientWidth < el.scrollWidth\n  )\n}\n\nfunction isScrollable(el: Element, skipOverflowHiddenElements?: boolean) {\n  if (el.clientHeight < el.scrollHeight || el.clientWidth < el.scrollWidth) {\n    const style = getComputedStyle(el, null)\n    return (\n      canOverflow(style.overflowY, skipOverflowHiddenElements) ||\n      canOverflow(style.overflowX, skipOverflowHiddenElements) ||\n      isHiddenByFrame(el)\n    )\n  }\n\n  return false\n}\n/**\n * Find out which edge to align against when logical scroll position is \"nearest\"\n * Interesting fact: \"nearest\" works similarily to \"if-needed\", if the element is fully visible it will not scroll it\n *\n * Legends:\n * ┌────────┐ ┏ ━ ━ ━ ┓\n * │ target │   frame\n * └────────┘ ┗ ━ ━ ━ ┛\n */\nfunction alignNearest(\n  scrollingEdgeStart: number,\n  scrollingEdgeEnd: number,\n  scrollingSize: number,\n  scrollingBorderStart: number,\n  scrollingBorderEnd: number,\n  elementEdgeStart: number,\n  elementEdgeEnd: number,\n  elementSize: number\n) {\n  /**\n   * If element edge A and element edge B are both outside scrolling box edge A and scrolling box edge B\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓\n   *          │  │\n   *        ┃ │  │ ┃        do nothing\n   *          │  │\n   *        ┗━│━━│━┛\n   *          └──┘\n   *\n   *  If element edge C and element edge D are both outside scrolling box edge C and scrolling box edge D\n   *\n   *    ┏ ━ ━ ━ ━ ┓\n   *   ┌───────────┐\n   *   │┃         ┃│        do nothing\n   *   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart < scrollingEdgeStart &&\n      elementEdgeEnd > scrollingEdgeEnd) ||\n    (elementEdgeStart > scrollingEdgeStart && elementEdgeEnd < scrollingEdgeEnd)\n  ) {\n    return 0\n  }\n\n  /**\n   * If element edge A is outside scrolling box edge A and element height is less than scrolling box height\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓         ┏━┌━━┐━┓\n   *          └──┘             │  │\n   *  from  ┃      ┃     to  ┃ └──┘ ┃\n   *\n   *        ┗━ ━━ ━┛         ┗━ ━━ ━┛\n   *\n   * If element edge B is outside scrolling box edge B and element height is greater than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━┌━━┐━┓\n   *                           │  │\n   *  from  ┃ ┌──┐ ┃     to  ┃ │  │ ┃\n   *          │  │             │  │\n   *        ┗━│━━│━┛         ┗━│━━│━┛\n   *          │  │             └──┘\n   *          │  │\n   *          └──┘\n   *\n   * If element edge C is outside scrolling box edge C and element width is less than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───┐                 ┌───┐\n   *  │ ┃ │       ┃         ┃   │     ┃\n   *  └───┘                 └───┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is greater than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *        ┌───────────┐   ┌───────────┐\n   *    ┃   │     ┃     │   ┃         ┃ │\n   *        └───────────┘   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart <= scrollingEdgeStart && elementSize <= scrollingSize) ||\n    (elementEdgeEnd >= scrollingEdgeEnd && elementSize >= scrollingSize)\n  ) {\n    return elementEdgeStart - scrollingEdgeStart - scrollingBorderStart\n  }\n\n  /**\n   * If element edge B is outside scrolling box edge B and element height is less than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━ ━━ ━┓\n   *\n   *  from  ┃      ┃     to  ┃ ┌──┐ ┃\n   *          ┌──┐             │  │\n   *        ┗━│━━│━┛         ┗━└━━┘━┛\n   *          └──┘\n   *\n   * If element edge A is outside scrolling box edge A and element height is greater than scrolling box height\n   *\n   *          ┌──┐\n   *          │  │\n   *          │  │             ┌──┐\n   *        ┏━│━━│━┓         ┏━│━━│━┓\n   *          │  │             │  │\n   *  from  ┃ └──┘ ┃     to  ┃ │  │ ┃\n   *                           │  │\n   *        ┗━ ━━ ━┛         ┗━└━━┘━┛\n   *\n   * If element edge C is outside scrolling box edge C and element width is greater than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───────────┐           ┌───────────┐\n   *  │     ┃     │   ┃       │ ┃         ┃\n   *  └───────────┘           └───────────┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is less than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *                ┌───┐             ┌───┐\n   *        ┃       │ ┃ │       ┃     │   ┃\n   *                └───┘             └───┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   */\n  if (\n    (elementEdgeEnd > scrollingEdgeEnd && elementSize < scrollingSize) ||\n    (elementEdgeStart < scrollingEdgeStart && elementSize > scrollingSize)\n  ) {\n    return elementEdgeEnd - scrollingEdgeEnd + scrollingBorderEnd\n  }\n\n  return 0\n}\n\nfunction getParentElement(element: Node): Element | null {\n  const parent = element.parentElement\n  if (parent == null) {\n    return (element.getRootNode() as ShadowRoot).host || null\n  }\n  return parent\n}\n\nexport default (target: Element, options: Options): CustomScrollAction[] => {\n  //TODO: remove this hack when microbundle will support typescript >= 4.0\n  const windowWithViewport = window as unknown as Window & {\n    visualViewport: visualViewport\n  }\n\n  const { scrollMode, block, inline, boundary, skipOverflowHiddenElements } =\n    options\n  // Allow using a callback to check the boundary\n  // The default behavior is to check if the current target matches the boundary element or not\n  // If undefined it'll check that target is never undefined (can happen as we recurse up the tree)\n  const checkBoundary =\n    typeof boundary === 'function' ? boundary : (node: any) => node !== boundary\n\n  if (!isElement(target)) {\n    throw new TypeError('Invalid target')\n  }\n\n  // Used to handle the top most element that can be scrolled\n  const scrollingElement = document.scrollingElement || document.documentElement\n\n  // Collect all the scrolling boxes, as defined in the spec: https://drafts.csswg.org/cssom-view/#scrolling-box\n  const frames: Element[] = []\n  let cursor: Element | null = target\n  while (isElement(cursor) && checkBoundary(cursor)) {\n    // Move cursor to parent\n    cursor = getParentElement(cursor)\n\n    // Stop when we reach the viewport\n    if (cursor === scrollingElement) {\n      frames.push(cursor)\n      break\n    }\n\n    // Skip document.body if it's not the scrollingElement and documentElement isn't independently scrollable\n    if (\n      cursor != null &&\n      cursor === document.body &&\n      isScrollable(cursor) &&\n      !isScrollable(document.documentElement)\n    ) {\n      continue\n    }\n\n    // Now we check if the element is scrollable, this code only runs if the loop haven't already hit the viewport or a custom boundary\n    if (cursor != null && isScrollable(cursor, skipOverflowHiddenElements)) {\n      frames.push(cursor)\n    }\n  }\n\n  // Support pinch-zooming properly, making sure elements scroll into the visual viewport\n  // Browsers that don't support visualViewport will report the layout viewport dimensions on document.documentElement.clientWidth/Height\n  // and viewport dimensions on window.innerWidth/Height\n  // https://www.quirksmode.org/mobile/viewports2.html\n  // https://bokand.github.io/viewport/index.html\n  const viewportWidth = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.width\n    : innerWidth\n  const viewportHeight = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.height\n    : innerHeight\n\n  // Newer browsers supports scroll[X|Y], page[X|Y]Offset is\n  const viewportX = window.scrollX || pageXOffset\n  const viewportY = window.scrollY || pageYOffset\n\n  const {\n    height: targetHeight,\n    width: targetWidth,\n    top: targetTop,\n    right: targetRight,\n    bottom: targetBottom,\n    left: targetLeft,\n  } = target.getBoundingClientRect()\n\n  // These values mutate as we loop through and generate scroll coordinates\n  let targetBlock: number =\n    block === 'start' || block === 'nearest'\n      ? targetTop\n      : block === 'end'\n      ? targetBottom\n      : targetTop + targetHeight / 2 // block === 'center\n  let targetInline: number =\n    inline === 'center'\n      ? targetLeft + targetWidth / 2\n      : inline === 'end'\n      ? targetRight\n      : targetLeft // inline === 'start || inline === 'nearest\n\n  // Collect new scroll positions\n  const computations: CustomScrollAction[] = []\n  // In chrome there's no longer a difference between caching the `frames.length` to a var or not, so we don't in this case (size > speed anyways)\n  for (let index = 0; index < frames.length; index++) {\n    const frame = frames[index]\n\n    // @TODO add a shouldScroll hook here that allows userland code to take control\n\n    const { height, width, top, right, bottom, left } =\n      frame.getBoundingClientRect()\n\n    // If the element is already visible we can end it here\n    // @TODO targetBlock and targetInline should be taken into account to be compliant with https://github.com/w3c/csswg-drafts/pull/1805/files#diff-3c17f0e43c20f8ecf89419d49e7ef5e0R1333\n    if (\n      scrollMode === 'if-needed' &&\n      targetTop >= 0 &&\n      targetLeft >= 0 &&\n      targetBottom <= viewportHeight &&\n      targetRight <= viewportWidth &&\n      targetTop >= top &&\n      targetBottom <= bottom &&\n      targetLeft >= left &&\n      targetRight <= right\n    ) {\n      // Break the loop and return the computations for things that are not fully visible\n      return computations\n    }\n\n    const frameStyle = getComputedStyle(frame)\n    const borderLeft = parseInt(frameStyle.borderLeftWidth as string, 10)\n    const borderTop = parseInt(frameStyle.borderTopWidth as string, 10)\n    const borderRight = parseInt(frameStyle.borderRightWidth as string, 10)\n    const borderBottom = parseInt(frameStyle.borderBottomWidth as string, 10)\n\n    let blockScroll: number = 0\n    let inlineScroll: number = 0\n\n    // The property existance checks for offfset[Width|Height] is because only HTMLElement objects have them, but any Element might pass by here\n    // @TODO find out if the \"as HTMLElement\" overrides can be dropped\n    const scrollbarWidth =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth -\n          (frame as HTMLElement).clientWidth -\n          borderLeft -\n          borderRight\n        : 0\n    const scrollbarHeight =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight -\n          (frame as HTMLElement).clientHeight -\n          borderTop -\n          borderBottom\n        : 0\n\n    const scaleX =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth === 0\n          ? 0\n          : width / (frame as HTMLElement).offsetWidth\n        : 0\n    const scaleY =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight === 0\n          ? 0\n          : height / (frame as HTMLElement).offsetHeight\n        : 0\n\n    if (scrollingElement === frame) {\n      // Handle viewport logic (document.documentElement or document.body)\n\n      if (block === 'start') {\n        blockScroll = targetBlock\n      } else if (block === 'end') {\n        blockScroll = targetBlock - viewportHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          viewportY,\n          viewportY + viewportHeight,\n          viewportHeight,\n          borderTop,\n          borderBottom,\n          viewportY + targetBlock,\n          viewportY + targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - viewportHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - viewportWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - viewportWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          viewportX,\n          viewportX + viewportWidth,\n          viewportWidth,\n          borderLeft,\n          borderRight,\n          viewportX + targetInline,\n          viewportX + targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      // Apply scroll position offsets and ensure they are within bounds\n      // @TODO add more test cases to cover this 100%\n      blockScroll = Math.max(0, blockScroll + viewportY)\n      inlineScroll = Math.max(0, inlineScroll + viewportX)\n    } else {\n      // Handle each scrolling frame that might exist between the target and the viewport\n\n      if (block === 'start') {\n        blockScroll = targetBlock - top - borderTop\n      } else if (block === 'end') {\n        blockScroll = targetBlock - bottom + borderBottom + scrollbarHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          top,\n          bottom,\n          height,\n          borderTop,\n          borderBottom + scrollbarHeight,\n          targetBlock,\n          targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - (top + height / 2) + scrollbarHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline - left - borderLeft\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - (left + width / 2) + scrollbarWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - right + borderRight + scrollbarWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          left,\n          right,\n          width,\n          borderLeft,\n          borderRight + scrollbarWidth,\n          targetInline,\n          targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      const { scrollLeft, scrollTop } = frame\n      // Ensure scroll coordinates are not out of bounds while applying scroll offsets\n      blockScroll = Math.max(\n        0,\n        Math.min(\n          scrollTop + blockScroll / scaleY,\n          frame.scrollHeight - height / scaleY + scrollbarHeight\n        )\n      )\n      inlineScroll = Math.max(\n        0,\n        Math.min(\n          scrollLeft + inlineScroll / scaleX,\n          frame.scrollWidth - width / scaleX + scrollbarWidth\n        )\n      )\n\n      // Cache the offset so that parent frames can scroll this into view correctly\n      targetBlock += scrollTop - blockScroll\n      targetInline += scrollLeft - inlineScroll\n    }\n\n    computations.push({ el: frame, top: blockScroll, left: inlineScroll })\n  }\n\n  return computations\n}\n"], "mappings": "AAuCA,SAAAA,EAAmBA,CAAA;EACjB,OAAqB,mBAAPA,CAAA,IAAyB,QAANA,CAAA,IAA8B,MAAhBA,CAAA,CAAGC,QACpD;AAAA;AAEA,SAASC,EACPF,CAAA,EACAE,CAAA;EAEA,SAAIA,CAAA,IAA2C,aAAbF,CAAA,KAId,cAAAA,CAAA,IAA0B,WAAbA,CACnC;AAAA;AAyBA,SAAqBG,EAACH,CAAA,EAAaG,CAAA;EACjC,IAAIH,CAAA,CAAGI,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBL,CAAA,CAAGM,WAAA,GAAcN,CAAA,CAAGO,WAAA,EAAa;IACxE,IAAWC,CAAA,GAAGC,gBAAA,CAAiBT,CAAA,EAAI;IACnC,OACaE,CAAA,CAACM,CAAA,CAAME,SAAA,EAAWP,CAAA,KAC7BD,CAAA,CAAYM,CAAA,CAAMG,SAAA,EAAWR,CAAA,KAhBnC,UAAyBH,CAAA;MACvB,IAAWE,CAAA,GAbb,UAAyBF,CAAA;QACvB,KAAKA,CAAA,CAAGY,aAAA,KAAkBZ,CAAA,CAAGY,aAAA,CAAcC,WAAA,EACzC,OAAO;QAGT;UACE,OAASb,CAAA,CAACY,aAAA,CAAcC,WAAA,CAAYC,YAGrC;QAAA,CAFC,QAAOd,CAAA;UACP,OACD;QAAA;MACH,CAGgB,CAAgBA,CAAA;MAC9B,SAAKE,CAAA,KAKHA,CAAA,CAAME,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBH,CAAA,CAAMI,WAAA,GAAcN,CAAA,CAAGO,WAAA,CAEnE;IAAA,CAQM,CAAgBP,CAAA,CAEnB;EAAA;EAED,QAAO,CACT;AAAA;AAUA,SAAAQ,EACER,CAAA,EACAE,CAAA,EACAC,CAAA,EACAK,CAAA,EACAO,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EAqBA,OACGF,CAAA,GAAmBhB,CAAA,IAClBiB,CAAA,GAAiBf,CAAA,IAClBc,CAAA,GAAmBhB,CAAA,IAAsBiB,CAAA,GAAiBf,CAAA,GAEpD,IA2CNc,CAAA,IAAoBhB,CAAA,IAAsBkB,CAAA,IAAef,CAAA,IACzDc,CAAA,IAAkBf,CAAA,IAAoBgB,CAAA,IAAef,CAAA,GAE/Ca,CAAA,GAAmBhB,CAAA,GAAqBQ,CAAA,GA4C9CS,CAAA,GAAiBf,CAAA,IAAoBgB,CAAA,GAAcf,CAAA,IACnDa,CAAA,GAAmBhB,CAAA,IAAsBkB,CAAA,GAAcf,CAAA,GAEjDc,CAAA,GAAiBf,CAAA,GAAmBa,CAAA,GAI/C;AAAA;AAUA,IAAAA,CAAA,YAAAA,CAAgBb,CAAA,EAAiBa,CAAA;EAE/B,IAAMC,CAAA,GAAqBG,MAAA;IAInBF,CAAA,GACNF,CAAA,CADMK,UAAA;IAAYF,CAAA,GAClBH,CAAA,CADkBM,KAAA;IAAOC,CAAA,GACzBP,CAAA,CADyBQ,MAAA;IAAQC,CAAA,GACjCT,CAAA,CADiCU,QAAA;IAAUC,CAAA,GAC3CX,CAAA,CAD2CY,0BAAA;IAKvCC,CAAA,GACgB,qBAAbJ,CAAA,GAA0BA,CAAA,GAAW,UAACxB,CAAA;MAAA,OAAkBA,CAAA,KAAKwB,CAAQ;IAAA;EAE9E,KAAKxB,CAAA,CAAUE,CAAA,GACb,MAAM,IAAA2B,SAAA,CAAc;EAStB,KALA,IA3BwBC,CAAA,EAClBC,CAAA,EA0BgBC,CAAA,GAAGC,QAAA,CAASC,gBAAA,IAAoBD,QAAA,CAASE,eAAA,EAGnDC,CAAA,GAAc,IAChBC,CAAA,GAAmBnC,CAAA,EACtBF,CAAA,CAAUqC,CAAA,KAAWT,CAAA,CAAcS,CAAA,IAAS;IAKjD,KAHAA,CAAA,GAhCY,SADRN,CAAA,IADkBD,CAAA,GAkCIO,CAAA,EAjCLC,aAAA,IAAAR,CAAA,CAELS,WAAA,GAA6BC,IAAA,IAAQ,OAAAT,CAAA,MAkCtCC,CAAA,EAAkB;MAC/BI,CAAA,CAAOK,IAAA,CAAKJ,CAAA;MACZ;IACD;IAIW,QAAVA,CAAA,IACAA,CAAA,KAAWJ,QAAA,CAASS,IAAA,IACpBvC,CAAA,CAAakC,CAAA,MACZlC,CAAA,CAAa8B,QAAA,CAASE,eAAA,KAMX,QAAVE,CAAA,IAAkBlC,CAAA,CAAakC,CAAA,EAAQX,CAAA,KACzCU,CAAA,CAAOK,IAAA,CAAKJ,CAAA,CAEf;EAAA;EA4CD,KArCA,IAAmBM,CAAA,GAAG3B,CAAA,CAAmB4B,cAAA,GACrC5B,CAAA,CAAmB4B,cAAA,CAAeC,KAAA,GAClCC,UAAA,EACgBC,CAAA,GAAG/B,CAAA,CAAmB4B,cAAA,GACtC5B,CAAA,CAAmB4B,cAAA,CAAeI,MAAA,GAClCC,WAAA,EAAAC,CAAA,GAGc/B,MAAA,CAAOgC,OAAA,IAAWC,WAAA,EAC9BC,CAAA,GAAYlC,MAAA,CAAOmC,OAAA,IAAWC,WAAA,EAEpCC,CAAA,GAOItD,CAAA,CAAOuD,qBAAA,IANDC,CAAA,GAAYF,CAAA,CAApBR,MAAA,EACOW,CAAA,GAAPH,CAAA,CAAAX,KAAA,EACKe,CAAA,GAASJ,CAAA,CAAdK,GAAA,EACOC,CAAA,GAAPN,CAAA,CAAAO,KAAA,EACQC,CAAA,GAAYR,CAAA,CAApBS,MAAA,EACMC,CAAA,GAANV,CAAA,CAAAW,IAAA,EAIEC,CAAA,GACQ,YAAVlD,CAAA,IAA+B,cAAVA,CAAA,GACjB0C,CAAA,GACU,UAAV1C,CAAA,GACA8C,CAAA,GACAJ,CAAA,GAAYF,CAAA,GAAe,GAC7BW,CAAA,GACS,aAAX/C,CAAA,GACI4C,CAAA,GAAaP,CAAA,GAAc,IAChB,UAAXrC,CAAA,GACAwC,CAAA,GACAI,CAAA,EAGAI,CAAA,GAAqC,IAAAC,CAAA,GAE1B,GAAGA,CAAA,GAAQnC,CAAA,CAAOoC,MAAA,EAAQD,CAAA,IAAS;IAClD,IAAME,CAAA,GAAQrC,CAAA,CAAOmC,CAAA;MAKnBG,CAAA,GAAAD,CAAA,CAAMhB,qBAAA;MADAkB,CAAA,GAAAD,CAAA,CAAA1B,MAAA;MAAQ4B,CAAA,GAAKF,CAAA,CAAL7B,KAAA;MAAOgC,CAAA,GAAAH,CAAA,CAAAb,GAAA;MAAKiB,CAAA,GAAKJ,CAAA,CAALX,KAAA;MAAOgB,CAAA,GAAAL,CAAA,CAAAT,MAAA;MAAQe,CAAA,GAAAN,CAAA,CAAAP,IAAA;IAK3C,IACiB,gBAAflD,CAAA,IACA2C,CAAA,IAAa,KACbM,CAAA,IAAc,KACdF,CAAA,IAAgBjB,CAAA,IAChBe,CAAA,IAAenB,CAAA,IACfiB,CAAA,IAAaiB,CAAA,IACbb,CAAA,IAAgBe,CAAA,IAChBb,CAAA,IAAcc,CAAA,IACdlB,CAAA,IAAegB,CAAA,EAGf,OACDR,CAAA;IAED,IAAMW,CAAA,GAAaxE,gBAAA,CAAiBgE,CAAA;MAC9BS,CAAA,GAAaC,QAAA,CAASF,CAAA,CAAWG,eAAA,EAA2B;MACnDC,CAAA,GAAGF,QAAA,CAASF,CAAA,CAAWK,cAAA,EAA0B;MAAAC,CAAA,GAC5CJ,QAAA,CAASF,CAAA,CAAWO,gBAAA,EAA4B;MAC9DC,CAAA,GAAeN,QAAA,CAASF,CAAA,CAAWS,iBAAA,EAA6B;MAEvDC,CAAA,GAAW;MACVC,CAAA,GAAW;MAIPC,CAAA,GAClB,iBAAiBpB,CAAA,GACZA,CAAA,CAAsBqB,WAAA,GACtBrB,CAAA,CAAsBnE,WAAA,GACvB4E,CAAA,GACAK,CAAA,GACA;MACAQ,CAAA,GACJ,kBAAAtB,CAAA,GACKA,CAAA,CAAsBuB,YAAA,GACtBvB,CAAA,CAAsBrE,YAAA,GACvBiF,CAAA,GACAI,CAAA,GACA;MAEAQ,CAAA,GACJ,iBAAsBxB,CAAA,GACqB,MAAtCA,CAAA,CAAsBqB,WAAA,GACrB,IACAlB,CAAA,GAASH,CAAA,CAAsBqB,WAAA,GACjC;MACAI,CAAA,GACJ,kBAAAzB,CAAA,GAC4C,MAAvCA,CAAA,CAAsBuB,YAAA,GACrB,IACArB,CAAA,GAAUF,CAAA,CAAsBuB,YAAA,GAClC;IAEN,IAAIhE,CAAA,KAAqByC,CAAA,EAIrBkB,CAAA,GADY,YAAVzE,CAAA,GACYkD,CAAA,GACK,UAAVlD,CAAA,GACKkD,CAAA,GAAcrB,CAAA,GACT,cAAV7B,CAAA,GACKV,CAAA,CACZ6C,CAAA,EACAA,CAAA,GAAYN,CAAA,EACZA,CAAA,EACAsC,CAAA,EACAI,CAAA,EACApC,CAAA,GAAYe,CAAA,EACZf,CAAA,GAAYe,CAAA,GAAcV,CAAA,EAC1BA,CAAA,IAIYU,CAAA,GAAcrB,CAAA,GAAiB,GAI7C6C,CAAA,GADa,YAAXtE,CAAA,GACa+C,CAAA,GACK,aAAX/C,CAAA,GACM+C,CAAA,GAAe1B,CAAA,GAAgB,IAC1B,UAAXrB,CAAA,GACM+C,CAAA,GAAe1B,CAAA,GAGfnC,CAAA,CACb0C,CAAA,EACAA,CAAA,GAAYP,CAAA,EACZA,CAAA,EACAuC,CAAA,EACAK,CAAA,EACArC,CAAA,GAAYmB,CAAA,EACZnB,CAAA,GAAYmB,CAAA,GAAeV,CAAA,EAC3BA,CAAA,GAMJgC,CAAA,GAAcQ,IAAA,CAAKC,GAAA,CAAI,GAAGT,CAAA,GAActC,CAAA,GACxCuC,CAAA,GAAeO,IAAA,CAAKC,GAAA,CAAI,GAAGR,CAAA,GAAe1C,CAAA,OACrC;MAIHyC,CAAA,GADY,YAAVzE,CAAA,GACYkD,CAAA,GAAcS,CAAA,GAAMQ,CAAA,GACf,UAAVnE,CAAA,GACKkD,CAAA,GAAcW,CAAA,GAASU,CAAA,GAAeM,CAAA,GACjC,cAAV7E,CAAA,GACKV,CAAA,CACZqE,CAAA,EACAE,CAAA,EACAJ,CAAA,EACAU,CAAA,EACAI,CAAA,GAAeM,CAAA,EACf3B,CAAA,EACAA,CAAA,GAAcV,CAAA,EACdA,CAAA,IAIYU,CAAA,IAAeS,CAAA,GAAMF,CAAA,GAAS,KAAKoB,CAAA,GAAkB,GAInEH,CAAA,GADa,YAAXtE,CAAA,GACa+C,CAAA,GAAeW,CAAA,GAAOE,CAAA,GACjB,aAAX5D,CAAA,GACM+C,CAAA,IAAgBW,CAAA,GAAOJ,CAAA,GAAQ,KAAKiB,CAAA,GAAiB,IAChD,UAAXvE,CAAA,GACM+C,CAAA,GAAeS,CAAA,GAAQS,CAAA,GAAcM,CAAA,GAGrCrF,CAAA,CACbwE,CAAA,EACAF,CAAA,EACAF,CAAA,EACAM,CAAA,EACAK,CAAA,GAAcM,CAAA,EACdxB,CAAA,EACAA,CAAA,GAAeV,CAAA,EACfA,CAAA;MAIJ,IAAQ0C,CAAA,GAA0B5B,CAAA,CAA1B6B,UAAA;QAAYC,CAAA,GAAc9B,CAAA,CAAd+B,SAAA;MAkBpBpC,CAAA,IAAemC,CAAA,IAhBfZ,CAAA,GAAcQ,IAAA,CAAKC,GAAA,CACjB,GACAD,IAAA,CAAKM,GAAA,CACHF,CAAA,GAAYZ,CAAA,GAAcO,CAAA,EAC1BzB,CAAA,CAAMpE,YAAA,GAAesE,CAAA,GAASuB,CAAA,GAASH,CAAA,KAa3C1B,CAAA,IAAgBgC,CAAA,IAVhBT,CAAA,GAAeO,IAAA,CAAKC,GAAA,CAClB,GACAD,IAAA,CAAKM,GAAA,CACHJ,CAAA,GAAaT,CAAA,GAAeK,CAAA,EAC5BxB,CAAA,CAAMlE,WAAA,GAAcqE,CAAA,GAAQqB,CAAA,GAASJ,CAAA,GAO1C;IAAA;IAEDvB,CAAA,CAAa7B,IAAA,CAAK;MAAEiE,EAAA,EAAIjC,CAAA;MAAOZ,GAAA,EAAK8B,CAAA;MAAaxB,IAAA,EAAMyB;IAAA,EACxD;EAAA;EAED,OAAAtB,CACD;AAAA;AAAA,SAAAvD,CAAA,IAAA4F,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}