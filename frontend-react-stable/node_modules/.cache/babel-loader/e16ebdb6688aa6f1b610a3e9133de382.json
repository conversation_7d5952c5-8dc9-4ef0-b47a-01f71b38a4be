{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Trigger from 'rc-trigger';\nvar BUILT_IN_PLACEMENTS = {\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  }\n};\nfunction PickerTrigger(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    popupElement = _ref.popupElement,\n    popupStyle = _ref.popupStyle,\n    visible = _ref.visible,\n    dropdownClassName = _ref.dropdownClassName,\n    dropdownAlign = _ref.dropdownAlign,\n    transitionName = _ref.transitionName,\n    getPopupContainer = _ref.getPopupContainer,\n    children = _ref.children,\n    range = _ref.range,\n    popupPlacement = _ref.popupPlacement,\n    direction = _ref.direction;\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var getPopupPlacement = function getPopupPlacement() {\n    if (popupPlacement !== undefined) {\n      return popupPlacement;\n    }\n    return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n  };\n  return /*#__PURE__*/React.createElement(Trigger, {\n    showAction: [],\n    hideAction: [],\n    popupPlacement: getPopupPlacement(),\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: transitionName,\n    popup: popupElement,\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    popupClassName: classNames(dropdownClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(dropdownPrefixCls, \"-range\"), range), _defineProperty(_classNames, \"\".concat(dropdownPrefixCls, \"-rtl\"), direction === 'rtl'), _classNames)),\n    popupStyle: popupStyle,\n    getPopupContainer: getPopupContainer\n  }, children);\n}\nexport default PickerTrigger;", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "<PERSON><PERSON>", "BUILT_IN_PLACEMENTS", "bottomLeft", "points", "offset", "overflow", "adjustX", "adjustY", "bottomRight", "topLeft", "topRight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_classNames", "prefixCls", "popupElement", "popupStyle", "visible", "dropdownClassName", "dropdownAlign", "transitionName", "getPopupContainer", "children", "range", "popupPlacement", "direction", "dropdownPrefixCls", "concat", "getPopupPlacement", "undefined", "createElement", "showAction", "hideAction", "builtinPlacements", "popupTransitionName", "popup", "popupAlign", "popupVisible", "popupClassName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/PickerTrigger.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Trigger from 'rc-trigger';\nvar BUILT_IN_PLACEMENTS = {\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  }\n};\nfunction PickerTrigger(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    popupElement = _ref.popupElement,\n    popupStyle = _ref.popupStyle,\n    visible = _ref.visible,\n    dropdownClassName = _ref.dropdownClassName,\n    dropdownAlign = _ref.dropdownAlign,\n    transitionName = _ref.transitionName,\n    getPopupContainer = _ref.getPopupContainer,\n    children = _ref.children,\n    range = _ref.range,\n    popupPlacement = _ref.popupPlacement,\n    direction = _ref.direction;\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var getPopupPlacement = function getPopupPlacement() {\n    if (popupPlacement !== undefined) {\n      return popupPlacement;\n    }\n    return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n  };\n  return /*#__PURE__*/React.createElement(Trigger, {\n    showAction: [],\n    hideAction: [],\n    popupPlacement: getPopupPlacement(),\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: transitionName,\n    popup: popupElement,\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    popupClassName: classNames(dropdownClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(dropdownPrefixCls, \"-range\"), range), _defineProperty(_classNames, \"\".concat(dropdownPrefixCls, \"-rtl\"), direction === 'rtl'), _classNames)),\n    popupStyle: popupStyle,\n    getPopupContainer: getPopupContainer\n  }, children);\n}\nexport default PickerTrigger;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,YAAY;AAChC,IAAIC,mBAAmB,GAAG;EACxBC,UAAU,EAAE;IACVC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,WAAW,EAAE;IACXL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDE,OAAO,EAAE;IACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC;AACD,SAASI,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,YAAY,GAAGH,IAAI,CAACG,YAAY;IAChCC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,OAAO,GAAGL,IAAI,CAACK,OAAO;IACtBC,iBAAiB,GAAGN,IAAI,CAACM,iBAAiB;IAC1CC,aAAa,GAAGP,IAAI,CAACO,aAAa;IAClCC,cAAc,GAAGR,IAAI,CAACQ,cAAc;IACpCC,iBAAiB,GAAGT,IAAI,CAACS,iBAAiB;IAC1CC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;IACxBC,KAAK,GAAGX,IAAI,CAACW,KAAK;IAClBC,cAAc,GAAGZ,IAAI,CAACY,cAAc;IACpCC,SAAS,GAAGb,IAAI,CAACa,SAAS;EAC5B,IAAIC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACb,SAAS,EAAE,WAAW,CAAC;EACzD,IAAIc,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIJ,cAAc,KAAKK,SAAS,EAAE;MAChC,OAAOL,cAAc;IACvB;IACA,OAAOC,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,YAAY;EAC3D,CAAC;EACD,OAAO,aAAa3B,KAAK,CAACgC,aAAa,CAAC9B,OAAO,EAAE;IAC/C+B,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdR,cAAc,EAAEI,iBAAiB,CAAC,CAAC;IACnCK,iBAAiB,EAAEhC,mBAAmB;IACtCa,SAAS,EAAEY,iBAAiB;IAC5BQ,mBAAmB,EAAEd,cAAc;IACnCe,KAAK,EAAEpB,YAAY;IACnBqB,UAAU,EAAEjB,aAAa;IACzBkB,YAAY,EAAEpB,OAAO;IACrBqB,cAAc,EAAEvC,UAAU,CAACmB,iBAAiB,GAAGL,WAAW,GAAG,CAAC,CAAC,EAAEhB,eAAe,CAACgB,WAAW,EAAE,EAAE,CAACc,MAAM,CAACD,iBAAiB,EAAE,QAAQ,CAAC,EAAEH,KAAK,CAAC,EAAE1B,eAAe,CAACgB,WAAW,EAAE,EAAE,CAACc,MAAM,CAACD,iBAAiB,EAAE,MAAM,CAAC,EAAED,SAAS,KAAK,KAAK,CAAC,EAAEZ,WAAW,CAAC,CAAC;IACpPG,UAAU,EAAEA,UAAU;IACtBK,iBAAiB,EAAEA;EACrB,CAAC,EAAEC,QAAQ,CAAC;AACd;AACA,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}