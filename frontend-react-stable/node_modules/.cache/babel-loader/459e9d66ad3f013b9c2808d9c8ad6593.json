{"ast": null, "code": "/** @license React v17.0.2\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var _assign = require('object-assign');\n\n    // TODO: this is special because it gets imported during build.\n    var ReactVersion = '17.0.2';\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    exports.Fragment = 0xeacb;\n    exports.StrictMode = 0xeacc;\n    exports.Profiler = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    exports.Suspense = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_BLOCK_TYPE = 0xead9;\n    var REACT_SERVER_BLOCK_TYPE = 0xeada;\n    var REACT_FUNDAMENTAL_TYPE = 0xead5;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_OPAQUE_ID_TYPE = 0xeae0;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      exports.Fragment = symbolFor('react.fragment');\n      exports.StrictMode = symbolFor('react.strict_mode');\n      exports.Profiler = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      exports.Suspense = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_BLOCK_TYPE = symbolFor('react.block');\n      REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n      REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n    }\n    var MAYBE_ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = '@@iterator';\n    function getIteratorFn(maybeIterable) {\n      if (maybeIterable === null || typeof maybeIterable !== 'object') {\n        return null;\n      }\n      var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n      if (typeof maybeIterator === 'function') {\n        return maybeIterator;\n      }\n      return null;\n    }\n\n    /**\n     * Keeps track of the current dispatcher.\n     */\n    var ReactCurrentDispatcher = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n\n    /**\n     * Keeps track of the current batch's configuration such as how long an update\n     * should suspend for if it needs to.\n     */\n    var ReactCurrentBatchConfig = {\n      transition: 0\n    };\n\n    /**\n     * Keeps track of the current owner.\n     *\n     * The current owner is the component who should own any components that are\n     * currently being constructed.\n     */\n    var ReactCurrentOwner = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n    var ReactDebugCurrentFrame = {};\n    var currentExtraStackFrame = null;\n    function setExtraStackFrame(stack) {\n      {\n        currentExtraStackFrame = stack;\n      }\n    }\n    {\n      ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n        {\n          currentExtraStackFrame = stack;\n        }\n      }; // Stack implementation injected by the current renderer.\n\n      ReactDebugCurrentFrame.getCurrentStack = null;\n      ReactDebugCurrentFrame.getStackAddendum = function () {\n        var stack = ''; // Add an extra top frame while an element is being validated\n\n        if (currentExtraStackFrame) {\n          stack += currentExtraStackFrame;\n        } // Delegate to the injected renderer-specific implementation\n\n        var impl = ReactDebugCurrentFrame.getCurrentStack;\n        if (impl) {\n          stack += impl() || '';\n        }\n        return stack;\n      };\n    }\n\n    /**\n     * Used by act() to track whether you're inside an act() scope.\n     */\n    var IsSomeRendererActing = {\n      current: false\n    };\n    var ReactSharedInternals = {\n      ReactCurrentDispatcher: ReactCurrentDispatcher,\n      ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n      ReactCurrentOwner: ReactCurrentOwner,\n      IsSomeRendererActing: IsSomeRendererActing,\n      // Used by renderers to avoid bundling object-assign twice in UMD bundles:\n      assign: _assign\n    };\n    {\n      ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n    }\n\n    // by calls to these methods by a Babel plugin.\n    //\n    // In PROD (or in packages without access to React internals),\n    // they are left as they are instead.\n\n    function warn(format) {\n      {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        printWarning('warn', format, args);\n      }\n    }\n    function error(format) {\n      {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        printWarning('error', format, args);\n      }\n    }\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        }\n        var argsWithFormat = args.map(function (item) {\n          return '' + item;\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    }\n    var didWarnStateUpdateForUnmountedComponent = {};\n    function warnNoop(publicInstance, callerName) {\n      {\n        var _constructor = publicInstance.constructor;\n        var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n        var warningKey = componentName + \".\" + callerName;\n        if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n          return;\n        }\n        error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n        didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n      }\n    }\n    /**\n     * This is the abstract API for an update queue.\n     */\n\n    var ReactNoopUpdateQueue = {\n      /**\n       * Checks whether or not this composite component is mounted.\n       * @param {ReactClass} publicInstance The instance we want to test.\n       * @return {boolean} True if mounted, false otherwise.\n       * @protected\n       * @final\n       */\n      isMounted: function (publicInstance) {\n        return false;\n      },\n      /**\n       * Forces an update. This should only be invoked when it is known with\n       * certainty that we are **not** in a DOM transaction.\n       *\n       * You may want to call this when you know that some deeper aspect of the\n       * component's state has changed but `setState` was not called.\n       *\n       * This will not invoke `shouldComponentUpdate`, but it will invoke\n       * `componentWillUpdate` and `componentDidUpdate`.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueForceUpdate: function (publicInstance, callback, callerName) {\n        warnNoop(publicInstance, 'forceUpdate');\n      },\n      /**\n       * Replaces all of the state. Always use this or `setState` to mutate state.\n       * You should treat `this.state` as immutable.\n       *\n       * There is no guarantee that `this.state` will be immediately updated, so\n       * accessing `this.state` after calling this method may return the old value.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} completeState Next state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n        warnNoop(publicInstance, 'replaceState');\n      },\n      /**\n       * Sets a subset of the state. This only exists because _pendingState is\n       * internal. This provides a merging strategy that is not available to deep\n       * properties which is confusing. TODO: Expose pendingState or don't use it\n       * during the merge.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} partialState Next partial state to be merged with state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} Name of the calling function in the public API.\n       * @internal\n       */\n      enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n        warnNoop(publicInstance, 'setState');\n      }\n    };\n    var emptyObject = {};\n    {\n      Object.freeze(emptyObject);\n    }\n    /**\n     * Base class helpers for the updating state of a component.\n     */\n\n    function Component(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n      // renderer.\n\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n    Component.prototype.isReactComponent = {};\n    /**\n     * Sets a subset of the state. Always use this to mutate\n     * state. You should treat `this.state` as immutable.\n     *\n     * There is no guarantee that `this.state` will be immediately updated, so\n     * accessing `this.state` after calling this method may return the old value.\n     *\n     * There is no guarantee that calls to `setState` will run synchronously,\n     * as they may eventually be batched together.  You can provide an optional\n     * callback that will be executed when the call to setState is actually\n     * completed.\n     *\n     * When a function is provided to setState, it will be called at some point in\n     * the future (not synchronously). It will be called with the up to date\n     * component arguments (state, props, context). These values can be different\n     * from this.* because your function may be called after receiveProps but before\n     * shouldComponentUpdate, and this new state, props, and context will not yet be\n     * assigned to this.\n     *\n     * @param {object|function} partialState Next partial state or function to\n     *        produce next partial state to be merged with current state.\n     * @param {?function} callback Called after state is updated.\n     * @final\n     * @protected\n     */\n\n    Component.prototype.setState = function (partialState, callback) {\n      if (!(typeof partialState === 'object' || typeof partialState === 'function' || partialState == null)) {\n        {\n          throw Error(\"setState(...): takes an object of state variables to update or a function which returns an object of state variables.\");\n        }\n      }\n      this.updater.enqueueSetState(this, partialState, callback, 'setState');\n    };\n    /**\n     * Forces an update. This should only be invoked when it is known with\n     * certainty that we are **not** in a DOM transaction.\n     *\n     * You may want to call this when you know that some deeper aspect of the\n     * component's state has changed but `setState` was not called.\n     *\n     * This will not invoke `shouldComponentUpdate`, but it will invoke\n     * `componentWillUpdate` and `componentDidUpdate`.\n     *\n     * @param {?function} callback Called after update is complete.\n     * @final\n     * @protected\n     */\n\n    Component.prototype.forceUpdate = function (callback) {\n      this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n    };\n    /**\n     * Deprecated APIs. These APIs used to exist on classic React classes but since\n     * we would like to deprecate them, we're not going to move them over to this\n     * modern base class. Instead, we define a getter that warns if it's accessed.\n     */\n\n    {\n      var deprecatedAPIs = {\n        isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n        replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n      };\n      var defineDeprecationWarning = function (methodName, info) {\n        Object.defineProperty(Component.prototype, methodName, {\n          get: function () {\n            warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n            return undefined;\n          }\n        });\n      };\n      for (var fnName in deprecatedAPIs) {\n        if (deprecatedAPIs.hasOwnProperty(fnName)) {\n          defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n        }\n      }\n    }\n    function ComponentDummy() {}\n    ComponentDummy.prototype = Component.prototype;\n    /**\n     * Convenience component with default shallow equality check for sCU.\n     */\n\n    function PureComponent(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject;\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n    var pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\n    pureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\n    _assign(pureComponentPrototype, Component.prototype);\n    pureComponentPrototype.isPureReactComponent = true;\n\n    // an immutable object with a single mutable value\n    function createRef() {\n      var refObject = {\n        current: null\n      };\n      {\n        Object.seal(refObject);\n      }\n      return refObject;\n    }\n    function getWrappedName(outerType, innerType, wrapperName) {\n      var functionName = innerType.displayName || innerType.name || '';\n      return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n    }\n    function getContextName(type) {\n      return type.displayName || 'Context';\n    }\n    function getComponentName(type) {\n      if (type == null) {\n        // Host root, text node or just invalid type.\n        return null;\n      }\n      {\n        if (typeof type.tag === 'number') {\n          error('Received an unexpected object in getComponentName(). ' + 'This is likely a bug in React. Please file an issue.');\n        }\n      }\n      if (typeof type === 'function') {\n        return type.displayName || type.name || null;\n      }\n      if (typeof type === 'string') {\n        return type;\n      }\n      switch (type) {\n        case exports.Fragment:\n          return 'Fragment';\n        case REACT_PORTAL_TYPE:\n          return 'Portal';\n        case exports.Profiler:\n          return 'Profiler';\n        case exports.StrictMode:\n          return 'StrictMode';\n        case exports.Suspense:\n          return 'Suspense';\n        case REACT_SUSPENSE_LIST_TYPE:\n          return 'SuspenseList';\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_CONTEXT_TYPE:\n            var context = type;\n            return getContextName(context) + '.Consumer';\n          case REACT_PROVIDER_TYPE:\n            var provider = type;\n            return getContextName(provider._context) + '.Provider';\n          case REACT_FORWARD_REF_TYPE:\n            return getWrappedName(type, type.render, 'ForwardRef');\n          case REACT_MEMO_TYPE:\n            return getComponentName(type.type);\n          case REACT_BLOCK_TYPE:\n            return getComponentName(type._render);\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                return getComponentName(init(payload));\n              } catch (x) {\n                return null;\n              }\n            }\n        }\n      }\n      return null;\n    }\n    var hasOwnProperty = Object.prototype.hasOwnProperty;\n    var RESERVED_PROPS = {\n      key: true,\n      ref: true,\n      __self: true,\n      __source: true\n    };\n    var specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n    {\n      didWarnAboutStringRefs = {};\n    }\n    function hasValidRef(config) {\n      {\n        if (hasOwnProperty.call(config, 'ref')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.ref !== undefined;\n    }\n    function hasValidKey(config) {\n      {\n        if (hasOwnProperty.call(config, 'key')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.key !== undefined;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      var warnAboutAccessingKey = function () {\n        {\n          if (!specialPropKeyWarningShown) {\n            specialPropKeyWarningShown = true;\n            error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n      warnAboutAccessingKey.isReactWarning = true;\n      Object.defineProperty(props, 'key', {\n        get: warnAboutAccessingKey,\n        configurable: true\n      });\n    }\n    function defineRefPropWarningGetter(props, displayName) {\n      var warnAboutAccessingRef = function () {\n        {\n          if (!specialPropRefWarningShown) {\n            specialPropRefWarningShown = true;\n            error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n    function warnIfStringRefCannotBeAutoConverted(config) {\n      {\n        if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n          var componentName = getComponentName(ReactCurrentOwner.current.type);\n          if (!didWarnAboutStringRefs[componentName]) {\n            error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n            didWarnAboutStringRefs[componentName] = true;\n          }\n        }\n      }\n    }\n    /**\n     * Factory method to create a new React element. This no longer adheres to\n     * the class pattern, so do not use new to call it. Also, instanceof check\n     * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n     * if something is a React Element.\n     *\n     * @param {*} type\n     * @param {*} props\n     * @param {*} key\n     * @param {string|object} ref\n     * @param {*} owner\n     * @param {*} self A *temporary* helper to detect places where `this` is\n     * different from the `owner` when React.createElement is called, so that we\n     * can warn. We want to get rid of owner and replace string `ref`s with arrow\n     * functions, and as long as `this` and owner are the same, there will be no\n     * change in behavior.\n     * @param {*} source An annotation object (added by a transpiler or otherwise)\n     * indicating filename, line number, and/or other information.\n     * @internal\n     */\n\n    var ReactElement = function (type, key, ref, self, source, owner, props) {\n      var element = {\n        // This tag allows us to uniquely identify this as a React Element\n        $$typeof: REACT_ELEMENT_TYPE,\n        // Built-in properties that belong on the element\n        type: type,\n        key: key,\n        ref: ref,\n        props: props,\n        // Record the component responsible for creating this element.\n        _owner: owner\n      };\n      {\n        // The validation flag is currently mutative. We put it on\n        // an external backing store so that we can freeze the whole object.\n        // This can be replaced with a WeakMap once they are implemented in\n        // commonly used development environments.\n        element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n        // the validation flag non-enumerable (where possible, which should\n        // include every environment we run tests in), so the test framework\n        // ignores it.\n\n        Object.defineProperty(element._store, 'validated', {\n          configurable: false,\n          enumerable: false,\n          writable: true,\n          value: false\n        }); // self and source are DEV only properties.\n\n        Object.defineProperty(element, '_self', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: self\n        }); // Two elements created in two different places should be considered\n        // equal for testing purposes and therefore we hide it from enumeration.\n\n        Object.defineProperty(element, '_source', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: source\n        });\n        if (Object.freeze) {\n          Object.freeze(element.props);\n          Object.freeze(element);\n        }\n      }\n      return element;\n    };\n    /**\n     * Create and return a new ReactElement of the given type.\n     * See https://reactjs.org/docs/react-api.html#createelement\n     */\n\n    function createElement(type, config, children) {\n      var propName; // Reserved names are extracted\n\n      var props = {};\n      var key = null;\n      var ref = null;\n      var self = null;\n      var source = null;\n      if (config != null) {\n        if (hasValidRef(config)) {\n          ref = config.ref;\n          {\n            warnIfStringRefCannotBeAutoConverted(config);\n          }\n        }\n        if (hasValidKey(config)) {\n          key = '' + config.key;\n        }\n        self = config.__self === undefined ? null : config.__self;\n        source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            props[propName] = config[propName];\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n      var childrenLength = arguments.length - 2;\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n        {\n          if (Object.freeze) {\n            Object.freeze(childArray);\n          }\n        }\n        props.children = childArray;\n      } // Resolve default props\n\n      if (type && type.defaultProps) {\n        var defaultProps = type.defaultProps;\n        for (propName in defaultProps) {\n          if (props[propName] === undefined) {\n            props[propName] = defaultProps[propName];\n          }\n        }\n      }\n      {\n        if (key || ref) {\n          var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n          if (key) {\n            defineKeyPropWarningGetter(props, displayName);\n          }\n          if (ref) {\n            defineRefPropWarningGetter(props, displayName);\n          }\n        }\n      }\n      return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n    }\n    function cloneAndReplaceKey(oldElement, newKey) {\n      var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n      return newElement;\n    }\n    /**\n     * Clone and return a new ReactElement using element as the starting point.\n     * See https://reactjs.org/docs/react-api.html#cloneelement\n     */\n\n    function cloneElement(element, config, children) {\n      if (!!(element === null || element === undefined)) {\n        {\n          throw Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n        }\n      }\n      var propName; // Original props are copied\n\n      var props = _assign({}, element.props); // Reserved names are extracted\n\n      var key = element.key;\n      var ref = element.ref; // Self is preserved since the owner is preserved.\n\n      var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n      // transpiler, and the original source is probably a better indicator of the\n      // true owner.\n\n      var source = element._source; // Owner will be preserved, unless ref is overridden\n\n      var owner = element._owner;\n      if (config != null) {\n        if (hasValidRef(config)) {\n          // Silently steal the ref from the parent.\n          ref = config.ref;\n          owner = ReactCurrentOwner.current;\n        }\n        if (hasValidKey(config)) {\n          key = '' + config.key;\n        } // Remaining properties override existing props\n\n        var defaultProps;\n        if (element.type && element.type.defaultProps) {\n          defaultProps = element.type.defaultProps;\n        }\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            if (config[propName] === undefined && defaultProps !== undefined) {\n              // Resolve default props\n              props[propName] = defaultProps[propName];\n            } else {\n              props[propName] = config[propName];\n            }\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n      var childrenLength = arguments.length - 2;\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n        props.children = childArray;\n      }\n      return ReactElement(element.type, key, ref, self, source, owner, props);\n    }\n    /**\n     * Verifies the object is a ReactElement.\n     * See https://reactjs.org/docs/react-api.html#isvalidelement\n     * @param {?object} object\n     * @return {boolean} True if `object` is a ReactElement.\n     * @final\n     */\n\n    function isValidElement(object) {\n      return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n    }\n    var SEPARATOR = '.';\n    var SUBSEPARATOR = ':';\n    /**\n     * Escape and wrap key so it is safe to use as a reactid\n     *\n     * @param {string} key to be escaped.\n     * @return {string} the escaped key.\n     */\n\n    function escape(key) {\n      var escapeRegex = /[=:]/g;\n      var escaperLookup = {\n        '=': '=0',\n        ':': '=2'\n      };\n      var escapedString = key.replace(escapeRegex, function (match) {\n        return escaperLookup[match];\n      });\n      return '$' + escapedString;\n    }\n    /**\n     * TODO: Test that a single child and an array with one item have the same key\n     * pattern.\n     */\n\n    var didWarnAboutMaps = false;\n    var userProvidedKeyEscapeRegex = /\\/+/g;\n    function escapeUserProvidedKey(text) {\n      return text.replace(userProvidedKeyEscapeRegex, '$&/');\n    }\n    /**\n     * Generate a key string that identifies a element within a set.\n     *\n     * @param {*} element A element that could contain a manual key.\n     * @param {number} index Index that is used if a manual key is not provided.\n     * @return {string}\n     */\n\n    function getElementKey(element, index) {\n      // Do some typechecking here since we call this blindly. We want to ensure\n      // that we don't block potential future ES APIs.\n      if (typeof element === 'object' && element !== null && element.key != null) {\n        // Explicit key\n        return escape('' + element.key);\n      } // Implicit key determined by the index in the set\n\n      return index.toString(36);\n    }\n    function mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n      var type = typeof children;\n      if (type === 'undefined' || type === 'boolean') {\n        // All of the above are perceived as null.\n        children = null;\n      }\n      var invokeCallback = false;\n      if (children === null) {\n        invokeCallback = true;\n      } else {\n        switch (type) {\n          case 'string':\n          case 'number':\n            invokeCallback = true;\n            break;\n          case 'object':\n            switch (children.$$typeof) {\n              case REACT_ELEMENT_TYPE:\n              case REACT_PORTAL_TYPE:\n                invokeCallback = true;\n            }\n        }\n      }\n      if (invokeCallback) {\n        var _child = children;\n        var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n        // so that it's consistent if the number of children grows:\n\n        var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n        if (Array.isArray(mappedChild)) {\n          var escapedChildKey = '';\n          if (childKey != null) {\n            escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n          }\n          mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n            return c;\n          });\n        } else if (mappedChild != null) {\n          if (isValidElement(mappedChild)) {\n            mappedChild = cloneAndReplaceKey(mappedChild,\n            // Keep both the (mapped) and old keys if they differ, just as\n            // traverseAllChildren used to do for objects as children\n            escapedPrefix + (\n            // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n            mappedChild.key && (!_child || _child.key !== mappedChild.key) ?\n            // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n            escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n          }\n          array.push(mappedChild);\n        }\n        return 1;\n      }\n      var child;\n      var nextName;\n      var subtreeCount = 0; // Count of children found in the current subtree.\n\n      var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n      if (Array.isArray(children)) {\n        for (var i = 0; i < children.length; i++) {\n          child = children[i];\n          nextName = nextNamePrefix + getElementKey(child, i);\n          subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n        }\n      } else {\n        var iteratorFn = getIteratorFn(children);\n        if (typeof iteratorFn === 'function') {\n          var iterableChildren = children;\n          {\n            // Warn about using Maps as children\n            if (iteratorFn === iterableChildren.entries) {\n              if (!didWarnAboutMaps) {\n                warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n              }\n              didWarnAboutMaps = true;\n            }\n          }\n          var iterator = iteratorFn.call(iterableChildren);\n          var step;\n          var ii = 0;\n          while (!(step = iterator.next()).done) {\n            child = step.value;\n            nextName = nextNamePrefix + getElementKey(child, ii++);\n            subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n          }\n        } else if (type === 'object') {\n          var childrenString = '' + children;\n          {\n            {\n              throw Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). If you meant to render a collection of children, use an array instead.\");\n            }\n          }\n        }\n      }\n      return subtreeCount;\n    }\n\n    /**\n     * Maps children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n     *\n     * The provided mapFunction(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} func The map function.\n     * @param {*} context Context for mapFunction.\n     * @return {object} Object containing the ordered map of results.\n     */\n    function mapChildren(children, func, context) {\n      if (children == null) {\n        return children;\n      }\n      var result = [];\n      var count = 0;\n      mapIntoArray(children, result, '', '', function (child) {\n        return func.call(context, child, count++);\n      });\n      return result;\n    }\n    /**\n     * Count the number of children that are typically specified as\n     * `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrencount\n     *\n     * @param {?*} children Children tree container.\n     * @return {number} The number of children.\n     */\n\n    function countChildren(children) {\n      var n = 0;\n      mapChildren(children, function () {\n        n++; // Don't return anything\n      });\n      return n;\n    }\n\n    /**\n     * Iterates through children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n     *\n     * The provided forEachFunc(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} forEachFunc\n     * @param {*} forEachContext Context for forEachContext.\n     */\n    function forEachChildren(children, forEachFunc, forEachContext) {\n      mapChildren(children, function () {\n        forEachFunc.apply(this, arguments); // Don't return anything.\n      }, forEachContext);\n    }\n    /**\n     * Flatten a children object (typically specified as `props.children`) and\n     * return an array with appropriately re-keyed children.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n     */\n\n    function toArray(children) {\n      return mapChildren(children, function (child) {\n        return child;\n      }) || [];\n    }\n    /**\n     * Returns the first child in a collection of children and verifies that there\n     * is only one child in the collection.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n     *\n     * The current implementation of this function assumes that a single child gets\n     * passed without a wrapper, but the purpose of this helper function is to\n     * abstract away the particular structure of children.\n     *\n     * @param {?object} children Child collection structure.\n     * @return {ReactElement} The first and only `ReactElement` contained in the\n     * structure.\n     */\n\n    function onlyChild(children) {\n      if (!isValidElement(children)) {\n        {\n          throw Error(\"React.Children.only expected to receive a single React element child.\");\n        }\n      }\n      return children;\n    }\n    function createContext(defaultValue, calculateChangedBits) {\n      if (calculateChangedBits === undefined) {\n        calculateChangedBits = null;\n      } else {\n        {\n          if (calculateChangedBits !== null && typeof calculateChangedBits !== 'function') {\n            error('createContext: Expected the optional second argument to be a ' + 'function. Instead received: %s', calculateChangedBits);\n          }\n        }\n      }\n      var context = {\n        $$typeof: REACT_CONTEXT_TYPE,\n        _calculateChangedBits: calculateChangedBits,\n        // As a workaround to support multiple concurrent renderers, we categorize\n        // some renderers as primary and others as secondary. We only expect\n        // there to be two concurrent renderers at most: React Native (primary) and\n        // Fabric (secondary); React DOM (primary) and React ART (secondary).\n        // Secondary renderers store their context values on separate fields.\n        _currentValue: defaultValue,\n        _currentValue2: defaultValue,\n        // Used to track how many concurrent renderers this context currently\n        // supports within in a single renderer. Such as parallel server rendering.\n        _threadCount: 0,\n        // These are circular\n        Provider: null,\n        Consumer: null\n      };\n      context.Provider = {\n        $$typeof: REACT_PROVIDER_TYPE,\n        _context: context\n      };\n      var hasWarnedAboutUsingNestedContextConsumers = false;\n      var hasWarnedAboutUsingConsumerProvider = false;\n      var hasWarnedAboutDisplayNameOnConsumer = false;\n      {\n        // A separate object, but proxies back to the original context object for\n        // backwards compatibility. It has a different $$typeof, so we can properly\n        // warn for the incorrect usage of Context as a Consumer.\n        var Consumer = {\n          $$typeof: REACT_CONTEXT_TYPE,\n          _context: context,\n          _calculateChangedBits: context._calculateChangedBits\n        }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n        Object.defineProperties(Consumer, {\n          Provider: {\n            get: function () {\n              if (!hasWarnedAboutUsingConsumerProvider) {\n                hasWarnedAboutUsingConsumerProvider = true;\n                error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n              }\n              return context.Provider;\n            },\n            set: function (_Provider) {\n              context.Provider = _Provider;\n            }\n          },\n          _currentValue: {\n            get: function () {\n              return context._currentValue;\n            },\n            set: function (_currentValue) {\n              context._currentValue = _currentValue;\n            }\n          },\n          _currentValue2: {\n            get: function () {\n              return context._currentValue2;\n            },\n            set: function (_currentValue2) {\n              context._currentValue2 = _currentValue2;\n            }\n          },\n          _threadCount: {\n            get: function () {\n              return context._threadCount;\n            },\n            set: function (_threadCount) {\n              context._threadCount = _threadCount;\n            }\n          },\n          Consumer: {\n            get: function () {\n              if (!hasWarnedAboutUsingNestedContextConsumers) {\n                hasWarnedAboutUsingNestedContextConsumers = true;\n                error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n              }\n              return context.Consumer;\n            }\n          },\n          displayName: {\n            get: function () {\n              return context.displayName;\n            },\n            set: function (displayName) {\n              if (!hasWarnedAboutDisplayNameOnConsumer) {\n                warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n                hasWarnedAboutDisplayNameOnConsumer = true;\n              }\n            }\n          }\n        }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n        context.Consumer = Consumer;\n      }\n      {\n        context._currentRenderer = null;\n        context._currentRenderer2 = null;\n      }\n      return context;\n    }\n    var Uninitialized = -1;\n    var Pending = 0;\n    var Resolved = 1;\n    var Rejected = 2;\n    function lazyInitializer(payload) {\n      if (payload._status === Uninitialized) {\n        var ctor = payload._result;\n        var thenable = ctor(); // Transition to the next state.\n\n        var pending = payload;\n        pending._status = Pending;\n        pending._result = thenable;\n        thenable.then(function (moduleObject) {\n          if (payload._status === Pending) {\n            var defaultExport = moduleObject.default;\n            {\n              if (defaultExport === undefined) {\n                error('lazy: Expected the result of a dynamic import() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' +\n                // Break up imports to avoid accidentally parsing them as dependencies.\n                'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n              }\n            } // Transition to the next state.\n\n            var resolved = payload;\n            resolved._status = Resolved;\n            resolved._result = defaultExport;\n          }\n        }, function (error) {\n          if (payload._status === Pending) {\n            // Transition to the next state.\n            var rejected = payload;\n            rejected._status = Rejected;\n            rejected._result = error;\n          }\n        });\n      }\n      if (payload._status === Resolved) {\n        return payload._result;\n      } else {\n        throw payload._result;\n      }\n    }\n    function lazy(ctor) {\n      var payload = {\n        // We use these fields to store the result.\n        _status: -1,\n        _result: ctor\n      };\n      var lazyType = {\n        $$typeof: REACT_LAZY_TYPE,\n        _payload: payload,\n        _init: lazyInitializer\n      };\n      {\n        // In production, this would just set it on the object.\n        var defaultProps;\n        var propTypes; // $FlowFixMe\n\n        Object.defineProperties(lazyType, {\n          defaultProps: {\n            configurable: true,\n            get: function () {\n              return defaultProps;\n            },\n            set: function (newDefaultProps) {\n              error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              defaultProps = newDefaultProps; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'defaultProps', {\n                enumerable: true\n              });\n            }\n          },\n          propTypes: {\n            configurable: true,\n            get: function () {\n              return propTypes;\n            },\n            set: function (newPropTypes) {\n              error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              propTypes = newPropTypes; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'propTypes', {\n                enumerable: true\n              });\n            }\n          }\n        });\n      }\n      return lazyType;\n    }\n    function forwardRef(render) {\n      {\n        if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n          error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n        } else if (typeof render !== 'function') {\n          error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n        } else {\n          if (render.length !== 0 && render.length !== 2) {\n            error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n          }\n        }\n        if (render != null) {\n          if (render.defaultProps != null || render.propTypes != null) {\n            error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n          }\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_FORWARD_REF_TYPE,\n        render: render\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name;\n            if (render.displayName == null) {\n              render.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n\n    // Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\n    var enableScopeAPI = false; // Experimental Create Event Handle API.\n\n    function isValidElementType(type) {\n      if (typeof type === 'string' || typeof type === 'function') {\n        return true;\n      } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n      if (type === exports.Fragment || type === exports.Profiler || type === REACT_DEBUG_TRACING_MODE_TYPE || type === exports.StrictMode || type === exports.Suspense || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI) {\n        return true;\n      }\n      if (typeof type === 'object' && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n          return true;\n        }\n      }\n      return false;\n    }\n    function memo(type, compare) {\n      {\n        if (!isValidElementType(type)) {\n          error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_MEMO_TYPE,\n        type: type,\n        compare: compare === undefined ? null : compare\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name;\n            if (type.displayName == null) {\n              type.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n    function resolveDispatcher() {\n      var dispatcher = ReactCurrentDispatcher.current;\n      if (!(dispatcher !== null)) {\n        {\n          throw Error(\"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)\\n2. You might be breaking the Rules of Hooks\\n3. You might have more than one copy of React in the same app\\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.\");\n        }\n      }\n      return dispatcher;\n    }\n    function useContext(Context, unstable_observedBits) {\n      var dispatcher = resolveDispatcher();\n      {\n        if (unstable_observedBits !== undefined) {\n          error('useContext() second argument is reserved for future ' + 'use in React. Passing it is not supported. ' + 'You passed: %s.%s', unstable_observedBits, typeof unstable_observedBits === 'number' && Array.isArray(arguments[2]) ? '\\n\\nDid you call array.map(useContext)? ' + 'Calling Hooks inside a loop is not supported. ' + 'Learn more at https://reactjs.org/link/rules-of-hooks' : '');\n        } // TODO: add a more generic warning for invalid values.\n\n        if (Context._context !== undefined) {\n          var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n          // and nobody should be using this in existing code.\n\n          if (realContext.Consumer === Context) {\n            error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n          } else if (realContext.Provider === Context) {\n            error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n          }\n        }\n      }\n      return dispatcher.useContext(Context, unstable_observedBits);\n    }\n    function useState(initialState) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useState(initialState);\n    }\n    function useReducer(reducer, initialArg, init) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useReducer(reducer, initialArg, init);\n    }\n    function useRef(initialValue) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useRef(initialValue);\n    }\n    function useEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useEffect(create, deps);\n    }\n    function useLayoutEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useLayoutEffect(create, deps);\n    }\n    function useCallback(callback, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useCallback(callback, deps);\n    }\n    function useMemo(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useMemo(create, deps);\n    }\n    function useImperativeHandle(ref, create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useImperativeHandle(ref, create, deps);\n    }\n    function useDebugValue(value, formatterFn) {\n      {\n        var dispatcher = resolveDispatcher();\n        return dispatcher.useDebugValue(value, formatterFn);\n      }\n    }\n\n    // Helpers to patch console.logs to avoid logging during side-effect free\n    // replaying on render function. This currently only patches the object\n    // lazily which won't cover if the log function was extracted eagerly.\n    // We could also eagerly patch the method.\n    var disabledDepth = 0;\n    var prevLog;\n    var prevInfo;\n    var prevWarn;\n    var prevError;\n    var prevGroup;\n    var prevGroupCollapsed;\n    var prevGroupEnd;\n    function disabledLog() {}\n    disabledLog.__reactDisabledLog = true;\n    function disableLogs() {\n      {\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          prevLog = console.log;\n          prevInfo = console.info;\n          prevWarn = console.warn;\n          prevError = console.error;\n          prevGroup = console.group;\n          prevGroupCollapsed = console.groupCollapsed;\n          prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n          var props = {\n            configurable: true,\n            enumerable: true,\n            value: disabledLog,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            info: props,\n            log: props,\n            warn: props,\n            error: props,\n            group: props,\n            groupCollapsed: props,\n            groupEnd: props\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        disabledDepth++;\n      }\n    }\n    function reenableLogs() {\n      {\n        disabledDepth--;\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          var props = {\n            configurable: true,\n            enumerable: true,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            log: _assign({}, props, {\n              value: prevLog\n            }),\n            info: _assign({}, props, {\n              value: prevInfo\n            }),\n            warn: _assign({}, props, {\n              value: prevWarn\n            }),\n            error: _assign({}, props, {\n              value: prevError\n            }),\n            group: _assign({}, props, {\n              value: prevGroup\n            }),\n            groupCollapsed: _assign({}, props, {\n              value: prevGroupCollapsed\n            }),\n            groupEnd: _assign({}, props, {\n              value: prevGroupEnd\n            })\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        if (disabledDepth < 0) {\n          error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n        }\n      }\n    }\n    var ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\n    var prefix;\n    function describeBuiltInComponentFrame(name, source, ownerFn) {\n      {\n        if (prefix === undefined) {\n          // Extract the VM specific prefix used by each line.\n          try {\n            throw Error();\n          } catch (x) {\n            var match = x.stack.trim().match(/\\n( *(at )?)/);\n            prefix = match && match[1] || '';\n          }\n        } // We use the prefix to ensure our stacks line up with native stack frames.\n\n        return '\\n' + prefix + name;\n      }\n    }\n    var reentry = false;\n    var componentFrameCache;\n    {\n      var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n      componentFrameCache = new PossiblyWeakMap();\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      // If something asked for a stack inside a fake render, it should get ignored.\n      if (!fn || reentry) {\n        return '';\n      }\n      {\n        var frame = componentFrameCache.get(fn);\n        if (frame !== undefined) {\n          return frame;\n        }\n      }\n      var control;\n      reentry = true;\n      var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n      Error.prepareStackTrace = undefined;\n      var previousDispatcher;\n      {\n        previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n        // for warnings.\n\n        ReactCurrentDispatcher$1.current = null;\n        disableLogs();\n      }\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            }\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          }\n          fn();\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          // This extracts the first frame from the sample that isn't also in the control.\n          // Skipping one frame that we assume is the frame that calls the two.\n          var sampleLines = sample.stack.split('\\n');\n          var controlLines = control.stack.split('\\n');\n          var s = sampleLines.length - 1;\n          var c = controlLines.length - 1;\n          while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n            // We expect at least one stack frame to be shared.\n            // Typically this will be the root most one. However, stack frames may be\n            // cut off due to maximum stack limits. In this case, one maybe cut off\n            // earlier than the other. We assume that the sample is longer or the same\n            // and there for cut off earlier. So we should find the root most frame in\n            // the sample somewhere in the control.\n            c--;\n          }\n          for (; s >= 1 && c >= 0; s--, c--) {\n            // Next we find the first one that isn't the same which should be the\n            // frame that called our sample function and the control.\n            if (sampleLines[s] !== controlLines[c]) {\n              // In V8, the first line is describing the message but other VMs don't.\n              // If we're about to return the first line, and the control is also on the same\n              // line, that's a pretty good indicator that our sample threw at same line as\n              // the control. I.e. before we entered the sample frame. So we ignore this result.\n              // This can happen if you passed a class to function component, or non-function.\n              if (s !== 1 || c !== 1) {\n                do {\n                  s--;\n                  c--; // We may still have similar intermediate frames from the construct call.\n                  // The next one that isn't the same should be our match though.\n\n                  if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                    // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                    var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at ');\n                    {\n                      if (typeof fn === 'function') {\n                        componentFrameCache.set(fn, _frame);\n                      }\n                    } // Return the line we found.\n\n                    return _frame;\n                  }\n                } while (s >= 1 && c >= 0);\n              }\n              break;\n            }\n          }\n        }\n      } finally {\n        reentry = false;\n        {\n          ReactCurrentDispatcher$1.current = previousDispatcher;\n          reenableLogs();\n        }\n        Error.prepareStackTrace = previousPrepareStackTrace;\n      } // Fallback to just using the name if we couldn't make it throw.\n\n      var name = fn ? fn.displayName || fn.name : '';\n      var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n      {\n        if (typeof fn === 'function') {\n          componentFrameCache.set(fn, syntheticFrame);\n        }\n      }\n      return syntheticFrame;\n    }\n    function describeFunctionComponentFrame(fn, source, ownerFn) {\n      {\n        return describeNativeComponentFrame(fn, false);\n      }\n    }\n    function shouldConstruct(Component) {\n      var prototype = Component.prototype;\n      return !!(prototype && prototype.isReactComponent);\n    }\n    function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n      if (type == null) {\n        return '';\n      }\n      if (typeof type === 'function') {\n        {\n          return describeNativeComponentFrame(type, shouldConstruct(type));\n        }\n      }\n      if (typeof type === 'string') {\n        return describeBuiltInComponentFrame(type);\n      }\n      switch (type) {\n        case exports.Suspense:\n          return describeBuiltInComponentFrame('Suspense');\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame('SuspenseList');\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return describeFunctionComponentFrame(type.render);\n          case REACT_MEMO_TYPE:\n            // Memo may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n          case REACT_BLOCK_TYPE:\n            return describeFunctionComponentFrame(type._render);\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                // Lazy may contain any component type so we recursively resolve it.\n                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n              } catch (x) {}\n            }\n        }\n      }\n      return '';\n    }\n    var loggedTypeFailures = {};\n    var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n    function setCurrentlyValidatingElement(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n        }\n      }\n    }\n    function checkPropTypes(typeSpecs, values, location, componentName, element) {\n      {\n        // $FlowFixMe This is okay but Flow doesn't know it.\n        var has = Function.call.bind(Object.prototype.hasOwnProperty);\n        for (var typeSpecName in typeSpecs) {\n          if (has(typeSpecs, typeSpecName)) {\n            var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n            // fail the render phase where it didn't fail before. So we log it.\n            // After these have been cleaned up, we'll let them throw.\n\n            try {\n              // This is intentionally an invariant that gets caught. It's the same\n              // behavior as without this statement except with a better message.\n              if (typeof typeSpecs[typeSpecName] !== 'function') {\n                var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n                err.name = 'Invariant Violation';\n                throw err;\n              }\n              error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n            } catch (ex) {\n              error$1 = ex;\n            }\n            if (error$1 && !(error$1 instanceof Error)) {\n              setCurrentlyValidatingElement(element);\n              error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n              setCurrentlyValidatingElement(null);\n            }\n            if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n              // Only monitor this failure once because there tends to be a lot of the\n              // same error.\n              loggedTypeFailures[error$1.message] = true;\n              setCurrentlyValidatingElement(element);\n              error('Failed %s type: %s', location, error$1.message);\n              setCurrentlyValidatingElement(null);\n            }\n          }\n        }\n      }\n    }\n    function setCurrentlyValidatingElement$1(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          setExtraStackFrame(stack);\n        } else {\n          setExtraStackFrame(null);\n        }\n      }\n    }\n    var propTypesMisspellWarningShown;\n    {\n      propTypesMisspellWarningShown = false;\n    }\n    function getDeclarationErrorAddendum() {\n      if (ReactCurrentOwner.current) {\n        var name = getComponentName(ReactCurrentOwner.current.type);\n        if (name) {\n          return '\\n\\nCheck the render method of `' + name + '`.';\n        }\n      }\n      return '';\n    }\n    function getSourceInfoErrorAddendum(source) {\n      if (source !== undefined) {\n        var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n        var lineNumber = source.lineNumber;\n        return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n      }\n      return '';\n    }\n    function getSourceInfoErrorAddendumForProps(elementProps) {\n      if (elementProps !== null && elementProps !== undefined) {\n        return getSourceInfoErrorAddendum(elementProps.__source);\n      }\n      return '';\n    }\n    /**\n     * Warn if there's no key explicitly set on dynamic arrays of children or\n     * object keys are not valid. This allows us to keep track of children between\n     * updates.\n     */\n\n    var ownerHasKeyUseWarning = {};\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = getDeclarationErrorAddendum();\n      if (!info) {\n        var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n        if (parentName) {\n          info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n        }\n      }\n      return info;\n    }\n    /**\n     * Warn if the element doesn't have an explicit key assigned to it.\n     * This element is in an array. The array could grow and shrink or be\n     * reordered. All children that haven't already been validated are required to\n     * have a \"key\" property assigned to it. Error statuses are cached so a warning\n     * will only be shown once.\n     *\n     * @internal\n     * @param {ReactElement} element Element that requires a key.\n     * @param {*} parentType element's parent's type.\n     */\n\n    function validateExplicitKey(element, parentType) {\n      if (!element._store || element._store.validated || element.key != null) {\n        return;\n      }\n      element._store.validated = true;\n      var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n      if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n        return;\n      }\n      ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n      // property, it may be the creator of the child that's responsible for\n      // assigning it a key.\n\n      var childOwner = '';\n      if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n        // Give the component that originally created this child.\n        childOwner = \" It was passed a child from \" + getComponentName(element._owner.type) + \".\";\n      }\n      {\n        setCurrentlyValidatingElement$1(element);\n        error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n        setCurrentlyValidatingElement$1(null);\n      }\n    }\n    /**\n     * Ensure that every element either is passed in a static location, in an\n     * array with an explicit keys property defined, or in an object literal\n     * with valid key property.\n     *\n     * @internal\n     * @param {ReactNode} node Statically passed child of any type.\n     * @param {*} parentType node's parent's type.\n     */\n\n    function validateChildKeys(node, parentType) {\n      if (typeof node !== 'object') {\n        return;\n      }\n      if (Array.isArray(node)) {\n        for (var i = 0; i < node.length; i++) {\n          var child = node[i];\n          if (isValidElement(child)) {\n            validateExplicitKey(child, parentType);\n          }\n        }\n      } else if (isValidElement(node)) {\n        // This element was passed in a valid location.\n        if (node._store) {\n          node._store.validated = true;\n        }\n      } else if (node) {\n        var iteratorFn = getIteratorFn(node);\n        if (typeof iteratorFn === 'function') {\n          // Entry iterators used to provide implicit keys,\n          // but now we print a separate warning for them later.\n          if (iteratorFn !== node.entries) {\n            var iterator = iteratorFn.call(node);\n            var step;\n            while (!(step = iterator.next()).done) {\n              if (isValidElement(step.value)) {\n                validateExplicitKey(step.value, parentType);\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * Given an element, validate that its props follow the propTypes definition,\n     * provided by the type.\n     *\n     * @param {ReactElement} element\n     */\n\n    function validatePropTypes(element) {\n      {\n        var type = element.type;\n        if (type === null || type === undefined || typeof type === 'string') {\n          return;\n        }\n        var propTypes;\n        if (typeof type === 'function') {\n          propTypes = type.propTypes;\n        } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        // Note: Memo only checks outer props here.\n        // Inner props are checked in the reconciler.\n        type.$$typeof === REACT_MEMO_TYPE)) {\n          propTypes = type.propTypes;\n        } else {\n          return;\n        }\n        if (propTypes) {\n          // Intentionally inside to avoid triggering lazy initializers:\n          var name = getComponentName(type);\n          checkPropTypes(propTypes, element.props, 'prop', name, element);\n        } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n          propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n          var _name = getComponentName(type);\n          error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n        }\n        if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n          error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n        }\n      }\n    }\n    /**\n     * Given a fragment, validate that it can only be provided with fragment props\n     * @param {ReactElement} fragment\n     */\n\n    function validateFragmentProps(fragment) {\n      {\n        var keys = Object.keys(fragment.props);\n        for (var i = 0; i < keys.length; i++) {\n          var key = keys[i];\n          if (key !== 'children' && key !== 'key') {\n            setCurrentlyValidatingElement$1(fragment);\n            error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n            setCurrentlyValidatingElement$1(null);\n            break;\n          }\n        }\n        if (fragment.ref !== null) {\n          setCurrentlyValidatingElement$1(fragment);\n          error('Invalid attribute `ref` supplied to `React.Fragment`.');\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n    function createElementWithValidation(type, props, children) {\n      var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n\n      if (!validType) {\n        var info = '';\n        if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n          info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n        }\n        var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n        if (sourceInfo) {\n          info += sourceInfo;\n        } else {\n          info += getDeclarationErrorAddendum();\n        }\n        var typeString;\n        if (type === null) {\n          typeString = 'null';\n        } else if (Array.isArray(type)) {\n          typeString = 'array';\n        } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n          typeString = \"<\" + (getComponentName(type.type) || 'Unknown') + \" />\";\n          info = ' Did you accidentally export a JSX literal instead of a component?';\n        } else {\n          typeString = typeof type;\n        }\n        {\n          error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n        }\n      }\n      var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n      // TODO: Drop this when these are no longer allowed as the type argument.\n\n      if (element == null) {\n        return element;\n      } // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing errors.\n      // We don't want exception behavior to differ between dev and prod.\n      // (Rendering will throw with a helpful message and as soon as the type is\n      // fixed, the key warnings will appear.)\n\n      if (validType) {\n        for (var i = 2; i < arguments.length; i++) {\n          validateChildKeys(arguments[i], type);\n        }\n      }\n      if (type === exports.Fragment) {\n        validateFragmentProps(element);\n      } else {\n        validatePropTypes(element);\n      }\n      return element;\n    }\n    var didWarnAboutDeprecatedCreateFactory = false;\n    function createFactoryWithValidation(type) {\n      var validatedFactory = createElementWithValidation.bind(null, type);\n      validatedFactory.type = type;\n      {\n        if (!didWarnAboutDeprecatedCreateFactory) {\n          didWarnAboutDeprecatedCreateFactory = true;\n          warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n        } // Legacy hook: remove it\n\n        Object.defineProperty(validatedFactory, 'type', {\n          enumerable: false,\n          get: function () {\n            warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n            Object.defineProperty(this, 'type', {\n              value: type\n            });\n            return type;\n          }\n        });\n      }\n      return validatedFactory;\n    }\n    function cloneElementWithValidation(element, props, children) {\n      var newElement = cloneElement.apply(this, arguments);\n      for (var i = 2; i < arguments.length; i++) {\n        validateChildKeys(arguments[i], newElement.type);\n      }\n      validatePropTypes(newElement);\n      return newElement;\n    }\n    {\n      try {\n        var frozenObject = Object.freeze({});\n        /* eslint-disable no-new */\n\n        new Map([[frozenObject, null]]);\n        new Set([frozenObject]);\n        /* eslint-enable no-new */\n      } catch (e) {}\n    }\n    var createElement$1 = createElementWithValidation;\n    var cloneElement$1 = cloneElementWithValidation;\n    var createFactory = createFactoryWithValidation;\n    var Children = {\n      map: mapChildren,\n      forEach: forEachChildren,\n      count: countChildren,\n      toArray: toArray,\n      only: onlyChild\n    };\n    exports.Children = Children;\n    exports.Component = Component;\n    exports.PureComponent = PureComponent;\n    exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\n    exports.cloneElement = cloneElement$1;\n    exports.createContext = createContext;\n    exports.createElement = createElement$1;\n    exports.createFactory = createFactory;\n    exports.createRef = createRef;\n    exports.forwardRef = forwardRef;\n    exports.isValidElement = isValidElement;\n    exports.lazy = lazy;\n    exports.memo = memo;\n    exports.useCallback = useCallback;\n    exports.useContext = useContext;\n    exports.useDebugValue = useDebugValue;\n    exports.useEffect = useEffect;\n    exports.useImperativeHandle = useImperativeHandle;\n    exports.useLayoutEffect = useLayoutEffect;\n    exports.useMemo = useMemo;\n    exports.useReducer = useReducer;\n    exports.useRef = useRef;\n    exports.useState = useState;\n    exports.version = ReactVersion;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "_assign", "require", "ReactVersion", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "exports", "Fragment", "StrictMode", "Profiler", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "Suspense", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_BLOCK_TYPE", "REACT_SERVER_BLOCK_TYPE", "REACT_FUNDAMENTAL_TYPE", "REACT_SCOPE_TYPE", "REACT_OPAQUE_ID_TYPE", "REACT_DEBUG_TRACING_MODE_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_LEGACY_HIDDEN_TYPE", "Symbol", "for", "symbolFor", "MAYBE_ITERATOR_SYMBOL", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "maybeIterator", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "ReactCurrentBatchConfig", "transition", "ReactCurrentOwner", "ReactDebugCurrentFrame", "currentExtraStackFrame", "setExtraStackFrame", "stack", "getCurrentStack", "getStackAddendum", "impl", "IsSomeRendererActing", "ReactSharedInternals", "assign", "warn", "format", "_len", "arguments", "length", "args", "Array", "_key", "printWarning", "error", "_len2", "_key2", "level", "concat", "argsWithFormat", "map", "item", "unshift", "Function", "prototype", "apply", "call", "console", "didWarnStateUpdateForUnmountedComponent", "warnNoop", "publicInstance", "callerName", "_constructor", "constructor", "componentName", "displayName", "name", "<PERSON><PERSON><PERSON>", "ReactNoopUpdateQueue", "isMounted", "enqueueForceUpdate", "callback", "enqueueReplaceState", "completeState", "enqueueSetState", "partialState", "emptyObject", "Object", "freeze", "Component", "props", "context", "updater", "refs", "isReactComponent", "setState", "Error", "forceUpdate", "deprecatedAPIs", "replaceState", "defineDeprecationWarning", "methodName", "info", "defineProperty", "get", "undefined", "fnName", "hasOwnProperty", "ComponentDummy", "PureComponent", "pureComponentPrototype", "isPureReactComponent", "createRef", "refObject", "seal", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "getContextName", "type", "getComponentName", "tag", "$$typeof", "provider", "_context", "render", "_render", "lazyComponent", "payload", "_payload", "init", "_init", "x", "RESERVED_PROPS", "key", "ref", "__self", "__source", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "hasValidRef", "config", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defineKeyPropWarningGetter", "warnAboutAccessingKey", "configurable", "defineRefPropWarningGetter", "warnAboutAccessingRef", "warnIfStringRefCannotBeAutoConverted", "stateNode", "ReactElement", "self", "source", "owner", "element", "_owner", "_store", "enumerable", "writable", "value", "createElement", "children", "propName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "defaultProps", "cloneAndReplaceKey", "oldElement", "new<PERSON>ey", "newElement", "_self", "_source", "cloneElement", "isValidElement", "object", "SEPARATOR", "SUBSEPARATOR", "escape", "escapeRegex", "escaper<PERSON><PERSON><PERSON>", "escapedString", "replace", "match", "didWarnAboutMaps", "userProvidedKeyEscapeRegex", "escapeUserProvidedKey", "text", "get<PERSON><PERSON><PERSON><PERSON>", "index", "toString", "mapIntoArray", "array", "escapedPrefix", "nameSoFar", "invokeCallback", "_child", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "c", "push", "child", "nextName", "subtreeCount", "nextNamePrefix", "iteratorFn", "iterable<PERSON><PERSON><PERSON>n", "entries", "step", "ii", "next", "done", "childrenString", "keys", "join", "mapChildren", "func", "result", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "forEachChildren", "forEachFunc", "forEachContext", "toArray", "<PERSON><PERSON><PERSON><PERSON>", "createContext", "defaultValue", "calculateChangedBits", "_calculateChangedBits", "_currentValue", "_currentValue2", "_threadCount", "Provider", "Consumer", "hasWarnedAboutUsingNestedContextConsumers", "hasWarnedAboutUsingConsumerProvider", "hasWarnedAboutDisplayNameOnConsumer", "defineProperties", "set", "_Provider", "_current<PERSON><PERSON><PERSON>", "_currentRenderer2", "Uninitialized", "Pending", "Resolved", "Rejected", "lazyInitializer", "_status", "ctor", "_result", "thenable", "pending", "then", "moduleObject", "defaultExport", "default", "resolved", "rejected", "lazy", "lazyType", "propTypes", "newDefaultProps", "newPropTypes", "forwardRef", "elementType", "ownName", "enableScopeAPI", "isValidElementType", "memo", "compare", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatcher", "useContext", "Context", "unstable_observedBits", "realContext", "useState", "initialState", "useReducer", "reducer", "initialArg", "useRef", "initialValue", "useEffect", "create", "deps", "useLayoutEffect", "useCallback", "useMemo", "useImperativeHandle", "useDebugValue", "formatterFn", "<PERSON><PERSON><PERSON><PERSON>", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "disabledLog", "__reactDisabledLog", "disableLogs", "log", "group", "groupCollapsed", "groupEnd", "reenableLogs", "ReactCurrentDispatcher$1", "prefix", "describeBuiltInComponentFrame", "ownerFn", "trim", "reentry", "componentFrameCache", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "frame", "control", "previousPrepareStackTrace", "prepareStackTrace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fake", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "_frame", "syntheticFrame", "describeFunctionComponentFrame", "shouldConstruct", "describeUnknownElementTypeFrameInDEV", "loggedTypeFailures", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement", "checkPropTypes", "typeSpecs", "values", "location", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "setCurrentlyValidatingElement$1", "propTypesMisspellWarningShown", "getDeclarationErrorAddendum", "getSourceInfoErrorAddendum", "fileName", "lineNumber", "getSourceInfoErrorAddendumForProps", "elementProps", "ownerHasKeyUseWarning", "getCurrentComponentErrorInfo", "parentType", "parentName", "validateExplicitKey", "validated", "currentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "validatePropTypes", "PropTypes", "_name", "getDefaultProps", "isReactClassApproved", "validateFragmentProps", "fragment", "createElementWithValidation", "validType", "sourceInfo", "typeString", "didWarnAboutDeprecatedCreateFactory", "createFactoryWithValidation", "validatedFactory", "cloneElementWithValidation", "frozenObject", "Set", "e", "createElement$1", "cloneElement$1", "createFactory", "Children", "for<PERSON>ach", "only", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "version"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react/cjs/react.development.js"], "sourcesContent": ["/** @license React v17.0.2\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar _assign = require('object-assign');\n\n// TODO: this is special because it gets imported during build.\nvar ReactVersion = '17.0.2';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nexports.Fragment = 0xeacb;\nexports.StrictMode = 0xeacc;\nexports.Profiler = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nexports.Suspense = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  exports.Fragment = symbolFor('react.fragment');\n  exports.StrictMode = symbolFor('react.strict_mode');\n  exports.Profiler = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  exports.Suspense = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\nvar MAYBE_ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: 0\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n/**\n * Used by act() to track whether you're inside an act() scope.\n */\nvar IsSomeRendererActing = {\n  current: false\n};\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner,\n  IsSomeRendererActing: IsSomeRendererActing,\n  // Used by renderers to avoid bundling object-assign twice in UMD bundles:\n  assign: _assign\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    printWarning('warn', format, args);\n  }\n}\nfunction error(format) {\n  {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    printWarning('error', format, args);\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    }\n\n    var argsWithFormat = args.map(function (item) {\n      return '' + item;\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (!(typeof partialState === 'object' || typeof partialState === 'function' || partialState == null)) {\n    {\n      throw Error( \"setState(...): takes an object of state variables to update or a function which returns an object of state variables.\" );\n    }\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\n_assign(pureComponentPrototype, Component.prototype);\n\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var functionName = innerType.displayName || innerType.name || '';\n  return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n}\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nfunction getComponentName(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentName(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case exports.Fragment:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case exports.Profiler:\n      return 'Profiler';\n\n    case exports.StrictMode:\n      return 'StrictMode';\n\n    case exports.Suspense:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        return getComponentName(type.type);\n\n      case REACT_BLOCK_TYPE:\n        return getComponentName(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentName(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentName(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (!!(element === null || element === undefined)) {\n    {\n      throw Error( \"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\" );\n    }\n  }\n\n  var propName; // Original props are copied\n\n  var props = _assign({}, element.props); // Reserved names are extracted\n\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (Array.isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      var childrenString = '' + children;\n\n      {\n        {\n          throw Error( \"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). If you meant to render a collection of children, use an array instead.\" );\n        }\n      }\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    {\n      throw Error( \"React.Children.only expected to receive a single React element child.\" );\n    }\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue, calculateChangedBits) {\n  if (calculateChangedBits === undefined) {\n    calculateChangedBits = null;\n  } else {\n    {\n      if (calculateChangedBits !== null && typeof calculateChangedBits !== 'function') {\n        error('createContext: Expected the optional second argument to be a ' + 'function. Instead received: %s', calculateChangedBits);\n      }\n    }\n  }\n\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    _calculateChangedBits: calculateChangedBits,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context,\n      _calculateChangedBits: context._calculateChangedBits\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n\n    var pending = payload;\n    pending._status = Pending;\n    pending._result = thenable;\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending) {\n        var defaultExport = moduleObject.default;\n\n        {\n          if (defaultExport === undefined) {\n            error('lazy: Expected the result of a dynamic import() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n          }\n        } // Transition to the next state.\n\n\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = defaultExport;\n      }\n    }, function (error) {\n      if (payload._status === Pending) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n  }\n\n  if (payload._status === Resolved) {\n    return payload._result;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: -1,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name;\n\n        if (render.displayName == null) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === exports.Fragment || type === exports.Profiler || type === REACT_DEBUG_TRACING_MODE_TYPE || type === exports.StrictMode || type === exports.Suspense || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name;\n\n        if (type.displayName == null) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  if (!(dispatcher !== null)) {\n    {\n      throw Error( \"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)\\n2. You might be breaking the Rules of Hooks\\n3. You might have more than one copy of React in the same app\\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.\" );\n    }\n  }\n\n  return dispatcher;\n}\n\nfunction useContext(Context, unstable_observedBits) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    if (unstable_observedBits !== undefined) {\n      error('useContext() second argument is reserved for future ' + 'use in React. Passing it is not supported. ' + 'You passed: %s.%s', unstable_observedBits, typeof unstable_observedBits === 'number' && Array.isArray(arguments[2]) ? '\\n\\nDid you call array.map(useContext)? ' + 'Calling Hooks inside a loop is not supported. ' + 'Learn more at https://reactjs.org/link/rules-of-hooks' : '');\n    } // TODO: add a more generic warning for invalid values.\n\n\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context, unstable_observedBits);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: _assign({}, props, {\n          value: prevLog\n        }),\n        info: _assign({}, props, {\n          value: prevInfo\n        }),\n        warn: _assign({}, props, {\n          value: prevWarn\n        }),\n        error: _assign({}, props, {\n          value: prevError\n        }),\n        group: _assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: _assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: _assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at ');\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case exports.Suspense:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_BLOCK_TYPE:\n        return describeFunctionComponentFrame(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentName(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentName(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (Array.isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentName(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentName(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (Array.isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentName(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === exports.Fragment) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\n{\n\n  try {\n    var frozenObject = Object.freeze({});\n    /* eslint-disable no-new */\n\n    new Map([[frozenObject, null]]);\n    new Set([frozenObject]);\n    /* eslint-enable no-new */\n  } catch (e) {\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.PureComponent = PureComponent;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useEffect = useEffect;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.version = ReactVersion;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ,IAAIC,OAAO,GAAGC,OAAO,CAAC,eAAe,CAAC;;IAEtC;IACA,IAAIC,YAAY,GAAG,QAAQ;;IAE3B;IACA;IACA;IACA;IACA;IACA,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,iBAAiB,GAAG,MAAM;IAC9BC,OAAO,CAACC,QAAQ,GAAG,MAAM;IACzBD,OAAO,CAACE,UAAU,GAAG,MAAM;IAC3BF,OAAO,CAACG,QAAQ,GAAG,MAAM;IACzB,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,sBAAsB,GAAG,MAAM;IACnCN,OAAO,CAACO,QAAQ,GAAG,MAAM;IACzB,IAAIC,wBAAwB,GAAG,MAAM;IACrC,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,uBAAuB,GAAG,MAAM;IACpC,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,6BAA6B,GAAG,MAAM;IAC1C,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,wBAAwB,GAAG,MAAM;IAErC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;MAC9C,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAG;MAC1BtB,kBAAkB,GAAGuB,SAAS,CAAC,eAAe,CAAC;MAC/CtB,iBAAiB,GAAGsB,SAAS,CAAC,cAAc,CAAC;MAC7CrB,OAAO,CAACC,QAAQ,GAAGoB,SAAS,CAAC,gBAAgB,CAAC;MAC9CrB,OAAO,CAACE,UAAU,GAAGmB,SAAS,CAAC,mBAAmB,CAAC;MACnDrB,OAAO,CAACG,QAAQ,GAAGkB,SAAS,CAAC,gBAAgB,CAAC;MAC9CjB,mBAAmB,GAAGiB,SAAS,CAAC,gBAAgB,CAAC;MACjDhB,kBAAkB,GAAGgB,SAAS,CAAC,eAAe,CAAC;MAC/Cf,sBAAsB,GAAGe,SAAS,CAAC,mBAAmB,CAAC;MACvDrB,OAAO,CAACO,QAAQ,GAAGc,SAAS,CAAC,gBAAgB,CAAC;MAC9Cb,wBAAwB,GAAGa,SAAS,CAAC,qBAAqB,CAAC;MAC3DZ,eAAe,GAAGY,SAAS,CAAC,YAAY,CAAC;MACzCX,eAAe,GAAGW,SAAS,CAAC,YAAY,CAAC;MACzCV,gBAAgB,GAAGU,SAAS,CAAC,aAAa,CAAC;MAC3CT,uBAAuB,GAAGS,SAAS,CAAC,oBAAoB,CAAC;MACzDR,sBAAsB,GAAGQ,SAAS,CAAC,mBAAmB,CAAC;MACvDP,gBAAgB,GAAGO,SAAS,CAAC,aAAa,CAAC;MAC3CN,oBAAoB,GAAGM,SAAS,CAAC,iBAAiB,CAAC;MACnDL,6BAA6B,GAAGK,SAAS,CAAC,wBAAwB,CAAC;MACnEJ,oBAAoB,GAAGI,SAAS,CAAC,iBAAiB,CAAC;MACnDH,wBAAwB,GAAGG,SAAS,CAAC,qBAAqB,CAAC;IAC7D;IAEA,IAAIC,qBAAqB,GAAG,OAAOH,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACI,QAAQ;IAC3E,IAAIC,oBAAoB,GAAG,YAAY;IACvC,SAASC,aAAaA,CAACC,aAAa,EAAE;MACpC,IAAIA,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;QAC/D,OAAO,IAAI;MACb;MAEA,IAAIC,aAAa,GAAGL,qBAAqB,IAAII,aAAa,CAACJ,qBAAqB,CAAC,IAAII,aAAa,CAACF,oBAAoB,CAAC;MAExH,IAAI,OAAOG,aAAa,KAAK,UAAU,EAAE;QACvC,OAAOA,aAAa;MACtB;MAEA,OAAO,IAAI;IACb;;IAEA;AACA;AACA;IACA,IAAIC,sBAAsB,GAAG;MAC3B;AACF;AACA;AACA;MACEC,OAAO,EAAE;IACX,CAAC;;IAED;AACA;AACA;AACA;IACA,IAAIC,uBAAuB,GAAG;MAC5BC,UAAU,EAAE;IACd,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACA,IAAIC,iBAAiB,GAAG;MACtB;AACF;AACA;AACA;MACEH,OAAO,EAAE;IACX,CAAC;IAED,IAAII,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAIC,sBAAsB,GAAG,IAAI;IACjC,SAASC,kBAAkBA,CAACC,KAAK,EAAE;MACjC;QACEF,sBAAsB,GAAGE,KAAK;MAChC;IACF;IAEA;MACEH,sBAAsB,CAACE,kBAAkB,GAAG,UAAUC,KAAK,EAAE;QAC3D;UACEF,sBAAsB,GAAGE,KAAK;QAChC;MACF,CAAC,CAAC,CAAC;;MAGHH,sBAAsB,CAACI,eAAe,GAAG,IAAI;MAE7CJ,sBAAsB,CAACK,gBAAgB,GAAG,YAAY;QACpD,IAAIF,KAAK,GAAG,EAAE,CAAC,CAAC;;QAEhB,IAAIF,sBAAsB,EAAE;UAC1BE,KAAK,IAAIF,sBAAsB;QACjC,CAAC,CAAC;;QAGF,IAAIK,IAAI,GAAGN,sBAAsB,CAACI,eAAe;QAEjD,IAAIE,IAAI,EAAE;UACRH,KAAK,IAAIG,IAAI,CAAC,CAAC,IAAI,EAAE;QACvB;QAEA,OAAOH,KAAK;MACd,CAAC;IACH;;IAEA;AACA;AACA;IACA,IAAII,oBAAoB,GAAG;MACzBX,OAAO,EAAE;IACX,CAAC;IAED,IAAIY,oBAAoB,GAAG;MACzBb,sBAAsB,EAAEA,sBAAsB;MAC9CE,uBAAuB,EAAEA,uBAAuB;MAChDE,iBAAiB,EAAEA,iBAAiB;MACpCQ,oBAAoB,EAAEA,oBAAoB;MAC1C;MACAE,MAAM,EAAE/C;IACV,CAAC;IAED;MACE8C,oBAAoB,CAACR,sBAAsB,GAAGA,sBAAsB;IACtE;;IAEA;IACA;IACA;IACA;;IAEA,SAASU,IAAIA,CAACC,MAAM,EAAE;MACpB;QACE,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAClC;QAEAC,YAAY,CAAC,MAAM,EAAEP,MAAM,EAAEI,IAAI,CAAC;MACpC;IACF;IACA,SAASI,KAAKA,CAACR,MAAM,EAAE;MACrB;QACE,KAAK,IAAIS,KAAK,GAAGP,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACI,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHN,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC,GAAGR,SAAS,CAACQ,KAAK,CAAC;QACpC;QAEAH,YAAY,CAAC,OAAO,EAAEP,MAAM,EAAEI,IAAI,CAAC;MACrC;IACF;IAEA,SAASG,YAAYA,CAACI,KAAK,EAAEX,MAAM,EAAEI,IAAI,EAAE;MACzC;MACA;MACA;QACE,IAAIf,sBAAsB,GAAGQ,oBAAoB,CAACR,sBAAsB;QACxE,IAAIG,KAAK,GAAGH,sBAAsB,CAACK,gBAAgB,CAAC,CAAC;QAErD,IAAIF,KAAK,KAAK,EAAE,EAAE;UAChBQ,MAAM,IAAI,IAAI;UACdI,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAAC,CAACpB,KAAK,CAAC,CAAC;QAC7B;QAEA,IAAIqB,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;UAC5C,OAAO,EAAE,GAAGA,IAAI;QAClB,CAAC,CAAC,CAAC,CAAC;;QAEJF,cAAc,CAACG,OAAO,CAAC,WAAW,GAAGhB,MAAM,CAAC,CAAC,CAAC;QAC9C;QACA;;QAEAiB,QAAQ,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,OAAO,CAACV,KAAK,CAAC,EAAEU,OAAO,EAAER,cAAc,CAAC;MACxE;IACF;IAEA,IAAIS,uCAAuC,GAAG,CAAC,CAAC;IAEhD,SAASC,QAAQA,CAACC,cAAc,EAAEC,UAAU,EAAE;MAC5C;QACE,IAAIC,YAAY,GAAGF,cAAc,CAACG,WAAW;QAC7C,IAAIC,aAAa,GAAGF,YAAY,KAAKA,YAAY,CAACG,WAAW,IAAIH,YAAY,CAACI,IAAI,CAAC,IAAI,YAAY;QACnG,IAAIC,UAAU,GAAGH,aAAa,GAAG,GAAG,GAAGH,UAAU;QAEjD,IAAIH,uCAAuC,CAACS,UAAU,CAAC,EAAE;UACvD;QACF;QAEAvB,KAAK,CAAC,wDAAwD,GAAG,oEAAoE,GAAG,qEAAqE,GAAG,4DAA4D,EAAEiB,UAAU,EAAEG,aAAa,CAAC;QAExSN,uCAAuC,CAACS,UAAU,CAAC,GAAG,IAAI;MAC5D;IACF;IACA;AACA;AACA;;IAGA,IAAIC,oBAAoB,GAAG;MACzB;AACF;AACA;AACA;AACA;AACA;AACA;MACEC,SAAS,EAAE,SAAAA,CAAUT,cAAc,EAAE;QACnC,OAAO,KAAK;MACd,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEU,kBAAkB,EAAE,SAAAA,CAAUV,cAAc,EAAEW,QAAQ,EAAEV,UAAU,EAAE;QAClEF,QAAQ,CAACC,cAAc,EAAE,aAAa,CAAC;MACzC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEY,mBAAmB,EAAE,SAAAA,CAAUZ,cAAc,EAAEa,aAAa,EAAEF,QAAQ,EAAEV,UAAU,EAAE;QAClFF,QAAQ,CAACC,cAAc,EAAE,cAAc,CAAC;MAC1C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEc,eAAe,EAAE,SAAAA,CAAUd,cAAc,EAAEe,YAAY,EAAEJ,QAAQ,EAAEV,UAAU,EAAE;QAC7EF,QAAQ,CAACC,cAAc,EAAE,UAAU,CAAC;MACtC;IACF,CAAC;IAED,IAAIgB,WAAW,GAAG,CAAC,CAAC;IAEpB;MACEC,MAAM,CAACC,MAAM,CAACF,WAAW,CAAC;IAC5B;IACA;AACA;AACA;;IAGA,SAASG,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MAC1C,IAAI,CAACF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAC,CAAC;;MAExB,IAAI,CAACE,IAAI,GAAGP,WAAW,CAAC,CAAC;MACzB;;MAEA,IAAI,CAACM,OAAO,GAAGA,OAAO,IAAId,oBAAoB;IAChD;IAEAW,SAAS,CAACzB,SAAS,CAAC8B,gBAAgB,GAAG,CAAC,CAAC;IACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEAL,SAAS,CAACzB,SAAS,CAAC+B,QAAQ,GAAG,UAAUV,YAAY,EAAEJ,QAAQ,EAAE;MAC/D,IAAI,EAAE,OAAOI,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,UAAU,IAAIA,YAAY,IAAI,IAAI,CAAC,EAAE;QACrG;UACE,MAAMW,KAAK,CAAE,uHAAwH,CAAC;QACxI;MACF;MAEA,IAAI,CAACJ,OAAO,CAACR,eAAe,CAAC,IAAI,EAAEC,YAAY,EAAEJ,QAAQ,EAAE,UAAU,CAAC;IACxE,CAAC;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGAQ,SAAS,CAACzB,SAAS,CAACiC,WAAW,GAAG,UAAUhB,QAAQ,EAAE;MACpD,IAAI,CAACW,OAAO,CAACZ,kBAAkB,CAAC,IAAI,EAAEC,QAAQ,EAAE,aAAa,CAAC;IAChE,CAAC;IACD;AACA;AACA;AACA;AACA;;IAGA;MACE,IAAIiB,cAAc,GAAG;QACnBnB,SAAS,EAAE,CAAC,WAAW,EAAE,uEAAuE,GAAG,+CAA+C,CAAC;QACnJoB,YAAY,EAAE,CAAC,cAAc,EAAE,kDAAkD,GAAG,iDAAiD;MACvI,CAAC;MAED,IAAIC,wBAAwB,GAAG,SAAAA,CAAUC,UAAU,EAAEC,IAAI,EAAE;QACzDf,MAAM,CAACgB,cAAc,CAACd,SAAS,CAACzB,SAAS,EAAEqC,UAAU,EAAE;UACrDG,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf3D,IAAI,CAAC,6DAA6D,EAAEyD,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;YAErF,OAAOG,SAAS;UAClB;QACF,CAAC,CAAC;MACJ,CAAC;MAED,KAAK,IAAIC,MAAM,IAAIR,cAAc,EAAE;QACjC,IAAIA,cAAc,CAACS,cAAc,CAACD,MAAM,CAAC,EAAE;UACzCN,wBAAwB,CAACM,MAAM,EAAER,cAAc,CAACQ,MAAM,CAAC,CAAC;QAC1D;MACF;IACF;IAEA,SAASE,cAAcA,CAAA,EAAG,CAAC;IAE3BA,cAAc,CAAC5C,SAAS,GAAGyB,SAAS,CAACzB,SAAS;IAC9C;AACA;AACA;;IAEA,SAAS6C,aAAaA,CAACnB,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MAC9C,IAAI,CAACF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAC,CAAC;;MAExB,IAAI,CAACE,IAAI,GAAGP,WAAW;MACvB,IAAI,CAACM,OAAO,GAAGA,OAAO,IAAId,oBAAoB;IAChD;IAEA,IAAIgC,sBAAsB,GAAGD,aAAa,CAAC7C,SAAS,GAAG,IAAI4C,cAAc,CAAC,CAAC;IAC3EE,sBAAsB,CAACrC,WAAW,GAAGoC,aAAa,CAAC,CAAC;;IAEpDhH,OAAO,CAACiH,sBAAsB,EAAErB,SAAS,CAACzB,SAAS,CAAC;IAEpD8C,sBAAsB,CAACC,oBAAoB,GAAG,IAAI;;IAElD;IACA,SAASC,SAASA,CAAA,EAAG;MACnB,IAAIC,SAAS,GAAG;QACdlF,OAAO,EAAE;MACX,CAAC;MAED;QACEwD,MAAM,CAAC2B,IAAI,CAACD,SAAS,CAAC;MACxB;MAEA,OAAOA,SAAS;IAClB;IAEA,SAASE,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAE;MACzD,IAAIC,YAAY,GAAGF,SAAS,CAAC1C,WAAW,IAAI0C,SAAS,CAACzC,IAAI,IAAI,EAAE;MAChE,OAAOwC,SAAS,CAACzC,WAAW,KAAK4C,YAAY,KAAK,EAAE,GAAGD,WAAW,GAAG,GAAG,GAAGC,YAAY,GAAG,GAAG,GAAGD,WAAW,CAAC;IAC9G;IAEA,SAASE,cAAcA,CAACC,IAAI,EAAE;MAC5B,OAAOA,IAAI,CAAC9C,WAAW,IAAI,SAAS;IACtC;IAEA,SAAS+C,gBAAgBA,CAACD,IAAI,EAAE;MAC9B,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB;QACA,OAAO,IAAI;MACb;MAEA;QACE,IAAI,OAAOA,IAAI,CAACE,GAAG,KAAK,QAAQ,EAAE;UAChCrE,KAAK,CAAC,uDAAuD,GAAG,sDAAsD,CAAC;QACzH;MACF;MAEA,IAAI,OAAOmE,IAAI,KAAK,UAAU,EAAE;QAC9B,OAAOA,IAAI,CAAC9C,WAAW,IAAI8C,IAAI,CAAC7C,IAAI,IAAI,IAAI;MAC9C;MAEA,IAAI,OAAO6C,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOA,IAAI;MACb;MAEA,QAAQA,IAAI;QACV,KAAKvH,OAAO,CAACC,QAAQ;UACnB,OAAO,UAAU;QAEnB,KAAKF,iBAAiB;UACpB,OAAO,QAAQ;QAEjB,KAAKC,OAAO,CAACG,QAAQ;UACnB,OAAO,UAAU;QAEnB,KAAKH,OAAO,CAACE,UAAU;UACrB,OAAO,YAAY;QAErB,KAAKF,OAAO,CAACO,QAAQ;UACnB,OAAO,UAAU;QAEnB,KAAKC,wBAAwB;UAC3B,OAAO,cAAc;MACzB;MAEA,IAAI,OAAO+G,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACG,QAAQ;UACnB,KAAKrH,kBAAkB;YACrB,IAAIoF,OAAO,GAAG8B,IAAI;YAClB,OAAOD,cAAc,CAAC7B,OAAO,CAAC,GAAG,WAAW;UAE9C,KAAKrF,mBAAmB;YACtB,IAAIuH,QAAQ,GAAGJ,IAAI;YACnB,OAAOD,cAAc,CAACK,QAAQ,CAACC,QAAQ,CAAC,GAAG,WAAW;UAExD,KAAKtH,sBAAsB;YACzB,OAAO2G,cAAc,CAACM,IAAI,EAAEA,IAAI,CAACM,MAAM,EAAE,YAAY,CAAC;UAExD,KAAKpH,eAAe;YAClB,OAAO+G,gBAAgB,CAACD,IAAI,CAACA,IAAI,CAAC;UAEpC,KAAK5G,gBAAgB;YACnB,OAAO6G,gBAAgB,CAACD,IAAI,CAACO,OAAO,CAAC;UAEvC,KAAKpH,eAAe;YAClB;cACE,IAAIqH,aAAa,GAAGR,IAAI;cACxB,IAAIS,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF,OAAOX,gBAAgB,CAACU,IAAI,CAACF,OAAO,CAAC,CAAC;cACxC,CAAC,CAAC,OAAOI,CAAC,EAAE;gBACV,OAAO,IAAI;cACb;YACF;QACJ;MACF;MAEA,OAAO,IAAI;IACb;IAEA,IAAI3B,cAAc,GAAGpB,MAAM,CAACvB,SAAS,CAAC2C,cAAc;IACpD,IAAI4B,cAAc,GAAG;MACnBC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIC,0BAA0B,EAAEC,0BAA0B,EAAEC,sBAAsB;IAElF;MACEA,sBAAsB,GAAG,CAAC,CAAC;IAC7B;IAEA,SAASC,WAAWA,CAACC,MAAM,EAAE;MAC3B;QACE,IAAIrC,cAAc,CAACzC,IAAI,CAAC8E,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAG1D,MAAM,CAAC2D,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACxC,GAAG;UAE/D,IAAIyC,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACP,GAAG,KAAKhC,SAAS;IACjC;IAEA,SAAS2C,WAAWA,CAACJ,MAAM,EAAE;MAC3B;QACE,IAAIrC,cAAc,CAACzC,IAAI,CAAC8E,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAG1D,MAAM,CAAC2D,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACxC,GAAG;UAE/D,IAAIyC,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACR,GAAG,KAAK/B,SAAS;IACjC;IAEA,SAAS4C,0BAA0BA,CAAC3D,KAAK,EAAEf,WAAW,EAAE;MACtD,IAAI2E,qBAAqB,GAAG,SAAAA,CAAA,EAAY;QACtC;UACE,IAAI,CAACV,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjCtF,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEqB,WAAW,CAAC;UAChR;QACF;MACF,CAAC;MAED2E,qBAAqB,CAACH,cAAc,GAAG,IAAI;MAC3C5D,MAAM,CAACgB,cAAc,CAACb,KAAK,EAAE,KAAK,EAAE;QAClCc,GAAG,EAAE8C,qBAAqB;QAC1BC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IAEA,SAASC,0BAA0BA,CAAC9D,KAAK,EAAEf,WAAW,EAAE;MACtD,IAAI8E,qBAAqB,GAAG,SAAAA,CAAA,EAAY;QACtC;UACE,IAAI,CAACZ,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjCvF,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEqB,WAAW,CAAC;UAChR;QACF;MACF,CAAC;MAED8E,qBAAqB,CAACN,cAAc,GAAG,IAAI;MAC3C5D,MAAM,CAACgB,cAAc,CAACb,KAAK,EAAE,KAAK,EAAE;QAClCc,GAAG,EAAEiD,qBAAqB;QAC1BF,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IAEA,SAASG,oCAAoCA,CAACV,MAAM,EAAE;MACpD;QACE,IAAI,OAAOA,MAAM,CAACP,GAAG,KAAK,QAAQ,IAAIvG,iBAAiB,CAACH,OAAO,IAAIiH,MAAM,CAACN,MAAM,IAAIxG,iBAAiB,CAACH,OAAO,CAAC4H,SAAS,KAAKX,MAAM,CAACN,MAAM,EAAE;UACzI,IAAIhE,aAAa,GAAGgD,gBAAgB,CAACxF,iBAAiB,CAACH,OAAO,CAAC0F,IAAI,CAAC;UAEpE,IAAI,CAACqB,sBAAsB,CAACpE,aAAa,CAAC,EAAE;YAC1CpB,KAAK,CAAC,+CAA+C,GAAG,qEAAqE,GAAG,oEAAoE,GAAG,iFAAiF,GAAG,2CAA2C,GAAG,iDAAiD,EAAEoB,aAAa,EAAEsE,MAAM,CAACP,GAAG,CAAC;YAEtZK,sBAAsB,CAACpE,aAAa,CAAC,GAAG,IAAI;UAC9C;QACF;MACF;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,IAAIkF,YAAY,GAAG,SAAAA,CAAUnC,IAAI,EAAEe,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAErE,KAAK,EAAE;MACvE,IAAIsE,OAAO,GAAG;QACZ;QACApC,QAAQ,EAAE5H,kBAAkB;QAC5B;QACAyH,IAAI,EAAEA,IAAI;QACVe,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA,GAAG;QACR/C,KAAK,EAAEA,KAAK;QACZ;QACAuE,MAAM,EAAEF;MACV,CAAC;MAED;QACE;QACA;QACA;QACA;QACAC,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB;QACA;QACA;;QAEA3E,MAAM,CAACgB,cAAc,CAACyD,OAAO,CAACE,MAAM,EAAE,WAAW,EAAE;UACjDX,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,CAAC;;QAEJ9E,MAAM,CAACgB,cAAc,CAACyD,OAAO,EAAE,OAAO,EAAE;UACtCT,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAER;QACT,CAAC,CAAC,CAAC,CAAC;QACJ;;QAEAtE,MAAM,CAACgB,cAAc,CAACyD,OAAO,EAAE,SAAS,EAAE;UACxCT,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAEP;QACT,CAAC,CAAC;QAEF,IAAIvE,MAAM,CAACC,MAAM,EAAE;UACjBD,MAAM,CAACC,MAAM,CAACwE,OAAO,CAACtE,KAAK,CAAC;UAC5BH,MAAM,CAACC,MAAM,CAACwE,OAAO,CAAC;QACxB;MACF;MAEA,OAAOA,OAAO;IAChB,CAAC;IACD;AACA;AACA;AACA;;IAEA,SAASM,aAAaA,CAAC7C,IAAI,EAAEuB,MAAM,EAAEuB,QAAQ,EAAE;MAC7C,IAAIC,QAAQ,CAAC,CAAC;;MAEd,IAAI9E,KAAK,GAAG,CAAC,CAAC;MACd,IAAI8C,GAAG,GAAG,IAAI;MACd,IAAIC,GAAG,GAAG,IAAI;MACd,IAAIoB,IAAI,GAAG,IAAI;MACf,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAId,MAAM,IAAI,IAAI,EAAE;QAClB,IAAID,WAAW,CAACC,MAAM,CAAC,EAAE;UACvBP,GAAG,GAAGO,MAAM,CAACP,GAAG;UAEhB;YACEiB,oCAAoC,CAACV,MAAM,CAAC;UAC9C;QACF;QAEA,IAAII,WAAW,CAACJ,MAAM,CAAC,EAAE;UACvBR,GAAG,GAAG,EAAE,GAAGQ,MAAM,CAACR,GAAG;QACvB;QAEAqB,IAAI,GAAGb,MAAM,CAACN,MAAM,KAAKjC,SAAS,GAAG,IAAI,GAAGuC,MAAM,CAACN,MAAM;QACzDoB,MAAM,GAAGd,MAAM,CAACL,QAAQ,KAAKlC,SAAS,GAAG,IAAI,GAAGuC,MAAM,CAACL,QAAQ,CAAC,CAAC;;QAEjE,KAAK6B,QAAQ,IAAIxB,MAAM,EAAE;UACvB,IAAIrC,cAAc,CAACzC,IAAI,CAAC8E,MAAM,EAAEwB,QAAQ,CAAC,IAAI,CAACjC,cAAc,CAAC5B,cAAc,CAAC6D,QAAQ,CAAC,EAAE;YACrF9E,KAAK,CAAC8E,QAAQ,CAAC,GAAGxB,MAAM,CAACwB,QAAQ,CAAC;UACpC;QACF;MACF,CAAC,CAAC;MACF;;MAGA,IAAIC,cAAc,GAAGzH,SAAS,CAACC,MAAM,GAAG,CAAC;MAEzC,IAAIwH,cAAc,KAAK,CAAC,EAAE;QACxB/E,KAAK,CAAC6E,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,MAAM,IAAIE,cAAc,GAAG,CAAC,EAAE;QAC7B,IAAIC,UAAU,GAAGvH,KAAK,CAACsH,cAAc,CAAC;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;UACvCD,UAAU,CAACC,CAAC,CAAC,GAAG3H,SAAS,CAAC2H,CAAC,GAAG,CAAC,CAAC;QAClC;QAEA;UACE,IAAIpF,MAAM,CAACC,MAAM,EAAE;YACjBD,MAAM,CAACC,MAAM,CAACkF,UAAU,CAAC;UAC3B;QACF;QAEAhF,KAAK,CAAC6E,QAAQ,GAAGG,UAAU;MAC7B,CAAC,CAAC;;MAGF,IAAIjD,IAAI,IAAIA,IAAI,CAACmD,YAAY,EAAE;QAC7B,IAAIA,YAAY,GAAGnD,IAAI,CAACmD,YAAY;QAEpC,KAAKJ,QAAQ,IAAII,YAAY,EAAE;UAC7B,IAAIlF,KAAK,CAAC8E,QAAQ,CAAC,KAAK/D,SAAS,EAAE;YACjCf,KAAK,CAAC8E,QAAQ,CAAC,GAAGI,YAAY,CAACJ,QAAQ,CAAC;UAC1C;QACF;MACF;MAEA;QACE,IAAIhC,GAAG,IAAIC,GAAG,EAAE;UACd,IAAI9D,WAAW,GAAG,OAAO8C,IAAI,KAAK,UAAU,GAAGA,IAAI,CAAC9C,WAAW,IAAI8C,IAAI,CAAC7C,IAAI,IAAI,SAAS,GAAG6C,IAAI;UAEhG,IAAIe,GAAG,EAAE;YACPa,0BAA0B,CAAC3D,KAAK,EAAEf,WAAW,CAAC;UAChD;UAEA,IAAI8D,GAAG,EAAE;YACPe,0BAA0B,CAAC9D,KAAK,EAAEf,WAAW,CAAC;UAChD;QACF;MACF;MAEA,OAAOiF,YAAY,CAACnC,IAAI,EAAEe,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAE5H,iBAAiB,CAACH,OAAO,EAAE2D,KAAK,CAAC;IACrF;IACA,SAASmF,kBAAkBA,CAACC,UAAU,EAAEC,MAAM,EAAE;MAC9C,IAAIC,UAAU,GAAGpB,YAAY,CAACkB,UAAU,CAACrD,IAAI,EAAEsD,MAAM,EAAED,UAAU,CAACrC,GAAG,EAAEqC,UAAU,CAACG,KAAK,EAAEH,UAAU,CAACI,OAAO,EAAEJ,UAAU,CAACb,MAAM,EAAEa,UAAU,CAACpF,KAAK,CAAC;MACjJ,OAAOsF,UAAU;IACnB;IACA;AACA;AACA;AACA;;IAEA,SAASG,YAAYA,CAACnB,OAAO,EAAEhB,MAAM,EAAEuB,QAAQ,EAAE;MAC/C,IAAI,CAAC,EAAEP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKvD,SAAS,CAAC,EAAE;QACjD;UACE,MAAMT,KAAK,CAAE,gFAAgF,GAAGgE,OAAO,GAAG,GAAI,CAAC;QACjH;MACF;MAEA,IAAIQ,QAAQ,CAAC,CAAC;;MAEd,IAAI9E,KAAK,GAAG7F,OAAO,CAAC,CAAC,CAAC,EAAEmK,OAAO,CAACtE,KAAK,CAAC,CAAC,CAAC;;MAGxC,IAAI8C,GAAG,GAAGwB,OAAO,CAACxB,GAAG;MACrB,IAAIC,GAAG,GAAGuB,OAAO,CAACvB,GAAG,CAAC,CAAC;;MAEvB,IAAIoB,IAAI,GAAGG,OAAO,CAACiB,KAAK,CAAC,CAAC;MAC1B;MACA;;MAEA,IAAInB,MAAM,GAAGE,OAAO,CAACkB,OAAO,CAAC,CAAC;;MAE9B,IAAInB,KAAK,GAAGC,OAAO,CAACC,MAAM;MAE1B,IAAIjB,MAAM,IAAI,IAAI,EAAE;QAClB,IAAID,WAAW,CAACC,MAAM,CAAC,EAAE;UACvB;UACAP,GAAG,GAAGO,MAAM,CAACP,GAAG;UAChBsB,KAAK,GAAG7H,iBAAiB,CAACH,OAAO;QACnC;QAEA,IAAIqH,WAAW,CAACJ,MAAM,CAAC,EAAE;UACvBR,GAAG,GAAG,EAAE,GAAGQ,MAAM,CAACR,GAAG;QACvB,CAAC,CAAC;;QAGF,IAAIoC,YAAY;QAEhB,IAAIZ,OAAO,CAACvC,IAAI,IAAIuC,OAAO,CAACvC,IAAI,CAACmD,YAAY,EAAE;UAC7CA,YAAY,GAAGZ,OAAO,CAACvC,IAAI,CAACmD,YAAY;QAC1C;QAEA,KAAKJ,QAAQ,IAAIxB,MAAM,EAAE;UACvB,IAAIrC,cAAc,CAACzC,IAAI,CAAC8E,MAAM,EAAEwB,QAAQ,CAAC,IAAI,CAACjC,cAAc,CAAC5B,cAAc,CAAC6D,QAAQ,CAAC,EAAE;YACrF,IAAIxB,MAAM,CAACwB,QAAQ,CAAC,KAAK/D,SAAS,IAAImE,YAAY,KAAKnE,SAAS,EAAE;cAChE;cACAf,KAAK,CAAC8E,QAAQ,CAAC,GAAGI,YAAY,CAACJ,QAAQ,CAAC;YAC1C,CAAC,MAAM;cACL9E,KAAK,CAAC8E,QAAQ,CAAC,GAAGxB,MAAM,CAACwB,QAAQ,CAAC;YACpC;UACF;QACF;MACF,CAAC,CAAC;MACF;;MAGA,IAAIC,cAAc,GAAGzH,SAAS,CAACC,MAAM,GAAG,CAAC;MAEzC,IAAIwH,cAAc,KAAK,CAAC,EAAE;QACxB/E,KAAK,CAAC6E,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,MAAM,IAAIE,cAAc,GAAG,CAAC,EAAE;QAC7B,IAAIC,UAAU,GAAGvH,KAAK,CAACsH,cAAc,CAAC;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;UACvCD,UAAU,CAACC,CAAC,CAAC,GAAG3H,SAAS,CAAC2H,CAAC,GAAG,CAAC,CAAC;QAClC;QAEAjF,KAAK,CAAC6E,QAAQ,GAAGG,UAAU;MAC7B;MAEA,OAAOd,YAAY,CAACI,OAAO,CAACvC,IAAI,EAAEe,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAErE,KAAK,CAAC;IACzE;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,SAAS0F,cAAcA,CAACC,MAAM,EAAE;MAC9B,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACzD,QAAQ,KAAK5H,kBAAkB;IAChG;IAEA,IAAIsL,SAAS,GAAG,GAAG;IACnB,IAAIC,YAAY,GAAG,GAAG;IACtB;AACA;AACA;AACA;AACA;AACA;;IAEA,SAASC,MAAMA,CAAChD,GAAG,EAAE;MACnB,IAAIiD,WAAW,GAAG,OAAO;MACzB,IAAIC,aAAa,GAAG;QAClB,GAAG,EAAE,IAAI;QACT,GAAG,EAAE;MACP,CAAC;MACD,IAAIC,aAAa,GAAGnD,GAAG,CAACoD,OAAO,CAACH,WAAW,EAAE,UAAUI,KAAK,EAAE;QAC5D,OAAOH,aAAa,CAACG,KAAK,CAAC;MAC7B,CAAC,CAAC;MACF,OAAO,GAAG,GAAGF,aAAa;IAC5B;IACA;AACA;AACA;AACA;;IAGA,IAAIG,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,0BAA0B,GAAG,MAAM;IAEvC,SAASC,qBAAqBA,CAACC,IAAI,EAAE;MACnC,OAAOA,IAAI,CAACL,OAAO,CAACG,0BAA0B,EAAE,KAAK,CAAC;IACxD;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASG,aAAaA,CAAClC,OAAO,EAAEmC,KAAK,EAAE;MACrC;MACA;MACA,IAAI,OAAOnC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACxB,GAAG,IAAI,IAAI,EAAE;QAC1E;QACA,OAAOgD,MAAM,CAAC,EAAE,GAAGxB,OAAO,CAACxB,GAAG,CAAC;MACjC,CAAC,CAAC;;MAGF,OAAO2D,KAAK,CAACC,QAAQ,CAAC,EAAE,CAAC;IAC3B;IAEA,SAASC,YAAYA,CAAC9B,QAAQ,EAAE+B,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAEvH,QAAQ,EAAE;MACzE,IAAIwC,IAAI,GAAG,OAAO8C,QAAQ;MAE1B,IAAI9C,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,SAAS,EAAE;QAC9C;QACA8C,QAAQ,GAAG,IAAI;MACjB;MAEA,IAAIkC,cAAc,GAAG,KAAK;MAE1B,IAAIlC,QAAQ,KAAK,IAAI,EAAE;QACrBkC,cAAc,GAAG,IAAI;MACvB,CAAC,MAAM;QACL,QAAQhF,IAAI;UACV,KAAK,QAAQ;UACb,KAAK,QAAQ;YACXgF,cAAc,GAAG,IAAI;YACrB;UAEF,KAAK,QAAQ;YACX,QAAQlC,QAAQ,CAAC3C,QAAQ;cACvB,KAAK5H,kBAAkB;cACvB,KAAKC,iBAAiB;gBACpBwM,cAAc,GAAG,IAAI;YACzB;QAEJ;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB,IAAIC,MAAM,GAAGnC,QAAQ;QACrB,IAAIoC,WAAW,GAAG1H,QAAQ,CAACyH,MAAM,CAAC,CAAC,CAAC;QACpC;;QAEA,IAAIE,QAAQ,GAAGJ,SAAS,KAAK,EAAE,GAAGlB,SAAS,GAAGY,aAAa,CAACQ,MAAM,EAAE,CAAC,CAAC,GAAGF,SAAS;QAElF,IAAIrJ,KAAK,CAAC0J,OAAO,CAACF,WAAW,CAAC,EAAE;UAC9B,IAAIG,eAAe,GAAG,EAAE;UAExB,IAAIF,QAAQ,IAAI,IAAI,EAAE;YACpBE,eAAe,GAAGd,qBAAqB,CAACY,QAAQ,CAAC,GAAG,GAAG;UACzD;UAEAP,YAAY,CAACM,WAAW,EAAEL,KAAK,EAAEQ,eAAe,EAAE,EAAE,EAAE,UAAUC,CAAC,EAAE;YACjE,OAAOA,CAAC;UACV,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIJ,WAAW,IAAI,IAAI,EAAE;UAC9B,IAAIvB,cAAc,CAACuB,WAAW,CAAC,EAAE;YAC/BA,WAAW,GAAG9B,kBAAkB,CAAC8B,WAAW;YAAE;YAC9C;YACAJ,aAAa;YAAK;YAClBI,WAAW,CAACnE,GAAG,KAAK,CAACkE,MAAM,IAAIA,MAAM,CAAClE,GAAG,KAAKmE,WAAW,CAACnE,GAAG,CAAC;YAAG;YACjEwD,qBAAqB,CAAC,EAAE,GAAGW,WAAW,CAACnE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGoE,QAAQ,CAAC;UACrE;UAEAN,KAAK,CAACU,IAAI,CAACL,WAAW,CAAC;QACzB;QAEA,OAAO,CAAC;MACV;MAEA,IAAIM,KAAK;MACT,IAAIC,QAAQ;MACZ,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAC;;MAEtB,IAAIC,cAAc,GAAGZ,SAAS,KAAK,EAAE,GAAGlB,SAAS,GAAGkB,SAAS,GAAGjB,YAAY;MAE5E,IAAIpI,KAAK,CAAC0J,OAAO,CAACtC,QAAQ,CAAC,EAAE;QAC3B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACtH,MAAM,EAAE0H,CAAC,EAAE,EAAE;UACxCsC,KAAK,GAAG1C,QAAQ,CAACI,CAAC,CAAC;UACnBuC,QAAQ,GAAGE,cAAc,GAAGlB,aAAa,CAACe,KAAK,EAAEtC,CAAC,CAAC;UACnDwC,YAAY,IAAId,YAAY,CAACY,KAAK,EAAEX,KAAK,EAAEC,aAAa,EAAEW,QAAQ,EAAEjI,QAAQ,CAAC;QAC/E;MACF,CAAC,MAAM;QACL,IAAIoI,UAAU,GAAG1L,aAAa,CAAC4I,QAAQ,CAAC;QAExC,IAAI,OAAO8C,UAAU,KAAK,UAAU,EAAE;UACpC,IAAIC,gBAAgB,GAAG/C,QAAQ;UAE/B;YACE;YACA,IAAI8C,UAAU,KAAKC,gBAAgB,CAACC,OAAO,EAAE;cAC3C,IAAI,CAACzB,gBAAgB,EAAE;gBACrBjJ,IAAI,CAAC,2CAA2C,GAAG,8CAA8C,CAAC;cACpG;cAEAiJ,gBAAgB,GAAG,IAAI;YACzB;UACF;UAEA,IAAIrK,QAAQ,GAAG4L,UAAU,CAACnJ,IAAI,CAACoJ,gBAAgB,CAAC;UAChD,IAAIE,IAAI;UACR,IAAIC,EAAE,GAAG,CAAC;UAEV,OAAO,CAAC,CAACD,IAAI,GAAG/L,QAAQ,CAACiM,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;YACrCV,KAAK,GAAGO,IAAI,CAACnD,KAAK;YAClB6C,QAAQ,GAAGE,cAAc,GAAGlB,aAAa,CAACe,KAAK,EAAEQ,EAAE,EAAE,CAAC;YACtDN,YAAY,IAAId,YAAY,CAACY,KAAK,EAAEX,KAAK,EAAEC,aAAa,EAAEW,QAAQ,EAAEjI,QAAQ,CAAC;UAC/E;QACF,CAAC,MAAM,IAAIwC,IAAI,KAAK,QAAQ,EAAE;UAC5B,IAAImG,cAAc,GAAG,EAAE,GAAGrD,QAAQ;UAElC;YACE;cACE,MAAMvE,KAAK,CAAE,iDAAiD,IAAI4H,cAAc,KAAK,iBAAiB,GAAG,oBAAoB,GAAGrI,MAAM,CAACsI,IAAI,CAACtD,QAAQ,CAAC,CAACuD,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAGF,cAAc,CAAC,GAAG,2EAA4E,CAAC;YAC1Q;UACF;QACF;MACF;MAEA,OAAOT,YAAY;IACrB;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,SAASY,WAAWA,CAACxD,QAAQ,EAAEyD,IAAI,EAAErI,OAAO,EAAE;MAC5C,IAAI4E,QAAQ,IAAI,IAAI,EAAE;QACpB,OAAOA,QAAQ;MACjB;MAEA,IAAI0D,MAAM,GAAG,EAAE;MACf,IAAIC,KAAK,GAAG,CAAC;MACb7B,YAAY,CAAC9B,QAAQ,EAAE0D,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,UAAUhB,KAAK,EAAE;QACtD,OAAOe,IAAI,CAAC9J,IAAI,CAACyB,OAAO,EAAEsH,KAAK,EAAEiB,KAAK,EAAE,CAAC;MAC3C,CAAC,CAAC;MACF,OAAOD,MAAM;IACf;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASE,aAAaA,CAAC5D,QAAQ,EAAE;MAC/B,IAAI6D,CAAC,GAAG,CAAC;MACTL,WAAW,CAACxD,QAAQ,EAAE,YAAY;QAChC6D,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;MACF,OAAOA,CAAC;IACV;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,SAASC,eAAeA,CAAC9D,QAAQ,EAAE+D,WAAW,EAAEC,cAAc,EAAE;MAC9DR,WAAW,CAACxD,QAAQ,EAAE,YAAY;QAChC+D,WAAW,CAACrK,KAAK,CAAC,IAAI,EAAEjB,SAAS,CAAC,CAAC,CAAC;MACtC,CAAC,EAAEuL,cAAc,CAAC;IACpB;IACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASC,OAAOA,CAACjE,QAAQ,EAAE;MACzB,OAAOwD,WAAW,CAACxD,QAAQ,EAAE,UAAU0C,KAAK,EAAE;QAC5C,OAAOA,KAAK;MACd,CAAC,CAAC,IAAI,EAAE;IACV;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASwB,SAASA,CAAClE,QAAQ,EAAE;MAC3B,IAAI,CAACa,cAAc,CAACb,QAAQ,CAAC,EAAE;QAC7B;UACE,MAAMvE,KAAK,CAAE,uEAAwE,CAAC;QACxF;MACF;MAEA,OAAOuE,QAAQ;IACjB;IAEA,SAASmE,aAAaA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;MACzD,IAAIA,oBAAoB,KAAKnI,SAAS,EAAE;QACtCmI,oBAAoB,GAAG,IAAI;MAC7B,CAAC,MAAM;QACL;UACE,IAAIA,oBAAoB,KAAK,IAAI,IAAI,OAAOA,oBAAoB,KAAK,UAAU,EAAE;YAC/EtL,KAAK,CAAC,+DAA+D,GAAG,gCAAgC,EAAEsL,oBAAoB,CAAC;UACjI;QACF;MACF;MAEA,IAAIjJ,OAAO,GAAG;QACZiC,QAAQ,EAAErH,kBAAkB;QAC5BsO,qBAAqB,EAAED,oBAAoB;QAC3C;QACA;QACA;QACA;QACA;QACAE,aAAa,EAAEH,YAAY;QAC3BI,cAAc,EAAEJ,YAAY;QAC5B;QACA;QACAK,YAAY,EAAE,CAAC;QACf;QACAC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDvJ,OAAO,CAACsJ,QAAQ,GAAG;QACjBrH,QAAQ,EAAEtH,mBAAmB;QAC7BwH,QAAQ,EAAEnC;MACZ,CAAC;MACD,IAAIwJ,yCAAyC,GAAG,KAAK;MACrD,IAAIC,mCAAmC,GAAG,KAAK;MAC/C,IAAIC,mCAAmC,GAAG,KAAK;MAE/C;QACE;QACA;QACA;QACA,IAAIH,QAAQ,GAAG;UACbtH,QAAQ,EAAErH,kBAAkB;UAC5BuH,QAAQ,EAAEnC,OAAO;UACjBkJ,qBAAqB,EAAElJ,OAAO,CAACkJ;QACjC,CAAC,CAAC,CAAC;;QAEHtJ,MAAM,CAAC+J,gBAAgB,CAACJ,QAAQ,EAAE;UAChCD,QAAQ,EAAE;YACRzI,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,IAAI,CAAC4I,mCAAmC,EAAE;gBACxCA,mCAAmC,GAAG,IAAI;gBAE1C9L,KAAK,CAAC,gFAAgF,GAAG,4EAA4E,CAAC;cACxK;cAEA,OAAOqC,OAAO,CAACsJ,QAAQ;YACzB,CAAC;YACDM,GAAG,EAAE,SAAAA,CAAUC,SAAS,EAAE;cACxB7J,OAAO,CAACsJ,QAAQ,GAAGO,SAAS;YAC9B;UACF,CAAC;UACDV,aAAa,EAAE;YACbtI,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOb,OAAO,CAACmJ,aAAa;YAC9B,CAAC;YACDS,GAAG,EAAE,SAAAA,CAAUT,aAAa,EAAE;cAC5BnJ,OAAO,CAACmJ,aAAa,GAAGA,aAAa;YACvC;UACF,CAAC;UACDC,cAAc,EAAE;YACdvI,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOb,OAAO,CAACoJ,cAAc;YAC/B,CAAC;YACDQ,GAAG,EAAE,SAAAA,CAAUR,cAAc,EAAE;cAC7BpJ,OAAO,CAACoJ,cAAc,GAAGA,cAAc;YACzC;UACF,CAAC;UACDC,YAAY,EAAE;YACZxI,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOb,OAAO,CAACqJ,YAAY;YAC7B,CAAC;YACDO,GAAG,EAAE,SAAAA,CAAUP,YAAY,EAAE;cAC3BrJ,OAAO,CAACqJ,YAAY,GAAGA,YAAY;YACrC;UACF,CAAC;UACDE,QAAQ,EAAE;YACR1I,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,IAAI,CAAC2I,yCAAyC,EAAE;gBAC9CA,yCAAyC,GAAG,IAAI;gBAEhD7L,KAAK,CAAC,gFAAgF,GAAG,4EAA4E,CAAC;cACxK;cAEA,OAAOqC,OAAO,CAACuJ,QAAQ;YACzB;UACF,CAAC;UACDvK,WAAW,EAAE;YACX6B,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOb,OAAO,CAAChB,WAAW;YAC5B,CAAC;YACD4K,GAAG,EAAE,SAAAA,CAAU5K,WAAW,EAAE;cAC1B,IAAI,CAAC0K,mCAAmC,EAAE;gBACxCxM,IAAI,CAAC,2DAA2D,GAAG,4EAA4E,EAAE8B,WAAW,CAAC;gBAE7J0K,mCAAmC,GAAG,IAAI;cAC5C;YACF;UACF;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJ1J,OAAO,CAACuJ,QAAQ,GAAGA,QAAQ;MAC7B;MAEA;QACEvJ,OAAO,CAAC8J,gBAAgB,GAAG,IAAI;QAC/B9J,OAAO,CAAC+J,iBAAiB,GAAG,IAAI;MAClC;MAEA,OAAO/J,OAAO;IAChB;IAEA,IAAIgK,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAEhB,SAASC,eAAeA,CAAC7H,OAAO,EAAE;MAChC,IAAIA,OAAO,CAAC8H,OAAO,KAAKL,aAAa,EAAE;QACrC,IAAIM,IAAI,GAAG/H,OAAO,CAACgI,OAAO;QAC1B,IAAIC,QAAQ,GAAGF,IAAI,CAAC,CAAC,CAAC,CAAC;;QAEvB,IAAIG,OAAO,GAAGlI,OAAO;QACrBkI,OAAO,CAACJ,OAAO,GAAGJ,OAAO;QACzBQ,OAAO,CAACF,OAAO,GAAGC,QAAQ;QAC1BA,QAAQ,CAACE,IAAI,CAAC,UAAUC,YAAY,EAAE;UACpC,IAAIpI,OAAO,CAAC8H,OAAO,KAAKJ,OAAO,EAAE;YAC/B,IAAIW,aAAa,GAAGD,YAAY,CAACE,OAAO;YAExC;cACE,IAAID,aAAa,KAAK9J,SAAS,EAAE;gBAC/BnD,KAAK,CAAC,wDAAwD,GAAG,0DAA0D;gBAAG;gBAC9H,oCAAoC,GAAG,uBAAuB,EAAEgN,YAAY,CAAC;cAC/E;YACF,CAAC,CAAC;;YAGF,IAAIG,QAAQ,GAAGvI,OAAO;YACtBuI,QAAQ,CAACT,OAAO,GAAGH,QAAQ;YAC3BY,QAAQ,CAACP,OAAO,GAAGK,aAAa;UAClC;QACF,CAAC,EAAE,UAAUjN,KAAK,EAAE;UAClB,IAAI4E,OAAO,CAAC8H,OAAO,KAAKJ,OAAO,EAAE;YAC/B;YACA,IAAIc,QAAQ,GAAGxI,OAAO;YACtBwI,QAAQ,CAACV,OAAO,GAAGF,QAAQ;YAC3BY,QAAQ,CAACR,OAAO,GAAG5M,KAAK;UAC1B;QACF,CAAC,CAAC;MACJ;MAEA,IAAI4E,OAAO,CAAC8H,OAAO,KAAKH,QAAQ,EAAE;QAChC,OAAO3H,OAAO,CAACgI,OAAO;MACxB,CAAC,MAAM;QACL,MAAMhI,OAAO,CAACgI,OAAO;MACvB;IACF;IAEA,SAASS,IAAIA,CAACV,IAAI,EAAE;MAClB,IAAI/H,OAAO,GAAG;QACZ;QACA8H,OAAO,EAAE,CAAC,CAAC;QACXE,OAAO,EAAED;MACX,CAAC;MACD,IAAIW,QAAQ,GAAG;QACbhJ,QAAQ,EAAEhH,eAAe;QACzBuH,QAAQ,EAAED,OAAO;QACjBG,KAAK,EAAE0H;MACT,CAAC;MAED;QACE;QACA,IAAInF,YAAY;QAChB,IAAIiG,SAAS,CAAC,CAAC;;QAEftL,MAAM,CAAC+J,gBAAgB,CAACsB,QAAQ,EAAE;UAChChG,YAAY,EAAE;YACZrB,YAAY,EAAE,IAAI;YAClB/C,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOoE,YAAY;YACrB,CAAC;YACD2E,GAAG,EAAE,SAAAA,CAAUuB,eAAe,EAAE;cAC9BxN,KAAK,CAAC,mEAAmE,GAAG,mEAAmE,GAAG,uDAAuD,CAAC;cAE1MsH,YAAY,GAAGkG,eAAe,CAAC,CAAC;cAChC;;cAEAvL,MAAM,CAACgB,cAAc,CAACqK,QAAQ,EAAE,cAAc,EAAE;gBAC9CzG,UAAU,EAAE;cACd,CAAC,CAAC;YACJ;UACF,CAAC;UACD0G,SAAS,EAAE;YACTtH,YAAY,EAAE,IAAI;YAClB/C,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOqK,SAAS;YAClB,CAAC;YACDtB,GAAG,EAAE,SAAAA,CAAUwB,YAAY,EAAE;cAC3BzN,KAAK,CAAC,gEAAgE,GAAG,mEAAmE,GAAG,uDAAuD,CAAC;cAEvMuN,SAAS,GAAGE,YAAY,CAAC,CAAC;cAC1B;;cAEAxL,MAAM,CAACgB,cAAc,CAACqK,QAAQ,EAAE,WAAW,EAAE;gBAC3CzG,UAAU,EAAE;cACd,CAAC,CAAC;YACJ;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAOyG,QAAQ;IACjB;IAEA,SAASI,UAAUA,CAACjJ,MAAM,EAAE;MAC1B;QACE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACH,QAAQ,KAAKjH,eAAe,EAAE;UACzD2C,KAAK,CAAC,8DAA8D,GAAG,mDAAmD,GAAG,wBAAwB,CAAC;QACxJ,CAAC,MAAM,IAAI,OAAOyE,MAAM,KAAK,UAAU,EAAE;UACvCzE,KAAK,CAAC,yDAAyD,EAAEyE,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,MAAM,CAAC;QAC5G,CAAC,MAAM;UACL,IAAIA,MAAM,CAAC9E,MAAM,KAAK,CAAC,IAAI8E,MAAM,CAAC9E,MAAM,KAAK,CAAC,EAAE;YAC9CK,KAAK,CAAC,8EAA8E,EAAEyE,MAAM,CAAC9E,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAAG,6CAA6C,CAAC;UACzM;QACF;QAEA,IAAI8E,MAAM,IAAI,IAAI,EAAE;UAClB,IAAIA,MAAM,CAAC6C,YAAY,IAAI,IAAI,IAAI7C,MAAM,CAAC8I,SAAS,IAAI,IAAI,EAAE;YAC3DvN,KAAK,CAAC,wEAAwE,GAAG,8CAA8C,CAAC;UAClI;QACF;MACF;MAEA,IAAI2N,WAAW,GAAG;QAChBrJ,QAAQ,EAAEpH,sBAAsB;QAChCuH,MAAM,EAAEA;MACV,CAAC;MAED;QACE,IAAImJ,OAAO;QACX3L,MAAM,CAACgB,cAAc,CAAC0K,WAAW,EAAE,aAAa,EAAE;UAChD9G,UAAU,EAAE,KAAK;UACjBZ,YAAY,EAAE,IAAI;UAClB/C,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf,OAAO0K,OAAO;UAChB,CAAC;UACD3B,GAAG,EAAE,SAAAA,CAAU3K,IAAI,EAAE;YACnBsM,OAAO,GAAGtM,IAAI;YAEd,IAAImD,MAAM,CAACpD,WAAW,IAAI,IAAI,EAAE;cAC9BoD,MAAM,CAACpD,WAAW,GAAGC,IAAI;YAC3B;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAOqM,WAAW;IACpB;;IAEA;;IAEA,IAAIE,cAAc,GAAG,KAAK,CAAC,CAAC;;IAE5B,SAASC,kBAAkBA,CAAC3J,IAAI,EAAE;MAChC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC1D,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAIA,IAAI,KAAKvH,OAAO,CAACC,QAAQ,IAAIsH,IAAI,KAAKvH,OAAO,CAACG,QAAQ,IAAIoH,IAAI,KAAKvG,6BAA6B,IAAIuG,IAAI,KAAKvH,OAAO,CAACE,UAAU,IAAIqH,IAAI,KAAKvH,OAAO,CAACO,QAAQ,IAAIgH,IAAI,KAAK/G,wBAAwB,IAAI+G,IAAI,KAAKrG,wBAAwB,IAAI+P,cAAc,EAAG;QAC7P,OAAO,IAAI;MACb;MAEA,IAAI,OAAO1J,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;QAC7C,IAAIA,IAAI,CAACG,QAAQ,KAAKhH,eAAe,IAAI6G,IAAI,CAACG,QAAQ,KAAKjH,eAAe,IAAI8G,IAAI,CAACG,QAAQ,KAAKtH,mBAAmB,IAAImH,IAAI,CAACG,QAAQ,KAAKrH,kBAAkB,IAAIkH,IAAI,CAACG,QAAQ,KAAKpH,sBAAsB,IAAIiH,IAAI,CAACG,QAAQ,KAAK7G,sBAAsB,IAAI0G,IAAI,CAACG,QAAQ,KAAK/G,gBAAgB,IAAI4G,IAAI,CAAC,CAAC,CAAC,KAAK3G,uBAAuB,EAAE;UAChU,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd;IAEA,SAASuQ,IAAIA,CAAC5J,IAAI,EAAE6J,OAAO,EAAE;MAC3B;QACE,IAAI,CAACF,kBAAkB,CAAC3J,IAAI,CAAC,EAAE;UAC7BnE,KAAK,CAAC,wDAAwD,GAAG,cAAc,EAAEmE,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,IAAI,CAAC;QACxH;MACF;MAEA,IAAIwJ,WAAW,GAAG;QAChBrJ,QAAQ,EAAEjH,eAAe;QACzB8G,IAAI,EAAEA,IAAI;QACV6J,OAAO,EAAEA,OAAO,KAAK7K,SAAS,GAAG,IAAI,GAAG6K;MAC1C,CAAC;MAED;QACE,IAAIJ,OAAO;QACX3L,MAAM,CAACgB,cAAc,CAAC0K,WAAW,EAAE,aAAa,EAAE;UAChD9G,UAAU,EAAE,KAAK;UACjBZ,YAAY,EAAE,IAAI;UAClB/C,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf,OAAO0K,OAAO;UAChB,CAAC;UACD3B,GAAG,EAAE,SAAAA,CAAU3K,IAAI,EAAE;YACnBsM,OAAO,GAAGtM,IAAI;YAEd,IAAI6C,IAAI,CAAC9C,WAAW,IAAI,IAAI,EAAE;cAC5B8C,IAAI,CAAC9C,WAAW,GAAGC,IAAI;YACzB;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAOqM,WAAW;IACpB;IAEA,SAASM,iBAAiBA,CAAA,EAAG;MAC3B,IAAIC,UAAU,GAAG1P,sBAAsB,CAACC,OAAO;MAE/C,IAAI,EAAEyP,UAAU,KAAK,IAAI,CAAC,EAAE;QAC1B;UACE,MAAMxL,KAAK,CAAE,ibAAkb,CAAC;QAClc;MACF;MAEA,OAAOwL,UAAU;IACnB;IAEA,SAASC,UAAUA,CAACC,OAAO,EAAEC,qBAAqB,EAAE;MAClD,IAAIH,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MAEpC;QACE,IAAII,qBAAqB,KAAKlL,SAAS,EAAE;UACvCnD,KAAK,CAAC,sDAAsD,GAAG,6CAA6C,GAAG,mBAAmB,EAAEqO,qBAAqB,EAAE,OAAOA,qBAAqB,KAAK,QAAQ,IAAIxO,KAAK,CAAC0J,OAAO,CAAC7J,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,0CAA0C,GAAG,gDAAgD,GAAG,uDAAuD,GAAG,EAAE,CAAC;QACrY,CAAC,CAAC;;QAGF,IAAI0O,OAAO,CAAC5J,QAAQ,KAAKrB,SAAS,EAAE;UAClC,IAAImL,WAAW,GAAGF,OAAO,CAAC5J,QAAQ,CAAC,CAAC;UACpC;;UAEA,IAAI8J,WAAW,CAAC1C,QAAQ,KAAKwC,OAAO,EAAE;YACpCpO,KAAK,CAAC,qFAAqF,GAAG,sFAAsF,CAAC;UACvL,CAAC,MAAM,IAAIsO,WAAW,CAAC3C,QAAQ,KAAKyC,OAAO,EAAE;YAC3CpO,KAAK,CAAC,yDAAyD,GAAG,mDAAmD,CAAC;UACxH;QACF;MACF;MAEA,OAAOkO,UAAU,CAACC,UAAU,CAACC,OAAO,EAAEC,qBAAqB,CAAC;IAC9D;IACA,SAASE,QAAQA,CAACC,YAAY,EAAE;MAC9B,IAAIN,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACK,QAAQ,CAACC,YAAY,CAAC;IAC1C;IACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,UAAU,EAAE7J,IAAI,EAAE;MAC7C,IAAIoJ,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACO,UAAU,CAACC,OAAO,EAAEC,UAAU,EAAE7J,IAAI,CAAC;IACzD;IACA,SAAS8J,MAAMA,CAACC,YAAY,EAAE;MAC5B,IAAIX,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACU,MAAM,CAACC,YAAY,CAAC;IACxC;IACA,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;MAC/B,IAAId,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACY,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC;IAC3C;IACA,SAASC,eAAeA,CAACF,MAAM,EAAEC,IAAI,EAAE;MACrC,IAAId,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACe,eAAe,CAACF,MAAM,EAAEC,IAAI,CAAC;IACjD;IACA,SAASE,WAAWA,CAACvN,QAAQ,EAAEqN,IAAI,EAAE;MACnC,IAAId,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACgB,WAAW,CAACvN,QAAQ,EAAEqN,IAAI,CAAC;IAC/C;IACA,SAASG,OAAOA,CAACJ,MAAM,EAAEC,IAAI,EAAE;MAC7B,IAAId,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACiB,OAAO,CAACJ,MAAM,EAAEC,IAAI,CAAC;IACzC;IACA,SAASI,mBAAmBA,CAACjK,GAAG,EAAE4J,MAAM,EAAEC,IAAI,EAAE;MAC9C,IAAId,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACkB,mBAAmB,CAACjK,GAAG,EAAE4J,MAAM,EAAEC,IAAI,CAAC;IAC1D;IACA,SAASK,aAAaA,CAACtI,KAAK,EAAEuI,WAAW,EAAE;MACzC;QACE,IAAIpB,UAAU,GAAGD,iBAAiB,CAAC,CAAC;QACpC,OAAOC,UAAU,CAACmB,aAAa,CAACtI,KAAK,EAAEuI,WAAW,CAAC;MACrD;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,OAAO;IACX,IAAIC,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IACb,IAAIC,SAAS;IACb,IAAIC,kBAAkB;IACtB,IAAIC,YAAY;IAEhB,SAASC,WAAWA,CAAA,EAAG,CAAC;IAExBA,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACrC,SAASC,WAAWA,CAAA,EAAG;MACrB;QACE,IAAIV,aAAa,KAAK,CAAC,EAAE;UACvB;UACAC,OAAO,GAAG3O,OAAO,CAACqP,GAAG;UACrBT,QAAQ,GAAG5O,OAAO,CAACmC,IAAI;UACvB0M,QAAQ,GAAG7O,OAAO,CAACtB,IAAI;UACvBoQ,SAAS,GAAG9O,OAAO,CAACb,KAAK;UACzB4P,SAAS,GAAG/O,OAAO,CAACsP,KAAK;UACzBN,kBAAkB,GAAGhP,OAAO,CAACuP,cAAc;UAC3CN,YAAY,GAAGjP,OAAO,CAACwP,QAAQ,CAAC,CAAC;;UAEjC,IAAIjO,KAAK,GAAG;YACV6D,YAAY,EAAE,IAAI;YAClBY,UAAU,EAAE,IAAI;YAChBE,KAAK,EAAEgJ,WAAW;YAClBjJ,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEH7E,MAAM,CAAC+J,gBAAgB,CAACnL,OAAO,EAAE;YAC/BmC,IAAI,EAAEZ,KAAK;YACX8N,GAAG,EAAE9N,KAAK;YACV7C,IAAI,EAAE6C,KAAK;YACXpC,KAAK,EAAEoC,KAAK;YACZ+N,KAAK,EAAE/N,KAAK;YACZgO,cAAc,EAAEhO,KAAK;YACrBiO,QAAQ,EAAEjO;UACZ,CAAC,CAAC;UACF;QACF;QAEAmN,aAAa,EAAE;MACjB;IACF;IACA,SAASe,YAAYA,CAAA,EAAG;MACtB;QACEf,aAAa,EAAE;QAEf,IAAIA,aAAa,KAAK,CAAC,EAAE;UACvB;UACA,IAAInN,KAAK,GAAG;YACV6D,YAAY,EAAE,IAAI;YAClBY,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEH7E,MAAM,CAAC+J,gBAAgB,CAACnL,OAAO,EAAE;YAC/BqP,GAAG,EAAE3T,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACtB2E,KAAK,EAAEyI;YACT,CAAC,CAAC;YACFxM,IAAI,EAAEzG,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACvB2E,KAAK,EAAE0I;YACT,CAAC,CAAC;YACFlQ,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACvB2E,KAAK,EAAE2I;YACT,CAAC,CAAC;YACF1P,KAAK,EAAEzD,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACxB2E,KAAK,EAAE4I;YACT,CAAC,CAAC;YACFQ,KAAK,EAAE5T,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACxB2E,KAAK,EAAE6I;YACT,CAAC,CAAC;YACFQ,cAAc,EAAE7T,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cACjC2E,KAAK,EAAE8I;YACT,CAAC,CAAC;YACFQ,QAAQ,EAAE9T,OAAO,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;cAC3B2E,KAAK,EAAE+I;YACT,CAAC;UACH,CAAC,CAAC;UACF;QACF;QAEA,IAAIP,aAAa,GAAG,CAAC,EAAE;UACrBvP,KAAK,CAAC,iCAAiC,GAAG,+CAA+C,CAAC;QAC5F;MACF;IACF;IAEA,IAAIuQ,wBAAwB,GAAGlR,oBAAoB,CAACb,sBAAsB;IAC1E,IAAIgS,MAAM;IACV,SAASC,6BAA6BA,CAACnP,IAAI,EAAEkF,MAAM,EAAEkK,OAAO,EAAE;MAC5D;QACE,IAAIF,MAAM,KAAKrN,SAAS,EAAE;UACxB;UACA,IAAI;YACF,MAAMT,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOsC,CAAC,EAAE;YACV,IAAIuD,KAAK,GAAGvD,CAAC,CAAChG,KAAK,CAAC2R,IAAI,CAAC,CAAC,CAACpI,KAAK,CAAC,cAAc,CAAC;YAChDiI,MAAM,GAAGjI,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;UAClC;QACF,CAAC,CAAC;;QAGF,OAAO,IAAI,GAAGiI,MAAM,GAAGlP,IAAI;MAC7B;IACF;IACA,IAAIsP,OAAO,GAAG,KAAK;IACnB,IAAIC,mBAAmB;IAEvB;MACE,IAAIC,eAAe,GAAG,OAAOC,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,GAAG;MACnEH,mBAAmB,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC7C;IAEA,SAASG,4BAA4BA,CAACC,EAAE,EAAEC,SAAS,EAAE;MACnD;MACA,IAAI,CAACD,EAAE,IAAIN,OAAO,EAAE;QAClB,OAAO,EAAE;MACX;MAEA;QACE,IAAIQ,KAAK,GAAGP,mBAAmB,CAAC3N,GAAG,CAACgO,EAAE,CAAC;QAEvC,IAAIE,KAAK,KAAKjO,SAAS,EAAE;UACvB,OAAOiO,KAAK;QACd;MACF;MAEA,IAAIC,OAAO;MACXT,OAAO,GAAG,IAAI;MACd,IAAIU,yBAAyB,GAAG5O,KAAK,CAAC6O,iBAAiB,CAAC,CAAC;;MAEzD7O,KAAK,CAAC6O,iBAAiB,GAAGpO,SAAS;MACnC,IAAIqO,kBAAkB;MAEtB;QACEA,kBAAkB,GAAGjB,wBAAwB,CAAC9R,OAAO,CAAC,CAAC;QACvD;;QAEA8R,wBAAwB,CAAC9R,OAAO,GAAG,IAAI;QACvCwR,WAAW,CAAC,CAAC;MACf;MAEA,IAAI;QACF;QACA,IAAIkB,SAAS,EAAE;UACb;UACA,IAAIM,IAAI,GAAG,SAAAA,CAAA,EAAY;YACrB,MAAM/O,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,CAAC;;UAGHT,MAAM,CAACgB,cAAc,CAACwO,IAAI,CAAC/Q,SAAS,EAAE,OAAO,EAAE;YAC7CuL,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf;cACA;cACA,MAAMvJ,KAAK,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UAEF,IAAI,OAAOgP,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACP,SAAS,EAAE;YACpD;YACA;YACA,IAAI;cACFO,OAAO,CAACP,SAAS,CAACM,IAAI,EAAE,EAAE,CAAC;YAC7B,CAAC,CAAC,OAAOzM,CAAC,EAAE;cACVqM,OAAO,GAAGrM,CAAC;YACb;YAEA0M,OAAO,CAACP,SAAS,CAACD,EAAE,EAAE,EAAE,EAAEO,IAAI,CAAC;UACjC,CAAC,MAAM;YACL,IAAI;cACFA,IAAI,CAAC7Q,IAAI,CAAC,CAAC;YACb,CAAC,CAAC,OAAOoE,CAAC,EAAE;cACVqM,OAAO,GAAGrM,CAAC;YACb;YAEAkM,EAAE,CAACtQ,IAAI,CAAC6Q,IAAI,CAAC/Q,SAAS,CAAC;UACzB;QACF,CAAC,MAAM;UACL,IAAI;YACF,MAAMgC,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOsC,CAAC,EAAE;YACVqM,OAAO,GAAGrM,CAAC;UACb;UAEAkM,EAAE,CAAC,CAAC;QACN;MACF,CAAC,CAAC,OAAOS,MAAM,EAAE;QACf;QACA,IAAIA,MAAM,IAAIN,OAAO,IAAI,OAAOM,MAAM,CAAC3S,KAAK,KAAK,QAAQ,EAAE;UACzD;UACA;UACA,IAAI4S,WAAW,GAAGD,MAAM,CAAC3S,KAAK,CAAC6S,KAAK,CAAC,IAAI,CAAC;UAC1C,IAAIC,YAAY,GAAGT,OAAO,CAACrS,KAAK,CAAC6S,KAAK,CAAC,IAAI,CAAC;UAC5C,IAAIE,CAAC,GAAGH,WAAW,CAACjS,MAAM,GAAG,CAAC;UAC9B,IAAI8J,CAAC,GAAGqI,YAAY,CAACnS,MAAM,GAAG,CAAC;UAE/B,OAAOoS,CAAC,IAAI,CAAC,IAAItI,CAAC,IAAI,CAAC,IAAImI,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACrI,CAAC,CAAC,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACAA,CAAC,EAAE;UACL;UAEA,OAAOsI,CAAC,IAAI,CAAC,IAAItI,CAAC,IAAI,CAAC,EAAEsI,CAAC,EAAE,EAAEtI,CAAC,EAAE,EAAE;YACjC;YACA;YACA,IAAImI,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACrI,CAAC,CAAC,EAAE;cACtC;cACA;cACA;cACA;cACA;cACA,IAAIsI,CAAC,KAAK,CAAC,IAAItI,CAAC,KAAK,CAAC,EAAE;gBACtB,GAAG;kBACDsI,CAAC,EAAE;kBACHtI,CAAC,EAAE,CAAC,CAAC;kBACL;;kBAEA,IAAIA,CAAC,GAAG,CAAC,IAAImI,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACrI,CAAC,CAAC,EAAE;oBAC/C;oBACA,IAAIuI,MAAM,GAAG,IAAI,GAAGJ,WAAW,CAACG,CAAC,CAAC,CAACzJ,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;oBAE9D;sBACE,IAAI,OAAO4I,EAAE,KAAK,UAAU,EAAE;wBAC5BL,mBAAmB,CAAC5E,GAAG,CAACiF,EAAE,EAAEc,MAAM,CAAC;sBACrC;oBACF,CAAC,CAAC;;oBAGF,OAAOA,MAAM;kBACf;gBACF,CAAC,QAAQD,CAAC,IAAI,CAAC,IAAItI,CAAC,IAAI,CAAC;cAC3B;cAEA;YACF;UACF;QACF;MACF,CAAC,SAAS;QACRmH,OAAO,GAAG,KAAK;QAEf;UACEL,wBAAwB,CAAC9R,OAAO,GAAG+S,kBAAkB;UACrDlB,YAAY,CAAC,CAAC;QAChB;QAEA5N,KAAK,CAAC6O,iBAAiB,GAAGD,yBAAyB;MACrD,CAAC,CAAC;;MAGF,IAAIhQ,IAAI,GAAG4P,EAAE,GAAGA,EAAE,CAAC7P,WAAW,IAAI6P,EAAE,CAAC5P,IAAI,GAAG,EAAE;MAC9C,IAAI2Q,cAAc,GAAG3Q,IAAI,GAAGmP,6BAA6B,CAACnP,IAAI,CAAC,GAAG,EAAE;MAEpE;QACE,IAAI,OAAO4P,EAAE,KAAK,UAAU,EAAE;UAC5BL,mBAAmB,CAAC5E,GAAG,CAACiF,EAAE,EAAEe,cAAc,CAAC;QAC7C;MACF;MAEA,OAAOA,cAAc;IACvB;IACA,SAASC,8BAA8BA,CAAChB,EAAE,EAAE1K,MAAM,EAAEkK,OAAO,EAAE;MAC3D;QACE,OAAOO,4BAA4B,CAACC,EAAE,EAAE,KAAK,CAAC;MAChD;IACF;IAEA,SAASiB,eAAeA,CAAChQ,SAAS,EAAE;MAClC,IAAIzB,SAAS,GAAGyB,SAAS,CAACzB,SAAS;MACnC,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAAC8B,gBAAgB,CAAC;IACpD;IAEA,SAAS4P,oCAAoCA,CAACjO,IAAI,EAAEqC,MAAM,EAAEkK,OAAO,EAAE;MAEnE,IAAIvM,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,EAAE;MACX;MAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC9B;UACE,OAAO8M,4BAA4B,CAAC9M,IAAI,EAAEgO,eAAe,CAAChO,IAAI,CAAC,CAAC;QAClE;MACF;MAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOsM,6BAA6B,CAACtM,IAAI,CAAC;MAC5C;MAEA,QAAQA,IAAI;QACV,KAAKvH,OAAO,CAACO,QAAQ;UACnB,OAAOsT,6BAA6B,CAAC,UAAU,CAAC;QAElD,KAAKrT,wBAAwB;UAC3B,OAAOqT,6BAA6B,CAAC,cAAc,CAAC;MACxD;MAEA,IAAI,OAAOtM,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACG,QAAQ;UACnB,KAAKpH,sBAAsB;YACzB,OAAOgV,8BAA8B,CAAC/N,IAAI,CAACM,MAAM,CAAC;UAEpD,KAAKpH,eAAe;YAClB;YACA,OAAO+U,oCAAoC,CAACjO,IAAI,CAACA,IAAI,EAAEqC,MAAM,EAAEkK,OAAO,CAAC;UAEzE,KAAKnT,gBAAgB;YACnB,OAAO2U,8BAA8B,CAAC/N,IAAI,CAACO,OAAO,CAAC;UAErD,KAAKpH,eAAe;YAClB;cACE,IAAIqH,aAAa,GAAGR,IAAI;cACxB,IAAIS,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF;gBACA,OAAOqN,oCAAoC,CAACtN,IAAI,CAACF,OAAO,CAAC,EAAE4B,MAAM,EAAEkK,OAAO,CAAC;cAC7E,CAAC,CAAC,OAAO1L,CAAC,EAAE,CAAC;YACf;QACJ;MACF;MAEA,OAAO,EAAE;IACX;IAEA,IAAIqN,kBAAkB,GAAG,CAAC,CAAC;IAC3B,IAAIC,wBAAwB,GAAGjT,oBAAoB,CAACR,sBAAsB;IAE1E,SAAS0T,6BAA6BA,CAAC7L,OAAO,EAAE;MAC9C;QACE,IAAIA,OAAO,EAAE;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAAM;UAC1B,IAAI3H,KAAK,GAAGoT,oCAAoC,CAAC1L,OAAO,CAACvC,IAAI,EAAEuC,OAAO,CAACkB,OAAO,EAAEnB,KAAK,GAAGA,KAAK,CAACtC,IAAI,GAAG,IAAI,CAAC;UAC1GmO,wBAAwB,CAACvT,kBAAkB,CAACC,KAAK,CAAC;QACpD,CAAC,MAAM;UACLsT,wBAAwB,CAACvT,kBAAkB,CAAC,IAAI,CAAC;QACnD;MACF;IACF;IAEA,SAASyT,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEvR,aAAa,EAAEsF,OAAO,EAAE;MAC3E;QACE;QACA,IAAIkM,GAAG,GAAGnS,QAAQ,CAACG,IAAI,CAACiS,IAAI,CAAC5Q,MAAM,CAACvB,SAAS,CAAC2C,cAAc,CAAC;QAE7D,KAAK,IAAIyP,YAAY,IAAIL,SAAS,EAAE;UAClC,IAAIG,GAAG,CAACH,SAAS,EAAEK,YAAY,CAAC,EAAE;YAChC,IAAIC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;YACtB;YACA;;YAEA,IAAI;cACF;cACA;cACA,IAAI,OAAON,SAAS,CAACK,YAAY,CAAC,KAAK,UAAU,EAAE;gBACjD,IAAIE,GAAG,GAAGtQ,KAAK,CAAC,CAACtB,aAAa,IAAI,aAAa,IAAI,IAAI,GAAGuR,QAAQ,GAAG,SAAS,GAAGG,YAAY,GAAG,gBAAgB,GAAG,8EAA8E,GAAG,OAAOL,SAAS,CAACK,YAAY,CAAC,GAAG,IAAI,GAAG,+FAA+F,CAAC;gBAC5UE,GAAG,CAAC1R,IAAI,GAAG,qBAAqB;gBAChC,MAAM0R,GAAG;cACX;cAEAD,OAAO,GAAGN,SAAS,CAACK,YAAY,CAAC,CAACJ,MAAM,EAAEI,YAAY,EAAE1R,aAAa,EAAEuR,QAAQ,EAAE,IAAI,EAAE,8CAA8C,CAAC;YACxI,CAAC,CAAC,OAAOM,EAAE,EAAE;cACXF,OAAO,GAAGE,EAAE;YACd;YAEA,IAAIF,OAAO,IAAI,EAAEA,OAAO,YAAYrQ,KAAK,CAAC,EAAE;cAC1C6P,6BAA6B,CAAC7L,OAAO,CAAC;cAEtC1G,KAAK,CAAC,8BAA8B,GAAG,qCAAqC,GAAG,+DAA+D,GAAG,iEAAiE,GAAG,gEAAgE,GAAG,iCAAiC,EAAEoB,aAAa,IAAI,aAAa,EAAEuR,QAAQ,EAAEG,YAAY,EAAE,OAAOC,OAAO,CAAC;cAElYR,6BAA6B,CAAC,IAAI,CAAC;YACrC;YAEA,IAAIQ,OAAO,YAAYrQ,KAAK,IAAI,EAAEqQ,OAAO,CAACG,OAAO,IAAIb,kBAAkB,CAAC,EAAE;cACxE;cACA;cACAA,kBAAkB,CAACU,OAAO,CAACG,OAAO,CAAC,GAAG,IAAI;cAC1CX,6BAA6B,CAAC7L,OAAO,CAAC;cAEtC1G,KAAK,CAAC,oBAAoB,EAAE2S,QAAQ,EAAEI,OAAO,CAACG,OAAO,CAAC;cAEtDX,6BAA6B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;MACF;IACF;IAEA,SAASY,+BAA+BA,CAACzM,OAAO,EAAE;MAChD;QACE,IAAIA,OAAO,EAAE;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAAM;UAC1B,IAAI3H,KAAK,GAAGoT,oCAAoC,CAAC1L,OAAO,CAACvC,IAAI,EAAEuC,OAAO,CAACkB,OAAO,EAAEnB,KAAK,GAAGA,KAAK,CAACtC,IAAI,GAAG,IAAI,CAAC;UAC1GpF,kBAAkB,CAACC,KAAK,CAAC;QAC3B,CAAC,MAAM;UACLD,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF;IACF;IAEA,IAAIqU,6BAA6B;IAEjC;MACEA,6BAA6B,GAAG,KAAK;IACvC;IAEA,SAASC,2BAA2BA,CAAA,EAAG;MACrC,IAAIzU,iBAAiB,CAACH,OAAO,EAAE;QAC7B,IAAI6C,IAAI,GAAG8C,gBAAgB,CAACxF,iBAAiB,CAACH,OAAO,CAAC0F,IAAI,CAAC;QAE3D,IAAI7C,IAAI,EAAE;UACR,OAAO,kCAAkC,GAAGA,IAAI,GAAG,IAAI;QACzD;MACF;MAEA,OAAO,EAAE;IACX;IAEA,SAASgS,0BAA0BA,CAAC9M,MAAM,EAAE;MAC1C,IAAIA,MAAM,KAAKrD,SAAS,EAAE;QACxB,IAAIoQ,QAAQ,GAAG/M,MAAM,CAAC+M,QAAQ,CAACjL,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;QACvD,IAAIkL,UAAU,GAAGhN,MAAM,CAACgN,UAAU;QAClC,OAAO,yBAAyB,GAAGD,QAAQ,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG;MACtE;MAEA,OAAO,EAAE;IACX;IAEA,SAASC,kCAAkCA,CAACC,YAAY,EAAE;MACxD,IAAIA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAKvQ,SAAS,EAAE;QACvD,OAAOmQ,0BAA0B,CAACI,YAAY,CAACrO,QAAQ,CAAC;MAC1D;MAEA,OAAO,EAAE;IACX;IACA;AACA;AACA;AACA;AACA;;IAGA,IAAIsO,qBAAqB,GAAG,CAAC,CAAC;IAE9B,SAASC,4BAA4BA,CAACC,UAAU,EAAE;MAChD,IAAI7Q,IAAI,GAAGqQ,2BAA2B,CAAC,CAAC;MAExC,IAAI,CAACrQ,IAAI,EAAE;QACT,IAAI8Q,UAAU,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGA,UAAU,CAACxS,WAAW,IAAIwS,UAAU,CAACvS,IAAI;QAExG,IAAIwS,UAAU,EAAE;UACd9Q,IAAI,GAAG,6CAA6C,GAAG8Q,UAAU,GAAG,IAAI;QAC1E;MACF;MAEA,OAAO9Q,IAAI;IACb;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAAS+Q,mBAAmBA,CAACrN,OAAO,EAAEmN,UAAU,EAAE;MAChD,IAAI,CAACnN,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAACoN,SAAS,IAAItN,OAAO,CAACxB,GAAG,IAAI,IAAI,EAAE;QACtE;MACF;MAEAwB,OAAO,CAACE,MAAM,CAACoN,SAAS,GAAG,IAAI;MAC/B,IAAIC,yBAAyB,GAAGL,4BAA4B,CAACC,UAAU,CAAC;MAExE,IAAIF,qBAAqB,CAACM,yBAAyB,CAAC,EAAE;QACpD;MACF;MAEAN,qBAAqB,CAACM,yBAAyB,CAAC,GAAG,IAAI,CAAC,CAAC;MACzD;MACA;;MAEA,IAAIC,UAAU,GAAG,EAAE;MAEnB,IAAIxN,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAID,OAAO,CAACC,MAAM,KAAK/H,iBAAiB,CAACH,OAAO,EAAE;QAC7E;QACAyV,UAAU,GAAG,8BAA8B,GAAG9P,gBAAgB,CAACsC,OAAO,CAACC,MAAM,CAACxC,IAAI,CAAC,GAAG,GAAG;MAC3F;MAEA;QACEgP,+BAA+B,CAACzM,OAAO,CAAC;QAExC1G,KAAK,CAAC,uDAAuD,GAAG,sEAAsE,EAAEiU,yBAAyB,EAAEC,UAAU,CAAC;QAE9Kf,+BAA+B,CAAC,IAAI,CAAC;MACvC;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASgB,iBAAiBA,CAACC,IAAI,EAAEP,UAAU,EAAE;MAC3C,IAAI,OAAOO,IAAI,KAAK,QAAQ,EAAE;QAC5B;MACF;MAEA,IAAIvU,KAAK,CAAC0J,OAAO,CAAC6K,IAAI,CAAC,EAAE;QACvB,KAAK,IAAI/M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+M,IAAI,CAACzU,MAAM,EAAE0H,CAAC,EAAE,EAAE;UACpC,IAAIsC,KAAK,GAAGyK,IAAI,CAAC/M,CAAC,CAAC;UAEnB,IAAIS,cAAc,CAAC6B,KAAK,CAAC,EAAE;YACzBoK,mBAAmB,CAACpK,KAAK,EAAEkK,UAAU,CAAC;UACxC;QACF;MACF,CAAC,MAAM,IAAI/L,cAAc,CAACsM,IAAI,CAAC,EAAE;QAC/B;QACA,IAAIA,IAAI,CAACxN,MAAM,EAAE;UACfwN,IAAI,CAACxN,MAAM,CAACoN,SAAS,GAAG,IAAI;QAC9B;MACF,CAAC,MAAM,IAAII,IAAI,EAAE;QACf,IAAIrK,UAAU,GAAG1L,aAAa,CAAC+V,IAAI,CAAC;QAEpC,IAAI,OAAOrK,UAAU,KAAK,UAAU,EAAE;UACpC;UACA;UACA,IAAIA,UAAU,KAAKqK,IAAI,CAACnK,OAAO,EAAE;YAC/B,IAAI9L,QAAQ,GAAG4L,UAAU,CAACnJ,IAAI,CAACwT,IAAI,CAAC;YACpC,IAAIlK,IAAI;YAER,OAAO,CAAC,CAACA,IAAI,GAAG/L,QAAQ,CAACiM,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;cACrC,IAAIvC,cAAc,CAACoC,IAAI,CAACnD,KAAK,CAAC,EAAE;gBAC9BgN,mBAAmB,CAAC7J,IAAI,CAACnD,KAAK,EAAE8M,UAAU,CAAC;cAC7C;YACF;UACF;QACF;MACF;IACF;IACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASQ,iBAAiBA,CAAC3N,OAAO,EAAE;MAClC;QACE,IAAIvC,IAAI,GAAGuC,OAAO,CAACvC,IAAI;QAEvB,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKhB,SAAS,IAAI,OAAOgB,IAAI,KAAK,QAAQ,EAAE;UACnE;QACF;QAEA,IAAIoJ,SAAS;QAEb,IAAI,OAAOpJ,IAAI,KAAK,UAAU,EAAE;UAC9BoJ,SAAS,GAAGpJ,IAAI,CAACoJ,SAAS;QAC5B,CAAC,MAAM,IAAI,OAAOpJ,IAAI,KAAK,QAAQ,KAAKA,IAAI,CAACG,QAAQ,KAAKpH,sBAAsB;QAAI;QACpF;QACAiH,IAAI,CAACG,QAAQ,KAAKjH,eAAe,CAAC,EAAE;UAClCkQ,SAAS,GAAGpJ,IAAI,CAACoJ,SAAS;QAC5B,CAAC,MAAM;UACL;QACF;QAEA,IAAIA,SAAS,EAAE;UACb;UACA,IAAIjM,IAAI,GAAG8C,gBAAgB,CAACD,IAAI,CAAC;UACjCqO,cAAc,CAACjF,SAAS,EAAE7G,OAAO,CAACtE,KAAK,EAAE,MAAM,EAAEd,IAAI,EAAEoF,OAAO,CAAC;QACjE,CAAC,MAAM,IAAIvC,IAAI,CAACmQ,SAAS,KAAKnR,SAAS,IAAI,CAACiQ,6BAA6B,EAAE;UACzEA,6BAA6B,GAAG,IAAI,CAAC,CAAC;;UAEtC,IAAImB,KAAK,GAAGnQ,gBAAgB,CAACD,IAAI,CAAC;UAElCnE,KAAK,CAAC,qGAAqG,EAAEuU,KAAK,IAAI,SAAS,CAAC;QAClI;QAEA,IAAI,OAAOpQ,IAAI,CAACqQ,eAAe,KAAK,UAAU,IAAI,CAACrQ,IAAI,CAACqQ,eAAe,CAACC,oBAAoB,EAAE;UAC5FzU,KAAK,CAAC,4DAA4D,GAAG,kEAAkE,CAAC;QAC1I;MACF;IACF;IACA;AACA;AACA;AACA;;IAGA,SAAS0U,qBAAqBA,CAACC,QAAQ,EAAE;MACvC;QACE,IAAIpK,IAAI,GAAGtI,MAAM,CAACsI,IAAI,CAACoK,QAAQ,CAACvS,KAAK,CAAC;QAEtC,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,IAAI,CAAC5K,MAAM,EAAE0H,CAAC,EAAE,EAAE;UACpC,IAAInC,GAAG,GAAGqF,IAAI,CAAClD,CAAC,CAAC;UAEjB,IAAInC,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,KAAK,EAAE;YACvCiO,+BAA+B,CAACwB,QAAQ,CAAC;YAEzC3U,KAAK,CAAC,kDAAkD,GAAG,0DAA0D,EAAEkF,GAAG,CAAC;YAE3HiO,+BAA+B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;QAEA,IAAIwB,QAAQ,CAACxP,GAAG,KAAK,IAAI,EAAE;UACzBgO,+BAA+B,CAACwB,QAAQ,CAAC;UAEzC3U,KAAK,CAAC,uDAAuD,CAAC;UAE9DmT,+BAA+B,CAAC,IAAI,CAAC;QACvC;MACF;IACF;IACA,SAASyB,2BAA2BA,CAACzQ,IAAI,EAAE/B,KAAK,EAAE6E,QAAQ,EAAE;MAC1D,IAAI4N,SAAS,GAAG/G,kBAAkB,CAAC3J,IAAI,CAAC,CAAC,CAAC;MAC1C;;MAEA,IAAI,CAAC0Q,SAAS,EAAE;QACd,IAAI7R,IAAI,GAAG,EAAE;QAEb,IAAImB,IAAI,KAAKhB,SAAS,IAAI,OAAOgB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAIlC,MAAM,CAACsI,IAAI,CAACpG,IAAI,CAAC,CAACxE,MAAM,KAAK,CAAC,EAAE;UACrGqD,IAAI,IAAI,4DAA4D,GAAG,wEAAwE;QACjJ;QAEA,IAAI8R,UAAU,GAAGrB,kCAAkC,CAACrR,KAAK,CAAC;QAE1D,IAAI0S,UAAU,EAAE;UACd9R,IAAI,IAAI8R,UAAU;QACpB,CAAC,MAAM;UACL9R,IAAI,IAAIqQ,2BAA2B,CAAC,CAAC;QACvC;QAEA,IAAI0B,UAAU;QAEd,IAAI5Q,IAAI,KAAK,IAAI,EAAE;UACjB4Q,UAAU,GAAG,MAAM;QACrB,CAAC,MAAM,IAAIlV,KAAK,CAAC0J,OAAO,CAACpF,IAAI,CAAC,EAAE;UAC9B4Q,UAAU,GAAG,OAAO;QACtB,CAAC,MAAM,IAAI5Q,IAAI,KAAKhB,SAAS,IAAIgB,IAAI,CAACG,QAAQ,KAAK5H,kBAAkB,EAAE;UACrEqY,UAAU,GAAG,GAAG,IAAI3Q,gBAAgB,CAACD,IAAI,CAACA,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;UACrEnB,IAAI,GAAG,oEAAoE;QAC7E,CAAC,MAAM;UACL+R,UAAU,GAAG,OAAO5Q,IAAI;QAC1B;QAEA;UACEnE,KAAK,CAAC,iEAAiE,GAAG,0DAA0D,GAAG,4BAA4B,EAAE+U,UAAU,EAAE/R,IAAI,CAAC;QACxL;MACF;MAEA,IAAI0D,OAAO,GAAGM,aAAa,CAACrG,KAAK,CAAC,IAAI,EAAEjB,SAAS,CAAC,CAAC,CAAC;MACpD;;MAEA,IAAIgH,OAAO,IAAI,IAAI,EAAE;QACnB,OAAOA,OAAO;MAChB,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAImO,SAAS,EAAE;QACb,KAAK,IAAIxN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3H,SAAS,CAACC,MAAM,EAAE0H,CAAC,EAAE,EAAE;UACzC8M,iBAAiB,CAACzU,SAAS,CAAC2H,CAAC,CAAC,EAAElD,IAAI,CAAC;QACvC;MACF;MAEA,IAAIA,IAAI,KAAKvH,OAAO,CAACC,QAAQ,EAAE;QAC7B6X,qBAAqB,CAAChO,OAAO,CAAC;MAChC,CAAC,MAAM;QACL2N,iBAAiB,CAAC3N,OAAO,CAAC;MAC5B;MAEA,OAAOA,OAAO;IAChB;IACA,IAAIsO,mCAAmC,GAAG,KAAK;IAC/C,SAASC,2BAA2BA,CAAC9Q,IAAI,EAAE;MACzC,IAAI+Q,gBAAgB,GAAGN,2BAA2B,CAAC/B,IAAI,CAAC,IAAI,EAAE1O,IAAI,CAAC;MACnE+Q,gBAAgB,CAAC/Q,IAAI,GAAGA,IAAI;MAE5B;QACE,IAAI,CAAC6Q,mCAAmC,EAAE;UACxCA,mCAAmC,GAAG,IAAI;UAE1CzV,IAAI,CAAC,6DAA6D,GAAG,6CAA6C,GAAG,gDAAgD,CAAC;QACxK,CAAC,CAAC;;QAGF0C,MAAM,CAACgB,cAAc,CAACiS,gBAAgB,EAAE,MAAM,EAAE;UAC9CrO,UAAU,EAAE,KAAK;UACjB3D,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf3D,IAAI,CAAC,wDAAwD,GAAG,qCAAqC,CAAC;YAEtG0C,MAAM,CAACgB,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;cAClC8D,KAAK,EAAE5C;YACT,CAAC,CAAC;YACF,OAAOA,IAAI;UACb;QACF,CAAC,CAAC;MACJ;MAEA,OAAO+Q,gBAAgB;IACzB;IACA,SAASC,0BAA0BA,CAACzO,OAAO,EAAEtE,KAAK,EAAE6E,QAAQ,EAAE;MAC5D,IAAIS,UAAU,GAAGG,YAAY,CAAClH,KAAK,CAAC,IAAI,EAAEjB,SAAS,CAAC;MAEpD,KAAK,IAAI2H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3H,SAAS,CAACC,MAAM,EAAE0H,CAAC,EAAE,EAAE;QACzC8M,iBAAiB,CAACzU,SAAS,CAAC2H,CAAC,CAAC,EAAEK,UAAU,CAACvD,IAAI,CAAC;MAClD;MAEAkQ,iBAAiB,CAAC3M,UAAU,CAAC;MAC7B,OAAOA,UAAU;IACnB;IAEA;MAEE,IAAI;QACF,IAAI0N,YAAY,GAAGnT,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC;;QAEA,IAAI8O,GAAG,CAAC,CAAC,CAACoE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAIC,GAAG,CAAC,CAACD,YAAY,CAAC,CAAC;QACvB;MACF,CAAC,CAAC,OAAOE,CAAC,EAAE,CACZ;IACF;IAEA,IAAIC,eAAe,GAAIX,2BAA2B;IAClD,IAAIY,cAAc,GAAIL,0BAA0B;IAChD,IAAIM,aAAa,GAAIR,2BAA2B;IAChD,IAAIS,QAAQ,GAAG;MACbpV,GAAG,EAAEmK,WAAW;MAChBkL,OAAO,EAAE5K,eAAe;MACxBH,KAAK,EAAEC,aAAa;MACpBK,OAAO,EAAEA,OAAO;MAChB0K,IAAI,EAAEzK;IACR,CAAC;IAEDvO,OAAO,CAAC8Y,QAAQ,GAAGA,QAAQ;IAC3B9Y,OAAO,CAACuF,SAAS,GAAGA,SAAS;IAC7BvF,OAAO,CAAC2G,aAAa,GAAGA,aAAa;IACrC3G,OAAO,CAACiZ,kDAAkD,GAAGxW,oBAAoB;IACjFzC,OAAO,CAACiL,YAAY,GAAG2N,cAAc;IACrC5Y,OAAO,CAACwO,aAAa,GAAGA,aAAa;IACrCxO,OAAO,CAACoK,aAAa,GAAGuO,eAAe;IACvC3Y,OAAO,CAAC6Y,aAAa,GAAGA,aAAa;IACrC7Y,OAAO,CAAC8G,SAAS,GAAGA,SAAS;IAC7B9G,OAAO,CAAC8Q,UAAU,GAAGA,UAAU;IAC/B9Q,OAAO,CAACkL,cAAc,GAAGA,cAAc;IACvClL,OAAO,CAACyQ,IAAI,GAAGA,IAAI;IACnBzQ,OAAO,CAACmR,IAAI,GAAGA,IAAI;IACnBnR,OAAO,CAACsS,WAAW,GAAGA,WAAW;IACjCtS,OAAO,CAACuR,UAAU,GAAGA,UAAU;IAC/BvR,OAAO,CAACyS,aAAa,GAAGA,aAAa;IACrCzS,OAAO,CAACkS,SAAS,GAAGA,SAAS;IAC7BlS,OAAO,CAACwS,mBAAmB,GAAGA,mBAAmB;IACjDxS,OAAO,CAACqS,eAAe,GAAGA,eAAe;IACzCrS,OAAO,CAACuS,OAAO,GAAGA,OAAO;IACzBvS,OAAO,CAAC6R,UAAU,GAAGA,UAAU;IAC/B7R,OAAO,CAACgS,MAAM,GAAGA,MAAM;IACvBhS,OAAO,CAAC2R,QAAQ,GAAGA,QAAQ;IAC3B3R,OAAO,CAACkZ,OAAO,GAAGrZ,YAAY;EAC5B,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script"}