{"ast": null, "code": "import Animate from './Animate';\nimport { configBezier, configSpring } from './easing';\nimport { translateStyle } from './util';\nimport AnimateGroup from './AnimateGroup';\nexport { configSpring, configBezier, AnimateGroup, translateStyle };\nexport default Animate;", "map": {"version": 3, "names": ["Animate", "config<PERSON><PERSON><PERSON>", "configS<PERSON>ring", "translateStyle", "AnimateGroup"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-smooth/es6/index.js"], "sourcesContent": ["import Animate from './Animate';\nimport { configBezier, configSpring } from './easing';\nimport { translateStyle } from './util';\nimport AnimateGroup from './AnimateGroup';\nexport { configSpring, configBezier, AnimateGroup, translateStyle };\nexport default Animate;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,SAASC,YAAY,EAAEC,YAAY,QAAQ,UAAU;AACrD,SAASC,cAAc,QAAQ,QAAQ;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASF,YAAY,EAAED,YAAY,EAAEG,YAAY,EAAED,cAAc;AACjE,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}