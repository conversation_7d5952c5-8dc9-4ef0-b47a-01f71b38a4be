{"ast": null, "code": "import _every from \"lodash/every\";\nimport _mapValues from \"lodash/mapValues\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport { getTicksOfScale, parseScale, checkDomainOfScale, getBandSizeOfAxis } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nimport { getPercentValue } from './DataUtils';\nimport { Bar } from '../cartesian/Bar';\n\n/**\n * Calculate the scale function, position, width, height of axes\n * @param  {Object} props     Latest props\n * @param  {Object} axisMap   The configuration of axes\n * @param  {Object} offset    The offset of main part in the svg element\n * @param  {String} axisType  The type of axes, x-axis or y-axis\n * @param  {String} chartName The name of chart\n * @return {Object} Configuration\n */\nexport var formatAxisMap = function formatAxisMap(props, axisMap, offset, axisType, chartName) {\n  var width = props.width,\n    height = props.height,\n    layout = props.layout,\n    children = props.children;\n  var ids = Object.keys(axisMap);\n  var steps = {\n    left: offset.left,\n    leftMirror: offset.left,\n    right: width - offset.right,\n    rightMirror: width - offset.right,\n    top: offset.top,\n    topMirror: offset.top,\n    bottom: height - offset.bottom,\n    bottomMirror: height - offset.bottom\n  };\n  var hasBar = !!findChildByType(children, Bar);\n  return ids.reduce(function (result, id) {\n    var axis = axisMap[id];\n    var orientation = axis.orientation,\n      domain = axis.domain,\n      _axis$padding = axis.padding,\n      padding = _axis$padding === void 0 ? {} : _axis$padding,\n      mirror = axis.mirror,\n      reversed = axis.reversed;\n    var offsetKey = \"\".concat(orientation).concat(mirror ? 'Mirror' : '');\n    var calculatedPadding, range, x, y, needSpace;\n    if (axis.type === 'number' && (axis.padding === 'gap' || axis.padding === 'no-gap')) {\n      var diff = domain[1] - domain[0];\n      var smallestDistanceBetweenValues = Infinity;\n      var sortedValues = axis.categoricalDomain.sort();\n      sortedValues.forEach(function (value, index) {\n        if (index > 0) {\n          smallestDistanceBetweenValues = Math.min((value || 0) - (sortedValues[index - 1] || 0), smallestDistanceBetweenValues);\n        }\n      });\n      var smallestDistanceInPercent = smallestDistanceBetweenValues / diff;\n      var rangeWidth = axis.layout === 'vertical' ? offset.height : offset.width;\n      if (axis.padding === 'gap') {\n        calculatedPadding = smallestDistanceInPercent * rangeWidth / 2;\n      }\n      if (axis.padding === 'no-gap') {\n        var gap = getPercentValue(props.barCategoryGap, smallestDistanceInPercent * rangeWidth);\n        var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n        calculatedPadding = halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n      }\n    }\n    if (axisType === 'xAxis') {\n      range = [offset.left + (padding.left || 0) + (calculatedPadding || 0), offset.left + offset.width - (padding.right || 0) - (calculatedPadding || 0)];\n    } else if (axisType === 'yAxis') {\n      range = layout === 'horizontal' ? [offset.top + offset.height - (padding.bottom || 0), offset.top + (padding.top || 0)] : [offset.top + (padding.top || 0) + (calculatedPadding || 0), offset.top + offset.height - (padding.bottom || 0) - (calculatedPadding || 0)];\n    } else {\n      range = axis.range;\n    }\n    if (reversed) {\n      range = [range[1], range[0]];\n    }\n    var _parseScale = parseScale(axis, chartName, hasBar),\n      scale = _parseScale.scale,\n      realScaleType = _parseScale.realScaleType;\n    scale.domain(domain).range(range);\n    checkDomainOfScale(scale);\n    var ticks = getTicksOfScale(scale, _objectSpread(_objectSpread({}, axis), {}, {\n      realScaleType: realScaleType\n    }));\n    if (axisType === 'xAxis') {\n      needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n      x = offset.left;\n      y = steps[offsetKey] - needSpace * axis.height;\n    } else if (axisType === 'yAxis') {\n      needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n      x = steps[offsetKey] - needSpace * axis.width;\n      y = offset.top;\n    }\n    var finalAxis = _objectSpread(_objectSpread(_objectSpread({}, axis), ticks), {}, {\n      realScaleType: realScaleType,\n      x: x,\n      y: y,\n      scale: scale,\n      width: axisType === 'xAxis' ? offset.width : axis.width,\n      height: axisType === 'yAxis' ? offset.height : axis.height\n    });\n    finalAxis.bandSize = getBandSizeOfAxis(finalAxis, ticks);\n    if (!axis.hide && axisType === 'xAxis') {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.height;\n    } else if (!axis.hide) {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.width;\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, id, finalAxis));\n  }, {});\n};\nexport var rectWithPoints = function rectWithPoints(_ref, _ref2) {\n  var x1 = _ref.x,\n    y1 = _ref.y;\n  var x2 = _ref2.x,\n    y2 = _ref2.y;\n  return {\n    x: Math.min(x1, x2),\n    y: Math.min(y1, y2),\n    width: Math.abs(x2 - x1),\n    height: Math.abs(y2 - y1)\n  };\n};\n\n/**\n * Compute the x, y, width, and height of a box from two reference points.\n * @param  {Object} coords     x1, x2, y1, and y2\n * @return {Object} object\n */\nexport var rectWithCoords = function rectWithCoords(_ref3) {\n  var x1 = _ref3.x1,\n    y1 = _ref3.y1,\n    x2 = _ref3.x2,\n    y2 = _ref3.y2;\n  return rectWithPoints({\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  });\n};\nexport var ScaleHelper = /*#__PURE__*/function () {\n  function ScaleHelper(scale) {\n    _classCallCheck(this, ScaleHelper);\n    this.scale = scale;\n  }\n  _createClass(ScaleHelper, [{\n    key: \"domain\",\n    get: function get() {\n      return this.scale.domain;\n    }\n  }, {\n    key: \"range\",\n    get: function get() {\n      return this.scale.range;\n    }\n  }, {\n    key: \"rangeMin\",\n    get: function get() {\n      return this.range()[0];\n    }\n  }, {\n    key: \"rangeMax\",\n    get: function get() {\n      return this.range()[1];\n    }\n  }, {\n    key: \"bandwidth\",\n    get: function get() {\n      return this.scale.bandwidth;\n    }\n  }, {\n    key: \"apply\",\n    value: function apply(value) {\n      var _ref4 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref4.bandAware,\n        position = _ref4.position;\n      if (value === undefined) {\n        return undefined;\n      }\n      if (position) {\n        switch (position) {\n          case 'start':\n            {\n              return this.scale(value);\n            }\n          case 'middle':\n            {\n              var offset = this.bandwidth ? this.bandwidth() / 2 : 0;\n              return this.scale(value) + offset;\n            }\n          case 'end':\n            {\n              var _offset = this.bandwidth ? this.bandwidth() : 0;\n              return this.scale(value) + _offset;\n            }\n          default:\n            {\n              return this.scale(value);\n            }\n        }\n      }\n      if (bandAware) {\n        var _offset2 = this.bandwidth ? this.bandwidth() / 2 : 0;\n        return this.scale(value) + _offset2;\n      }\n      return this.scale(value);\n    }\n  }, {\n    key: \"isInRange\",\n    value: function isInRange(value) {\n      var range = this.range();\n      var first = range[0];\n      var last = range[range.length - 1];\n      return first <= last ? value >= first && value <= last : value >= last && value <= first;\n    }\n  }], [{\n    key: \"create\",\n    value: function create(obj) {\n      return new ScaleHelper(obj);\n    }\n  }]);\n  return ScaleHelper;\n}();\n_defineProperty(ScaleHelper, \"EPS\", 1e-4);\nexport var createLabeledScales = function createLabeledScales(options) {\n  var scales = Object.keys(options).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, ScaleHelper.create(options[key])));\n  }, {});\n  return _objectSpread(_objectSpread({}, scales), {}, {\n    apply: function apply(coord) {\n      var _ref5 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref5.bandAware,\n        position = _ref5.position;\n      return _mapValues(coord, function (value, label) {\n        return scales[label].apply(value, {\n          bandAware: bandAware,\n          position: position\n        });\n      });\n    },\n    isInRange: function isInRange(coord) {\n      return _every(coord, function (value, label) {\n        return scales[label].isInRange(value);\n      });\n    }\n  });\n};", "map": {"version": 3, "names": ["_every", "_mapValues", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread", "arguments", "source", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "value", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "call", "Number", "getTicksOfScale", "parseScale", "checkDomainOfScale", "getBandSizeOfAxis", "findChildByType", "getPercentValue", "Bar", "formatAxisMap", "axisMap", "offset", "axisType", "chartName", "width", "height", "layout", "children", "ids", "steps", "left", "leftMirror", "right", "rightMirror", "top", "topMirror", "bottom", "bottomMirror", "<PERSON><PERSON><PERSON>", "reduce", "result", "id", "axis", "orientation", "domain", "_axis$padding", "padding", "mirror", "reversed", "offsetKey", "concat", "calculatedPadding", "range", "x", "y", "needSpace", "type", "diff", "smallestDistanceBetweenValues", "Infinity", "sortedValues", "categoricalDomain", "sort", "index", "Math", "min", "smallestDistanceInPercent", "rangeWidth", "gap", "barCategoryGap", "halfBand", "_parseScale", "scale", "realScaleType", "ticks", "finalAxis", "bandSize", "hide", "rectWithPoints", "_ref", "_ref2", "x1", "y1", "x2", "y2", "abs", "rectWithCoords", "_ref3", "ScaleHelper", "get", "bandwidth", "_ref4", "bandAware", "position", "_offset", "_offset2", "isInRange", "first", "last", "create", "createLabeledScales", "options", "scales", "coord", "_ref5", "label"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/util/CartesianUtils.js"], "sourcesContent": ["import _every from \"lodash/every\";\nimport _mapValues from \"lodash/mapValues\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { getTicksOfScale, parseScale, checkDomainOfScale, getBandSizeOfAxis } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nimport { getPercentValue } from './DataUtils';\nimport { Bar } from '../cartesian/Bar';\n\n/**\n * Calculate the scale function, position, width, height of axes\n * @param  {Object} props     Latest props\n * @param  {Object} axisMap   The configuration of axes\n * @param  {Object} offset    The offset of main part in the svg element\n * @param  {String} axisType  The type of axes, x-axis or y-axis\n * @param  {String} chartName The name of chart\n * @return {Object} Configuration\n */\nexport var formatAxisMap = function formatAxisMap(props, axisMap, offset, axisType, chartName) {\n  var width = props.width,\n    height = props.height,\n    layout = props.layout,\n    children = props.children;\n  var ids = Object.keys(axisMap);\n  var steps = {\n    left: offset.left,\n    leftMirror: offset.left,\n    right: width - offset.right,\n    rightMirror: width - offset.right,\n    top: offset.top,\n    topMirror: offset.top,\n    bottom: height - offset.bottom,\n    bottomMirror: height - offset.bottom\n  };\n  var hasBar = !!findChildByType(children, Bar);\n  return ids.reduce(function (result, id) {\n    var axis = axisMap[id];\n    var orientation = axis.orientation,\n      domain = axis.domain,\n      _axis$padding = axis.padding,\n      padding = _axis$padding === void 0 ? {} : _axis$padding,\n      mirror = axis.mirror,\n      reversed = axis.reversed;\n    var offsetKey = \"\".concat(orientation).concat(mirror ? 'Mirror' : '');\n    var calculatedPadding, range, x, y, needSpace;\n    if (axis.type === 'number' && (axis.padding === 'gap' || axis.padding === 'no-gap')) {\n      var diff = domain[1] - domain[0];\n      var smallestDistanceBetweenValues = Infinity;\n      var sortedValues = axis.categoricalDomain.sort();\n      sortedValues.forEach(function (value, index) {\n        if (index > 0) {\n          smallestDistanceBetweenValues = Math.min((value || 0) - (sortedValues[index - 1] || 0), smallestDistanceBetweenValues);\n        }\n      });\n      var smallestDistanceInPercent = smallestDistanceBetweenValues / diff;\n      var rangeWidth = axis.layout === 'vertical' ? offset.height : offset.width;\n      if (axis.padding === 'gap') {\n        calculatedPadding = smallestDistanceInPercent * rangeWidth / 2;\n      }\n      if (axis.padding === 'no-gap') {\n        var gap = getPercentValue(props.barCategoryGap, smallestDistanceInPercent * rangeWidth);\n        var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n        calculatedPadding = halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n      }\n    }\n    if (axisType === 'xAxis') {\n      range = [offset.left + (padding.left || 0) + (calculatedPadding || 0), offset.left + offset.width - (padding.right || 0) - (calculatedPadding || 0)];\n    } else if (axisType === 'yAxis') {\n      range = layout === 'horizontal' ? [offset.top + offset.height - (padding.bottom || 0), offset.top + (padding.top || 0)] : [offset.top + (padding.top || 0) + (calculatedPadding || 0), offset.top + offset.height - (padding.bottom || 0) - (calculatedPadding || 0)];\n    } else {\n      range = axis.range;\n    }\n    if (reversed) {\n      range = [range[1], range[0]];\n    }\n    var _parseScale = parseScale(axis, chartName, hasBar),\n      scale = _parseScale.scale,\n      realScaleType = _parseScale.realScaleType;\n    scale.domain(domain).range(range);\n    checkDomainOfScale(scale);\n    var ticks = getTicksOfScale(scale, _objectSpread(_objectSpread({}, axis), {}, {\n      realScaleType: realScaleType\n    }));\n    if (axisType === 'xAxis') {\n      needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n      x = offset.left;\n      y = steps[offsetKey] - needSpace * axis.height;\n    } else if (axisType === 'yAxis') {\n      needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n      x = steps[offsetKey] - needSpace * axis.width;\n      y = offset.top;\n    }\n    var finalAxis = _objectSpread(_objectSpread(_objectSpread({}, axis), ticks), {}, {\n      realScaleType: realScaleType,\n      x: x,\n      y: y,\n      scale: scale,\n      width: axisType === 'xAxis' ? offset.width : axis.width,\n      height: axisType === 'yAxis' ? offset.height : axis.height\n    });\n    finalAxis.bandSize = getBandSizeOfAxis(finalAxis, ticks);\n    if (!axis.hide && axisType === 'xAxis') {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.height;\n    } else if (!axis.hide) {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.width;\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, id, finalAxis));\n  }, {});\n};\nexport var rectWithPoints = function rectWithPoints(_ref, _ref2) {\n  var x1 = _ref.x,\n    y1 = _ref.y;\n  var x2 = _ref2.x,\n    y2 = _ref2.y;\n  return {\n    x: Math.min(x1, x2),\n    y: Math.min(y1, y2),\n    width: Math.abs(x2 - x1),\n    height: Math.abs(y2 - y1)\n  };\n};\n\n/**\n * Compute the x, y, width, and height of a box from two reference points.\n * @param  {Object} coords     x1, x2, y1, and y2\n * @return {Object} object\n */\nexport var rectWithCoords = function rectWithCoords(_ref3) {\n  var x1 = _ref3.x1,\n    y1 = _ref3.y1,\n    x2 = _ref3.x2,\n    y2 = _ref3.y2;\n  return rectWithPoints({\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  });\n};\nexport var ScaleHelper = /*#__PURE__*/function () {\n  function ScaleHelper(scale) {\n    _classCallCheck(this, ScaleHelper);\n    this.scale = scale;\n  }\n  _createClass(ScaleHelper, [{\n    key: \"domain\",\n    get: function get() {\n      return this.scale.domain;\n    }\n  }, {\n    key: \"range\",\n    get: function get() {\n      return this.scale.range;\n    }\n  }, {\n    key: \"rangeMin\",\n    get: function get() {\n      return this.range()[0];\n    }\n  }, {\n    key: \"rangeMax\",\n    get: function get() {\n      return this.range()[1];\n    }\n  }, {\n    key: \"bandwidth\",\n    get: function get() {\n      return this.scale.bandwidth;\n    }\n  }, {\n    key: \"apply\",\n    value: function apply(value) {\n      var _ref4 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref4.bandAware,\n        position = _ref4.position;\n      if (value === undefined) {\n        return undefined;\n      }\n      if (position) {\n        switch (position) {\n          case 'start':\n            {\n              return this.scale(value);\n            }\n          case 'middle':\n            {\n              var offset = this.bandwidth ? this.bandwidth() / 2 : 0;\n              return this.scale(value) + offset;\n            }\n          case 'end':\n            {\n              var _offset = this.bandwidth ? this.bandwidth() : 0;\n              return this.scale(value) + _offset;\n            }\n          default:\n            {\n              return this.scale(value);\n            }\n        }\n      }\n      if (bandAware) {\n        var _offset2 = this.bandwidth ? this.bandwidth() / 2 : 0;\n        return this.scale(value) + _offset2;\n      }\n      return this.scale(value);\n    }\n  }, {\n    key: \"isInRange\",\n    value: function isInRange(value) {\n      var range = this.range();\n      var first = range[0];\n      var last = range[range.length - 1];\n      return first <= last ? value >= first && value <= last : value >= last && value <= first;\n    }\n  }], [{\n    key: \"create\",\n    value: function create(obj) {\n      return new ScaleHelper(obj);\n    }\n  }]);\n  return ScaleHelper;\n}();\n_defineProperty(ScaleHelper, \"EPS\", 1e-4);\nexport var createLabeledScales = function createLabeledScales(options) {\n  var scales = Object.keys(options).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, ScaleHelper.create(options[key])));\n  }, {});\n  return _objectSpread(_objectSpread({}, scales), {}, {\n    apply: function apply(coord) {\n      var _ref5 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref5.bandAware,\n        position = _ref5.position;\n      return _mapValues(coord, function (value, label) {\n        return scales[label].apply(value, {\n          bandAware: bandAware,\n          position: position\n        });\n      });\n    },\n    isInRange: function isInRange(coord) {\n      return _every(coord, function (value, label) {\n        return scales[label].isInRange(value);\n      });\n    }\n  });\n};"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEmB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASkB,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGV,MAAM,CAACU,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIR,MAAM,CAACW,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGZ,MAAM,CAACW,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOd,MAAM,CAACe,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACjB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEa,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAAC1B,MAAM,EAAE;EAAE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,SAAS,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAI0B,MAAM,GAAG,IAAI,IAAID,SAAS,CAACzB,CAAC,CAAC,GAAGyB,SAAS,CAACzB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGa,OAAO,CAACP,MAAM,CAACoB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUlB,GAAG,EAAE;MAAEmB,eAAe,CAAC9B,MAAM,EAAEW,GAAG,EAAEiB,MAAM,CAACjB,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGH,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAAChC,MAAM,EAAEQ,MAAM,CAACuB,yBAAyB,CAACH,MAAM,CAAC,CAAC,GAAGb,OAAO,CAACP,MAAM,CAACoB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUlB,GAAG,EAAE;MAAEH,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEW,GAAG,EAAEH,MAAM,CAACe,wBAAwB,CAACK,MAAM,EAAEjB,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOX,MAAM;AAAE;AACzf,SAAS8B,eAAeA,CAACxC,GAAG,EAAEqB,GAAG,EAAEsB,KAAK,EAAE;EAAEtB,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIrB,GAAG,EAAE;IAAEkB,MAAM,CAACC,cAAc,CAACnB,GAAG,EAAEqB,GAAG,EAAE;MAAEsB,KAAK,EAAEA,KAAK;MAAE5B,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEjB,GAAG,CAACqB,GAAG,CAAC,GAAGsB,KAAK;EAAE;EAAE,OAAO3C,GAAG;AAAE;AAC3O,SAASoB,cAAcA,CAACwB,GAAG,EAAE;EAAE,IAAIvB,GAAG,GAAGwB,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO7C,OAAO,CAACsB,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGyB,MAAM,CAACzB,GAAG,CAAC;AAAE;AAC5H,SAASwB,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIjD,OAAO,CAACgD,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC9C,MAAM,CAACiD,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACI,IAAI,CAACN,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIjD,OAAO,CAACqD,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAI5C,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACwC,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGQ,MAAM,EAAEP,KAAK,CAAC;AAAE;AAC5X,SAASQ,eAAe,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,cAAc;AACjG,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,GAAG,QAAQ,kBAAkB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACnD,KAAK,EAAEoD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC7F,IAAIC,KAAK,GAAGxD,KAAK,CAACwD,KAAK;IACrBC,MAAM,GAAGzD,KAAK,CAACyD,MAAM;IACrBC,MAAM,GAAG1D,KAAK,CAAC0D,MAAM;IACrBC,QAAQ,GAAG3D,KAAK,CAAC2D,QAAQ;EAC3B,IAAIC,GAAG,GAAGrD,MAAM,CAACU,IAAI,CAACmC,OAAO,CAAC;EAC9B,IAAIS,KAAK,GAAG;IACVC,IAAI,EAAET,MAAM,CAACS,IAAI;IACjBC,UAAU,EAAEV,MAAM,CAACS,IAAI;IACvBE,KAAK,EAAER,KAAK,GAAGH,MAAM,CAACW,KAAK;IAC3BC,WAAW,EAAET,KAAK,GAAGH,MAAM,CAACW,KAAK;IACjCE,GAAG,EAAEb,MAAM,CAACa,GAAG;IACfC,SAAS,EAAEd,MAAM,CAACa,GAAG;IACrBE,MAAM,EAAEX,MAAM,GAAGJ,MAAM,CAACe,MAAM;IAC9BC,YAAY,EAAEZ,MAAM,GAAGJ,MAAM,CAACe;EAChC,CAAC;EACD,IAAIE,MAAM,GAAG,CAAC,CAACtB,eAAe,CAACW,QAAQ,EAAET,GAAG,CAAC;EAC7C,OAAOU,GAAG,CAACW,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACtC,IAAIC,IAAI,GAAGtB,OAAO,CAACqB,EAAE,CAAC;IACtB,IAAIE,WAAW,GAAGD,IAAI,CAACC,WAAW;MAChCC,MAAM,GAAGF,IAAI,CAACE,MAAM;MACpBC,aAAa,GAAGH,IAAI,CAACI,OAAO;MAC5BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;MACvDE,MAAM,GAAGL,IAAI,CAACK,MAAM;MACpBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IAC1B,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACP,WAAW,CAAC,CAACO,MAAM,CAACH,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC;IACrE,IAAII,iBAAiB,EAAEC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,SAAS;IAC7C,IAAIb,IAAI,CAACc,IAAI,KAAK,QAAQ,KAAKd,IAAI,CAACI,OAAO,KAAK,KAAK,IAAIJ,IAAI,CAACI,OAAO,KAAK,QAAQ,CAAC,EAAE;MACnF,IAAIW,IAAI,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;MAChC,IAAIc,6BAA6B,GAAGC,QAAQ;MAC5C,IAAIC,YAAY,GAAGlB,IAAI,CAACmB,iBAAiB,CAACC,IAAI,CAAC,CAAC;MAChDF,YAAY,CAAChE,OAAO,CAAC,UAAUI,KAAK,EAAE+D,KAAK,EAAE;QAC3C,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbL,6BAA6B,GAAGM,IAAI,CAACC,GAAG,CAAC,CAACjE,KAAK,IAAI,CAAC,KAAK4D,YAAY,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEL,6BAA6B,CAAC;QACxH;MACF,CAAC,CAAC;MACF,IAAIQ,yBAAyB,GAAGR,6BAA6B,GAAGD,IAAI;MACpE,IAAIU,UAAU,GAAGzB,IAAI,CAAChB,MAAM,KAAK,UAAU,GAAGL,MAAM,CAACI,MAAM,GAAGJ,MAAM,CAACG,KAAK;MAC1E,IAAIkB,IAAI,CAACI,OAAO,KAAK,KAAK,EAAE;QAC1BK,iBAAiB,GAAGe,yBAAyB,GAAGC,UAAU,GAAG,CAAC;MAChE;MACA,IAAIzB,IAAI,CAACI,OAAO,KAAK,QAAQ,EAAE;QAC7B,IAAIsB,GAAG,GAAGnD,eAAe,CAACjD,KAAK,CAACqG,cAAc,EAAEH,yBAAyB,GAAGC,UAAU,CAAC;QACvF,IAAIG,QAAQ,GAAGJ,yBAAyB,GAAGC,UAAU,GAAG,CAAC;QACzDhB,iBAAiB,GAAGmB,QAAQ,GAAGF,GAAG,GAAG,CAACE,QAAQ,GAAGF,GAAG,IAAID,UAAU,GAAGC,GAAG;MAC1E;IACF;IACA,IAAI9C,QAAQ,KAAK,OAAO,EAAE;MACxB8B,KAAK,GAAG,CAAC/B,MAAM,CAACS,IAAI,IAAIgB,OAAO,CAAChB,IAAI,IAAI,CAAC,CAAC,IAAIqB,iBAAiB,IAAI,CAAC,CAAC,EAAE9B,MAAM,CAACS,IAAI,GAAGT,MAAM,CAACG,KAAK,IAAIsB,OAAO,CAACd,KAAK,IAAI,CAAC,CAAC,IAAImB,iBAAiB,IAAI,CAAC,CAAC,CAAC;IACtJ,CAAC,MAAM,IAAI7B,QAAQ,KAAK,OAAO,EAAE;MAC/B8B,KAAK,GAAG1B,MAAM,KAAK,YAAY,GAAG,CAACL,MAAM,CAACa,GAAG,GAAGb,MAAM,CAACI,MAAM,IAAIqB,OAAO,CAACV,MAAM,IAAI,CAAC,CAAC,EAAEf,MAAM,CAACa,GAAG,IAAIY,OAAO,CAACZ,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAACb,MAAM,CAACa,GAAG,IAAIY,OAAO,CAACZ,GAAG,IAAI,CAAC,CAAC,IAAIiB,iBAAiB,IAAI,CAAC,CAAC,EAAE9B,MAAM,CAACa,GAAG,GAAGb,MAAM,CAACI,MAAM,IAAIqB,OAAO,CAACV,MAAM,IAAI,CAAC,CAAC,IAAIe,iBAAiB,IAAI,CAAC,CAAC,CAAC;IACvQ,CAAC,MAAM;MACLC,KAAK,GAAGV,IAAI,CAACU,KAAK;IACpB;IACA,IAAIJ,QAAQ,EAAE;MACZI,KAAK,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,IAAImB,WAAW,GAAG1D,UAAU,CAAC6B,IAAI,EAAEnB,SAAS,EAAEe,MAAM,CAAC;MACnDkC,KAAK,GAAGD,WAAW,CAACC,KAAK;MACzBC,aAAa,GAAGF,WAAW,CAACE,aAAa;IAC3CD,KAAK,CAAC5B,MAAM,CAACA,MAAM,CAAC,CAACQ,KAAK,CAACA,KAAK,CAAC;IACjCtC,kBAAkB,CAAC0D,KAAK,CAAC;IACzB,IAAIE,KAAK,GAAG9D,eAAe,CAAC4D,KAAK,EAAE/E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5E+B,aAAa,EAAEA;IACjB,CAAC,CAAC,CAAC;IACH,IAAInD,QAAQ,KAAK,OAAO,EAAE;MACxBiC,SAAS,GAAGZ,WAAW,KAAK,KAAK,IAAI,CAACI,MAAM,IAAIJ,WAAW,KAAK,QAAQ,IAAII,MAAM;MAClFM,CAAC,GAAGhC,MAAM,CAACS,IAAI;MACfwB,CAAC,GAAGzB,KAAK,CAACoB,SAAS,CAAC,GAAGM,SAAS,GAAGb,IAAI,CAACjB,MAAM;IAChD,CAAC,MAAM,IAAIH,QAAQ,KAAK,OAAO,EAAE;MAC/BiC,SAAS,GAAGZ,WAAW,KAAK,MAAM,IAAI,CAACI,MAAM,IAAIJ,WAAW,KAAK,OAAO,IAAII,MAAM;MAClFM,CAAC,GAAGxB,KAAK,CAACoB,SAAS,CAAC,GAAGM,SAAS,GAAGb,IAAI,CAAClB,KAAK;MAC7C8B,CAAC,GAAGjC,MAAM,CAACa,GAAG;IAChB;IACA,IAAIyC,SAAS,GAAGlF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/ED,aAAa,EAAEA,aAAa;MAC5BpB,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJkB,KAAK,EAAEA,KAAK;MACZhD,KAAK,EAAEF,QAAQ,KAAK,OAAO,GAAGD,MAAM,CAACG,KAAK,GAAGkB,IAAI,CAAClB,KAAK;MACvDC,MAAM,EAAEH,QAAQ,KAAK,OAAO,GAAGD,MAAM,CAACI,MAAM,GAAGiB,IAAI,CAACjB;IACtD,CAAC,CAAC;IACFkD,SAAS,CAACC,QAAQ,GAAG7D,iBAAiB,CAAC4D,SAAS,EAAED,KAAK,CAAC;IACxD,IAAI,CAAChC,IAAI,CAACmC,IAAI,IAAIvD,QAAQ,KAAK,OAAO,EAAE;MACtCO,KAAK,CAACoB,SAAS,CAAC,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIoB,SAAS,CAAClD,MAAM;IAC7D,CAAC,MAAM,IAAI,CAACiB,IAAI,CAACmC,IAAI,EAAE;MACrBhD,KAAK,CAACoB,SAAS,CAAC,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIoB,SAAS,CAACnD,KAAK;IAC5D;IACA,OAAO/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE3C,eAAe,CAAC,CAAC,CAAC,EAAE4C,EAAE,EAAEkC,SAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD,OAAO,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC/D,IAAIC,EAAE,GAAGF,IAAI,CAAC1B,CAAC;IACb6B,EAAE,GAAGH,IAAI,CAACzB,CAAC;EACb,IAAI6B,EAAE,GAAGH,KAAK,CAAC3B,CAAC;IACd+B,EAAE,GAAGJ,KAAK,CAAC1B,CAAC;EACd,OAAO;IACLD,CAAC,EAAEW,IAAI,CAACC,GAAG,CAACgB,EAAE,EAAEE,EAAE,CAAC;IACnB7B,CAAC,EAAEU,IAAI,CAACC,GAAG,CAACiB,EAAE,EAAEE,EAAE,CAAC;IACnB5D,KAAK,EAAEwC,IAAI,CAACqB,GAAG,CAACF,EAAE,GAAGF,EAAE,CAAC;IACxBxD,MAAM,EAAEuC,IAAI,CAACqB,GAAG,CAACD,EAAE,GAAGF,EAAE;EAC1B,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIN,EAAE,GAAGM,KAAK,CAACN,EAAE;IACfC,EAAE,GAAGK,KAAK,CAACL,EAAE;IACbC,EAAE,GAAGI,KAAK,CAACJ,EAAE;IACbC,EAAE,GAAGG,KAAK,CAACH,EAAE;EACf,OAAON,cAAc,CAAC;IACpBzB,CAAC,EAAE4B,EAAE;IACL3B,CAAC,EAAE4B;EACL,CAAC,EAAE;IACD7B,CAAC,EAAE8B,EAAE;IACL7B,CAAC,EAAE8B;EACL,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAII,WAAW,GAAG,aAAa,YAAY;EAChD,SAASA,WAAWA,CAAChB,KAAK,EAAE;IAC1B9G,eAAe,CAAC,IAAI,EAAE8H,WAAW,CAAC;IAClC,IAAI,CAAChB,KAAK,GAAGA,KAAK;EACpB;EACA7F,YAAY,CAAC6G,WAAW,EAAE,CAAC;IACzB9G,GAAG,EAAE,QAAQ;IACb+G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAAC5B,MAAM;IAC1B;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,OAAO;IACZ+G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAACpB,KAAK;IACzB;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,UAAU;IACf+G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,UAAU;IACf+G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,WAAW;IAChB+G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAACkB,SAAS;IAC7B;EACF,CAAC,EAAE;IACDhH,GAAG,EAAE,OAAO;IACZsB,KAAK,EAAE,SAASR,KAAKA,CAACQ,KAAK,EAAE;MAC3B,IAAI2F,KAAK,GAAGjG,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKc,SAAS,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChFkG,SAAS,GAAGD,KAAK,CAACC,SAAS;QAC3BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;MAC3B,IAAI7F,KAAK,KAAKQ,SAAS,EAAE;QACvB,OAAOA,SAAS;MAClB;MACA,IAAIqF,QAAQ,EAAE;QACZ,QAAQA,QAAQ;UACd,KAAK,OAAO;YACV;cACE,OAAO,IAAI,CAACrB,KAAK,CAACxE,KAAK,CAAC;YAC1B;UACF,KAAK,QAAQ;YACX;cACE,IAAIqB,MAAM,GAAG,IAAI,CAACqE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;cACtD,OAAO,IAAI,CAAClB,KAAK,CAACxE,KAAK,CAAC,GAAGqB,MAAM;YACnC;UACF,KAAK,KAAK;YACR;cACE,IAAIyE,OAAO,GAAG,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC;cACnD,OAAO,IAAI,CAAClB,KAAK,CAACxE,KAAK,CAAC,GAAG8F,OAAO;YACpC;UACF;YACE;cACE,OAAO,IAAI,CAACtB,KAAK,CAACxE,KAAK,CAAC;YAC1B;QACJ;MACF;MACA,IAAI4F,SAAS,EAAE;QACb,IAAIG,QAAQ,GAAG,IAAI,CAACL,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACxD,OAAO,IAAI,CAAClB,KAAK,CAACxE,KAAK,CAAC,GAAG+F,QAAQ;MACrC;MACA,OAAO,IAAI,CAACvB,KAAK,CAACxE,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE;IACDtB,GAAG,EAAE,WAAW;IAChBsB,KAAK,EAAE,SAASgG,SAASA,CAAChG,KAAK,EAAE;MAC/B,IAAIoD,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MACxB,IAAI6C,KAAK,GAAG7C,KAAK,CAAC,CAAC,CAAC;MACpB,IAAI8C,IAAI,GAAG9C,KAAK,CAACA,KAAK,CAAClF,MAAM,GAAG,CAAC,CAAC;MAClC,OAAO+H,KAAK,IAAIC,IAAI,GAAGlG,KAAK,IAAIiG,KAAK,IAAIjG,KAAK,IAAIkG,IAAI,GAAGlG,KAAK,IAAIkG,IAAI,IAAIlG,KAAK,IAAIiG,KAAK;IAC1F;EACF,CAAC,CAAC,EAAE,CAAC;IACHvH,GAAG,EAAE,QAAQ;IACbsB,KAAK,EAAE,SAASmG,MAAMA,CAAC9I,GAAG,EAAE;MAC1B,OAAO,IAAImI,WAAW,CAACnI,GAAG,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;EACH,OAAOmI,WAAW;AACpB,CAAC,CAAC,CAAC;AACH3F,eAAe,CAAC2F,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;AACzC,OAAO,IAAIY,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAE;EACrE,IAAIC,MAAM,GAAG/H,MAAM,CAACU,IAAI,CAACoH,OAAO,CAAC,CAAC9D,MAAM,CAAC,UAAU9B,GAAG,EAAE/B,GAAG,EAAE;IAC3D,OAAOe,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEZ,eAAe,CAAC,CAAC,CAAC,EAAEnB,GAAG,EAAE8G,WAAW,CAACW,MAAM,CAACE,OAAO,CAAC3H,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9G,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOe,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IAClD9G,KAAK,EAAE,SAASA,KAAKA,CAAC+G,KAAK,EAAE;MAC3B,IAAIC,KAAK,GAAG9G,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKc,SAAS,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChFkG,SAAS,GAAGY,KAAK,CAACZ,SAAS;QAC3BC,QAAQ,GAAGW,KAAK,CAACX,QAAQ;MAC3B,OAAO1I,UAAU,CAACoJ,KAAK,EAAE,UAAUvG,KAAK,EAAEyG,KAAK,EAAE;QAC/C,OAAOH,MAAM,CAACG,KAAK,CAAC,CAACjH,KAAK,CAACQ,KAAK,EAAE;UAChC4F,SAAS,EAAEA,SAAS;UACpBC,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDG,SAAS,EAAE,SAASA,SAASA,CAACO,KAAK,EAAE;MACnC,OAAOrJ,MAAM,CAACqJ,KAAK,EAAE,UAAUvG,KAAK,EAAEyG,KAAK,EAAE;QAC3C,OAAOH,MAAM,CAACG,KAAK,CAAC,CAACT,SAAS,CAAChG,KAAK,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}