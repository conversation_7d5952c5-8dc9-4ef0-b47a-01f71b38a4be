{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport RadiusBottomrightOutlinedSvg from \"@ant-design/icons-svg/es/asn/RadiusBottomrightOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar RadiusBottomrightOutlined = function RadiusBottomrightOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: RadiusBottomrightOutlinedSvg\n  }));\n};\nRadiusBottomrightOutlined.displayName = 'RadiusBottomrightOutlined';\nexport default /*#__PURE__*/React.forwardRef(RadiusBottomrightOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "RadiusBottomrightOutlinedSvg", "AntdIcon", "RadiusBott<PERSON>rightOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/RadiusBottomrightOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport RadiusBottomrightOutlinedSvg from \"@ant-design/icons-svg/es/asn/RadiusBottomrightOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar RadiusBottomrightOutlined = function RadiusBottomrightOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: RadiusBottomrightOutlinedSvg\n  }));\n};\nRadiusBottomrightOutlined.displayName = 'RadiusBottomrightOutlined';\nexport default /*#__PURE__*/React.forwardRef(RadiusBottomrightOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,4BAA4B,MAAM,wDAAwD;AACjG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7E,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,yBAAyB,CAACK,WAAW,GAAG,2BAA2B;AACnE,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}