{"ast": null, "code": "function stringifyNode(node, custom) {\n  var type = node.type;\n  var value = node.value;\n  var buf;\n  var customResult;\n  if (custom && (customResult = custom(node)) !== undefined) {\n    return customResult;\n  } else if (type === \"word\" || type === \"space\") {\n    return value;\n  } else if (type === \"string\") {\n    buf = node.quote || \"\";\n    return buf + value + (node.unclosed ? \"\" : buf);\n  } else if (type === \"comment\") {\n    return \"/*\" + value + (node.unclosed ? \"\" : \"*/\");\n  } else if (type === \"div\") {\n    return (node.before || \"\") + value + (node.after || \"\");\n  } else if (Array.isArray(node.nodes)) {\n    buf = stringify(node.nodes);\n    if (type !== \"function\") {\n      return buf;\n    }\n    return value + \"(\" + (node.before || \"\") + buf + (node.after || \"\") + (node.unclosed ? \"\" : \")\");\n  }\n  return value;\n}\nfunction stringify(nodes, custom) {\n  var result, i;\n  if (Array.isArray(nodes)) {\n    result = \"\";\n    for (i = nodes.length - 1; ~i; i -= 1) {\n      result = stringifyNode(nodes[i], custom) + result;\n    }\n    return result;\n  }\n  return stringifyNode(nodes, custom);\n}\nmodule.exports = stringify;", "map": {"version": 3, "names": ["stringifyNode", "node", "custom", "type", "value", "buf", "customResult", "undefined", "quote", "unclosed", "before", "after", "Array", "isArray", "nodes", "stringify", "result", "i", "length", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/node_modules/postcss-value-parser/lib/stringify.js"], "sourcesContent": ["function stringifyNode(node, custom) {\n  var type = node.type;\n  var value = node.value;\n  var buf;\n  var customResult;\n\n  if (custom && (customResult = custom(node)) !== undefined) {\n    return customResult;\n  } else if (type === \"word\" || type === \"space\") {\n    return value;\n  } else if (type === \"string\") {\n    buf = node.quote || \"\";\n    return buf + value + (node.unclosed ? \"\" : buf);\n  } else if (type === \"comment\") {\n    return \"/*\" + value + (node.unclosed ? \"\" : \"*/\");\n  } else if (type === \"div\") {\n    return (node.before || \"\") + value + (node.after || \"\");\n  } else if (Array.isArray(node.nodes)) {\n    buf = stringify(node.nodes);\n    if (type !== \"function\") {\n      return buf;\n    }\n    return (\n      value +\n      \"(\" +\n      (node.before || \"\") +\n      buf +\n      (node.after || \"\") +\n      (node.unclosed ? \"\" : \")\")\n    );\n  }\n  return value;\n}\n\nfunction stringify(nodes, custom) {\n  var result, i;\n\n  if (Array.isArray(nodes)) {\n    result = \"\";\n    for (i = nodes.length - 1; ~i; i -= 1) {\n      result = stringifyNode(nodes[i], custom) + result;\n    }\n    return result;\n  }\n  return stringifyNode(nodes, custom);\n}\n\nmodule.exports = stringify;\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACnC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,IAAIC,KAAK,GAAGH,IAAI,CAACG,KAAK;EACtB,IAAIC,GAAG;EACP,IAAIC,YAAY;EAEhB,IAAIJ,MAAM,IAAI,CAACI,YAAY,GAAGJ,MAAM,CAACD,IAAI,CAAC,MAAMM,SAAS,EAAE;IACzD,OAAOD,YAAY;EACrB,CAAC,MAAM,IAAIH,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;IAC9C,OAAOC,KAAK;EACd,CAAC,MAAM,IAAID,IAAI,KAAK,QAAQ,EAAE;IAC5BE,GAAG,GAAGJ,IAAI,CAACO,KAAK,IAAI,EAAE;IACtB,OAAOH,GAAG,GAAGD,KAAK,IAAIH,IAAI,CAACQ,QAAQ,GAAG,EAAE,GAAGJ,GAAG,CAAC;EACjD,CAAC,MAAM,IAAIF,IAAI,KAAK,SAAS,EAAE;IAC7B,OAAO,IAAI,GAAGC,KAAK,IAAIH,IAAI,CAACQ,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC;EACnD,CAAC,MAAM,IAAIN,IAAI,KAAK,KAAK,EAAE;IACzB,OAAO,CAACF,IAAI,CAACS,MAAM,IAAI,EAAE,IAAIN,KAAK,IAAIH,IAAI,CAACU,KAAK,IAAI,EAAE,CAAC;EACzD,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACZ,IAAI,CAACa,KAAK,CAAC,EAAE;IACpCT,GAAG,GAAGU,SAAS,CAACd,IAAI,CAACa,KAAK,CAAC;IAC3B,IAAIX,IAAI,KAAK,UAAU,EAAE;MACvB,OAAOE,GAAG;IACZ;IACA,OACED,KAAK,GACL,GAAG,IACFH,IAAI,CAACS,MAAM,IAAI,EAAE,CAAC,GACnBL,GAAG,IACFJ,IAAI,CAACU,KAAK,IAAI,EAAE,CAAC,IACjBV,IAAI,CAACQ,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC;EAE9B;EACA,OAAOL,KAAK;AACd;AAEA,SAASW,SAASA,CAACD,KAAK,EAAEZ,MAAM,EAAE;EAChC,IAAIc,MAAM,EAAEC,CAAC;EAEb,IAAIL,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC,EAAE;IACxBE,MAAM,GAAG,EAAE;IACX,KAAKC,CAAC,GAAGH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE,CAACD,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrCD,MAAM,GAAGhB,aAAa,CAACc,KAAK,CAACG,CAAC,CAAC,EAAEf,MAAM,CAAC,GAAGc,MAAM;IACnD;IACA,OAAOA,MAAM;EACf;EACA,OAAOhB,aAAa,CAACc,KAAK,EAAEZ,MAAM,CAAC;AACrC;AAEAiB,MAAM,CAACC,OAAO,GAAGL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}