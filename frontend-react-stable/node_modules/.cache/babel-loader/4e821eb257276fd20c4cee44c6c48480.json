{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function file2Obj(file) {\n  return _extends(_extends({}, file), {\n    lastModified: file.lastModified,\n    lastModifiedDate: file.lastModifiedDate,\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    uid: file.uid,\n    percent: 0,\n    originFileObj: file\n  });\n}\n/** Upload fileList. Replace file if exist or just push into it. */\nexport function updateFileList(file, fileList) {\n  var nextFileList = _toConsumableArray(fileList);\n  var fileIndex = nextFileList.findIndex(function (_ref) {\n    var uid = _ref.uid;\n    return uid === file.uid;\n  });\n  if (fileIndex === -1) {\n    nextFileList.push(file);\n  } else {\n    nextFileList[fileIndex] = file;\n  }\n  return nextFileList;\n}\nexport function getFileItem(file, fileList) {\n  var matchKey = file.uid !== undefined ? 'uid' : 'name';\n  return fileList.filter(function (item) {\n    return item[matchKey] === file[matchKey];\n  })[0];\n}\nexport function removeFileItem(file, fileList) {\n  var matchKey = file.uid !== undefined ? 'uid' : 'name';\n  var removed = fileList.filter(function (item) {\n    return item[matchKey] !== file[matchKey];\n  });\n  if (removed.length === fileList.length) {\n    return null;\n  }\n  return removed;\n}\n// ==================== Default Image Preview ====================\nvar extname = function extname() {\n  var url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var temp = url.split('/');\n  var filename = temp[temp.length - 1];\n  var filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n};\nvar isImageFileType = function isImageFileType(type) {\n  return type.indexOf('image/') === 0;\n};\nexport var isImageUrl = function isImageUrl(file) {\n  if (file.type && !file.thumbUrl) {\n    return isImageFileType(file.type);\n  }\n  var url = file.thumbUrl || file.url || '';\n  var extension = extname(url);\n  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {\n    return true;\n  }\n  if (/^data:/.test(url)) {\n    // other file types of base64\n    return false;\n  }\n  if (extension) {\n    // other file types which have extension\n    return false;\n  }\n  return true;\n};\nvar MEASURE_SIZE = 200;\nexport function previewImage(file) {\n  return new Promise(function (resolve) {\n    if (!file.type || !isImageFileType(file.type)) {\n      resolve('');\n      return;\n    }\n    var canvas = document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = \"position: fixed; left: 0; top: 0; width: \".concat(MEASURE_SIZE, \"px; height: \").concat(MEASURE_SIZE, \"px; z-index: 9999; display: none;\");\n    document.body.appendChild(canvas);\n    var ctx = canvas.getContext('2d');\n    var img = new Image();\n    img.onload = function () {\n      var width = img.width,\n        height = img.height;\n      var drawWidth = MEASURE_SIZE;\n      var drawHeight = MEASURE_SIZE;\n      var offsetX = 0;\n      var offsetY = 0;\n      if (width > height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      var dataURL = canvas.toDataURL();\n      document.body.removeChild(canvas);\n      resolve(dataURL);\n    };\n    img.crossOrigin = 'anonymous';\n    if (file.type.startsWith('image/svg+xml')) {\n      var reader = new FileReader();\n      reader.addEventListener('load', function () {\n        if (reader.result) img.src = reader.result;\n      });\n      reader.readAsDataURL(file);\n    } else {\n      img.src = window.URL.createObjectURL(file);\n    }\n  });\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_extends", "file2Obj", "file", "lastModified", "lastModifiedDate", "name", "size", "type", "uid", "percent", "originFileObj", "updateFileList", "fileList", "nextFileList", "fileIndex", "findIndex", "_ref", "push", "getFileItem", "matchKey", "undefined", "filter", "item", "removeFileItem", "removed", "length", "extname", "url", "arguments", "temp", "split", "filename", "filenameWithoutSuffix", "exec", "isImageFileType", "indexOf", "isImageUrl", "thumbUrl", "extension", "test", "MEASURE_SIZE", "previewImage", "Promise", "resolve", "canvas", "document", "createElement", "width", "height", "style", "cssText", "concat", "body", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "img", "Image", "onload", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "dataURL", "toDataURL", "<PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "startsWith", "reader", "FileReader", "addEventListener", "result", "src", "readAsDataURL", "window", "URL", "createObjectURL"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/upload/utils.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function file2Obj(file) {\n  return _extends(_extends({}, file), {\n    lastModified: file.lastModified,\n    lastModifiedDate: file.lastModifiedDate,\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    uid: file.uid,\n    percent: 0,\n    originFileObj: file\n  });\n}\n/** Upload fileList. Replace file if exist or just push into it. */\nexport function updateFileList(file, fileList) {\n  var nextFileList = _toConsumableArray(fileList);\n  var fileIndex = nextFileList.findIndex(function (_ref) {\n    var uid = _ref.uid;\n    return uid === file.uid;\n  });\n  if (fileIndex === -1) {\n    nextFileList.push(file);\n  } else {\n    nextFileList[fileIndex] = file;\n  }\n  return nextFileList;\n}\nexport function getFileItem(file, fileList) {\n  var matchKey = file.uid !== undefined ? 'uid' : 'name';\n  return fileList.filter(function (item) {\n    return item[matchKey] === file[matchKey];\n  })[0];\n}\nexport function removeFileItem(file, fileList) {\n  var matchKey = file.uid !== undefined ? 'uid' : 'name';\n  var removed = fileList.filter(function (item) {\n    return item[matchKey] !== file[matchKey];\n  });\n  if (removed.length === fileList.length) {\n    return null;\n  }\n  return removed;\n}\n// ==================== Default Image Preview ====================\nvar extname = function extname() {\n  var url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var temp = url.split('/');\n  var filename = temp[temp.length - 1];\n  var filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n};\nvar isImageFileType = function isImageFileType(type) {\n  return type.indexOf('image/') === 0;\n};\nexport var isImageUrl = function isImageUrl(file) {\n  if (file.type && !file.thumbUrl) {\n    return isImageFileType(file.type);\n  }\n  var url = file.thumbUrl || file.url || '';\n  var extension = extname(url);\n  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {\n    return true;\n  }\n  if (/^data:/.test(url)) {\n    // other file types of base64\n    return false;\n  }\n  if (extension) {\n    // other file types which have extension\n    return false;\n  }\n  return true;\n};\nvar MEASURE_SIZE = 200;\nexport function previewImage(file) {\n  return new Promise(function (resolve) {\n    if (!file.type || !isImageFileType(file.type)) {\n      resolve('');\n      return;\n    }\n    var canvas = document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = \"position: fixed; left: 0; top: 0; width: \".concat(MEASURE_SIZE, \"px; height: \").concat(MEASURE_SIZE, \"px; z-index: 9999; display: none;\");\n    document.body.appendChild(canvas);\n    var ctx = canvas.getContext('2d');\n    var img = new Image();\n    img.onload = function () {\n      var width = img.width,\n        height = img.height;\n      var drawWidth = MEASURE_SIZE;\n      var drawHeight = MEASURE_SIZE;\n      var offsetX = 0;\n      var offsetY = 0;\n      if (width > height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      var dataURL = canvas.toDataURL();\n      document.body.removeChild(canvas);\n      resolve(dataURL);\n    };\n    img.crossOrigin = 'anonymous';\n    if (file.type.startsWith('image/svg+xml')) {\n      var reader = new FileReader();\n      reader.addEventListener('load', function () {\n        if (reader.result) img.src = reader.result;\n      });\n      reader.readAsDataURL(file);\n    } else {\n      img.src = window.URL.createObjectURL(file);\n    }\n  });\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEE,IAAI,CAAC,EAAE;IAClCC,YAAY,EAAED,IAAI,CAACC,YAAY;IAC/BC,gBAAgB,EAAEF,IAAI,CAACE,gBAAgB;IACvCC,IAAI,EAAEH,IAAI,CAACG,IAAI;IACfC,IAAI,EAAEJ,IAAI,CAACI,IAAI;IACfC,IAAI,EAAEL,IAAI,CAACK,IAAI;IACfC,GAAG,EAAEN,IAAI,CAACM,GAAG;IACbC,OAAO,EAAE,CAAC;IACVC,aAAa,EAAER;EACjB,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASS,cAAcA,CAACT,IAAI,EAAEU,QAAQ,EAAE;EAC7C,IAAIC,YAAY,GAAGd,kBAAkB,CAACa,QAAQ,CAAC;EAC/C,IAAIE,SAAS,GAAGD,YAAY,CAACE,SAAS,CAAC,UAAUC,IAAI,EAAE;IACrD,IAAIR,GAAG,GAAGQ,IAAI,CAACR,GAAG;IAClB,OAAOA,GAAG,KAAKN,IAAI,CAACM,GAAG;EACzB,CAAC,CAAC;EACF,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,YAAY,CAACI,IAAI,CAACf,IAAI,CAAC;EACzB,CAAC,MAAM;IACLW,YAAY,CAACC,SAAS,CAAC,GAAGZ,IAAI;EAChC;EACA,OAAOW,YAAY;AACrB;AACA,OAAO,SAASK,WAAWA,CAAChB,IAAI,EAAEU,QAAQ,EAAE;EAC1C,IAAIO,QAAQ,GAAGjB,IAAI,CAACM,GAAG,KAAKY,SAAS,GAAG,KAAK,GAAG,MAAM;EACtD,OAAOR,QAAQ,CAACS,MAAM,CAAC,UAAUC,IAAI,EAAE;IACrC,OAAOA,IAAI,CAACH,QAAQ,CAAC,KAAKjB,IAAI,CAACiB,QAAQ,CAAC;EAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACA,OAAO,SAASI,cAAcA,CAACrB,IAAI,EAAEU,QAAQ,EAAE;EAC7C,IAAIO,QAAQ,GAAGjB,IAAI,CAACM,GAAG,KAAKY,SAAS,GAAG,KAAK,GAAG,MAAM;EACtD,IAAII,OAAO,GAAGZ,QAAQ,CAACS,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC5C,OAAOA,IAAI,CAACH,QAAQ,CAAC,KAAKjB,IAAI,CAACiB,QAAQ,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIK,OAAO,CAACC,MAAM,KAAKb,QAAQ,CAACa,MAAM,EAAE;IACtC,OAAO,IAAI;EACb;EACA,OAAOD,OAAO;AAChB;AACA;AACA,IAAIE,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EAC/B,IAAIC,GAAG,GAAGC,SAAS,CAACH,MAAM,GAAG,CAAC,IAAIG,SAAS,CAAC,CAAC,CAAC,KAAKR,SAAS,GAAGQ,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAChF,IAAIC,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EACzB,IAAIC,QAAQ,GAAGF,IAAI,CAACA,IAAI,CAACJ,MAAM,GAAG,CAAC,CAAC;EACpC,IAAIO,qBAAqB,GAAGD,QAAQ,CAACD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACrD,OAAO,CAAC,aAAa,CAACG,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;AACD,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAAC3B,IAAI,EAAE;EACnD,OAAOA,IAAI,CAAC4B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AACrC,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAClC,IAAI,EAAE;EAChD,IAAIA,IAAI,CAACK,IAAI,IAAI,CAACL,IAAI,CAACmC,QAAQ,EAAE;IAC/B,OAAOH,eAAe,CAAChC,IAAI,CAACK,IAAI,CAAC;EACnC;EACA,IAAIoB,GAAG,GAAGzB,IAAI,CAACmC,QAAQ,IAAInC,IAAI,CAACyB,GAAG,IAAI,EAAE;EACzC,IAAIW,SAAS,GAAGZ,OAAO,CAACC,GAAG,CAAC;EAC5B,IAAI,eAAe,CAACY,IAAI,CAACZ,GAAG,CAAC,IAAI,0DAA0D,CAACY,IAAI,CAACD,SAAS,CAAC,EAAE;IAC3G,OAAO,IAAI;EACb;EACA,IAAI,QAAQ,CAACC,IAAI,CAACZ,GAAG,CAAC,EAAE;IACtB;IACA,OAAO,KAAK;EACd;EACA,IAAIW,SAAS,EAAE;IACb;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIE,YAAY,GAAG,GAAG;AACtB,OAAO,SAASC,YAAYA,CAACvC,IAAI,EAAE;EACjC,OAAO,IAAIwC,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpC,IAAI,CAACzC,IAAI,CAACK,IAAI,IAAI,CAAC2B,eAAe,CAAChC,IAAI,CAACK,IAAI,CAAC,EAAE;MAC7CoC,OAAO,CAAC,EAAE,CAAC;MACX;IACF;IACA,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7CF,MAAM,CAACG,KAAK,GAAGP,YAAY;IAC3BI,MAAM,CAACI,MAAM,GAAGR,YAAY;IAC5BI,MAAM,CAACK,KAAK,CAACC,OAAO,GAAG,2CAA2C,CAACC,MAAM,CAACX,YAAY,EAAE,cAAc,CAAC,CAACW,MAAM,CAACX,YAAY,EAAE,mCAAmC,CAAC;IACjKK,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;IACjC,IAAIU,GAAG,GAAGV,MAAM,CAACW,UAAU,CAAC,IAAI,CAAC;IACjC,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACrBD,GAAG,CAACE,MAAM,GAAG,YAAY;MACvB,IAAIX,KAAK,GAAGS,GAAG,CAACT,KAAK;QACnBC,MAAM,GAAGQ,GAAG,CAACR,MAAM;MACrB,IAAIW,SAAS,GAAGnB,YAAY;MAC5B,IAAIoB,UAAU,GAAGpB,YAAY;MAC7B,IAAIqB,OAAO,GAAG,CAAC;MACf,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIf,KAAK,GAAGC,MAAM,EAAE;QAClBY,UAAU,GAAGZ,MAAM,IAAIR,YAAY,GAAGO,KAAK,CAAC;QAC5Ce,OAAO,GAAG,EAAEF,UAAU,GAAGD,SAAS,CAAC,GAAG,CAAC;MACzC,CAAC,MAAM;QACLA,SAAS,GAAGZ,KAAK,IAAIP,YAAY,GAAGQ,MAAM,CAAC;QAC3Ca,OAAO,GAAG,EAAEF,SAAS,GAAGC,UAAU,CAAC,GAAG,CAAC;MACzC;MACAN,GAAG,CAACS,SAAS,CAACP,GAAG,EAAEK,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;MAC3D,IAAII,OAAO,GAAGpB,MAAM,CAACqB,SAAS,CAAC,CAAC;MAChCpB,QAAQ,CAACO,IAAI,CAACc,WAAW,CAACtB,MAAM,CAAC;MACjCD,OAAO,CAACqB,OAAO,CAAC;IAClB,CAAC;IACDR,GAAG,CAACW,WAAW,GAAG,WAAW;IAC7B,IAAIjE,IAAI,CAACK,IAAI,CAAC6D,UAAU,CAAC,eAAe,CAAC,EAAE;MACzC,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC7BD,MAAM,CAACE,gBAAgB,CAAC,MAAM,EAAE,YAAY;QAC1C,IAAIF,MAAM,CAACG,MAAM,EAAEhB,GAAG,CAACiB,GAAG,GAAGJ,MAAM,CAACG,MAAM;MAC5C,CAAC,CAAC;MACFH,MAAM,CAACK,aAAa,CAACxE,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLsD,GAAG,CAACiB,GAAG,GAAGE,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC3E,IAAI,CAAC;IAC5C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}