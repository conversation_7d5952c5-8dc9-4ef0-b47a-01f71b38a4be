{"ast": null, "code": "import { createContext } from 'react';\nexport const ReactReduxContext = /*#__PURE__*/createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\nexport default ReactReduxContext;", "map": {"version": 3, "names": ["createContext", "ReactReduxContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/components/Context.js"], "sourcesContent": ["import { createContext } from 'react';\nexport const ReactReduxContext = /*#__PURE__*/createContext(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\n\nexport default ReactReduxContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,MAAMC,iBAAiB,GAAG,aAAaD,aAAa,CAAC,IAAI,CAAC;AAEjE,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,iBAAiB,CAACI,WAAW,GAAG,YAAY;AAC9C;AAEA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}