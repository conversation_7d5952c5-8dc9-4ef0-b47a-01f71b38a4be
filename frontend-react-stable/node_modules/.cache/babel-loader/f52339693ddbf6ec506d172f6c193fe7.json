{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport RcTextArea from 'rc-textarea';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport SizeContext from '../config-provider/SizeContext';\nimport { FormItemInputContext } from '../form/context';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport ClearableLabeledInput from './ClearableLabeledInput';\nimport { fixControlledValue, resolveOnChange, triggerFocus } from './Input';\nfunction fixEmojiLength(value, maxLength) {\n  return _toConsumableArray(value || '').slice(0, maxLength).join('');\n}\nfunction setTriggerValue(isCursorInEnd, preValue, triggerValue, maxLength) {\n  var newTriggerValue = triggerValue;\n  if (isCursorInEnd) {\n    // 光标在尾部，直接截断\n    newTriggerValue = fixEmojiLength(triggerValue, maxLength);\n  } else if (_toConsumableArray(preValue || '').length < triggerValue.length && _toConsumableArray(triggerValue || '').length > maxLength) {\n    // 光标在中间，如果最后的值超过最大值，则采用原先的值\n    newTriggerValue = preValue;\n  }\n  return newTriggerValue;\n}\nvar TextArea = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    _a$bordered = _a.bordered,\n    bordered = _a$bordered === void 0 ? true : _a$bordered,\n    _a$showCount = _a.showCount,\n    showCount = _a$showCount === void 0 ? false : _a$showCount,\n    maxLength = _a.maxLength,\n    className = _a.className,\n    style = _a.style,\n    customizeSize = _a.size,\n    customDisabled = _a.disabled,\n    onCompositionStart = _a.onCompositionStart,\n    onCompositionEnd = _a.onCompositionEnd,\n    onChange = _a.onChange,\n    customStatus = _a.status,\n    props = __rest(_a, [\"prefixCls\", \"bordered\", \"showCount\", \"maxLength\", \"className\", \"style\", \"size\", \"disabled\", \"onCompositionStart\", \"onCompositionEnd\", \"onChange\", \"status\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var size = React.useContext(SizeContext);\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  var _React$useContext2 = React.useContext(FormItemInputContext),\n    contextStatus = _React$useContext2.status,\n    hasFeedback = _React$useContext2.hasFeedback,\n    isFormItemInput = _React$useContext2.isFormItemInput,\n    feedbackIcon = _React$useContext2.feedbackIcon;\n  var mergedStatus = getMergedStatus(contextStatus, customStatus);\n  var innerRef = React.useRef(null);\n  var clearableInputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    compositing = _React$useState2[0],\n    setCompositing = _React$useState2[1];\n  var oldCompositionValueRef = React.useRef();\n  var oldSelectionStartRef = React.useRef(0);\n  var _useMergedState = useMergedState(props.defaultValue, {\n      value: props.value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var hidden = props.hidden;\n  var handleSetValue = function handleSetValue(val, callback) {\n    if (props.value === undefined) {\n      setValue(val);\n      callback === null || callback === void 0 ? void 0 : callback();\n    }\n  };\n  // =========================== Value Update ===========================\n  // Max length value\n  var hasMaxLength = Number(maxLength) > 0;\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    setCompositing(true);\n    // 拼音输入前保存一份旧值\n    oldCompositionValueRef.current = value;\n    // 保存旧的光标位置\n    oldSelectionStartRef.current = e.currentTarget.selectionStart;\n    onCompositionStart === null || onCompositionStart === void 0 ? void 0 : onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    var _a;\n    setCompositing(false);\n    var triggerValue = e.currentTarget.value;\n    if (hasMaxLength) {\n      var isCursorInEnd = oldSelectionStartRef.current >= maxLength + 1 || oldSelectionStartRef.current === ((_a = oldCompositionValueRef.current) === null || _a === void 0 ? void 0 : _a.length);\n      triggerValue = setTriggerValue(isCursorInEnd, oldCompositionValueRef.current, triggerValue, maxLength);\n    }\n    // Patch composition onChange when value changed\n    if (triggerValue !== value) {\n      handleSetValue(triggerValue);\n      resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n    }\n    onCompositionEnd === null || onCompositionEnd === void 0 ? void 0 : onCompositionEnd(e);\n  };\n  var handleChange = function handleChange(e) {\n    var triggerValue = e.target.value;\n    if (!compositing && hasMaxLength) {\n      // 1. 复制粘贴超过maxlength的情况 2.未超过maxlength的情况\n      var isCursorInEnd = e.target.selectionStart >= maxLength + 1 || e.target.selectionStart === triggerValue.length || !e.target.selectionStart;\n      triggerValue = setTriggerValue(isCursorInEnd, value, triggerValue, maxLength);\n    }\n    handleSetValue(triggerValue);\n    resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n  };\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    var _a, _b, _c;\n    handleSetValue('');\n    (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    resolveOnChange((_c = (_b = innerRef.current) === null || _b === void 0 ? void 0 : _b.resizableTextArea) === null || _c === void 0 ? void 0 : _c.textArea, e, onChange);\n  };\n  var prefixCls = getPrefixCls('input', customizePrefixCls);\n  React.useImperativeHandle(ref, function () {\n    var _a;\n    return {\n      resizableTextArea: (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea,\n      focus: function focus(option) {\n        var _a, _b;\n        triggerFocus((_b = (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) === null || _b === void 0 ? void 0 : _b.textArea, option);\n      },\n      blur: function blur() {\n        var _a;\n        return (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n      }\n    };\n  });\n  var textArea = /*#__PURE__*/React.createElement(RcTextArea, _extends({}, omit(props, ['allowClear']), {\n    disabled: mergedDisabled,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, className, className && !showCount), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small' || customizeSize === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large' || customizeSize === 'large'), _classNames), getStatusClassNames(prefixCls, mergedStatus)),\n    style: showCount ? {\n      resize: style === null || style === void 0 ? void 0 : style.resize\n    } : style,\n    prefixCls: prefixCls,\n    onCompositionStart: onInternalCompositionStart,\n    onChange: handleChange,\n    onCompositionEnd: onInternalCompositionEnd,\n    ref: innerRef\n  }));\n  var val = fixControlledValue(value);\n  if (!compositing && hasMaxLength && (props.value === null || props.value === undefined)) {\n    // fix #27612 将value转为数组进行截取，解决 '😂'.length === 2 等emoji表情导致的截取乱码的问题\n    val = fixEmojiLength(val, maxLength);\n  }\n  // TextArea\n  var textareaNode = /*#__PURE__*/React.createElement(ClearableLabeledInput, _extends({\n    disabled: mergedDisabled\n  }, props, {\n    prefixCls: prefixCls,\n    direction: direction,\n    inputType: \"text\",\n    value: val,\n    element: textArea,\n    handleReset: handleReset,\n    ref: clearableInputRef,\n    bordered: bordered,\n    status: customStatus,\n    style: showCount ? undefined : style\n  }));\n  // Only show text area wrapper when needed\n  if (showCount || hasFeedback) {\n    var _classNames2;\n    var valueLength = _toConsumableArray(val).length;\n    var dataCount = '';\n    if (_typeof(showCount) === 'object') {\n      dataCount = showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      hidden: hidden,\n      className: classNames(\"\".concat(prefixCls, \"-textarea\"), (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-show-count\"), showCount), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-in-form-item\"), isFormItemInput), _classNames2), getStatusClassNames(\"\".concat(prefixCls, \"-textarea\"), mergedStatus, hasFeedback), className),\n      style: style,\n      \"data-count\": dataCount\n    }, textareaNode, hasFeedback && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-textarea-suffix\")\n    }, feedbackIcon));\n  }\n  return textareaNode;\n});\nexport default TextArea;", "map": {"version": 3, "names": ["_typeof", "_defineProperty", "_extends", "_slicedToArray", "_toConsumableArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "RcTextArea", "useMergedState", "omit", "React", "ConfigContext", "DisabledContext", "SizeContext", "FormItemInputContext", "getMergedStatus", "getStatusClassNames", "ClearableLabeledInput", "fixControlledValue", "resolveOnChange", "triggerFocus", "fixEmojiLength", "value", "max<PERSON><PERSON><PERSON>", "slice", "join", "setTriggerValue", "isCursorInEnd", "preValue", "triggerValue", "newTriggerValue", "TextArea", "forwardRef", "_a", "ref", "_classNames", "customizePrefixCls", "prefixCls", "_a$bordered", "bordered", "_a$showCount", "showCount", "className", "style", "customizeSize", "size", "customDisabled", "disabled", "onCompositionStart", "onCompositionEnd", "onChange", "customStatus", "status", "props", "_React$useContext", "useContext", "getPrefixCls", "direction", "mergedDisabled", "_React$useContext2", "contextStatus", "hasFeedback", "isFormItemInput", "feedbackIcon", "mergedStatus", "innerRef", "useRef", "clearableInputRef", "_React$useState", "useState", "_React$useState2", "compositing", "setCompositing", "oldCompositionValueRef", "oldSelectionStartRef", "_useMergedState", "defaultValue", "_useMergedState2", "setValue", "hidden", "handleSetValue", "val", "callback", "undefined", "hasMaxLength", "Number", "onInternalCompositionStart", "current", "currentTarget", "selectionStart", "onInternalCompositionEnd", "handleChange", "target", "handleReset", "_b", "_c", "focus", "resizableTextArea", "textArea", "useImperativeHandle", "option", "blur", "createElement", "concat", "resize", "textareaNode", "inputType", "element", "_classNames2", "valueLength", "dataCount", "formatter", "count"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/TextArea.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport RcTextArea from 'rc-textarea';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport SizeContext from '../config-provider/SizeContext';\nimport { FormItemInputContext } from '../form/context';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport ClearableLabeledInput from './ClearableLabeledInput';\nimport { fixControlledValue, resolveOnChange, triggerFocus } from './Input';\nfunction fixEmojiLength(value, maxLength) {\n  return _toConsumableArray(value || '').slice(0, maxLength).join('');\n}\nfunction setTriggerValue(isCursorInEnd, preValue, triggerValue, maxLength) {\n  var newTriggerValue = triggerValue;\n  if (isCursorInEnd) {\n    // 光标在尾部，直接截断\n    newTriggerValue = fixEmojiLength(triggerValue, maxLength);\n  } else if (_toConsumableArray(preValue || '').length < triggerValue.length && _toConsumableArray(triggerValue || '').length > maxLength) {\n    // 光标在中间，如果最后的值超过最大值，则采用原先的值\n    newTriggerValue = preValue;\n  }\n  return newTriggerValue;\n}\nvar TextArea = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    _a$bordered = _a.bordered,\n    bordered = _a$bordered === void 0 ? true : _a$bordered,\n    _a$showCount = _a.showCount,\n    showCount = _a$showCount === void 0 ? false : _a$showCount,\n    maxLength = _a.maxLength,\n    className = _a.className,\n    style = _a.style,\n    customizeSize = _a.size,\n    customDisabled = _a.disabled,\n    onCompositionStart = _a.onCompositionStart,\n    onCompositionEnd = _a.onCompositionEnd,\n    onChange = _a.onChange,\n    customStatus = _a.status,\n    props = __rest(_a, [\"prefixCls\", \"bordered\", \"showCount\", \"maxLength\", \"className\", \"style\", \"size\", \"disabled\", \"onCompositionStart\", \"onCompositionEnd\", \"onChange\", \"status\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var size = React.useContext(SizeContext);\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  var _React$useContext2 = React.useContext(FormItemInputContext),\n    contextStatus = _React$useContext2.status,\n    hasFeedback = _React$useContext2.hasFeedback,\n    isFormItemInput = _React$useContext2.isFormItemInput,\n    feedbackIcon = _React$useContext2.feedbackIcon;\n  var mergedStatus = getMergedStatus(contextStatus, customStatus);\n  var innerRef = React.useRef(null);\n  var clearableInputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    compositing = _React$useState2[0],\n    setCompositing = _React$useState2[1];\n  var oldCompositionValueRef = React.useRef();\n  var oldSelectionStartRef = React.useRef(0);\n  var _useMergedState = useMergedState(props.defaultValue, {\n      value: props.value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var hidden = props.hidden;\n  var handleSetValue = function handleSetValue(val, callback) {\n    if (props.value === undefined) {\n      setValue(val);\n      callback === null || callback === void 0 ? void 0 : callback();\n    }\n  };\n  // =========================== Value Update ===========================\n  // Max length value\n  var hasMaxLength = Number(maxLength) > 0;\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    setCompositing(true);\n    // 拼音输入前保存一份旧值\n    oldCompositionValueRef.current = value;\n    // 保存旧的光标位置\n    oldSelectionStartRef.current = e.currentTarget.selectionStart;\n    onCompositionStart === null || onCompositionStart === void 0 ? void 0 : onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    var _a;\n    setCompositing(false);\n    var triggerValue = e.currentTarget.value;\n    if (hasMaxLength) {\n      var isCursorInEnd = oldSelectionStartRef.current >= maxLength + 1 || oldSelectionStartRef.current === ((_a = oldCompositionValueRef.current) === null || _a === void 0 ? void 0 : _a.length);\n      triggerValue = setTriggerValue(isCursorInEnd, oldCompositionValueRef.current, triggerValue, maxLength);\n    }\n    // Patch composition onChange when value changed\n    if (triggerValue !== value) {\n      handleSetValue(triggerValue);\n      resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n    }\n    onCompositionEnd === null || onCompositionEnd === void 0 ? void 0 : onCompositionEnd(e);\n  };\n  var handleChange = function handleChange(e) {\n    var triggerValue = e.target.value;\n    if (!compositing && hasMaxLength) {\n      // 1. 复制粘贴超过maxlength的情况 2.未超过maxlength的情况\n      var isCursorInEnd = e.target.selectionStart >= maxLength + 1 || e.target.selectionStart === triggerValue.length || !e.target.selectionStart;\n      triggerValue = setTriggerValue(isCursorInEnd, value, triggerValue, maxLength);\n    }\n    handleSetValue(triggerValue);\n    resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n  };\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    var _a, _b, _c;\n    handleSetValue('');\n    (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    resolveOnChange((_c = (_b = innerRef.current) === null || _b === void 0 ? void 0 : _b.resizableTextArea) === null || _c === void 0 ? void 0 : _c.textArea, e, onChange);\n  };\n  var prefixCls = getPrefixCls('input', customizePrefixCls);\n  React.useImperativeHandle(ref, function () {\n    var _a;\n    return {\n      resizableTextArea: (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea,\n      focus: function focus(option) {\n        var _a, _b;\n        triggerFocus((_b = (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) === null || _b === void 0 ? void 0 : _b.textArea, option);\n      },\n      blur: function blur() {\n        var _a;\n        return (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n      }\n    };\n  });\n  var textArea = /*#__PURE__*/React.createElement(RcTextArea, _extends({}, omit(props, ['allowClear']), {\n    disabled: mergedDisabled,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, className, className && !showCount), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small' || customizeSize === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large' || customizeSize === 'large'), _classNames), getStatusClassNames(prefixCls, mergedStatus)),\n    style: showCount ? {\n      resize: style === null || style === void 0 ? void 0 : style.resize\n    } : style,\n    prefixCls: prefixCls,\n    onCompositionStart: onInternalCompositionStart,\n    onChange: handleChange,\n    onCompositionEnd: onInternalCompositionEnd,\n    ref: innerRef\n  }));\n  var val = fixControlledValue(value);\n  if (!compositing && hasMaxLength && (props.value === null || props.value === undefined)) {\n    // fix #27612 将value转为数组进行截取，解决 '😂'.length === 2 等emoji表情导致的截取乱码的问题\n    val = fixEmojiLength(val, maxLength);\n  }\n  // TextArea\n  var textareaNode = /*#__PURE__*/React.createElement(ClearableLabeledInput, _extends({\n    disabled: mergedDisabled\n  }, props, {\n    prefixCls: prefixCls,\n    direction: direction,\n    inputType: \"text\",\n    value: val,\n    element: textArea,\n    handleReset: handleReset,\n    ref: clearableInputRef,\n    bordered: bordered,\n    status: customStatus,\n    style: showCount ? undefined : style\n  }));\n  // Only show text area wrapper when needed\n  if (showCount || hasFeedback) {\n    var _classNames2;\n    var valueLength = _toConsumableArray(val).length;\n    var dataCount = '';\n    if (_typeof(showCount) === 'object') {\n      dataCount = showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      hidden: hidden,\n      className: classNames(\"\".concat(prefixCls, \"-textarea\"), (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-show-count\"), showCount), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-textarea-in-form-item\"), isFormItemInput), _classNames2), getStatusClassNames(\"\".concat(prefixCls, \"-textarea\"), mergedStatus, hasFeedback), className),\n      style: style,\n      \"data-count\": dataCount\n    }, textareaNode, hasFeedback && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-textarea-suffix\")\n    }, feedbackIcon));\n  }\n  return textareaNode;\n});\nexport default TextArea;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,gCAAgC;AACxD,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC3E,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,YAAY,QAAQ,SAAS;AAC3E,SAASC,cAAcA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,OAAOhC,kBAAkB,CAAC+B,KAAK,IAAI,EAAE,CAAC,CAACE,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACrE;AACA,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEN,SAAS,EAAE;EACzE,IAAIO,eAAe,GAAGD,YAAY;EAClC,IAAIF,aAAa,EAAE;IACjB;IACAG,eAAe,GAAGT,cAAc,CAACQ,YAAY,EAAEN,SAAS,CAAC;EAC3D,CAAC,MAAM,IAAIhC,kBAAkB,CAACqC,QAAQ,IAAI,EAAE,CAAC,CAACxB,MAAM,GAAGyB,YAAY,CAACzB,MAAM,IAAIb,kBAAkB,CAACsC,YAAY,IAAI,EAAE,CAAC,CAACzB,MAAM,GAAGmB,SAAS,EAAE;IACvI;IACAO,eAAe,GAAGF,QAAQ;EAC5B;EACA,OAAOE,eAAe;AACxB;AACA,IAAIC,QAAQ,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAC9D,IAAIC,WAAW;EACf,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACnCC,WAAW,GAAGL,EAAE,CAACM,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,WAAW;IACtDE,YAAY,GAAGP,EAAE,CAACQ,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IAC1DjB,SAAS,GAAGU,EAAE,CAACV,SAAS;IACxBmB,SAAS,GAAGT,EAAE,CAACS,SAAS;IACxBC,KAAK,GAAGV,EAAE,CAACU,KAAK;IAChBC,aAAa,GAAGX,EAAE,CAACY,IAAI;IACvBC,cAAc,GAAGb,EAAE,CAACc,QAAQ;IAC5BC,kBAAkB,GAAGf,EAAE,CAACe,kBAAkB;IAC1CC,gBAAgB,GAAGhB,EAAE,CAACgB,gBAAgB;IACtCC,QAAQ,GAAGjB,EAAE,CAACiB,QAAQ;IACtBC,YAAY,GAAGlB,EAAE,CAACmB,MAAM;IACxBC,KAAK,GAAG7D,MAAM,CAACyC,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;EACnL,IAAIqB,iBAAiB,GAAG5C,KAAK,CAAC6C,UAAU,CAAC5C,aAAa,CAAC;IACrD6C,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIZ,IAAI,GAAGnC,KAAK,CAAC6C,UAAU,CAAC1C,WAAW,CAAC;EACxC;EACA,IAAIkC,QAAQ,GAAGrC,KAAK,CAAC6C,UAAU,CAAC3C,eAAe,CAAC;EAChD,IAAI8C,cAAc,GAAGZ,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC,QAAQ;EACrG,IAAIY,kBAAkB,GAAGjD,KAAK,CAAC6C,UAAU,CAACzC,oBAAoB,CAAC;IAC7D8C,aAAa,GAAGD,kBAAkB,CAACP,MAAM;IACzCS,WAAW,GAAGF,kBAAkB,CAACE,WAAW;IAC5CC,eAAe,GAAGH,kBAAkB,CAACG,eAAe;IACpDC,YAAY,GAAGJ,kBAAkB,CAACI,YAAY;EAChD,IAAIC,YAAY,GAAGjD,eAAe,CAAC6C,aAAa,EAAET,YAAY,CAAC;EAC/D,IAAIc,QAAQ,GAAGvD,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,iBAAiB,GAAGzD,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAIE,eAAe,GAAG1D,KAAK,CAAC2D,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhF,cAAc,CAAC8E,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,sBAAsB,GAAG/D,KAAK,CAACwD,MAAM,CAAC,CAAC;EAC3C,IAAIQ,oBAAoB,GAAGhE,KAAK,CAACwD,MAAM,CAAC,CAAC,CAAC;EAC1C,IAAIS,eAAe,GAAGnE,cAAc,CAAC6C,KAAK,CAACuB,YAAY,EAAE;MACrDtD,KAAK,EAAE+B,KAAK,CAAC/B;IACf,CAAC,CAAC;IACFuD,gBAAgB,GAAGvF,cAAc,CAACqF,eAAe,EAAE,CAAC,CAAC;IACrDrD,KAAK,GAAGuD,gBAAgB,CAAC,CAAC,CAAC;IAC3BC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIE,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;EACzB,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IAC1D,IAAI7B,KAAK,CAAC/B,KAAK,KAAK6D,SAAS,EAAE;MAC7BL,QAAQ,CAACG,GAAG,CAAC;MACbC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;IAChE;EACF,CAAC;EACD;EACA;EACA,IAAIE,YAAY,GAAGC,MAAM,CAAC9D,SAAS,CAAC,GAAG,CAAC;EACxC,IAAI+D,0BAA0B,GAAG,SAASA,0BAA0BA,CAAC5F,CAAC,EAAE;IACtE8E,cAAc,CAAC,IAAI,CAAC;IACpB;IACAC,sBAAsB,CAACc,OAAO,GAAGjE,KAAK;IACtC;IACAoD,oBAAoB,CAACa,OAAO,GAAG7F,CAAC,CAAC8F,aAAa,CAACC,cAAc;IAC7DzC,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACtD,CAAC,CAAC;EAC/F,CAAC;EACD,IAAIgG,wBAAwB,GAAG,SAASA,wBAAwBA,CAAChG,CAAC,EAAE;IAClE,IAAIuC,EAAE;IACNuC,cAAc,CAAC,KAAK,CAAC;IACrB,IAAI3C,YAAY,GAAGnC,CAAC,CAAC8F,aAAa,CAAClE,KAAK;IACxC,IAAI8D,YAAY,EAAE;MAChB,IAAIzD,aAAa,GAAG+C,oBAAoB,CAACa,OAAO,IAAIhE,SAAS,GAAG,CAAC,IAAImD,oBAAoB,CAACa,OAAO,MAAM,CAACtD,EAAE,GAAGwC,sBAAsB,CAACc,OAAO,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7B,MAAM,CAAC;MAC5LyB,YAAY,GAAGH,eAAe,CAACC,aAAa,EAAE8C,sBAAsB,CAACc,OAAO,EAAE1D,YAAY,EAAEN,SAAS,CAAC;IACxG;IACA;IACA,IAAIM,YAAY,KAAKP,KAAK,EAAE;MAC1B0D,cAAc,CAACnD,YAAY,CAAC;MAC5BV,eAAe,CAACzB,CAAC,CAAC8F,aAAa,EAAE9F,CAAC,EAAEwD,QAAQ,EAAErB,YAAY,CAAC;IAC7D;IACAoB,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACvD,CAAC,CAAC;EACzF,CAAC;EACD,IAAIiG,YAAY,GAAG,SAASA,YAAYA,CAACjG,CAAC,EAAE;IAC1C,IAAImC,YAAY,GAAGnC,CAAC,CAACkG,MAAM,CAACtE,KAAK;IACjC,IAAI,CAACiD,WAAW,IAAIa,YAAY,EAAE;MAChC;MACA,IAAIzD,aAAa,GAAGjC,CAAC,CAACkG,MAAM,CAACH,cAAc,IAAIlE,SAAS,GAAG,CAAC,IAAI7B,CAAC,CAACkG,MAAM,CAACH,cAAc,KAAK5D,YAAY,CAACzB,MAAM,IAAI,CAACV,CAAC,CAACkG,MAAM,CAACH,cAAc;MAC3I5D,YAAY,GAAGH,eAAe,CAACC,aAAa,EAAEL,KAAK,EAAEO,YAAY,EAAEN,SAAS,CAAC;IAC/E;IACAyD,cAAc,CAACnD,YAAY,CAAC;IAC5BV,eAAe,CAACzB,CAAC,CAAC8F,aAAa,EAAE9F,CAAC,EAAEwD,QAAQ,EAAErB,YAAY,CAAC;EAC7D,CAAC;EACD;EACA,IAAIgE,WAAW,GAAG,SAASA,WAAWA,CAACnG,CAAC,EAAE;IACxC,IAAIuC,EAAE,EAAE6D,EAAE,EAAEC,EAAE;IACdf,cAAc,CAAC,EAAE,CAAC;IAClB,CAAC/C,EAAE,GAAGgC,QAAQ,CAACsB,OAAO,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+D,KAAK,CAAC,CAAC;IACvE7E,eAAe,CAAC,CAAC4E,EAAE,GAAG,CAACD,EAAE,GAAG7B,QAAQ,CAACsB,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,iBAAiB,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,QAAQ,EAAExG,CAAC,EAAEwD,QAAQ,CAAC;EACzK,CAAC;EACD,IAAIb,SAAS,GAAGmB,YAAY,CAAC,OAAO,EAAEpB,kBAAkB,CAAC;EACzD1B,KAAK,CAACyF,mBAAmB,CAACjE,GAAG,EAAE,YAAY;IACzC,IAAID,EAAE;IACN,OAAO;MACLgE,iBAAiB,EAAE,CAAChE,EAAE,GAAGgC,QAAQ,CAACsB,OAAO,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgE,iBAAiB;MACpGD,KAAK,EAAE,SAASA,KAAKA,CAACI,MAAM,EAAE;QAC5B,IAAInE,EAAE,EAAE6D,EAAE;QACV1E,YAAY,CAAC,CAAC0E,EAAE,GAAG,CAAC7D,EAAE,GAAGgC,QAAQ,CAACsB,OAAO,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgE,iBAAiB,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,EAAEE,MAAM,CAAC;MACjK,CAAC;MACDC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIpE,EAAE;QACN,OAAO,CAACA,EAAE,GAAGgC,QAAQ,CAACsB,OAAO,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoE,IAAI,CAAC,CAAC;MAC/E;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIH,QAAQ,GAAG,aAAaxF,KAAK,CAAC4F,aAAa,CAAC/F,UAAU,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,IAAI,CAAC4C,KAAK,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE;IACpGN,QAAQ,EAAEW,cAAc;IACxBhB,SAAS,EAAEpC,UAAU,EAAE6B,WAAW,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACoE,MAAM,CAAClE,SAAS,EAAE,aAAa,CAAC,EAAE,CAACE,QAAQ,CAAC,EAAEnD,eAAe,CAAC+C,WAAW,EAAEO,SAAS,EAAEA,SAAS,IAAI,CAACD,SAAS,CAAC,EAAErD,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACoE,MAAM,CAAClE,SAAS,EAAE,KAAK,CAAC,EAAEQ,IAAI,KAAK,OAAO,IAAID,aAAa,KAAK,OAAO,CAAC,EAAExD,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACoE,MAAM,CAAClE,SAAS,EAAE,KAAK,CAAC,EAAEQ,IAAI,KAAK,OAAO,IAAID,aAAa,KAAK,OAAO,CAAC,EAAET,WAAW,GAAGnB,mBAAmB,CAACqB,SAAS,EAAE2B,YAAY,CAAC,CAAC;IACxcrB,KAAK,EAAEF,SAAS,GAAG;MACjB+D,MAAM,EAAE7D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC6D;IAC9D,CAAC,GAAG7D,KAAK;IACTN,SAAS,EAAEA,SAAS;IACpBW,kBAAkB,EAAEsC,0BAA0B;IAC9CpC,QAAQ,EAAEyC,YAAY;IACtB1C,gBAAgB,EAAEyC,wBAAwB;IAC1CxD,GAAG,EAAE+B;EACP,CAAC,CAAC,CAAC;EACH,IAAIgB,GAAG,GAAG/D,kBAAkB,CAACI,KAAK,CAAC;EACnC,IAAI,CAACiD,WAAW,IAAIa,YAAY,KAAK/B,KAAK,CAAC/B,KAAK,KAAK,IAAI,IAAI+B,KAAK,CAAC/B,KAAK,KAAK6D,SAAS,CAAC,EAAE;IACvF;IACAF,GAAG,GAAG5D,cAAc,CAAC4D,GAAG,EAAE1D,SAAS,CAAC;EACtC;EACA;EACA,IAAIkF,YAAY,GAAG,aAAa/F,KAAK,CAAC4F,aAAa,CAACrF,qBAAqB,EAAE5B,QAAQ,CAAC;IAClF0D,QAAQ,EAAEW;EACZ,CAAC,EAAEL,KAAK,EAAE;IACRhB,SAAS,EAAEA,SAAS;IACpBoB,SAAS,EAAEA,SAAS;IACpBiD,SAAS,EAAE,MAAM;IACjBpF,KAAK,EAAE2D,GAAG;IACV0B,OAAO,EAAET,QAAQ;IACjBL,WAAW,EAAEA,WAAW;IACxB3D,GAAG,EAAEiC,iBAAiB;IACtB5B,QAAQ,EAAEA,QAAQ;IAClBa,MAAM,EAAED,YAAY;IACpBR,KAAK,EAAEF,SAAS,GAAG0C,SAAS,GAAGxC;EACjC,CAAC,CAAC,CAAC;EACH;EACA,IAAIF,SAAS,IAAIoB,WAAW,EAAE;IAC5B,IAAI+C,YAAY;IAChB,IAAIC,WAAW,GAAGtH,kBAAkB,CAAC0F,GAAG,CAAC,CAAC7E,MAAM;IAChD,IAAI0G,SAAS,GAAG,EAAE;IAClB,IAAI3H,OAAO,CAACsD,SAAS,CAAC,KAAK,QAAQ,EAAE;MACnCqE,SAAS,GAAGrE,SAAS,CAACsE,SAAS,CAAC;QAC9BzF,KAAK,EAAE2D,GAAG;QACV+B,KAAK,EAAEH,WAAW;QAClBtF,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLuF,SAAS,GAAG,EAAE,CAACP,MAAM,CAACM,WAAW,CAAC,CAACN,MAAM,CAACnB,YAAY,GAAG,KAAK,CAACmB,MAAM,CAAChF,SAAS,CAAC,GAAG,EAAE,CAAC;IACxF;IACA,OAAO,aAAab,KAAK,CAAC4F,aAAa,CAAC,KAAK,EAAE;MAC7CvB,MAAM,EAAEA,MAAM;MACdrC,SAAS,EAAEpC,UAAU,CAAC,EAAE,CAACiG,MAAM,CAAClE,SAAS,EAAE,WAAW,CAAC,GAAGuE,YAAY,GAAG,CAAC,CAAC,EAAExH,eAAe,CAACwH,YAAY,EAAE,EAAE,CAACL,MAAM,CAAClE,SAAS,EAAE,eAAe,CAAC,EAAEoB,SAAS,KAAK,KAAK,CAAC,EAAErE,eAAe,CAACwH,YAAY,EAAE,EAAE,CAACL,MAAM,CAAClE,SAAS,EAAE,sBAAsB,CAAC,EAAEI,SAAS,CAAC,EAAErD,eAAe,CAACwH,YAAY,EAAE,EAAE,CAACL,MAAM,CAAClE,SAAS,EAAE,wBAAwB,CAAC,EAAEyB,eAAe,CAAC,EAAE8C,YAAY,GAAG5F,mBAAmB,CAAC,EAAE,CAACuF,MAAM,CAAClE,SAAS,EAAE,WAAW,CAAC,EAAE2B,YAAY,EAAEH,WAAW,CAAC,EAAEnB,SAAS,CAAC;MAC5cC,KAAK,EAAEA,KAAK;MACZ,YAAY,EAAEmE;IAChB,CAAC,EAAEL,YAAY,EAAE5C,WAAW,IAAI,aAAanD,KAAK,CAAC4F,aAAa,CAAC,MAAM,EAAE;MACvE5D,SAAS,EAAE,EAAE,CAAC6D,MAAM,CAAClE,SAAS,EAAE,kBAAkB;IACpD,CAAC,EAAE0B,YAAY,CAAC,CAAC;EACnB;EACA,OAAO0C,YAAY;AACrB,CAAC,CAAC;AACF,eAAe1E,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}