{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.slice = exports.map = void 0;\nvar array = Array.prototype;\nvar slice = array.slice;\nexports.slice = slice;\nvar map = array.map;\nexports.map = map;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "slice", "map", "array", "Array", "prototype"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/array.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.slice = exports.map = void 0;\nvar array = Array.prototype;\nvar slice = array.slice;\nexports.slice = slice;\nvar map = array.map;\nexports.map = map;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,GAAG,GAAG,KAAK,CAAC;AACpC,IAAIC,KAAK,GAAGC,KAAK,CAACC,SAAS;AAC3B,IAAIJ,KAAK,GAAGE,KAAK,CAACF,KAAK;AACvBF,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrB,IAAIC,GAAG,GAAGC,KAAK,CAACD,GAAG;AACnBH,OAAO,CAACG,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script"}