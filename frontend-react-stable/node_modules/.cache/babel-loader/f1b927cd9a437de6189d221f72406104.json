{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from '@rc-component/portal';\nimport DrawerPopup from './DrawerPopup';\nimport { warnCheck } from './util';\nvar Drawer = function Drawer(props) {\n  var open = props.open,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    prefixCls = props.prefixCls,\n    afterOpenChange = props.afterOpenChange,\n    destroyOnClose = props.destroyOnClose,\n    mask = props.mask;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  // ============================= Warn =============================\n  if (process.env.NODE_ENV !== 'production') {\n    warnCheck(props);\n  }\n  // ============================= Open =============================\n  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {\n    setAnimatedVisible(nextVisible);\n    afterOpenChange === null || afterOpenChange === void 0 ? void 0 : afterOpenChange(nextVisible);\n  };\n  // ============================ Render ============================\n  if (!forceRender && !animatedVisible && !open && destroyOnClose) {\n    return null;\n  }\n  var sharedDrawerProps = _objectSpread(_objectSpread({}, props), {}, {\n    prefixCls: prefixCls,\n    afterOpenChange: internalAfterOpenChange\n  });\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: mask && (open || animatedVisible)\n  }, /*#__PURE__*/React.createElement(DrawerPopup, _extends({}, sharedDrawerProps, {\n    inline: getContainer === false\n  })));\n};\n// Default Value.\n// Since spread with default value will make this all over components.\n// Let's maintain this in one place.\nDrawer.defaultProps = {\n  open: false,\n  prefixCls: 'rc-drawer',\n  placement: 'right',\n  autoFocus: true,\n  keyboard: true,\n  width: 378,\n  mask: true,\n  maskClosable: true\n};\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "React", "Portal", "Drawer<PERSON><PERSON><PERSON>", "warn<PERSON>heck", "Drawer", "props", "open", "getContainer", "forceRender", "prefixCls", "afterOpenChange", "destroyOnClose", "mask", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "process", "env", "NODE_ENV", "internalAfterOpenChange", "nextVisible", "sharedDrawerProps", "createElement", "autoDestroy", "autoLock", "inline", "defaultProps", "placement", "autoFocus", "keyboard", "width", "maskClosable", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-drawer/es/Drawer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from '@rc-component/portal';\nimport DrawerPopup from './DrawerPopup';\nimport { warnCheck } from './util';\nvar Drawer = function Drawer(props) {\n  var open = props.open,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    prefixCls = props.prefixCls,\n    afterOpenChange = props.afterOpenChange,\n    destroyOnClose = props.destroyOnClose,\n    mask = props.mask;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  // ============================= Warn =============================\n  if (process.env.NODE_ENV !== 'production') {\n    warnCheck(props);\n  }\n  // ============================= Open =============================\n  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {\n    setAnimatedVisible(nextVisible);\n    afterOpenChange === null || afterOpenChange === void 0 ? void 0 : afterOpenChange(nextVisible);\n  };\n  // ============================ Render ============================\n  if (!forceRender && !animatedVisible && !open && destroyOnClose) {\n    return null;\n  }\n  var sharedDrawerProps = _objectSpread(_objectSpread({}, props), {}, {\n    prefixCls: prefixCls,\n    afterOpenChange: internalAfterOpenChange\n  });\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: mask && (open || animatedVisible)\n  }, /*#__PURE__*/React.createElement(DrawerPopup, _extends({}, sharedDrawerProps, {\n    inline: getContainer === false\n  })));\n};\n// Default Value.\n// Since spread with default value will make this all over components.\n// Let's maintain this in one place.\nDrawer.defaultProps = {\n  open: false,\n  prefixCls: 'rc-drawer',\n  placement: 'right',\n  autoFocus: true,\n  keyboard: true,\n  width: 378,\n  mask: true,\n  maskClosable: true\n};\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,SAAS,QAAQ,QAAQ;AAClC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,eAAe,GAAGL,KAAK,CAACK,eAAe;IACvCC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,IAAI,GAAGP,KAAK,CAACO,IAAI;EACnB,IAAIC,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhB,cAAc,CAACc,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjB,SAAS,CAACE,KAAK,CAAC;EAClB;EACA;EACA,IAAIgB,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,WAAW,EAAE;IAC1EL,kBAAkB,CAACK,WAAW,CAAC;IAC/BZ,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACY,WAAW,CAAC;EAChG,CAAC;EACD;EACA,IAAI,CAACd,WAAW,IAAI,CAACQ,eAAe,IAAI,CAACV,IAAI,IAAIK,cAAc,EAAE;IAC/D,OAAO,IAAI;EACb;EACA,IAAIY,iBAAiB,GAAGzB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEO,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAClEI,SAAS,EAAEA,SAAS;IACpBC,eAAe,EAAEW;EACnB,CAAC,CAAC;EACF,OAAO,aAAarB,KAAK,CAACwB,aAAa,CAACvB,MAAM,EAAE;IAC9CK,IAAI,EAAEA,IAAI,IAAIE,WAAW,IAAIQ,eAAe;IAC5CS,WAAW,EAAE,KAAK;IAClBlB,YAAY,EAAEA,YAAY;IAC1BmB,QAAQ,EAAEd,IAAI,KAAKN,IAAI,IAAIU,eAAe;EAC5C,CAAC,EAAE,aAAahB,KAAK,CAACwB,aAAa,CAACtB,WAAW,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE0B,iBAAiB,EAAE;IAC/EI,MAAM,EAAEpB,YAAY,KAAK;EAC3B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACAH,MAAM,CAACwB,YAAY,GAAG;EACpBtB,IAAI,EAAE,KAAK;EACXG,SAAS,EAAE,WAAW;EACtBoB,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,GAAG;EACVpB,IAAI,EAAE,IAAI;EACVqB,YAAY,EAAE;AAChB,CAAC;AACD,IAAIf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChB,MAAM,CAAC8B,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAe9B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}