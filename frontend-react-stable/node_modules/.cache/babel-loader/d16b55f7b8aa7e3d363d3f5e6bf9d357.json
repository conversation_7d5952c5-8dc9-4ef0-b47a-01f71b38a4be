{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.copy = copy;\nexports.default = sequential;\nexports.sequentialLog = sequentialLog;\nexports.sequentialPow = sequentialPow;\nexports.sequentialSqrt = sequentialSqrt;\nexports.sequentialSymlog = sequentialSymlog;\nvar _index = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nvar _linear = require(\"./linear.js\");\nvar _log = require(\"./log.js\");\nvar _symlog = require(\"./symlog.js\");\nvar _pow = require(\"./pow.js\");\nfunction transformer() {\n  var x0 = 0,\n    x1 = 1,\n    t0,\n    t1,\n    k10,\n    transform,\n    interpolator = _continuous.identity,\n    clamp = false,\n    unknown;\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n  scale.range = range(_index.interpolate);\n  scale.rangeRound = range(_index.interpolateRound);\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\nfunction copy(source, target) {\n  return target.domain(source.domain()).interpolator(source.interpolator()).clamp(source.clamp()).unknown(source.unknown());\n}\nfunction sequential() {\n  var scale = (0, _linear.linearish)(transformer()(_continuous.identity));\n  scale.copy = function () {\n    return copy(scale, sequential());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction sequentialLog() {\n  var scale = (0, _log.loggish)(transformer()).domain([1, 10]);\n  scale.copy = function () {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction sequentialSymlog() {\n  var scale = (0, _symlog.symlogish)(transformer());\n  scale.copy = function () {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction sequentialPow() {\n  var scale = (0, _pow.powish)(transformer());\n  scale.copy = function () {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "copy", "default", "sequential", "sequentialLog", "sequentialPow", "sequentialSqrt", "sequentialSymlog", "_index", "require", "_continuous", "_init", "_linear", "_log", "_symlog", "_pow", "transformer", "x0", "x1", "t0", "t1", "k10", "transform", "interpolator", "identity", "clamp", "unknown", "scale", "x", "isNaN", "Math", "max", "min", "domain", "_", "arguments", "length", "range", "interpolate", "r0", "r1", "rangeRound", "interpolateRound", "t", "source", "target", "linearish", "initInterpolator", "apply", "loggish", "base", "symlogish", "constant", "powish", "exponent"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/sequential.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.copy = copy;\nexports.default = sequential;\nexports.sequentialLog = sequentialLog;\nexports.sequentialPow = sequentialPow;\nexports.sequentialSqrt = sequentialSqrt;\nexports.sequentialSymlog = sequentialSymlog;\n\nvar _index = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nvar _linear = require(\"./linear.js\");\n\nvar _log = require(\"./log.js\");\n\nvar _symlog = require(\"./symlog.js\");\n\nvar _pow = require(\"./pow.js\");\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = _continuous.identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(_index.interpolate);\n  scale.rangeRound = range(_index.interpolateRound);\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nfunction copy(source, target) {\n  return target.domain(source.domain()).interpolator(source.interpolator()).clamp(source.clamp()).unknown(source.unknown());\n}\n\nfunction sequential() {\n  var scale = (0, _linear.linearish)(transformer()(_continuous.identity));\n\n  scale.copy = function () {\n    return copy(scale, sequential());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialLog() {\n  var scale = (0, _log.loggish)(transformer()).domain([1, 10]);\n\n  scale.copy = function () {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSymlog() {\n  var scale = (0, _symlog.symlogish)(transformer());\n\n  scale.copy = function () {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialPow() {\n  var scale = (0, _pow.powish)(transformer());\n\n  scale.copy = function () {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBF,OAAO,CAACG,OAAO,GAAGC,UAAU;AAC5BJ,OAAO,CAACK,aAAa,GAAGA,aAAa;AACrCL,OAAO,CAACM,aAAa,GAAGA,aAAa;AACrCN,OAAO,CAACO,cAAc,GAAGA,cAAc;AACvCP,OAAO,CAACQ,gBAAgB,GAAGA,gBAAgB;AAE3C,IAAIC,MAAM,GAAGC,OAAO,CAAC,iDAAiD,CAAC;AAEvE,IAAIC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIG,OAAO,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAII,IAAI,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAE9B,IAAIK,OAAO,GAAGL,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIM,IAAI,GAAGN,OAAO,CAAC,UAAU,CAAC;AAE9B,SAASO,WAAWA,CAAA,EAAG;EACrB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,EAAE;IACFC,EAAE;IACFC,GAAG;IACHC,SAAS;IACTC,YAAY,GAAGb,WAAW,CAACc,QAAQ;IACnCC,KAAK,GAAG,KAAK;IACbC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,GAAGH,YAAY,CAACF,GAAG,KAAK,CAAC,GAAG,GAAG,IAAIO,CAAC,GAAG,CAACN,SAAS,CAACM,CAAC,CAAC,GAAGT,EAAE,IAAIE,GAAG,EAAEI,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EACxJ;EAEAD,KAAK,CAACM,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAI,CAACnB,EAAE,EAAEC,EAAE,CAAC,GAAGgB,CAAC,EAAEf,EAAE,GAAGG,SAAS,CAACL,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEG,EAAE,GAAGE,SAAS,CAACJ,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEG,GAAG,GAAGF,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEQ,KAAK,IAAI,CAACV,EAAE,EAAEC,EAAE,CAAC;EACrJ,CAAC;EAEDS,KAAK,CAACF,KAAK,GAAG,UAAUS,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIX,KAAK,GAAG,CAAC,CAACS,CAAC,EAAEP,KAAK,IAAIF,KAAK;EACxD,CAAC;EAEDE,KAAK,CAACJ,YAAY,GAAG,UAAUW,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIb,YAAY,GAAGW,CAAC,EAAEP,KAAK,IAAIJ,YAAY;EACpE,CAAC;EAED,SAASc,KAAKA,CAACC,WAAW,EAAE;IAC1B,OAAO,UAAUJ,CAAC,EAAE;MAClB,IAAIK,EAAE,EAAEC,EAAE;MACV,OAAOL,SAAS,CAACC,MAAM,IAAI,CAACG,EAAE,EAAEC,EAAE,CAAC,GAAGN,CAAC,EAAEX,YAAY,GAAGe,WAAW,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAEb,KAAK,IAAI,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IAC1H,CAAC;EACH;EAEAI,KAAK,CAACU,KAAK,GAAGA,KAAK,CAAC7B,MAAM,CAAC8B,WAAW,CAAC;EACvCX,KAAK,CAACc,UAAU,GAAGJ,KAAK,CAAC7B,MAAM,CAACkC,gBAAgB,CAAC;EAEjDf,KAAK,CAACD,OAAO,GAAG,UAAUQ,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIV,OAAO,GAAGQ,CAAC,EAAEP,KAAK,IAAID,OAAO;EAC1D,CAAC;EAED,OAAO,UAAUiB,CAAC,EAAE;IAClBrB,SAAS,GAAGqB,CAAC,EAAExB,EAAE,GAAGwB,CAAC,CAAC1B,EAAE,CAAC,EAAEG,EAAE,GAAGuB,CAAC,CAACzB,EAAE,CAAC,EAAEG,GAAG,GAAGF,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIA,EAAE,GAAGD,EAAE,CAAC;IAC1E,OAAOQ,KAAK;EACd,CAAC;AACH;AAEA,SAAS1B,IAAIA,CAAC2C,MAAM,EAAEC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACZ,MAAM,CAACW,MAAM,CAACX,MAAM,CAAC,CAAC,CAAC,CAACV,YAAY,CAACqB,MAAM,CAACrB,YAAY,CAAC,CAAC,CAAC,CAACE,KAAK,CAACmB,MAAM,CAACnB,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAACkB,MAAM,CAAClB,OAAO,CAAC,CAAC,CAAC;AAC3H;AAEA,SAASvB,UAAUA,CAAA,EAAG;EACpB,IAAIwB,KAAK,GAAG,CAAC,CAAC,EAAEf,OAAO,CAACkC,SAAS,EAAE9B,WAAW,CAAC,CAAC,CAACN,WAAW,CAACc,QAAQ,CAAC,CAAC;EAEvEG,KAAK,CAAC1B,IAAI,GAAG,YAAY;IACvB,OAAOA,IAAI,CAAC0B,KAAK,EAAExB,UAAU,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,OAAOQ,KAAK,CAACoC,gBAAgB,CAACC,KAAK,CAACrB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAAS/B,aAAaA,CAAA,EAAG;EACvB,IAAIuB,KAAK,GAAG,CAAC,CAAC,EAAEd,IAAI,CAACoC,OAAO,EAAEjC,WAAW,CAAC,CAAC,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAE5DN,KAAK,CAAC1B,IAAI,GAAG,YAAY;IACvB,OAAOA,IAAI,CAAC0B,KAAK,EAAEvB,aAAa,CAAC,CAAC,CAAC,CAAC8C,IAAI,CAACvB,KAAK,CAACuB,IAAI,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,OAAOvC,KAAK,CAACoC,gBAAgB,CAACC,KAAK,CAACrB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAAS5B,gBAAgBA,CAAA,EAAG;EAC1B,IAAIoB,KAAK,GAAG,CAAC,CAAC,EAAEb,OAAO,CAACqC,SAAS,EAAEnC,WAAW,CAAC,CAAC,CAAC;EAEjDW,KAAK,CAAC1B,IAAI,GAAG,YAAY;IACvB,OAAOA,IAAI,CAAC0B,KAAK,EAAEpB,gBAAgB,CAAC,CAAC,CAAC,CAAC6C,QAAQ,CAACzB,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EACnE,CAAC;EAED,OAAOzC,KAAK,CAACoC,gBAAgB,CAACC,KAAK,CAACrB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAAS9B,aAAaA,CAAA,EAAG;EACvB,IAAIsB,KAAK,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACsC,MAAM,EAAErC,WAAW,CAAC,CAAC,CAAC;EAE3CW,KAAK,CAAC1B,IAAI,GAAG,YAAY;IACvB,OAAOA,IAAI,CAAC0B,KAAK,EAAEtB,aAAa,CAAC,CAAC,CAAC,CAACiD,QAAQ,CAAC3B,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC;EAChE,CAAC;EAED,OAAO3C,KAAK,CAACoC,gBAAgB,CAACC,KAAK,CAACrB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAAS7B,cAAcA,CAAA,EAAG;EACxB,OAAOD,aAAa,CAAC2C,KAAK,CAAC,IAAI,EAAEb,SAAS,CAAC,CAACmB,QAAQ,CAAC,GAAG,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "script"}