{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nimport LocaleReceiver from '../../locale-provider/LocaleReceiver';\nimport defaultLocale from '../../locale/default';\nimport ConfirmDialog from '../ConfirmDialog';\nvar HookModal = function HookModal(_ref, ref) {\n  var afterClose = _ref.afterClose,\n    config = _ref.config;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    open = _React$useState2[0],\n    setOpen = _React$useState2[1];\n  var _React$useState3 = React.useState(config),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    innerConfig = _React$useState4[0],\n    setInnerConfig = _React$useState4[1];\n  var _React$useContext = React.useContext(ConfigContext),\n    direction = _React$useContext.direction,\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('modal');\n  var rootPrefixCls = getPrefixCls();\n  var close = function close() {\n    setOpen(false);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var triggerCancel = args.some(function (param) {\n      return param && param.triggerCancel;\n    });\n    if (innerConfig.onCancel && triggerCancel) {\n      innerConfig.onCancel.apply(innerConfig, [function () {}].concat(_toConsumableArray(args.slice(1))));\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      destroy: close,\n      update: function update(newConfig) {\n        setInnerConfig(function (originConfig) {\n          return _extends(_extends({}, originConfig), newConfig);\n        });\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Modal\",\n    defaultLocale: defaultLocale.Modal\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(ConfirmDialog, _extends({\n      prefixCls: prefixCls,\n      rootPrefixCls: rootPrefixCls\n    }, innerConfig, {\n      close: close,\n      open: open,\n      afterClose: afterClose,\n      okText: innerConfig.okText || (innerConfig.okCancel ? contextLocale.okText : contextLocale.justOkText),\n      direction: direction,\n      cancelText: innerConfig.cancelText || contextLocale.cancelText\n    }));\n  });\n};\nexport default /*#__PURE__*/React.forwardRef(HookModal);", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_slicedToArray", "React", "ConfigContext", "LocaleReceiver", "defaultLocale", "ConfirmDialog", "HookModal", "_ref", "ref", "afterClose", "config", "_React$useState", "useState", "_React$useState2", "open", "<PERSON><PERSON><PERSON>", "_React$useState3", "_React$useState4", "innerConfig", "setInnerConfig", "_React$useContext", "useContext", "direction", "getPrefixCls", "prefixCls", "rootPrefixCls", "close", "_len", "arguments", "length", "args", "Array", "_key", "triggerCancel", "some", "param", "onCancel", "apply", "concat", "slice", "useImperativeHandle", "destroy", "update", "newConfig", "originConfig", "createElement", "componentName", "Modal", "contextLocale", "okText", "okCancel", "justOkText", "cancelText", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/modal/useModal/HookModal.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nimport LocaleReceiver from '../../locale-provider/LocaleReceiver';\nimport defaultLocale from '../../locale/default';\nimport ConfirmDialog from '../ConfirmDialog';\nvar HookModal = function HookModal(_ref, ref) {\n  var afterClose = _ref.afterClose,\n    config = _ref.config;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    open = _React$useState2[0],\n    setOpen = _React$useState2[1];\n  var _React$useState3 = React.useState(config),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    innerConfig = _React$useState4[0],\n    setInnerConfig = _React$useState4[1];\n  var _React$useContext = React.useContext(ConfigContext),\n    direction = _React$useContext.direction,\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('modal');\n  var rootPrefixCls = getPrefixCls();\n  var close = function close() {\n    setOpen(false);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var triggerCancel = args.some(function (param) {\n      return param && param.triggerCancel;\n    });\n    if (innerConfig.onCancel && triggerCancel) {\n      innerConfig.onCancel.apply(innerConfig, [function () {}].concat(_toConsumableArray(args.slice(1))));\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      destroy: close,\n      update: function update(newConfig) {\n        setInnerConfig(function (originConfig) {\n          return _extends(_extends({}, originConfig), newConfig);\n        });\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Modal\",\n    defaultLocale: defaultLocale.Modal\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(ConfirmDialog, _extends({\n      prefixCls: prefixCls,\n      rootPrefixCls: rootPrefixCls\n    }, innerConfig, {\n      close: close,\n      open: open,\n      afterClose: afterClose,\n      okText: innerConfig.okText || (innerConfig.okCancel ? contextLocale.okText : contextLocale.justOkText),\n      direction: direction,\n      cancelText: innerConfig.cancelText || contextLocale.cancelText\n    }));\n  });\n};\nexport default /*#__PURE__*/React.forwardRef(HookModal);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC5C,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC9BC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGb,cAAc,CAACW,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC/B,IAAIG,gBAAgB,GAAGf,KAAK,CAACW,QAAQ,CAACF,MAAM,CAAC;IAC3CO,gBAAgB,GAAGjB,cAAc,CAACgB,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,iBAAiB,GAAGnB,KAAK,CAACoB,UAAU,CAACnB,aAAa,CAAC;IACrDoB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;EAC/C,IAAIC,SAAS,GAAGD,YAAY,CAAC,OAAO,CAAC;EACrC,IAAIE,aAAa,GAAGF,YAAY,CAAC,CAAC;EAClC,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3BX,OAAO,CAAC,KAAK,CAAC;IACd,KAAK,IAAIY,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACA,IAAIC,aAAa,GAAGH,IAAI,CAACI,IAAI,CAAC,UAAUC,KAAK,EAAE;MAC7C,OAAOA,KAAK,IAAIA,KAAK,CAACF,aAAa;IACrC,CAAC,CAAC;IACF,IAAIf,WAAW,CAACkB,QAAQ,IAAIH,aAAa,EAAE;MACzCf,WAAW,CAACkB,QAAQ,CAACC,KAAK,CAACnB,WAAW,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAACoB,MAAM,CAACvC,kBAAkB,CAAC+B,IAAI,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrG;EACF,CAAC;EACDtC,KAAK,CAACuC,mBAAmB,CAAChC,GAAG,EAAE,YAAY;IACzC,OAAO;MACLiC,OAAO,EAAEf,KAAK;MACdgB,MAAM,EAAE,SAASA,MAAMA,CAACC,SAAS,EAAE;QACjCxB,cAAc,CAAC,UAAUyB,YAAY,EAAE;UACrC,OAAO9C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8C,YAAY,CAAC,EAAED,SAAS,CAAC;QACxD,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAa1C,KAAK,CAAC4C,aAAa,CAAC1C,cAAc,EAAE;IACtD2C,aAAa,EAAE,OAAO;IACtB1C,aAAa,EAAEA,aAAa,CAAC2C;EAC/B,CAAC,EAAE,UAAUC,aAAa,EAAE;IAC1B,OAAO,aAAa/C,KAAK,CAAC4C,aAAa,CAACxC,aAAa,EAAEP,QAAQ,CAAC;MAC9D0B,SAAS,EAAEA,SAAS;MACpBC,aAAa,EAAEA;IACjB,CAAC,EAAEP,WAAW,EAAE;MACdQ,KAAK,EAAEA,KAAK;MACZZ,IAAI,EAAEA,IAAI;MACVL,UAAU,EAAEA,UAAU;MACtBwC,MAAM,EAAE/B,WAAW,CAAC+B,MAAM,KAAK/B,WAAW,CAACgC,QAAQ,GAAGF,aAAa,CAACC,MAAM,GAAGD,aAAa,CAACG,UAAU,CAAC;MACtG7B,SAAS,EAAEA,SAAS;MACpB8B,UAAU,EAAElC,WAAW,CAACkC,UAAU,IAAIJ,aAAa,CAACI;IACtD,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AACD,eAAe,aAAanD,KAAK,CAACoD,UAAU,CAAC/C,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}