{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcDays = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nvar _duration = require(\"./duration.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar utcDay = (0, _interval.default)(function (date) {\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCDate(date.getUTCDate() + step);\n}, function (start, end) {\n  return (end - start) / _duration.durationDay;\n}, function (date) {\n  return date.getUTCDate() - 1;\n});\nvar _default = utcDay;\nexports.default = _default;\nvar utcDays = utcDay.range;\nexports.utcDays = utcDays;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcDays", "default", "_interval", "_interopRequireDefault", "require", "_duration", "obj", "__esModule", "utcDay", "date", "setUTCHours", "step", "setUTCDate", "getUTCDate", "start", "end", "durationDay", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/utcDay.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcDays = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nvar _duration = require(\"./duration.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar utcDay = (0, _interval.default)(function (date) {\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCDate(date.getUTCDate() + step);\n}, function (start, end) {\n  return (end - start) / _duration.durationDay;\n}, function (date) {\n  return date.getUTCDate() - 1;\n});\nvar _default = utcDay;\nexports.default = _default;\nvar utcDays = utcDay.range;\nexports.utcDays = utcDays;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE1C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,MAAM,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACD,OAAO,EAAE,UAAUQ,IAAI,EAAE;EAClDA,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,UAAUD,IAAI,EAAEE,IAAI,EAAE;EACvBF,IAAI,CAACG,UAAU,CAACH,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC3C,CAAC,EAAE,UAAUG,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIT,SAAS,CAACW,WAAW;AAC9C,CAAC,EAAE,UAAUP,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACI,UAAU,CAAC,CAAC,GAAG,CAAC;AAC9B,CAAC,CAAC;AACF,IAAII,QAAQ,GAAGT,MAAM;AACrBV,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,OAAO,GAAGQ,MAAM,CAACU,KAAK;AAC1BpB,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}