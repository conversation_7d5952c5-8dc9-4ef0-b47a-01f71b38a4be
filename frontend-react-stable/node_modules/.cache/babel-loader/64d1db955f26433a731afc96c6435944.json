{"ast": null, "code": "var isObject = require('./isObject'),\n  now = require('./now'),\n  toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n  nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n    lastThis,\n    maxWait,\n    result,\n    timerId,\n    lastCallTime,\n    lastInvokeTime = 0,\n    leading = false,\n    maxing = false,\n    trailing = true;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  function invokeFunc(time) {\n    var args = lastArgs,\n      thisArg = lastThis;\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n      timeSinceLastInvoke = time - lastInvokeTime,\n      timeWaiting = wait - timeSinceLastCall;\n    return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;\n  }\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n      timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return lastCallTime === undefined || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;\n  }\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n  function debounced() {\n    var time = now(),\n      isInvoking = shouldInvoke(time);\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\nmodule.exports = debounce;", "map": {"version": 3, "names": ["isObject", "require", "now", "toNumber", "FUNC_ERROR_TEXT", "nativeMax", "Math", "max", "nativeMin", "min", "debounce", "func", "wait", "options", "lastArgs", "lastThis", "max<PERSON><PERSON>", "result", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "trailing", "TypeError", "invokeFunc", "time", "args", "thisArg", "undefined", "apply", "leading<PERSON>dge", "setTimeout", "timerExpired", "remainingWait", "timeSinceLastCall", "timeSinceLastInvoke", "timeWaiting", "shouldInvoke", "trailingEdge", "cancel", "clearTimeout", "flush", "debounced", "isInvoking", "arguments", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/debounce.js"], "sourcesContent": ["var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;EAChCC,GAAG,GAAGD,OAAO,CAAC,OAAO,CAAC;EACtBE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIG,eAAe,GAAG,qBAAqB;;AAE3C;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrC,IAAIC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,MAAM;IACNC,OAAO;IACPC,YAAY;IACZC,cAAc,GAAG,CAAC;IAClBC,OAAO,GAAG,KAAK;IACfC,MAAM,GAAG,KAAK;IACdC,QAAQ,GAAG,IAAI;EAEnB,IAAI,OAAOZ,IAAI,IAAI,UAAU,EAAE;IAC7B,MAAM,IAAIa,SAAS,CAACpB,eAAe,CAAC;EACtC;EACAQ,IAAI,GAAGT,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;EAC1B,IAAIZ,QAAQ,CAACa,OAAO,CAAC,EAAE;IACrBQ,OAAO,GAAG,CAAC,CAACR,OAAO,CAACQ,OAAO;IAC3BC,MAAM,GAAG,SAAS,IAAIT,OAAO;IAC7BG,OAAO,GAAGM,MAAM,GAAGjB,SAAS,CAACF,QAAQ,CAACU,OAAO,CAACG,OAAO,CAAC,IAAI,CAAC,EAAEJ,IAAI,CAAC,GAAGI,OAAO;IAC5EO,QAAQ,GAAG,UAAU,IAAIV,OAAO,GAAG,CAAC,CAACA,OAAO,CAACU,QAAQ,GAAGA,QAAQ;EAClE;EAEA,SAASE,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAIC,IAAI,GAAGb,QAAQ;MACfc,OAAO,GAAGb,QAAQ;IAEtBD,QAAQ,GAAGC,QAAQ,GAAGc,SAAS;IAC/BT,cAAc,GAAGM,IAAI;IACrBT,MAAM,GAAGN,IAAI,CAACmB,KAAK,CAACF,OAAO,EAAED,IAAI,CAAC;IAClC,OAAOV,MAAM;EACf;EAEA,SAASc,WAAWA,CAACL,IAAI,EAAE;IACzB;IACAN,cAAc,GAAGM,IAAI;IACrB;IACAR,OAAO,GAAGc,UAAU,CAACC,YAAY,EAAErB,IAAI,CAAC;IACxC;IACA,OAAOS,OAAO,GAAGI,UAAU,CAACC,IAAI,CAAC,GAAGT,MAAM;EAC5C;EAEA,SAASiB,aAAaA,CAACR,IAAI,EAAE;IAC3B,IAAIS,iBAAiB,GAAGT,IAAI,GAAGP,YAAY;MACvCiB,mBAAmB,GAAGV,IAAI,GAAGN,cAAc;MAC3CiB,WAAW,GAAGzB,IAAI,GAAGuB,iBAAiB;IAE1C,OAAOb,MAAM,GACTd,SAAS,CAAC6B,WAAW,EAAErB,OAAO,GAAGoB,mBAAmB,CAAC,GACrDC,WAAW;EACjB;EAEA,SAASC,YAAYA,CAACZ,IAAI,EAAE;IAC1B,IAAIS,iBAAiB,GAAGT,IAAI,GAAGP,YAAY;MACvCiB,mBAAmB,GAAGV,IAAI,GAAGN,cAAc;;IAE/C;IACA;IACA;IACA,OAAQD,YAAY,KAAKU,SAAS,IAAKM,iBAAiB,IAAIvB,IAAK,IAC9DuB,iBAAiB,GAAG,CAAE,IAAKb,MAAM,IAAIc,mBAAmB,IAAIpB,OAAQ;EACzE;EAEA,SAASiB,YAAYA,CAAA,EAAG;IACtB,IAAIP,IAAI,GAAGxB,GAAG,CAAC,CAAC;IAChB,IAAIoC,YAAY,CAACZ,IAAI,CAAC,EAAE;MACtB,OAAOa,YAAY,CAACb,IAAI,CAAC;IAC3B;IACA;IACAR,OAAO,GAAGc,UAAU,CAACC,YAAY,EAAEC,aAAa,CAACR,IAAI,CAAC,CAAC;EACzD;EAEA,SAASa,YAAYA,CAACb,IAAI,EAAE;IAC1BR,OAAO,GAAGW,SAAS;;IAEnB;IACA;IACA,IAAIN,QAAQ,IAAIT,QAAQ,EAAE;MACxB,OAAOW,UAAU,CAACC,IAAI,CAAC;IACzB;IACAZ,QAAQ,GAAGC,QAAQ,GAAGc,SAAS;IAC/B,OAAOZ,MAAM;EACf;EAEA,SAASuB,MAAMA,CAAA,EAAG;IAChB,IAAItB,OAAO,KAAKW,SAAS,EAAE;MACzBY,YAAY,CAACvB,OAAO,CAAC;IACvB;IACAE,cAAc,GAAG,CAAC;IAClBN,QAAQ,GAAGK,YAAY,GAAGJ,QAAQ,GAAGG,OAAO,GAAGW,SAAS;EAC1D;EAEA,SAASa,KAAKA,CAAA,EAAG;IACf,OAAOxB,OAAO,KAAKW,SAAS,GAAGZ,MAAM,GAAGsB,YAAY,CAACrC,GAAG,CAAC,CAAC,CAAC;EAC7D;EAEA,SAASyC,SAASA,CAAA,EAAG;IACnB,IAAIjB,IAAI,GAAGxB,GAAG,CAAC,CAAC;MACZ0C,UAAU,GAAGN,YAAY,CAACZ,IAAI,CAAC;IAEnCZ,QAAQ,GAAG+B,SAAS;IACpB9B,QAAQ,GAAG,IAAI;IACfI,YAAY,GAAGO,IAAI;IAEnB,IAAIkB,UAAU,EAAE;MACd,IAAI1B,OAAO,KAAKW,SAAS,EAAE;QACzB,OAAOE,WAAW,CAACZ,YAAY,CAAC;MAClC;MACA,IAAIG,MAAM,EAAE;QACV;QACAmB,YAAY,CAACvB,OAAO,CAAC;QACrBA,OAAO,GAAGc,UAAU,CAACC,YAAY,EAAErB,IAAI,CAAC;QACxC,OAAOa,UAAU,CAACN,YAAY,CAAC;MACjC;IACF;IACA,IAAID,OAAO,KAAKW,SAAS,EAAE;MACzBX,OAAO,GAAGc,UAAU,CAACC,YAAY,EAAErB,IAAI,CAAC;IAC1C;IACA,OAAOK,MAAM;EACf;EACA0B,SAAS,CAACH,MAAM,GAAGA,MAAM;EACzBG,SAAS,CAACD,KAAK,GAAGA,KAAK;EACvB,OAAOC,SAAS;AAClB;AAEAG,MAAM,CAACC,OAAO,GAAGrC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}