{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport ResponsiveObserve from '../../_util/responsiveObserve';\nfunction useBreakpoint() {\n  var refreshOnChange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var screensRef = useRef({});\n  var forceUpdate = useForceUpdate();\n  useEffect(function () {\n    var token = ResponsiveObserve.subscribe(function (supportScreens) {\n      screensRef.current = supportScreens;\n      if (refreshOnChange) {\n        forceUpdate();\n      }\n    });\n    return function () {\n      return ResponsiveObserve.unsubscribe(token);\n    };\n  }, []);\n  return screensRef.current;\n}\nexport default useBreakpoint;", "map": {"version": 3, "names": ["useEffect", "useRef", "useForceUpdate", "ResponsiveObserve", "useBreakpoint", "refreshOnChange", "arguments", "length", "undefined", "screensRef", "forceUpdate", "token", "subscribe", "supportScreens", "current", "unsubscribe"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/grid/hooks/useBreakpoint.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport ResponsiveObserve from '../../_util/responsiveObserve';\nfunction useBreakpoint() {\n  var refreshOnChange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var screensRef = useRef({});\n  var forceUpdate = useForceUpdate();\n  useEffect(function () {\n    var token = ResponsiveObserve.subscribe(function (supportScreens) {\n      screensRef.current = supportScreens;\n      if (refreshOnChange) {\n        forceUpdate();\n      }\n    });\n    return function () {\n      return ResponsiveObserve.unsubscribe(token);\n    };\n  }, []);\n  return screensRef.current;\n}\nexport default useBreakpoint;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9F,IAAIG,UAAU,GAAGR,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,IAAIS,WAAW,GAAGR,cAAc,CAAC,CAAC;EAClCF,SAAS,CAAC,YAAY;IACpB,IAAIW,KAAK,GAAGR,iBAAiB,CAACS,SAAS,CAAC,UAAUC,cAAc,EAAE;MAChEJ,UAAU,CAACK,OAAO,GAAGD,cAAc;MACnC,IAAIR,eAAe,EAAE;QACnBK,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF,OAAO,YAAY;MACjB,OAAOP,iBAAiB,CAACY,WAAW,CAACJ,KAAK,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,UAAU,CAACK,OAAO;AAC3B;AACA,eAAeV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}