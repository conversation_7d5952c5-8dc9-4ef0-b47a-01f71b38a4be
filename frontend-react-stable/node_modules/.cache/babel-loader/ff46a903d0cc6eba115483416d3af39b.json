{"ast": null, "code": "\"use strict\";\n\nvar _CSSTransition = _interopRequireDefault(require(\"./CSSTransition\"));\nvar _ReplaceTransition = _interopRequireDefault(require(\"./ReplaceTransition\"));\nvar _TransitionGroup = _interopRequireDefault(require(\"./TransitionGroup\"));\nvar _Transition = _interopRequireDefault(require(\"./Transition\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nmodule.exports = {\n  Transition: _Transition.default,\n  TransitionGroup: _TransitionGroup.default,\n  ReplaceTransition: _ReplaceTransition.default,\n  CSSTransition: _CSSTransition.default\n};", "map": {"version": 3, "names": ["_CSSTransition", "_interopRequireDefault", "require", "_ReplaceTransition", "_TransitionGroup", "_Transition", "obj", "__esModule", "default", "module", "exports", "Transition", "TransitionGroup", "ReplaceTransition", "CSSTransition"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-transition-group/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _CSSTransition = _interopRequireDefault(require(\"./CSSTransition\"));\n\nvar _ReplaceTransition = _interopRequireDefault(require(\"./ReplaceTransition\"));\n\nvar _TransitionGroup = _interopRequireDefault(require(\"./TransitionGroup\"));\n\nvar _Transition = _interopRequireDefault(require(\"./Transition\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nmodule.exports = {\n  Transition: _Transition.default,\n  TransitionGroup: _TransitionGroup.default,\n  ReplaceTransition: _ReplaceTransition.default,\n  CSSTransition: _CSSTransition.default\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEvE,IAAIC,kBAAkB,GAAGF,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAE/E,IAAIE,gBAAgB,GAAGH,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE3E,IAAIG,WAAW,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAEjE,SAASD,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9FG,MAAM,CAACC,OAAO,GAAG;EACfC,UAAU,EAAEN,WAAW,CAACG,OAAO;EAC/BI,eAAe,EAAER,gBAAgB,CAACI,OAAO;EACzCK,iBAAiB,EAAEV,kBAAkB,CAACK,OAAO;EAC7CM,aAAa,EAAEd,cAAc,CAACQ;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}