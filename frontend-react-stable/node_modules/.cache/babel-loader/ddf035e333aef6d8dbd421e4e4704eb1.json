{"ast": null, "code": "import Context from './components/Context';\nexport * from './icons';\nexport * from './components/twoTonePrimaryColor';\nexport { default as createFromIconfontCN } from './components/IconFont';\nexport { default } from './components/Icon';\nvar IconProvider = Context.Provider;\nexport { IconProvider };", "map": {"version": 3, "names": ["Context", "default", "createFromIconfontCN", "IconProvider", "Provider"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/index.js"], "sourcesContent": ["import Context from './components/Context';\nexport * from './icons';\nexport * from './components/twoTonePrimaryColor';\nexport { default as createFromIconfontCN } from './components/IconFont';\nexport { default } from './components/Icon';\nvar IconProvider = Context.Provider;\nexport { IconProvider };"], "mappings": "AAAA,OAAOA,OAAO,MAAM,sBAAsB;AAC1C,cAAc,SAAS;AACvB,cAAc,kCAAkC;AAChD,SAASC,OAAO,IAAIC,oBAAoB,QAAQ,uBAAuB;AACvE,SAASD,OAAO,QAAQ,mBAAmB;AAC3C,IAAIE,YAAY,GAAGH,OAAO,CAACI,QAAQ;AACnC,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}