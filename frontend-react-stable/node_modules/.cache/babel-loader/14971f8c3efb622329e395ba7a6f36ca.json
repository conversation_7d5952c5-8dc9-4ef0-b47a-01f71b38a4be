{"ast": null, "code": "import _isNil from \"lodash/isNil\";\nimport _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"value\", \"background\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Render a group of bar\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport Animate from 'react-smooth';\nimport { Rectangle } from '../shape/Rectangle';\nimport { Layer } from '../container/Layer';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { uniqueId, mathSign, interpolateNumber } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, findPositionOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Bar = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Bar, _PureComponent);\n  var _super = _createSuper(Bar);\n  function Bar() {\n    var _this;\n    _classCallCheck(this, Bar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-bar-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (onAnimationStart) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Bar, [{\n    key: \"renderRectanglesStatically\",\n    value: function renderRectanglesStatically(data) {\n      var _this2 = this;\n      var shape = this.props.shape;\n      var baseProps = filterProps(this.props);\n      return data && data.map(function (entry, i) {\n        var props = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n          index: i\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-bar-rectangle\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"rectangle-\".concat(i) // eslint-disable-line react/no-array-index-key\n          ,\n\n          role: \"img\"\n        }), Bar.renderRectangle(shape, props));\n      });\n    }\n  }, {\n    key: \"renderRectanglesWithAnimation\",\n    value: function renderRectanglesWithAnimation() {\n      var _this3 = this;\n      var _this$props = this.props,\n        data = _this$props.data,\n        layout = _this$props.layout,\n        isAnimationActive = _this$props.isAnimationActive,\n        animationBegin = _this$props.animationBegin,\n        animationDuration = _this$props.animationDuration,\n        animationEasing = _this$props.animationEasing,\n        animationId = _this$props.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"bar-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorX = interpolateNumber(prev.x, entry.x);\n            var interpolatorY = interpolateNumber(prev.y, entry.y);\n            var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n            var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolatorX(t),\n              y: interpolatorY(t),\n              width: interpolatorWidth(t),\n              height: interpolatorHeight(t)\n            });\n          }\n          if (layout === 'horizontal') {\n            var _interpolatorHeight = interpolateNumber(0, entry.height);\n            var h = _interpolatorHeight(t);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              y: entry.y + entry.height - h,\n              height: h\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.width);\n          var w = interpolator(t);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            width: w\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderRectanglesStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderRectangles\",\n    value: function renderRectangles() {\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        isAnimationActive = _this$props2.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !_isEqual(prevData, data))) {\n        return this.renderRectanglesWithAnimation();\n      }\n      return this.renderRectanglesStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this4 = this;\n      var data = this.props.data;\n      var backgroundProps = filterProps(this.props.background);\n      return data.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          index: i,\n          key: \"background-bar-\".concat(i),\n          className: 'recharts-bar-background-rectangle'\n        });\n        return Bar.renderRectangle(_this4.props.background, props);\n      });\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        xAxis = _this$props3.xAxis,\n        yAxis = _this$props3.yAxis,\n        layout = _this$props3.layout,\n        children = _this$props3.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var offset = layout === 'vertical' ? data[0].height / 2 : data[0].width / 2;\n      function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item, i) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"error-bar-\".concat(i),\n          // eslint-disable-line react/no-array-index-key\n          data: data,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          offset: offset,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        hide = _this$props4.hide,\n        data = _this$props4.data,\n        className = _this$props4.className,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        left = _this$props4.left,\n        top = _this$props4.top,\n        width = _this$props4.width,\n        height = _this$props4.height,\n        isAnimationActive = _this$props4.isAnimationActive,\n        background = _this$props4.background,\n        id = _this$props4.id;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = classNames('recharts-bar', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-bar-rectangles\",\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, background ? this.renderBackground() : null, this.renderRectangles()), this.renderErrorBar(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"renderRectangle\",\n    value: function renderRectangle(option, props) {\n      var rectangle;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = /*#__PURE__*/React.createElement(Rectangle, props);\n      }\n      return rectangle;\n    }\n  }]);\n  return Bar;\n}(PureComponent);\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'rect',\n  minPointSize: 0,\n  hide: false,\n  // data of bar\n  data: [],\n  layout: 'vertical',\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease'\n});\n_defineProperty(Bar, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var layout = props.layout;\n  var _item$props = item.props,\n    dataKey = _item$props.dataKey,\n    children = _item$props.children,\n    minPointSize = _item$props.minPointSize;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var rects = displayedData.map(function (entry, index) {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!_isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'horizontal') {\n      var _ref4;\n      var _ref3 = [yAxis.scale(value[0]), yAxis.scale(value[1])],\n        baseValueScale = _ref3[0],\n        currentValueScale = _ref3[1];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      y = (_ref4 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref4 !== void 0 ? _ref4 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = Number.isNaN(computedHeight) ? 0 : computedHeight;\n      background = {\n        x: x,\n        y: yAxis.y,\n        width: width,\n        height: yAxis.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var _ref5 = [xAxis.scale(value[0]), xAxis.scale(value[1])],\n        _baseValueScale = _ref5[0],\n        _currentValueScale = _ref5[1];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: xAxis.x,\n        y: y,\n        width: xAxis.width,\n        height: height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), {}, {\n      x: x,\n      y: y,\n      width: width,\n      height: height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background: background\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    });\n  });\n  return _objectSpread({\n    data: rects,\n    layout: layout\n  }, offset);\n});", "map": {"version": 3, "names": ["_isNil", "_isEqual", "_isFunction", "_isArray", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "_extends", "assign", "bind", "arguments", "hasOwnProperty", "apply", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "classNames", "Animate", "Rectangle", "Layer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cell", "LabelList", "uniqueId", "mathSign", "interpolateNumber", "filterProps", "findAllByType", "Global", "getCateCoordinateOfBar", "getValueByDataKey", "truncateByDomain", "getBaseValueOfBar", "findPositionOfBar", "getTooltipItem", "adaptEventsOfChild", "Bar", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "onAnimationEnd", "setState", "onAnimationStart", "renderRectanglesStatically", "data", "_this2", "shape", "baseProps", "map", "entry", "index", "createElement", "className", "role", "renderRectangle", "renderRectanglesWithAnimation", "_this3", "_this$props", "layout", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animationId", "prevData", "state", "begin", "duration", "isActive", "easing", "from", "t", "to", "handleAnimationEnd", "handleAnimationStart", "_ref", "stepData", "prev", "interpolatorX", "x", "interpolatorY", "y", "interpolatorWidth", "width", "interpolatorHeight", "height", "_interpolatorHeight", "h", "interpolator", "w", "renderRectangles", "_this$props2", "renderBackground", "_this4", "backgroundProps", "background", "rest", "fill", "renderErrorBar", "needClip", "clipPathId", "_this$props3", "xAxis", "yAxis", "children", "errorBarItems", "offset", "dataPointFormatter", "dataPoint", "dataKey", "errorVal", "errorBarProps", "clipPath", "item", "cloneElement", "render", "_this$props4", "hide", "left", "top", "id", "layerClass", "allowDataOverflow", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curData", "option", "rectangle", "isValidElement", "xAxisId", "yAxisId", "legendType", "minPointSize", "isSsr", "_ref2", "barPosition", "bandSize", "xAxisTicks", "yAxisTicks", "stackedData", "dataStartIndex", "displayedData", "pos", "_item$props", "numericAxis", "stackedDomain", "scale", "domain", "baseValue", "cells", "rects", "_ref4", "_ref3", "baseValueScale", "currentValueScale", "axis", "ticks", "size", "computedHeight", "isNaN", "Math", "abs", "delta", "_ref5", "_baseValueScale", "_currentValueScale", "_delta", "payload", "tooltipPayload", "tooltipPosition"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/Bar.js"], "sourcesContent": ["import _isNil from \"lodash/isNil\";\nimport _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"value\", \"background\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Render a group of bar\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport Animate from 'react-smooth';\nimport { Rectangle } from '../shape/Rectangle';\nimport { Layer } from '../container/Layer';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { uniqueId, mathSign, interpolateNumber } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, findPositionOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Bar = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Bar, _PureComponent);\n  var _super = _createSuper(Bar);\n  function Bar() {\n    var _this;\n    _classCallCheck(this, Bar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-bar-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (onAnimationStart) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Bar, [{\n    key: \"renderRectanglesStatically\",\n    value: function renderRectanglesStatically(data) {\n      var _this2 = this;\n      var shape = this.props.shape;\n      var baseProps = filterProps(this.props);\n      return data && data.map(function (entry, i) {\n        var props = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n          index: i\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-bar-rectangle\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"rectangle-\".concat(i) // eslint-disable-line react/no-array-index-key\n          ,\n          role: \"img\"\n        }), Bar.renderRectangle(shape, props));\n      });\n    }\n  }, {\n    key: \"renderRectanglesWithAnimation\",\n    value: function renderRectanglesWithAnimation() {\n      var _this3 = this;\n      var _this$props = this.props,\n        data = _this$props.data,\n        layout = _this$props.layout,\n        isAnimationActive = _this$props.isAnimationActive,\n        animationBegin = _this$props.animationBegin,\n        animationDuration = _this$props.animationDuration,\n        animationEasing = _this$props.animationEasing,\n        animationId = _this$props.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"bar-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorX = interpolateNumber(prev.x, entry.x);\n            var interpolatorY = interpolateNumber(prev.y, entry.y);\n            var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n            var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolatorX(t),\n              y: interpolatorY(t),\n              width: interpolatorWidth(t),\n              height: interpolatorHeight(t)\n            });\n          }\n          if (layout === 'horizontal') {\n            var _interpolatorHeight = interpolateNumber(0, entry.height);\n            var h = _interpolatorHeight(t);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              y: entry.y + entry.height - h,\n              height: h\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.width);\n          var w = interpolator(t);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            width: w\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderRectanglesStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderRectangles\",\n    value: function renderRectangles() {\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        isAnimationActive = _this$props2.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !_isEqual(prevData, data))) {\n        return this.renderRectanglesWithAnimation();\n      }\n      return this.renderRectanglesStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this4 = this;\n      var data = this.props.data;\n      var backgroundProps = filterProps(this.props.background);\n      return data.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          index: i,\n          key: \"background-bar-\".concat(i),\n          className: 'recharts-bar-background-rectangle'\n        });\n        return Bar.renderRectangle(_this4.props.background, props);\n      });\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        xAxis = _this$props3.xAxis,\n        yAxis = _this$props3.yAxis,\n        layout = _this$props3.layout,\n        children = _this$props3.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var offset = layout === 'vertical' ? data[0].height / 2 : data[0].width / 2;\n      function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item, i) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"error-bar-\".concat(i),\n          // eslint-disable-line react/no-array-index-key\n          data: data,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          offset: offset,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        hide = _this$props4.hide,\n        data = _this$props4.data,\n        className = _this$props4.className,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        left = _this$props4.left,\n        top = _this$props4.top,\n        width = _this$props4.width,\n        height = _this$props4.height,\n        isAnimationActive = _this$props4.isAnimationActive,\n        background = _this$props4.background,\n        id = _this$props4.id;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = classNames('recharts-bar', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-bar-rectangles\",\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, background ? this.renderBackground() : null, this.renderRectangles()), this.renderErrorBar(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"renderRectangle\",\n    value: function renderRectangle(option, props) {\n      var rectangle;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = /*#__PURE__*/React.createElement(Rectangle, props);\n      }\n      return rectangle;\n    }\n  }]);\n  return Bar;\n}(PureComponent);\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'rect',\n  minPointSize: 0,\n  hide: false,\n  // data of bar\n  data: [],\n  layout: 'vertical',\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease'\n});\n_defineProperty(Bar, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var layout = props.layout;\n  var _item$props = item.props,\n    dataKey = _item$props.dataKey,\n    children = _item$props.children,\n    minPointSize = _item$props.minPointSize;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var rects = displayedData.map(function (entry, index) {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!_isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'horizontal') {\n      var _ref4;\n      var _ref3 = [yAxis.scale(value[0]), yAxis.scale(value[1])],\n        baseValueScale = _ref3[0],\n        currentValueScale = _ref3[1];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      y = (_ref4 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref4 !== void 0 ? _ref4 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = Number.isNaN(computedHeight) ? 0 : computedHeight;\n      background = {\n        x: x,\n        y: yAxis.y,\n        width: width,\n        height: yAxis.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var _ref5 = [xAxis.scale(value[0]), xAxis.scale(value[1])],\n        _baseValueScale = _ref5[0],\n        _currentValueScale = _ref5[1];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: xAxis.x,\n        y: y,\n        width: xAxis.width,\n        height: height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), {}, {\n      x: x,\n      y: y,\n      width: width,\n      height: height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background: background\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    });\n  });\n  return _objectSpread({\n    data: rects,\n    layout: layout\n  }, offset);\n});"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;AACvC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT,SAASa,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUf,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGkB,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACqB,cAAc,CAACP,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOa,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AAAE;AAClV,SAASG,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIT,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACQ,MAAM,CAAC;EAAE,IAAIhB,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIiB,OAAO,GAAGlB,MAAM,CAACC,qBAAqB,CAACe,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOpB,MAAM,CAACqB,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEd,IAAI,CAACe,IAAI,CAACT,KAAK,CAACN,IAAI,EAAEU,OAAO,CAAC;EAAE;EAAE,OAAOV,IAAI;AAAE;AACpV,SAASgB,aAAaA,CAAC5B,MAAM,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIL,MAAM,GAAG,IAAI,IAAIkB,SAAS,CAACb,CAAC,CAAC,GAAGa,SAAS,CAACb,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAE4B,eAAe,CAAC9B,MAAM,EAAEE,GAAG,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAAC2B,yBAAyB,GAAG3B,MAAM,CAAC4B,gBAAgB,CAAChC,MAAM,EAAEI,MAAM,CAAC2B,yBAAyB,CAACjC,MAAM,CAAC,CAAC,GAAGqB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAEE,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAEE,GAAG,EAAEE,MAAM,CAACqB,wBAAwB,CAAC3B,MAAM,EAAEI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AACzf,SAASkC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACtC,MAAM,EAAEuC,KAAK,EAAE;EAAE,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAChC,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIqC,UAAU,GAAGD,KAAK,CAACpC,CAAC,CAAC;IAAEqC,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEtC,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAE2C,cAAc,CAACH,UAAU,CAACtC,GAAG,CAAC,EAAEsC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACxC,SAAS,EAAEiD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAE1C,MAAM,CAAC6B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAACpD,SAAS,GAAGQ,MAAM,CAAC8C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACrD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEwD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAErC,MAAM,CAAC6B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGhD,MAAM,CAACmD,cAAc,GAAGnD,MAAM,CAACmD,cAAc,CAACxC,IAAI,CAAC,CAAC,GAAG,SAASqC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACpE,WAAW;MAAEqE,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE9C,SAAS,EAAEiD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC5C,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAAE;IAAE,OAAOoD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE3D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI2B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAAC9E,SAAS,CAAC+E,OAAO,CAACjE,IAAI,CAACwD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG3D,MAAM,CAACmD,cAAc,GAAGnD,MAAM,CAACyE,cAAc,CAAC9D,IAAI,CAAC,CAAC,GAAG,SAASgD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAIpD,MAAM,CAACyE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAACtC,GAAG,EAAEU,GAAG,EAAEiD,KAAK,EAAE;EAAEjD,GAAG,GAAGyC,cAAc,CAACzC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIV,GAAG,EAAE;IAAEY,MAAM,CAAC6B,cAAc,CAACzC,GAAG,EAAEU,GAAG,EAAE;MAAEiD,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAElD,GAAG,CAACU,GAAG,CAAC,GAAGiD,KAAK;EAAE;EAAE,OAAO3D,GAAG;AAAE;AAC3O,SAASmD,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAI5E,GAAG,GAAG6E,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOvF,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAG8E,MAAM,CAAC9E,GAAG,CAAC;AAAE;AAC5H,SAAS6E,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI3F,OAAO,CAAC0F,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACxF,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACzE,IAAI,CAACuE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI3F,OAAO,CAAC+F,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,cAAc;AAClC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AACzE,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,sBAAsB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,oBAAoB;AACtJ,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAO,IAAIC,GAAG,GAAG,aAAa,UAAUC,cAAc,EAAE;EACtDhE,SAAS,CAAC+D,GAAG,EAAEC,cAAc,CAAC;EAC9B,IAAIC,MAAM,GAAGvD,YAAY,CAACqD,GAAG,CAAC;EAC9B,SAASA,GAAGA,CAAA,EAAG;IACb,IAAIG,KAAK;IACT/E,eAAe,CAAC,IAAI,EAAE4E,GAAG,CAAC;IAC1B,KAAK,IAAII,IAAI,GAAGlG,SAAS,CAACT,MAAM,EAAE4G,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGrG,SAAS,CAACqG,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAACtG,IAAI,CAACQ,KAAK,CAAC8F,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDrF,eAAe,CAACwC,sBAAsB,CAAC2C,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACFzF,eAAe,CAACwC,sBAAsB,CAAC2C,KAAK,CAAC,EAAE,IAAI,EAAEhB,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC/EnE,eAAe,CAACwC,sBAAsB,CAAC2C,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIO,cAAc,GAAGP,KAAK,CAAC1E,KAAK,CAACiF,cAAc;MAC/CP,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIC,cAAc,EAAE;QAClBA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF1F,eAAe,CAACwC,sBAAsB,CAAC2C,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjF,IAAIS,gBAAgB,GAAGT,KAAK,CAAC1E,KAAK,CAACmF,gBAAgB;MACnDT,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIG,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOT,KAAK;EACd;EACArE,YAAY,CAACkE,GAAG,EAAE,CAAC;IACjB5G,GAAG,EAAE,4BAA4B;IACjCiD,KAAK,EAAE,SAASwE,0BAA0BA,CAACC,IAAI,EAAE;MAC/C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,KAAK,GAAG,IAAI,CAACvF,KAAK,CAACuF,KAAK;MAC5B,IAAIC,SAAS,GAAG3B,WAAW,CAAC,IAAI,CAAC7D,KAAK,CAAC;MACvC,OAAOqF,IAAI,IAAIA,IAAI,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE9H,CAAC,EAAE;QAC1C,IAAIoC,KAAK,GAAGX,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmG,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAChFC,KAAK,EAAE/H;QACT,CAAC,CAAC;QACF,OAAO,aAAaqF,KAAK,CAAC2C,aAAa,CAACtC,KAAK,EAAEhF,QAAQ,CAAC;UACtDuH,SAAS,EAAE;QACb,CAAC,EAAEvB,kBAAkB,CAACgB,MAAM,CAACtF,KAAK,EAAE0F,KAAK,EAAE9H,CAAC,CAAC,EAAE;UAC7CD,GAAG,EAAE,YAAY,CAACoH,MAAM,CAACnH,CAAC,CAAC,CAAC;UAAA;;UAE5BkI,IAAI,EAAE;QACR,CAAC,CAAC,EAAEvB,GAAG,CAACwB,eAAe,CAACR,KAAK,EAAEvF,KAAK,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrC,GAAG,EAAE,+BAA+B;IACpCiD,KAAK,EAAE,SAASoF,6BAA6BA,CAAA,EAAG;MAC9C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,WAAW,GAAG,IAAI,CAAClG,KAAK;QAC1BqF,IAAI,GAAGa,WAAW,CAACb,IAAI;QACvBc,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB;QACjDC,cAAc,GAAGH,WAAW,CAACG,cAAc;QAC3CC,iBAAiB,GAAGJ,WAAW,CAACI,iBAAiB;QACjDC,eAAe,GAAGL,WAAW,CAACK,eAAe;QAC7CC,WAAW,GAAGN,WAAW,CAACM,WAAW;MACvC,IAAIC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,OAAO,aAAaxD,KAAK,CAAC2C,aAAa,CAACxC,OAAO,EAAE;QAC/CuD,KAAK,EAAEN,cAAc;QACrBO,QAAQ,EAAEN,iBAAiB;QAC3BO,QAAQ,EAAET,iBAAiB;QAC3BU,MAAM,EAAEP,eAAe;QACvBQ,IAAI,EAAE;UACJC,CAAC,EAAE;QACL,CAAC;QACDC,EAAE,EAAE;UACFD,CAAC,EAAE;QACL,CAAC;QACDrJ,GAAG,EAAE,MAAM,CAACoH,MAAM,CAACyB,WAAW,CAAC;QAC/BvB,cAAc,EAAE,IAAI,CAACiC,kBAAkB;QACvC/B,gBAAgB,EAAE,IAAI,CAACgC;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIJ,CAAC,GAAGI,IAAI,CAACJ,CAAC;QACd,IAAIK,QAAQ,GAAGhC,IAAI,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;UAC9C,IAAI2B,IAAI,GAAGb,QAAQ,IAAIA,QAAQ,CAACd,KAAK,CAAC;UACtC,IAAI2B,IAAI,EAAE;YACR,IAAIC,aAAa,GAAG3D,iBAAiB,CAAC0D,IAAI,CAACE,CAAC,EAAE9B,KAAK,CAAC8B,CAAC,CAAC;YACtD,IAAIC,aAAa,GAAG7D,iBAAiB,CAAC0D,IAAI,CAACI,CAAC,EAAEhC,KAAK,CAACgC,CAAC,CAAC;YACtD,IAAIC,iBAAiB,GAAG/D,iBAAiB,CAAC0D,IAAI,CAACM,KAAK,EAAElC,KAAK,CAACkC,KAAK,CAAC;YAClE,IAAIC,kBAAkB,GAAGjE,iBAAiB,CAAC0D,IAAI,CAACQ,MAAM,EAAEpC,KAAK,CAACoC,MAAM,CAAC;YACrE,OAAOzI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjD8B,CAAC,EAAED,aAAa,CAACP,CAAC,CAAC;cACnBU,CAAC,EAAED,aAAa,CAACT,CAAC,CAAC;cACnBY,KAAK,EAAED,iBAAiB,CAACX,CAAC,CAAC;cAC3Bc,MAAM,EAAED,kBAAkB,CAACb,CAAC;YAC9B,CAAC,CAAC;UACJ;UACA,IAAIb,MAAM,KAAK,YAAY,EAAE;YAC3B,IAAI4B,mBAAmB,GAAGnE,iBAAiB,CAAC,CAAC,EAAE8B,KAAK,CAACoC,MAAM,CAAC;YAC5D,IAAIE,CAAC,GAAGD,mBAAmB,CAACf,CAAC,CAAC;YAC9B,OAAO3H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDgC,CAAC,EAAEhC,KAAK,CAACgC,CAAC,GAAGhC,KAAK,CAACoC,MAAM,GAAGE,CAAC;cAC7BF,MAAM,EAAEE;YACV,CAAC,CAAC;UACJ;UACA,IAAIC,YAAY,GAAGrE,iBAAiB,CAAC,CAAC,EAAE8B,KAAK,CAACkC,KAAK,CAAC;UACpD,IAAIM,CAAC,GAAGD,YAAY,CAACjB,CAAC,CAAC;UACvB,OAAO3H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDkC,KAAK,EAAEM;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAajF,KAAK,CAAC2C,aAAa,CAACtC,KAAK,EAAE,IAAI,EAAE2C,MAAM,CAACb,0BAA0B,CAACiC,QAAQ,CAAC,CAAC;MACnG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1J,GAAG,EAAE,kBAAkB;IACvBiD,KAAK,EAAE,SAASuH,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,YAAY,GAAG,IAAI,CAACpI,KAAK;QAC3BqF,IAAI,GAAG+C,YAAY,CAAC/C,IAAI;QACxBe,iBAAiB,GAAGgC,YAAY,CAAChC,iBAAiB;MACpD,IAAIK,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,IAAIL,iBAAiB,IAAIf,IAAI,IAAIA,IAAI,CAACrH,MAAM,KAAK,CAACyI,QAAQ,IAAI,CAAC7J,QAAQ,CAAC6J,QAAQ,EAAEpB,IAAI,CAAC,CAAC,EAAE;QACxF,OAAO,IAAI,CAACW,6BAA6B,CAAC,CAAC;MAC7C;MACA,OAAO,IAAI,CAACZ,0BAA0B,CAACC,IAAI,CAAC;IAC9C;EACF,CAAC,EAAE;IACD1H,GAAG,EAAE,kBAAkB;IACvBiD,KAAK,EAAE,SAASyH,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIjD,IAAI,GAAG,IAAI,CAACrF,KAAK,CAACqF,IAAI;MAC1B,IAAIkD,eAAe,GAAG1E,WAAW,CAAC,IAAI,CAAC7D,KAAK,CAACwI,UAAU,CAAC;MACxD,OAAOnD,IAAI,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE9H,CAAC,EAAE;QAClC,IAAIgD,KAAK,GAAG8E,KAAK,CAAC9E,KAAK;UACrB4H,UAAU,GAAG9C,KAAK,CAAC8C,UAAU;UAC7BC,IAAI,GAAGnL,wBAAwB,CAACoI,KAAK,EAAE3I,SAAS,CAAC;QACnD,IAAI,CAACyL,UAAU,EAAE;UACf,OAAO,IAAI;QACb;QACA,IAAIxI,KAAK,GAAGX,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoJ,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/FC,IAAI,EAAE;QACR,CAAC,EAAEF,UAAU,CAAC,EAAED,eAAe,CAAC,EAAEjE,kBAAkB,CAACgE,MAAM,CAACtI,KAAK,EAAE0F,KAAK,EAAE9H,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACjF+H,KAAK,EAAE/H,CAAC;UACRD,GAAG,EAAE,iBAAiB,CAACoH,MAAM,CAACnH,CAAC,CAAC;UAChCiI,SAAS,EAAE;QACb,CAAC,CAAC;QACF,OAAOtB,GAAG,CAACwB,eAAe,CAACuC,MAAM,CAACtI,KAAK,CAACwI,UAAU,EAAExI,KAAK,CAAC;MAC5D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrC,GAAG,EAAE,gBAAgB;IACrBiD,KAAK,EAAE,SAAS+H,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;MACnD,IAAI,IAAI,CAAC7I,KAAK,CAACoG,iBAAiB,IAAI,CAAC,IAAI,CAACM,KAAK,CAAC1B,mBAAmB,EAAE;QACnE,OAAO,IAAI;MACb;MACA,IAAI8D,YAAY,GAAG,IAAI,CAAC9I,KAAK;QAC3BqF,IAAI,GAAGyD,YAAY,CAACzD,IAAI;QACxB0D,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,KAAK,GAAGF,YAAY,CAACE,KAAK;QAC1B7C,MAAM,GAAG2C,YAAY,CAAC3C,MAAM;QAC5B8C,QAAQ,GAAGH,YAAY,CAACG,QAAQ;MAClC,IAAIC,aAAa,GAAGpF,aAAa,CAACmF,QAAQ,EAAE1F,QAAQ,CAAC;MACrD,IAAI,CAAC2F,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,IAAIC,MAAM,GAAGhD,MAAM,KAAK,UAAU,GAAGd,IAAI,CAAC,CAAC,CAAC,CAACyC,MAAM,GAAG,CAAC,GAAGzC,IAAI,CAAC,CAAC,CAAC,CAACuC,KAAK,GAAG,CAAC;MAC3E,SAASwB,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAE;QAC9C,OAAO;UACL9B,CAAC,EAAE6B,SAAS,CAAC7B,CAAC;UACdE,CAAC,EAAE2B,SAAS,CAAC3B,CAAC;UACd9G,KAAK,EAAEyI,SAAS,CAACzI,KAAK;UACtB2I,QAAQ,EAAEtF,iBAAiB,CAACoF,SAAS,EAAEC,OAAO;QAChD,CAAC;MACH;MACA,IAAIE,aAAa,GAAG;QAClBC,QAAQ,EAAEb,QAAQ,GAAG,gBAAgB,CAAC7D,MAAM,CAAC8D,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAa5F,KAAK,CAAC2C,aAAa,CAACtC,KAAK,EAAEkG,aAAa,EAAEN,aAAa,CAACzD,GAAG,CAAC,UAAUiE,IAAI,EAAE9L,CAAC,EAAE;QACjG,OAAO,aAAaqF,KAAK,CAAC0G,YAAY,CAACD,IAAI,EAAE;UAC3C/L,GAAG,EAAE,YAAY,CAACoH,MAAM,CAACnH,CAAC,CAAC;UAC3B;UACAyH,IAAI,EAAEA,IAAI;UACV0D,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZ7C,MAAM,EAAEA,MAAM;UACdgD,MAAM,EAAEA,MAAM;UACdC,kBAAkB,EAAEA;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDzL,GAAG,EAAE,QAAQ;IACbiD,KAAK,EAAE,SAASgJ,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC7J,KAAK;QAC3B8J,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBzE,IAAI,GAAGwE,YAAY,CAACxE,IAAI;QACxBQ,SAAS,GAAGgE,YAAY,CAAChE,SAAS;QAClCkD,KAAK,GAAGc,YAAY,CAACd,KAAK;QAC1BC,KAAK,GAAGa,YAAY,CAACb,KAAK;QAC1Be,IAAI,GAAGF,YAAY,CAACE,IAAI;QACxBC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBpC,KAAK,GAAGiC,YAAY,CAACjC,KAAK;QAC1BE,MAAM,GAAG+B,YAAY,CAAC/B,MAAM;QAC5B1B,iBAAiB,GAAGyD,YAAY,CAACzD,iBAAiB;QAClDoC,UAAU,GAAGqB,YAAY,CAACrB,UAAU;QACpCyB,EAAE,GAAGJ,YAAY,CAACI,EAAE;MACtB,IAAIH,IAAI,IAAI,CAACzE,IAAI,IAAI,CAACA,IAAI,CAACrH,MAAM,EAAE;QACjC,OAAO,IAAI;MACb;MACA,IAAIgH,mBAAmB,GAAG,IAAI,CAAC0B,KAAK,CAAC1B,mBAAmB;MACxD,IAAIkF,UAAU,GAAG/G,UAAU,CAAC,cAAc,EAAE0C,SAAS,CAAC;MACtD,IAAI+C,QAAQ,GAAGG,KAAK,IAAIA,KAAK,CAACoB,iBAAiB,IAAInB,KAAK,IAAIA,KAAK,CAACmB,iBAAiB;MACnF,IAAItB,UAAU,GAAGlM,MAAM,CAACsN,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MAC1C,OAAO,aAAahH,KAAK,CAAC2C,aAAa,CAACtC,KAAK,EAAE;QAC7CuC,SAAS,EAAEqE;MACb,CAAC,EAAEtB,QAAQ,GAAG,aAAa3F,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa3C,KAAK,CAAC2C,aAAa,CAAC,UAAU,EAAE;QACxGqE,EAAE,EAAE,WAAW,CAAClF,MAAM,CAAC8D,UAAU;MACnC,CAAC,EAAE,aAAa5F,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE;QAC1C4B,CAAC,EAAEuC,IAAI;QACPrC,CAAC,EAAEsC,GAAG;QACNpC,KAAK,EAAEA,KAAK;QACZE,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,aAAa7E,KAAK,CAAC2C,aAAa,CAACtC,KAAK,EAAE;QACnDuC,SAAS,EAAE,yBAAyB;QACpC4D,QAAQ,EAAEb,QAAQ,GAAG,gBAAgB,CAAC7D,MAAM,CAAC8D,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAEL,UAAU,GAAG,IAAI,CAACH,gBAAgB,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,CAACF,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACQ,cAAc,CAACC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,CAACzC,iBAAiB,IAAIpB,mBAAmB,KAAKvB,SAAS,CAAC2G,kBAAkB,CAAC,IAAI,CAACpK,KAAK,EAAEqF,IAAI,CAAC,CAAC;IACrN;EACF,CAAC,CAAC,EAAE,CAAC;IACH1H,GAAG,EAAE,0BAA0B;IAC/BiD,KAAK,EAAE,SAASyJ,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC9D,WAAW,KAAK+D,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAC9D,WAAW;UACtCiE,OAAO,EAAEH,SAAS,CAACjF,IAAI;UACvBoB,QAAQ,EAAE8D,SAAS,CAACE;QACtB,CAAC;MACH;MACA,IAAIH,SAAS,CAACjF,IAAI,KAAKkF,SAAS,CAACE,OAAO,EAAE;QACxC,OAAO;UACLA,OAAO,EAAEH,SAAS,CAACjF;QACrB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD1H,GAAG,EAAE,iBAAiB;IACtBiD,KAAK,EAAE,SAASmF,eAAeA,CAAC2E,MAAM,EAAE1K,KAAK,EAAE;MAC7C,IAAI2K,SAAS;MACb,IAAK,aAAa1H,KAAK,CAAC2H,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,SAAS,GAAG,aAAa1H,KAAK,CAAC0G,YAAY,CAACe,MAAM,EAAE1K,KAAK,CAAC;MAC5D,CAAC,MAAM,IAAInD,WAAW,CAAC6N,MAAM,CAAC,EAAE;QAC9BC,SAAS,GAAGD,MAAM,CAAC1K,KAAK,CAAC;MAC3B,CAAC,MAAM;QACL2K,SAAS,GAAG,aAAa1H,KAAK,CAAC2C,aAAa,CAACvC,SAAS,EAAErD,KAAK,CAAC;MAChE;MACA,OAAO2K,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EACH,OAAOpG,GAAG;AACZ,CAAC,CAACrB,aAAa,CAAC;AAChB3D,eAAe,CAACgF,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1ChF,eAAe,CAACgF,GAAG,EAAE,cAAc,EAAE;EACnCsG,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,MAAM;EAClBC,YAAY,EAAE,CAAC;EACflB,IAAI,EAAE,KAAK;EACX;EACAzE,IAAI,EAAE,EAAE;EACRc,MAAM,EAAE,UAAU;EAClBC,iBAAiB,EAAE,CAACrC,MAAM,CAACkH,KAAK;EAChC5E,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACFhH,eAAe,CAACgF,GAAG,EAAE,iBAAiB,EAAE,UAAU2G,KAAK,EAAE;EACvD,IAAIlL,KAAK,GAAGkL,KAAK,CAAClL,KAAK;IACrB0J,IAAI,GAAGwB,KAAK,CAACxB,IAAI;IACjByB,WAAW,GAAGD,KAAK,CAACC,WAAW;IAC/BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBrC,KAAK,GAAGmC,KAAK,CAACnC,KAAK;IACnBC,KAAK,GAAGkC,KAAK,CAAClC,KAAK;IACnBqC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCtC,MAAM,GAAG+B,KAAK,CAAC/B,MAAM;EACvB,IAAIuC,GAAG,GAAGtH,iBAAiB,CAAC+G,WAAW,EAAEzB,IAAI,CAAC;EAC9C,IAAI,CAACgC,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIvF,MAAM,GAAGnG,KAAK,CAACmG,MAAM;EACzB,IAAIwF,WAAW,GAAGjC,IAAI,CAAC1J,KAAK;IAC1BsJ,OAAO,GAAGqC,WAAW,CAACrC,OAAO;IAC7BL,QAAQ,GAAG0C,WAAW,CAAC1C,QAAQ;IAC/B+B,YAAY,GAAGW,WAAW,CAACX,YAAY;EACzC,IAAIY,WAAW,GAAGzF,MAAM,KAAK,YAAY,GAAG6C,KAAK,GAAGD,KAAK;EACzD,IAAI8C,aAAa,GAAGN,WAAW,GAAGK,WAAW,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;EACnE,IAAIC,SAAS,GAAG7H,iBAAiB,CAAC;IAChCyH,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAIK,KAAK,GAAGnI,aAAa,CAACmF,QAAQ,EAAEzF,IAAI,CAAC;EACzC,IAAI0I,KAAK,GAAGT,aAAa,CAAChG,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACpD,IAAI/E,KAAK,EAAE4G,CAAC,EAAEE,CAAC,EAAEE,KAAK,EAAEE,MAAM,EAAEU,UAAU;IAC1C,IAAI+C,WAAW,EAAE;MACf3K,KAAK,GAAGsD,gBAAgB,CAACqH,WAAW,CAACC,cAAc,GAAG7F,KAAK,CAAC,EAAEkG,aAAa,CAAC;IAC9E,CAAC,MAAM;MACLjL,KAAK,GAAGqD,iBAAiB,CAACyB,KAAK,EAAE4D,OAAO,CAAC;MACzC,IAAI,CAACxM,QAAQ,CAAC8D,KAAK,CAAC,EAAE;QACpBA,KAAK,GAAG,CAACoL,SAAS,EAAEpL,KAAK,CAAC;MAC5B;IACF;IACA,IAAIuF,MAAM,KAAK,YAAY,EAAE;MAC3B,IAAIgG,KAAK;MACT,IAAIC,KAAK,GAAG,CAACpD,KAAK,CAAC8C,KAAK,CAAClL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEoI,KAAK,CAAC8C,KAAK,CAAClL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDyL,cAAc,GAAGD,KAAK,CAAC,CAAC,CAAC;QACzBE,iBAAiB,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC9B5E,CAAC,GAAGxD,sBAAsB,CAAC;QACzBuI,IAAI,EAAExD,KAAK;QACXyD,KAAK,EAAEnB,UAAU;QACjBD,QAAQ,EAAEA,QAAQ;QAClBjC,MAAM,EAAEuC,GAAG,CAACvC,MAAM;QAClBzD,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACT,CAAC,CAAC;MACF+B,CAAC,GAAG,CAACyE,KAAK,GAAGG,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGD,cAAc,MAAM,IAAI,IAAIF,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGrJ,SAAS;MAC9J8E,KAAK,GAAG8D,GAAG,CAACe,IAAI;MAChB,IAAIC,cAAc,GAAGL,cAAc,GAAGC,iBAAiB;MACvDxE,MAAM,GAAG9E,MAAM,CAAC2J,KAAK,CAACD,cAAc,CAAC,GAAG,CAAC,GAAGA,cAAc;MAC1DlE,UAAU,GAAG;QACXhB,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEsB,KAAK,CAACtB,CAAC;QACVE,KAAK,EAAEA,KAAK;QACZE,MAAM,EAAEkB,KAAK,CAAClB;MAChB,CAAC;MACD,IAAI8E,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,GAAG,CAAC,IAAI4B,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAAC,GAAG8E,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,EAAE;QAC3E,IAAI8B,KAAK,GAAGnJ,QAAQ,CAACmE,MAAM,IAAIkD,YAAY,CAAC,IAAI4B,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAAC,CAAC;QAC1FJ,CAAC,IAAIoF,KAAK;QACVhF,MAAM,IAAIgF,KAAK;MACjB;IACF,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,CAAChE,KAAK,CAAC+C,KAAK,CAAClL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEmI,KAAK,CAAC+C,KAAK,CAAClL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDoM,eAAe,GAAGD,KAAK,CAAC,CAAC,CAAC;QAC1BE,kBAAkB,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC/BvF,CAAC,GAAGwF,eAAe;MACnBtF,CAAC,GAAG1D,sBAAsB,CAAC;QACzBuI,IAAI,EAAEvD,KAAK;QACXwD,KAAK,EAAElB,UAAU;QACjBF,QAAQ,EAAEA,QAAQ;QAClBjC,MAAM,EAAEuC,GAAG,CAACvC,MAAM;QAClBzD,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACT,CAAC,CAAC;MACFiC,KAAK,GAAGqF,kBAAkB,GAAGD,eAAe;MAC5ClF,MAAM,GAAG4D,GAAG,CAACe,IAAI;MACjBjE,UAAU,GAAG;QACXhB,CAAC,EAAEuB,KAAK,CAACvB,CAAC;QACVE,CAAC,EAAEA,CAAC;QACJE,KAAK,EAAEmB,KAAK,CAACnB,KAAK;QAClBE,MAAM,EAAEA;MACV,CAAC;MACD,IAAI8E,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,GAAG,CAAC,IAAI4B,IAAI,CAACC,GAAG,CAACjF,KAAK,CAAC,GAAGgF,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,EAAE;QAC1E,IAAIkC,MAAM,GAAGvJ,QAAQ,CAACiE,KAAK,IAAIoD,YAAY,CAAC,IAAI4B,IAAI,CAACC,GAAG,CAAC7B,YAAY,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAACjF,KAAK,CAAC,CAAC;QACzFA,KAAK,IAAIsF,MAAM;MACjB;IACF;IACA,OAAO7N,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/D8B,CAAC,EAAEA,CAAC;MACJE,CAAC,EAAEA,CAAC;MACJE,KAAK,EAAEA,KAAK;MACZE,MAAM,EAAEA,MAAM;MACdlH,KAAK,EAAE2K,WAAW,GAAG3K,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrCuM,OAAO,EAAEzH,KAAK;MACd8C,UAAU,EAAEA;IACd,CAAC,EAAEyD,KAAK,IAAIA,KAAK,CAACtG,KAAK,CAAC,IAAIsG,KAAK,CAACtG,KAAK,CAAC,CAAC3F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACnDoN,cAAc,EAAE,CAAC/I,cAAc,CAACqF,IAAI,EAAEhE,KAAK,CAAC,CAAC;MAC7C2H,eAAe,EAAE;QACf7F,CAAC,EAAEA,CAAC,GAAGI,KAAK,GAAG,CAAC;QAChBF,CAAC,EAAEA,CAAC,GAAGI,MAAM,GAAG;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOzI,aAAa,CAAC;IACnBgG,IAAI,EAAE6G,KAAK;IACX/F,MAAM,EAAEA;EACV,CAAC,EAAEgD,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}