{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport Trigger from 'rc-trigger';\nimport * as React from 'react';\nimport DropdownMenu from './DropdownMenu';\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\nvar KeywordTrigger = /*#__PURE__*/function (_React$Component) {\n  _inherits(KeywordTrigger, _React$Component);\n  var _super = _createSuper(KeywordTrigger);\n  function KeywordTrigger() {\n    var _this;\n    _classCallCheck(this, KeywordTrigger);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.getDropdownPrefix = function () {\n      return \"\".concat(_this.props.prefixCls, \"-dropdown\");\n    };\n    _this.getDropdownElement = function () {\n      var options = _this.props.options;\n      return /*#__PURE__*/React.createElement(DropdownMenu, {\n        prefixCls: _this.getDropdownPrefix(),\n        options: options\n      });\n    };\n    _this.getDropDownPlacement = function () {\n      var _this$props = _this.props,\n        placement = _this$props.placement,\n        direction = _this$props.direction;\n      var popupPlacement = 'topRight';\n      if (direction === 'rtl') {\n        popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n      } else {\n        popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n      }\n      return popupPlacement;\n    };\n    return _this;\n  }\n  _createClass(KeywordTrigger, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        children = _this$props2.children,\n        visible = _this$props2.visible,\n        transitionName = _this$props2.transitionName,\n        getPopupContainer = _this$props2.getPopupContainer;\n      var popupElement = this.getDropdownElement();\n      return /*#__PURE__*/React.createElement(Trigger, {\n        prefixCls: this.getDropdownPrefix(),\n        popupVisible: visible,\n        popup: popupElement,\n        popupPlacement: this.getDropDownPlacement(),\n        popupTransitionName: transitionName,\n        builtinPlacements: BUILT_IN_PLACEMENTS,\n        getPopupContainer: getPopupContainer,\n        popupClassName: this.props.dropdownClassName\n      }, children);\n    }\n  }]);\n  return KeywordTrigger;\n}(React.Component);\nexport default KeywordTrigger;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "<PERSON><PERSON>", "React", "DropdownMenu", "BUILT_IN_PLACEMENTS", "bottomRight", "points", "offset", "overflow", "adjustX", "adjustY", "bottomLeft", "topRight", "topLeft", "KeywordTrigger", "_React$Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "getDropdownPrefix", "props", "prefixCls", "getDropdownElement", "options", "createElement", "getDropDownPlacement", "_this$props", "placement", "direction", "popupPlacement", "key", "value", "render", "_this$props2", "children", "visible", "transitionName", "getPopupContainer", "popupElement", "popupVisible", "popup", "popupTransitionName", "builtinPlacements", "popupClassName", "dropdownClassName", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-mentions/es/KeywordTrigger.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport Trigger from 'rc-trigger';\nimport * as React from 'react';\nimport DropdownMenu from './DropdownMenu';\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\n\nvar KeywordTrigger = /*#__PURE__*/function (_React$Component) {\n  _inherits(KeywordTrigger, _React$Component);\n\n  var _super = _createSuper(KeywordTrigger);\n\n  function KeywordTrigger() {\n    var _this;\n\n    _classCallCheck(this, KeywordTrigger);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _this.getDropdownPrefix = function () {\n      return \"\".concat(_this.props.prefixCls, \"-dropdown\");\n    };\n\n    _this.getDropdownElement = function () {\n      var options = _this.props.options;\n      return /*#__PURE__*/React.createElement(DropdownMenu, {\n        prefixCls: _this.getDropdownPrefix(),\n        options: options\n      });\n    };\n\n    _this.getDropDownPlacement = function () {\n      var _this$props = _this.props,\n          placement = _this$props.placement,\n          direction = _this$props.direction;\n      var popupPlacement = 'topRight';\n\n      if (direction === 'rtl') {\n        popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n      } else {\n        popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n      }\n\n      return popupPlacement;\n    };\n\n    return _this;\n  }\n\n  _createClass(KeywordTrigger, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          children = _this$props2.children,\n          visible = _this$props2.visible,\n          transitionName = _this$props2.transitionName,\n          getPopupContainer = _this$props2.getPopupContainer;\n      var popupElement = this.getDropdownElement();\n      return /*#__PURE__*/React.createElement(Trigger, {\n        prefixCls: this.getDropdownPrefix(),\n        popupVisible: visible,\n        popup: popupElement,\n        popupPlacement: this.getDropDownPlacement(),\n        popupTransitionName: transitionName,\n        builtinPlacements: BUILT_IN_PLACEMENTS,\n        getPopupContainer: getPopupContainer,\n        popupClassName: this.props.dropdownClassName\n      }, children);\n    }\n  }]);\n\n  return KeywordTrigger;\n}(React.Component);\n\nexport default KeywordTrigger;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,mBAAmB,GAAG;EACxBC,WAAW,EAAE;IACXC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDE,QAAQ,EAAE;IACRN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,OAAO,EAAE;IACPP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC;AAED,IAAII,cAAc,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC5DhB,SAAS,CAACe,cAAc,EAAEC,gBAAgB,CAAC;EAE3C,IAAIC,MAAM,GAAGhB,YAAY,CAACc,cAAc,CAAC;EAEzC,SAASA,cAAcA,CAAA,EAAG;IACxB,IAAIG,KAAK;IAETpB,eAAe,CAAC,IAAI,EAAEiB,cAAc,CAAC;IAErC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IAEtDJ,KAAK,CAACU,iBAAiB,GAAG,YAAY;MACpC,OAAO,EAAE,CAACD,MAAM,CAACT,KAAK,CAACW,KAAK,CAACC,SAAS,EAAE,WAAW,CAAC;IACtD,CAAC;IAEDZ,KAAK,CAACa,kBAAkB,GAAG,YAAY;MACrC,IAAIC,OAAO,GAAGd,KAAK,CAACW,KAAK,CAACG,OAAO;MACjC,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAAC7B,YAAY,EAAE;QACpD0B,SAAS,EAAEZ,KAAK,CAACU,iBAAiB,CAAC,CAAC;QACpCI,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ,CAAC;IAEDd,KAAK,CAACgB,oBAAoB,GAAG,YAAY;MACvC,IAAIC,WAAW,GAAGjB,KAAK,CAACW,KAAK;QACzBO,SAAS,GAAGD,WAAW,CAACC,SAAS;QACjCC,SAAS,GAAGF,WAAW,CAACE,SAAS;MACrC,IAAIC,cAAc,GAAG,UAAU;MAE/B,IAAID,SAAS,KAAK,KAAK,EAAE;QACvBC,cAAc,GAAGF,SAAS,KAAK,KAAK,GAAG,SAAS,GAAG,YAAY;MACjE,CAAC,MAAM;QACLE,cAAc,GAAGF,SAAS,KAAK,KAAK,GAAG,UAAU,GAAG,aAAa;MACnE;MAEA,OAAOE,cAAc;IACvB,CAAC;IAED,OAAOpB,KAAK;EACd;EAEAnB,YAAY,CAACgB,cAAc,EAAE,CAAC;IAC5BwB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACb,KAAK;QACzBc,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,OAAO,GAAGF,YAAY,CAACE,OAAO;QAC9BC,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;MACtD,IAAIC,YAAY,GAAG,IAAI,CAAChB,kBAAkB,CAAC,CAAC;MAC5C,OAAO,aAAa5B,KAAK,CAAC8B,aAAa,CAAC/B,OAAO,EAAE;QAC/C4B,SAAS,EAAE,IAAI,CAACF,iBAAiB,CAAC,CAAC;QACnCoB,YAAY,EAAEJ,OAAO;QACrBK,KAAK,EAAEF,YAAY;QACnBT,cAAc,EAAE,IAAI,CAACJ,oBAAoB,CAAC,CAAC;QAC3CgB,mBAAmB,EAAEL,cAAc;QACnCM,iBAAiB,EAAE9C,mBAAmB;QACtCyC,iBAAiB,EAAEA,iBAAiB;QACpCM,cAAc,EAAE,IAAI,CAACvB,KAAK,CAACwB;MAC7B,CAAC,EAAEV,QAAQ,CAAC;IACd;EACF,CAAC,CAAC,CAAC;EAEH,OAAO5B,cAAc;AACvB,CAAC,CAACZ,KAAK,CAACmD,SAAS,CAAC;AAElB,eAAevC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}