{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\nconst initialState = {\n  isAuthenticated: false,\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: false,\n  error: null,\n  showChangePassword: false,\n  forceChangePassword: false\n};\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk('auth/login', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.login(credentials);\n    const {\n      access_token\n    } = response.data;\n\n    // 获取用户信息\n    const userResponse = await authAPI.getUsers(access_token);\n    const userData = userResponse.data[credentials.username];\n    return {\n      token: access_token,\n      user: {\n        username: credentials.username,\n        isDefault: (userData === null || userData === void 0 ? void 0 : userData.is_default) || false\n      }\n    };\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '登录失败');\n  }\n});\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk('auth/changePassword', async (data, {\n  getState,\n  rejectWithValue\n}) => {\n  try {\n    const state = getState();\n    const response = await authAPI.changePassword(data, state.auth.token);\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '修改密码失败');\n  }\n});\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: state => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: state => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // 登录\n    .addCase(loginAsync.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(loginAsync.fulfilled, (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = true;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.showChangePassword = action.payload.user.isDefault;\n      state.forceChangePassword = action.payload.user.isDefault;\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('username', action.payload.user.username);\n    }).addCase(loginAsync.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    })\n    // 修改密码\n    .addCase(changePasswordAsync.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(changePasswordAsync.fulfilled, state => {\n      state.loading = false;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      if (state.user) {\n        state.user.isDefault = false;\n      }\n    }).addCase(changePasswordAsync.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nexport const {\n  logout,\n  clearError,\n  setShowChangePassword,\n  restoreAuth\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authAPI", "initialState", "isAuthenticated", "user", "token", "localStorage", "getItem", "loading", "error", "showChangePassword", "forceChangePassword", "loginAsync", "credentials", "rejectWithValue", "response", "login", "access_token", "data", "userResponse", "getUsers", "userData", "username", "isDefault", "is_default", "_error$response", "_error$response$data", "detail", "changePasswordAsync", "getState", "state", "changePassword", "auth", "_error$response2", "_error$response2$data", "authSlice", "name", "reducers", "logout", "removeItem", "clearError", "setShowChangePassword", "action", "payload", "restoreAuth", "extraReducers", "builder", "addCase", "pending", "fulfilled", "setItem", "rejected", "actions", "reducer"], "sources": ["/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\nexport interface User {\n  username: string;\n  isDefault: boolean;\n}\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  user: User | null;\n  token: string | null;\n  loading: boolean;\n  error: string | null;\n  showChangePassword: boolean;\n  forceChangePassword: boolean;\n}\n\nconst initialState: AuthState = {\n  isAuthenticated: false,\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: false,\n  error: null,\n  showChangePassword: false,\n  forceChangePassword: false,\n};\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { access_token } = response.data;\n      \n      // 获取用户信息\n      const userResponse = await authAPI.getUsers(access_token);\n      const userData = userResponse.data[credentials.username];\n      \n      return {\n        token: access_token,\n        user: {\n          username: credentials.username,\n          isDefault: userData?.is_default || false,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '登录失败');\n    }\n  }\n);\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk(\n  'auth/changePassword',\n  async (\n    data: { username: string; old_password: string; new_password: string; confirm_password: string },\n    { getState, rejectWithValue }\n  ) => {\n    try {\n      const state = getState() as { auth: AuthState };\n      const response = await authAPI.changePassword(data, state.auth.token!);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '修改密码失败');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: (state) => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action: PayloadAction<boolean>) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false, // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // 登录\n      .addCase(loginAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(loginAsync.fulfilled, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = true;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.showChangePassword = action.payload.user.isDefault;\n        state.forceChangePassword = action.payload.user.isDefault;\n        localStorage.setItem('token', action.payload.token);\n        localStorage.setItem('username', action.payload.user.username);\n      })\n      .addCase(loginAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // 修改密码\n      .addCase(changePasswordAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(changePasswordAsync.fulfilled, (state) => {\n        state.loading = false;\n        state.showChangePassword = false;\n        state.forceChangePassword = false;\n        if (state.user) {\n          state.user.isDefault = false;\n        }\n      })\n      .addCase(changePasswordAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const { logout, clearError, setShowChangePassword, restoreAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,OAAO,QAAQ,oBAAoB;AAiB5C,MAAMC,YAAuB,GAAG;EAC9BC,eAAe,EAAE,KAAK;EACtBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,kBAAkB,EAAE,KAAK;EACzBC,mBAAmB,EAAE;AACvB,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAGZ,gBAAgB,CACxC,YAAY,EACZ,OAAOa,WAAmD,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAClF,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMd,OAAO,CAACe,KAAK,CAACH,WAAW,CAAC;IACjD,MAAM;MAAEI;IAAa,CAAC,GAAGF,QAAQ,CAACG,IAAI;;IAEtC;IACA,MAAMC,YAAY,GAAG,MAAMlB,OAAO,CAACmB,QAAQ,CAACH,YAAY,CAAC;IACzD,MAAMI,QAAQ,GAAGF,YAAY,CAACD,IAAI,CAACL,WAAW,CAACS,QAAQ,CAAC;IAExD,OAAO;MACLjB,KAAK,EAAEY,YAAY;MACnBb,IAAI,EAAE;QACJkB,QAAQ,EAAET,WAAW,CAACS,QAAQ;QAC9BC,SAAS,EAAE,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,UAAU,KAAI;MACrC;IACF,CAAC;EACH,CAAC,CAAC,OAAOf,KAAU,EAAE;IAAA,IAAAgB,eAAA,EAAAC,oBAAA;IACnB,OAAOZ,eAAe,CAAC,EAAAW,eAAA,GAAAhB,KAAK,CAACM,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;EAChE;AACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAG5B,gBAAgB,CACjD,qBAAqB,EACrB,OACEkB,IAAgG,EAChG;EAAEW,QAAQ;EAAEf;AAAgB,CAAC,KAC1B;EACH,IAAI;IACF,MAAMgB,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAC/C,MAAMd,QAAQ,GAAG,MAAMd,OAAO,CAAC8B,cAAc,CAACb,IAAI,EAAEY,KAAK,CAACE,IAAI,CAAC3B,KAAM,CAAC;IACtE,OAAOU,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOT,KAAU,EAAE;IAAA,IAAAwB,gBAAA,EAAAC,qBAAA;IACnB,OAAOpB,eAAe,CAAC,EAAAmB,gBAAA,GAAAxB,KAAK,CAACM,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI,QAAQ,CAAC;EAClE;AACF,CACF,CAAC;AAED,MAAMQ,SAAS,GAAGpC,WAAW,CAAC;EAC5BqC,IAAI,EAAE,MAAM;EACZlC,YAAY;EACZmC,QAAQ,EAAE;IACRC,MAAM,EAAGR,KAAK,IAAK;MACjBA,KAAK,CAAC3B,eAAe,GAAG,KAAK;MAC7B2B,KAAK,CAAC1B,IAAI,GAAG,IAAI;MACjB0B,KAAK,CAACzB,KAAK,GAAG,IAAI;MAClByB,KAAK,CAACpB,kBAAkB,GAAG,KAAK;MAChCoB,KAAK,CAACnB,mBAAmB,GAAG,KAAK;MACjCL,YAAY,CAACiC,UAAU,CAAC,OAAO,CAAC;MAChCjC,YAAY,CAACiC,UAAU,CAAC,UAAU,CAAC;IACrC,CAAC;IACDC,UAAU,EAAGV,KAAK,IAAK;MACrBA,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDgC,qBAAqB,EAAEA,CAACX,KAAK,EAAEY,MAA8B,KAAK;MAChEZ,KAAK,CAACpB,kBAAkB,GAAGgC,MAAM,CAACC,OAAO;IAC3C,CAAC;IACD;IACAC,WAAW,EAAGd,KAAK,IAAK;MACtB,MAAMzB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMe,QAAQ,GAAGhB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD,IAAIF,KAAK,IAAIiB,QAAQ,EAAE;QACrBQ,KAAK,CAACzB,KAAK,GAAGA,KAAK;QACnByB,KAAK,CAAC3B,eAAe,GAAG,IAAI;QAC5B2B,KAAK,CAAC1B,IAAI,GAAG;UACXkB,QAAQ,EAAEA,QAAQ;UAClBC,SAAS,EAAE,KAAK,CAAE;QACpB,CAAC;QACD;MACF;IACF;EACF,CAAC;EACDsB,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACnC,UAAU,CAACoC,OAAO,EAAGlB,KAAK,IAAK;MACtCA,KAAK,CAACtB,OAAO,GAAG,IAAI;MACpBsB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDsC,OAAO,CAACnC,UAAU,CAACqC,SAAS,EAAE,CAACnB,KAAK,EAAEY,MAAM,KAAK;MAChDZ,KAAK,CAACtB,OAAO,GAAG,KAAK;MACrBsB,KAAK,CAAC3B,eAAe,GAAG,IAAI;MAC5B2B,KAAK,CAAC1B,IAAI,GAAGsC,MAAM,CAACC,OAAO,CAACvC,IAAI;MAChC0B,KAAK,CAACzB,KAAK,GAAGqC,MAAM,CAACC,OAAO,CAACtC,KAAK;MAClCyB,KAAK,CAACpB,kBAAkB,GAAGgC,MAAM,CAACC,OAAO,CAACvC,IAAI,CAACmB,SAAS;MACxDO,KAAK,CAACnB,mBAAmB,GAAG+B,MAAM,CAACC,OAAO,CAACvC,IAAI,CAACmB,SAAS;MACzDjB,YAAY,CAAC4C,OAAO,CAAC,OAAO,EAAER,MAAM,CAACC,OAAO,CAACtC,KAAK,CAAC;MACnDC,YAAY,CAAC4C,OAAO,CAAC,UAAU,EAAER,MAAM,CAACC,OAAO,CAACvC,IAAI,CAACkB,QAAQ,CAAC;IAChE,CAAC,CAAC,CACDyB,OAAO,CAACnC,UAAU,CAACuC,QAAQ,EAAE,CAACrB,KAAK,EAAEY,MAAM,KAAK;MAC/CZ,KAAK,CAACtB,OAAO,GAAG,KAAK;MACrBsB,KAAK,CAACrB,KAAK,GAAGiC,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCI,OAAO,CAACnB,mBAAmB,CAACoB,OAAO,EAAGlB,KAAK,IAAK;MAC/CA,KAAK,CAACtB,OAAO,GAAG,IAAI;MACpBsB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDsC,OAAO,CAACnB,mBAAmB,CAACqB,SAAS,EAAGnB,KAAK,IAAK;MACjDA,KAAK,CAACtB,OAAO,GAAG,KAAK;MACrBsB,KAAK,CAACpB,kBAAkB,GAAG,KAAK;MAChCoB,KAAK,CAACnB,mBAAmB,GAAG,KAAK;MACjC,IAAImB,KAAK,CAAC1B,IAAI,EAAE;QACd0B,KAAK,CAAC1B,IAAI,CAACmB,SAAS,GAAG,KAAK;MAC9B;IACF,CAAC,CAAC,CACDwB,OAAO,CAACnB,mBAAmB,CAACuB,QAAQ,EAAE,CAACrB,KAAK,EAAEY,MAAM,KAAK;MACxDZ,KAAK,CAACtB,OAAO,GAAG,KAAK;MACrBsB,KAAK,CAACrB,KAAK,GAAGiC,MAAM,CAACC,OAAiB;IACxC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEL,MAAM;EAAEE,UAAU;EAAEC,qBAAqB;EAAEG;AAAY,CAAC,GAAGT,SAAS,CAACiB,OAAO;AAC3F,eAAejB,SAAS,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}