{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction UnitNumber(_ref) {\n  var prefixCls = _ref.prefixCls,\n    value = _ref.value,\n    current = _ref.current,\n    _ref$offset = _ref.offset,\n    offset = _ref$offset === void 0 ? 0 : _ref$offset;\n  var style;\n  if (offset) {\n    style = {\n      position: 'absolute',\n      top: \"\".concat(offset, \"00%\"),\n      left: 0\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    style: style,\n    className: classNames(\"\".concat(prefixCls, \"-only-unit\"), {\n      current: current\n    })\n  }, value);\n}\nfunction getOffset(start, end, unit) {\n  var index = start;\n  var offset = 0;\n  while ((index + 10) % 10 !== end) {\n    index += unit;\n    offset += unit;\n  }\n  return offset;\n}\nexport default function SingleNumber(props) {\n  var prefixCls = props.prefixCls,\n    originCount = props.count,\n    originValue = props.value;\n  var value = Number(originValue);\n  var count = Math.abs(originCount);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n  var _React$useState3 = React.useState(count),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevCount = _React$useState4[0],\n    setPrevCount = _React$useState4[1];\n  // ============================= Events =============================\n  var onTransitionEnd = function onTransitionEnd() {\n    setPrevValue(value);\n    setPrevCount(count);\n  };\n  // Fallback if transition event not support\n  React.useEffect(function () {\n    var timeout = setTimeout(function () {\n      onTransitionEnd();\n    }, 1000);\n    return function () {\n      clearTimeout(timeout);\n    };\n  }, [value]);\n  // ============================= Render =============================\n  // Render unit list\n  var unitNodes;\n  var offsetStyle;\n  if (prevValue === value || Number.isNaN(value) || Number.isNaN(prevValue)) {\n    // Nothing to change\n    unitNodes = [/*#__PURE__*/React.createElement(UnitNumber, _extends({}, props, {\n      key: value,\n      current: true\n    }))];\n    offsetStyle = {\n      transition: 'none'\n    };\n  } else {\n    unitNodes = [];\n    // Fill basic number units\n    var end = value + 10;\n    var unitNumberList = [];\n    for (var index = value; index <= end; index += 1) {\n      unitNumberList.push(index);\n    }\n    // Fill with number unit nodes\n    var prevIndex = unitNumberList.findIndex(function (n) {\n      return n % 10 === prevValue;\n    });\n    unitNodes = unitNumberList.map(function (n, index) {\n      var singleUnit = n % 10;\n      return /*#__PURE__*/React.createElement(UnitNumber, _extends({}, props, {\n        key: n,\n        value: singleUnit,\n        offset: index - prevIndex,\n        current: index === prevIndex\n      }));\n    });\n    // Calculate container offset value\n    var unit = prevCount < count ? 1 : -1;\n    offsetStyle = {\n      transform: \"translateY(\".concat(-getOffset(prevValue, value, unit), \"00%)\")\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-only\"),\n    style: offsetStyle,\n    onTransitionEnd: onTransitionEnd\n  }, unitNodes);\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "classNames", "React", "UnitNumber", "_ref", "prefixCls", "value", "current", "_ref$offset", "offset", "style", "position", "top", "concat", "left", "createElement", "className", "getOffset", "start", "end", "unit", "index", "SingleNumber", "props", "originCount", "count", "originValue", "Number", "Math", "abs", "_React$useState", "useState", "_React$useState2", "prevValue", "setPrevValue", "_React$useState3", "_React$useState4", "prevCount", "setPrevCount", "onTransitionEnd", "useEffect", "timeout", "setTimeout", "clearTimeout", "unitNodes", "offsetStyle", "isNaN", "key", "transition", "unitNumberList", "push", "prevIndex", "findIndex", "n", "map", "singleUnit", "transform"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/badge/SingleNumber.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction UnitNumber(_ref) {\n  var prefixCls = _ref.prefixCls,\n    value = _ref.value,\n    current = _ref.current,\n    _ref$offset = _ref.offset,\n    offset = _ref$offset === void 0 ? 0 : _ref$offset;\n  var style;\n  if (offset) {\n    style = {\n      position: 'absolute',\n      top: \"\".concat(offset, \"00%\"),\n      left: 0\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    style: style,\n    className: classNames(\"\".concat(prefixCls, \"-only-unit\"), {\n      current: current\n    })\n  }, value);\n}\nfunction getOffset(start, end, unit) {\n  var index = start;\n  var offset = 0;\n  while ((index + 10) % 10 !== end) {\n    index += unit;\n    offset += unit;\n  }\n  return offset;\n}\nexport default function SingleNumber(props) {\n  var prefixCls = props.prefixCls,\n    originCount = props.count,\n    originValue = props.value;\n  var value = Number(originValue);\n  var count = Math.abs(originCount);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n  var _React$useState3 = React.useState(count),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevCount = _React$useState4[0],\n    setPrevCount = _React$useState4[1];\n  // ============================= Events =============================\n  var onTransitionEnd = function onTransitionEnd() {\n    setPrevValue(value);\n    setPrevCount(count);\n  };\n  // Fallback if transition event not support\n  React.useEffect(function () {\n    var timeout = setTimeout(function () {\n      onTransitionEnd();\n    }, 1000);\n    return function () {\n      clearTimeout(timeout);\n    };\n  }, [value]);\n  // ============================= Render =============================\n  // Render unit list\n  var unitNodes;\n  var offsetStyle;\n  if (prevValue === value || Number.isNaN(value) || Number.isNaN(prevValue)) {\n    // Nothing to change\n    unitNodes = [/*#__PURE__*/React.createElement(UnitNumber, _extends({}, props, {\n      key: value,\n      current: true\n    }))];\n    offsetStyle = {\n      transition: 'none'\n    };\n  } else {\n    unitNodes = [];\n    // Fill basic number units\n    var end = value + 10;\n    var unitNumberList = [];\n    for (var index = value; index <= end; index += 1) {\n      unitNumberList.push(index);\n    }\n    // Fill with number unit nodes\n    var prevIndex = unitNumberList.findIndex(function (n) {\n      return n % 10 === prevValue;\n    });\n    unitNodes = unitNumberList.map(function (n, index) {\n      var singleUnit = n % 10;\n      return /*#__PURE__*/React.createElement(UnitNumber, _extends({}, props, {\n        key: n,\n        value: singleUnit,\n        offset: index - prevIndex,\n        current: index === prevIndex\n      }));\n    });\n    // Calculate container offset value\n    var unit = prevCount < count ? 1 : -1;\n    offsetStyle = {\n      transform: \"translateY(\".concat(-getOffset(prevValue, value, unit), \"00%)\")\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-only\"),\n    style: offsetStyle,\n    onTransitionEnd: onTransitionEnd\n  }, unitNodes);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,WAAW,GAAGJ,IAAI,CAACK,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;EACnD,IAAIE,KAAK;EACT,IAAID,MAAM,EAAE;IACVC,KAAK,GAAG;MACNC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,EAAE,CAACC,MAAM,CAACJ,MAAM,EAAE,KAAK,CAAC;MAC7BK,IAAI,EAAE;IACR,CAAC;EACH;EACA,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC9CL,KAAK,EAAEA,KAAK;IACZM,SAAS,EAAEf,UAAU,CAAC,EAAE,CAACY,MAAM,CAACR,SAAS,EAAE,YAAY,CAAC,EAAE;MACxDE,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,EAAED,KAAK,CAAC;AACX;AACA,SAASW,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnC,IAAIC,KAAK,GAAGH,KAAK;EACjB,IAAIT,MAAM,GAAG,CAAC;EACd,OAAO,CAACY,KAAK,GAAG,EAAE,IAAI,EAAE,KAAKF,GAAG,EAAE;IAChCE,KAAK,IAAID,IAAI;IACbX,MAAM,IAAIW,IAAI;EAChB;EACA,OAAOX,MAAM;AACf;AACA,eAAe,SAASa,YAAYA,CAACC,KAAK,EAAE;EAC1C,IAAIlB,SAAS,GAAGkB,KAAK,CAAClB,SAAS;IAC7BmB,WAAW,GAAGD,KAAK,CAACE,KAAK;IACzBC,WAAW,GAAGH,KAAK,CAACjB,KAAK;EAC3B,IAAIA,KAAK,GAAGqB,MAAM,CAACD,WAAW,CAAC;EAC/B,IAAID,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACL,WAAW,CAAC;EACjC,IAAIM,eAAe,GAAG5B,KAAK,CAAC6B,QAAQ,CAACzB,KAAK,CAAC;IACzC0B,gBAAgB,GAAGhC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAGjC,KAAK,CAAC6B,QAAQ,CAACN,KAAK,CAAC;IAC1CW,gBAAgB,GAAGpC,cAAc,CAACmC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC;EACA,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CL,YAAY,CAAC5B,KAAK,CAAC;IACnBgC,YAAY,CAACb,KAAK,CAAC;EACrB,CAAC;EACD;EACAvB,KAAK,CAACsC,SAAS,CAAC,YAAY;IAC1B,IAAIC,OAAO,GAAGC,UAAU,CAAC,YAAY;MACnCH,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,YAAY;MACjBI,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACnC,KAAK,CAAC,CAAC;EACX;EACA;EACA,IAAIsC,SAAS;EACb,IAAIC,WAAW;EACf,IAAIZ,SAAS,KAAK3B,KAAK,IAAIqB,MAAM,CAACmB,KAAK,CAACxC,KAAK,CAAC,IAAIqB,MAAM,CAACmB,KAAK,CAACb,SAAS,CAAC,EAAE;IACzE;IACAW,SAAS,GAAG,CAAC,aAAa1C,KAAK,CAACa,aAAa,CAACZ,UAAU,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;MAC5EwB,GAAG,EAAEzC,KAAK;MACVC,OAAO,EAAE;IACX,CAAC,CAAC,CAAC,CAAC;IACJsC,WAAW,GAAG;MACZG,UAAU,EAAE;IACd,CAAC;EACH,CAAC,MAAM;IACLJ,SAAS,GAAG,EAAE;IACd;IACA,IAAIzB,GAAG,GAAGb,KAAK,GAAG,EAAE;IACpB,IAAI2C,cAAc,GAAG,EAAE;IACvB,KAAK,IAAI5B,KAAK,GAAGf,KAAK,EAAEe,KAAK,IAAIF,GAAG,EAAEE,KAAK,IAAI,CAAC,EAAE;MAChD4B,cAAc,CAACC,IAAI,CAAC7B,KAAK,CAAC;IAC5B;IACA;IACA,IAAI8B,SAAS,GAAGF,cAAc,CAACG,SAAS,CAAC,UAAUC,CAAC,EAAE;MACpD,OAAOA,CAAC,GAAG,EAAE,KAAKpB,SAAS;IAC7B,CAAC,CAAC;IACFW,SAAS,GAAGK,cAAc,CAACK,GAAG,CAAC,UAAUD,CAAC,EAAEhC,KAAK,EAAE;MACjD,IAAIkC,UAAU,GAAGF,CAAC,GAAG,EAAE;MACvB,OAAO,aAAanD,KAAK,CAACa,aAAa,CAACZ,UAAU,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;QACtEwB,GAAG,EAAEM,CAAC;QACN/C,KAAK,EAAEiD,UAAU;QACjB9C,MAAM,EAAEY,KAAK,GAAG8B,SAAS;QACzB5C,OAAO,EAAEc,KAAK,KAAK8B;MACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;IACA,IAAI/B,IAAI,GAAGiB,SAAS,GAAGZ,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACrCoB,WAAW,GAAG;MACZW,SAAS,EAAE,aAAa,CAAC3C,MAAM,CAAC,CAACI,SAAS,CAACgB,SAAS,EAAE3B,KAAK,EAAEc,IAAI,CAAC,EAAE,MAAM;IAC5E,CAAC;EACH;EACA,OAAO,aAAalB,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAE,EAAE,CAACH,MAAM,CAACR,SAAS,EAAE,OAAO,CAAC;IACxCK,KAAK,EAAEmC,WAAW;IAClBN,eAAe,EAAEA;EACnB,CAAC,EAAEK,SAAS,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}