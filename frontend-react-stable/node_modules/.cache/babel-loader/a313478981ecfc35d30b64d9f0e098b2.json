{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useState, useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport Dropdown from 'rc-dropdown';\nimport AddButton from './AddButton';\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    tabs = _ref.tabs,\n    locale = _ref.locale,\n    mobile = _ref.mobile,\n    _ref$moreIcon = _ref.moreIcon,\n    moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n    moreTransitionName = _ref.moreTransitionName,\n    style = _ref.style,\n    className = _ref.className,\n    editable = _ref.editable,\n    tabBarGutter = _ref.tabBarGutter,\n    rtl = _ref.rtl,\n    removeAriaLabel = _ref.removeAriaLabel,\n    onTabClick = _ref.onTabClick,\n    getPopupContainer = _ref.getPopupContainer,\n    popupClassName = _ref.popupClassName;\n\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n        domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var removable = editable && tab.closable !== false && !tab.disabled;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: tab.key,\n      id: \"\".concat(popupId, \"-\").concat(tab.key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(tab.key),\n      disabled: tab.disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, tab.label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, tab.key);\n      }\n    }, tab.closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  } // ========================= Effect =========================\n\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]); // ========================= Render =========================\n\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: tabs.length ? open : false,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\nexport default /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "React", "classNames", "useState", "useEffect", "KeyCode", "<PERSON><PERSON>", "MenuItem", "Dropdown", "AddButton", "OperationNode", "_ref", "ref", "prefixCls", "id", "tabs", "locale", "mobile", "_ref$moreIcon", "moreIcon", "moreTransitionName", "style", "className", "editable", "tabBarGutter", "rtl", "removeAriaLabel", "onTabClick", "getPopupContainer", "popupClassName", "_useState", "_useState2", "open", "<PERSON><PERSON><PERSON>", "_useState3", "_useState4", "<PERSON><PERSON><PERSON>", "setSelectedKey", "popupId", "concat", "dropdownPrefix", "selectedItemId", "dropdownAriaLabel", "onRemoveTab", "event", "key", "preventDefault", "stopPropagation", "onEdit", "menu", "createElement", "onClick", "_ref2", "domEvent", "tabIndex", "role", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "map", "tab", "removable", "closable", "disabled", "label", "type", "e", "closeIcon", "removeIcon", "selectOffset", "offset", "enabledTabs", "filter", "selectedIndex", "findIndex", "len", "length", "i", "onKeyDown", "which", "DOWN", "SPACE", "ENTER", "includes", "UP", "ESC", "ele", "document", "getElementById", "scrollIntoView", "moreStyle", "visibility", "order", "overlayClassName", "moreNode", "overlay", "trigger", "visible", "transitionName", "onVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "memo", "forwardRef", "_", "next", "tabMoving"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabNavList/OperationNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useState, useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport Dropdown from 'rc-dropdown';\nimport AddButton from './AddButton';\n\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n      id = _ref.id,\n      tabs = _ref.tabs,\n      locale = _ref.locale,\n      mobile = _ref.mobile,\n      _ref$moreIcon = _ref.moreIcon,\n      moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n      moreTransitionName = _ref.moreTransitionName,\n      style = _ref.style,\n      className = _ref.className,\n      editable = _ref.editable,\n      tabBarGutter = _ref.tabBarGutter,\n      rtl = _ref.rtl,\n      removeAriaLabel = _ref.removeAriaLabel,\n      onTabClick = _ref.onTabClick,\n      getPopupContainer = _ref.getPopupContainer,\n      popupClassName = _ref.popupClassName;\n\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      open = _useState2[0],\n      setOpen = _useState2[1];\n\n  var _useState3 = useState(null),\n      _useState4 = _slicedToArray(_useState3, 2),\n      selectedKey = _useState4[0],\n      setSelectedKey = _useState4[1];\n\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n          domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var removable = editable && tab.closable !== false && !tab.disabled;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: tab.key,\n      id: \"\".concat(popupId, \"-\").concat(tab.key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(tab.key),\n      disabled: tab.disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, tab.label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, tab.key);\n      }\n    }, tab.closeIcon || editable.removeIcon || '×'));\n  }));\n\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n\n  function onKeyDown(e) {\n    var which = e.which;\n\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n\n      return;\n    }\n\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  } // ========================= Effect =========================\n\n\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]); // ========================= Render =========================\n\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: tabs.length ? open : false,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\n\nexport default /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (// https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,SAAS,MAAM,aAAa;AAEnC,SAASC,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAChC,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,EAAE,GAAGH,IAAI,CAACG,EAAE;IACZC,IAAI,GAAGJ,IAAI,CAACI,IAAI;IAChBC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,aAAa,GAAGP,IAAI,CAACQ,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,aAAa;IAC5DE,kBAAkB,GAAGT,IAAI,CAACS,kBAAkB;IAC5CC,KAAK,GAAGV,IAAI,CAACU,KAAK;IAClBC,SAAS,GAAGX,IAAI,CAACW,SAAS;IAC1BC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;IACxBC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,GAAG,GAAGd,IAAI,CAACc,GAAG;IACdC,eAAe,GAAGf,IAAI,CAACe,eAAe;IACtCC,UAAU,GAAGhB,IAAI,CAACgB,UAAU;IAC5BC,iBAAiB,GAAGjB,IAAI,CAACiB,iBAAiB;IAC1CC,cAAc,GAAGlB,IAAI,CAACkB,cAAc;;EAExC;EACA,IAAIC,SAAS,GAAG3B,QAAQ,CAAC,KAAK,CAAC;IAC3B4B,UAAU,GAAG/B,cAAc,CAAC8B,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIG,UAAU,GAAG/B,QAAQ,CAAC,IAAI,CAAC;IAC3BgC,UAAU,GAAGnC,cAAc,CAACkC,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAElC,IAAIG,OAAO,GAAG,EAAE,CAACC,MAAM,CAACzB,EAAE,EAAE,aAAa,CAAC;EAC1C,IAAI0B,cAAc,GAAG,EAAE,CAACD,MAAM,CAAC1B,SAAS,EAAE,WAAW,CAAC;EACtD,IAAI4B,cAAc,GAAGL,WAAW,KAAK,IAAI,GAAG,EAAE,CAACG,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACH,WAAW,CAAC,GAAG,IAAI;EAC9F,IAAIM,iBAAiB,GAAG1B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC0B,iBAAiB;EAEhG,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/BD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IACvBxB,QAAQ,CAACyB,MAAM,CAAC,QAAQ,EAAE;MACxBH,GAAG,EAAEA,GAAG;MACRD,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EAEA,IAAIK,IAAI,GAAG,aAAahD,KAAK,CAACiD,aAAa,CAAC5C,IAAI,EAAE;IAChD6C,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/B,IAAIP,GAAG,GAAGO,KAAK,CAACP,GAAG;QACfQ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B1B,UAAU,CAACkB,GAAG,EAAEQ,QAAQ,CAAC;MACzBpB,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC;IACDpB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAACC,cAAc,EAAE,OAAO,CAAC;IAC7C1B,EAAE,EAAEwB,OAAO;IACXgB,QAAQ,EAAE,CAAC,CAAC;IACZC,IAAI,EAAE,SAAS;IACf,uBAAuB,EAAEd,cAAc;IACvCe,YAAY,EAAE,CAACpB,WAAW,CAAC;IAC3B,YAAY,EAAEM,iBAAiB,KAAKe,SAAS,GAAGf,iBAAiB,GAAG;EACtE,CAAC,EAAE3B,IAAI,CAAC2C,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzB,IAAIC,SAAS,GAAGrC,QAAQ,IAAIoC,GAAG,CAACE,QAAQ,KAAK,KAAK,IAAI,CAACF,GAAG,CAACG,QAAQ;IACnE,OAAO,aAAa7D,KAAK,CAACiD,aAAa,CAAC3C,QAAQ,EAAE;MAChDsC,GAAG,EAAEc,GAAG,CAACd,GAAG;MACZ/B,EAAE,EAAE,EAAE,CAACyB,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACoB,GAAG,CAACd,GAAG,CAAC;MAC3CU,IAAI,EAAE,QAAQ;MACd,eAAe,EAAEzC,EAAE,IAAI,EAAE,CAACyB,MAAM,CAACzB,EAAE,EAAE,SAAS,CAAC,CAACyB,MAAM,CAACoB,GAAG,CAACd,GAAG,CAAC;MAC/DiB,QAAQ,EAAEH,GAAG,CAACG;IAChB,CAAC,EAAE,aAAa7D,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAES,GAAG,CAACI,KAAK,CAAC,EAAEH,SAAS,IAAI,aAAa3D,KAAK,CAACiD,aAAa,CAAC,QAAQ,EAAE;MACpHc,IAAI,EAAE,QAAQ;MACd,YAAY,EAAEtC,eAAe,IAAI,QAAQ;MACzC4B,QAAQ,EAAE,CAAC;MACXhC,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACC,cAAc,EAAE,mBAAmB,CAAC;MACzDW,OAAO,EAAE,SAASA,OAAOA,CAACc,CAAC,EAAE;QAC3BA,CAAC,CAAClB,eAAe,CAAC,CAAC;QACnBJ,WAAW,CAACsB,CAAC,EAAEN,GAAG,CAACd,GAAG,CAAC;MACzB;IACF,CAAC,EAAEc,GAAG,CAACO,SAAS,IAAI3C,QAAQ,CAAC4C,UAAU,IAAI,GAAG,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC;EAEH,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC5B,IAAIC,WAAW,GAAGvD,IAAI,CAACwD,MAAM,CAAC,UAAUZ,GAAG,EAAE;MAC3C,OAAO,CAACA,GAAG,CAACG,QAAQ;IACtB,CAAC,CAAC;IACF,IAAIU,aAAa,GAAGF,WAAW,CAACG,SAAS,CAAC,UAAUd,GAAG,EAAE;MACvD,OAAOA,GAAG,CAACd,GAAG,KAAKT,WAAW;IAChC,CAAC,CAAC,IAAI,CAAC;IACP,IAAIsC,GAAG,GAAGJ,WAAW,CAACK,MAAM;IAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/BJ,aAAa,GAAG,CAACA,aAAa,GAAGH,MAAM,GAAGK,GAAG,IAAIA,GAAG;MACpD,IAAIf,GAAG,GAAGW,WAAW,CAACE,aAAa,CAAC;MAEpC,IAAI,CAACb,GAAG,CAACG,QAAQ,EAAE;QACjBzB,cAAc,CAACsB,GAAG,CAACd,GAAG,CAAC;QACvB;MACF;IACF;EACF;EAEA,SAASgC,SAASA,CAACZ,CAAC,EAAE;IACpB,IAAIa,KAAK,GAAGb,CAAC,CAACa,KAAK;IAEnB,IAAI,CAAC9C,IAAI,EAAE;MACT,IAAI,CAAC3B,OAAO,CAAC0E,IAAI,EAAE1E,OAAO,CAAC2E,KAAK,EAAE3E,OAAO,CAAC4E,KAAK,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAChE7C,OAAO,CAAC,IAAI,CAAC;QACbgC,CAAC,CAACnB,cAAc,CAAC,CAAC;MACpB;MAEA;IACF;IAEA,QAAQgC,KAAK;MACX,KAAKzE,OAAO,CAAC8E,EAAE;QACbf,YAAY,CAAC,CAAC,CAAC,CAAC;QAChBH,CAAC,CAACnB,cAAc,CAAC,CAAC;QAClB;MAEF,KAAKzC,OAAO,CAAC0E,IAAI;QACfX,YAAY,CAAC,CAAC,CAAC;QACfH,CAAC,CAACnB,cAAc,CAAC,CAAC;QAClB;MAEF,KAAKzC,OAAO,CAAC+E,GAAG;QACdnD,OAAO,CAAC,KAAK,CAAC;QACd;MAEF,KAAK5B,OAAO,CAAC2E,KAAK;MAClB,KAAK3E,OAAO,CAAC4E,KAAK;QAChB,IAAI7C,WAAW,KAAK,IAAI,EAAET,UAAU,CAACS,WAAW,EAAE6B,CAAC,CAAC;QACpD;IACJ;EACF,CAAC,CAAC;;EAGF7D,SAAS,CAAC,YAAY;IACpB;IACA,IAAIiF,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC9C,cAAc,CAAC;IAEjD,IAAI4C,GAAG,IAAIA,GAAG,CAACG,cAAc,EAAE;MAC7BH,GAAG,CAACG,cAAc,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAACpD,WAAW,CAAC,CAAC;EACjBhC,SAAS,CAAC,YAAY;IACpB,IAAI,CAAC4B,IAAI,EAAE;MACTK,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAIyD,SAAS,GAAG1F,eAAe,CAAC,CAAC,CAAC,EAAE0B,GAAG,GAAG,aAAa,GAAG,YAAY,EAAED,YAAY,CAAC;EAErF,IAAI,CAACT,IAAI,CAAC4D,MAAM,EAAE;IAChBc,SAAS,CAACC,UAAU,GAAG,QAAQ;IAC/BD,SAAS,CAACE,KAAK,GAAG,CAAC;EACrB;EAEA,IAAIC,gBAAgB,GAAG1F,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwC,MAAM,CAACC,cAAc,EAAE,MAAM,CAAC,EAAEf,GAAG,CAAC,CAAC;EAC9F,IAAIoE,QAAQ,GAAG5E,MAAM,GAAG,IAAI,GAAG,aAAahB,KAAK,CAACiD,aAAa,CAAC1C,QAAQ,EAAE;IACxEK,SAAS,EAAE2B,cAAc;IACzBsD,OAAO,EAAE7C,IAAI;IACb8C,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,OAAO,EAAEjF,IAAI,CAAC4D,MAAM,GAAG3C,IAAI,GAAG,KAAK;IACnCiE,cAAc,EAAE7E,kBAAkB;IAClC8E,eAAe,EAAEjE,OAAO;IACxB2D,gBAAgB,EAAE1F,UAAU,CAAC0F,gBAAgB,EAAE/D,cAAc,CAAC;IAC9DsE,eAAe,EAAE,GAAG;IACpBC,eAAe,EAAE,GAAG;IACpBxE,iBAAiB,EAAEA;EACrB,CAAC,EAAE,aAAa3B,KAAK,CAACiD,aAAa,CAAC,QAAQ,EAAE;IAC5Cc,IAAI,EAAE,QAAQ;IACd1C,SAAS,EAAE,EAAE,CAACiB,MAAM,CAAC1B,SAAS,EAAE,WAAW,CAAC;IAC5CQ,KAAK,EAAEoE,SAAS;IAChBnC,QAAQ,EAAE,CAAC,CAAC;IACZ,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,SAAS;IAC1B,eAAe,EAAEhB,OAAO;IACxBxB,EAAE,EAAE,EAAE,CAACyB,MAAM,CAACzB,EAAE,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAEkB,IAAI;IACrB6C,SAAS,EAAEA;EACb,CAAC,EAAE1D,QAAQ,CAAC,CAAC;EACb,OAAO,aAAalB,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC7C5B,SAAS,EAAEpB,UAAU,CAAC,EAAE,CAACqC,MAAM,CAAC1B,SAAS,EAAE,iBAAiB,CAAC,EAAES,SAAS,CAAC;IACzED,KAAK,EAAEA,KAAK;IACZT,GAAG,EAAEA;EACP,CAAC,EAAEiF,QAAQ,EAAE,aAAa5F,KAAK,CAACiD,aAAa,CAACzC,SAAS,EAAE;IACvDI,SAAS,EAAEA,SAAS;IACpBG,MAAM,EAAEA,MAAM;IACdO,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AAEA,eAAe,aAAatB,KAAK,CAACoG,IAAI,CAAE,aAAapG,KAAK,CAACqG,UAAU,CAAC5F,aAAa,CAAC,EAAE,UAAU6F,CAAC,EAAEC,IAAI,EAAE;EACvG;IAAQ;IACN;IACAA,IAAI,CAACC;EAAS;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}