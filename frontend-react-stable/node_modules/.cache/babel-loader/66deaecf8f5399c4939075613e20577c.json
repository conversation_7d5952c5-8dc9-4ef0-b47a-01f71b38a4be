{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar epsilon2 = 1e-12;\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\nvar _default = function zoomRho(rho, rho2, rho4) {\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0],\n      uy0 = p0[1],\n      w0 = p0[2],\n      ux1 = p1[0],\n      uy1 = p1[1],\n      w1 = p1[2],\n      dx = ux1 - ux0,\n      dy = uy1 - uy0,\n      d2 = dx * dx + dy * dy,\n      i,\n      S; // Special case for u0 ≅ u1.\n\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function (t) {\n        return [ux0 + t * dx, uy0 + t * dy, w0 * Math.exp(rho * t * S)];\n      };\n    } // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n        b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n        b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n        r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n        r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function (t) {\n        var s = t * S,\n          coshr0 = cosh(r0),\n          u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [ux0 + u * dx, uy0 + u * dy, w0 * coshr0 / cosh(rho * s + r0)];\n      };\n    }\n    i.duration = S * 1000 * rho / Math.SQRT2;\n    return i;\n  }\n  zoom.rho = function (_) {\n    var _1 = Math.max(1e-3, +_),\n      _2 = _1 * _1,\n      _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n  return zoom;\n}(Math.SQRT2, 2, 4);\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "epsilon2", "cosh", "x", "Math", "exp", "sinh", "tanh", "_default", "zoomRho", "rho", "rho2", "rho4", "zoom", "p0", "p1", "ux0", "uy0", "w0", "ux1", "uy1", "w1", "dx", "dy", "d2", "i", "S", "log", "t", "d1", "sqrt", "b0", "b1", "r0", "r1", "s", "coshr0", "u", "duration", "SQRT2", "_", "_1", "max", "_2", "_4"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/zoom.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nvar _default = function zoomRho(rho, rho2, rho4) {\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0],\n        uy0 = p0[1],\n        w0 = p0[2],\n        ux1 = p1[0],\n        uy1 = p1[1],\n        w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S; // Special case for u0 ≅ u1.\n\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n\n      i = function (t) {\n        return [ux0 + t * dx, uy0 + t * dy, w0 * Math.exp(rho * t * S)];\n      };\n    } // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n\n      i = function (t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [ux0 + u * dx, uy0 + u * dy, w0 * coshr0 / cosh(rho * s + r0)];\n      };\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n    return i;\n  }\n\n  zoom.rho = function (_) {\n    var _1 = Math.max(1e-3, +_),\n        _2 = _1 * _1,\n        _4 = _2 * _2;\n\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n}(Math.SQRT2, 2, 4);\n\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,QAAQ,GAAG,KAAK;AAEpB,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,CAAC;AACxC;AAEA,SAASG,IAAIA,CAACH,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,CAAC;AACxC;AAEA,SAASI,IAAIA,CAACJ,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;AAC9C;AAEA,IAAIK,QAAQ,GAAG,SAASC,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/C;EACA;EACA,SAASC,IAAIA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACpB,IAAIC,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;MACXG,GAAG,GAAGH,EAAE,CAAC,CAAC,CAAC;MACXI,EAAE,GAAGJ,EAAE,CAAC,CAAC,CAAC;MACVK,GAAG,GAAGJ,EAAE,CAAC,CAAC,CAAC;MACXK,GAAG,GAAGL,EAAE,CAAC,CAAC,CAAC;MACXM,EAAE,GAAGN,EAAE,CAAC,CAAC,CAAC;MACVO,EAAE,GAAGH,GAAG,GAAGH,GAAG;MACdO,EAAE,GAAGH,GAAG,GAAGH,GAAG;MACdO,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;MACtBE,CAAC;MACDC,CAAC,CAAC,CAAC;;IAEP,IAAIF,EAAE,GAAGvB,QAAQ,EAAE;MACjByB,CAAC,GAAGtB,IAAI,CAACuB,GAAG,CAACN,EAAE,GAAGH,EAAE,CAAC,GAAGR,GAAG;MAE3Be,CAAC,GAAG,SAAAA,CAAUG,CAAC,EAAE;QACf,OAAO,CAACZ,GAAG,GAAGY,CAAC,GAAGN,EAAE,EAAEL,GAAG,GAAGW,CAAC,GAAGL,EAAE,EAAEL,EAAE,GAAGd,IAAI,CAACC,GAAG,CAACK,GAAG,GAAGkB,CAAC,GAAGF,CAAC,CAAC,CAAC;MACjE,CAAC;IACH,CAAC,CAAC;IAAA,KACG;MACH,IAAIG,EAAE,GAAGzB,IAAI,CAAC0B,IAAI,CAACN,EAAE,CAAC;QAClBO,EAAE,GAAG,CAACV,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGN,IAAI,GAAGY,EAAE,KAAK,CAAC,GAAGN,EAAE,GAAGP,IAAI,GAAGkB,EAAE,CAAC;QAC3DG,EAAE,GAAG,CAACX,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGN,IAAI,GAAGY,EAAE,KAAK,CAAC,GAAGH,EAAE,GAAGV,IAAI,GAAGkB,EAAE,CAAC;QAC3DI,EAAE,GAAG7B,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAAC0B,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,CAAC;QAC1CG,EAAE,GAAG9B,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAAC0B,IAAI,CAACE,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,CAAC;MAC9CN,CAAC,GAAG,CAACQ,EAAE,GAAGD,EAAE,IAAIvB,GAAG;MAEnBe,CAAC,GAAG,SAAAA,CAAUG,CAAC,EAAE;QACf,IAAIO,CAAC,GAAGP,CAAC,GAAGF,CAAC;UACTU,MAAM,GAAGlC,IAAI,CAAC+B,EAAE,CAAC;UACjBI,CAAC,GAAGnB,EAAE,IAAIP,IAAI,GAAGkB,EAAE,CAAC,IAAIO,MAAM,GAAG7B,IAAI,CAACG,GAAG,GAAGyB,CAAC,GAAGF,EAAE,CAAC,GAAG3B,IAAI,CAAC2B,EAAE,CAAC,CAAC;QACnE,OAAO,CAACjB,GAAG,GAAGqB,CAAC,GAAGf,EAAE,EAAEL,GAAG,GAAGoB,CAAC,GAAGd,EAAE,EAAEL,EAAE,GAAGkB,MAAM,GAAGlC,IAAI,CAACQ,GAAG,GAAGyB,CAAC,GAAGF,EAAE,CAAC,CAAC;MACvE,CAAC;IACH;IAEAR,CAAC,CAACa,QAAQ,GAAGZ,CAAC,GAAG,IAAI,GAAGhB,GAAG,GAAGN,IAAI,CAACmC,KAAK;IACxC,OAAOd,CAAC;EACV;EAEAZ,IAAI,CAACH,GAAG,GAAG,UAAU8B,CAAC,EAAE;IACtB,IAAIC,EAAE,GAAGrC,IAAI,CAACsC,GAAG,CAAC,IAAI,EAAE,CAACF,CAAC,CAAC;MACvBG,EAAE,GAAGF,EAAE,GAAGA,EAAE;MACZG,EAAE,GAAGD,EAAE,GAAGA,EAAE;IAEhB,OAAOlC,OAAO,CAACgC,EAAE,EAAEE,EAAE,EAAEC,EAAE,CAAC;EAC5B,CAAC;EAED,OAAO/B,IAAI;AACb,CAAC,CAACT,IAAI,CAACmC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAEnBzC,OAAO,CAACE,OAAO,GAAGQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}