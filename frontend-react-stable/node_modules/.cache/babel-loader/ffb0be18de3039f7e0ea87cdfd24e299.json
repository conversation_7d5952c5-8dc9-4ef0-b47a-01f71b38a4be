{"ast": null, "code": "export { getTickValues, getNiceTickValues, getTickValuesFixedDomain } from './getNiceTickValues';", "map": {"version": 3, "names": ["getTick<PERSON><PERSON>ues", "getNiceTickValues", "getTickValuesFixedDomain"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts-scale/es6/index.js"], "sourcesContent": ["export { getTickValues, getNiceTickValues, getTickValuesFixedDomain } from './getNiceTickValues';"], "mappings": "AAAA,SAASA,aAAa,EAAEC,iBAAiB,EAAEC,wBAAwB,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}