{"ast": null, "code": "import * as React from 'react';\nimport useForceUpdate from './useForceUpdate';\nexport default function useSyncState(initialValue) {\n  var ref = React.useRef(initialValue);\n  var forceUpdate = useForceUpdate();\n  return [function () {\n    return ref.current;\n  }, function (newValue) {\n    ref.current = newValue;\n    // re-render\n    forceUpdate();\n  }];\n}", "map": {"version": 3, "names": ["React", "useForceUpdate", "useSyncState", "initialValue", "ref", "useRef", "forceUpdate", "current", "newValue"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/hooks/useSyncState.js"], "sourcesContent": ["import * as React from 'react';\nimport useForceUpdate from './useForceUpdate';\nexport default function useSyncState(initialValue) {\n  var ref = React.useRef(initialValue);\n  var forceUpdate = useForceUpdate();\n  return [function () {\n    return ref.current;\n  }, function (newValue) {\n    ref.current = newValue;\n    // re-render\n    forceUpdate();\n  }];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAE;EACjD,IAAIC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAACF,YAAY,CAAC;EACpC,IAAIG,WAAW,GAAGL,cAAc,CAAC,CAAC;EAClC,OAAO,CAAC,YAAY;IAClB,OAAOG,GAAG,CAACG,OAAO;EACpB,CAAC,EAAE,UAAUC,QAAQ,EAAE;IACrBJ,GAAG,CAACG,OAAO,GAAGC,QAAQ;IACtB;IACAF,WAAW,CAAC,CAAC;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}