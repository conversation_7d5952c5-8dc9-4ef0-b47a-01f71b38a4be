{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, Table, Alert, Statistic, Row, Col, Progress } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay = ({\n  result\n}) => {\n  // 表格列定义\n  const predictionColumns = [{\n    title: '时间戳',\n    dataIndex: 'timestamp',\n    key: 'timestamp',\n    width: 180\n  }, {\n    title: '实际值',\n    dataIndex: 'actual',\n    key: 'actual',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '预测值',\n    dataIndex: 'predicted',\n    key: 'predicted',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '异常状态',\n    dataIndex: 'is_anomaly',\n    key: 'is_anomaly',\n    render: isAnomaly => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: isAnomaly ? '#ff4d4f' : '#52c41a'\n      },\n      children: isAnomaly ? '异常' : '正常'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",\n          value: result.suggested_threshold,\n          precision: 2,\n          valueStyle: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u68C0\\u6D4B\\u5230\\u7684\\u5F02\\u5E38\\u70B9\\u6570\\u91CF\",\n          value: result.anomaly_count,\n          valueStyle: {\n            color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u8FD9\\u662F\\u57FA\\u4E8E\\u56FE\\u4E2D\\u7684\\u52A8\\u6001\\u9608\\u503C\\uFF08\\u7EA2\\u8272\\u865A\\u7EBF\\uFF09\\u68C0\\u6D4B\\u51FA\\u7684\\u3001\\u6D41\\u91CF\\u8D85\\u8FC7\\u9608\\u503C\\u7684\\u5177\\u4F53\\u65F6\\u95F4\\u70B9\\u6570\\u91CF\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), result.predictions && result.predictions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u9884\\u6D4B\\u7ED3\\u679C\\u56FE\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 400,\n          marginTop: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: result.predictions.slice(0, 200) // 显示前200个点\n            ,\n            margin: {\n              top: 5,\n              right: 30,\n              left: 20,\n              bottom: 5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"timestamp\",\n              tick: {\n                fontSize: 10\n              },\n              interval: \"preserveStartEnd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              labelFormatter: value => `时间: ${value}`,\n              formatter: (value, name) => [Number(value === null || value === void 0 ? void 0 : value.toFixed(2)), name]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"actual\",\n              stroke: \"#8884d8\",\n              strokeWidth: 2,\n              dot: false,\n              name: \"\\u5B9E\\u9645\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"predicted\",\n              stroke: \"#82ca9d\",\n              strokeWidth: 2,\n              dot: false,\n              name: \"\\u9884\\u6D4B\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(ReferenceLine, {\n              y: result.suggested_threshold,\n              stroke: \"#ff4d4f\",\n              strokeDasharray: \"5 5\",\n              label: \"\\u5EFA\\u8BAE\\u9608\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u9884\\u6D4B\\u8BE6\\u60C5\\uFF08\\u524D100\\u6761\\uFF09\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: predictionColumns,\n        dataSource: result.predictions.slice(0, 100) // 只显示前100条\n        ,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        },\n        size: \"small\",\n        style: {\n          marginTop: 8\n        },\n        rowKey: (_, index) => `${index}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_c = PredictionResultDisplay;\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\nconst ModelPredictionPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('/data/output');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('/data/output');\n  const [availableModels, setAvailableModels] = useState([]);\n  const [availablePthFiles, setAvailablePthFiles] = useState([]);\n  const [selectedModels, setSelectedModels] = useState([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState('');\n  const [selectedProt, setSelectedProt] = useState('');\n  const [selectedDatatype, setSelectedDatatype] = useState('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState([]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailableModels(response.data.models || []);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取模型文件列表失败');\n      setAvailableModels([]);\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = modelFile => {\n    if (!modelFile) return;\n\n    // 自动匹配参数文件\n    const baseNameWithoutExt = modelFile.replace('.pth', '');\n    const possibleParamsFile = `${baseNameWithoutExt}_params.json`;\n    const possibleScalerFile = `${baseNameWithoutExt}_scaler_y.pkl`;\n    setSelectedParamsFile(possibleParamsFile);\n    setSelectedScalerFile(possibleScalerFile);\n\n    // 尝试从文件名自动识别协议和数据类型\n    const parts = baseNameWithoutExt.split('_');\n    if (parts.length >= 2) {\n      var _datatypeOptions;\n      const protocol = parts[0];\n      const datatype = parts[1];\n      if (protocolOptions.includes(protocol) && (_datatypeOptions = datatypeOptions[protocol]) !== null && _datatypeOptions !== void 0 && _datatypeOptions.includes(datatype)) {\n        setSelectedProt(protocol);\n        setSelectedDatatype(datatype);\n        setShowManualSelection(false);\n      } else {\n        setShowManualSelection(true);\n      }\n    } else {\n      setShowManualSelection(true);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = modelFile => {\n    setSelectedModelFile(modelFile);\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 800); // 800ms延迟\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n  useEffect(() => {\n    if (modelDir) {\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 800); // 800ms延迟\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict = [];\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n      modelsToPredict = selectedModels.map(modelFile => {\n        const model = availableModels.find(m => m.model_file === modelFile);\n        return model || {\n          model_file: modelFile,\n          params_file: '',\n          scaler_file: '',\n          protocol: '',\n          datatype: ''\n        };\n      });\n    }\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n    try {\n      const allResults = [];\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round(i / modelsToPredict.length * 90));\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        const response = await modelPredictionAPI.predict(formData);\n        if (response.data) {\n          allResults.push({\n            model_name: `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || []\n          });\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : csvDir && selectedCsvFile;\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: loading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: csvFilesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCsvFile,\n                onChange: setSelectedCsvFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: csvFilesLoading,\n                children: availableCsvFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6A21\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: modelDir,\n            onChange: e => setModelDir(e.target.value),\n            placeholder: \"\\u4F8B\\u5982: /data/output\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: predictionMode,\n            onChange: e => setPredictionMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multiple\",\n              children: \"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this), predictionMode === 'single' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedModelFile,\n              onChange: handleModelFileChange,\n              placeholder: \"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this), selectedModelFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8,\n                    padding: 12,\n                    backgroundColor: '#f5f5f5',\n                    borderRadius: 4\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u53C2\\u6570\\u6587\\u4EF6:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 28\n                    }, this), \" \", selectedParamsFile]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 28\n                    }, this), \" \", selectedScalerFile]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this), !showManualSelection && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"\\u534F\\u8BAE:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 32\n                      }, this), \" \", selectedProt]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 32\n                      }, this), \" \", selectedDatatype]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this), showManualSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedProt,\n                      onChange: setSelectedProt,\n                      placeholder: \"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",\n                      style: {\n                        width: '100%'\n                      },\n                      children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                        value: prot,\n                        children: prot\n                      }, prot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 616,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 29\n                    }, this), selectedProt && /*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedDatatype,\n                      onChange: setSelectedDatatype,\n                      placeholder: `选择与模型对应的 ${selectedProt} 数据类型`,\n                      style: {\n                        width: '100%'\n                      },\n                      children: (datatypeOptions[selectedProt] || []).map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                        value: datatype,\n                        children: datatype\n                      }, datatype, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              mode: \"multiple\",\n              value: selectedModels,\n              onChange: setSelectedModels,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availableModels.map(model => /*#__PURE__*/_jsxDEV(Option, {\n                value: model.model_file,\n                children: [model.model_file, \" (\", model.protocol, \"_\", model.datatype, \")\"]\n              }, model.model_file, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this), availableModels.length === 0 && !modelsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",\n          description: \"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartPrediction,\n        loading: predicting,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: predicting ? '正在预测...' : '开始预测与检测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this), predicting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 7\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      children: results.length > 1 ?\n      /*#__PURE__*/\n      // 多模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n            onChange: _ => {\n              // 这里可以添加选择特定模型结果的逻辑\n            },\n            children: results.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: index,\n              children: result.model_name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 15\n        }, this), results.map((result, index) => /*#__PURE__*/_jsxDEV(Card, {\n          type: \"inner\",\n          title: `模型: ${result.model_name}`,\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: result\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 19\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 17\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // 单模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", results[0].model_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n          result: results[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 457,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelPredictionPage, \"H4Gyt/yKTg0raXhMAg6NTJCbgHM=\");\n_c2 = ModelPredictionPage;\nexport default ModelPredictionPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PredictionResultDisplay\");\n$RefreshReg$(_c2, \"ModelPredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "Table", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "ReferenceLine", "modelPredictionAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "result", "predictionColumns", "title", "dataIndex", "key", "width", "render", "value", "toFixed", "isAnomaly", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "span", "suggested_threshold", "precision", "valueStyle", "type", "showIcon", "marginTop", "anomaly_count", "fontSize", "predictions", "length", "strong", "height", "data", "slice", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "interval", "labelFormatter", "formatter", "name", "Number", "stroke", "strokeWidth", "dot", "y", "label", "columns", "dataSource", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "size", "<PERSON><PERSON><PERSON>", "_", "index", "_c", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "_s", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availableModels", "setAvailableModels", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "models", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "baseNameWithoutExt", "replace", "possibleParamsFile", "possibleScalerFile", "parts", "split", "_datatypeOptions", "protocol", "datatype", "includes", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "map", "model", "find", "m", "allResults", "i", "Math", "round", "formData", "FormData", "append", "originFileObj", "predict", "push", "model_name", "success", "_error$response3", "_error$response3$data", "isFormValid", "hasData", "level", "className", "direction", "Group", "e", "target", "compact", "display", "placeholder", "flex", "onClick", "loading", "disabled", "marginLeft", "spinning", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Table,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n  // 表格列定义\n  const predictionColumns = [\n    {\n      title: '时间戳',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 180,\n    },\n    {\n      title: '实际值',\n      dataIndex: 'actual',\n      key: 'actual',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '预测值',\n      dataIndex: 'predicted',\n      key: 'predicted',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '异常状态',\n      dataIndex: 'is_anomaly',\n      key: 'is_anomaly',\n      render: (isAnomaly: boolean) => (\n        <span style={{ color: isAnomaly ? '#ff4d4f' : '#52c41a' }}>\n          {isAnomaly ? '异常' : '正常'}\n        </span>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={12}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n        <Col span={12}>\n          <Statistic\n            title=\"检测到的异常点数量\"\n            value={result.anomaly_count}\n            valueStyle={{ color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a' }}\n          />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            这是基于图中的动态阈值（红色虚线）检测出的、流量超过阈值的具体时间点数量。\n          </Text>\n        </Col>\n      </Row>\n\n      {/* 预测图表 */}\n      {result.predictions && result.predictions.length > 0 && (\n        <div style={{ marginBottom: 24 }}>\n          <Text strong>预测结果图表</Text>\n          <div style={{ height: 400, marginTop: 8 }}>\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart\n                data={result.predictions.slice(0, 200)} // 显示前200个点\n                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n              >\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis\n                  dataKey=\"timestamp\"\n                  tick={{ fontSize: 10 }}\n                  interval=\"preserveStartEnd\"\n                />\n                <YAxis />\n                <Tooltip\n                  labelFormatter={(value) => `时间: ${value}`}\n                  formatter={(value: number, name: string) => [Number(value?.toFixed(2)), name]}\n                />\n                <Legend />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"actual\"\n                  stroke=\"#8884d8\"\n                  strokeWidth={2}\n                  dot={false}\n                  name=\"实际值\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"predicted\"\n                  stroke=\"#82ca9d\"\n                  strokeWidth={2}\n                  dot={false}\n                  name=\"预测值\"\n                />\n                {result.suggested_threshold && (\n                  <ReferenceLine\n                    y={result.suggested_threshold}\n                    stroke=\"#ff4d4f\"\n                    strokeDasharray=\"5 5\"\n                    label=\"建议阈值\"\n                  />\n                )}\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      <div>\n        <Text strong>预测详情（前100条）：</Text>\n        <Table\n          columns={predictionColumns}\n          dataSource={result.predictions.slice(0, 100)} // 只显示前100条\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n          size=\"small\"\n          style={{ marginTop: 8 }}\n          rowKey={(_, index) => `${index}`}\n        />\n      </div>\n    </div>\n  );\n};\n\ninterface ModelFile {\n  model_file: string;\n  params_file: string;\n  scaler_file: string;\n  protocol: string;\n  datatype: string;\n}\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  anomaly_count: number;\n  suggested_threshold: number;\n  predictions: Array<{\n    timestamp: string;\n    actual: number;\n    predicted: number;\n    is_anomaly: boolean;\n  }>;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('/data/output');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('/data/output');\n  const [availableModels, setAvailableModels] = useState<ModelFile[]>([]);\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailableModels(response.data.models || []);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailableModels([]);\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = (modelFile: string) => {\n    if (!modelFile) return;\n\n    // 自动匹配参数文件\n    const baseNameWithoutExt = modelFile.replace('.pth', '');\n    const possibleParamsFile = `${baseNameWithoutExt}_params.json`;\n    const possibleScalerFile = `${baseNameWithoutExt}_scaler_y.pkl`;\n\n    setSelectedParamsFile(possibleParamsFile);\n    setSelectedScalerFile(possibleScalerFile);\n\n    // 尝试从文件名自动识别协议和数据类型\n    const parts = baseNameWithoutExt.split('_');\n    if (parts.length >= 2) {\n      const protocol = parts[0];\n      const datatype = parts[1];\n\n      if (protocolOptions.includes(protocol) &&\n          datatypeOptions[protocol as keyof typeof datatypeOptions]?.includes(datatype)) {\n        setSelectedProt(protocol);\n        setSelectedDatatype(datatype);\n        setShowManualSelection(false);\n      } else {\n        setShowManualSelection(true);\n      }\n    } else {\n      setShowManualSelection(true);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 800); // 800ms延迟\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir) {\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 800); // 800ms延迟\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n      modelsToPredict = selectedModels.map(modelFile => {\n        const model = availableModels.find(m => m.model_file === modelFile);\n        return model || {\n          model_file: modelFile,\n          params_file: '',\n          scaler_file: '',\n          protocol: '',\n          datatype: ''\n        };\n      });\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={2}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={loading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input\n              value={modelDir}\n              onChange={(e) => setModelDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                        <p><strong>参数文件:</strong> {selectedParamsFile}</p>\n                        <p><strong>标准化器文件:</strong> {selectedScalerFile}</p>\n                        {!showManualSelection && (\n                          <>\n                            <p><strong>协议:</strong> {selectedProt}</p>\n                            <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                          </>\n                        )}\n                      </div>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availableModels.map((model) => (\n                    <Option key={model.model_file} value={model.model_file}>\n                      {model.model_file} ({model.protocol}_{model.datatype})\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availableModels.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示\n            <div>\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 16 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  onChange={(_) => {\n                    // 这里可以添加选择特定模型结果的逻辑\n                  }}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {results.map((result, index) => (\n                <Card\n                  key={index}\n                  type=\"inner\"\n                  title={`模型: ${result.model_name}`}\n                  style={{ marginBottom: 16 }}\n                >\n                  <PredictionResultDisplay result={result} />\n                </Card>\n              ))}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,QACH,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,UAAU;AAC5H,SAASC,kBAAkB,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B;AAAQ,CAAC,GAAGjC,MAAM;AAC1B,MAAM;EAAEkC;AAAO,CAAC,GAAGhC,MAAM;;AAEzB;AACA,MAAMiC,uBAA+D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EACtF;EACA,MAAMC,iBAAiB,GAAG,CACxB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGG,SAAkB,iBACzBjB,OAAA;MAAMkB,KAAK,EAAE;QAAEC,KAAK,EAAEF,SAAS,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAG,QAAA,EACvDH,SAAS,GAAG,IAAI,GAAG;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAEV,CAAC,CACF;EAED,oBACExB,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAAChB,GAAG;MAACyC,MAAM,EAAE,EAAG;MAACP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC3CpB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,8DAAiB;UACvBK,KAAK,EAAEP,MAAM,CAACoB,mBAAoB;UAClCC,SAAS,EAAE,CAAE;UACbC,UAAU,EAAE;YAAEX,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDhB,MAAM,CAACoB,mBAAmB,iBACzB5B,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,kNAAwC;UAChDoD,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRd,KAAK,EAAE;YAAEe,SAAS,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,wDAAW;UACjBK,KAAK,EAAEP,MAAM,CAAC0B,aAAc;UAC5BJ,UAAU,EAAE;YAAEX,KAAK,EAAEX,MAAM,CAAC0B,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG;UAAU;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACFxB,OAAA,CAACI,IAAI;UAAC2B,IAAI,EAAC,WAAW;UAACb,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAG,CAAE;UAAAf,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhB,MAAM,CAAC4B,WAAW,IAAI5B,MAAM,CAAC4B,WAAW,CAACC,MAAM,GAAG,CAAC,iBAClDrC,OAAA;MAAKkB,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;QAACkC,MAAM;QAAAlB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1BxB,OAAA;QAAKkB,KAAK,EAAE;UAAEqB,MAAM,EAAE,GAAG;UAAEN,SAAS,EAAE;QAAE,CAAE;QAAAb,QAAA,eACxCpB,OAAA,CAACJ,mBAAmB;UAACiB,KAAK,EAAC,MAAM;UAAC0B,MAAM,EAAC,MAAM;UAAAnB,QAAA,eAC7CpB,OAAA,CAACX,SAAS;YACRmD,IAAI,EAAEhC,MAAM,CAAC4B,WAAW,CAACK,KAAK,CAAC,CAAC,EAAE,GAAG,CAAE,CAAC;YAAA;YACxCC,MAAM,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,IAAI,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAA1B,QAAA,gBAEnDpB,OAAA,CAACP,aAAa;cAACsD,eAAe,EAAC;YAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCxB,OAAA,CAACT,KAAK;cACJyD,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAE;gBAAEd,QAAQ,EAAE;cAAG,CAAE;cACvBe,QAAQ,EAAC;YAAkB;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxB,OAAA,CAACR,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTxB,OAAA,CAACN,OAAO;cACNyD,cAAc,EAAGpC,KAAK,IAAK,OAAOA,KAAK,EAAG;cAC1CqC,SAAS,EAAEA,CAACrC,KAAa,EAAEsC,IAAY,KAAK,CAACC,MAAM,CAACvC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEqC,IAAI;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACFxB,OAAA,CAACL,MAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfiB,OAAO,EAAC,QAAQ;cAChBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAK;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfiB,OAAO,EAAC,WAAW;cACnBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAK;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EACDhB,MAAM,CAACoB,mBAAmB,iBACzB5B,OAAA,CAACH,aAAa;cACZ6D,CAAC,EAAElD,MAAM,CAACoB,mBAAoB;cAC9B2B,MAAM,EAAC,SAAS;cAChBR,eAAe,EAAC,KAAK;cACrBY,KAAK,EAAC;YAAM;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDxB,OAAA;MAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;QAACkC,MAAM;QAAAlB,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChCxB,OAAA,CAACnB,KAAK;QACJ+E,OAAO,EAAEnD,iBAAkB;QAC3BoD,UAAU,EAAErD,MAAM,CAAC4B,WAAW,CAACK,KAAK,CAAC,CAAC,EAAE,GAAG,CAAE,CAAC;QAAA;QAC9CqB,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,IAAI,EAAC,OAAO;QACZlD,KAAK,EAAE;UAAEe,SAAS,EAAE;QAAE,CAAE;QACxBoC,MAAM,EAAEA,CAACC,CAAC,EAAEC,KAAK,KAAK,GAAGA,KAAK;MAAG;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACgD,EAAA,GAtIIjE,uBAA+D;AAgJrE;AACA,MAAMkE,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC9C,MAAMC,eAAe,GAAG;EACtBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACnCC,IAAI,EAAE,CAAC,KAAK;AACd,CAAC;AAcD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAAClB,UAAU,EAAEmB,aAAa,CAAC,GAAGhH,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAACiH,YAAY,EAAEC,eAAe,CAAC,GAAGlH,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACmH,MAAM,EAAEC,SAAS,CAAC,GAAGpH,QAAQ,CAAC,cAAc,CAAC;EACpD,MAAM,CAACqH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtH,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACuH,eAAe,EAAEC,kBAAkB,CAAC,GAAGxH,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACyH,eAAe,EAAEC,kBAAkB,CAAC,GAAG1H,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC2H,QAAQ,EAAEC,WAAW,CAAC,GAAG5H,QAAQ,CAAC,cAAc,CAAC;EACxD,MAAM,CAAC6H,eAAe,EAAEC,kBAAkB,CAAC,GAAG9H,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC+H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhI,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACiI,cAAc,EAAEC,iBAAiB,CAAC,GAAGlI,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqI,cAAc,EAAEC,iBAAiB,CAAC,GAAGtI,QAAQ,CAAwB,QAAQ,CAAC;;EAErF;EACA,MAAM,CAACuI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxI,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAACyI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1I,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC2I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5I,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC6I,YAAY,EAAEC,eAAe,CAAC,GAAG9I,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAAC+I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhJ,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACiJ,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqJ,QAAQ,EAAEC,WAAW,CAAC,GAAGtJ,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuJ,OAAO,EAAEC,UAAU,CAAC,GAAGxJ,QAAQ,CAAqB,EAAE,CAAC;;EAE9D;EACA,MAAMyJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACtC,MAAM,EAAE;IAEbO,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAM5H,kBAAkB,CAAC6H,YAAY,CAACxC,MAAM,CAAC;MAC9DG,oBAAoB,CAACoC,QAAQ,CAAClF,IAAI,CAACoF,KAAK,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBpJ,OAAO,CAACkJ,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtF,IAAI,cAAAuF,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAa,CAAC;MAC5D1C,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMuC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACtC,QAAQ,EAAE;IAEfS,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAM5H,kBAAkB,CAACoI,cAAc,CAACvC,QAAQ,CAAC;MAClEG,kBAAkB,CAAC4B,QAAQ,CAAClF,IAAI,CAAC2F,MAAM,IAAI,EAAE,CAAC;MAC9CnC,oBAAoB,CAAC0B,QAAQ,CAAClF,IAAI,CAAC4F,SAAS,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAQ,gBAAA,EAAAC,qBAAA;MACnB3J,OAAO,CAACkJ,KAAK,CAAC,EAAAQ,gBAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7F,IAAI,cAAA8F,qBAAA,uBAApBA,qBAAA,CAAsBN,MAAM,KAAI,YAAY,CAAC;MAC3DlC,kBAAkB,CAAC,EAAE,CAAC;MACtBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmC,cAAc,GAAIC,SAAiB,IAAK;IAC5C,IAAI,CAACA,SAAS,EAAE;;IAEhB;IACA,MAAMC,kBAAkB,GAAGD,SAAS,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACxD,MAAMC,kBAAkB,GAAG,GAAGF,kBAAkB,cAAc;IAC9D,MAAMG,kBAAkB,GAAG,GAAGH,kBAAkB,eAAe;IAE/D/B,qBAAqB,CAACiC,kBAAkB,CAAC;IACzC/B,qBAAqB,CAACgC,kBAAkB,CAAC;;IAEzC;IACA,MAAMC,KAAK,GAAGJ,kBAAkB,CAACK,KAAK,CAAC,GAAG,CAAC;IAC3C,IAAID,KAAK,CAACxG,MAAM,IAAI,CAAC,EAAE;MAAA,IAAA0G,gBAAA;MACrB,MAAMC,QAAQ,GAAGH,KAAK,CAAC,CAAC,CAAC;MACzB,MAAMI,QAAQ,GAAGJ,KAAK,CAAC,CAAC,CAAC;MAEzB,IAAIpE,eAAe,CAACyE,QAAQ,CAACF,QAAQ,CAAC,KAAAD,gBAAA,GAClCrE,eAAe,CAACsE,QAAQ,CAAiC,cAAAD,gBAAA,eAAzDA,gBAAA,CAA2DG,QAAQ,CAACD,QAAQ,CAAC,EAAE;QACjFnC,eAAe,CAACkC,QAAQ,CAAC;QACzBhC,mBAAmB,CAACiC,QAAQ,CAAC;QAC7B/B,sBAAsB,CAAC,KAAK,CAAC;MAC/B,CAAC,MAAM;QACLA,sBAAsB,CAAC,IAAI,CAAC;MAC9B;IACF,CAAC,MAAM;MACLA,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMiC,qBAAqB,GAAIX,SAAiB,IAAK;IACnDhC,oBAAoB,CAACgC,SAAS,CAAC;IAC/BD,cAAc,CAACC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACAvK,SAAS,CAAC,MAAM;IACd,IAAI4F,UAAU,KAAK,OAAO,IAAIsB,MAAM,EAAE;MACpC,MAAMiE,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B5B,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAET,OAAO,MAAM6B,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACvF,UAAU,EAAEsB,MAAM,CAAC,CAAC;EAExBlH,SAAS,CAAC,MAAM;IACd,IAAI0H,QAAQ,EAAE;MACZ,MAAMyD,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BpB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAET,OAAO,MAAMqB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACzD,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM4D,WAAW,GAAG;IAClBlG,IAAI,EAAE,MAAM;IACZmG,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACxH,MAAM,GAAG,CAAC,EAAE;QAC5B6C,eAAe,CAAC0E,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL3E,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAM4E,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAIjG,UAAU,KAAK,QAAQ,IAAI,CAACoB,YAAY,EAAE;MAC5CtG,OAAO,CAACkJ,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIhE,UAAU,KAAK,OAAO,KAAK,CAACsB,MAAM,IAAI,CAACI,eAAe,CAAC,EAAE;MAC3D5G,OAAO,CAACkJ,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;;IAEA;IACA,IAAIkC,eAMF,GAAG,EAAE;IAEP,IAAI1D,cAAc,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACE,iBAAiB,IAAI,CAACM,YAAY,IAAI,CAACE,gBAAgB,EAAE;QAC5DpI,OAAO,CAACkJ,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MACAkC,eAAe,GAAG,CAAC;QACjBC,UAAU,EAAEzD,iBAAiB;QAC7B0D,WAAW,EAAExD,kBAAkB;QAC/ByD,WAAW,EAAEvD,kBAAkB;QAC/BqC,QAAQ,EAAEnC,YAAY;QACtBoC,QAAQ,EAAElC;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAId,cAAc,CAAC5D,MAAM,KAAK,CAAC,EAAE;QAC/B1D,OAAO,CAACkJ,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;MACAkC,eAAe,GAAG9D,cAAc,CAACkE,GAAG,CAAC3B,SAAS,IAAI;QAChD,MAAM4B,KAAK,GAAGvE,eAAe,CAACwE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,UAAU,KAAKxB,SAAS,CAAC;QACnE,OAAO4B,KAAK,IAAI;UACdJ,UAAU,EAAExB,SAAS;UACrByB,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE,EAAE;UACflB,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC,CAAC;IACJ;IAEA7B,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM+C,UAA8B,GAAG,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,eAAe,CAAC1H,MAAM,EAAEmI,CAAC,EAAE,EAAE;QAC/C,MAAMJ,KAAK,GAAGL,eAAe,CAACS,CAAC,CAAC;;QAEhC;QACAlD,WAAW,CAACmD,IAAI,CAACC,KAAK,CAAEF,CAAC,GAAGT,eAAe,CAAC1H,MAAM,GAAI,EAAE,CAAC,CAAC;QAE1D,IAAI0H,eAAe,CAAC1H,MAAM,GAAG,CAAC,EAAE;UAC9B1D,OAAO,CAACiL,IAAI,CAAC,UAAUY,CAAC,GAAG,CAAC,IAAIT,eAAe,CAAC1H,MAAM,KAAK+H,KAAK,CAACJ,UAAU,UAAU,CAAC;QACxF;QAEA,MAAMW,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAI/G,UAAU,KAAK,QAAQ,IAAIoB,YAAY,EAAE;UAC3C;UACA0F,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE5F,YAAY,CAAC6F,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE1F,MAAM,CAAC;UAClCwF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEtF,eAAe,CAAC;QACnD;QAEAoF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAET,KAAK,CAACJ,UAAU,CAAC;QACnDW,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAET,KAAK,CAACH,WAAW,CAAC;QACrDU,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAET,KAAK,CAACF,WAAW,CAAC;QACrDS,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAET,KAAK,CAACpB,QAAQ,CAAC;QAChD2B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAET,KAAK,CAACnB,QAAQ,CAAC;QACpD0B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElF,QAAQ,CAAC;QAE1C,MAAM+B,QAAQ,GAAG,MAAM5H,kBAAkB,CAACiL,OAAO,CAACJ,QAAQ,CAAC;QAE3D,IAAIjD,QAAQ,CAAClF,IAAI,EAAE;UACjB+H,UAAU,CAACS,IAAI,CAAC;YACdC,UAAU,EAAE,GAAGb,KAAK,CAACpB,QAAQ,IAAIoB,KAAK,CAACnB,QAAQ,EAAE;YACjD/G,aAAa,EAAEwF,QAAQ,CAAClF,IAAI,CAACN,aAAa,IAAI,CAAC;YAC/CN,mBAAmB,EAAE8F,QAAQ,CAAClF,IAAI,CAACZ,mBAAmB,IAAI,CAAC;YAC3DQ,WAAW,EAAEsF,QAAQ,CAAClF,IAAI,CAACJ,WAAW,IAAI;UAC5C,CAAC,CAAC;UAEF,IAAI2H,eAAe,CAAC1H,MAAM,GAAG,CAAC,EAAE;YAC9B1D,OAAO,CAACuM,OAAO,CAAC,QAAQd,KAAK,CAACJ,UAAU,OAAO,CAAC;UAClD;QACF;MACF;MAEA1C,WAAW,CAAC,GAAG,CAAC;MAChBE,UAAU,CAAC+C,UAAU,CAAC;MACtB5L,OAAO,CAACuM,OAAO,CAAC,SAASX,UAAU,CAAClI,MAAM,SAAS,CAAC;IAEtD,CAAC,CAAC,OAAOwF,KAAU,EAAE;MAAA,IAAAsD,gBAAA,EAAAC,qBAAA;MACnBzM,OAAO,CAACkJ,KAAK,CAAC,EAAAsD,gBAAA,GAAAtD,KAAK,CAACH,QAAQ,cAAAyD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3I,IAAI,cAAA4I,qBAAA,uBAApBA,qBAAA,CAAsBpD,MAAM,KAAI,MAAM,CAAC;IACvD,CAAC,SAAS;MACRZ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMiE,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAGzH,UAAU,KAAK,QAAQ,GAAGoB,YAAY,GAAIE,MAAM,IAAII,eAAgB;IAEpF,IAAIc,cAAc,KAAK,QAAQ,EAAE;MAC/B,OAAOiF,OAAO,IAAI/E,iBAAiB,IAAIM,YAAY,IAAIE,gBAAgB;IACzE,CAAC,MAAM;MACL,OAAOuE,OAAO,IAAIrF,cAAc,CAAC5D,MAAM,GAAG,CAAC;IAC7C;EACF,CAAC;EAID,oBACErC,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;MAACoL,KAAK,EAAE,CAAE;MAAAnK,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACpCxB,OAAA,CAACI,IAAI;MAAC2B,IAAI,EAAC,WAAW;MAAAX,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxB,OAAA,CAACtB,OAAO;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXxB,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,gCAAO;MAAC8K,SAAS,EAAC,eAAe;MAAApK,QAAA,eAC3CpB,OAAA,CAACvB,KAAK;QAACgN,SAAS,EAAC,UAAU;QAACrH,IAAI,EAAC,OAAO;QAAClD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BxB,OAAA,CAAC7B,KAAK,CAACuN,KAAK;YACV3K,KAAK,EAAE8C,UAAW;YAClB8F,QAAQ,EAAGgC,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;YAC/CG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCxB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,OAAO;cAAAK,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLqC,UAAU,KAAK,QAAQ,iBACtB7D,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCxB,OAAA,CAACK,OAAO;YAAA,GAAKkJ,WAAW;YAAErI,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAChDpB,OAAA;cAAGwL,SAAS,EAAC,sBAAsB;cAAApK,QAAA,eACjCpB,OAAA,CAACb,aAAa;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJxB,OAAA;cAAGwL,SAAS,EAAC,iBAAiB;cAAApK,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDxB,OAAA;cAAGwL,SAAS,EAAC,iBAAiB;cAAApK,QAAA,EAAC;YAE/B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGAqC,UAAU,KAAK,OAAO,iBACrB7D,OAAA,CAACvB,KAAK;UAACgN,SAAS,EAAC,UAAU;UAACvK,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAO,CAAE;UAAAO,QAAA,gBACnDpB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAAC3B,KAAK,CAACqN,KAAK;cAACG,OAAO;cAAC3K,KAAK,EAAE;gBAAEe,SAAS,EAAE,CAAC;gBAAE6J,OAAO,EAAE;cAAO,CAAE;cAAA1K,QAAA,gBAC5DpB,OAAA,CAAC3B,KAAK;gBACJ0C,KAAK,EAAEoE,MAAO;gBACdwE,QAAQ,EAAGgC,CAAC,IAAKvG,SAAS,CAACuG,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;gBAC3CgL,WAAW,EAAC,4BAAkB;gBAC9B7K,KAAK,EAAE;kBAAE8K,IAAI,EAAE;gBAAE;cAAE;gBAAA3K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFxB,OAAA,CAACzB,MAAM;gBACLwD,IAAI,EAAC,SAAS;gBACdkK,OAAO,EAAExE,aAAc;gBACvByE,OAAO,EAAEA,OAAQ;gBACjBC,QAAQ,EAAE,CAAChH,MAAO;gBAClBjE,KAAK,EAAE;kBAAEkL,UAAU,EAAE;gBAAE,CAAE;gBAAAhL,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAACpB,IAAI;cAACyN,QAAQ,EAAE5G,eAAgB;cAAArE,QAAA,eAC9BpB,OAAA,CAAC1B,MAAM;gBACLyC,KAAK,EAAEwE,eAAgB;gBACvBoE,QAAQ,EAAEnE,kBAAmB;gBAC7BuG,WAAW,EAAC,mCAAU;gBACtB7K,KAAK,EAAE;kBAAEL,KAAK,EAAE,MAAM;kBAAEoB,SAAS,EAAE;gBAAE,CAAE;gBACvCiK,OAAO,EAAEzG,eAAgB;gBAAArE,QAAA,EAExBiE,iBAAiB,CAAC8E,GAAG,CAAEmC,IAAI,iBAC1BtM,OAAA,CAACM,MAAM;kBAAYS,KAAK,EAAEuL,IAAK;kBAAAlL,QAAA,EAC5BkL;gBAAI,GADMA,IAAI;kBAAAjL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,0BAAM;MAAC8K,SAAS,EAAC,eAAe;MAAApK,QAAA,eAC1CpB,OAAA,CAACvB,KAAK;QAACgN,SAAS,EAAC,UAAU;QAACrH,IAAI,EAAC,OAAO;QAAClD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC3B,KAAK;YACJ0C,KAAK,EAAE4E,QAAS;YAChBgE,QAAQ,EAAGgC,CAAC,IAAK/F,WAAW,CAAC+F,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;YAC7CgL,WAAW,EAAC,4BAAkB;YAC9B7K,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC7B,KAAK,CAACuN,KAAK;YACV3K,KAAK,EAAEsF,cAAe;YACtBsD,QAAQ,EAAGgC,CAAC,IAAKrF,iBAAiB,CAACqF,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;YACnDG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,UAAU;cAAAK,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAEL6E,cAAc,KAAK,QAAQ,gBAC1BrG,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAACpB,IAAI;YAACyN,QAAQ,EAAElG,aAAc;YAAA/E,QAAA,eAC5BpB,OAAA,CAAC1B,MAAM;cACLyC,KAAK,EAAEwF,iBAAkB;cACzBoD,QAAQ,EAAER,qBAAsB;cAChC4C,WAAW,EAAC,kMAAkC;cAC9C7K,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvCiK,OAAO,EAAE/F,aAAc;cAAA/E,QAAA,EAEtB2E,iBAAiB,CAACoE,GAAG,CAAEmC,IAAI,iBAC1BtM,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEuL,IAAK;gBAAAlL,QAAA,EAC5BkL;cAAI,GADMA,IAAI;gBAAAjL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAEN+E,iBAAiB,iBAChBvG,OAAA;YAAKkB,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAG,CAAE;YAAAb,QAAA,eAC5BpB,OAAA,CAACvB,KAAK;cAACgN,SAAS,EAAC,UAAU;cAACvK,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cAAAO,QAAA,gBACnDpB,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAAAX,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxB,OAAA;kBAAKkB,KAAK,EAAE;oBAAEe,SAAS,EAAE,CAAC;oBAAEsK,OAAO,EAAE,EAAE;oBAAEC,eAAe,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAArL,QAAA,gBACrFpB,OAAA;oBAAAoB,QAAA,gBAAGpB,OAAA;sBAAAoB,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiF,kBAAkB;kBAAA;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClDxB,OAAA;oBAAAoB,QAAA,gBAAGpB,OAAA;sBAAAoB,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACmF,kBAAkB;kBAAA;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnD,CAACyF,mBAAmB,iBACnBjH,OAAA,CAAAE,SAAA;oBAAAkB,QAAA,gBACEpB,OAAA;sBAAAoB,QAAA,gBAAGpB,OAAA;wBAAAoB,QAAA,EAAQ;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACqF,YAAY;oBAAA;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1CxB,OAAA;sBAAAoB,QAAA,gBAAGpB,OAAA;wBAAAoB,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuF,gBAAgB;oBAAA;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,eAChD,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELyF,mBAAmB,iBAClBjH,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAACkC,MAAM;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCxB,OAAA;kBAAKkB,KAAK,EAAE;oBAAEe,SAAS,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAC3BpB,OAAA,CAACvB,KAAK;oBAACgN,SAAS,EAAC,UAAU;oBAACvK,KAAK,EAAE;sBAAEL,KAAK,EAAE;oBAAO,CAAE;oBAAAO,QAAA,gBACnDpB,OAAA,CAAC1B,MAAM;sBACLyC,KAAK,EAAE8F,YAAa;sBACpB8C,QAAQ,EAAE7C,eAAgB;sBAC1BiF,WAAW,EAAC,8DAAY;sBACxB7K,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExBqD,eAAe,CAAC0F,GAAG,CAAEuC,IAAI,iBACxB1M,OAAA,CAACM,MAAM;wBAAYS,KAAK,EAAE2L,IAAK;wBAAAtL,QAAA,EAC5BsL;sBAAI,GADMA,IAAI;wBAAArL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EAERqF,YAAY,iBACX7G,OAAA,CAAC1B,MAAM;sBACLyC,KAAK,EAAEgG,gBAAiB;sBACxB4C,QAAQ,EAAE3C,mBAAoB;sBAC9B+E,WAAW,EAAE,YAAYlF,YAAY,OAAQ;sBAC7C3F,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExB,CAACsD,eAAe,CAACmC,YAAY,CAAiC,IAAI,EAAE,EAAEsD,GAAG,CAAElB,QAAQ,iBAClFjJ,OAAA,CAACM,MAAM;wBAAgBS,KAAK,EAAEkI,QAAS;wBAAA7H,QAAA,EACpC6H;sBAAQ,GADEA,QAAQ;wBAAA5H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEb,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAACpB,IAAI;YAACyN,QAAQ,EAAElG,aAAc;YAAA/E,QAAA,eAC5BpB,OAAA,CAAC1B,MAAM;cACLqO,IAAI,EAAC,UAAU;cACf5L,KAAK,EAAEkF,cAAe;cACtB0D,QAAQ,EAAEzD,iBAAkB;cAC5B6F,WAAW,EAAC,4FAAiB;cAC7B7K,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvCiK,OAAO,EAAE/F,aAAc;cAAA/E,QAAA,EAEtByE,eAAe,CAACsE,GAAG,CAAEC,KAAK,iBACzBpK,OAAA,CAACM,MAAM;gBAAwBS,KAAK,EAAEqJ,KAAK,CAACJ,UAAW;gBAAA5I,QAAA,GACpDgJ,KAAK,CAACJ,UAAU,EAAC,IAAE,EAACI,KAAK,CAACpB,QAAQ,EAAC,GAAC,EAACoB,KAAK,CAACnB,QAAQ,EAAC,GACvD;cAAA,GAFamB,KAAK,CAACJ,UAAU;gBAAA3I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAqE,eAAe,CAACxD,MAAM,KAAK,CAAC,IAAI,CAAC8D,aAAa,iBAC7CnG,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,4CAAS;UACjBiO,WAAW,EAAC,oOAA2C;UACvD7K,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC9B,IAAI;MAACsN,SAAS,EAAC,eAAe;MAAApK,QAAA,gBAC7BpB,OAAA,CAACzB,MAAM;QACLwD,IAAI,EAAC,SAAS;QACdqC,IAAI,EAAC,OAAO;QACZyI,IAAI,eAAE7M,OAAA,CAACZ,kBAAkB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7ByK,OAAO,EAAEnC,qBAAsB;QAC/BoC,OAAO,EAAE/E,UAAW;QACpBgF,QAAQ,EAAE,CAACd,WAAW,CAAC,CAAE;QACzBG,SAAS,EAAC,eAAe;QAAApK,QAAA,EAExB+F,UAAU,GAAG,SAAS,GAAG;MAAS;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EAGR2F,UAAU,iBACTnH,OAAA;QAAKwL,SAAS,EAAC,kBAAkB;QAAApK,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;UAAAgB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBxB,OAAA,CAACd,QAAQ;UAAC4N,OAAO,EAAEzF,QAAS;UAAC0F,MAAM,EAAC;QAAQ;UAAA1L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGN+F,OAAO,CAAClF,MAAM,GAAG,CAAC,iBACjBrC,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,0BAAM;MAAC8K,SAAS,EAAC,eAAe;MAAApK,QAAA,EACzCmG,OAAO,CAAClF,MAAM,GAAG,CAAC;MAAA;MACjB;MACArC,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;UAACoL,KAAK,EAAE,CAAE;UAAAnK,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxB,OAAA;UAAKkB,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAAC1B,MAAM;YACL4C,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAEoB,SAAS,EAAE;YAAE,CAAE;YACvC8J,WAAW,EAAC,sCAAQ;YACpBpC,QAAQ,EAAGrF,CAAC,IAAK;cACf;YAAA,CACA;YAAAlD,QAAA,EAEDmG,OAAO,CAAC4C,GAAG,CAAC,CAAC3J,MAAM,EAAE+D,KAAK,kBACzBvE,OAAA,CAACM,MAAM;cAAaS,KAAK,EAAEwD,KAAM;cAAAnD,QAAA,EAC9BZ,MAAM,CAACyK;YAAU,GADP1G,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL+F,OAAO,CAAC4C,GAAG,CAAC,CAAC3J,MAAM,EAAE+D,KAAK,kBACzBvE,OAAA,CAAC9B,IAAI;UAEH6D,IAAI,EAAC,OAAO;UACZrB,KAAK,EAAE,OAAOF,MAAM,CAACyK,UAAU,EAAG;UAClC/J,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,eAE5BpB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEA;UAAO;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GALtC+C,KAAK;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMN,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;MAAA;MAEN;MACAxB,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;UAACoL,KAAK,EAAE,CAAE;UAAAnK,QAAA,GAAC,6BAAO,EAACmG,OAAO,CAAC,CAAC,CAAC,CAAC0D,UAAU;QAAA;UAAA5J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvDxB,OAAA,CAACO,uBAAuB;UAACC,MAAM,EAAE+G,OAAO,CAAC,CAAC;QAAE;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACuD,EAAA,CAxiBID,mBAA6B;AAAAkI,GAAA,GAA7BlI,mBAA6B;AA0iBnC,eAAeA,mBAAmB;AAAC,IAAAN,EAAA,EAAAwI,GAAA;AAAAC,YAAA,CAAAzI,EAAA;AAAAyI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}