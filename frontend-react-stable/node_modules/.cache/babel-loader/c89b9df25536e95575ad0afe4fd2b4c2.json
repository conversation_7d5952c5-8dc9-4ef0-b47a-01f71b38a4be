{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree, { TreeNode } from 'rc-tree';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport DirectoryTree from './DirectoryTree';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport renderSwitcherIcon from './utils/iconUtil';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    virtual = _React$useContext.virtual;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$showIcon = props.showIcon,\n    showIcon = _props$showIcon === void 0 ? false : _props$showIcon,\n    showLine = props.showLine,\n    _switcherIcon = props.switcherIcon,\n    _props$blockNode = props.blockNode,\n    blockNode = _props$blockNode === void 0 ? false : _props$blockNode,\n    children = props.children,\n    _props$checkable = props.checkable,\n    checkable = _props$checkable === void 0 ? false : _props$checkable,\n    _props$selectable = props.selectable,\n    selectable = _props$selectable === void 0 ? true : _props$selectable,\n    draggable = props.draggable,\n    _props$motion = props.motion,\n    motion = _props$motion === void 0 ? _extends(_extends({}, collapseMotion), {\n      motionAppear: false\n    }) : _props$motion;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n  var newProps = _extends(_extends({}, props), {\n    checkable: checkable,\n    selectable: selectable,\n    showIcon: showIcon,\n    motion: motion,\n    blockNode: blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n    var mergedDraggable = {};\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block-node\"), blockNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-unselectable\"), !selectable), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nTree.TreeNode = TreeNode;\nTree.DirectoryTree = DirectoryTree;\nexport default Tree;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "_extends", "Holder<PERSON><PERSON><PERSON>", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "TreeNode", "React", "ConfigContext", "collapseMotion", "DirectoryTree", "dropIndicatorRender", "renderSwitcherIcon", "Tree", "forwardRef", "props", "ref", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "virtual", "customizePrefixCls", "prefixCls", "className", "_props$showIcon", "showIcon", "showLine", "_switcherIcon", "switcherIcon", "_props$blockNode", "blockNode", "children", "_props$checkable", "checkable", "_props$selectable", "selectable", "draggable", "_props$motion", "motion", "motionAppear", "newProps", "Boolean", "draggableConfig", "useMemo", "mergedDraggable", "nodeDraggable", "icon", "createElement", "itemHeight", "concat", "nodeProps"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tree/Tree.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree, { TreeNode } from 'rc-tree';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport DirectoryTree from './DirectoryTree';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport renderSwitcherIcon from './utils/iconUtil';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    virtual = _React$useContext.virtual;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$showIcon = props.showIcon,\n    showIcon = _props$showIcon === void 0 ? false : _props$showIcon,\n    showLine = props.showLine,\n    _switcherIcon = props.switcherIcon,\n    _props$blockNode = props.blockNode,\n    blockNode = _props$blockNode === void 0 ? false : _props$blockNode,\n    children = props.children,\n    _props$checkable = props.checkable,\n    checkable = _props$checkable === void 0 ? false : _props$checkable,\n    _props$selectable = props.selectable,\n    selectable = _props$selectable === void 0 ? true : _props$selectable,\n    draggable = props.draggable,\n    _props$motion = props.motion,\n    motion = _props$motion === void 0 ? _extends(_extends({}, collapseMotion), {\n      motionAppear: false\n    }) : _props$motion;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n  var newProps = _extends(_extends({}, props), {\n    checkable: checkable,\n    selectable: selectable,\n    showIcon: showIcon,\n    motion: motion,\n    blockNode: blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n    var mergedDraggable = {};\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block-node\"), blockNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-unselectable\"), !selectable), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nTree.TreeNode = TreeNode;\nTree.DirectoryTree = DirectoryTree;\nexport default Tree;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,IAAIC,QAAQ,QAAQ,SAAS;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,kBAAkB,MAAM,kBAAkB;AACjD,IAAIC,IAAI,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW;EACf,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACX,aAAa,CAAC;IACrDY,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;IACvCC,OAAO,GAAGJ,iBAAiB,CAACI,OAAO;EACrC,IAAIC,kBAAkB,GAAGR,KAAK,CAACS,SAAS;IACtCC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,eAAe,GAAGX,KAAK,CAACY,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,aAAa,GAAGd,KAAK,CAACe,YAAY;IAClCC,gBAAgB,GAAGhB,KAAK,CAACiB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,gBAAgB,GAAGnB,KAAK,CAACoB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,SAAS,GAAGvB,KAAK,CAACuB,SAAS;IAC3BC,aAAa,GAAGxB,KAAK,CAACyB,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGrC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,cAAc,CAAC,EAAE;MACzEgC,YAAY,EAAE;IAChB,CAAC,CAAC,GAAGF,aAAa;EACpB,IAAIf,SAAS,GAAGJ,YAAY,CAAC,MAAM,EAAEG,kBAAkB,CAAC;EACxD,IAAImB,QAAQ,GAAGxC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,CAAC,EAAE;IAC3CoB,SAAS,EAAEA,SAAS;IACpBE,UAAU,EAAEA,UAAU;IACtBV,QAAQ,EAAEA,QAAQ;IAClBa,MAAM,EAAEA,MAAM;IACdR,SAAS,EAAEA,SAAS;IACpBJ,QAAQ,EAAEe,OAAO,CAACf,QAAQ,CAAC;IAC3BjB,mBAAmB,EAAEA;EACvB,CAAC,CAAC;EACF,IAAIiC,eAAe,GAAGrC,KAAK,CAACsC,OAAO,CAAC,YAAY;IAC9C,IAAI,CAACP,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IACA,IAAIQ,eAAe,GAAG,CAAC,CAAC;IACxB,QAAQ7C,OAAO,CAACqC,SAAS,CAAC;MACxB,KAAK,UAAU;QACbQ,eAAe,CAACC,aAAa,GAAGT,SAAS;QACzC;MACF,KAAK,QAAQ;QACXQ,eAAe,GAAG5C,QAAQ,CAAC,CAAC,CAAC,EAAEoC,SAAS,CAAC;QACzC;MACF;QACE;MACF;IACF;IAEA,IAAIQ,eAAe,CAACE,IAAI,KAAK,KAAK,EAAE;MAClCF,eAAe,CAACE,IAAI,GAAGF,eAAe,CAACE,IAAI,IAAI,aAAazC,KAAK,CAAC0C,aAAa,CAAC9C,cAAc,EAAE,IAAI,CAAC;IACvG;IACA,OAAO2C,eAAe;EACxB,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EACf,OAAO,aAAa/B,KAAK,CAAC0C,aAAa,CAAC5C,MAAM,EAAEH,QAAQ,CAAC;IACvDgD,UAAU,EAAE,EAAE;IACdlC,GAAG,EAAEA,GAAG;IACRM,OAAO,EAAEA;EACX,CAAC,EAAEoB,QAAQ,EAAE;IACXlB,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAErB,UAAU,EAAEa,WAAW,GAAG,CAAC,CAAC,EAAEjB,eAAe,CAACiB,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC3B,SAAS,EAAE,YAAY,CAAC,EAAE,CAACG,QAAQ,CAAC,EAAE3B,eAAe,CAACiB,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC3B,SAAS,EAAE,aAAa,CAAC,EAAEQ,SAAS,CAAC,EAAEhC,eAAe,CAACiB,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC3B,SAAS,EAAE,eAAe,CAAC,EAAE,CAACa,UAAU,CAAC,EAAErC,eAAe,CAACiB,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEH,SAAS,KAAK,KAAK,CAAC,EAAEJ,WAAW,GAAGQ,SAAS,CAAC;IAC/XJ,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEA,SAAS,GAAG,aAAa5B,KAAK,CAAC0C,aAAa,CAAC,MAAM,EAAE;MAC9DxB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAAC3B,SAAS,EAAE,iBAAiB;IACnD,CAAC,CAAC,GAAGW,SAAS;IACdE,UAAU,EAAEA,UAAU;IACtBP,YAAY,EAAE,SAASA,YAAYA,CAACsB,SAAS,EAAE;MAC7C,OAAOxC,kBAAkB,CAACY,SAAS,EAAEK,aAAa,EAAED,QAAQ,EAAEwB,SAAS,CAAC;IAC1E,CAAC;IACDd,SAAS,EAAEM;EACb,CAAC,CAAC,EAAEX,QAAQ,CAAC;AACf,CAAC,CAAC;AACFpB,IAAI,CAACP,QAAQ,GAAGA,QAAQ;AACxBO,IAAI,CAACH,aAAa,GAAGA,aAAa;AAClC,eAAeG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}