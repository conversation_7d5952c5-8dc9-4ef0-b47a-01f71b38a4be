{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { List } from 'rc-field-form';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nimport { FormItemPrefixContext } from './context';\nvar FormList = function FormList(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    props = __rest(_a, [\"prefixCls\", \"children\"]);\n  process.env.NODE_ENV !== \"production\" ? warning(!!props.name, 'Form.List', 'Miss `name` prop.') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  var contextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: 'error'\n    };\n  }, [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, _extends({}, props), function (fields, operation, meta) {\n    return /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n      value: contextValue\n    }, children(fields.map(function (field) {\n      return _extends(_extends({}, field), {\n        fieldKey: field.key\n      });\n    }), operation, {\n      errors: meta.errors,\n      warnings: meta.warnings\n    }));\n  });\n};\nexport default FormList;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "List", "React", "ConfigContext", "warning", "FormItemPrefixContext", "FormList", "_a", "customizePrefixCls", "prefixCls", "children", "props", "process", "env", "NODE_ENV", "name", "_React$useContext", "useContext", "getPrefixCls", "contextValue", "useMemo", "status", "createElement", "fields", "operation", "meta", "Provider", "value", "map", "field", "<PERSON><PERSON><PERSON>", "key", "errors", "warnings"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/FormList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { List } from 'rc-field-form';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nimport { FormItemPrefixContext } from './context';\nvar FormList = function FormList(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    props = __rest(_a, [\"prefixCls\", \"children\"]);\n  process.env.NODE_ENV !== \"production\" ? warning(!!props.name, 'Form.List', 'Miss `name` prop.') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  var contextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: 'error'\n    };\n  }, [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, _extends({}, props), function (fields, operation, meta) {\n    return /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n      value: contextValue\n    }, children(fields.map(function (field) {\n      return _extends(_extends({}, field), {\n        fieldKey: field.key\n      });\n    }), operation, {\n      errors: meta.errors,\n      warnings: meta.warnings\n    }));\n  });\n};\nexport default FormList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,SAASW,IAAI,QAAQ,eAAe;AACpC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,qBAAqB,QAAQ,WAAW;AACjD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,EAAE,EAAE;EACnC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IACtBC,KAAK,GAAGxB,MAAM,CAACoB,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EAC/CK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,OAAO,CAAC,CAAC,CAACO,KAAK,CAACI,IAAI,EAAE,WAAW,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAC;EACxG,IAAIC,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACd,aAAa,CAAC;IACrDe,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIT,SAAS,GAAGS,YAAY,CAAC,MAAM,EAAEV,kBAAkB,CAAC;EACxD,IAAIW,YAAY,GAAGjB,KAAK,CAACkB,OAAO,CAAC,YAAY;IAC3C,OAAO;MACLX,SAAS,EAAEA,SAAS;MACpBY,MAAM,EAAE;IACV,CAAC;EACH,CAAC,EAAE,CAACZ,SAAS,CAAC,CAAC;EACf,OAAO,aAAaP,KAAK,CAACoB,aAAa,CAACrB,IAAI,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAAC,EAAE,UAAUY,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAE;IACpG,OAAO,aAAavB,KAAK,CAACoB,aAAa,CAACjB,qBAAqB,CAACqB,QAAQ,EAAE;MACtEC,KAAK,EAAER;IACT,CAAC,EAAET,QAAQ,CAACa,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;MACtC,OAAO3C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE;QACnCC,QAAQ,EAAED,KAAK,CAACE;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC,EAAEP,SAAS,EAAE;MACbQ,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBC,QAAQ,EAAER,IAAI,CAACQ;IACjB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AACD,eAAe3B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}