{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _postcssValueParser = require('postcss-value-parser');\nvar _postcssValueParser2 = _interopRequireDefault(_postcssValueParser);\nvar _parser = require('./parser');\nvar _reducer = require('./lib/reducer');\nvar _reducer2 = _interopRequireDefault(_reducer);\nvar _stringifier = require('./lib/stringifier');\nvar _stringifier2 = _interopRequireDefault(_stringifier);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n// eslint-disable-line\nvar MATCH_CALC = /((?:\\-[a-z]+\\-)?calc)/;\nexports.default = function (value) {\n  var precision = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n  return (0, _postcssValueParser2.default)(value).walk(function (node) {\n    // skip anything which isn't a calc() function\n    if (node.type !== 'function' || !MATCH_CALC.test(node.value)) return;\n\n    // stringify calc expression and produce an AST\n    var contents = _postcssValueParser2.default.stringify(node.nodes);\n\n    // skip constant() and env()\n    if (contents.indexOf('constant') >= 0 || contents.indexOf('env') >= 0) return;\n    var ast = _parser.parser.parse(contents);\n\n    // reduce AST to its simplest form, that is, either to a single value\n    // or a simplified calc expression\n    var reducedAst = (0, _reducer2.default)(ast, precision);\n\n    // stringify AST and write it back\n    node.type = 'word';\n    node.value = (0, _stringifier2.default)(node.value, reducedAst, precision);\n  }, true).toString();\n};\nmodule.exports = exports['default'];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_postcss<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_postcssValueParser2", "_interopRequireDefault", "_parser", "_reducer", "_reducer2", "_stringifier", "_stringifier2", "obj", "__esModule", "default", "MATCH_CALC", "precision", "arguments", "length", "undefined", "walk", "node", "type", "test", "contents", "stringify", "nodes", "indexOf", "ast", "parser", "parse", "reducedAst", "toString", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/dist/index.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _postcssValueParser = require('postcss-value-parser');\n\nvar _postcssValueParser2 = _interopRequireDefault(_postcssValueParser);\n\nvar _parser = require('./parser');\n\nvar _reducer = require('./lib/reducer');\n\nvar _reducer2 = _interopRequireDefault(_reducer);\n\nvar _stringifier = require('./lib/stringifier');\n\nvar _stringifier2 = _interopRequireDefault(_stringifier);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// eslint-disable-line\nvar MATCH_CALC = /((?:\\-[a-z]+\\-)?calc)/;\n\nexports.default = function (value) {\n  var precision = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n\n  return (0, _postcssValueParser2.default)(value).walk(function (node) {\n    // skip anything which isn't a calc() function\n    if (node.type !== 'function' || !MATCH_CALC.test(node.value)) return;\n\n    // stringify calc expression and produce an AST\n    var contents = _postcssValueParser2.default.stringify(node.nodes);\n\n    // skip constant() and env()\n    if (contents.indexOf('constant') >= 0 || contents.indexOf('env') >= 0) return;\n\n    var ast = _parser.parser.parse(contents);\n\n    // reduce AST to its simplest form, that is, either to a single value\n    // or a simplified calc expression\n    var reducedAst = (0, _reducer2.default)(ast, precision);\n\n    // stringify AST and write it back\n    node.type = 'word';\n    node.value = (0, _stringifier2.default)(node.value, reducedAst, precision);\n  }, true).toString();\n};\n\nmodule.exports = exports['default'];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,mBAAmB,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAEzD,IAAIC,oBAAoB,GAAGC,sBAAsB,CAACH,mBAAmB,CAAC;AAEtE,IAAII,OAAO,GAAGH,OAAO,CAAC,UAAU,CAAC;AAEjC,IAAII,QAAQ,GAAGJ,OAAO,CAAC,eAAe,CAAC;AAEvC,IAAIK,SAAS,GAAGH,sBAAsB,CAACE,QAAQ,CAAC;AAEhD,IAAIE,YAAY,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAE/C,IAAIO,aAAa,GAAGL,sBAAsB,CAACI,YAAY,CAAC;AAExD,SAASJ,sBAAsBA,CAACM,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;;AAE9F;AACA,IAAIG,UAAU,GAAG,uBAAuB;AAExCd,OAAO,CAACa,OAAO,GAAG,UAAUZ,KAAK,EAAE;EACjC,IAAIc,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAErF,OAAO,CAAC,CAAC,EAAEZ,oBAAoB,CAACS,OAAO,EAAEZ,KAAK,CAAC,CAACkB,IAAI,CAAC,UAAUC,IAAI,EAAE;IACnE;IACA,IAAIA,IAAI,CAACC,IAAI,KAAK,UAAU,IAAI,CAACP,UAAU,CAACQ,IAAI,CAACF,IAAI,CAACnB,KAAK,CAAC,EAAE;;IAE9D;IACA,IAAIsB,QAAQ,GAAGnB,oBAAoB,CAACS,OAAO,CAACW,SAAS,CAACJ,IAAI,CAACK,KAAK,CAAC;;IAEjE;IACA,IAAIF,QAAQ,CAACG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAIH,QAAQ,CAACG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAEvE,IAAIC,GAAG,GAAGrB,OAAO,CAACsB,MAAM,CAACC,KAAK,CAACN,QAAQ,CAAC;;IAExC;IACA;IACA,IAAIO,UAAU,GAAG,CAAC,CAAC,EAAEtB,SAAS,CAACK,OAAO,EAAEc,GAAG,EAAEZ,SAAS,CAAC;;IAEvD;IACAK,IAAI,CAACC,IAAI,GAAG,MAAM;IAClBD,IAAI,CAACnB,KAAK,GAAG,CAAC,CAAC,EAAES,aAAa,CAACG,OAAO,EAAEO,IAAI,CAACnB,KAAK,EAAE6B,UAAU,EAAEf,SAAS,CAAC;EAC5E,CAAC,EAAE,IAAI,CAAC,CAACgB,QAAQ,CAAC,CAAC;AACrB,CAAC;AAEDC,MAAM,CAAChC,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}