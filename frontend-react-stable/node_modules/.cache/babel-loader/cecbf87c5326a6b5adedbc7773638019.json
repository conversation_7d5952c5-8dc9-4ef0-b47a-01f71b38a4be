{"ast": null, "code": "import CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nexport default function getIcons(_ref) {\n  var suffixIcon = _ref.suffixIcon,\n    clearIcon = _ref.clearIcon,\n    menuItemSelectedIcon = _ref.menuItemSelectedIcon,\n    removeIcon = _ref.removeIcon,\n    loading = _ref.loading,\n    multiple = _ref.multiple,\n    hasFeedback = _ref.hasFeedback,\n    prefixCls = _ref.prefixCls,\n    showArrow = _ref.showArrow,\n    feedbackIcon = _ref.feedbackIcon;\n  // Clear Icon\n  var mergedClearIcon = clearIcon !== null && clearIcon !== void 0 ? clearIcon : /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  // Validation Feedback Icon\n  var getSuffixIconNode = function getSuffixIconNode(arrowIcon) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showArrow !== false && arrowIcon, hasFeedback && feedbackIcon);\n  };\n  // Arrow item icon\n  var mergedSuffixIcon = null;\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode(/*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    var iconCls = \"\".concat(prefixCls, \"-suffix\");\n    mergedSuffixIcon = function mergedSuffixIcon(_ref2) {\n      var open = _ref2.open,\n        showSearch = _ref2.showSearch;\n      if (open && showSearch) {\n        return getSuffixIconNode(/*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n      return getSuffixIconNode(/*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  }\n  // Checked item icon\n  var mergedItemIcon = null;\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n  var mergedRemoveIcon = null;\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}", "map": {"version": 3, "names": ["CheckOutlined", "CloseCircleFilled", "CloseOutlined", "DownOutlined", "LoadingOutlined", "SearchOutlined", "React", "getIcons", "_ref", "suffixIcon", "clearIcon", "menuItemSelectedIcon", "removeIcon", "loading", "multiple", "hasFeedback", "prefixCls", "showArrow", "feedbackIcon", "mergedClearIcon", "createElement", "getSuffixIconNode", "arrowIcon", "Fragment", "mergedSuffixIcon", "undefined", "spin", "iconCls", "concat", "_ref2", "open", "showSearch", "className", "mergedItemIcon", "mergedRemoveIcon", "itemIcon"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/select/utils/iconUtil.js"], "sourcesContent": ["import CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nexport default function getIcons(_ref) {\n  var suffixIcon = _ref.suffixIcon,\n    clearIcon = _ref.clearIcon,\n    menuItemSelectedIcon = _ref.menuItemSelectedIcon,\n    removeIcon = _ref.removeIcon,\n    loading = _ref.loading,\n    multiple = _ref.multiple,\n    hasFeedback = _ref.hasFeedback,\n    prefixCls = _ref.prefixCls,\n    showArrow = _ref.showArrow,\n    feedbackIcon = _ref.feedbackIcon;\n  // Clear Icon\n  var mergedClearIcon = clearIcon !== null && clearIcon !== void 0 ? clearIcon : /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  // Validation Feedback Icon\n  var getSuffixIconNode = function getSuffixIconNode(arrowIcon) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showArrow !== false && arrowIcon, hasFeedback && feedbackIcon);\n  };\n  // Arrow item icon\n  var mergedSuffixIcon = null;\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode( /*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    var iconCls = \"\".concat(prefixCls, \"-suffix\");\n    mergedSuffixIcon = function mergedSuffixIcon(_ref2) {\n      var open = _ref2.open,\n        showSearch = _ref2.showSearch;\n      if (open && showSearch) {\n        return getSuffixIconNode( /*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n      return getSuffixIconNode( /*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  }\n  // Checked item icon\n  var mergedItemIcon = null;\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n  var mergedRemoveIcon = null;\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC9BC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,oBAAoB,GAAGH,IAAI,CAACG,oBAAoB;IAChDC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,OAAO,GAAGL,IAAI,CAACK,OAAO;IACtBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,WAAW,GAAGP,IAAI,CAACO,WAAW;IAC9BC,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAC1BC,SAAS,GAAGT,IAAI,CAACS,SAAS;IAC1BC,YAAY,GAAGV,IAAI,CAACU,YAAY;EAClC;EACA,IAAIC,eAAe,GAAGT,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,aAAaJ,KAAK,CAACc,aAAa,CAACnB,iBAAiB,EAAE,IAAI,CAAC;EACxI;EACA,IAAIoB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,SAAS,EAAE;IAC5D,OAAO,aAAahB,KAAK,CAACc,aAAa,CAACd,KAAK,CAACiB,QAAQ,EAAE,IAAI,EAAEN,SAAS,KAAK,KAAK,IAAIK,SAAS,EAAEP,WAAW,IAAIG,YAAY,CAAC;EAC9H,CAAC;EACD;EACA,IAAIM,gBAAgB,GAAG,IAAI;EAC3B,IAAIf,UAAU,KAAKgB,SAAS,EAAE;IAC5BD,gBAAgB,GAAGH,iBAAiB,CAACZ,UAAU,CAAC;EAClD,CAAC,MAAM,IAAII,OAAO,EAAE;IAClBW,gBAAgB,GAAGH,iBAAiB,CAAE,aAAaf,KAAK,CAACc,aAAa,CAAChB,eAAe,EAAE;MACtFsB,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL,IAAIC,OAAO,GAAG,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC;IAC7CQ,gBAAgB,GAAG,SAASA,gBAAgBA,CAACK,KAAK,EAAE;MAClD,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;QACnBC,UAAU,GAAGF,KAAK,CAACE,UAAU;MAC/B,IAAID,IAAI,IAAIC,UAAU,EAAE;QACtB,OAAOV,iBAAiB,CAAE,aAAaf,KAAK,CAACc,aAAa,CAACf,cAAc,EAAE;UACzE2B,SAAS,EAAEL;QACb,CAAC,CAAC,CAAC;MACL;MACA,OAAON,iBAAiB,CAAE,aAAaf,KAAK,CAACc,aAAa,CAACjB,YAAY,EAAE;QACvE6B,SAAS,EAAEL;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EACA;EACA,IAAIM,cAAc,GAAG,IAAI;EACzB,IAAItB,oBAAoB,KAAKc,SAAS,EAAE;IACtCQ,cAAc,GAAGtB,oBAAoB;EACvC,CAAC,MAAM,IAAIG,QAAQ,EAAE;IACnBmB,cAAc,GAAG,aAAa3B,KAAK,CAACc,aAAa,CAACpB,aAAa,EAAE,IAAI,CAAC;EACxE,CAAC,MAAM;IACLiC,cAAc,GAAG,IAAI;EACvB;EACA,IAAIC,gBAAgB,GAAG,IAAI;EAC3B,IAAItB,UAAU,KAAKa,SAAS,EAAE;IAC5BS,gBAAgB,GAAGtB,UAAU;EAC/B,CAAC,MAAM;IACLsB,gBAAgB,GAAG,aAAa5B,KAAK,CAACc,aAAa,CAAClB,aAAa,EAAE,IAAI,CAAC;EAC1E;EACA,OAAO;IACLQ,SAAS,EAAES,eAAe;IAC1BV,UAAU,EAAEe,gBAAgB;IAC5BW,QAAQ,EAAEF,cAAc;IACxBrB,UAAU,EAAEsB;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}