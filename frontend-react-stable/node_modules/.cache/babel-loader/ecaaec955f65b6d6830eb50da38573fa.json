{"ast": null, "code": "'use strict';\n\nfunction replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp('(^|\\\\s)' + classToRemove + '(?:\\\\s|$)', 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\nmodule.exports = function removeClass(element, className) {\n  if (element.classList) element.classList.remove(className);else if (typeof element.className === 'string') element.className = replaceClassName(element.className, className);else element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n};", "map": {"version": 3, "names": ["replaceClassName", "origClass", "classToRemove", "replace", "RegExp", "module", "exports", "removeClass", "element", "className", "classList", "remove", "setAttribute", "baseVal"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/dom-helpers/class/removeClass.js"], "sourcesContent": ["'use strict';\n\nfunction replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp('(^|\\\\s)' + classToRemove + '(?:\\\\s|$)', 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n\nmodule.exports = function removeClass(element, className) {\n  if (element.classList) element.classList.remove(className);else if (typeof element.className === 'string') element.className = replaceClassName(element.className, className);else element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EAClD,OAAOD,SAAS,CAACE,OAAO,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGF,aAAa,GAAG,WAAW,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACzI;AAEAE,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACxD,IAAID,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACE,SAAS,CAACC,MAAM,CAACF,SAAS,CAAC,CAAC,KAAK,IAAI,OAAOD,OAAO,CAACC,SAAS,KAAK,QAAQ,EAAED,OAAO,CAACC,SAAS,GAAGT,gBAAgB,CAACQ,OAAO,CAACC,SAAS,EAAEA,SAAS,CAAC,CAAC,KAAKD,OAAO,CAACI,YAAY,CAAC,OAAO,EAAEZ,gBAAgB,CAACQ,OAAO,CAACC,SAAS,IAAID,OAAO,CAACC,SAAS,CAACI,OAAO,IAAI,EAAE,EAAEJ,SAAS,CAAC,CAAC;AACrS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}