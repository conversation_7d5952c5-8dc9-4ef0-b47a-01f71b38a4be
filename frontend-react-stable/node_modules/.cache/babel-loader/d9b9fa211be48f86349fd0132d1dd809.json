{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantile;\nexports.quantileSorted = quantileSorted;\nvar _max = _interopRequireDefault(require(\"./max.js\"));\nvar _min = _interopRequireDefault(require(\"./min.js\"));\nvar _quickselect = _interopRequireDefault(require(\"./quickselect.js\"));\nvar _number = _interopRequireWildcard(require(\"./number.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from((0, _number.numbers)(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return (0, _min.default)(values);\n  if (p >= 1) return (0, _max.default)(values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = (0, _max.default)((0, _quickselect.default)(values, i0).subarray(0, i0 + 1)),\n    value1 = (0, _min.default)(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileSorted(values, p, valueof = _number.default) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = +valueof(values[i0], i0, values),\n    value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "quantile", "quantileSorted", "_max", "_interopRequireDefault", "require", "_min", "_quickselect", "_number", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "values", "p", "valueof", "Float64Array", "from", "numbers", "n", "length", "i", "i0", "Math", "floor", "value0", "subarray", "value1"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/quantile.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantile;\nexports.quantileSorted = quantileSorted;\n\nvar _max = _interopRequireDefault(require(\"./max.js\"));\n\nvar _min = _interopRequireDefault(require(\"./min.js\"));\n\nvar _quickselect = _interopRequireDefault(require(\"./quickselect.js\"));\n\nvar _number = _interopRequireWildcard(require(\"./number.js\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from((0, _number.numbers)(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return (0, _min.default)(values);\n  if (p >= 1) return (0, _max.default)(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = (0, _max.default)((0, _quickselect.default)(values, i0).subarray(0, i0 + 1)),\n      value1 = (0, _min.default)(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileSorted(values, p, valueof = _number.default) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,cAAc,GAAGA,cAAc;AAEvC,IAAIC,IAAI,GAAGC,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEtD,IAAIC,IAAI,GAAGF,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEtD,IAAIE,YAAY,GAAGH,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtE,IAAIG,OAAO,GAAGC,uBAAuB,CAACJ,OAAO,CAAC,aAAa,CAAC,CAAC;AAE7D,SAASK,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASF,uBAAuBA,CAACM,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEf,OAAO,EAAEe;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGzB,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC0B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAI3B,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEhC,MAAM,CAACC,cAAc,CAACuB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACpB,OAAO,GAAGe,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAAShB,sBAAsBA,CAACW,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEf,OAAO,EAAEe;EAAI,CAAC;AAAE;AAE9F,SAASd,QAAQA,CAAC4B,MAAM,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACpCF,MAAM,GAAGG,YAAY,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzB,OAAO,CAAC0B,OAAO,EAAEL,MAAM,EAAEE,OAAO,CAAC,CAAC;EACjE,IAAI,EAAEI,CAAC,GAAGN,MAAM,CAACO,MAAM,CAAC,EAAE;EAC1B,IAAI,CAACN,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAIK,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE7B,IAAI,CAACN,OAAO,EAAE6B,MAAM,CAAC;EAC5D,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE3B,IAAI,CAACH,OAAO,EAAE6B,MAAM,CAAC;EAC5C,IAAIM,CAAC;IACDE,CAAC,GAAG,CAACF,CAAC,GAAG,CAAC,IAAIL,CAAC;IACfQ,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAG,CAAC,CAAC,EAAEtC,IAAI,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEO,YAAY,CAACP,OAAO,EAAE6B,MAAM,EAAES,EAAE,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAEJ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrFK,MAAM,GAAG,CAAC,CAAC,EAAErC,IAAI,CAACN,OAAO,EAAE6B,MAAM,CAACa,QAAQ,CAACJ,EAAE,GAAG,CAAC,CAAC,CAAC;EACvD,OAAOG,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C;AAEA,SAASpC,cAAcA,CAAC2B,MAAM,EAAEC,CAAC,EAAEC,OAAO,GAAGvB,OAAO,CAACR,OAAO,EAAE;EAC5D,IAAI,EAAEmC,CAAC,GAAGN,MAAM,CAACO,MAAM,CAAC,EAAE;EAC1B,IAAI,CAACN,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAIK,CAAC,GAAG,CAAC,EAAE,OAAO,CAACJ,OAAO,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC;EACjE,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAO,CAACC,OAAO,CAACF,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEN,MAAM,CAAC;EACzD,IAAIM,CAAC;IACDE,CAAC,GAAG,CAACF,CAAC,GAAG,CAAC,IAAIL,CAAC;IACfQ,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAG,CAACV,OAAO,CAACF,MAAM,CAACS,EAAE,CAAC,EAAEA,EAAE,EAAET,MAAM,CAAC;IACzCc,MAAM,GAAG,CAACZ,OAAO,CAACF,MAAM,CAACS,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAET,MAAM,CAAC;EACrD,OAAOY,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "script"}