{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _math = require(\"../math.js\");\nconst sqrt3 = (0, _math.sqrt)(3);\nvar _default = {\n  draw(context, size) {\n    const s = (0, _math.sqrt)(size) * 0.6824;\n    const t = s / 2;\n    const u = s * sqrt3 / 2; // cos(Math.PI / 6)\n\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_math", "require", "sqrt3", "sqrt", "_default", "draw", "context", "size", "s", "t", "u", "moveTo", "lineTo", "closePath"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/symbol/triangle2.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _math = require(\"../math.js\");\n\nconst sqrt3 = (0, _math.sqrt)(3);\nvar _default = {\n  draw(context, size) {\n    const s = (0, _math.sqrt)(size) * 0.6824;\n    const t = s / 2;\n    const u = s * sqrt3 / 2; // cos(Math.PI / 6)\n\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEF,KAAK,CAACG,IAAI,EAAE,CAAC,CAAC;AAChC,IAAIC,QAAQ,GAAG;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAG,CAAC,CAAC,EAAER,KAAK,CAACG,IAAI,EAAEI,IAAI,CAAC,GAAG,MAAM;IACxC,MAAME,CAAC,GAAGD,CAAC,GAAG,CAAC;IACf,MAAME,CAAC,GAAGF,CAAC,GAAGN,KAAK,GAAG,CAAC,CAAC,CAAC;;IAEzBI,OAAO,CAACK,MAAM,CAAC,CAAC,EAAE,CAACH,CAAC,CAAC;IACrBF,OAAO,CAACM,MAAM,CAACF,CAAC,EAAED,CAAC,CAAC;IACpBH,OAAO,CAACM,MAAM,CAAC,CAACF,CAAC,EAAED,CAAC,CAAC;IACrBH,OAAO,CAACO,SAAS,CAAC,CAAC;EACrB;AAEF,CAAC;AACDhB,OAAO,CAACE,OAAO,GAAGK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}