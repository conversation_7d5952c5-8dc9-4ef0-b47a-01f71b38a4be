{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _color = require(\"./color.js\");\nfunction _default(a, b) {\n  var i = (0, _color.hue)(+a, +b);\n  return function (t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_color", "require", "a", "b", "i", "hue", "t", "x", "Math", "floor"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/hue.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _color = require(\"./color.js\");\n\nfunction _default(a, b) {\n  var i = (0, _color.hue)(+a, +b);\n  return function (t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AAElC,SAASF,QAAQA,CAACG,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEJ,MAAM,CAACK,GAAG,EAAE,CAACH,CAAC,EAAE,CAACC,CAAC,CAAC;EAC/B,OAAO,UAAUG,CAAC,EAAE;IAClB,IAAIC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;IACZ,OAAOC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC;EACtC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}