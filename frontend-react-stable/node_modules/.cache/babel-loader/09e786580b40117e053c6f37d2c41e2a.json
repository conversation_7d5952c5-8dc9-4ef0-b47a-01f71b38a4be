{"ast": null, "code": "import warning from '../_util/warning';\nimport { FormProvider } from './context';\nimport ErrorList from './ErrorList';\nimport InternalForm, { useForm, useWatch } from './Form';\nimport Item from './FormItem';\nimport List from './FormList';\nimport useFormInstance from './hooks/useFormInstance';\nvar Form = InternalForm;\nForm.Item = Item;\nForm.List = List;\nForm.ErrorList = ErrorList;\nForm.useForm = useForm;\nForm.useFormInstance = useFormInstance;\nForm.useWatch = useWatch;\nForm.Provider = FormProvider;\nForm.create = function () {\n  process.env.NODE_ENV !== \"production\" ? warning(false, 'Form', 'antd v4 removed `Form.create`. Please remove or use `@ant-design/compatible` instead.') : void 0;\n};\nexport default Form;", "map": {"version": 3, "names": ["warning", "FormProvider", "ErrorList", "InternalForm", "useForm", "useWatch", "<PERSON><PERSON>", "List", "useFormInstance", "Form", "Provider", "create", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/index.js"], "sourcesContent": ["import warning from '../_util/warning';\nimport { FormProvider } from './context';\nimport ErrorList from './ErrorList';\nimport InternalForm, { useForm, useWatch } from './Form';\nimport Item from './FormItem';\nimport List from './FormList';\nimport useFormInstance from './hooks/useFormInstance';\nvar Form = InternalForm;\nForm.Item = Item;\nForm.List = List;\nForm.ErrorList = ErrorList;\nForm.useForm = useForm;\nForm.useFormInstance = useFormInstance;\nForm.useWatch = useWatch;\nForm.Provider = FormProvider;\nForm.create = function () {\n  process.env.NODE_ENV !== \"production\" ? warning(false, 'Form', 'antd v4 removed `Form.create`. Please remove or use `@ant-design/compatible` instead.') : void 0;\n};\nexport default Form;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AACtC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,QAAQ;AACxD,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,eAAe,MAAM,yBAAyB;AACrD,IAAIC,IAAI,GAAGN,YAAY;AACvBM,IAAI,CAACH,IAAI,GAAGA,IAAI;AAChBG,IAAI,CAACF,IAAI,GAAGA,IAAI;AAChBE,IAAI,CAACP,SAAS,GAAGA,SAAS;AAC1BO,IAAI,CAACL,OAAO,GAAGA,OAAO;AACtBK,IAAI,CAACD,eAAe,GAAGA,eAAe;AACtCC,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;AACxBI,IAAI,CAACC,QAAQ,GAAGT,YAAY;AAC5BQ,IAAI,CAACE,MAAM,GAAG,YAAY;EACxBC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,uFAAuF,CAAC,GAAG,KAAK,CAAC;AAClK,CAAC;AACD,eAAeS,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}