{"ast": null, "code": "/**\n * @remix-run/router v1.3.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  function createHref(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 1\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 0\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning$1(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\n\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\n\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\n\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\n\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  let index = getIndex(); // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || {\n      idx: null\n    };\n    return state.idx;\n  }\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({\n        action,\n        location: history.location,\n        delta\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location); // try...catch because iOS limits us to 100 pushState calls :/\n\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 1\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 0\n      });\n    }\n  }\n  function createURL(to) {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n    let href = typeof to === \"string\" ? to : createPath(to);\n    invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n    return new URL(href, base);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n} //#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nfunction isIndexRoute(route) {\n  return route.index === true;\n} // Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\n\nfunction convertRoutesToDataRoutes(routes, parentPath, allIds) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (allIds === void 0) {\n    allIds = new Set();\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!allIds.has(id), \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    allIds.add(id);\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, {\n        id\n      });\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, {\n        id,\n        children: route.children ? convertRoutesToDataRoutes(route.children, treePath, allIds) : undefined\n      });\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\n\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i],\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    safelyDecodeURI(pathname));\n  }\n  return matches;\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta); // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    } // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    var _route$path;\n\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\n\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments; // Optional path segments are denoted by a trailing `?`\n\n  let isOptional = first.endsWith(\"?\"); // Compute the corresponding required segment: `foo?` -> `foo`\n\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = []; // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\"))); // Then if this is an optional value, add all child versions without\n\n  if (isOptional) {\n    result.push(...restExploded);\n  } // for absolute paths, ensure `/` instead of empty segment\n\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname) {\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    if (!match) return null;\n    Object.assign(matchedParams, match.params);\n    let route = meta.route;\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\n\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  return path.replace(/^:(\\w+)(\\??)/g, (_, key, optional) => {\n    let param = params[key];\n    if (optional === \"?\") {\n      return param == null ? \"\" : param;\n    }\n    if (param == null) {\n      invariant(false, \"Missing \\\":\" + key + \"\\\" param\");\n    }\n    return param;\n  }).replace(/\\/:(\\w+)(\\??)/g, (_, key, optional) => {\n    let param = params[key];\n    if (optional === \"?\") {\n      return param == null ? \"\" : \"/\" + param;\n    }\n    if (param == null) {\n      invariant(false, \"Missing \\\":\" + key + \"\\\" param\");\n    }\n    return \"/\" + param;\n  }) // Remove any optional markers from optional static segments\n  .replace(/\\?/g, \"\").replace(/(\\/?)\\*/, (_, prefix, __, str) => {\n    const star = \"*\";\n    if (params[star] == null) {\n      // If no splat was provided, trim the trailing slash _unless_ it's\n      // the entire path\n      return str === \"/*\" ? \"/\" : \"\";\n    } // Apply the splat\n\n    return \"\" + prefix + params[star];\n  });\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\n\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, paramNames] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = paramNames.reduce((memo, paramName, index) => {\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    memo[paramName] = safelyDecodeURIComponent(captureGroups[index] || \"\", paramName);\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let paramNames = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:(\\w+)/g, (_, paramName) => {\n    paramNames.push(paramName);\n    return \"/([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, paramNames];\n}\nfunction safelyDecodeURI(value) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\nfunction safelyDecodeURIComponent(value, paramName) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(false, \"The value for the URL param \\\"\" + paramName + \"\\\" will not be decoded because\" + (\" the string \\\"\" + value + \"\\\" is a malformed URL segment. This is probably\") + (\" due to a bad percent encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\n\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  } // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * @private\n */\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging @remix-run/router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\n\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\n\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n/**\n * @private\n */\n\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from; // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\"); // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    } // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from); // Ensure the pathname has a trailing slash if the original \"to\" had one\n\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\"); // Or if this was a link to the current path which has a trailing slash\n\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\n\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\n\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\n\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\n\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\n\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\n\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data, responseInit) {\n    this.pendingKeysSet = new Set();\n    this.subscribers = new Set();\n    this.deferredKeys = [];\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\"); // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref) => {\n      let [key, value] = _ref;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n    this.init = responseInit;\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key); // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, null, data), error => this.onSettle(promise, key, error)); // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeysSet.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    if (error) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    this.emit(false, key);\n    return data;\n  }\n  emit(aborted, settledKey) {\n    this.subscribers.forEach(subscriber => subscriber(aborted, settledKey));\n  }\n  subscribe(fn) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\nconst defer = function defer(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  return new DeferredData(data, responseInit);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\n\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\n\nclass ErrorResponse {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\n\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined\n};\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser; //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\n\nfunction createRouter(init) {\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let dataRoutes = convertRoutesToDataRoutes(init.routes); // Cleanup function for history\n\n  let unlistenHistory = null; // Externally-provided functions to call on all state changes\n\n  let subscribers = new Set(); // Externally-provided object to hold scroll restoration locations during routing\n\n  let savedScrollPositions = null; // Externally-provided function to get scroll restoration keys\n\n  let getScrollRestorationKey = null; // Externally-provided function to get current scroll position\n\n  let getScrollPosition = null; // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, init.basename);\n  let initialErrors = null;\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  let initialized = !initialMatches.some(m => m.route.loader) || init.hydrationData != null;\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map()\n  }; // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n\n  let pendingAction = Action.Pop; // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n\n  let pendingPreventScrollReset = false; // AbortController for the active navigation\n\n  let pendingNavigationController; // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n\n  let isUninterruptedRevalidation = false; // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidate()\n  //  - X-Remix-Revalidate (from redirect)\n\n  let isRevalidationRequired = false; // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n\n  let cancelledDeferredRoutes = []; // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n\n  let cancelledFetcherLoads = []; // AbortControllers for any in-flight fetchers\n\n  let fetchControllers = new Map(); // Track loads based on the order in which they started\n\n  let incrementingLoadId = 0; // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n\n  let pendingNavigationLoadId = -1; // Fetchers that triggered data reloads as a result of their actions\n\n  let fetchReloadIds = new Map(); // Fetchers that triggered redirect navigations from their actions\n\n  let fetchRedirectIds = new Set(); // Most recent href/match for fetcher.load calls for fetchers\n\n  let fetchLoadMatches = new Map(); // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n\n  let activeDeferreds = new Map(); // We ony support a single active blocker at the moment since we don't have\n  // any compelling use cases for multi-blocker yet\n\n  let activeBlocker = null; // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n\n  let blockerFunctions = new Map(); // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n\n  let ignoreNextHistoryUpdate = false; // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location,\n        delta\n      } = _ref;\n\n      // Ignore this event if it was just us resetting the URL from a\n      // blocked POP navigation\n      if (ignoreNextHistoryUpdate) {\n        ignoreNextHistoryUpdate = false;\n        return;\n      }\n      warning(activeBlocker != null && delta === null, \"You are trying to use a blocker on a POP navigation to a location \" + \"that was not created by @remix-run/router. This will fail silently in \" + \"production. This can happen if you are navigating outside the router \" + \"via `window.history.pushState`/`window.location.hash` instead of using \" + \"router navigation APIs.  This can also happen if you are using \" + \"createHashRouter and the user manually changes the URL.\");\n      let blockerKey = shouldBlockNavigation({\n        currentLocation: state.location,\n        nextLocation: location,\n        historyAction\n      });\n      if (blockerKey && delta != null) {\n        // Restore the URL to match the current UI, but don't update router state\n        ignoreNextHistoryUpdate = true;\n        init.history.go(delta * -1); // Put the blocker into a blocked state\n\n        updateBlocker(blockerKey, {\n          state: \"blocked\",\n          location,\n          proceed() {\n            updateBlocker(blockerKey, {\n              state: \"proceeding\",\n              proceed: undefined,\n              reset: undefined,\n              location\n            }); // Re-do the same POP navigation we just blocked\n\n            init.history.go(delta);\n          },\n          reset() {\n            deleteBlocker(blockerKey);\n            updateState({\n              blockers: new Map(router.state.blockers)\n            });\n          }\n        });\n        return;\n      }\n      return startNavigation(historyAction, location);\n    }); // Kick off initial data load if needed.  Use Pop to avoid modifying history\n\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location);\n    }\n    return router;\n  } // Clean up a router and it's side effects\n\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  } // Subscribe to state updates for the router\n\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  } // Update our state and notify the calling context of the change\n\n  function updateState(newState) {\n    state = _extends({}, state, newState);\n    subscribers.forEach(subscriber => subscriber(state));\n  } // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n\n  function completeNavigation(location, newState) {\n    var _location$state, _location$state2;\n\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    } // Always preserve any existing loaderData from re-used routes\n\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData; // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n\n    for (let [key] of blockerFunctions) {\n      deleteBlocker(key);\n    } // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && ((_location$state2 = location.state) == null ? void 0 : _location$state2._isRedirect) !== true;\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset,\n      blockers: new Map(state.blockers)\n    }));\n    if (isUninterruptedRevalidation) ;else if (pendingAction === Action.Pop) ;else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    } // Reset stateful navigation vars\n\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  } // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(to, opts);\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state); // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n\n    nextLocation = _extends({}, nextLocation, init.history.encodeLocation(nextLocation));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ;else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation\n          }); // Send the same navigation through\n\n          navigate(to, opts);\n        },\n        reset() {\n          deleteBlocker(blockerKey);\n          updateState({\n            blockers: new Map(state.blockers)\n          });\n        }\n      });\n      return;\n    }\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace\n    });\n  } // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    }); // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n\n    if (state.navigation.state === \"submitting\") {\n      return;\n    } // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    } // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation\n    });\n  } // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true; // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(dataRoutes, location, init.basename); // Short circuit with a 404 on the root error boundary if we match nothing\n\n    if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes); // Cancel all pending deferred on 404s since we don't keep any routes\n\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      });\n      return;\n    } // Short circuit if it's only a hash change and not a mutation submission\n    // For example, on /page#hash and submit a <Form method=\"post\"> which will\n    // default to a navigation to /page\n\n    if (isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, {\n        matches\n      });\n      return;\n    } // Create a controller/Request for this navigation\n\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(init.history, location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionData;\n    let pendingError;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError\n      };\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(request, location, opts.submission, matches, {\n        replace: opts.replace\n      });\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n      let navigation = _extends({\n        state: \"loading\",\n        location\n      }, opts.submission);\n      loadingNavigation = navigation; // Create a GET request for the loaders\n\n      request = new Request(request.url, {\n        signal: request.signal\n      });\n    } // Call loaders\n\n    let {\n      shortCircuited,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, loadingNavigation, opts && opts.submission, opts && opts.replace, pendingActionData, pendingError);\n    if (shortCircuited) {\n      return;\n    } // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches\n    }, pendingActionData ? {\n      actionData: pendingActionData\n    } : {}, {\n      loaderData,\n      errors\n    }));\n  } // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n\n  async function handleAction(request, location, submission, matches, opts) {\n    interruptActiveLoads(); // Put us in a submitting state\n\n    let navigation = _extends({\n      state: \"submitting\",\n      location\n    }, submission);\n    updateState({\n      navigation\n    }); // Call our action and get the result\n\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, router.basename);\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace = result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id); // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: {\n          [boundaryMatch.route.id]: result.error\n        }\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    return {\n      pendingActionData: {\n        [actionMatch.route.id]: result.data\n      }\n    };\n  } // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n\n  async function handleLoaders(request, location, matches, overrideNavigation, submission, replace, pendingActionData, pendingError) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n    if (!loadingNavigation) {\n      let navigation = _extends({\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      }, submission);\n      loadingNavigation = navigation;\n    } // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n\n    let activeSubmission = submission ? submission : loadingNavigation.formMethod && loadingNavigation.formAction && loadingNavigation.formData && loadingNavigation.formEncType ? {\n      formMethod: loadingNavigation.formMethod,\n      formAction: loadingNavigation.formAction,\n      formData: loadingNavigation.formData,\n      formEncType: loadingNavigation.formEncType\n    } : undefined;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, activeSubmission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches); // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId)); // Short circuit if we have no loaders to run\n\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null\n      }, pendingActionData ? {\n        actionData: pendingActionData\n      } : {}));\n      return {\n        shortCircuited: true\n      };\n    } // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach(rf => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true\n        };\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState(_extends({\n        navigation: loadingNavigation\n      }, actionData ? Object.keys(actionData).length === 0 ? {\n        actionData: null\n      } : {\n        actionData\n      } : {}, revalidatingFetchers.length > 0 ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n    }\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach(rf => fetchControllers.set(rf.key, pendingNavigationController));\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    } // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n\n    revalidatingFetchers.forEach(rf => fetchControllers.delete(rf.key)); // If any loaders returned a redirect Response, start a new REPLACE navigation\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      await startRedirectNavigation(state, redirect, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    } // Process and commit output from loaders\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, matchesToLoad, loaderResults, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds); // Wire up subscribers to update loaderData as promises settle\n\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    return _extends({\n      loaderData,\n      errors\n    }, didAbortFetchLoads || revalidatingFetchers.length > 0 ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getFetcher(key) {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  } // Trigger a fetcher load/submit for the given fetcher key\n\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let matches = matchRoutes(dataRoutes, href, init.basename);\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: href\n      }));\n      return;\n    }\n    let {\n      path,\n      submission\n    } = normalizeNavigateOptions(href, opts, true);\n    let match = getTargetMatch(matches, path);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    } // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n\n    fetchLoadMatches.set(key, {\n      routeId,\n      path,\n      match,\n      matches\n    });\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  } // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    if (!match.route.action) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    } // Put this fetcher into it's submitting state\n\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher = _extends({\n      state: \"submitting\"\n    }, submission, {\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true\n    });\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the action for the fetcher\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal, submission);\n    fetchControllers.set(key, abortController);\n    let actionResult = await callLoaderOrAction(\"action\", fetchRequest, match, requestMatches, router.basename);\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n      let loadingFetcher = _extends({\n        state: \"loading\"\n      }, submission, {\n        data: undefined,\n        \" _hasFetcherDoneAnything \": true\n      });\n      state.fetchers.set(key, loadingFetcher);\n      updateState({\n        fetchers: new Map(state.fetchers)\n      });\n      return startRedirectNavigation(state, actionResult, {\n        isFetchActionRedirect: true\n      });\n    } // Process any non-redirect errors thrown\n\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    } // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(init.history, nextLocation, abortController.signal);\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(dataRoutes, state.navigation.location, init.basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = _extends({\n      state: \"loading\",\n      data: actionResult.data\n    }, submission, {\n      \" _hasFetcherDoneAnything \": true\n    });\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, submission, nextLocation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, {\n      [match.route.id]: actionResult.data\n    }, undefined,\n    // No need to send through errors since we short circuit above\n    fetchLoadMatches); // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n\n    revalidatingFetchers.filter(rf => rf.key !== key).forEach(rf => {\n      let staleKey = rf.key;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = {\n        state: \"loading\",\n        data: existingFetcher && existingFetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      fetchControllers.set(staleKey, abortController);\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(r => fetchControllers.delete(r.key));\n    let redirect = findRedirect(results);\n    if (redirect) {\n      return startRedirectNavigation(state, redirect);\n    } // Process and commit output from loaders\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, state.matches, matchesToLoad, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    let doneFetcher = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true\n    };\n    state.fetchers.set(key, doneFetcher);\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId); // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState(_extends({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors)\n      }, didAbortFetchLoads ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n      isRevalidationRequired = false;\n    }\n  } // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n\n  async function handleFetcherLoader(key, routeId, path, match, matches, submission) {\n    let existingFetcher = state.fetchers.get(key); // Put this fetcher into it's loading state\n\n    let loadingFetcher = _extends({\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined\n    }, submission, {\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true\n    });\n    state.fetchers.set(key, loadingFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the loader for this fetcher route match\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal);\n    fetchControllers.set(key, abortController);\n    let result = await callLoaderOrAction(\"loader\", fetchRequest, match, matches, router.basename); // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    } // We can delete this so long as we weren't aborted by ou our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    } // If the loader threw a redirect Response, start a new REPLACE navigation\n\n    if (isRedirectResult(result)) {\n      await startRedirectNavigation(state, result);\n      return;\n    } // Process any non-redirect errors thrown\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key); // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error\n        }\n      });\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\"); // Put the fetcher back into an idle state\n\n    let doneFetcher = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n\n  async function startRedirectNavigation(state, redirect, _temp) {\n    var _window;\n    let {\n      submission,\n      replace,\n      isFetchActionRedirect\n    } = _temp === void 0 ? {} : _temp;\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n    let redirectLocation = createLocation(state.location, redirect.location,\n    // TODO: This can be removed once we get rid of useTransition in Remix v2\n    _extends({\n      _isRedirect: true\n    }, isFetchActionRedirect ? {\n      _isFetchActionRedirect: true\n    } : {}));\n    invariant(redirectLocation, \"Expected a location on the redirect navigation\"); // Check if this an external redirect that goes to a new origin\n\n    if (isBrowser && typeof ((_window = window) == null ? void 0 : _window.location) !== \"undefined\") {\n      let newOrigin = init.history.createURL(redirect.location).origin;\n      if (window.location.origin !== newOrigin) {\n        if (replace) {\n          window.location.replace(redirect.location);\n        } else {\n          window.location.assign(redirect.location);\n        }\n        return;\n      }\n    } // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true ? Action.Replace : Action.Push; // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n\n    let {\n      formMethod,\n      formAction,\n      formEncType,\n      formData\n    } = state.navigation;\n    if (!submission && formMethod && formAction && formData && formEncType) {\n      submission = {\n        formMethod,\n        formAction,\n        formEncType,\n        formData\n      };\n    } // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n\n    if (redirectPreserveMethodStatusCodes.has(redirect.status) && submission && isMutationMethod(submission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, submission, {\n          formAction: redirect.location\n        }),\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset\n      });\n    } else {\n      // Otherwise, we kick off a new loading navigation, preserving the\n      // submission info for the duration of this navigation\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: {\n          state: \"loading\",\n          location: redirectLocation,\n          formMethod: submission ? submission.formMethod : undefined,\n          formAction: submission ? submission.formAction : undefined,\n          formEncType: submission ? submission.formEncType : undefined,\n          formData: submission ? submission.formData : undefined\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset\n      });\n    }\n  }\n  async function callLoadersAndMaybeResolveData(currentMatches, matches, matchesToLoad, fetchersToLoad, request) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, router.basename)), ...fetchersToLoad.map(f => callLoaderOrAction(\"loader\", createClientSideRequest(init.history, f.path, request.signal), f.match, f.matches, router.basename))]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n    await Promise.all([resolveDeferredResults(currentMatches, matchesToLoad, loaderResults, request.signal, false, state.loaderData), resolveDeferredResults(currentMatches, fetchersToLoad.map(f => f.match), fetcherResults, request.signal, true)]);\n    return {\n      results,\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true; // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds()); // Abort in-flight fetcher loads\n\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n  function setFetcherError(key, routeId, error) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function deleteFetcher(key) {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, \"Expected fetch controller: \" + key);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n      }\n    }\n    markFetchersDone(doneKeys);\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n      if (activeBlocker == null) {\n        // This is now the active blocker\n        activeBlocker = key;\n      } else if (key !== activeBlocker) {\n        warning(false, \"A router only supports one blocker at a time\");\n      }\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n    if (activeBlocker === key) {\n      activeBlocker = null;\n    }\n  } // Utility function to update blockers, ensuring valid state transitions\n\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER; // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n\n    invariant(blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\", \"Invalid blocker state transition: \" + blocker.state + \" -> \" + newBlocker.state);\n    state.blockers.set(key, newBlocker);\n    updateState({\n      blockers: new Map(state.blockers)\n    });\n  }\n  function shouldBlockNavigation(_ref2) {\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = _ref2;\n    if (activeBlocker == null) {\n      return;\n    } // We only allow a single blocker at the moment.  This will need to be\n    // updated if we enhance to support multiple blockers in the future\n\n    let blockerFunction = blockerFunctions.get(activeBlocker);\n    invariant(blockerFunction, \"Could not find a function for the active blocker\");\n    let blocker = state.blockers.get(activeBlocker);\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    } // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n\n    if (blockerFunction({\n      currentLocation,\n      nextLocation,\n      historyAction\n    })) {\n      return activeBlocker;\n    }\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  } // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || (location => location.key); // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  router = {\n    get basename() {\n      return init.basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds\n  };\n  return router;\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nconst UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let dataRoutes = convertRoutesToDataRoutes(routes);\n  let basename = (opts ? opts.basename : null) || \"/\";\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n\n  async function query(request, _temp2) {\n    let {\n      requestContext\n    } = _temp2 === void 0 ? {} : _temp2;\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename); // SSR supports HEAD requests while SPA doesn't\n\n    if (!isValidMethod(method) && method !== \"head\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    } // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n\n  async function queryRoute(request, _temp3) {\n    let {\n      routeId,\n      requestContext\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename); // SSR supports HEAD requests while SPA doesn't\n\n    if (!isValidMethod(method) && method !== \"head\" && method !== \"options\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let result = await queryImpl(request, location, matches, requestContext, match);\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    } // Pick off the right state value to return\n\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      var _result$activeDeferre;\n      let data = Object.values(result.loaderData)[0];\n      if ((_result$activeDeferre = result.activeDeferreds) != null && _result$activeDeferre[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n    return undefined;\n  }\n  async function queryImpl(request, location, matches, requestContext, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, requestContext, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      } // Redirects are always returned since they don't propagate to catch\n      // boundaries\n\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, basename, true, isRouteRequest, requestContext);\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(method + \"() call aborted\");\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(request, matches, requestContext, undefined, {\n        [boundaryMatch.route.id]: result.error\n      }); // action status codes take precedence over loader status codes\n\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    } // Create a GET request for the loaders\n\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n    return _extends({}, context, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      },\n      actionHeaders: _extends({}, result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {})\n    });\n  }\n  async function loadRouteData(request, matches, requestContext, routeMatch, pendingActionError) {\n    let isRouteRequest = routeMatch != null; // Short circuit if we have no loaders to run (queryRoute())\n\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n    let requestMatches = routeMatch ? [routeMatch] : getLoaderMatchesUntilBoundary(matches, Object.keys(pendingActionError || {})[0]);\n    let matchesToLoad = requestMatches.filter(m => m.route.loader); // Short circuit if we have no loaders to run (query())\n\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, basename, true, isRouteRequest, requestContext))]);\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(method + \"() call aborted\");\n    } // Process and commit output from loaders\n\n    let activeDeferreds = new Map();\n    let context = processRouteLoaderData(matches, matchesToLoad, results, pendingActionError, activeDeferreds); // Add a null for any non-loader matches for proper revalidation on the client\n\n    let executedLoaders = new Set(matchesToLoad.map(match => match.route.id));\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches,\n      activeDeferreds: activeDeferreds.size > 0 ? Object.fromEntries(activeDeferreds.entries()) : null\n    });\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\n\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && \"formData\" in opts;\n} // Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\n\nfunction normalizeNavigateOptions(to, opts, isFetcher) {\n  if (isFetcher === void 0) {\n    isFetcher = false;\n  }\n  let path = typeof to === \"string\" ? to : createPath(to); // Return location verbatim on non-submission navigations\n\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  } // Create a Submission on non-GET navigations\n\n  let submission;\n  if (opts.formData) {\n    submission = {\n      formMethod: opts.formMethod || \"get\",\n      formAction: stripHashFromPath(path),\n      formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n      formData: opts.formData\n    };\n    if (isMutationMethod(submission.formMethod)) {\n      return {\n        path,\n        submission\n      };\n    }\n  } // Flatten submission onto URLSearchParams for GET submissions\n\n  let parsedPath = parsePath(path);\n  let searchParams = convertFormDataToSearchParams(opts.formData); // Since fetcher GET submissions only run a single loader (as opposed to\n  // navigation GET submissions which run all loaders), we need to preserve\n  // any incoming ?index params\n\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = \"?\" + searchParams;\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n} // Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\n\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex(m => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\nfunction getMatchesToLoad(history, state, matches, submission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches) {\n  let actionResult = pendingError ? Object.values(pendingError)[0] : pendingActionData ? Object.values(pendingActionData)[0] : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  let defaultShouldRevalidate =\n  // Forced revalidation due to submission, useRevalidate, or X-Remix-Revalidate\n  isRevalidationRequired ||\n  // Clicked the same link, resubmitted a GET form\n  currentUrl.toString() === nextUrl.toString() ||\n  // Search params affect all loaders\n  currentUrl.search !== nextUrl.search; // Pick navigation matches that are net-new or qualify for revalidation\n\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    if (match.route.loader == null) {\n      return false;\n    } // Always call the loader on new route instances and pending defer cancellations\n\n    if (isNewLoader(state.loaderData, state.matches[index], match) || cancelledDeferredRoutes.some(id => id === match.route.id)) {\n      return true;\n    } // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n    return shouldRevalidateLoader(match, _extends({\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params\n    }, submission, {\n      actionResult,\n      defaultShouldRevalidate: defaultShouldRevalidate || isNewRouteInstance(currentRouteMatch, nextRouteMatch)\n    }));\n  }); // Pick fetcher.loads that need to be revalidated\n\n  let revalidatingFetchers = [];\n  fetchLoadMatches && fetchLoadMatches.forEach((f, key) => {\n    if (!matches.some(m => m.route.id === f.routeId)) {\n      // This fetcher is not going to be present in the subsequent render so\n      // there's no need to revalidate it\n      return;\n    } else if (cancelledFetcherLoads.includes(key)) {\n      // This fetcher was cancelled from a prior action submission - force reload\n      revalidatingFetchers.push(_extends({\n        key\n      }, f));\n    } else {\n      // Revalidating fetchers are decoupled from the route matches since they\n      // hit a static href, so they _always_ check shouldRevalidate and the\n      // default is strictly if a revalidation is explicitly required (action\n      // submissions, useRevalidator, X-Remix-Revalidate).\n      let shouldRevalidate = shouldRevalidateLoader(f.match, _extends({\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params\n      }, submission, {\n        actionResult,\n        defaultShouldRevalidate\n      }));\n      if (shouldRevalidate) {\n        revalidatingFetchers.push(_extends({\n          key\n        }, f));\n      }\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id; // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n\n  let isMissingData = currentLoaderData[match.route.id] === undefined; // Always load if this is a net-new route or we don't yet have data\n\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\nasync function callLoaderOrAction(type, request, match, matches, basename, isStaticRequest, isRouteRequest, requestContext) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  if (isStaticRequest === void 0) {\n    isStaticRequest = false;\n  }\n  if (isRouteRequest === void 0) {\n    isRouteRequest = false;\n  }\n  let resultType;\n  let result; // Setup a promise we can race against so that abort signals short circuit\n\n  let reject;\n  let abortPromise = new Promise((_, r) => reject = r);\n  let onReject = () => reject();\n  request.signal.addEventListener(\"abort\", onReject);\n  try {\n    let handler = match.route[type];\n    invariant(handler, \"Could not find the \" + type + \" to run on the \\\"\" + match.route.id + \"\\\" route\");\n    result = await Promise.race([handler({\n      request,\n      params: match.params,\n      context: requestContext\n    }), abortPromise]);\n    invariant(result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    request.signal.removeEventListener(\"abort\", onReject);\n  }\n  if (isResponse(result)) {\n    let status = result.status; // Process redirects\n\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n      let isAbsolute = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i.test(location); // Support relative routing in internal redirects\n\n      if (!isAbsolute) {\n        let activeMatches = matches.slice(0, matches.indexOf(match) + 1);\n        let routePathnames = getPathContributingMatches(activeMatches).map(match => match.pathnameBase);\n        let resolvedLocation = resolveTo(location, routePathnames, new URL(request.url).pathname);\n        invariant(createPath(resolvedLocation), \"Unable to resolve redirect location: \" + location); // Prepend the basename to the redirect location if we have one\n\n        if (basename) {\n          let path = resolvedLocation.pathname;\n          resolvedLocation.pathname = path === \"/\" ? basename : joinPaths([basename, path]);\n        }\n        location = createPath(resolvedLocation);\n      } else if (!isStaticRequest) {\n        // Strip off the protocol+origin for same-origin absolute redirects.\n        // If this is a static reques, we can let it go back to the browser\n        // as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\") ? new URL(currentUrl.protocol + location) : new URL(location);\n        if (url.origin === currentUrl.origin) {\n          location = url.pathname + url.search + url.hash;\n        }\n      } // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null\n      };\n    } // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result\n      };\n    }\n    let data;\n    let contentType = result.headers.get(\"Content-Type\"); // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (resultType === ResultType.error) {\n    return {\n      type: resultType,\n      error: result\n    };\n  }\n  if (result instanceof DeferredData) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n} // Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\n\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType,\n      formData\n    } = submission;\n    init.method = formMethod.toUpperCase();\n    init.body = formEncType === \"application/x-www-form-urlencoded\" ? convertFormDataToSearchParams(formData) : formData;\n  } // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, value instanceof File ? value.name : value);\n  }\n  return searchParams;\n}\nfunction processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {}; // Process loader results into state.loaderData/state.errors\n\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error; // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n      errors = errors || {}; // Prefer higher error values if lower errors bubble to the same boundary\n\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      } // Clear our any prior loaderData for the throwing route\n\n      loaderData[id] = undefined; // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      } // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n\n      if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  }); // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, matchesToLoad, results, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds); // Process results from our revalidating fetchers\n\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let {\n      key,\n      match\n    } = revalidatingFetchers[index];\n    invariant(fetcherResults !== undefined && fetcherResults[index] !== undefined, \"Did not find corresponding fetcher result\");\n    let result = fetcherResults[index]; // Process fetcher non-redirect errors\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n} // Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\n\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\nfunction getInternalRouterError(status, _temp4) {\n  let {\n    pathname,\n    routeId,\n    method,\n    type\n  } = _temp4 === void 0 ? {} : _temp4;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n  return new ErrorResponse(status || 500, statusText, new Error(errorMessage), true);\n} // Find any returned redirect errors, starting from the lowest match\n\nfunction findRedirect(results) {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash !== b.hash;\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isQueryRouteResponse(obj) {\n  return obj && isResponse(obj.response) && (obj.type === ResultType.data || ResultType.error);\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method);\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method);\n}\nasync function resolveDeferredResults(currentMatches, matchesToLoad, results, signal, isFetcher, currentLoaderData) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, isFetcher).then(result => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n} // Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\n\nfunction createUseMatchesMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  } // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n} //#endregion\n\nexport { AbortedDeferredError, Action, ErrorResponse, IDLE_BLOCKER, IDLE_FETCHER, IDLE_NAVIGATION, UNSAFE_DEFERRED_SYMBOL, DeferredData as UNSAFE_DeferredData, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, getPathContributingMatches as UNSAFE_getPathContributingMatches, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, createStaticHandler, defer, generatePath, getStaticContextFromError, getToPathname, invariant, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, resolvePath, resolveTo, stripBasename, warning };", "map": {"version": 3, "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning$1", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "warning", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "parentPath", "allIds", "Set", "treePath", "id", "join", "children", "has", "add", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "matchRouteBranch", "safelyDecodeURI", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "startsWith", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "params", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "_", "optional", "param", "prefix", "__", "str", "star", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "regexpSource", "RegExp", "decodeURI", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "data", "init", "responseInit", "status", "headers", "Headers", "set", "Response", "Aborted<PERSON>eferredError", "DeferredData", "constructor", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "trackPromise", "done", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "v", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref2", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "ErrorResponse", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "createRouter", "dataRoutes", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "initialized", "m", "loader", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "loaderData", "actionData", "errors", "fetchers", "Map", "blockers", "pendingAction", "pendingPreventScrollReset", "pendingNavigationController", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeDeferreds", "activeBlocker", "blockerFunctions", "ignoreNextHistoryUpdate", "initialize", "blockerKey", "shouldBlockNavigation", "currentLocation", "updateBlocker", "deleteBlocker", "updateState", "startNavigation", "dispose", "clear", "deleteFetcher", "newState", "completeNavigation", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "getSavedScrollPosition", "navigate", "opts", "submission", "normalizeNavigateOptions", "userReplace", "pendingError", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "loadingNavigation", "notFoundMatches", "cancelActiveDeferreds", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionData", "findNearestBoundary", "actionOutput", "handleAction", "shortCircuited", "pendingActionError", "Request", "handleLoaders", "actionMatch", "getTargetMatch", "type", "method", "routeId", "callLoaderOrAction", "isRedirectResult", "startRedirectNavigation", "isErrorResult", "boundaryMatch", "isDeferredResult", "activeSubmission", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "rf", "fetcher", "revalidatingFetcher", "results", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "processLoaderData", "deferredData", "markFetchRedirectsDone", "didAbortFetchLoads", "abortStaleFetchLoads", "getFetcher", "fetch", "abort<PERSON><PERSON><PERSON>", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "existingFetcher", "abortController", "fetchRequest", "actionResult", "loadingFetcher", "isFetchActionRedirect", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "_temp", "_window", "redirectLocation", "_isFetchActionRedirect", "new<PERSON><PERSON><PERSON>", "redirectHistoryAction", "currentMatches", "fetchersToLoad", "all", "f", "resolveDeferredResults", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "userMatches", "createUseMatchesMatch", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "createStaticHandler", "query", "_temp2", "requestContext", "isValidMethod", "methodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "isResponse", "queryRoute", "_temp3", "find", "values", "_result$activeDeferre", "routeMatch", "submit", "loadRouteData", "isQueryRouteResponse", "isRedirectResponse", "response", "isRouteRequest", "Location", "context", "loaderRequest", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "executedLoaders", "fromEntries", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "isSubmissionNavigation", "isFetcher", "stripHashFromPath", "searchParams", "convertFormDataToSearchParams", "hasNakedIndexQuery", "append", "boundaryId", "boundaryMatches", "findIndex", "currentUrl", "nextUrl", "defaultShouldRevalidate", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "isNewRouteInstance", "shouldRevalidate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "loaderMatch", "arg", "routeChoice", "isStaticRequest", "resultType", "onReject", "handler", "isAbsolute", "activeMatches", "resolvedLocation", "protocol", "contentType", "text", "deferred", "toUpperCase", "body", "URLSearchParams", "File", "name", "found<PERSON><PERSON>r", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "hasErrorBou<PERSON>ry", "_temp4", "errorMessage", "obj", "isRevalidatingLoader", "unwrap", "getAll", "handle", "pathMatches"], "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: any;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. May be either a URL or the pieces of a\n * URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nfunction warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: any;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  status: number;\n  location: string;\n  revalidate: boolean;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: any;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\nexport type MutationFormMethod = \"post\" | \"put\" | \"patch\" | \"delete\";\nexport type FormMethod = \"get\" | MutationFormMethod;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\";\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport interface Submission {\n  formMethod: FormMethod;\n  formAction: string;\n  formEncType: FormEncType;\n  formData: FormData;\n}\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs {\n  request: Request;\n  params: Params;\n  context?: any;\n}\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Route loader function signature\n */\nexport interface LoaderFunction {\n  (args: LoaderFunctionArgs): Promise<Response> | Response | Promise<any> | any;\n}\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction {\n  (args: ActionFunctionArgs): Promise<Response> | Response | Promise<any> | any;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: {\n    currentUrl: URL;\n    currentParams: AgnosticDataRouteMatch[\"params\"];\n    nextUrl: URL;\n    nextParams: AgnosticDataRouteMatch[\"params\"];\n    formMethod?: Submission[\"formMethod\"];\n    formAction?: Submission[\"formAction\"];\n    formEncType?: Submission[\"formEncType\"];\n    formData?: Submission[\"formData\"];\n    actionResult?: DataResult;\n    defaultShouldRevalidate: boolean;\n  }): boolean;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction;\n  action?: ActionFunction;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\ntype PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  parentPath: number[] = [],\n  allIds: Set<string> = new Set<string>()\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !allIds.has(id),\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n    allIds.add(id);\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = { ...route, id };\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        id,\n        children: route.children\n          ? convertRoutesToDataRoutes(route.children, treePath, allIds)\n          : undefined,\n      };\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      // Incoming pathnames are generally encoded from either window.location\n      // or from router.navigate, but we want to match against the unencoded\n      // paths in the route definitions.  Memory router locations won't be\n      // encoded here but there also shouldn't be anything to decode so this\n      // should be a safe operation.  This avoids needing matchRoutes to be\n      // history-aware.\n      safelyDecodeURI(pathname)\n    );\n  }\n\n  return matches;\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  return (\n    path\n      .replace(\n        /^:(\\w+)(\\??)/g,\n        (_, key: PathParam<Path>, optional: string | undefined) => {\n          let param = params[key];\n          if (optional === \"?\") {\n            return param == null ? \"\" : param;\n          }\n          if (param == null) {\n            invariant(false, `Missing \":${key}\" param`);\n          }\n          return param;\n        }\n      )\n      .replace(\n        /\\/:(\\w+)(\\??)/g,\n        (_, key: PathParam<Path>, optional: string | undefined) => {\n          let param = params[key];\n          if (optional === \"?\") {\n            return param == null ? \"\" : `/${param}`;\n          }\n          if (param == null) {\n            invariant(false, `Missing \":${key}\" param`);\n          }\n          return `/${param}`;\n        }\n      )\n      // Remove any optional markers from optional static segments\n      .replace(/\\?/g, \"\")\n      .replace(/(\\/?)\\*/, (_, prefix, __, str) => {\n        const star = \"*\" as PathParam<Path>;\n\n        if (params[star] == null) {\n          // If no splat was provided, trim the trailing slash _unless_ it's\n          // the entire path\n          return str === \"/*\" ? \"/\" : \"\";\n        }\n\n        // Apply the splat\n        return `${prefix}${params[star]}`;\n      })\n  );\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/\\/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"/([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURI(value: string) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * @private\n */\nexport function warning(cond: any, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging @remix-run/router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, null, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    if (error) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\nexport class ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  error?: Error;\n  internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n} from \"./history\";\nimport type {\n  DataResult,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DeferredResult,\n  ErrorR<PERSON>ult,\n  FormEncType,\n  FormMethod,\n  Redirect<PERSON><PERSON>ult,\n  RouteData,\n  AgnosticRouteObject,\n  Submission,\n  SuccessResult,\n  AgnosticRouteMatch,\n  MutationFormMethod,\n  ShouldRevalidateFunction,\n} from \"./utils\";\nimport {\n  DeferredData,\n  ErrorResponse,\n  ResultType,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  resolveTo,\n  warning,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string,\n    opts?: RouterNavigateOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key?: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key?: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  basename?: string;\n  routes: AgnosticRouteObject[];\n  history: History;\n  hydrationData?: HydrationState;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: { requestContext?: unknown }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: { routeId?: string; requestContext?: unknown }\n  ): Promise<any>;\n}\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (state: RouterState): void;\n}\n\ninterface UseMatchesMatch {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: unknown;\n  handle: unknown;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UseMatchesMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\n/**\n * Options for a navigate() call for a Link navigation\n */\ntype LinkNavigateOptions = {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n};\n\n/**\n * Options for a navigate() call for a Form navigation\n */\ntype SubmissionNavigateOptions = {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  formMethod?: FormMethod;\n  formEncType?: FormEncType;\n  formData: FormData;\n};\n\n/**\n * Options to pass to navigate() for either a Link or Form navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions =\n  | Omit<LinkNavigateOptions, \"replace\">\n  | Omit<SubmissionNavigateOptions, \"replace\">;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Error thrown from the current action, keyed by the route containing the\n   * error boundary to render the error.  To be committed to the state after\n   * loaders have completed\n   */\n  pendingActionError?: RouteData;\n  /**\n   * Data returned from the current action, keyed by the route owning the action.\n   * To be committed to the state after loaders have completed\n   */\n  pendingActionData?: RouteData;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n  match: AgnosticDataRouteMatch;\n  matches: AgnosticDataRouteMatch[];\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n}\n\n/**\n * Wrapper object to allow us to throw any response out from callLoaderOrAction\n * for queryRouter while preserving whether or not it was thrown or returned\n * from the loader/action\n */\ninterface QueryRouteResponse {\n  type: ResultType.data | ResultType.error;\n  response: Response;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser;\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let dataRoutes = convertRoutesToDataRoutes(init.routes);\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(\n    dataRoutes,\n    init.history.location,\n    init.basename\n  );\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  let initialized =\n    !initialMatches.some((m) => m.route.loader) || init.hydrationData != null;\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidate()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations from their actions\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // We ony support a single active blocker at the moment since we don't have\n  // any compelling use cases for multi-blocker yet\n  let activeBlocker: string | null = null;\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (ignoreNextHistoryUpdate) {\n          ignoreNextHistoryUpdate = false;\n          return;\n        }\n\n        warning(\n          activeBlocker != null && delta === null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          ignoreNextHistoryUpdate = true;\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked\n              init.history.go(delta);\n            },\n            reset() {\n              deleteBlocker(blockerKey!);\n              updateState({ blockers: new Map(router.state.blockers) });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location);\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(newState: Partial<RouterState>): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n    subscribers.forEach((subscriber) => subscriber(state));\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    for (let [key] of blockerFunctions) {\n      deleteBlocker(key);\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    updateState({\n      ...newState, // matches, errors, fetchers go through as-is\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(\n        location,\n        newState.matches || state.matches\n      ),\n      preventScrollReset,\n      blockers: new Map(state.blockers),\n    });\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(to, opts);\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          deleteBlocker(blockerKey!);\n          updateState({ blockers: new Map(state.blockers) });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      submission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponse;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(dataRoutes, location, init.basename);\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error,\n        },\n      });\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a mutation submission\n    // For example, on /page#hash and submit a <Form method=\"post\"> which will\n    // default to a navigation to /page\n    if (\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionData: RouteData | undefined;\n    let pendingError: RouteData | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError,\n      };\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        { replace: opts.replace }\n      );\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        ...opts.submission,\n      };\n      loadingNavigation = navigation;\n\n      // Create a GET request for the loaders\n      request = new Request(request.url, { signal: request.signal });\n    }\n\n    // Call loaders\n    let { shortCircuited, loaderData, errors } = await handleLoaders(\n      request,\n      location,\n      matches,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.replace,\n      pendingActionData,\n      pendingError\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches,\n      ...(pendingActionData ? { actionData: pendingActionData } : {}),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    opts?: { replace?: boolean }\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation: NavigationStates[\"Submitting\"] = {\n      state: \"submitting\",\n      location,\n      ...submission,\n    };\n    updateState({ navigation });\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        router.basename\n      );\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace =\n          result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, { submission, replace });\n      return { shortCircuited: true };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: { [boundaryMatch.route.id]: result.error },\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    return {\n      pendingActionData: { [actionMatch.route.id]: result.data },\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    replace?: boolean,\n    pendingActionData?: RouteData,\n    pendingError?: RouteData\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n    if (!loadingNavigation) {\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        ...submission,\n      };\n      loadingNavigation = navigation;\n    }\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission = submission\n      ? submission\n      : loadingNavigation.formMethod &&\n        loadingNavigation.formAction &&\n        loadingNavigation.formData &&\n        loadingNavigation.formEncType\n      ? {\n          formMethod: loadingNavigation.formMethod,\n          formAction: loadingNavigation.formAction,\n          formData: loadingNavigation.formData,\n          formEncType: loadingNavigation.formEncType,\n        }\n      : undefined;\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      pendingActionData,\n      pendingError,\n      fetchLoadMatches\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      completeNavigation(location, {\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null,\n        ...(pendingActionData ? { actionData: pendingActionData } : {}),\n      });\n      return { shortCircuited: true };\n    }\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach((rf) => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true,\n        };\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState({\n        navigation: loadingNavigation,\n        ...(actionData\n          ? Object.keys(actionData).length === 0\n            ? { actionData: null }\n            : { actionData }\n          : {}),\n        ...(revalidatingFetchers.length > 0\n          ? { fetchers: new Map(state.fetchers) }\n          : {}),\n      });\n    }\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach((rf) =>\n      fetchControllers.set(rf.key, pendingNavigationController!)\n    );\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      await startRedirectNavigation(state, redirect, { replace });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingError,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n\n    return {\n      loaderData,\n      errors,\n      ...(didAbortFetchLoads || revalidatingFetchers.length > 0\n        ? { fetchers: new Map(state.fetchers) }\n        : {}),\n    };\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n\n    let matches = matchRoutes(dataRoutes, href, init.basename);\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: href })\n      );\n      return;\n    }\n\n    let { path, submission } = normalizeNavigateOptions(href, opts, true);\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path, match, matches });\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId,\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher: FetcherStates[\"Submitting\"] = {\n      state: \"submitting\",\n      ...submission,\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, fetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n    fetchControllers.set(key, abortController);\n\n    let actionResult = await callLoaderOrAction(\n      \"action\",\n      fetchRequest,\n      match,\n      requestMatches,\n      router.basename\n    );\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n      let loadingFetcher: FetcherStates[\"Loading\"] = {\n        state: \"loading\",\n        ...submission,\n        data: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, loadingFetcher);\n      updateState({ fetchers: new Map(state.fetchers) });\n\n      return startRedirectNavigation(state, actionResult, {\n        isFetchActionRedirect: true,\n      });\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n\n      nextLocation,\n      abortController.signal\n    );\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(dataRoutes, state.navigation.location, init.basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      data: actionResult.data,\n      ...submission,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      { [match.route.id]: actionResult.data },\n      undefined, // No need to send through errors since we short circuit above\n      fetchLoadMatches\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: existingFetcher && existingFetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true,\n        };\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        fetchControllers.set(staleKey, abortController);\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      return startRedirectNavigation(state, redirect);\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, doneFetcher);\n\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        ...(didAbortFetchLoads ? { fetchers: new Map(state.fetchers) } : {}),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    // Put this fetcher into it's loading state\n    let loadingFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      ...submission,\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, loadingFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n    fetchControllers.set(key, abortController);\n    let result: DataResult = await callLoaderOrAction(\n      \"loader\",\n      fetchRequest,\n      match,\n      matches,\n      router.basename\n    );\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by ou our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      await startRedirectNavigation(state, result);\n      return;\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key);\n      // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error,\n        },\n      });\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    state: RouterState,\n    redirect: RedirectResult,\n    {\n      submission,\n      replace,\n      isFetchActionRedirect,\n    }: {\n      submission?: Submission;\n      replace?: boolean;\n      isFetchActionRedirect?: boolean;\n    } = {}\n  ) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n\n    let redirectLocation = createLocation(\n      state.location,\n      redirect.location,\n      // TODO: This can be removed once we get rid of useTransition in Remix v2\n      {\n        _isRedirect: true,\n        ...(isFetchActionRedirect ? { _isFetchActionRedirect: true } : {}),\n      }\n    );\n    invariant(\n      redirectLocation,\n      \"Expected a location on the redirect navigation\"\n    );\n\n    // Check if this an external redirect that goes to a new origin\n    if (isBrowser && typeof window?.location !== \"undefined\") {\n      let newOrigin = init.history.createURL(redirect.location).origin;\n      if (window.location.origin !== newOrigin) {\n        if (replace) {\n          window.location.replace(redirect.location);\n        } else {\n          window.location.assign(redirect.location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType, formData } = state.navigation;\n    if (!submission && formMethod && formAction && formData && formEncType) {\n      submission = {\n        formMethod,\n        formAction,\n        formEncType,\n        formData,\n      };\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.status) &&\n      submission &&\n      isMutationMethod(submission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...submission,\n          formAction: redirect.location,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else {\n      // Otherwise, we kick off a new loading navigation, preserving the\n      // submission info for the duration of this navigation\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: {\n          state: \"loading\",\n          location: redirectLocation,\n          formMethod: submission ? submission.formMethod : undefined,\n          formAction: submission ? submission.formAction : undefined,\n          formEncType: submission ? submission.formEncType : undefined,\n          formData: submission ? submission.formData : undefined,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\"loader\", request, match, matches, router.basename)\n      ),\n      ...fetchersToLoad.map((f) =>\n        callLoaderOrAction(\n          \"loader\",\n          createClientSideRequest(init.history, f.path, request.signal),\n          f.match,\n          f.matches,\n          router.basename\n        )\n      ),\n    ]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        request.signal,\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map((f) => f.match),\n        fetcherResults,\n        request.signal,\n        true\n      ),\n    ]);\n\n    return { results, loaderResults, fetcherResults };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function setFetcherError(key: string, routeId: string, error: any) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error,\n      },\n      fetchers: new Map(state.fetchers),\n    });\n  }\n\n  function deleteFetcher(key: string): void {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): void {\n    let doneKeys = [];\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n      }\n    }\n    markFetchersDone(doneKeys);\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n      if (activeBlocker == null) {\n        // This is now the active blocker\n        activeBlocker = key;\n      } else if (key !== activeBlocker) {\n        warning(false, \"A router only supports one blocker at a time\");\n      }\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n    if (activeBlocker === key) {\n      activeBlocker = null;\n    }\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    state.blockers.set(key, newBlocker);\n    updateState({ blockers: new Map(state.blockers) });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (activeBlocker == null) {\n      return;\n    }\n\n    // We only allow a single blocker at the moment.  This will need to be\n    // updated if we enhance to support multiple blockers in the future\n    let blockerFunction = blockerFunctions.get(activeBlocker);\n    invariant(\n      blockerFunction,\n      \"Could not find a function for the active blocker\"\n    );\n    let blocker = state.blockers.get(activeBlocker);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return activeBlocker;\n    }\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || ((location) => location.key);\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  router = {\n    get basename() {\n      return init.basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: {\n    basename?: string;\n  }\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let dataRoutes = convertRoutesToDataRoutes(routes);\n  let basename = (opts ? opts.basename : null) || \"/\";\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(\n    request: Request,\n    { requestContext }: { requestContext?: unknown } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"head\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n    }: { requestContext?: unknown; routeId?: string } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"head\" && method !== \"options\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      match\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        basename,\n        true,\n        isRouteRequest,\n        requestContext\n      );\n\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(`${method}() call aborted`);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        undefined,\n        {\n          [boundaryMatch.route.id]: result.error,\n        }\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n\n    return {\n      ...context,\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      actionHeaders: {\n        ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n      },\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch,\n    pendingActionError?: RouteData\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (isRouteRequest && !routeMatch?.route.loader) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : getLoaderMatchesUntilBoundary(\n          matches,\n          Object.keys(pendingActionError || {})[0]\n        );\n    let matchesToLoad = requestMatches.filter((m) => m.route.loader);\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          basename,\n          true,\n          isRouteRequest,\n          requestContext\n        )\n      ),\n    ]);\n\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(`${method}() call aborted`);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionError,\n      activeDeferreds\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction isSubmissionNavigation(\n  opts: RouterNavigateOptions\n): opts is SubmissionNavigateOptions {\n  return opts != null && \"formData\" in opts;\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  to: To,\n  opts?: RouterNavigateOptions,\n  isFetcher = false\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponse;\n} {\n  let path = typeof to === \"string\" ? to : createPath(to);\n\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  // Create a Submission on non-GET navigations\n  let submission: Submission | undefined;\n  if (opts.formData) {\n    submission = {\n      formMethod: opts.formMethod || \"get\",\n      formAction: stripHashFromPath(path),\n      formEncType:\n        (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n      formData: opts.formData,\n    };\n\n    if (isMutationMethod(submission.formMethod)) {\n      return { path, submission };\n    }\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  let searchParams = convertFormDataToSearchParams(opts.formData);\n  // Since fetcher GET submissions only run a single loader (as opposed to\n  // navigation GET submissions which run all loaders), we need to preserve\n  // any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId?: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  pendingActionData?: RouteData,\n  pendingError?: RouteData,\n  fetchLoadMatches?: Map<string, FetchLoadMatch>\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingError\n    ? Object.values(pendingError)[0]\n    : pendingActionData\n    ? Object.values(pendingActionData)[0]\n    : undefined;\n\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  let defaultShouldRevalidate =\n    // Forced revalidation due to submission, useRevalidate, or X-Remix-Revalidate\n    isRevalidationRequired ||\n    // Clicked the same link, resubmitted a GET form\n    currentUrl.toString() === nextUrl.toString() ||\n    // Search params affect all loaders\n    currentUrl.search !== nextUrl.search;\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    if (match.route.loader == null) {\n      return false;\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      defaultShouldRevalidate:\n        defaultShouldRevalidate ||\n        isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches &&\n    fetchLoadMatches.forEach((f, key) => {\n      if (!matches.some((m) => m.route.id === f.routeId)) {\n        // This fetcher is not going to be present in the subsequent render so\n        // there's no need to revalidate it\n        return;\n      } else if (cancelledFetcherLoads.includes(key)) {\n        // This fetcher was cancelled from a prior action submission - force reload\n        revalidatingFetchers.push({ key, ...f });\n      } else {\n        // Revalidating fetchers are decoupled from the route matches since they\n        // hit a static href, so they _always_ check shouldRevalidate and the\n        // default is strictly if a revalidation is explicitly required (action\n        // submissions, useRevalidator, X-Remix-Revalidate).\n        let shouldRevalidate = shouldRevalidateLoader(f.match, {\n          currentUrl,\n          currentParams: state.matches[state.matches.length - 1].params,\n          nextUrl,\n          nextParams: matches[matches.length - 1].params,\n          ...submission,\n          actionResult,\n          defaultShouldRevalidate,\n        });\n        if (shouldRevalidate) {\n          revalidatingFetchers.push({ key, ...f });\n        }\n      }\n    });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: Parameters<ShouldRevalidateFunction>[0]\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  matches: AgnosticDataRouteMatch[],\n  basename = \"/\",\n  isStaticRequest: boolean = false,\n  isRouteRequest: boolean = false,\n  requestContext?: unknown\n): Promise<DataResult> {\n  let resultType;\n  let result;\n\n  // Setup a promise we can race against so that abort signals short circuit\n  let reject: () => void;\n  let abortPromise = new Promise((_, r) => (reject = r));\n  let onReject = () => reject();\n  request.signal.addEventListener(\"abort\", onReject);\n\n  try {\n    let handler = match.route[type];\n    invariant<Function>(\n      handler,\n      `Could not find the ${type} to run on the \"${match.route.id}\" route`\n    );\n\n    result = await Promise.race([\n      handler({ request, params: match.params, context: requestContext }),\n      abortPromise,\n    ]);\n\n    invariant(\n      result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    request.signal.removeEventListener(\"abort\", onReject);\n  }\n\n  if (isResponse(result)) {\n    let status = result.status;\n\n    // Process redirects\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(\n        location,\n        \"Redirects returned/thrown from loaders/actions must have a Location header\"\n      );\n\n      let isAbsolute = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i.test(location);\n\n      // Support relative routing in internal redirects\n      if (!isAbsolute) {\n        let activeMatches = matches.slice(0, matches.indexOf(match) + 1);\n        let routePathnames = getPathContributingMatches(activeMatches).map(\n          (match) => match.pathnameBase\n        );\n        let resolvedLocation = resolveTo(\n          location,\n          routePathnames,\n          new URL(request.url).pathname\n        );\n        invariant(\n          createPath(resolvedLocation),\n          `Unable to resolve redirect location: ${location}`\n        );\n\n        // Prepend the basename to the redirect location if we have one\n        if (basename) {\n          let path = resolvedLocation.pathname;\n          resolvedLocation.pathname =\n            path === \"/\" ? basename : joinPaths([basename, path]);\n        }\n\n        location = createPath(resolvedLocation);\n      } else if (!isStaticRequest) {\n        // Strip off the protocol+origin for same-origin absolute redirects.\n        // If this is a static reques, we can let it go back to the browser\n        // as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\")\n          ? new URL(currentUrl.protocol + location)\n          : new URL(location);\n        if (url.origin === currentUrl.origin) {\n          location = url.pathname + url.search + url.hash;\n        }\n      }\n\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null,\n      };\n    }\n\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result,\n      };\n    }\n\n    let data: any;\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return { type: resultType, error: result };\n  }\n\n  if (result instanceof DeferredData) {\n    return { type: ResultType.deferred, deferredData: result };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType, formData } = submission;\n    init.method = formMethod.toUpperCase();\n    init.body =\n      formEncType === \"application/x-www-form-urlencoded\"\n        ? convertFormDataToSearchParams(formData)\n        : formData;\n  }\n\n  // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, value instanceof File ? value.name : value);\n  }\n\n  return searchParams;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      // Prefer higher error values if lower errors bubble to the same boundary\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      }\n\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (\n        result.statusCode != null &&\n        result.statusCode !== 200 &&\n        !foundError\n      ) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingError,\n    activeDeferreds\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, match } = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n    id: `__shim-error-route__`,\n  };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\";\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponse(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results: DataResult[]): RedirectResult | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  return (\n    a.pathname === b.pathname && a.search === b.search && a.hash !== b.hash\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj: any): obj is QueryRouteResponse {\n  return (\n    obj &&\n    isResponse(obj.response) &&\n    (obj.type === ResultType.data || ResultType.error)\n  );\n}\n\nfunction isValidMethod(method: string): method is FormMethod {\n  return validRequestMethods.has(method as FormMethod);\n}\n\nfunction isMutationMethod(method?: string): method is MutationFormMethod {\n  return validMutationMethods.has(method as MutationFormMethod);\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  signal: AbortSignal,\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\n// Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\nfunction createUseMatchesMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UseMatchesMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id] as unknown,\n    handle: route.handle as unknown,\n  };\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;;AAEG;IACSA,MAAA;AAAZ,WAAYA,MAAZ,EAAkB;EAChB;;;;;;AAMG;EACHA,MAAA;EAEA;;;;AAIG;;EACHA,MAAA;EAEA;;;AAGG;;EACHA,MAAA;AACD,CAtBD,EAAYA,MAAM,KAANA,MAAM,GAsBjB,EAtBiB,CAAlB;AAwLA,MAAMC,iBAAiB,GAAG,UAA1B;AA+BA;;;AAGG;;AACa,SAAAC,oBACdC,OADc,EACoB;EAAA,IAAlCA,OAAkC;IAAlCA,OAAkC,GAAF,EAAE;EAAA;EAElC,IAAI;IAAEC,cAAc,GAAG,CAAC,GAAD,CAAnB;IAA0BC,YAA1B;IAAwCC,QAAQ,GAAG;EAAnD,IAA6DH,OAAjE;EACA,IAAII,OAAJ,CAHkC;;EAIlCA,OAAO,GAAGH,cAAc,CAACI,GAAf,CAAmB,CAACC,KAAD,EAAQC,KAAR,KAC3BC,oBAAoB,CAClBF,KADkB,EAElB,OAAOA,KAAP,KAAiB,QAAjB,GAA4B,IAA5B,GAAmCA,KAAK,CAACG,KAFvB,EAGlBF,KAAK,KAAK,CAAV,GAAc,SAAd,GAA0BG,SAHR,CADZ,CAAV;EAOA,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAhB,GAAuBE,OAAO,CAACQ,MAAR,GAAiB,CAAxC,GAA4CV,YADxB,CAAtB;EAGA,IAAIW,MAAM,GAAGhB,MAAM,CAACiB,GAApB;EACA,IAAIC,QAAQ,GAAoB,IAAhC;EAEA,SAASJ,UAATA,CAAoBK,CAApB,EAA6B;IAC3B,OAAOC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASH,CAAT,EAAY,CAAZ,CAAT,EAAyBZ,OAAO,CAACQ,MAAR,GAAiB,CAA1C,CAAP;EACD;EACD,SAASQ,kBAATA,CAAA,EAA2B;IACzB,OAAOhB,OAAO,CAACG,KAAD,CAAd;EACD;EACD,SAASC,oBAATA,CACEa,EADF,EAEEZ,KAFF,EAGEa,GAHF,EAGc;IAAA,IADZb,KACY;MADZA,KACY,GADC,IACD;IAAA;IAEZ,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,GAAGK,QAAxB,GAAmC,GADf,EAE3BJ,EAF2B,EAG3BZ,KAH2B,EAI3Ba,GAJ2B,CAA7B;IAMAI,SAAO,CACLH,QAAQ,CAACE,QAAT,CAAkBE,MAAlB,CAAyB,CAAzB,CAAgC,QAD3B,+DAEsDC,IAAI,CAACC,SAAL,CACzDR,EADyD,CAFtD,CAAP;IAMA,OAAOE,QAAP;EACD;EAED,SAASO,UAATA,CAAoBT,EAApB,EAA0B;IACxB,OAAO,OAAOA,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BU,UAAU,CAACV,EAAD,CAA/C;EACD;EAED,IAAIW,OAAO,GAAkB;IAC3B,IAAIzB,KAAJA,CAAA,EAAS;MACP,OAAOA,KAAP;KAFyB;IAI3B,IAAIM,MAAJA,CAAA,EAAU;MACR,OAAOA,MAAP;KALyB;IAO3B,IAAIU,QAAJA,CAAA,EAAY;MACV,OAAOH,kBAAkB,EAAzB;KARyB;IAU3BU,UAV2B;IAW3BG,SAASA,CAACZ,EAAD,EAAG;MACV,OAAO,IAAIa,GAAJ,CAAQJ,UAAU,CAACT,EAAD,CAAlB,EAAwB,kBAAxB,CAAP;KAZyB;IAc3Bc,cAAcA,CAACd,EAAD,EAAO;MACnB,IAAIe,IAAI,GAAG,OAAOf,EAAP,KAAc,QAAd,GAAyBgB,SAAS,CAAChB,EAAD,CAAlC,GAAyCA,EAApD;MACA,OAAO;QACLI,QAAQ,EAAEW,IAAI,CAACX,QAAL,IAAiB,EADtB;QAELa,MAAM,EAAEF,IAAI,CAACE,MAAL,IAAe,EAFlB;QAGLC,IAAI,EAAEH,IAAI,CAACG,IAAL,IAAa;OAHrB;KAhByB;IAsB3BC,IAAIA,CAACnB,EAAD,EAAKZ,KAAL,EAAU;MACZI,MAAM,GAAGhB,MAAM,CAAC4C,IAAhB;MACA,IAAIC,YAAY,GAAGlC,oBAAoB,CAACa,EAAD,EAAKZ,KAAL,CAAvC;MACAF,KAAK,IAAI,CAAT;MACAH,OAAO,CAACuC,MAAR,CAAepC,KAAf,EAAsBH,OAAO,CAACQ,MAA9B,EAAsC8B,YAAtC;MACA,IAAIvC,QAAQ,IAAIY,QAAhB,EAA0B;QACxBA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEmB,YAApB;UAAkCE,KAAK,EAAE;QAAzC,CAAD,CAAR;MACD;KA7BwB;IA+B3BC,OAAOA,CAACxB,EAAD,EAAKZ,KAAL,EAAU;MACfI,MAAM,GAAGhB,MAAM,CAACiD,OAAhB;MACA,IAAIJ,YAAY,GAAGlC,oBAAoB,CAACa,EAAD,EAAKZ,KAAL,CAAvC;MACAL,OAAO,CAACG,KAAD,CAAP,GAAiBmC,YAAjB;MACA,IAAIvC,QAAQ,IAAIY,QAAhB,EAA0B;QACxBA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEmB,YAApB;UAAkCE,KAAK,EAAE;QAAzC,CAAD,CAAR;MACD;KArCwB;IAuC3BG,EAAEA,CAACH,KAAD,EAAM;MACN/B,MAAM,GAAGhB,MAAM,CAACiB,GAAhB;MACA,IAAIkC,SAAS,GAAGrC,UAAU,CAACJ,KAAK,GAAGqC,KAAT,CAA1B;MACA,IAAIF,YAAY,GAAGtC,OAAO,CAAC4C,SAAD,CAA1B;MACAzC,KAAK,GAAGyC,SAAR;MACA,IAAIjC,QAAJ,EAAc;QACZA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEmB,YAApB;UAAkCE;QAAlC,CAAD,CAAR;MACD;KA9CwB;IAgD3BK,MAAMA,CAACC,EAAD,EAAa;MACjBnC,QAAQ,GAAGmC,EAAX;MACA,OAAO,MAAK;QACVnC,QAAQ,GAAG,IAAX;OADF;IAGD;GArDH;EAwDA,OAAOiB,OAAP;AACD;AAkBD;;;;;;AAMG;;AACa,SAAAmB,qBACdnD,OADc,EACqB;EAAA,IAAnCA,OAAmC;IAAnCA,OAAmC,GAAF,EAAE;EAAA;EAEnC,SAASoD,qBAATA,CACEC,MADF,EAEEC,aAFF,EAEkC;IAEhC,IAAI;MAAE7B,QAAF;MAAYa,MAAZ;MAAoBC;KAAS,GAAAc,MAAM,CAAC9B,QAAxC;IACA,OAAOC,cAAc,CACnB,EADmB,EAEnB;MAAEC,QAAF;MAAYa,MAAZ;MAAoBC;IAApB,CAFmB;IAAA;IAIlBe,aAAa,CAAC7C,KAAd,IAAuB6C,aAAa,CAAC7C,KAAd,CAAoB8C,GAA5C,IAAoD,IAJjC,EAKlBD,aAAa,CAAC7C,KAAd,IAAuB6C,aAAa,CAAC7C,KAAd,CAAoBa,GAA5C,IAAoD,SALjC,CAArB;EAOD;EAED,SAASkC,iBAATA,CAA2BH,MAA3B,EAA2ChC,EAA3C,EAAiD;IAC/C,OAAO,OAAOA,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BU,UAAU,CAACV,EAAD,CAA/C;EACD;EAED,OAAOoC,kBAAkB,CACvBL,qBADuB,EAEvBI,iBAFuB,EAGvB,IAHuB,EAIvBxD,OAJuB,CAAzB;AAMD;AAsBD;;;;;;;AAOG;;AACa,SAAA0D,kBACd1D,OADc,EACkB;EAAA,IAAhCA,OAAgC;IAAhCA,OAAgC,GAAF,EAAE;EAAA;EAEhC,SAAS2D,kBAATA,CACEN,MADF,EAEEC,aAFF,EAEkC;IAEhC,IAAI;MACF7B,QAAQ,GAAG,GADT;MAEFa,MAAM,GAAG,EAFP;MAGFC,IAAI,GAAG;IAHL,IAIAF,SAAS,CAACgB,MAAM,CAAC9B,QAAP,CAAgBgB,IAAhB,CAAqBqB,MAArB,CAA4B,CAA5B,CAAD,CAJb;IAKA,OAAOpC,cAAc,CACnB,EADmB,EAEnB;MAAEC,QAAF;MAAYa,MAAZ;MAAoBC;IAApB,CAFmB;IAAA;IAIlBe,aAAa,CAAC7C,KAAd,IAAuB6C,aAAa,CAAC7C,KAAd,CAAoB8C,GAA5C,IAAoD,IAJjC,EAKlBD,aAAa,CAAC7C,KAAd,IAAuB6C,aAAa,CAAC7C,KAAd,CAAoBa,GAA5C,IAAoD,SALjC,CAArB;EAOD;EAED,SAASuC,cAATA,CAAwBR,MAAxB,EAAwChC,EAAxC,EAA8C;IAC5C,IAAIyC,IAAI,GAAGT,MAAM,CAACU,QAAP,CAAgBC,aAAhB,CAA8B,MAA9B,CAAX;IACA,IAAIC,IAAI,GAAG,EAAX;IAEA,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAL,CAAkB,MAAlB,CAAZ,EAAuC;MACrC,IAAIC,GAAG,GAAGd,MAAM,CAAC9B,QAAP,CAAgB0C,IAA1B;MACA,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAJ,CAAY,GAAZ,CAAhB;MACAJ,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAf,GAAmBD,GAAnB,GAAyBA,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAaF,SAAb,CAAhC;IACD;IAED,OAAOH,IAAI,GAAG,GAAP,IAAc,OAAO5C,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BU,UAAU,CAACV,EAAD,CAAtD,CAAP;EACD;EAED,SAASkD,oBAATA,CAA8BhD,QAA9B,EAAkDF,EAAlD,EAAwD;IACtDK,SAAO,CACLH,QAAQ,CAACE,QAAT,CAAkBE,MAAlB,CAAyB,CAAzB,CAAgC,QAD3B,iEAEwDC,IAAI,CAACC,SAAL,CAC3DR,EAD2D,CAFxD,GAAP;EAMD;EAED,OAAOoC,kBAAkB,CACvBE,kBADuB,EAEvBE,cAFuB,EAGvBU,oBAHuB,EAIvBvE,OAJuB,CAAzB;AAMD;AAee,SAAAwE,UAAUC,KAAV,EAAsBC,OAAtB,EAAsC;EACpD,IAAID,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,IAA7B,IAAqC,OAAOA,KAAP,KAAiB,WAA1D,EAAuE;IACrE,MAAM,IAAIE,KAAJ,CAAUD,OAAV,CAAN;EACD;AACF;AAED,SAAShD,SAATkD,CAAiBC,IAAjB,EAA4BH,OAA5B,EAA2C;EACzC,IAAI,CAACG,IAAL,EAAW;IACT;IACA,IAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAaL,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIC,KAAJ,CAAUD,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOM,CAAP,EAAU;EACb;AACF;AAED,SAASC,SAATA,CAAA,EAAkB;EAChB,OAAOhE,IAAI,CAACiE,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BvB,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAP;AACD;AAED;;AAEG;;AACH,SAASwB,eAATA,CAAyB7D,QAAzB,EAA6ChB,KAA7C,EAA0D;EACxD,OAAO;IACLgD,GAAG,EAAEhC,QAAQ,CAACd,KADT;IAELa,GAAG,EAAEC,QAAQ,CAACD,GAFT;IAGL+D,GAAG,EAAE9E;GAHP;AAKD;AAED;;AAEG;;AACG,SAAUiB,cAAVA,CACJ8D,OADI,EAEJjE,EAFI,EAGJZ,KAHI,EAIJa,GAJI,EAIQ;EAAA,IADZb,KACY;IADZA,KACY,GADC,IACD;EAAA;EAEZ,IAAIc,QAAQ,GAAAgE,QAAA;IACV9D,QAAQ,EAAE,OAAO6D,OAAP,KAAmB,QAAnB,GAA8BA,OAA9B,GAAwCA,OAAO,CAAC7D,QADhD;IAEVa,MAAM,EAAE,EAFE;IAGVC,IAAI,EAAE;GACF,SAAOlB,EAAP,KAAc,QAAd,GAAyBgB,SAAS,CAAChB,EAAD,CAAlC,GAAyCA,EAJnC;IAKVZ,KALU;IAMV;IACA;IACA;IACA;IACAa,GAAG,EAAGD,EAAE,IAAKA,EAAe,CAACC,GAAxB,IAAgCA,GAAhC,IAAuC2D,SAAS;GAVvD;EAYA,OAAO1D,QAAP;AACD;AAED;;AAEG;;AACa,SAAAQ,WAIAyD,IAAA;EAAA,IAJW;IACzB/D,QAAQ,GAAG,GADc;IAEzBa,MAAM,GAAG,EAFgB;IAGzBC,IAAI,GAAG;GACO,GAAAiD,IAAA;EACd,IAAIlD,MAAM,IAAIA,MAAM,KAAK,GAAzB,EACEb,QAAQ,IAAIa,MAAM,CAACX,MAAP,CAAc,CAAd,CAAqB,QAArB,GAA2BW,MAA3B,GAAoC,MAAMA,MAAtD;EACF,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAArB,EACEd,QAAQ,IAAIc,IAAI,CAACZ,MAAL,CAAY,CAAZ,CAAmB,QAAnB,GAAyBY,IAAzB,GAAgC,MAAMA,IAAlD;EACF,OAAOd,QAAP;AACD;AAED;;AAEG;;AACG,SAAUY,SAAVA,CAAoBD,IAApB,EAAgC;EACpC,IAAIqD,UAAU,GAAkB,EAAhC;EAEA,IAAIrD,IAAJ,EAAU;IACR,IAAIgC,SAAS,GAAGhC,IAAI,CAACiC,OAAL,CAAa,GAAb,CAAhB;IACA,IAAID,SAAS,IAAI,CAAjB,EAAoB;MAClBqB,UAAU,CAAClD,IAAX,GAAkBH,IAAI,CAACwB,MAAL,CAAYQ,SAAZ,CAAlB;MACAhC,IAAI,GAAGA,IAAI,CAACwB,MAAL,CAAY,CAAZ,EAAeQ,SAAf,CAAP;IACD;IAED,IAAIsB,WAAW,GAAGtD,IAAI,CAACiC,OAAL,CAAa,GAAb,CAAlB;IACA,IAAIqB,WAAW,IAAI,CAAnB,EAAsB;MACpBD,UAAU,CAACnD,MAAX,GAAoBF,IAAI,CAACwB,MAAL,CAAY8B,WAAZ,CAApB;MACAtD,IAAI,GAAGA,IAAI,CAACwB,MAAL,CAAY,CAAZ,EAAe8B,WAAf,CAAP;IACD;IAED,IAAItD,IAAJ,EAAU;MACRqD,UAAU,CAAChE,QAAX,GAAsBW,IAAtB;IACD;EACF;EAED,OAAOqD,UAAP;AACD;AASD,SAAShC,kBAATA,CACEkC,WADF,EAEE7D,UAFF,EAGE8D,gBAHF,EAIE5F,OAJF,EAIiC;EAAA,IAA/BA,OAA+B;IAA/BA,OAA+B,GAAF,EAAE;EAAA;EAE/B,IAAI;IAAEqD,MAAM,GAAGU,QAAQ,CAAC8B,WAApB;IAAkC1F,QAAQ,GAAG;EAA7C,IAAuDH,OAA3D;EACA,IAAIsD,aAAa,GAAGD,MAAM,CAACrB,OAA3B;EACA,IAAInB,MAAM,GAAGhB,MAAM,CAACiB,GAApB;EACA,IAAIC,QAAQ,GAAoB,IAAhC;EAEA,IAAIR,KAAK,GAAGuF,QAAQ,EAApB,CAP+B;EAS/B;EACA;;EACA,IAAIvF,KAAK,IAAI,IAAb,EAAmB;IACjBA,KAAK,GAAG,CAAR;IACA+C,aAAa,CAACyC,YAAd,CAAgCR,QAAA,KAAAjC,aAAa,CAAC7C,KAA9C;MAAqD4E,GAAG,EAAE9E;IAA1D,IAAmE,EAAnE;EACD;EAED,SAASuF,QAATA,CAAA,EAAiB;IACf,IAAIrF,KAAK,GAAG6C,aAAa,CAAC7C,KAAd,IAAuB;MAAE4E,GAAG,EAAE;KAA1C;IACA,OAAO5E,KAAK,CAAC4E,GAAb;EACD;EAED,SAASW,SAATA,CAAA,EAAkB;IAChBnF,MAAM,GAAGhB,MAAM,CAACiB,GAAhB;IACA,IAAIkC,SAAS,GAAG8C,QAAQ,EAAxB;IACA,IAAIlD,KAAK,GAAGI,SAAS,IAAI,IAAb,GAAoB,IAApB,GAA2BA,SAAS,GAAGzC,KAAnD;IACAA,KAAK,GAAGyC,SAAR;IACA,IAAIjC,QAAJ,EAAc;MACZA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAES,OAAO,CAACT,QAA5B;QAAsCqB;MAAtC,CAAD,CAAR;IACD;EACF;EAED,SAASJ,IAATA,CAAcnB,EAAd,EAAsBZ,KAAtB,EAAiC;IAC/BI,MAAM,GAAGhB,MAAM,CAAC4C,IAAhB;IACA,IAAIlB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAT,EAAmBF,EAAnB,EAAuBZ,KAAvB,CAA7B;IACA,IAAImF,gBAAJ,EAAsBA,gBAAgB,CAACrE,QAAD,EAAWF,EAAX,CAAhB;IAEtBd,KAAK,GAAGuF,QAAQ,KAAK,CAArB;IACA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAD,EAAWhB,KAAX,CAAlC;IACA,IAAI4D,GAAG,GAAGnC,OAAO,CAACF,UAAR,CAAmBP,QAAnB,CAAV,CAP+B;;IAU/B,IAAI;MACF+B,aAAa,CAAC4C,SAAd,CAAwBD,YAAxB,EAAsC,EAAtC,EAA0C9B,GAA1C;KADF,CAEE,OAAOgC,KAAP,EAAc;MACd;MACA;MACA9C,MAAM,CAAC9B,QAAP,CAAgB6E,MAAhB,CAAuBjC,GAAvB;IACD;IAED,IAAIhE,QAAQ,IAAIY,QAAhB,EAA0B;MACxBA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAES,OAAO,CAACT,QAA5B;QAAsCqB,KAAK,EAAE;MAA7C,CAAD,CAAR;IACD;EACF;EAED,SAASC,OAATA,CAAiBxB,EAAjB,EAAyBZ,KAAzB,EAAoC;IAClCI,MAAM,GAAGhB,MAAM,CAACiD,OAAhB;IACA,IAAIvB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAT,EAAmBF,EAAnB,EAAuBZ,KAAvB,CAA7B;IACA,IAAImF,gBAAJ,EAAsBA,gBAAgB,CAACrE,QAAD,EAAWF,EAAX,CAAhB;IAEtBd,KAAK,GAAGuF,QAAQ,EAAhB;IACA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAD,EAAWhB,KAAX,CAAlC;IACA,IAAI4D,GAAG,GAAGnC,OAAO,CAACF,UAAR,CAAmBP,QAAnB,CAAV;IACA+B,aAAa,CAACyC,YAAd,CAA2BE,YAA3B,EAAyC,EAAzC,EAA6C9B,GAA7C;IAEA,IAAIhE,QAAQ,IAAIY,QAAhB,EAA0B;MACxBA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAES,OAAO,CAACT,QAA5B;QAAsCqB,KAAK,EAAE;MAA7C,CAAD,CAAR;IACD;EACF;EAED,SAASX,SAATA,CAAmBZ,EAAnB,EAAyB;IACvB;IACA;IACA;IACA,IAAIyC,IAAI,GACNT,MAAM,CAAC9B,QAAP,CAAgB8E,MAAhB,KAA2B,MAA3B,GACIhD,MAAM,CAAC9B,QAAP,CAAgB8E,MADpB,GAEIhD,MAAM,CAAC9B,QAAP,CAAgB0C,IAHtB;IAKA,IAAIA,IAAI,GAAG,OAAO5C,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BU,UAAU,CAACV,EAAD,CAAnD;IACAmD,SAAS,CACPV,IADO,EAE+D,wEAAAG,IAF/D,CAAT;IAIA,OAAO,IAAI/B,GAAJ,CAAQ+B,IAAR,EAAcH,IAAd,CAAP;EACD;EAED,IAAI9B,OAAO,GAAY;IACrB,IAAInB,MAAJA,CAAA,EAAU;MACR,OAAOA,MAAP;KAFmB;IAIrB,IAAIU,QAAJA,CAAA,EAAY;MACV,OAAOoE,WAAW,CAACtC,MAAD,EAASC,aAAT,CAAlB;KALmB;IAOrBL,MAAMA,CAACC,EAAD,EAAa;MACjB,IAAInC,QAAJ,EAAc;QACZ,MAAM,IAAI4D,KAAJ,CAAU,4CAAV,CAAN;MACD;MACDtB,MAAM,CAACiD,gBAAP,CAAwBxG,iBAAxB,EAA2CkG,SAA3C;MACAjF,QAAQ,GAAGmC,EAAX;MAEA,OAAO,MAAK;QACVG,MAAM,CAACkD,mBAAP,CAA2BzG,iBAA3B,EAA8CkG,SAA9C;QACAjF,QAAQ,GAAG,IAAX;OAFF;KAdmB;IAmBrBe,UAAUA,CAACT,EAAD,EAAG;MACX,OAAOS,UAAU,CAACuB,MAAD,EAAShC,EAAT,CAAjB;KApBmB;IAsBrBY,SAtBqB;IAuBrBE,cAAcA,CAACd,EAAD,EAAG;MACf;MACA,IAAI8C,GAAG,GAAGlC,SAAS,CAACZ,EAAD,CAAnB;MACA,OAAO;QACLI,QAAQ,EAAE0C,GAAG,CAAC1C,QADT;QAELa,MAAM,EAAE6B,GAAG,CAAC7B,MAFP;QAGLC,IAAI,EAAE4B,GAAG,CAAC5B;OAHZ;KA1BmB;IAgCrBC,IAhCqB;IAiCrBK,OAjCqB;IAkCrBE,EAAEA,CAAC/B,CAAD,EAAE;MACF,OAAOsC,aAAa,CAACP,EAAd,CAAiB/B,CAAjB,CAAP;IACD;GApCH;EAuCA,OAAOgB,OAAP;AACD;;ACpsBD,IAAYwE,UAAZ;AAAA,WAAYA,UAAZ,EAAsB;EACpBA,UAAA;EACAA,UAAA;EACAA,UAAA;EACAA,UAAA;AACD,CALD,EAAYA,UAAU,KAAVA,UAAU,GAKrB,EALqB,CAAtB;AAmQA,SAASC,YAATA,CACEC,KADF,EAC4B;EAE1B,OAAOA,KAAK,CAACnG,KAAN,KAAgB,IAAvB;AACD;AAGD;;AACM,SAAUoG,yBAAVA,CACJC,MADI,EAEJC,UAFI,EAGJC,MAHI,EAGmC;EAAA,IADvCD,UACuC;IADvCA,UACuC,GADhB,EACgB;EAAA;EAAA,IAAvCC,MAAuC;IAAvCA,MAAuC,GAAjB,IAAIC,GAAJ,EAAiB;EAAA;EAEvC,OAAOH,MAAM,CAACvG,GAAP,CAAW,CAACqG,KAAD,EAAQnG,KAAR,KAAiB;IACjC,IAAIyG,QAAQ,GAAG,CAAC,GAAGH,UAAJ,EAAgBtG,KAAhB,CAAf;IACA,IAAI0G,EAAE,GAAG,OAAOP,KAAK,CAACO,EAAb,KAAoB,QAApB,GAA+BP,KAAK,CAACO,EAArC,GAA0CD,QAAQ,CAACE,IAAT,CAAc,GAAd,CAAnD;IACA1C,SAAS,CACPkC,KAAK,CAACnG,KAAN,KAAgB,IAAhB,IAAwB,CAACmG,KAAK,CAACS,QADxB,EAAT;IAIA3C,SAAS,CACP,CAACsC,MAAM,CAACM,GAAP,CAAWH,EAAX,CADM,EAEP,wCAAqCA,EAArC,mBACE,wDAHK,CAAT;IAKAH,MAAM,CAACO,GAAP,CAAWJ,EAAX;IAEA,IAAIR,YAAY,CAACC,KAAD,CAAhB,EAAyB;MACvB,IAAIY,UAAU,GAAA/B,QAAA,KAAsCmB,KAAtC;QAA6CO;OAA3D;MACA,OAAOK,UAAP;IACD,CAHD,MAGO;MACL,IAAIC,iBAAiB,GAAAhC,QAAA,KAChBmB,KADgB;QAEnBO,EAFmB;QAGnBE,QAAQ,EAAET,KAAK,CAACS,QAAN,GACNR,yBAAyB,CAACD,KAAK,CAACS,QAAP,EAAiBH,QAAjB,EAA2BF,MAA3B,CADnB,GAENpG;OALN;MAOA,OAAO6G,iBAAP;IACD;EACF,CA3BM,CAAP;AA4BD;AAED;;;;AAIG;;AACG,SAAUC,WAAVA,CAGJZ,MAHI,EAIJa,WAJI,EAKJC,QALI,EAKU;EAAA,IAAdA,QAAc;IAAdA,QAAc,GAAH,GAAG;EAAA;EAEd,IAAInG,QAAQ,GACV,OAAOkG,WAAP,KAAuB,QAAvB,GAAkCpF,SAAS,CAACoF,WAAD,CAA3C,GAA2DA,WAD7D;EAGA,IAAIhG,QAAQ,GAAGkG,aAAa,CAACpG,QAAQ,CAACE,QAAT,IAAqB,GAAtB,EAA2BiG,QAA3B,CAA5B;EAEA,IAAIjG,QAAQ,IAAI,IAAhB,EAAsB;IACpB,OAAO,IAAP;EACD;EAED,IAAImG,QAAQ,GAAGC,aAAa,CAACjB,MAAD,CAA5B;EACAkB,iBAAiB,CAACF,QAAD,CAAjB;EAEA,IAAIG,OAAO,GAAG,IAAd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBD,OAAO,IAAI,IAAX,IAAmBC,CAAC,GAAGJ,QAAQ,CAAChH,MAAhD,EAAwD,EAAEoH,CAA1D,EAA6D;IAC3DD,OAAO,GAAGE,gBAAgB,CACxBL,QAAQ,CAACI,CAAD,CADgB;IAAA;IAGxB;IACA;IACA;IACA;IACA;IACAE,eAAe,CAACzG,QAAD,CARS,CAA1B;EAUD;EAED,OAAOsG,OAAP;AACD;AAmBD,SAASF,aAATA,CAGEjB,MAHF,EAIEgB,QAJF,EAKEO,WALF,EAMEtB,UANF,EAMiB;EAAA,IAFfe,QAEe;IAFfA,QAEe,GAF4B,EAE5B;EAAA;EAAA,IADfO,WACe;IADfA,WACe,GAD6B,EAC7B;EAAA;EAAA,IAAftB,UAAe;IAAfA,UAAe,GAAF,EAAE;EAAA;EAEf,IAAIuB,YAAY,GAAGA,CACjB1B,KADiB,EAEjBnG,KAFiB,EAGjB8H,YAHiB,KAIf;IACF,IAAIC,IAAI,GAA+B;MACrCD,YAAY,EACVA,YAAY,KAAK3H,SAAjB,GAA6BgG,KAAK,CAACtE,IAAN,IAAc,EAA3C,GAAgDiG,YAFb;MAGrCE,aAAa,EAAE7B,KAAK,CAAC6B,aAAN,KAAwB,IAHF;MAIrCC,aAAa,EAAEjI,KAJsB;MAKrCmG;KALF;IAQA,IAAI4B,IAAI,CAACD,YAAL,CAAkBI,UAAlB,CAA6B,GAA7B,CAAJ,EAAuC;MACrCjE,SAAS,CACP8D,IAAI,CAACD,YAAL,CAAkBI,UAAlB,CAA6B5B,UAA7B,CADO,EAEP,2BAAwByB,IAAI,CAACD,YAA7B,GACM,kCAAAxB,UADN,oHAFO,CAAT;MAOAyB,IAAI,CAACD,YAAL,GAAoBC,IAAI,CAACD,YAAL,CAAkB/D,KAAlB,CAAwBuC,UAAU,CAACjG,MAAnC,CAApB;IACD;IAED,IAAIwB,IAAI,GAAGsG,SAAS,CAAC,CAAC7B,UAAD,EAAayB,IAAI,CAACD,YAAlB,CAAD,CAApB;IACA,IAAIM,UAAU,GAAGR,WAAW,CAACS,MAAZ,CAAmBN,IAAnB,CAAjB,CArBE;IAwBF;IACA;;IACA,IAAI5B,KAAK,CAACS,QAAN,IAAkBT,KAAK,CAACS,QAAN,CAAevG,MAAf,GAAwB,CAA9C,EAAiD;MAC/C4D,SAAS;MAAA;MAEP;MACAkC,KAAK,CAACnG,KAAN,KAAgB,IAHT,EAIP,yDACuC,4CAAA6B,IADvC,SAJO,CAAT;MAQAyF,aAAa,CAACnB,KAAK,CAACS,QAAP,EAAiBS,QAAjB,EAA2Be,UAA3B,EAAuCvG,IAAvC,CAAb;IACD,CApCC;IAuCF;;IACA,IAAIsE,KAAK,CAACtE,IAAN,IAAc,IAAd,IAAsB,CAACsE,KAAK,CAACnG,KAAjC,EAAwC;MACtC;IACD;IAEDqH,QAAQ,CAACpF,IAAT,CAAc;MACZJ,IADY;MAEZyG,KAAK,EAAEC,YAAY,CAAC1G,IAAD,EAAOsE,KAAK,CAACnG,KAAb,CAFP;MAGZoI;KAHF;GAhDF;EAsDA/B,MAAM,CAACmC,OAAP,CAAe,CAACrC,KAAD,EAAQnG,KAAR,KAAiB;IAAA,IAAAyI,WAAA;;IAC9B;IACA,IAAItC,KAAK,CAACtE,IAAN,KAAe,EAAf,IAAqB,EAAC,CAAA4G,WAAA,GAAAtC,KAAK,CAACtE,IAAP,aAAC4G,WAAY,CAAAC,QAAZ,CAAqB,GAArB,CAAD,CAAzB,EAAqD;MACnDb,YAAY,CAAC1B,KAAD,EAAQnG,KAAR,CAAZ;IACD,CAFD,MAEO;MACL,KAAK,IAAI2I,QAAT,IAAqBC,uBAAuB,CAACzC,KAAK,CAACtE,IAAP,CAA5C,EAA0D;QACxDgG,YAAY,CAAC1B,KAAD,EAAQnG,KAAR,EAAe2I,QAAf,CAAZ;MACD;IACF;GARH;EAWA,OAAOtB,QAAP;AACD;AAED;;;;;;;;;;;;;AAaG;;AACH,SAASuB,uBAATA,CAAiC/G,IAAjC,EAA6C;EAC3C,IAAIgH,QAAQ,GAAGhH,IAAI,CAACiH,KAAL,CAAW,GAAX,CAAf;EACA,IAAID,QAAQ,CAACxI,MAAT,KAAoB,CAAxB,EAA2B,OAAO,EAAP;EAE3B,IAAI,CAAC0I,KAAD,EAAQ,GAAGC,IAAX,CAAmB,GAAAH,QAAvB,CAJ2C;;EAO3C,IAAII,UAAU,GAAGF,KAAK,CAACG,QAAN,CAAe,GAAf,CAAjB,CAP2C;;EAS3C,IAAIC,QAAQ,GAAGJ,KAAK,CAACzG,OAAN,CAAc,KAAd,EAAqB,EAArB,CAAf;EAEA,IAAI0G,IAAI,CAAC3I,MAAL,KAAgB,CAApB,EAAuB;IACrB;IACA;IACA,OAAO4I,UAAU,GAAG,CAACE,QAAD,EAAW,EAAX,CAAH,GAAoB,CAACA,QAAD,CAArC;EACD;EAED,IAAIC,YAAY,GAAGR,uBAAuB,CAACI,IAAI,CAACrC,IAAL,CAAU,GAAV,CAAD,CAA1C;EAEA,IAAI0C,MAAM,GAAa,EAAvB,CAnB2C;EAsB3C;EACA;EACA;EACA;EACA;EACA;;EACAA,MAAM,CAACpH,IAAP,CACE,GAAGmH,YAAY,CAACtJ,GAAb,CAAkBwJ,OAAD,IAClBA,OAAO,KAAK,EAAZ,GAAiBH,QAAjB,GAA4B,CAACA,QAAD,EAAWG,OAAX,EAAoB3C,IAApB,CAAyB,GAAzB,CAD3B,CADL,EA5B2C;;EAmC3C,IAAIsC,UAAJ,EAAgB;IACdI,MAAM,CAACpH,IAAP,CAAY,GAAGmH,YAAf;EACD,CArC0C;;EAwC3C,OAAOC,MAAM,CAACvJ,GAAP,CAAY6I,QAAD,IAChB9G,IAAI,CAACqG,UAAL,CAAgB,GAAhB,KAAwBS,QAAQ,KAAK,EAArC,GAA0C,GAA1C,GAAgDA,QAD3C,CAAP;AAGD;AAED,SAASpB,iBAATA,CAA2BF,QAA3B,EAAkD;EAChDA,QAAQ,CAACkC,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KACZD,CAAC,CAAClB,KAAF,KAAYmB,CAAC,CAACnB,KAAd,GACImB,CAAC,CAACnB,KAAF,GAAUkB,CAAC,CAAClB,KADhB;EAAA,EAEIoB,cAAc,CACZF,CAAC,CAACpB,UAAF,CAAatI,GAAb,CAAkBiI,IAAD,IAAUA,IAAI,CAACE,aAAhC,CADY,EAEZwB,CAAC,CAACrB,UAAF,CAAatI,GAAb,CAAkBiI,IAAD,IAAUA,IAAI,CAACE,aAAhC,CAFY,CAHpB;AAQD;AAED,MAAM0B,OAAO,GAAG,QAAhB;AACA,MAAMC,mBAAmB,GAAG,CAA5B;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,iBAAiB,GAAG,CAA1B;AACA,MAAMC,kBAAkB,GAAG,EAA3B;AACA,MAAMC,YAAY,GAAG,CAAC,CAAtB;AACA,MAAMC,OAAO,GAAIC,CAAD,IAAeA,CAAC,KAAK,GAArC;AAEA,SAAS3B,YAATA,CAAsB1G,IAAtB,EAAoC7B,KAApC,EAA8D;EAC5D,IAAI6I,QAAQ,GAAGhH,IAAI,CAACiH,KAAL,CAAW,GAAX,CAAf;EACA,IAAIqB,YAAY,GAAGtB,QAAQ,CAACxI,MAA5B;EACA,IAAIwI,QAAQ,CAACuB,IAAT,CAAcH,OAAd,CAAJ,EAA4B;IAC1BE,YAAY,IAAIH,YAAhB;EACD;EAED,IAAIhK,KAAJ,EAAW;IACTmK,YAAY,IAAIN,eAAhB;EACD;EAED,OAAOhB,QAAQ,CACZwB,MADI,CACIH,CAAD,IAAO,CAACD,OAAO,CAACC,CAAD,CADlB,CAEJ,CAAAI,MAFI,CAGH,CAAChC,KAAD,EAAQiC,OAAR,KACEjC,KAAK,IACJqB,OAAO,CAACa,IAAR,CAAaD,OAAb,IACGX,mBADH,GAEGW,OAAO,KAAK,EAAZ,GACAT,iBADA,GAEAC,kBALC,CAJJ,EAUHI,YAVG,CAAP;AAYD;AAED,SAAST,cAATA,CAAwBF,CAAxB,EAAqCC,CAArC,EAAgD;EAC9C,IAAIgB,QAAQ,GACVjB,CAAC,CAACnJ,MAAF,KAAaoJ,CAAC,CAACpJ,MAAf,IAAyBmJ,CAAC,CAACzF,KAAF,CAAQ,CAAR,EAAW,CAAC,CAAZ,CAAe,CAAA2G,KAAf,CAAqB,CAACjK,CAAD,EAAIgH,CAAJ,KAAUhH,CAAC,KAAKgJ,CAAC,CAAChC,CAAD,CAAtC,CAD3B;EAGA,OAAOgD,QAAQ;EAAA;EAEX;EACA;EACA;EACAjB,CAAC,CAACA,CAAC,CAACnJ,MAAF,GAAW,CAAZ,CAAD,GAAkBoJ,CAAC,CAACA,CAAC,CAACpJ,MAAF,GAAW,CAAZ,CALR;EAAA;EAOX;EACA,CARJ;AASD;AAED,SAASqH,gBAATA,CAIEiD,MAJF,EAKEzJ,QALF,EAKkB;EAEhB,IAAI;IAAEkH;EAAF,IAAiBuC,MAArB;EAEA,IAAIC,aAAa,GAAG,EAApB;EACA,IAAIC,eAAe,GAAG,GAAtB;EACA,IAAIrD,OAAO,GAAoD,EAA/D;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,UAAU,CAAC/H,MAA/B,EAAuC,EAAEoH,CAAzC,EAA4C;IAC1C,IAAIM,IAAI,GAAGK,UAAU,CAACX,CAAD,CAArB;IACA,IAAIqD,GAAG,GAAGrD,CAAC,KAAKW,UAAU,CAAC/H,MAAX,GAAoB,CAApC;IACA,IAAI0K,iBAAiB,GACnBF,eAAe,KAAK,GAApB,GACI3J,QADJ,GAEIA,QAAQ,CAAC6C,KAAT,CAAe8G,eAAe,CAACxK,MAA/B,KAA0C,GAHhD;IAIA,IAAI2K,KAAK,GAAGC,SAAS,CACnB;MAAEpJ,IAAI,EAAEkG,IAAI,CAACD,YAAb;MAA2BE,aAAa,EAAED,IAAI,CAACC,aAA/C;MAA8D8C;KAD3C,EAEnBC,iBAFmB,CAArB;IAKA,IAAI,CAACC,KAAL,EAAY,OAAO,IAAP;IAEZE,MAAM,CAACrF,MAAP,CAAc+E,aAAd,EAA6BI,KAAK,CAACG,MAAnC;IAEA,IAAIhF,KAAK,GAAG4B,IAAI,CAAC5B,KAAjB;IAEAqB,OAAO,CAACvF,IAAR,CAAa;MACX;MACAkJ,MAAM,EAAEP,aAFG;MAGX1J,QAAQ,EAAEiH,SAAS,CAAC,CAAC0C,eAAD,EAAkBG,KAAK,CAAC9J,QAAxB,CAAD,CAHR;MAIXkK,YAAY,EAAEC,iBAAiB,CAC7BlD,SAAS,CAAC,CAAC0C,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CADoB,CAJpB;MAOXjF;KAPF;IAUA,IAAI6E,KAAK,CAACI,YAAN,KAAuB,GAA3B,EAAgC;MAC9BP,eAAe,GAAG1C,SAAS,CAAC,CAAC0C,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CAA3B;IACD;EACF;EAED,OAAO5D,OAAP;AACD;AAED;;;;AAIG;;SACa8D,aACdC,YAAA,EACAJ,MAAA,EAEa;EAAA,IAFbA,MAEa;IAFbA,MAEa,GAAT,EAAS;EAAA;EAEb,IAAItJ,IAAI,GAAG0J,YAAX;EACA,IAAI1J,IAAI,CAACqH,QAAL,CAAc,GAAd,KAAsBrH,IAAI,KAAK,GAA/B,IAAsC,CAACA,IAAI,CAACqH,QAAL,CAAc,IAAd,CAA3C,EAAgE;IAC9D7E,OAAO,CACL,KADK,EAEL,eAAe,GAAAxC,IAAf,iDACMA,IAAI,CAACS,OAAL,CAAa,KAAb,EAAoB,IAApB,CADN,wJAGsCT,IAAI,CAACS,OAAL,CAAa,KAAb,EAAoB,IAApB,CAHtC,SAFK,CAAP;IAOAT,IAAI,GAAGA,IAAI,CAACS,OAAL,CAAa,KAAb,EAAoB,IAApB,CAAP;EACD;EAED,OACET,IAAI,CACDS,OADH,CAEI,eAFJ,EAGI,CAACkJ,CAAD,EAAIzK,GAAJ,EAA0B0K,QAA1B,KAA0D;IACxD,IAAIC,KAAK,GAAGP,MAAM,CAACpK,GAAD,CAAlB;IACA,IAAI0K,QAAQ,KAAK,GAAjB,EAAsB;MACpB,OAAOC,KAAK,IAAI,IAAT,GAAgB,EAAhB,GAAqBA,KAA5B;IACD;IACD,IAAIA,KAAK,IAAI,IAAb,EAAmB;MACjBzH,SAAS,CAAC,KAAD,EAAqB,gBAAAlD,GAArB,GAAT;IACD;IACD,OAAO2K,KAAP;GAXN,EAcGpJ,OAdH,CAeI,gBAfJ,EAgBI,CAACkJ,CAAD,EAAIzK,GAAJ,EAA0B0K,QAA1B,KAA0D;IACxD,IAAIC,KAAK,GAAGP,MAAM,CAACpK,GAAD,CAAlB;IACA,IAAI0K,QAAQ,KAAK,GAAjB,EAAsB;MACpB,OAAOC,KAAK,IAAI,IAAT,GAAgB,EAAhB,SAAyBA,KAAhC;IACD;IACD,IAAIA,KAAK,IAAI,IAAb,EAAmB;MACjBzH,SAAS,CAAC,KAAD,EAAqB,gBAAAlD,GAArB,GAAT;IACD;IACD,aAAW2K,KAAX;EACD,CAzBL,CA2BE;EAAA,CACCpJ,OA5BH,CA4BW,KA5BX,EA4BkB,EA5BlB,EA6BGA,OA7BH,CA6BW,SA7BX,EA6BsB,CAACkJ,CAAD,EAAIG,MAAJ,EAAYC,EAAZ,EAAgBC,GAAhB,KAAuB;IACzC,MAAMC,IAAI,GAAG,GAAb;IAEA,IAAIX,MAAM,CAACW,IAAD,CAAN,IAAgB,IAApB,EAA0B;MACxB;MACA;MACA,OAAOD,GAAG,KAAK,IAAR,GAAe,GAAf,GAAqB,EAA5B;IACD,CAPwC;;IAUzC,YAAUF,MAAV,GAAmBR,MAAM,CAACW,IAAD,CAAzB;EACD,CAxCH,CADF;AA2CD;AAiDD;;;;;AAKG;;AACa,SAAAb,UAIdc,OAJc,EAKd7K,QALc,EAKE;EAEhB,IAAI,OAAO6K,OAAP,KAAmB,QAAvB,EAAiC;IAC/BA,OAAO,GAAG;MAAElK,IAAI,EAAEkK,OAAR;MAAiB/D,aAAa,EAAE,KAAhC;MAAuC8C,GAAG,EAAE;KAAtD;EACD;EAED,IAAI,CAACkB,OAAD,EAAUC,UAAV,CAAwB,GAAAC,WAAW,CACrCH,OAAO,CAAClK,IAD6B,EAErCkK,OAAO,CAAC/D,aAF6B,EAGrC+D,OAAO,CAACjB,GAH6B,CAAvC;EAMA,IAAIE,KAAK,GAAG9J,QAAQ,CAAC8J,KAAT,CAAegB,OAAf,CAAZ;EACA,IAAI,CAAChB,KAAL,EAAY,OAAO,IAAP;EAEZ,IAAIH,eAAe,GAAGG,KAAK,CAAC,CAAD,CAA3B;EACA,IAAII,YAAY,GAAGP,eAAe,CAACvI,OAAhB,CAAwB,SAAxB,EAAmC,IAAnC,CAAnB;EACA,IAAI6J,aAAa,GAAGnB,KAAK,CAACjH,KAAN,CAAY,CAAZ,CAApB;EACA,IAAIoH,MAAM,GAAWc,UAAU,CAAC3B,MAAX,CACnB,CAAC8B,IAAD,EAAOC,SAAP,EAAkBrM,KAAlB,KAA2B;IACzB;IACA;IACA,IAAIqM,SAAS,KAAK,GAAlB,EAAuB;MACrB,IAAIC,UAAU,GAAGH,aAAa,CAACnM,KAAD,CAAb,IAAwB,EAAzC;MACAoL,YAAY,GAAGP,eAAe,CAC3B9G,KADY,CACN,CADM,EACH8G,eAAe,CAACxK,MAAhB,GAAyBiM,UAAU,CAACjM,MADjC,CAEZ,CAAAiC,OAFY,CAEJ,SAFI,EAEO,IAFP,CAAf;IAGD;IAED8J,IAAI,CAACC,SAAD,CAAJ,GAAkBE,wBAAwB,CACxCJ,aAAa,CAACnM,KAAD,CAAb,IAAwB,EADgB,EAExCqM,SAFwC,CAA1C;IAIA,OAAOD,IAAP;GAfiB,EAiBnB,EAjBmB,CAArB;EAoBA,OAAO;IACLjB,MADK;IAELjK,QAAQ,EAAE2J,eAFL;IAGLO,YAHK;IAILW;GAJF;AAMD;AAED,SAASG,WAATA,CACErK,IADF,EAEEmG,aAFF,EAGE8C,GAHF,EAGY;EAAA,IADV9C,aACU;IADVA,aACU,GADM,KACN;EAAA;EAAA,IAAV8C,GAAU;IAAVA,GAAU,GAAJ,IAAI;EAAA;EAEVzG,OAAO,CACLxC,IAAI,KAAK,GAAT,IAAgB,CAACA,IAAI,CAACqH,QAAL,CAAc,GAAd,CAAjB,IAAuCrH,IAAI,CAACqH,QAAL,CAAc,IAAd,CADlC,EAEL,eAAe,GAAArH,IAAf,iDACMA,IAAI,CAACS,OAAL,CAAa,KAAb,EAAoB,IAApB,CADN,wJAGsCT,IAAI,CAACS,OAAL,CAAa,KAAb,EAAoB,IAApB,CAHtC,SAFK,CAAP;EAQA,IAAI2J,UAAU,GAAa,EAA3B;EACA,IAAIO,YAAY,GACd,MACA3K,IAAI,CACDS,OADH,CACW,SADX,EACsB,EADtB,CAC0B;EAAA,CACvBA,OAFH,CAEW,MAFX,EAEmB,GAFnB,CAEwB;EAAA,CACrBA,OAHH,CAGW,qBAHX,EAGkC,MAHlC,CAG0C;EAAA,CACvCA,OAJH,CAIW,WAJX,EAIwB,CAACkJ,CAAD,EAAYa,SAAZ,KAAiC;IACrDJ,UAAU,CAAChK,IAAX,CAAgBoK,SAAhB;IACA,OAAO,YAAP;EACD,CAPH,CAFF;EAWA,IAAIxK,IAAI,CAACqH,QAAL,CAAc,GAAd,CAAJ,EAAwB;IACtB+C,UAAU,CAAChK,IAAX,CAAgB,GAAhB;IACAuK,YAAY,IACV3K,IAAI,KAAK,GAAT,IAAgBA,IAAI,KAAK,IAAzB,GACI,OADJ;IAAA,EAEI,mBAHN,CAFsB;GAAxB,MAMO,IAAIiJ,GAAJ,EAAS;IACd;IACA0B,YAAY,IAAI,OAAhB;GAFK,MAGA,IAAI3K,IAAI,KAAK,EAAT,IAAeA,IAAI,KAAK,GAA5B,EAAiC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA2K,YAAY,IAAI,eAAhB;EACD,CATM,MASA;EAIP,IAAIR,OAAO,GAAG,IAAIS,MAAJ,CAAWD,YAAX,EAAyBxE,aAAa,GAAG7H,SAAH,GAAe,GAArD,CAAd;EAEA,OAAO,CAAC6L,OAAD,EAAUC,UAAV,CAAP;AACD;AAED,SAAStE,eAATA,CAAyBzD,KAAzB,EAAsC;EACpC,IAAI;IACF,OAAOwI,SAAS,CAACxI,KAAD,CAAhB;GADF,CAEE,OAAO0B,KAAP,EAAc;IACdvB,OAAO,CACL,KADK,EAEL,oBAAiBH,KAAjB,GAEe,kIAAA0B,KAFf,QAFK,CAAP;IAOA,OAAO1B,KAAP;EACD;AACF;AAED,SAASqI,wBAATA,CAAkCrI,KAAlC,EAAiDmI,SAAjD,EAAkE;EAChE,IAAI;IACF,OAAOM,kBAAkB,CAACzI,KAAD,CAAzB;GADF,CAEE,OAAO0B,KAAP,EAAc;IACdvB,OAAO,CACL,KADK,EAEL,gCAAgC,GAAAgI,SAAhC,0DACkBnI,KADlB,8FAEqC0B,KAFrC,QAFK,CAAP;IAOA,OAAO1B,KAAP;EACD;AACF;AAED;;AAEG;;AACa,SAAAkD,cACdlG,QADc,EAEdiG,QAFc,EAEE;EAEhB,IAAIA,QAAQ,KAAK,GAAjB,EAAsB,OAAOjG,QAAP;EAEtB,IAAI,CAACA,QAAQ,CAAC0L,WAAT,EAAuB,CAAA1E,UAAvB,CAAkCf,QAAQ,CAACyF,WAAT,EAAlC,CAAL,EAAgE;IAC9D,OAAO,IAAP;EACD,CANe;EAShB;;EACA,IAAIC,UAAU,GAAG1F,QAAQ,CAAC+B,QAAT,CAAkB,GAAlB,IACb/B,QAAQ,CAAC9G,MAAT,GAAkB,CADL,GAEb8G,QAAQ,CAAC9G,MAFb;EAGA,IAAIyM,QAAQ,GAAG5L,QAAQ,CAACE,MAAT,CAAgByL,UAAhB,CAAf;EACA,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAA7B,EAAkC;IAChC;IACA,OAAO,IAAP;EACD;EAED,OAAO5L,QAAQ,CAAC6C,KAAT,CAAe8I,UAAf,KAA8B,GAArC;AACD;AAED;;AAEG;;AACa,SAAAxI,QAAQC,IAAR,EAAmBH,OAAnB,EAAkC;EAChD,IAAI,CAACG,IAAL,EAAW;IACT;IACA,IAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAaL,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIC,KAAJ,CAAUD,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOM,CAAP,EAAU;EACb;AACF;AAED;;;;AAIG;;SACasI,YAAYjM,EAAA,EAAQkM,YAAA,EAAkB;EAAA,IAAlBA,YAAkB;IAAlBA,YAAkB,GAAH,GAAG;EAAA;EACpD,IAAI;IACF9L,QAAQ,EAAE+L,UADR;IAEFlL,MAAM,GAAG,EAFP;IAGFC,IAAI,GAAG;GACL,UAAOlB,EAAP,KAAc,QAAd,GAAyBgB,SAAS,CAAChB,EAAD,CAAlC,GAAyCA,EAJ7C;EAMA,IAAII,QAAQ,GAAG+L,UAAU,GACrBA,UAAU,CAAC/E,UAAX,CAAsB,GAAtB,IACE+E,UADF,GAEEC,eAAe,CAACD,UAAD,EAAaD,YAAb,CAHI,GAIrBA,YAJJ;EAMA,OAAO;IACL9L,QADK;IAELa,MAAM,EAAEoL,eAAe,CAACpL,MAAD,CAFlB;IAGLC,IAAI,EAAEoL,aAAa,CAACpL,IAAD;GAHrB;AAKD;AAED,SAASkL,eAATA,CAAyBpF,YAAzB,EAA+CkF,YAA/C,EAAmE;EACjE,IAAInE,QAAQ,GAAGmE,YAAY,CAAC1K,OAAb,CAAqB,MAArB,EAA6B,EAA7B,EAAiCwG,KAAjC,CAAuC,GAAvC,CAAf;EACA,IAAIuE,gBAAgB,GAAGvF,YAAY,CAACgB,KAAb,CAAmB,GAAnB,CAAvB;EAEAuE,gBAAgB,CAAC7E,OAAjB,CAA0B+B,OAAD,IAAY;IACnC,IAAIA,OAAO,KAAK,IAAhB,EAAsB;MACpB;MACA,IAAI1B,QAAQ,CAACxI,MAAT,GAAkB,CAAtB,EAAyBwI,QAAQ,CAACyE,GAAT;IAC1B,CAHD,MAGO,IAAI/C,OAAO,KAAK,GAAhB,EAAqB;MAC1B1B,QAAQ,CAAC5G,IAAT,CAAcsI,OAAd;IACD;GANH;EASA,OAAO1B,QAAQ,CAACxI,MAAT,GAAkB,CAAlB,GAAsBwI,QAAQ,CAAClC,IAAT,CAAc,GAAd,CAAtB,GAA2C,GAAlD;AACD;AAED,SAAS4G,mBAATA,CACEC,IADF,EAEEC,KAFF,EAGEC,IAHF,EAIE7L,IAJF,EAIqB;EAEnB,OACE,oBAAqB,GAAA2L,IAArB,GACQ,mDAAAC,KADR,GAC0B,cAAApM,IAAI,CAACC,SAAL,CACxBO,IADwB,CAD1B,qDAIQ6L,IAJR,GADF;AAQD;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;;AACG,SAAUC,0BAAVA,CAEJnG,OAFI,EAEQ;EACZ,OAAOA,OAAO,CAAC6C,MAAR,CACL,CAACW,KAAD,EAAQhL,KAAR,KACEA,KAAK,KAAK,CAAV,IAAgBgL,KAAK,CAAC7E,KAAN,CAAYtE,IAAZ,IAAoBmJ,KAAK,CAAC7E,KAAN,CAAYtE,IAAZ,CAAiBxB,MAAjB,GAA0B,CAF3D,CAAP;AAID;AAED;;AAEG;;AACG,SAAUuN,SAAVA,CACJC,KADI,EAEJC,cAFI,EAGJC,gBAHI,EAIJC,cAJI,EAIkB;EAAA,IAAtBA,cAAsB;IAAtBA,cAAsB,GAAL,KAAK;EAAA;EAEtB,IAAIlN,EAAJ;EACA,IAAI,OAAO+M,KAAP,KAAiB,QAArB,EAA+B;IAC7B/M,EAAE,GAAGgB,SAAS,CAAC+L,KAAD,CAAd;EACD,CAFD,MAEO;IACL/M,EAAE,GAAAkE,QAAA,KAAQ6I,KAAR,CAAF;IAEA5J,SAAS,CACP,CAACnD,EAAE,CAACI,QAAJ,IAAgB,CAACJ,EAAE,CAACI,QAAH,CAAYwH,QAAZ,CAAqB,GAArB,CADV,EAEP6E,mBAAmB,CAAC,GAAD,EAAM,UAAN,EAAkB,QAAlB,EAA4BzM,EAA5B,CAFZ,CAAT;IAIAmD,SAAS,CACP,CAACnD,EAAE,CAACI,QAAJ,IAAgB,CAACJ,EAAE,CAACI,QAAH,CAAYwH,QAAZ,CAAqB,GAArB,CADV,EAEP6E,mBAAmB,CAAC,GAAD,EAAM,UAAN,EAAkB,MAAlB,EAA0BzM,EAA1B,CAFZ,CAAT;IAIAmD,SAAS,CACP,CAACnD,EAAE,CAACiB,MAAJ,IAAc,CAACjB,EAAE,CAACiB,MAAH,CAAU2G,QAAV,CAAmB,GAAnB,CADR,EAEP6E,mBAAmB,CAAC,GAAD,EAAM,QAAN,EAAgB,MAAhB,EAAwBzM,EAAxB,CAFZ,CAAT;EAID;EAED,IAAImN,WAAW,GAAGJ,KAAK,KAAK,EAAV,IAAgB/M,EAAE,CAACI,QAAH,KAAgB,EAAlD;EACA,IAAI+L,UAAU,GAAGgB,WAAW,GAAG,GAAH,GAASnN,EAAE,CAACI,QAAxC;EAEA,IAAIgN,IAAJ,CAzBsB;EA4BtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIF,cAAc,IAAIf,UAAU,IAAI,IAApC,EAA0C;IACxCiB,IAAI,GAAGH,gBAAP;EACD,CAFD,MAEO;IACL,IAAII,kBAAkB,GAAGL,cAAc,CAACzN,MAAf,GAAwB,CAAjD;IAEA,IAAI4M,UAAU,CAAC/E,UAAX,CAAsB,IAAtB,CAAJ,EAAiC;MAC/B,IAAIkG,UAAU,GAAGnB,UAAU,CAACnE,KAAX,CAAiB,GAAjB,CAAjB,CAD+B;MAI/B;MACA;;MACA,OAAOsF,UAAU,CAAC,CAAD,CAAV,KAAkB,IAAzB,EAA+B;QAC7BA,UAAU,CAACC,KAAX;QACAF,kBAAkB,IAAI,CAAtB;MACD;MAEDrN,EAAE,CAACI,QAAH,GAAckN,UAAU,CAACzH,IAAX,CAAgB,GAAhB,CAAd;IACD,CAfI;IAkBL;;IACAuH,IAAI,GAAGC,kBAAkB,IAAI,CAAtB,GAA0BL,cAAc,CAACK,kBAAD,CAAxC,GAA+D,GAAtE;EACD;EAED,IAAItM,IAAI,GAAGkL,WAAW,CAACjM,EAAD,EAAKoN,IAAL,CAAtB,CA5DsB;;EA+DtB,IAAII,wBAAwB,GAC1BrB,UAAU,IAAIA,UAAU,KAAK,GAA7B,IAAoCA,UAAU,CAAC/D,QAAX,CAAoB,GAApB,CADtC,CA/DsB;;EAkEtB,IAAIqF,uBAAuB,GACzB,CAACN,WAAW,IAAIhB,UAAU,KAAK,GAA/B,KAAuCc,gBAAgB,CAAC7E,QAAjB,CAA0B,GAA1B,CADzC;EAEA,IACE,CAACrH,IAAI,CAACX,QAAL,CAAcgI,QAAd,CAAuB,GAAvB,CAAD,KACCoF,wBAAwB,IAAIC,uBAD7B,CADF,EAGE;IACA1M,IAAI,CAACX,QAAL,IAAiB,GAAjB;EACD;EAED,OAAOW,IAAP;AACD;AAED;;AAEG;;AACG,SAAU2M,aAAVA,CAAwB1N,EAAxB,EAA8B;EAClC;EACA,OAAOA,EAAE,KAAK,EAAP,IAAcA,EAAW,CAACI,QAAZ,KAAyB,EAAvC,GACH,GADG,GAEH,OAAOJ,EAAP,KAAc,QAAd,GACAgB,SAAS,CAAChB,EAAD,CAAT,CAAcI,QADd,GAEAJ,EAAE,CAACI,QAJP;AAKD;AAED;;AAEG;;MACUiH,SAAS,GAAIsG,KAAD,IACvBA,KAAK,CAAC9H,IAAN,CAAW,GAAX,EAAgBrE,OAAhB,CAAwB,QAAxB,EAAkC,GAAlC;AAEF;;AAEG;;MACU+I,iBAAiB,GAAInK,QAAD,IAC/BA,QAAQ,CAACoB,OAAT,CAAiB,MAAjB,EAAyB,EAAzB,CAA6B,CAAAA,OAA7B,CAAqC,MAArC,EAA6C,GAA7C;AAEF;;AAEG;;AACI,MAAM6K,eAAe,GAAIpL,MAAD,IAC7B,CAACA,MAAD,IAAWA,MAAM,KAAK,GAAtB,GACI,EADJ,GAEIA,MAAM,CAACmG,UAAP,CAAkB,GAAlB,CACA,GAAAnG,MADA,GAEA,MAAMA,MALL;AAOP;;AAEG;;AACI,MAAMqL,aAAa,GAAIpL,IAAD,IAC3B,CAACA,IAAD,IAASA,IAAI,KAAK,GAAlB,GAAwB,EAAxB,GAA6BA,IAAI,CAACkG,UAAL,CAAgB,GAAhB,CAAuB,GAAAlG,IAAvB,GAA8B,MAAMA,IAD5D;AAQP;;;AAGG;;AACI,MAAM0M,IAAI,GAAiB,SAArBA,IAAqBA,CAACC,IAAD,EAAOC,IAAP,EAAoB;EAAA,IAAbA,IAAa;IAAbA,IAAa,GAAN,EAAM;EAAA;EACpD,IAAIC,YAAY,GAAG,OAAOD,IAAP,KAAgB,QAAhB,GAA2B;IAAEE,MAAM,EAAEF;EAAV,CAA3B,GAA8CA,IAAjE;EAEA,IAAIG,OAAO,GAAG,IAAIC,OAAJ,CAAYH,YAAY,CAACE,OAAzB,CAAd;EACA,IAAI,CAACA,OAAO,CAAClI,GAAR,CAAY,cAAZ,CAAL,EAAkC;IAChCkI,OAAO,CAACE,GAAR,CAAY,cAAZ,EAA4B,iCAA5B;EACD;EAED,OAAO,IAAIC,QAAJ,CAAa7N,IAAI,CAACC,SAAL,CAAeqN,IAAf,CAAb,EAAA3J,QAAA,KACF6J,YADE;IAELE;GAFF;AAID;AAQK,MAAOI,oBAAP,SAAoC/K,KAApC,CAAyC;MAElCgL,YAAA,CAAY;EAWvBC,WAAYA,CAAAV,IAAA,EAA+BE,YAA/B,EAA0D;IAV9D,KAAAS,cAAA,GAA8B,IAAI9I,GAAJ,EAA9B;IAIA,KAAA+I,WAAA,GACN,IAAI/I,GAAJ,EADM;IAIR,IAAY,CAAAgJ,YAAZ,GAAyB,EAAzB;IAGEvL,SAAS,CACP0K,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,IAAoC,CAACc,KAAK,CAACC,OAAN,CAAcf,IAAd,CAD9B,EAEP,oCAFO,CAAT,CADoE;IAOpE;;IACA,IAAIgB,MAAJ;IACA,KAAKC,YAAL,GAAoB,IAAIC,OAAJ,CAAY,CAACrE,CAAD,EAAIsE,CAAJ,KAAWH,MAAM,GAAGG,CAAhC,CAApB;IACA,KAAKC,UAAL,GAAkB,IAAIC,eAAJ,EAAlB;IACA,IAAIC,OAAO,GAAGA,CAAA,KACZN,MAAM,CAAC,IAAIR,oBAAJ,CAAyB,uBAAzB,CAAD,CADR;IAEA,KAAKe,mBAAL,GAA2B,MACzB,KAAKH,UAAL,CAAgBI,MAAhB,CAAuBnK,mBAAvB,CAA2C,OAA3C,EAAoDiK,OAApD,CADF;IAEA,IAAK,CAAAF,UAAL,CAAgBI,MAAhB,CAAuBpK,gBAAvB,CAAwC,OAAxC,EAAiDkK,OAAjD;IAEA,IAAK,CAAAtB,IAAL,GAAYzD,MAAM,CAACrL,OAAP,CAAe8O,IAAf,CAAqB,CAAArE,MAArB,CACV,CAAC8F,GAAD,EAAAnL,IAAA;MAAA,IAAM,CAAClE,GAAD,EAAMmD,KAAN,CAAN,GAAAe,IAAA;MAAA,OACEiG,MAAM,CAACrF,MAAP,CAAcuK,GAAd,EAAmB;QACjB,CAACrP,GAAD,GAAO,KAAKsP,YAAL,CAAkBtP,GAAlB,EAAuBmD,KAAvB;MADU,CAAnB,CADF;KADU,EAKV,EALU,CAAZ;IAQA,IAAI,KAAKoM,IAAT,EAAe;MACb;MACA,KAAKJ,mBAAL;IACD;IAED,IAAK,CAAAtB,IAAL,GAAYC,YAAZ;EACD;EAEOwB,YAAYA,CAClBtP,GADkB,EAElBmD,KAFkB,EAEe;IAEjC,IAAI,EAAEA,KAAK,YAAY2L,OAAnB,CAAJ,EAAiC;MAC/B,OAAO3L,KAAP;IACD;IAED,KAAKsL,YAAL,CAAkBvN,IAAlB,CAAuBlB,GAAvB;IACA,KAAKuO,cAAL,CAAoBxI,GAApB,CAAwB/F,GAAxB,EAPiC;IAUjC;;IACA,IAAIwP,OAAO,GAAmBV,OAAO,CAACW,IAAR,CAAa,CAACtM,KAAD,EAAQ,KAAK0L,YAAb,CAAb,EAAyCa,IAAzC,CAC3B9B,IAAD,IAAU,KAAK+B,QAAL,CAAcH,OAAd,EAAuBxP,GAAvB,EAA4B,IAA5B,EAAkC4N,IAAlC,CADkB,EAE3B/I,KAAD,IAAW,KAAK8K,QAAL,CAAcH,OAAd,EAAuBxP,GAAvB,EAA4B6E,KAA5B,CAFiB,CAA9B,CAXiC;IAiBjC;;IACA2K,OAAO,CAACI,KAAR,CAAc,MAAO,EAArB;IAEAzF,MAAM,CAAC0F,cAAP,CAAsBL,OAAtB,EAA+B,UAA/B,EAA2C;MAAEM,GAAG,EAAEA,CAAA,KAAM;KAAxD;IACA,OAAON,OAAP;EACD;EAEOG,QAAQA,CACdH,OADc,EAEdxP,GAFc,EAGd6E,KAHc,EAId+I,IAJc,EAIA;IAEd,IACE,KAAKoB,UAAL,CAAgBI,MAAhB,CAAuBW,OAAvB,IACAlL,KAAK,YAAYuJ,oBAFnB,EAGE;MACA,KAAKe,mBAAL;MACAhF,MAAM,CAAC0F,cAAP,CAAsBL,OAAtB,EAA+B,QAA/B,EAAyC;QAAEM,GAAG,EAAEA,CAAA,KAAMjL;OAAtD;MACA,OAAOiK,OAAO,CAACF,MAAR,CAAe/J,KAAf,CAAP;IACD;IAED,KAAK0J,cAAL,CAAoByB,MAApB,CAA2BhQ,GAA3B;IAEA,IAAI,KAAKuP,IAAT,EAAe;MACb;MACA,KAAKJ,mBAAL;IACD;IAED,IAAItK,KAAJ,EAAW;MACTsF,MAAM,CAAC0F,cAAP,CAAsBL,OAAtB,EAA+B,QAA/B,EAAyC;QAAEM,GAAG,EAAEA,CAAA,KAAMjL;OAAtD;MACA,KAAKoL,IAAL,CAAU,KAAV,EAAiBjQ,GAAjB;MACA,OAAO8O,OAAO,CAACF,MAAR,CAAe/J,KAAf,CAAP;IACD;IAEDsF,MAAM,CAAC0F,cAAP,CAAsBL,OAAtB,EAA+B,OAA/B,EAAwC;MAAEM,GAAG,EAAEA,CAAA,KAAMlC;KAArD;IACA,KAAKqC,IAAL,CAAU,KAAV,EAAiBjQ,GAAjB;IACA,OAAO4N,IAAP;EACD;EAEOqC,IAAIA,CAACF,OAAD,EAAmBG,UAAnB,EAAsC;IAChD,IAAK,CAAA1B,WAAL,CAAiB/G,OAAjB,CAA0B0I,UAAD,IAAgBA,UAAU,CAACJ,OAAD,EAAUG,UAAV,CAAnD;EACD;EAEDE,SAASA,CAACxO,EAAD,EAAoD;IAC3D,KAAK4M,WAAL,CAAiBzI,GAAjB,CAAqBnE,EAArB;IACA,OAAO,MAAM,IAAK,CAAA4M,WAAL,CAAiBwB,MAAjB,CAAwBpO,EAAxB,CAAb;EACD;EAEDyO,MAAMA,CAAA;IACJ,IAAK,CAAArB,UAAL,CAAgBsB,KAAhB;IACA,KAAK/B,cAAL,CAAoB9G,OAApB,CAA4B,CAAC8I,CAAD,EAAIC,CAAJ,KAAU,KAAKjC,cAAL,CAAoByB,MAApB,CAA2BQ,CAA3B,CAAtC;IACA,IAAK,CAAAP,IAAL,CAAU,IAAV;EACD;EAEgB,MAAXQ,WAAWA,CAACrB,MAAD,EAAoB;IACnC,IAAIW,OAAO,GAAG,KAAd;IACA,IAAI,CAAC,IAAK,CAAAR,IAAV,EAAgB;MACd,IAAIL,OAAO,GAAGA,CAAA,KAAM,KAAKmB,MAAL,EAApB;MACAjB,MAAM,CAACpK,gBAAP,CAAwB,OAAxB,EAAiCkK,OAAjC;MACAa,OAAO,GAAG,MAAM,IAAIjB,OAAJ,CAAa4B,OAAD,IAAY;QACtC,IAAK,CAAAN,SAAL,CAAgBL,OAAD,IAAY;UACzBX,MAAM,CAACnK,mBAAP,CAA2B,OAA3B,EAAoCiK,OAApC;UACA,IAAIa,OAAO,IAAI,IAAK,CAAAR,IAApB,EAA0B;YACxBmB,OAAO,CAACX,OAAD,CAAP;UACD;SAJH;MAMD,CAPe,CAAhB;IAQD;IACD,OAAOA,OAAP;EACD;EAEO,IAAJR,IAAIA,CAAA;IACN,OAAO,IAAK,CAAAhB,cAAL,CAAoBoC,IAApB,KAA6B,CAApC;EACD;EAEgB,IAAbC,aAAaA,CAAA;IACf1N,SAAS,CACP,IAAK,CAAA0K,IAAL,KAAc,IAAd,IAAsB,IAAK,CAAA2B,IADpB,EAEP,2DAFO,CAAT;IAKA,OAAOpF,MAAM,CAACrL,OAAP,CAAe,KAAK8O,IAApB,CAA0B,CAAArE,MAA1B,CACL,CAAC8F,GAAD,EAAAwB,KAAA;MAAA,IAAM,CAAC7Q,GAAD,EAAMmD,KAAN,CAAN,GAAA0N,KAAA;MAAA,OACE1G,MAAM,CAACrF,MAAP,CAAcuK,GAAd,EAAmB;QACjB,CAACrP,GAAD,GAAO8Q,oBAAoB,CAAC3N,KAAD;MADV,CAAnB,CADF;KADK,EAKL,EALK,CAAP;EAOD;EAEc,IAAX4N,WAAWA,CAAA;IACb,OAAOrC,KAAK,CAACvB,IAAN,CAAW,KAAKoB,cAAhB,CAAP;EACD;AA5JsB;AA+JzB,SAASyC,gBAATA,CAA0B7N,KAA1B,EAAoC;EAClC,OACEA,KAAK,YAAY2L,OAAjB,IAA6B3L,KAAwB,CAAC8N,QAAzB,KAAsC,IADrE;AAGD;AAED,SAASH,oBAATA,CAA8B3N,KAA9B,EAAwC;EACtC,IAAI,CAAC6N,gBAAgB,CAAC7N,KAAD,CAArB,EAA8B;IAC5B,OAAOA,KAAP;EACD;EAED,IAAIA,KAAK,CAAC+N,MAAV,EAAkB;IAChB,MAAM/N,KAAK,CAAC+N,MAAZ;EACD;EACD,OAAO/N,KAAK,CAACgO,KAAb;AACD;AAOM,MAAMC,KAAK,GAAkB,SAAvBA,KAAuBA,CAACxD,IAAD,EAAOC,IAAP,EAAoB;EAAA,IAAbA,IAAa;IAAbA,IAAa,GAAN,EAAM;EAAA;EACtD,IAAIC,YAAY,GAAG,OAAOD,IAAP,KAAgB,QAAhB,GAA2B;IAAEE,MAAM,EAAEF;EAAV,CAA3B,GAA8CA,IAAjE;EAEA,OAAO,IAAIQ,YAAJ,CAAiBT,IAAjB,EAAuBE,YAAvB,CAAP;AACD;AAOD;;;AAGG;;AACI,MAAMuD,QAAQ,GAAqB,SAA7BA,QAA6BA,CAACxO,GAAD,EAAMgL,IAAN,EAAoB;EAAA,IAAdA,IAAc;IAAdA,IAAc,GAAP,GAAO;EAAA;EAC5D,IAAIC,YAAY,GAAGD,IAAnB;EACA,IAAI,OAAOC,YAAP,KAAwB,QAA5B,EAAsC;IACpCA,YAAY,GAAG;MAAEC,MAAM,EAAED;KAAzB;GADF,MAEO,IAAI,OAAOA,YAAY,CAACC,MAApB,KAA+B,WAAnC,EAAgD;IACrDD,YAAY,CAACC,MAAb,GAAsB,GAAtB;EACD;EAED,IAAIC,OAAO,GAAG,IAAIC,OAAJ,CAAYH,YAAY,CAACE,OAAzB,CAAd;EACAA,OAAO,CAACE,GAAR,CAAY,UAAZ,EAAwBrL,GAAxB;EAEA,OAAO,IAAIsL,QAAJ,CAAa,IAAb,EAAAlK,QAAA,KACF6J,YADE;IAELE;GAFF;AAID;AAED;;;AAGG;;MACUsD,aAAA,CAAa;EAOxBhD,WACEA,CAAAP,MAAA,EACAwD,UADA,EAEA3D,IAFA,EAGA4D,QAHA,EAGgB;IAAA,IAAhBA,QAAgB;MAAhBA,QAAgB,GAAL,KAAK;IAAA;IAEhB,IAAK,CAAAzD,MAAL,GAAcA,MAAd;IACA,KAAKwD,UAAL,GAAkBA,UAAU,IAAI,EAAhC;IACA,IAAK,CAAAC,QAAL,GAAgBA,QAAhB;IACA,IAAI5D,IAAI,YAAYvK,KAApB,EAA2B;MACzB,KAAKuK,IAAL,GAAYA,IAAI,CAAC/J,QAAL,EAAZ;MACA,IAAK,CAAAgB,KAAL,GAAa+I,IAAb;IACD,CAHD,MAGO;MACL,IAAK,CAAAA,IAAL,GAAYA,IAAZ;IACD;EACF;AAtBuB;AAyB1B;;;AAGG;;AACG,SAAU6D,oBAAVA,CAA+B5M,KAA/B,EAAyC;EAC7C,OACEA,KAAK,IAAI,IAAT,IACA,OAAOA,KAAK,CAACkJ,MAAb,KAAwB,QADxB,IAEA,OAAOlJ,KAAK,CAAC0M,UAAb,KAA4B,QAF5B,IAGA,OAAO1M,KAAK,CAAC2M,QAAb,KAA0B,SAH1B,IAIA,UAAU3M,KALZ;AAOD;ACj0BD,MAAM6M,uBAAuB,GAAyB,CACpD,MADoD,EAEpD,KAFoD,EAGpD,OAHoD,EAIpD,QAJoD,CAAtD;AAMA,MAAMC,oBAAoB,GAAG,IAAIlM,GAAJ,CAC3BiM,uBAD2B,CAA7B;AAIA,MAAME,sBAAsB,GAAiB,CAC3C,KAD2C,EAE3C,GAAGF,uBAFwC,CAA7C;AAIA,MAAMG,mBAAmB,GAAG,IAAIpM,GAAJ,CAAoBmM,sBAApB,CAA5B;AAEA,MAAME,mBAAmB,GAAG,IAAIrM,GAAJ,CAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,CAAR,CAA5B;AACA,MAAMsM,iCAAiC,GAAG,IAAItM,GAAJ,CAAQ,CAAC,GAAD,EAAM,GAAN,CAAR,CAA1C;AAEO,MAAMuM,eAAe,GAA6B;EACvD7S,KAAK,EAAE,MADgD;EAEvDc,QAAQ,EAAEb,SAF6C;EAGvD6S,UAAU,EAAE7S,SAH2C;EAIvD8S,UAAU,EAAE9S,SAJ2C;EAKvD+S,WAAW,EAAE/S,SAL0C;EAMvDgT,QAAQ,EAAEhT;AAN6C;AASlD,MAAMiT,YAAY,GAA0B;EACjDlT,KAAK,EAAE,MAD0C;EAEjDyO,IAAI,EAAExO,SAF2C;EAGjD6S,UAAU,EAAE7S,SAHqC;EAIjD8S,UAAU,EAAE9S,SAJqC;EAKjD+S,WAAW,EAAE/S,SALoC;EAMjDgT,QAAQ,EAAEhT;AANuC;AAS5C,MAAMkT,YAAY,GAAqB;EAC5CnT,KAAK,EAAE,WADqC;EAE5CoT,OAAO,EAAEnT,SAFmC;EAG5CoT,KAAK,EAAEpT,SAHqC;EAI5Ca,QAAQ,EAAEb;AAJkC;AAO9C,MAAMqT,SAAS,GACb,OAAO1Q,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACU,QAAd,KAA2B,WAD3B,IAEA,OAAOV,MAAM,CAACU,QAAP,CAAgBiQ,aAAvB,KAAyC,WAH3C;AAIA,MAAMC,QAAQ,GAAG,CAACF,SAAlB;AAGA;AACA;AACA;;AAEA;;AAEG;;AACG,SAAUG,YAAVA,CAAuB/E,IAAvB,EAAuC;EAC3C3K,SAAS,CACP2K,IAAI,CAACvI,MAAL,CAAYhG,MAAZ,GAAqB,CADd,EAEP,2DAFO,CAAT;EAKA,IAAIuT,UAAU,GAAGxN,yBAAyB,CAACwI,IAAI,CAACvI,MAAN,CAA1C,CAN2C;;EAQ3C,IAAIwN,eAAe,GAAwB,IAA3C,CAR2C;;EAU3C,IAAItE,WAAW,GAAG,IAAI/I,GAAJ,EAAlB,CAV2C;;EAY3C,IAAIsN,oBAAoB,GAAkC,IAA1D,CAZ2C;;EAc3C,IAAIC,uBAAuB,GAA2C,IAAtE,CAd2C;;EAgB3C,IAAIC,iBAAiB,GAAqC,IAA1D,CAhB2C;EAkB3C;EACA;EACA;EACA;EACA;;EACA,IAAIC,qBAAqB,GAAGrF,IAAI,CAACsF,aAAL,IAAsB,IAAlD;EAEA,IAAIC,cAAc,GAAGlN,WAAW,CAC9B2M,UAD8B,EAE9BhF,IAAI,CAACnN,OAAL,CAAaT,QAFiB,EAG9B4N,IAAI,CAACzH,QAHyB,CAAhC;EAKA,IAAIiN,aAAa,GAAqB,IAAtC;EAEA,IAAID,cAAc,IAAI,IAAtB,EAA4B;IAC1B;IACA;IACA,IAAIvO,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;MACtCnT,QAAQ,EAAE0N,IAAI,CAACnN,OAAL,CAAaT,QAAb,CAAsBE;IADM,CAAN,CAAlC;IAGA,IAAI;MAAEsG,OAAF;MAAWrB;KAAU,GAAAmO,sBAAsB,CAACV,UAAD,CAA/C;IACAO,cAAc,GAAG3M,OAAjB;IACA4M,aAAa,GAAG;MAAE,CAACjO,KAAK,CAACO,EAAP,GAAYd;KAA9B;EACD;EAED,IAAI2O,WAAW,GACb,CAACJ,cAAc,CAAC/J,IAAf,CAAqBoK,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQsO,MAAnC,CAAD,IAA+C7F,IAAI,CAACsF,aAAL,IAAsB,IADvE;EAGA,IAAIQ,MAAJ;EACA,IAAIxU,KAAK,GAAgB;IACvByU,aAAa,EAAE/F,IAAI,CAACnN,OAAL,CAAanB,MADL;IAEvBU,QAAQ,EAAE4N,IAAI,CAACnN,OAAL,CAAaT,QAFA;IAGvBwG,OAAO,EAAE2M,cAHc;IAIvBI,WAJuB;IAKvBK,UAAU,EAAE7B,eALW;IAMvB;IACA8B,qBAAqB,EAAEjG,IAAI,CAACsF,aAAL,IAAsB,IAAtB,GAA6B,KAA7B,GAAqC,IAPrC;IAQvBY,kBAAkB,EAAE,KARG;IASvBC,YAAY,EAAE,MATS;IAUvBC,UAAU,EAAGpG,IAAI,CAACsF,aAAL,IAAsBtF,IAAI,CAACsF,aAAL,CAAmBc,UAA1C,IAAyD,EAV9C;IAWvBC,UAAU,EAAGrG,IAAI,CAACsF,aAAL,IAAsBtF,IAAI,CAACsF,aAAL,CAAmBe,UAA1C,IAAyD,IAX9C;IAYvBC,MAAM,EAAGtG,IAAI,CAACsF,aAAL,IAAsBtF,IAAI,CAACsF,aAAL,CAAmBgB,MAA1C,IAAqDd,aAZtC;IAavBe,QAAQ,EAAE,IAAIC,GAAJ,EAba;IAcvBC,QAAQ,EAAE,IAAID,GAAJ;EAda,CAAzB,CA/C2C;EAiE3C;;EACA,IAAIE,aAAa,GAAkBhW,MAAa,CAACiB,GAAjD,CAlE2C;EAqE3C;;EACA,IAAIgV,yBAAyB,GAAG,KAAhC,CAtE2C;;EAyE3C,IAAIC,2BAAJ,CAzE2C;EA4E3C;;EACA,IAAIC,2BAA2B,GAAG,KAAlC,CA7E2C;EAgF3C;EACA;EACA;;EACA,IAAIC,sBAAsB,GAAG,KAA7B,CAnF2C;EAsF3C;;EACA,IAAIC,uBAAuB,GAAa,EAAxC,CAvF2C;EA0F3C;;EACA,IAAIC,qBAAqB,GAAa,EAAtC,CA3F2C;;EA8F3C,IAAIC,gBAAgB,GAAG,IAAIT,GAAJ,EAAvB,CA9F2C;;EAiG3C,IAAIU,kBAAkB,GAAG,CAAzB,CAjG2C;EAoG3C;EACA;;EACA,IAAIC,uBAAuB,GAAG,CAAC,CAA/B,CAtG2C;;EAyG3C,IAAIC,cAAc,GAAG,IAAIZ,GAAJ,EAArB,CAzG2C;;EA4G3C,IAAIa,gBAAgB,GAAG,IAAIzP,GAAJ,EAAvB,CA5G2C;;EA+G3C,IAAI0P,gBAAgB,GAAG,IAAId,GAAJ,EAAvB,CA/G2C;EAkH3C;EACA;EACA;;EACA,IAAIe,eAAe,GAAG,IAAIf,GAAJ,EAAtB,CArH2C;EAwH3C;;EACA,IAAIgB,aAAa,GAAkB,IAAnC,CAzH2C;EA4H3C;;EACA,IAAIC,gBAAgB,GAAG,IAAIjB,GAAJ,EAAvB,CA7H2C;EAgI3C;;EACA,IAAIkB,uBAAuB,GAAG,KAA9B,CAjI2C;EAoI3C;EACA;;EACA,SAASC,UAATA,CAAA,EAAmB;IACjB;IACA;IACA1C,eAAe,GAAGjF,IAAI,CAACnN,OAAL,CAAaiB,MAAb,CAChBuC,IAA+C;MAAA,IAA9C;QAAE3E,MAAM,EAAEqU,aAAV;QAAyB3T,QAAzB;QAAmCqB;OAAW,GAAA4C,IAAA;;MAC7C;MACA;MACA,IAAIqR,uBAAJ,EAA6B;QAC3BA,uBAAuB,GAAG,KAA1B;QACA;MACD;MAEDjS,OAAO,CACL+R,aAAa,IAAI,IAAjB,IAAyB/T,KAAK,KAAK,IAD9B,EAEL,uEACE,wEADF,GAEE,uEAFF,GAGE,yEAHF,GAIE,iEAJF,GAKE,yDAPG,CAAP;MAUA,IAAImU,UAAU,GAAGC,qBAAqB,CAAC;QACrCC,eAAe,EAAExW,KAAK,CAACc,QADc;QAErCmB,YAAY,EAAEnB,QAFuB;QAGrC2T;MAHqC,CAAD,CAAtC;MAMA,IAAI6B,UAAU,IAAInU,KAAK,IAAI,IAA3B,EAAiC;QAC/B;QACAiU,uBAAuB,GAAG,IAA1B;QACA1H,IAAI,CAACnN,OAAL,CAAae,EAAb,CAAgBH,KAAK,GAAG,CAAC,CAAzB,EAH+B;;QAM/BsU,aAAa,CAACH,UAAD,EAAa;UACxBtW,KAAK,EAAE,SADiB;UAExBc,QAFwB;UAGxBsS,OAAOA,CAAA;YACLqD,aAAa,CAACH,UAAD,EAAc;cACzBtW,KAAK,EAAE,YADkB;cAEzBoT,OAAO,EAAEnT,SAFgB;cAGzBoT,KAAK,EAAEpT,SAHkB;cAIzBa;aAJW,CAAb,CADK;;YAQL4N,IAAI,CAACnN,OAAL,CAAae,EAAb,CAAgBH,KAAhB;WAXsB;UAaxBkR,KAAKA,CAAA;YACHqD,aAAa,CAACJ,UAAD,CAAb;YACAK,WAAW,CAAC;cAAExB,QAAQ,EAAE,IAAID,GAAJ,CAAQV,MAAM,CAACxU,KAAP,CAAamV,QAArB;YAAZ,CAAD,CAAX;UACD;QAhBuB,CAAb,CAAb;QAkBA;MACD;MAED,OAAOyB,eAAe,CAACnC,aAAD,EAAgB3T,QAAhB,CAAtB;KApDc,CAAlB,CAHiB;;IA4DjB,IAAI,CAACd,KAAK,CAACqU,WAAX,EAAwB;MACtBuC,eAAe,CAACxX,MAAa,CAACiB,GAAf,EAAoBL,KAAK,CAACc,QAA1B,CAAf;IACD;IAED,OAAO0T,MAAP;EACD,CAvM0C;;EA0M3C,SAASqC,OAATA,CAAA,EAAgB;IACd,IAAIlD,eAAJ,EAAqB;MACnBA,eAAe;IAChB;IACDtE,WAAW,CAACyH,KAAZ;IACAxB,2BAA2B,IAAIA,2BAA2B,CAACnE,KAA5B,EAA/B;IACAnR,KAAK,CAACiV,QAAN,CAAe3M,OAAf,CAAuB,CAACgD,CAAD,EAAIzK,GAAJ,KAAYkW,aAAa,CAAClW,GAAD,CAAhD;IACAb,KAAK,CAACmV,QAAN,CAAe7M,OAAf,CAAuB,CAACgD,CAAD,EAAIzK,GAAJ,KAAY6V,aAAa,CAAC7V,GAAD,CAAhD;EACD,CAlN0C;;EAqN3C,SAASoQ,SAATA,CAAmBxO,EAAnB,EAAuC;IACrC4M,WAAW,CAACzI,GAAZ,CAAgBnE,EAAhB;IACA,OAAO,MAAM4M,WAAW,CAACwB,MAAZ,CAAmBpO,EAAnB,CAAb;EACD,CAxN0C;;EA2N3C,SAASkU,WAATA,CAAqBK,QAArB,EAAmD;IACjDhX,KAAK,GACA8E,QAAA,KAAA9E,KADA,EAEAgX,QAFA,CAAL;IAIA3H,WAAW,CAAC/G,OAAZ,CAAqB0I,UAAD,IAAgBA,UAAU,CAAChR,KAAD,CAA9C;EACD,CAjO0C;EAoO3C;EACA;EACA;EACA;;EACA,SAASiX,kBAATA,CACEnW,QADF,EAEEkW,QAFF,EAE4E;IAAA,IAAAE,eAAA,EAAAC,gBAAA;;IAE1E;IACA;IACA;IACA;IACA;IACA,IAAIC,cAAc,GAChBpX,KAAK,CAAC+U,UAAN,IAAoB,IAApB,IACA/U,KAAK,CAAC0U,UAAN,CAAiB5B,UAAjB,IAA+B,IAD/B,IAEAuE,gBAAgB,CAACrX,KAAK,CAAC0U,UAAN,CAAiB5B,UAAlB,CAFhB,IAGA9S,KAAK,CAAC0U,UAAN,CAAiB1U,KAAjB,KAA2B,SAH3B,IAIA,EAAAkX,eAAA,GAAApW,QAAQ,CAACd,KAAT,KAAgB,gBAAAkX,eAAA,CAAAI,WAAhB,MAAgC,IALlC;IAOA,IAAIvC,UAAJ;IACA,IAAIiC,QAAQ,CAACjC,UAAb,EAAyB;MACvB,IAAI/J,MAAM,CAACuM,IAAP,CAAYP,QAAQ,CAACjC,UAArB,CAAiC,CAAA5U,MAAjC,GAA0C,CAA9C,EAAiD;QAC/C4U,UAAU,GAAGiC,QAAQ,CAACjC,UAAtB;MACD,CAFD,MAEO;QACL;QACAA,UAAU,GAAG,IAAb;MACD;KANH,MAOO,IAAIqC,cAAJ,EAAoB;MACzB;MACArC,UAAU,GAAG/U,KAAK,CAAC+U,UAAnB;IACD,CAHM,MAGA;MACL;MACAA,UAAU,GAAG,IAAb;IACD,CA5ByE;;IA+B1E,IAAID,UAAU,GAAGkC,QAAQ,CAAClC,UAAT,GACb0C,eAAe,CACbxX,KAAK,CAAC8U,UADO,EAEbkC,QAAQ,CAAClC,UAFI,EAGbkC,QAAQ,CAAC1P,OAAT,IAAoB,EAHP,EAIb0P,QAAQ,CAAChC,MAJI,CADF,GAObhV,KAAK,CAAC8U,UAPV,CA/B0E;IAyC1E;;IACA,KAAK,IAAI,CAACjU,GAAD,CAAT,IAAkBsV,gBAAlB,EAAoC;MAClCO,aAAa,CAAC7V,GAAD,CAAb;IACD,CA5CyE;IA+C1E;;IACA,IAAI+T,kBAAkB,GACpBS,yBAAyB,KAAK,IAA9B,IACCrV,KAAK,CAAC0U,UAAN,CAAiB5B,UAAjB,IAA+B,IAA/B,IACCuE,gBAAgB,CAACrX,KAAK,CAAC0U,UAAN,CAAiB5B,UAAlB,CADjB,IAEC,EAAAqE,gBAAA,GAAArW,QAAQ,CAACd,KAAT,KAAgB,gBAAAmX,gBAAA,CAAAG,WAAhB,MAAgC,IAJpC;IAMAX,WAAW,CAAA7R,QAAA,KACNkS,QADM;MAETjC,UAFS;MAGTD,UAHS;MAITL,aAAa,EAAEW,aAJN;MAKTtU,QALS;MAMTuT,WAAW,EAAE,IANJ;MAOTK,UAAU,EAAE7B,eAPH;MAQTgC,YAAY,EAAE,MARL;MASTF,qBAAqB,EAAE8C,sBAAsB,CAC3C3W,QAD2C,EAE3CkW,QAAQ,CAAC1P,OAAT,IAAoBtH,KAAK,CAACsH,OAFiB,CATpC;MAaTsN,kBAbS;MAcTO,QAAQ,EAAE,IAAID,GAAJ,CAAQlV,KAAK,CAACmV,QAAd;KAdZ;IAiBA,IAAII,2BAAJ,EAAiC,CAAjC,KAEO,IAAIH,aAAa,KAAKhW,MAAa,CAACiB,GAApC,EAAyC,CAAzC,KAEA,IAAI+U,aAAa,KAAKhW,MAAa,CAAC4C,IAApC,EAA0C;MAC/C0M,IAAI,CAACnN,OAAL,CAAaQ,IAAb,CAAkBjB,QAAlB,EAA4BA,QAAQ,CAACd,KAArC;IACD,CAFM,MAEA,IAAIoV,aAAa,KAAKhW,MAAa,CAACiD,OAApC,EAA6C;MAClDqM,IAAI,CAACnN,OAAL,CAAaa,OAAb,CAAqBtB,QAArB,EAA+BA,QAAQ,CAACd,KAAxC;IACD,CA/EyE;;IAkF1EoV,aAAa,GAAGhW,MAAa,CAACiB,GAA9B;IACAgV,yBAAyB,GAAG,KAA5B;IACAE,2BAA2B,GAAG,KAA9B;IACAC,sBAAsB,GAAG,KAAzB;IACAC,uBAAuB,GAAG,EAA1B;IACAC,qBAAqB,GAAG,EAAxB;EACD,CAlU0C;EAqU3C;;EACA,eAAegC,QAAfA,CACE9W,EADF,EAEE+W,IAFF,EAE8B;IAE5B,IAAI,OAAO/W,EAAP,KAAc,QAAlB,EAA4B;MAC1B8N,IAAI,CAACnN,OAAL,CAAae,EAAb,CAAgB1B,EAAhB;MACA;IACD;IAED,IAAI;MAAEe,IAAF;MAAQiW,UAAR;MAAoBlS;IAApB,IAA8BmS,wBAAwB,CAACjX,EAAD,EAAK+W,IAAL,CAA1D;IAEA,IAAInB,eAAe,GAAGxW,KAAK,CAACc,QAA5B;IACA,IAAImB,YAAY,GAAGlB,cAAc,CAACf,KAAK,CAACc,QAAP,EAAiBa,IAAjB,EAAuBgW,IAAI,IAAIA,IAAI,CAAC3X,KAApC,CAAjC,CAV4B;IAa5B;IACA;IACA;IACA;;IACAiC,YAAY,GAAA6C,QAAA,KACP7C,YADO,EAEPyM,IAAI,CAACnN,OAAL,CAAaG,cAAb,CAA4BO,YAA5B,CAFO,CAAZ;IAKA,IAAI6V,WAAW,GAAGH,IAAI,IAAIA,IAAI,CAACvV,OAAL,IAAgB,IAAxB,GAA+BuV,IAAI,CAACvV,OAApC,GAA8CnC,SAAhE;IAEA,IAAIwU,aAAa,GAAGrV,MAAa,CAAC4C,IAAlC;IAEA,IAAI8V,WAAW,KAAK,IAApB,EAA0B;MACxBrD,aAAa,GAAGrV,MAAa,CAACiD,OAA9B;IACD,CAFD,MAEO,IAAIyV,WAAW,KAAK,KAApB,EAA2B,CAA3B,KAEA,IACLF,UAAU,IAAI,IAAd,IACAP,gBAAgB,CAACO,UAAU,CAAC9E,UAAZ,CADhB,IAEA8E,UAAU,CAAC7E,UAAX,KAA0B/S,KAAK,CAACc,QAAN,CAAeE,QAAf,GAA0BhB,KAAK,CAACc,QAAN,CAAee,MAH9D,EAIL;MACA;MACA;MACA;MACA;MACA4S,aAAa,GAAGrV,MAAa,CAACiD,OAA9B;IACD;IAED,IAAIuS,kBAAkB,GACpB+C,IAAI,IAAI,oBAAwB,IAAAA,IAAhC,GACIA,IAAI,CAAC/C,kBAAL,KAA4B,IADhC,GAEI3U,SAHN;IAKA,IAAIqW,UAAU,GAAGC,qBAAqB,CAAC;MACrCC,eADqC;MAErCvU,YAFqC;MAGrCwS;IAHqC,CAAD,CAAtC;IAKA,IAAI6B,UAAJ,EAAgB;MACd;MACAG,aAAa,CAACH,UAAD,EAAa;QACxBtW,KAAK,EAAE,SADiB;QAExBc,QAAQ,EAAEmB,YAFc;QAGxBmR,OAAOA,CAAA;UACLqD,aAAa,CAACH,UAAD,EAAc;YACzBtW,KAAK,EAAE,YADkB;YAEzBoT,OAAO,EAAEnT,SAFgB;YAGzBoT,KAAK,EAAEpT,SAHkB;YAIzBa,QAAQ,EAAEmB;WAJC,CAAb,CADK;;UAQLyV,QAAQ,CAAC9W,EAAD,EAAK+W,IAAL,CAAR;SAXsB;QAaxBtE,KAAKA,CAAA;UACHqD,aAAa,CAACJ,UAAD,CAAb;UACAK,WAAW,CAAC;YAAExB,QAAQ,EAAE,IAAID,GAAJ,CAAQlV,KAAK,CAACmV,QAAd;UAAZ,CAAD,CAAX;QACD;MAhBuB,CAAb,CAAb;MAkBA;IACD;IAED,OAAO,MAAMyB,eAAe,CAACnC,aAAD,EAAgBxS,YAAhB,EAA8B;MACxD2V,UADwD;MAExD;MACA;MACAG,YAAY,EAAErS,KAJ0C;MAKxDkP,kBALwD;MAMxDxS,OAAO,EAAEuV,IAAI,IAAIA,IAAI,CAACvV;IANkC,CAA9B,CAA5B;EAQD,CA3Z0C;EA8Z3C;EACA;;EACA,SAAS4V,UAATA,CAAA,EAAmB;IACjBC,oBAAoB;IACpBtB,WAAW,CAAC;MAAE9B,YAAY,EAAE;KAAjB,CAAX,CAFiB;IAKjB;;IACA,IAAI7U,KAAK,CAAC0U,UAAN,CAAiB1U,KAAjB,KAA2B,YAA/B,EAA6C;MAC3C;IACD,CARgB;IAWjB;IACA;;IACA,IAAIA,KAAK,CAAC0U,UAAN,CAAiB1U,KAAjB,KAA2B,MAA/B,EAAuC;MACrC4W,eAAe,CAAC5W,KAAK,CAACyU,aAAP,EAAsBzU,KAAK,CAACc,QAA5B,EAAsC;QACnDoX,8BAA8B,EAAE;MADmB,CAAtC,CAAf;MAGA;IACD,CAlBgB;IAqBjB;IACA;;IACAtB,eAAe,CACbxB,aAAa,IAAIpV,KAAK,CAACyU,aADV,EAEbzU,KAAK,CAAC0U,UAAN,CAAiB5T,QAFJ,EAGb;MAAEqX,kBAAkB,EAAEnY,KAAK,CAAC0U;IAA5B,CAHa,CAAf;EAKD,CA5b0C;EA+b3C;EACA;;EACA,eAAekC,eAAfA,CACEnC,aADF,EAEE3T,QAFF,EAGE6W,IAHF,EAUG;IAED;IACA;IACA;IACArC,2BAA2B,IAAIA,2BAA2B,CAACnE,KAA5B,EAA/B;IACAmE,2BAA2B,GAAG,IAA9B;IACAF,aAAa,GAAGX,aAAhB;IACAc,2BAA2B,GACzB,CAACoC,IAAI,IAAIA,IAAI,CAACO,8BAAd,MAAkD,IADpD,CARC;IAYD;;IACAE,kBAAkB,CAACpY,KAAK,CAACc,QAAP,EAAiBd,KAAK,CAACsH,OAAvB,CAAlB;IACA+N,yBAAyB,GAAG,CAACsC,IAAI,IAAIA,IAAI,CAAC/C,kBAAd,MAAsC,IAAlE;IAEA,IAAIyD,iBAAiB,GAAGV,IAAI,IAAIA,IAAI,CAACQ,kBAArC;IACA,IAAI7Q,OAAO,GAAGP,WAAW,CAAC2M,UAAD,EAAa5S,QAAb,EAAuB4N,IAAI,CAACzH,QAA5B,CAAzB,CAjBC;;IAoBD,IAAI,CAACK,OAAL,EAAc;MACZ,IAAI5B,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QAAEnT,QAAQ,EAAEF,QAAQ,CAACE;MAArB,CAAN,CAAlC;MACA,IAAI;QAAEsG,OAAO,EAAEgR,eAAX;QAA4BrS;MAA5B,IACFmO,sBAAsB,CAACV,UAAD,CADxB,CAFY;;MAKZ6E,qBAAqB;MACrBtB,kBAAkB,CAACnW,QAAD,EAAW;QAC3BwG,OAAO,EAAEgR,eADkB;QAE3BxD,UAAU,EAAE,EAFe;QAG3BE,MAAM,EAAE;UACN,CAAC/O,KAAK,CAACO,EAAP,GAAYd;QADN;MAHmB,CAAX,CAAlB;MAOA;IACD,CAlCA;IAqCD;IACA;;IACA,IACE8S,gBAAgB,CAACxY,KAAK,CAACc,QAAP,EAAiBA,QAAjB,CAAhB,IACA,EAAE6W,IAAI,IAAIA,IAAI,CAACC,UAAb,IAA2BP,gBAAgB,CAACM,IAAI,CAACC,UAAL,CAAgB9E,UAAjB,CAA7C,CAFF,EAGE;MACAmE,kBAAkB,CAACnW,QAAD,EAAW;QAAEwG;MAAF,CAAX,CAAlB;MACA;IACD,CA7CA;;IAgDDgO,2BAA2B,GAAG,IAAIxF,eAAJ,EAA9B;IACA,IAAI2I,OAAO,GAAGC,uBAAuB,CACnChK,IAAI,CAACnN,OAD8B,EAEnCT,QAFmC,EAGnCwU,2BAA2B,CAACrF,MAHO,EAInC0H,IAAI,IAAIA,IAAI,CAACC,UAJsB,CAArC;IAMA,IAAIe,iBAAJ;IACA,IAAIZ,YAAJ;IAEA,IAAIJ,IAAI,IAAIA,IAAI,CAACI,YAAjB,EAA+B;MAC7B;MACA;MACA;MACA;MACAA,YAAY,GAAG;QACb,CAACa,mBAAmB,CAACtR,OAAD,CAAnB,CAA6BrB,KAA7B,CAAmCO,EAApC,GAAyCmR,IAAI,CAACI;OADhD;IAGD,CARD,MAQO,IACLJ,IAAI,IACJA,IAAI,CAACC,UADL,IAEAP,gBAAgB,CAACM,IAAI,CAACC,UAAL,CAAgB9E,UAAjB,CAHX,EAIL;MACA;MACA,IAAI+F,YAAY,GAAG,MAAMC,YAAY,CACnCL,OADmC,EAEnC3X,QAFmC,EAGnC6W,IAAI,CAACC,UAH8B,EAInCtQ,OAJmC,EAKnC;QAAElF,OAAO,EAAEuV,IAAI,CAACvV;MAAhB,CALmC,CAArC;MAQA,IAAIyW,YAAY,CAACE,cAAjB,EAAiC;QAC/B;MACD;MAEDJ,iBAAiB,GAAGE,YAAY,CAACF,iBAAjC;MACAZ,YAAY,GAAGc,YAAY,CAACG,kBAA5B;MAEA,IAAItE,UAAU,GAAA5P,QAAA;QACZ9E,KAAK,EAAE,SADK;QAEZc;OACG,EAAA6W,IAAI,CAACC,UAHI,CAAd;MAKAS,iBAAiB,GAAG3D,UAApB,CAtBA;;MAyBA+D,OAAO,GAAG,IAAIQ,OAAJ,CAAYR,OAAO,CAAC/U,GAApB,EAAyB;QAAEuM,MAAM,EAAEwI,OAAO,CAACxI;MAAlB,CAAzB,CAAV;IACD,CAhGA;;IAmGD,IAAI;MAAE8I,cAAF;MAAkBjE,UAAlB;MAA8BE;KAAW,SAAMkE,aAAa,CAC9DT,OAD8D,EAE9D3X,QAF8D,EAG9DwG,OAH8D,EAI9D+Q,iBAJ8D,EAK9DV,IAAI,IAAIA,IAAI,CAACC,UALiD,EAM9DD,IAAI,IAAIA,IAAI,CAACvV,OANiD,EAO9DuW,iBAP8D,EAQ9DZ,YAR8D,CAAhE;IAWA,IAAIgB,cAAJ,EAAoB;MAClB;IACD,CAhHA;IAmHD;IACA;;IACAzD,2BAA2B,GAAG,IAA9B;IAEA2B,kBAAkB,CAACnW,QAAD,EAAAgE,QAAA;MAChBwC;IADgB,GAEZqR,iBAAiB,GAAG;MAAE5D,UAAU,EAAE4D;IAAd,CAAH,GAAuC,EAF5C;MAGhB7D,UAHgB;MAIhBE;KAJF;EAMD,CAxkB0C;EA2kB3C;;EACA,eAAe8D,YAAfA,CACEL,OADF,EAEE3X,QAFF,EAGE8W,UAHF,EAIEtQ,OAJF,EAKEqQ,IALF,EAK8B;IAE5BM,oBAAoB,GAFQ;;IAK5B,IAAIvD,UAAU,GAAA5P,QAAA;MACZ9E,KAAK,EAAE,YADK;MAEZc;IAFY,GAGT8W,UAHS,CAAd;IAKAjB,WAAW,CAAC;MAAEjC;KAAH,CAAX,CAV4B;;IAa5B,IAAIvL,MAAJ;IACA,IAAIgQ,WAAW,GAAGC,cAAc,CAAC9R,OAAD,EAAUxG,QAAV,CAAhC;IAEA,IAAI,CAACqY,WAAW,CAAClT,KAAZ,CAAkB7F,MAAvB,EAA+B;MAC7B+I,MAAM,GAAG;QACPkQ,IAAI,EAAEtT,UAAU,CAACL,KADV;QAEPA,KAAK,EAAEyO,sBAAsB,CAAC,GAAD,EAAM;UACjCmF,MAAM,EAAEb,OAAO,CAACa,MADiB;UAEjCtY,QAAQ,EAAEF,QAAQ,CAACE,QAFc;UAGjCuY,OAAO,EAAEJ,WAAW,CAAClT,KAAZ,CAAkBO;SAHA;OAF/B;IAQD,CATD,MASO;MACL2C,MAAM,GAAG,MAAMqQ,kBAAkB,CAC/B,QAD+B,EAE/Bf,OAF+B,EAG/BU,WAH+B,EAI/B7R,OAJ+B,EAK/BkN,MAAM,CAACvN,QALwB,CAAjC;MAQA,IAAIwR,OAAO,CAACxI,MAAR,CAAeW,OAAnB,EAA4B;QAC1B,OAAO;UAAEmI,cAAc,EAAE;SAAzB;MACD;IACF;IAED,IAAIU,gBAAgB,CAACtQ,MAAD,CAApB,EAA8B;MAC5B,IAAI/G,OAAJ;MACA,IAAIuV,IAAI,IAAIA,IAAI,CAACvV,OAAL,IAAgB,IAA5B,EAAkC;QAChCA,OAAO,GAAGuV,IAAI,CAACvV,OAAf;MACD,CAFD,MAEO;QACL;QACA;QACA;QACAA,OAAO,GACL+G,MAAM,CAACrI,QAAP,KAAoBd,KAAK,CAACc,QAAN,CAAeE,QAAf,GAA0BhB,KAAK,CAACc,QAAN,CAAee,MAD/D;MAED;MACD,MAAM6X,uBAAuB,CAAC1Z,KAAD,EAAQmJ,MAAR,EAAgB;QAAEyO,UAAF;QAAcxV;MAAd,CAAhB,CAA7B;MACA,OAAO;QAAE2W,cAAc,EAAE;OAAzB;IACD;IAED,IAAIY,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIyQ,aAAa,GAAGhB,mBAAmB,CAACtR,OAAD,EAAU6R,WAAW,CAAClT,KAAZ,CAAkBO,EAA5B,CAAvC,CAHyB;MAMzB;MACA;MACA;;MACA,IAAI,CAACmR,IAAI,IAAIA,IAAI,CAACvV,OAAd,MAA2B,IAA/B,EAAqC;QACnCgT,aAAa,GAAGhW,MAAa,CAAC4C,IAA9B;MACD;MAED,OAAO;QACL;QACA2W,iBAAiB,EAAE,EAFd;QAGLK,kBAAkB,EAAE;UAAE,CAACY,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,GAA0B2C,MAAM,CAACzD;QAAnC;OAHtB;IAKD;IAED,IAAImU,gBAAgB,CAAC1Q,MAAD,CAApB,EAA8B;MAC5B,MAAMgL,sBAAsB,CAAC,GAAD,EAAM;QAAEkF,IAAI,EAAE;MAAR,CAAN,CAA5B;IACD;IAED,OAAO;MACLV,iBAAiB,EAAE;QAAE,CAACQ,WAAW,CAAClT,KAAZ,CAAkBO,EAAnB,GAAwB2C,MAAM,CAACsF;MAAjC;KADrB;EAGD,CAlqB0C;EAqqB3C;;EACA,eAAeyK,aAAfA,CACET,OADF,EAEE3X,QAFF,EAGEwG,OAHF,EAIE6Q,kBAJF,EAKEP,UALF,EAMExV,OANF,EAOEuW,iBAPF,EAQEZ,YARF,EAQ0B;IAExB;IACA,IAAIM,iBAAiB,GAAGF,kBAAxB;IACA,IAAI,CAACE,iBAAL,EAAwB;MACtB,IAAI3D,UAAU,GAAA5P,QAAA;QACZ9E,KAAK,EAAE,SADK;QAEZc,QAFY;QAGZgS,UAAU,EAAE7S,SAHA;QAIZ8S,UAAU,EAAE9S,SAJA;QAKZ+S,WAAW,EAAE/S,SALD;QAMZgT,QAAQ,EAAEhT;MANE,GAOT2X,UAPS,CAAd;MASAS,iBAAiB,GAAG3D,UAApB;IACD,CAfuB;IAkBxB;;IACA,IAAIoF,gBAAgB,GAAGlC,UAAU,GAC7BA,UAD6B,GAE7BS,iBAAiB,CAACvF,UAAlB,IACAuF,iBAAiB,CAACtF,UADlB,IAEAsF,iBAAiB,CAACpF,QAFlB,IAGAoF,iBAAiB,CAACrF,WAHlB,GAIA;MACEF,UAAU,EAAEuF,iBAAiB,CAACvF,UADhC;MAEEC,UAAU,EAAEsF,iBAAiB,CAACtF,UAFhC;MAGEE,QAAQ,EAAEoF,iBAAiB,CAACpF,QAH9B;MAIED,WAAW,EAAEqF,iBAAiB,CAACrF;IAJjC,CAJA,GAUA/S,SAZJ;IAcA,IAAI,CAAC8Z,aAAD,EAAgBC,oBAAhB,IAAwCC,gBAAgB,CAC1DvL,IAAI,CAACnN,OADqD,EAE1DvB,KAF0D,EAG1DsH,OAH0D,EAI1DwS,gBAJ0D,EAK1DhZ,QAL0D,EAM1D0U,sBAN0D,EAO1DC,uBAP0D,EAQ1DC,qBAR0D,EAS1DiD,iBAT0D,EAU1DZ,YAV0D,EAW1D/B,gBAX0D,CAA5D,CAjCwB;IAgDxB;IACA;;IACAuC,qBAAqB,CAClBgB,OAAD,IACE,EAAEjS,OAAO,IAAIA,OAAO,CAAC4C,IAAR,CAAcoK,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAe+S,OAAnC,CAAb,KACCQ,aAAa,IAAIA,aAAa,CAAC7P,IAAd,CAAoBoK,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAe+S,OAAzC,CAHD,CAArB,CAlDwB;;IAyDxB,IAAIQ,aAAa,CAAC5Z,MAAd,KAAyB,CAAzB,IAA8B6Z,oBAAoB,CAAC7Z,MAArB,KAAgC,CAAlE,EAAqE;MACnE8W,kBAAkB,CAACnW,QAAD,EAAAgE,QAAA;QAChBwC,OADgB;QAEhBwN,UAAU,EAAE,EAFI;QAGhB;QACAE,MAAM,EAAE+C,YAAY,IAAI;MAJR,GAKZY,iBAAiB,GAAG;QAAE5D,UAAU,EAAE4D;OAAjB,GAAuC,EAL5C,CAAlB;MAOA,OAAO;QAAEI,cAAc,EAAE;OAAzB;IACD,CAlEuB;IAqExB;IACA;IACA;;IACA,IAAI,CAACxD,2BAAL,EAAkC;MAChCyE,oBAAoB,CAAC1R,OAArB,CAA8B4R,EAAD,IAAO;QAClC,IAAIC,OAAO,GAAGna,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmBuJ,EAAE,CAACrZ,GAAtB,CAAd;QACA,IAAIuZ,mBAAmB,GAA6B;UAClDpa,KAAK,EAAE,SAD2C;UAElDyO,IAAI,EAAE0L,OAAO,IAAIA,OAAO,CAAC1L,IAFyB;UAGlDqE,UAAU,EAAE7S,SAHsC;UAIlD8S,UAAU,EAAE9S,SAJsC;UAKlD+S,WAAW,EAAE/S,SALqC;UAMlDgT,QAAQ,EAAEhT,SANwC;UAOlD,2BAA6B;SAP/B;QASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBmL,EAAE,CAACrZ,GAAtB,EAA2BuZ,mBAA3B;OAXF;MAaA,IAAIrF,UAAU,GAAG4D,iBAAiB,IAAI3Y,KAAK,CAAC+U,UAA5C;MACA4B,WAAW,CAAA7R,QAAA;QACT4P,UAAU,EAAE2D;OACR,EAAAtD,UAAU,GACV/J,MAAM,CAACuM,IAAP,CAAYxC,UAAZ,CAAwB,CAAA5U,MAAxB,KAAmC,CAAnC,GACE;QAAE4U,UAAU,EAAE;MAAd,CADF,GAEE;QAAEA;OAHM,GAIV,EANK,EAOLiF,oBAAoB,CAAC7Z,MAArB,GAA8B,CAA9B,GACA;QAAE8U,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;OADZ,GAEA,EATK,CAAX;IAWD;IAEDY,uBAAuB,GAAG,EAAED,kBAA5B;IACAoE,oBAAoB,CAAC1R,OAArB,CAA8B4R,EAAD,IAC3BvE,gBAAgB,CAAC5G,GAAjB,CAAqBmL,EAAE,CAACrZ,GAAxB,EAA6ByU,2BAA7B,CADF;IAIA,IAAI;MAAE+E,OAAF;MAAWC,aAAX;MAA0BC;IAA1B,IACF,MAAMC,8BAA8B,CAClCxa,KAAK,CAACsH,OAD4B,EAElCA,OAFkC,EAGlCyS,aAHkC,EAIlCC,oBAJkC,EAKlCvB,OALkC,CADtC;IASA,IAAIA,OAAO,CAACxI,MAAR,CAAeW,OAAnB,EAA4B;MAC1B,OAAO;QAAEmI,cAAc,EAAE;OAAzB;IACD,CApHuB;IAuHxB;IACA;;IACAiB,oBAAoB,CAAC1R,OAArB,CAA8B4R,EAAD,IAAQvE,gBAAgB,CAAC9E,MAAjB,CAAwBqJ,EAAE,CAACrZ,GAA3B,CAArC,EAzHwB;;IA4HxB,IAAIqR,QAAQ,GAAGuI,YAAY,CAACJ,OAAD,CAA3B;IACA,IAAInI,QAAJ,EAAc;MACZ,MAAMwH,uBAAuB,CAAC1Z,KAAD,EAAQkS,QAAR,EAAkB;QAAE9P;MAAF,CAAlB,CAA7B;MACA,OAAO;QAAE2W,cAAc,EAAE;OAAzB;IACD,CAhIuB;;IAmIxB,IAAI;MAAEjE,UAAF;MAAcE;IAAd,IAAyB0F,iBAAiB,CAC5C1a,KAD4C,EAE5CsH,OAF4C,EAG5CyS,aAH4C,EAI5CO,aAJ4C,EAK5CvC,YAL4C,EAM5CiC,oBAN4C,EAO5CO,cAP4C,EAQ5CtE,eAR4C,CAA9C,CAnIwB;;IA+IxBA,eAAe,CAAC3N,OAAhB,CAAwB,CAACqS,YAAD,EAAepB,OAAf,KAA0B;MAChDoB,YAAY,CAAC1J,SAAb,CAAwBL,OAAD,IAAY;QACjC;QACA;QACA;QACA,IAAIA,OAAO,IAAI+J,YAAY,CAACvK,IAA5B,EAAkC;UAChC6F,eAAe,CAACpF,MAAhB,CAAuB0I,OAAvB;QACD;OANH;KADF;IAWAqB,sBAAsB;IACtB,IAAIC,kBAAkB,GAAGC,oBAAoB,CAACjF,uBAAD,CAA7C;IAEA,OAAA/Q,QAAA;MACEgQ,UADF;MAEEE;IAFF,GAGM6F,kBAAkB,IAAIb,oBAAoB,CAAC7Z,MAArB,GAA8B,CAApD,GACA;MAAE8U,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;IAAZ,CADA,GAEA,EALN;EAOD;EAED,SAAS8F,UAATA,CAAiCla,GAAjC,EAA4C;IAC1C,OAAOb,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmB9P,GAAnB,KAA2BqS,YAAlC;EACD,CAt1B0C;;EAy1B3C,SAAS8H,KAATA,CACEna,GADF,EAEE0Y,OAFF,EAGE/V,IAHF,EAIEmU,IAJF,EAI2B;IAEzB,IAAInE,QAAJ,EAAc;MACZ,MAAM,IAAItP,KAAJ,CACJ,8EACE,8EADF,GAEE,6CAHE,CAAN;IAKD;IAED,IAAIyR,gBAAgB,CAAChP,GAAjB,CAAqB9F,GAArB,CAAJ,EAA+Boa,YAAY,CAACpa,GAAD,CAAZ;IAE/B,IAAIyG,OAAO,GAAGP,WAAW,CAAC2M,UAAD,EAAalQ,IAAb,EAAmBkL,IAAI,CAACzH,QAAxB,CAAzB;IACA,IAAI,CAACK,OAAL,EAAc;MACZ4T,eAAe,CACbra,GADa,EAEb0Y,OAFa,EAGbpF,sBAAsB,CAAC,GAAD,EAAM;QAAEnT,QAAQ,EAAEwC;MAAZ,CAAN,CAHT,CAAf;MAKA;IACD;IAED,IAAI;MAAE7B,IAAF;MAAQiW;IAAR,IAAuBC,wBAAwB,CAACrU,IAAD,EAAOmU,IAAP,EAAa,IAAb,CAAnD;IACA,IAAI7M,KAAK,GAAGsO,cAAc,CAAC9R,OAAD,EAAU3F,IAAV,CAA1B;IAEA0T,yBAAyB,GAAG,CAACsC,IAAI,IAAIA,IAAI,CAAC/C,kBAAd,MAAsC,IAAlE;IAEA,IAAIgD,UAAU,IAAIP,gBAAgB,CAACO,UAAU,CAAC9E,UAAZ,CAAlC,EAA2D;MACzDqI,mBAAmB,CAACta,GAAD,EAAM0Y,OAAN,EAAe5X,IAAf,EAAqBmJ,KAArB,EAA4BxD,OAA5B,EAAqCsQ,UAArC,CAAnB;MACA;IACD,CA9BwB;IAiCzB;;IACA5B,gBAAgB,CAACjH,GAAjB,CAAqBlO,GAArB,EAA0B;MAAE0Y,OAAF;MAAW5X,IAAX;MAAiBmJ,KAAjB;MAAwBxD;KAAlD;IACA8T,mBAAmB,CAACva,GAAD,EAAM0Y,OAAN,EAAe5X,IAAf,EAAqBmJ,KAArB,EAA4BxD,OAA5B,EAAqCsQ,UAArC,CAAnB;EACD,CAj4B0C;EAo4B3C;;EACA,eAAeuD,mBAAfA,CACEta,GADF,EAEE0Y,OAFF,EAGE5X,IAHF,EAIEmJ,KAJF,EAKEuQ,cALF,EAMEzD,UANF,EAMwB;IAEtBK,oBAAoB;IACpBjC,gBAAgB,CAACnF,MAAjB,CAAwBhQ,GAAxB;IAEA,IAAI,CAACiK,KAAK,CAAC7E,KAAN,CAAY7F,MAAjB,EAAyB;MACvB,IAAIsF,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QACtCmF,MAAM,EAAE1B,UAAU,CAAC9E,UADmB;QAEtC9R,QAAQ,EAAEW,IAF4B;QAGtC4X,OAAO,EAAEA;MAH6B,CAAN,CAAlC;MAKA2B,eAAe,CAACra,GAAD,EAAM0Y,OAAN,EAAe7T,KAAf,CAAf;MACA;IACD,CAbqB;;IAgBtB,IAAI4V,eAAe,GAAGtb,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmB9P,GAAnB,CAAtB;IACA,IAAIsZ,OAAO,GAAArV,QAAA;MACT9E,KAAK,EAAE;IADE,GAEN4X,UAFM;MAGTnJ,IAAI,EAAE6M,eAAe,IAAIA,eAAe,CAAC7M,IAHhC;MAIT,2BAA6B;KAJ/B;IAMAzO,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBsZ,OAAxB;IACAxD,WAAW,CAAC;MAAE1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;KAAb,CAAX,CAxBsB;;IA2BtB,IAAIsG,eAAe,GAAG,IAAIzL,eAAJ,EAAtB;IACA,IAAI0L,YAAY,GAAG9C,uBAAuB,CACxChK,IAAI,CAACnN,OADmC,EAExCI,IAFwC,EAGxC4Z,eAAe,CAACtL,MAHwB,EAIxC2H,UAJwC,CAA1C;IAMAjC,gBAAgB,CAAC5G,GAAjB,CAAqBlO,GAArB,EAA0B0a,eAA1B;IAEA,IAAIE,YAAY,GAAG,MAAMjC,kBAAkB,CACzC,QADyC,EAEzCgC,YAFyC,EAGzC1Q,KAHyC,EAIzCuQ,cAJyC,EAKzC7G,MAAM,CAACvN,QALkC,CAA3C;IAQA,IAAIuU,YAAY,CAACvL,MAAb,CAAoBW,OAAxB,EAAiC;MAC/B;MACA;MACA,IAAI+E,gBAAgB,CAAChF,GAAjB,CAAqB9P,GAArB,MAA8B0a,eAAlC,EAAmD;QACjD5F,gBAAgB,CAAC9E,MAAjB,CAAwBhQ,GAAxB;MACD;MACD;IACD;IAED,IAAI4Y,gBAAgB,CAACgC,YAAD,CAApB,EAAoC;MAClC9F,gBAAgB,CAAC9E,MAAjB,CAAwBhQ,GAAxB;MACAkV,gBAAgB,CAACnP,GAAjB,CAAqB/F,GAArB;MACA,IAAI6a,cAAc,GAAA5W,QAAA;QAChB9E,KAAK,EAAE;MADS,GAEb4X,UAFa;QAGhBnJ,IAAI,EAAExO,SAHU;QAIhB,2BAA6B;OAJ/B;MAMAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwB6a,cAAxB;MACA/E,WAAW,CAAC;QAAE1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;MAAZ,CAAD,CAAX;MAEA,OAAOyE,uBAAuB,CAAC1Z,KAAD,EAAQyb,YAAR,EAAsB;QAClDE,qBAAqB,EAAE;MAD2B,CAAtB,CAA9B;IAGD,CApEqB;;IAuEtB,IAAIhC,aAAa,CAAC8B,YAAD,CAAjB,EAAiC;MAC/BP,eAAe,CAACra,GAAD,EAAM0Y,OAAN,EAAekC,YAAY,CAAC/V,KAA5B,CAAf;MACA;IACD;IAED,IAAImU,gBAAgB,CAAC4B,YAAD,CAApB,EAAoC;MAClC,MAAMtH,sBAAsB,CAAC,GAAD,EAAM;QAAEkF,IAAI,EAAE;MAAR,CAAN,CAA5B;IACD,CA9EqB;IAiFtB;;IACA,IAAIpX,YAAY,GAAGjC,KAAK,CAAC0U,UAAN,CAAiB5T,QAAjB,IAA6Bd,KAAK,CAACc,QAAtD;IACA,IAAI8a,mBAAmB,GAAGlD,uBAAuB,CAC/ChK,IAAI,CAACnN,OAD0C,EAG/CU,YAH+C,EAI/CsZ,eAAe,CAACtL,MAJ+B,CAAjD;IAMA,IAAI3I,OAAO,GACTtH,KAAK,CAAC0U,UAAN,CAAiB1U,KAAjB,KAA2B,MAA3B,GACI+G,WAAW,CAAC2M,UAAD,EAAa1T,KAAK,CAAC0U,UAAN,CAAiB5T,QAA9B,EAAwC4N,IAAI,CAACzH,QAA7C,CADf,GAEIjH,KAAK,CAACsH,OAHZ;IAKAvD,SAAS,CAACuD,OAAD,EAAU,8CAAV,CAAT;IAEA,IAAIuU,MAAM,GAAG,EAAEjG,kBAAf;IACAE,cAAc,CAAC/G,GAAf,CAAmBlO,GAAnB,EAAwBgb,MAAxB;IAEA,IAAIC,WAAW,GAAAhX,QAAA;MACb9E,KAAK,EAAE,SADM;MAEbyO,IAAI,EAAEgN,YAAY,CAAChN;IAFN,GAGVmJ,UAHU;MAIb,2BAA6B;KAJ/B;IAMA5X,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBib,WAAxB;IAEA,IAAI,CAAC/B,aAAD,EAAgBC,oBAAhB,IAAwCC,gBAAgB,CAC1DvL,IAAI,CAACnN,OADqD,EAE1DvB,KAF0D,EAG1DsH,OAH0D,EAI1DsQ,UAJ0D,EAK1D3V,YAL0D,EAM1DuT,sBAN0D,EAO1DC,uBAP0D,EAQ1DC,qBAR0D,EAS1D;MAAE,CAAC5K,KAAK,CAAC7E,KAAN,CAAYO,EAAb,GAAkBiV,YAAY,CAAChN;KATyB,EAU1DxO,SAV0D;IAAA;IAW1D+V,gBAX0D,CAA5D,CA3GsB;IA0HtB;IACA;;IACAgE,oBAAoB,CACjB7P,MADH,CACW+P,EAAD,IAAQA,EAAE,CAACrZ,GAAH,KAAWA,GAD7B,EAEGyH,OAFH,CAEY4R,EAAD,IAAO;MACd,IAAI6B,QAAQ,GAAG7B,EAAE,CAACrZ,GAAlB;MACA,IAAIya,eAAe,GAAGtb,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmBoL,QAAnB,CAAtB;MACA,IAAI3B,mBAAmB,GAA6B;QAClDpa,KAAK,EAAE,SAD2C;QAElDyO,IAAI,EAAE6M,eAAe,IAAIA,eAAe,CAAC7M,IAFS;QAGlDqE,UAAU,EAAE7S,SAHsC;QAIlD8S,UAAU,EAAE9S,SAJsC;QAKlD+S,WAAW,EAAE/S,SALqC;QAMlDgT,QAAQ,EAAEhT,SANwC;QAOlD,2BAA6B;OAP/B;MASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBgN,QAAnB,EAA6B3B,mBAA7B;MACAzE,gBAAgB,CAAC5G,GAAjB,CAAqBgN,QAArB,EAA+BR,eAA/B;KAfJ;IAkBA5E,WAAW,CAAC;MAAE1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;IAAZ,CAAD,CAAX;IAEA,IAAI;MAAEoF,OAAF;MAAWC,aAAX;MAA0BC;IAA1B,IACF,MAAMC,8BAA8B,CAClCxa,KAAK,CAACsH,OAD4B,EAElCA,OAFkC,EAGlCyS,aAHkC,EAIlCC,oBAJkC,EAKlC4B,mBALkC,CADtC;IASA,IAAIL,eAAe,CAACtL,MAAhB,CAAuBW,OAA3B,EAAoC;MAClC;IACD;IAEDkF,cAAc,CAACjF,MAAf,CAAsBhQ,GAAtB;IACA8U,gBAAgB,CAAC9E,MAAjB,CAAwBhQ,GAAxB;IACAmZ,oBAAoB,CAAC1R,OAArB,CAA8BsH,CAAD,IAAO+F,gBAAgB,CAAC9E,MAAjB,CAAwBjB,CAAC,CAAC/O,GAA1B,CAApC;IAEA,IAAIqR,QAAQ,GAAGuI,YAAY,CAACJ,OAAD,CAA3B;IACA,IAAInI,QAAJ,EAAc;MACZ,OAAOwH,uBAAuB,CAAC1Z,KAAD,EAAQkS,QAAR,CAA9B;IACD,CApKqB;;IAuKtB,IAAI;MAAE4C,UAAF;MAAcE;IAAd,IAAyB0F,iBAAiB,CAC5C1a,KAD4C,EAE5CA,KAAK,CAACsH,OAFsC,EAG5CyS,aAH4C,EAI5CO,aAJ4C,EAK5Cra,SAL4C,EAM5C+Z,oBAN4C,EAO5CO,cAP4C,EAQ5CtE,eAR4C,CAA9C;IAWA,IAAI+F,WAAW,GAA0B;MACvChc,KAAK,EAAE,MADgC;MAEvCyO,IAAI,EAAEgN,YAAY,CAAChN,IAFoB;MAGvCqE,UAAU,EAAE7S,SAH2B;MAIvC8S,UAAU,EAAE9S,SAJ2B;MAKvC+S,WAAW,EAAE/S,SAL0B;MAMvCgT,QAAQ,EAAEhT,SAN6B;MAOvC,2BAA6B;KAP/B;IASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBmb,WAAxB;IAEA,IAAInB,kBAAkB,GAAGC,oBAAoB,CAACe,MAAD,CAA7C,CA7LsB;IAgMtB;IACA;;IACA,IACE7b,KAAK,CAAC0U,UAAN,CAAiB1U,KAAjB,KAA2B,SAA3B,IACA6b,MAAM,GAAGhG,uBAFX,EAGE;MACA9R,SAAS,CAACqR,aAAD,EAAgB,yBAAhB,CAAT;MACAE,2BAA2B,IAAIA,2BAA2B,CAACnE,KAA5B,EAA/B;MAEA8F,kBAAkB,CAACjX,KAAK,CAAC0U,UAAN,CAAiB5T,QAAlB,EAA4B;QAC5CwG,OAD4C;QAE5CwN,UAF4C;QAG5CE,MAH4C;QAI5CC,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;MAJkC,CAA5B,CAAlB;IAMD,CAbD,MAaO;MACL;MACA;MACA;MACA0B,WAAW,CAAA7R,QAAA;QACTkQ,MADS;QAETF,UAAU,EAAE0C,eAAe,CACzBxX,KAAK,CAAC8U,UADmB,EAEzBA,UAFyB,EAGzBxN,OAHyB,EAIzB0N,MAJyB;MAFlB,GAQL6F,kBAAkB,GAAG;QAAE5F,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;OAAf,GAA2C,EARxD,CAAX;MAUAO,sBAAsB,GAAG,KAAzB;IACD;EACF,CA1mC0C;;EA6mC3C,eAAe4F,mBAAfA,CACEva,GADF,EAEE0Y,OAFF,EAGE5X,IAHF,EAIEmJ,KAJF,EAKExD,OALF,EAMEsQ,UANF,EAMyB;IAEvB,IAAI0D,eAAe,GAAGtb,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmB9P,GAAnB,CAAtB,CAFuB;;IAIvB,IAAI6a,cAAc,GAAA5W,QAAA;MAChB9E,KAAK,EAAE,SADS;MAEhB8S,UAAU,EAAE7S,SAFI;MAGhB8S,UAAU,EAAE9S,SAHI;MAIhB+S,WAAW,EAAE/S,SAJG;MAKhBgT,QAAQ,EAAEhT;IALM,GAMb2X,UANa;MAOhBnJ,IAAI,EAAE6M,eAAe,IAAIA,eAAe,CAAC7M,IAPzB;MAQhB,2BAA6B;KAR/B;IAUAzO,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwB6a,cAAxB;IACA/E,WAAW,CAAC;MAAE1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;KAAb,CAAX,CAfuB;;IAkBvB,IAAIsG,eAAe,GAAG,IAAIzL,eAAJ,EAAtB;IACA,IAAI0L,YAAY,GAAG9C,uBAAuB,CACxChK,IAAI,CAACnN,OADmC,EAExCI,IAFwC,EAGxC4Z,eAAe,CAACtL,MAHwB,CAA1C;IAKA0F,gBAAgB,CAAC5G,GAAjB,CAAqBlO,GAArB,EAA0B0a,eAA1B;IACA,IAAIpS,MAAM,GAAe,MAAMqQ,kBAAkB,CAC/C,QAD+C,EAE/CgC,YAF+C,EAG/C1Q,KAH+C,EAI/CxD,OAJ+C,EAK/CkN,MAAM,CAACvN,QALwC,CAAjD,CAzBuB;IAkCvB;IACA;IACA;;IACA,IAAI4S,gBAAgB,CAAC1Q,MAAD,CAApB,EAA8B;MAC5BA,MAAM,GACJ,CAAC,MAAM8S,mBAAmB,CAAC9S,MAAD,EAASqS,YAAY,CAACvL,MAAtB,EAA8B,IAA9B,CAA1B,KACA9G,MAFF;IAGD,CAzCsB;IA4CvB;;IACA,IAAIwM,gBAAgB,CAAChF,GAAjB,CAAqB9P,GAArB,MAA8B0a,eAAlC,EAAmD;MACjD5F,gBAAgB,CAAC9E,MAAjB,CAAwBhQ,GAAxB;IACD;IAED,IAAI2a,YAAY,CAACvL,MAAb,CAAoBW,OAAxB,EAAiC;MAC/B;IACD,CAnDsB;;IAsDvB,IAAI6I,gBAAgB,CAACtQ,MAAD,CAApB,EAA8B;MAC5B,MAAMuQ,uBAAuB,CAAC1Z,KAAD,EAAQmJ,MAAR,CAA7B;MACA;IACD,CAzDsB;;IA4DvB,IAAIwQ,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;MACzB,IAAIyQ,aAAa,GAAGhB,mBAAmB,CAAC5Y,KAAK,CAACsH,OAAP,EAAgBiS,OAAhB,CAAvC;MACAvZ,KAAK,CAACiV,QAAN,CAAepE,MAAf,CAAsBhQ,GAAtB,EAFyB;MAIzB;MACA;;MACA8V,WAAW,CAAC;QACV1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd,CADA;QAEVD,MAAM,EAAE;UACN,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,GAA0B2C,MAAM,CAACzD;QAD3B;MAFE,CAAD,CAAX;MAMA;IACD;IAED3B,SAAS,CAAC,CAAC8V,gBAAgB,CAAC1Q,MAAD,CAAlB,EAA4B,iCAA5B,CAAT,CA3EuB;;IA8EvB,IAAI6S,WAAW,GAA0B;MACvChc,KAAK,EAAE,MADgC;MAEvCyO,IAAI,EAAEtF,MAAM,CAACsF,IAF0B;MAGvCqE,UAAU,EAAE7S,SAH2B;MAIvC8S,UAAU,EAAE9S,SAJ2B;MAKvC+S,WAAW,EAAE/S,SAL0B;MAMvCgT,QAAQ,EAAEhT,SAN6B;MAOvC,2BAA6B;KAP/B;IASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBmb,WAAxB;IACArF,WAAW,CAAC;MAAE1B,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;IAAZ,CAAD,CAAX;EACD;EAED;;;;;;;;;;;;;;;;;;AAkBG;;EACH,eAAeyE,uBAAfA,CACE1Z,KADF,EAEEkS,QAFF,EAWQgK,KAAA;IAAA,IAAAC,OAAA;IAAA,IARN;MACEvE,UADF;MAEExV,OAFF;MAGEuZ;IAHF,CAQM,GAAAO,KAAA,cAAF,EAAE,GAAAA,KAAA;IAEN,IAAIhK,QAAQ,CAAC8F,UAAb,EAAyB;MACvBxC,sBAAsB,GAAG,IAAzB;IACD;IAED,IAAI4G,gBAAgB,GAAGrb,cAAc,CACnCf,KAAK,CAACc,QAD6B,EAEnCoR,QAAQ,CAACpR,QAF0B;IAAA;IAAAgE,QAAA;MAKjCwS,WAAW,EAAE;IALoB,GAM7BqE,qBAAqB,GAAG;MAAEU,sBAAsB,EAAE;KAA7B,GAAsC,EAN9B,CAArC;IASAtY,SAAS,CACPqY,gBADO,EAEP,gDAFO,CAAT,CAfM;;IAqBN,IAAI9I,SAAS,IAAI,QAAO,CAAA6I,OAAA,GAAAvZ,MAAP,qBAAOuZ,OAAQ,CAAArb,QAAf,CAA4B,gBAA7C,EAA0D;MACxD,IAAIwb,SAAS,GAAG5N,IAAI,CAACnN,OAAL,CAAaC,SAAb,CAAuB0Q,QAAQ,CAACpR,QAAhC,EAA0C8E,MAA1D;MACA,IAAIhD,MAAM,CAAC9B,QAAP,CAAgB8E,MAAhB,KAA2B0W,SAA/B,EAA0C;QACxC,IAAIla,OAAJ,EAAa;UACXQ,MAAM,CAAC9B,QAAP,CAAgBsB,OAAhB,CAAwB8P,QAAQ,CAACpR,QAAjC;QACD,CAFD,MAEO;UACL8B,MAAM,CAAC9B,QAAP,CAAgB6E,MAAhB,CAAuBuM,QAAQ,CAACpR,QAAhC;QACD;QACD;MACD;IACF,CA/BK;IAkCN;;IACAwU,2BAA2B,GAAG,IAA9B;IAEA,IAAIiH,qBAAqB,GACvBna,OAAO,KAAK,IAAZ,GAAmBhD,MAAa,CAACiD,OAAjC,GAA2CjD,MAAa,CAAC4C,IAD3D,CArCM;IAyCN;;IACA,IAAI;MAAE8Q,UAAF;MAAcC,UAAd;MAA0BC,WAA1B;MAAuCC;KAAa,GAAAjT,KAAK,CAAC0U,UAA9D;IACA,IAAI,CAACkD,UAAD,IAAe9E,UAAf,IAA6BC,UAA7B,IAA2CE,QAA3C,IAAuDD,WAA3D,EAAwE;MACtE4E,UAAU,GAAG;QACX9E,UADW;QAEXC,UAFW;QAGXC,WAHW;QAIXC;OAJF;IAMD,CAlDK;IAqDN;IACA;;IACA,IACEL,iCAAiC,CAACjM,GAAlC,CAAsCuL,QAAQ,CAACtD,MAA/C,KACAgJ,UADA,IAEAP,gBAAgB,CAACO,UAAU,CAAC9E,UAAZ,CAHlB,EAIE;MACA,MAAM8D,eAAe,CAAC2F,qBAAD,EAAwBH,gBAAxB,EAA0C;QAC7DxE,UAAU,EAAA9S,QAAA,KACL8S,UADK;UAER7E,UAAU,EAAEb,QAAQ,CAACpR;SAHsC;QAK7D;QACA8T,kBAAkB,EAAES;MANyC,CAA1C,CAArB;IAQD,CAbD,MAaO;MACL;MACA;MACA,MAAMuB,eAAe,CAAC2F,qBAAD,EAAwBH,gBAAxB,EAA0C;QAC7DjE,kBAAkB,EAAE;UAClBnY,KAAK,EAAE,SADW;UAElBc,QAAQ,EAAEsb,gBAFQ;UAGlBtJ,UAAU,EAAE8E,UAAU,GAAGA,UAAU,CAAC9E,UAAd,GAA2B7S,SAH/B;UAIlB8S,UAAU,EAAE6E,UAAU,GAAGA,UAAU,CAAC7E,UAAd,GAA2B9S,SAJ/B;UAKlB+S,WAAW,EAAE4E,UAAU,GAAGA,UAAU,CAAC5E,WAAd,GAA4B/S,SALjC;UAMlBgT,QAAQ,EAAE2E,UAAU,GAAGA,UAAU,CAAC3E,QAAd,GAAyBhT;SAPc;QAS7D;QACA2U,kBAAkB,EAAES;MAVyC,CAA1C,CAArB;IAYD;EACF;EAED,eAAemF,8BAAfA,CACEgC,cADF,EAEElV,OAFF,EAGEyS,aAHF,EAIE0C,cAJF,EAKEhE,OALF,EAKkB;IAEhB;IACA;IACA;IACA,IAAI4B,OAAO,GAAG,MAAM1K,OAAO,CAAC+M,GAAR,CAAY,CAC9B,GAAG3C,aAAa,CAACna,GAAd,CAAmBkL,KAAD,IACnB0O,kBAAkB,CAAC,QAAD,EAAWf,OAAX,EAAoB3N,KAApB,EAA2BxD,OAA3B,EAAoCkN,MAAM,CAACvN,QAA3C,CADjB,CAD2B,EAI9B,GAAGwV,cAAc,CAAC7c,GAAf,CAAoB+c,CAAD,IACpBnD,kBAAkB,CAChB,QADgB,EAEhBd,uBAAuB,CAAChK,IAAI,CAACnN,OAAN,EAAeob,CAAC,CAAChb,IAAjB,EAAuB8W,OAAO,CAACxI,MAA/B,CAFP,EAGhB0M,CAAC,CAAC7R,KAHc,EAIhB6R,CAAC,CAACrV,OAJc,EAKhBkN,MAAM,CAACvN,QALS,CADjB,CAJ2B,CAAZ,CAApB;IAcA,IAAIqT,aAAa,GAAGD,OAAO,CAACxW,KAAR,CAAc,CAAd,EAAiBkW,aAAa,CAAC5Z,MAA/B,CAApB;IACA,IAAIoa,cAAc,GAAGF,OAAO,CAACxW,KAAR,CAAckW,aAAa,CAAC5Z,MAA5B,CAArB;IAEA,MAAMwP,OAAO,CAAC+M,GAAR,CAAY,CAChBE,sBAAsB,CACpBJ,cADoB,EAEpBzC,aAFoB,EAGpBO,aAHoB,EAIpB7B,OAAO,CAACxI,MAJY,EAKpB,KALoB,EAMpBjQ,KAAK,CAAC8U,UANc,CADN,EAShB8H,sBAAsB,CACpBJ,cADoB,EAEpBC,cAAc,CAAC7c,GAAf,CAAoB+c,CAAD,IAAOA,CAAC,CAAC7R,KAA5B,CAFoB,EAGpByP,cAHoB,EAIpB9B,OAAO,CAACxI,MAJY,EAKpB,IALoB,CATN,CAAZ,CAAN;IAkBA,OAAO;MAAEoK,OAAF;MAAWC,aAAX;MAA0BC;KAAjC;EACD;EAED,SAAStC,oBAATA,CAAA,EAA6B;IAC3B;IACAzC,sBAAsB,GAAG,IAAzB,CAF2B;IAK3B;;IACAC,uBAAuB,CAAC1T,IAAxB,CAA6B,GAAGwW,qBAAqB,EAArD,EAN2B;;IAS3BvC,gBAAgB,CAAC1N,OAAjB,CAAyB,CAACgD,CAAD,EAAIzK,GAAJ,KAAW;MAClC,IAAI8U,gBAAgB,CAAChP,GAAjB,CAAqB9F,GAArB,CAAJ,EAA+B;QAC7B6U,qBAAqB,CAAC3T,IAAtB,CAA2BlB,GAA3B;QACAoa,YAAY,CAACpa,GAAD,CAAZ;MACD;KAJH;EAMD;EAED,SAASqa,eAATA,CAAyBra,GAAzB,EAAsC0Y,OAAtC,EAAuD7T,KAAvD,EAAiE;IAC/D,IAAIkU,aAAa,GAAGhB,mBAAmB,CAAC5Y,KAAK,CAACsH,OAAP,EAAgBiS,OAAhB,CAAvC;IACAxC,aAAa,CAAClW,GAAD,CAAb;IACA8V,WAAW,CAAC;MACV3B,MAAM,EAAE;QACN,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,GAA0Bd;OAFlB;MAIVuP,QAAQ,EAAE,IAAIC,GAAJ,CAAQlV,KAAK,CAACiV,QAAd;IAJA,CAAD,CAAX;EAMD;EAED,SAAS8B,aAATA,CAAuBlW,GAAvB,EAAkC;IAChC,IAAI8U,gBAAgB,CAAChP,GAAjB,CAAqB9F,GAArB,CAAJ,EAA+Boa,YAAY,CAACpa,GAAD,CAAZ;IAC/BmV,gBAAgB,CAACnF,MAAjB,CAAwBhQ,GAAxB;IACAiV,cAAc,CAACjF,MAAf,CAAsBhQ,GAAtB;IACAkV,gBAAgB,CAAClF,MAAjB,CAAwBhQ,GAAxB;IACAb,KAAK,CAACiV,QAAN,CAAepE,MAAf,CAAsBhQ,GAAtB;EACD;EAED,SAASoa,YAATA,CAAsBpa,GAAtB,EAAiC;IAC/B,IAAIgP,UAAU,GAAG8F,gBAAgB,CAAChF,GAAjB,CAAqB9P,GAArB,CAAjB;IACAkD,SAAS,CAAC8L,UAAD,EAA2C,gCAAAhP,GAA3C,CAAT;IACAgP,UAAU,CAACsB,KAAX;IACAwE,gBAAgB,CAAC9E,MAAjB,CAAwBhQ,GAAxB;EACD;EAED,SAASgc,gBAATA,CAA0BtF,IAA1B,EAAwC;IACtC,KAAK,IAAI1W,GAAT,IAAgB0W,IAAhB,EAAsB;MACpB,IAAI4C,OAAO,GAAGY,UAAU,CAACla,GAAD,CAAxB;MACA,IAAImb,WAAW,GAA0B;QACvChc,KAAK,EAAE,MADgC;QAEvCyO,IAAI,EAAE0L,OAAO,CAAC1L,IAFyB;QAGvCqE,UAAU,EAAE7S,SAH2B;QAIvC8S,UAAU,EAAE9S,SAJ2B;QAKvC+S,WAAW,EAAE/S,SAL0B;QAMvCgT,QAAQ,EAAEhT,SAN6B;QAOvC,2BAA6B;OAP/B;MASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBmb,WAAxB;IACD;EACF;EAED,SAASpB,sBAATA,CAAA,EAA+B;IAC7B,IAAIkC,QAAQ,GAAG,EAAf;IACA,KAAK,IAAIjc,GAAT,IAAgBkV,gBAAhB,EAAkC;MAChC,IAAIoE,OAAO,GAAGna,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmB9P,GAAnB,CAAd;MACAkD,SAAS,CAACoW,OAAD,EAA+B,uBAAAtZ,GAA/B,CAAT;MACA,IAAIsZ,OAAO,CAACna,KAAR,KAAkB,SAAtB,EAAiC;QAC/B+V,gBAAgB,CAAClF,MAAjB,CAAwBhQ,GAAxB;QACAic,QAAQ,CAAC/a,IAAT,CAAclB,GAAd;MACD;IACF;IACDgc,gBAAgB,CAACC,QAAD,CAAhB;EACD;EAED,SAAShC,oBAATA,CAA8BiC,QAA9B,EAA8C;IAC5C,IAAIC,UAAU,GAAG,EAAjB;IACA,KAAK,IAAI,CAACnc,GAAD,EAAM2F,EAAN,CAAT,IAAsBsP,cAAtB,EAAsC;MACpC,IAAItP,EAAE,GAAGuW,QAAT,EAAmB;QACjB,IAAI5C,OAAO,GAAGna,KAAK,CAACiV,QAAN,CAAetE,GAAf,CAAmB9P,GAAnB,CAAd;QACAkD,SAAS,CAACoW,OAAD,EAA+B,uBAAAtZ,GAA/B,CAAT;QACA,IAAIsZ,OAAO,CAACna,KAAR,KAAkB,SAAtB,EAAiC;UAC/Bib,YAAY,CAACpa,GAAD,CAAZ;UACAiV,cAAc,CAACjF,MAAf,CAAsBhQ,GAAtB;UACAmc,UAAU,CAACjb,IAAX,CAAgBlB,GAAhB;QACD;MACF;IACF;IACDgc,gBAAgB,CAACG,UAAD,CAAhB;IACA,OAAOA,UAAU,CAAC7c,MAAX,GAAoB,CAA3B;EACD;EAED,SAAS8c,UAATA,CAAoBpc,GAApB,EAAiC4B,EAAjC,EAAoD;IAClD,IAAIya,OAAO,GAAYld,KAAK,CAACmV,QAAN,CAAexE,GAAf,CAAmB9P,GAAnB,KAA2BsS,YAAlD;IAEA,IAAIgD,gBAAgB,CAACxF,GAAjB,CAAqB9P,GAArB,MAA8B4B,EAAlC,EAAsC;MACpC0T,gBAAgB,CAACpH,GAAjB,CAAqBlO,GAArB,EAA0B4B,EAA1B;MACA,IAAIyT,aAAa,IAAI,IAArB,EAA2B;QACzB;QACAA,aAAa,GAAGrV,GAAhB;MACD,CAHD,MAGO,IAAIA,GAAG,KAAKqV,aAAZ,EAA2B;QAChC/R,OAAO,CAAC,KAAD,EAAQ,8CAAR,CAAP;MACD;IACF;IAED,OAAO+Y,OAAP;EACD;EAED,SAASxG,aAATA,CAAuB7V,GAAvB,EAAkC;IAChCb,KAAK,CAACmV,QAAN,CAAetE,MAAf,CAAsBhQ,GAAtB;IACAsV,gBAAgB,CAACtF,MAAjB,CAAwBhQ,GAAxB;IACA,IAAIqV,aAAa,KAAKrV,GAAtB,EAA2B;MACzBqV,aAAa,GAAG,IAAhB;IACD;EACF,CAj+C0C;;EAo+C3C,SAASO,aAATA,CAAuB5V,GAAvB,EAAoCsc,UAApC,EAAuD;IACrD,IAAID,OAAO,GAAGld,KAAK,CAACmV,QAAN,CAAexE,GAAf,CAAmB9P,GAAnB,KAA2BsS,YAAzC,CADqD;IAIrD;;IACApP,SAAS,CACNmZ,OAAO,CAACld,KAAR,KAAkB,WAAlB,IAAiCmd,UAAU,CAACnd,KAAX,KAAqB,SAAvD,IACGkd,OAAO,CAACld,KAAR,KAAkB,SAAlB,IAA+Bmd,UAAU,CAACnd,KAAX,KAAqB,SADvD,IAEGkd,OAAO,CAACld,KAAR,KAAkB,SAAlB,IAA+Bmd,UAAU,CAACnd,KAAX,KAAqB,YAFvD,IAGGkd,OAAO,CAACld,KAAR,KAAkB,SAAlB,IAA+Bmd,UAAU,CAACnd,KAAX,KAAqB,WAHvD,IAIGkd,OAAO,CAACld,KAAR,KAAkB,YAAlB,IAAkCmd,UAAU,CAACnd,KAAX,KAAqB,WALnD,yCAM8Bkd,OAAO,CAACld,KANtC,YAMkDmd,UAAU,CAACnd,KAN7D,CAAT;IASAA,KAAK,CAACmV,QAAN,CAAepG,GAAf,CAAmBlO,GAAnB,EAAwBsc,UAAxB;IACAxG,WAAW,CAAC;MAAExB,QAAQ,EAAE,IAAID,GAAJ,CAAQlV,KAAK,CAACmV,QAAd;IAAZ,CAAD,CAAX;EACD;EAED,SAASoB,qBAATA,CAQC7E,KAAA;IAAA,IAR8B;MAC7B8E,eAD6B;MAE7BvU,YAF6B;MAG7BwS;KAKD,GAAA/C,KAAA;IACC,IAAIwE,aAAa,IAAI,IAArB,EAA2B;MACzB;IACD,CAHF;IAMC;;IACA,IAAIkH,eAAe,GAAGjH,gBAAgB,CAACxF,GAAjB,CAAqBuF,aAArB,CAAtB;IACAnS,SAAS,CACPqZ,eADO,EAEP,kDAFO,CAAT;IAIA,IAAIF,OAAO,GAAGld,KAAK,CAACmV,QAAN,CAAexE,GAAf,CAAmBuF,aAAnB,CAAd;IAEA,IAAIgH,OAAO,IAAIA,OAAO,CAACld,KAAR,KAAkB,YAAjC,EAA+C;MAC7C;MACA;MACA;IACD,CAlBF;IAqBC;;IACA,IAAIod,eAAe,CAAC;MAAE5G,eAAF;MAAmBvU,YAAnB;MAAiCwS;IAAjC,CAAD,CAAnB,EAAuE;MACrE,OAAOyB,aAAP;IACD;EACF;EAED,SAASqC,qBAATA,CACE8E,SADF,EAC0C;IAExC,IAAIC,iBAAiB,GAAa,EAAlC;IACArH,eAAe,CAAC3N,OAAhB,CAAwB,CAACiV,GAAD,EAAMhE,OAAN,KAAiB;MACvC,IAAI,CAAC8D,SAAD,IAAcA,SAAS,CAAC9D,OAAD,CAA3B,EAAsC;QACpC;QACA;QACA;QACAgE,GAAG,CAACrM,MAAJ;QACAoM,iBAAiB,CAACvb,IAAlB,CAAuBwX,OAAvB;QACAtD,eAAe,CAACpF,MAAhB,CAAuB0I,OAAvB;MACD;KARH;IAUA,OAAO+D,iBAAP;EACD,CAxiD0C;EA2iD3C;;EACA,SAASE,uBAATA,CACEC,SADF,EAEEC,WAFF,EAGEC,MAHF,EAG0C;IAExC/J,oBAAoB,GAAG6J,SAAvB;IACA3J,iBAAiB,GAAG4J,WAApB;IACA7J,uBAAuB,GAAG8J,MAAM,KAAM7c,QAAD,IAAcA,QAAQ,CAACD,GAA5B,CAAhC,CAJwC;IAOxC;IACA;;IACA,IAAI,CAACkT,qBAAD,IAA0B/T,KAAK,CAAC0U,UAAN,KAAqB7B,eAAnD,EAAoE;MAClEkB,qBAAqB,GAAG,IAAxB;MACA,IAAI6J,CAAC,GAAGnG,sBAAsB,CAACzX,KAAK,CAACc,QAAP,EAAiBd,KAAK,CAACsH,OAAvB,CAA9B;MACA,IAAIsW,CAAC,IAAI,IAAT,EAAe;QACbjH,WAAW,CAAC;UAAEhC,qBAAqB,EAAEiJ;QAAzB,CAAD,CAAX;MACD;IACF;IAED,OAAO,MAAK;MACVhK,oBAAoB,GAAG,IAAvB;MACAE,iBAAiB,GAAG,IAApB;MACAD,uBAAuB,GAAG,IAA1B;KAHF;EAKD;EAED,SAASuE,kBAATA,CACEtX,QADF,EAEEwG,OAFF,EAEmC;IAEjC,IAAIsM,oBAAoB,IAAIC,uBAAxB,IAAmDC,iBAAvD,EAA0E;MACxE,IAAI+J,WAAW,GAAGvW,OAAO,CAAC1H,GAAR,CAAa0U,CAAD,IAC5BwJ,qBAAqB,CAACxJ,CAAD,EAAItU,KAAK,CAAC8U,UAAV,CADL,CAAlB;MAGA,IAAIjU,GAAG,GAAGgT,uBAAuB,CAAC/S,QAAD,EAAW+c,WAAX,CAAvB,IAAkD/c,QAAQ,CAACD,GAArE;MACA+S,oBAAoB,CAAC/S,GAAD,CAApB,GAA4BiT,iBAAiB,EAA7C;IACD;EACF;EAED,SAAS2D,sBAATA,CACE3W,QADF,EAEEwG,OAFF,EAEmC;IAEjC,IAAIsM,oBAAoB,IAAIC,uBAAxB,IAAmDC,iBAAvD,EAA0E;MACxE,IAAI+J,WAAW,GAAGvW,OAAO,CAAC1H,GAAR,CAAa0U,CAAD,IAC5BwJ,qBAAqB,CAACxJ,CAAD,EAAItU,KAAK,CAAC8U,UAAV,CADL,CAAlB;MAGA,IAAIjU,GAAG,GAAGgT,uBAAuB,CAAC/S,QAAD,EAAW+c,WAAX,CAAvB,IAAkD/c,QAAQ,CAACD,GAArE;MACA,IAAI+c,CAAC,GAAGhK,oBAAoB,CAAC/S,GAAD,CAA5B;MACA,IAAI,OAAO+c,CAAP,KAAa,QAAjB,EAA2B;QACzB,OAAOA,CAAP;MACD;IACF;IACD,OAAO,IAAP;EACD;EAEDpJ,MAAM,GAAG;IACP,IAAIvN,QAAJA,CAAA,EAAY;MACV,OAAOyH,IAAI,CAACzH,QAAZ;KAFK;IAIP,IAAIjH,KAAJA,CAAA,EAAS;MACP,OAAOA,KAAP;KALK;IAOP,IAAImG,MAAJA,CAAA,EAAU;MACR,OAAOuN,UAAP;KARK;IAUP2C,UAVO;IAWPpF,SAXO;IAYPuM,uBAZO;IAaP9F,QAbO;IAcPsD,KAdO;IAePhD,UAfO;IAgBP;IACA;IACA3W,UAAU,EAAGT,EAAD,IAAY8N,IAAI,CAACnN,OAAL,CAAaF,UAAb,CAAwBT,EAAxB,CAlBjB;IAmBPc,cAAc,EAAGd,EAAD,IAAY8N,IAAI,CAACnN,OAAL,CAAaG,cAAb,CAA4Bd,EAA5B,CAnBrB;IAoBPma,UApBO;IAqBPhE,aArBO;IAsBPF,OAtBO;IAuBPoG,UAvBO;IAwBPvG,aAxBO;IAyBPqH,yBAAyB,EAAEpI,gBAzBpB;IA0BPqI,wBAAwB,EAAE/H;GA1B5B;EA6BA,OAAOzB,MAAP;AACD;AAGD;AACA;AACA;;MAEayJ,sBAAsB,GAAGC,MAAM,CAAC,UAAD;AAE5B,SAAAC,oBACdhY,MADc,EAEdwR,IAFc,EAIb;EAED5T,SAAS,CACPoC,MAAM,CAAChG,MAAP,GAAgB,CADT,EAEP,kEAFO,CAAT;EAKA,IAAIuT,UAAU,GAAGxN,yBAAyB,CAACC,MAAD,CAA1C;EACA,IAAIc,QAAQ,GAAG,CAAC0Q,IAAI,GAAGA,IAAI,CAAC1Q,QAAR,GAAmB,IAAxB,KAAiC,GAAhD;EAEA;;;;;;;;;;;;;;;;;;AAkBG;;EACH,eAAemX,KAAfA,CACE3F,OADF,EAEuD4F,MAAA;IAAA,IAArD;MAAEC;IAAF,CAAqD,GAAAD,MAAA,cAAF,EAAE,GAAAA,MAAA;IAErD,IAAI3a,GAAG,GAAG,IAAIjC,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,CAAV;IACA,IAAI4V,MAAM,GAAGb,OAAO,CAACa,MAAR,CAAe5M,WAAf,EAAb;IACA,IAAI5L,QAAQ,GAAGC,cAAc,CAAC,EAAD,EAAKO,UAAU,CAACoC,GAAD,CAAf,EAAsB,IAAtB,EAA4B,SAA5B,CAA7B;IACA,IAAI4D,OAAO,GAAGP,WAAW,CAAC2M,UAAD,EAAa5S,QAAb,EAAuBmG,QAAvB,CAAzB,CALqD;;IAQrD,IAAI,CAACsX,aAAa,CAACjF,MAAD,CAAd,IAA0BA,MAAM,KAAK,MAAzC,EAAiD;MAC/C,IAAI5T,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QAAEmF;MAAF,CAAN,CAAlC;MACA,IAAI;QAAEhS,OAAO,EAAEkX,uBAAX;QAAoCvY;OACtC,GAAAmO,sBAAsB,CAACV,UAAD,CADxB;MAEA,OAAO;QACLzM,QADK;QAELnG,QAFK;QAGLwG,OAAO,EAAEkX,uBAHJ;QAIL1J,UAAU,EAAE,EAJP;QAKLC,UAAU,EAAE,IALP;QAMLC,MAAM,EAAE;UACN,CAAC/O,KAAK,CAACO,EAAP,GAAYd;SAPT;QASL+Y,UAAU,EAAE/Y,KAAK,CAACkJ,MATb;QAUL8P,aAAa,EAAE,EAVV;QAWLC,aAAa,EAAE,EAXV;QAYL1I,eAAe,EAAE;OAZnB;IAcD,CAlBD,MAkBO,IAAI,CAAC3O,OAAL,EAAc;MACnB,IAAI5B,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QAAEnT,QAAQ,EAAEF,QAAQ,CAACE;MAArB,CAAN,CAAlC;MACA,IAAI;QAAEsG,OAAO,EAAEgR,eAAX;QAA4BrS;OAC9B,GAAAmO,sBAAsB,CAACV,UAAD,CADxB;MAEA,OAAO;QACLzM,QADK;QAELnG,QAFK;QAGLwG,OAAO,EAAEgR,eAHJ;QAILxD,UAAU,EAAE,EAJP;QAKLC,UAAU,EAAE,IALP;QAMLC,MAAM,EAAE;UACN,CAAC/O,KAAK,CAACO,EAAP,GAAYd;SAPT;QASL+Y,UAAU,EAAE/Y,KAAK,CAACkJ,MATb;QAUL8P,aAAa,EAAE,EAVV;QAWLC,aAAa,EAAE,EAXV;QAYL1I,eAAe,EAAE;OAZnB;IAcD;IAED,IAAI9M,MAAM,GAAG,MAAMyV,SAAS,CAACnG,OAAD,EAAU3X,QAAV,EAAoBwG,OAApB,EAA6BgX,cAA7B,CAA5B;IACA,IAAIO,UAAU,CAAC1V,MAAD,CAAd,EAAwB;MACtB,OAAOA,MAAP;IACD,CAjDoD;IAoDrD;IACA;;IACA,OAAArE,QAAA;MAAShE,QAAT;MAAmBmG;IAAnB,GAAgCkC,MAAhC;EACD;EAED;;;;;;;;;;;;;;;;;;;AAmBG;;EACH,eAAe2V,UAAfA,CACErG,OADF,EAKwDsG,MAAA;IAAA,IAHtD;MACExF,OADF;MAEE+E;IAFF,CAGsD,GAAAS,MAAA,cAAF,EAAE,GAAAA,MAAA;IAEtD,IAAIrb,GAAG,GAAG,IAAIjC,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,CAAV;IACA,IAAI4V,MAAM,GAAGb,OAAO,CAACa,MAAR,CAAe5M,WAAf,EAAb;IACA,IAAI5L,QAAQ,GAAGC,cAAc,CAAC,EAAD,EAAKO,UAAU,CAACoC,GAAD,CAAf,EAAsB,IAAtB,EAA4B,SAA5B,CAA7B;IACA,IAAI4D,OAAO,GAAGP,WAAW,CAAC2M,UAAD,EAAa5S,QAAb,EAAuBmG,QAAvB,CAAzB,CALsD;;IAQtD,IAAI,CAACsX,aAAa,CAACjF,MAAD,CAAd,IAA0BA,MAAM,KAAK,MAArC,IAA+CA,MAAM,KAAK,SAA9D,EAAyE;MACvE,MAAMnF,sBAAsB,CAAC,GAAD,EAAM;QAAEmF;MAAF,CAAN,CAA5B;IACD,CAFD,MAEO,IAAI,CAAChS,OAAL,EAAc;MACnB,MAAM6M,sBAAsB,CAAC,GAAD,EAAM;QAAEnT,QAAQ,EAAEF,QAAQ,CAACE;MAArB,CAAN,CAA5B;IACD;IAED,IAAI8J,KAAK,GAAGyO,OAAO,GACfjS,OAAO,CAAC0X,IAAR,CAAc1K,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAe+S,OAAnC,CADe,GAEfH,cAAc,CAAC9R,OAAD,EAAUxG,QAAV,CAFlB;IAIA,IAAIyY,OAAO,IAAI,CAACzO,KAAhB,EAAuB;MACrB,MAAMqJ,sBAAsB,CAAC,GAAD,EAAM;QAChCnT,QAAQ,EAAEF,QAAQ,CAACE,QADa;QAEhCuY;MAFgC,CAAN,CAA5B;IAID,CALD,MAKO,IAAI,CAACzO,KAAL,EAAY;MACjB;MACA,MAAMqJ,sBAAsB,CAAC,GAAD,EAAM;QAAEnT,QAAQ,EAAEF,QAAQ,CAACE;MAArB,CAAN,CAA5B;IACD;IAED,IAAImI,MAAM,GAAG,MAAMyV,SAAS,CAC1BnG,OAD0B,EAE1B3X,QAF0B,EAG1BwG,OAH0B,EAI1BgX,cAJ0B,EAK1BxT,KAL0B,CAA5B;IAOA,IAAI+T,UAAU,CAAC1V,MAAD,CAAd,EAAwB;MACtB,OAAOA,MAAP;IACD;IAED,IAAIzD,KAAK,GAAGyD,MAAM,CAAC6L,MAAP,GAAgBhK,MAAM,CAACiU,MAAP,CAAc9V,MAAM,CAAC6L,MAArB,EAA6B,CAA7B,CAAhB,GAAkD/U,SAA9D;IACA,IAAIyF,KAAK,KAAKzF,SAAd,EAAyB;MACvB;MACA;MACA;MACA;MACA,MAAMyF,KAAN;IACD,CA9CqD;;IAiDtD,IAAIyD,MAAM,CAAC4L,UAAX,EAAuB;MACrB,OAAO/J,MAAM,CAACiU,MAAP,CAAc9V,MAAM,CAAC4L,UAArB,CAAiC,EAAjC,CAAP;IACD;IAED,IAAI5L,MAAM,CAAC2L,UAAX,EAAuB;MAAA,IAAAoK,qBAAA;MACrB,IAAIzQ,IAAI,GAAGzD,MAAM,CAACiU,MAAP,CAAc9V,MAAM,CAAC2L,UAArB,CAAiC,EAAjC,CAAX;MACA,IAAI,CAAAoK,qBAAA,GAAA/V,MAAM,CAAC8M,eAAX,KAAI,QAAAiJ,qBAAA,CAAyBpU,KAAK,CAAC7E,KAAN,CAAYO,EAArC,CAAJ,EAA8C;QAC5CiI,IAAI,CAACwP,sBAAD,CAAJ,GAA+B9U,MAAM,CAAC8M,eAAP,CAAuBnL,KAAK,CAAC7E,KAAN,CAAYO,EAAnC,CAA/B;MACD;MACD,OAAOiI,IAAP;IACD;IAED,OAAOxO,SAAP;EACD;EAED,eAAe2e,SAAfA,CACEnG,OADF,EAEE3X,QAFF,EAGEwG,OAHF,EAIEgX,cAJF,EAKEa,UALF,EAKqC;IAEnCpb,SAAS,CACP0U,OAAO,CAACxI,MADD,EAEP,sEAFO,CAAT;IAKA,IAAI;MACF,IAAIoH,gBAAgB,CAACoB,OAAO,CAACa,MAAR,CAAe5M,WAAf,EAAD,CAApB,EAAoD;QAClD,IAAIvD,MAAM,GAAG,MAAMiW,MAAM,CACvB3G,OADuB,EAEvBnR,OAFuB,EAGvB6X,UAAU,IAAI/F,cAAc,CAAC9R,OAAD,EAAUxG,QAAV,CAHL,EAIvBwd,cAJuB,EAKvBa,UAAU,IAAI,IALS,CAAzB;QAOA,OAAOhW,MAAP;MACD;MAED,IAAIA,MAAM,GAAG,MAAMkW,aAAa,CAC9B5G,OAD8B,EAE9BnR,OAF8B,EAG9BgX,cAH8B,EAI9Ba,UAJ8B,CAAhC;MAMA,OAAON,UAAU,CAAC1V,MAAD,CAAV,GACHA,MADG,GAAArE,QAAA,KAGEqE,MAHF;QAID4L,UAAU,EAAE,IAJX;QAKD4J,aAAa,EAAE;OALrB;KAlBF,CAyBE,OAAOpa,CAAP,EAAU;MACV;MACA;MACA;MACA,IAAI+a,oBAAoB,CAAC/a,CAAD,CAAxB,EAA6B;QAC3B,IAAIA,CAAC,CAAC8U,IAAF,KAAWtT,UAAU,CAACL,KAAtB,IAA+B,CAAC6Z,kBAAkB,CAAChb,CAAC,CAACib,QAAH,CAAtD,EAAoE;UAClE,MAAMjb,CAAC,CAACib,QAAR;QACD;QACD,OAAOjb,CAAC,CAACib,QAAT;MACD,CATS;MAWV;;MACA,IAAID,kBAAkB,CAAChb,CAAD,CAAtB,EAA2B;QACzB,OAAOA,CAAP;MACD;MACD,MAAMA,CAAN;IACD;EACF;EAED,eAAe6a,MAAfA,CACE3G,OADF,EAEEnR,OAFF,EAGE6R,WAHF,EAIEmF,cAJF,EAKEmB,cALF,EAKyB;IAEvB,IAAItW,MAAJ;IAEA,IAAI,CAACgQ,WAAW,CAAClT,KAAZ,CAAkB7F,MAAvB,EAA+B;MAC7B,IAAIsF,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QACtCmF,MAAM,EAAEb,OAAO,CAACa,MADsB;QAEtCtY,QAAQ,EAAE,IAAIS,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,EAAqB1C,QAFO;QAGtCuY,OAAO,EAAEJ,WAAW,CAAClT,KAAZ,CAAkBO;MAHW,CAAN,CAAlC;MAKA,IAAIiZ,cAAJ,EAAoB;QAClB,MAAM/Z,KAAN;MACD;MACDyD,MAAM,GAAG;QACPkQ,IAAI,EAAEtT,UAAU,CAACL,KADV;QAEPA;OAFF;IAID,CAbD,MAaO;MACLyD,MAAM,GAAG,MAAMqQ,kBAAkB,CAC/B,QAD+B,EAE/Bf,OAF+B,EAG/BU,WAH+B,EAI/B7R,OAJ+B,EAK/BL,QAL+B,EAM/B,IAN+B,EAO/BwY,cAP+B,EAQ/BnB,cAR+B,CAAjC;MAWA,IAAI7F,OAAO,CAACxI,MAAR,CAAeW,OAAnB,EAA4B;QAC1B,IAAI0I,MAAM,GAAGmG,cAAc,GAAG,YAAH,GAAkB,OAA7C;QACA,MAAM,IAAIvb,KAAJ,CAAaoV,MAAb,GAAN;MACD;IACF;IAED,IAAIG,gBAAgB,CAACtQ,MAAD,CAApB,EAA8B;MAC5B;MACA;MACA;MACA;MACA,MAAM,IAAI6F,QAAJ,CAAa,IAAb,EAAmB;QACvBJ,MAAM,EAAEzF,MAAM,CAACyF,MADQ;QAEvBC,OAAO,EAAE;UACP6Q,QAAQ,EAAEvW,MAAM,CAACrI;QADV;MAFc,CAAnB,CAAN;IAMD;IAED,IAAI+Y,gBAAgB,CAAC1Q,MAAD,CAApB,EAA8B;MAC5B,IAAIzD,KAAK,GAAGyO,sBAAsB,CAAC,GAAD,EAAM;QAAEkF,IAAI,EAAE;MAAR,CAAN,CAAlC;MACA,IAAIoG,cAAJ,EAAoB;QAClB,MAAM/Z,KAAN;MACD;MACDyD,MAAM,GAAG;QACPkQ,IAAI,EAAEtT,UAAU,CAACL,KADV;QAEPA;OAFF;IAID;IAED,IAAI+Z,cAAJ,EAAoB;MAClB;MACA;MACA,IAAI9F,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;QACzB,MAAMA,MAAM,CAACzD,KAAb;MACD;MAED,OAAO;QACL4B,OAAO,EAAE,CAAC6R,WAAD,CADJ;QAELrE,UAAU,EAAE,EAFP;QAGLC,UAAU,EAAE;UAAE,CAACoE,WAAW,CAAClT,KAAZ,CAAkBO,EAAnB,GAAwB2C,MAAM,CAACsF;SAHxC;QAILuG,MAAM,EAAE,IAJH;QAKL;QACA;QACAyJ,UAAU,EAAE,GAPP;QAQLC,aAAa,EAAE,EARV;QASLC,aAAa,EAAE,EATV;QAUL1I,eAAe,EAAE;OAVnB;IAYD;IAED,IAAI0D,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIyQ,aAAa,GAAGhB,mBAAmB,CAACtR,OAAD,EAAU6R,WAAW,CAAClT,KAAZ,CAAkBO,EAA5B,CAAvC;MACA,IAAImZ,OAAO,GAAG,MAAMN,aAAa,CAC/B5G,OAD+B,EAE/BnR,OAF+B,EAG/BgX,cAH+B,EAI/Bre,SAJ+B,EAK/B;QACE,CAAC2Z,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,GAA0B2C,MAAM,CAACzD;OANJ,CAAjC,CAJyB;;MAezB,OAAAZ,QAAA,KACK6a,OADL;QAEElB,UAAU,EAAEnM,oBAAoB,CAACnJ,MAAM,CAACzD,KAAR,CAApB,GACRyD,MAAM,CAACzD,KAAP,CAAakJ,MADL,GAER,GAJN;QAKEmG,UAAU,EAAE,IALd;QAME4J,aAAa,EACP7Z,QAAA,KAAAqE,MAAM,CAAC0F,OAAP,GAAiB;UAAE,CAACsK,WAAW,CAAClT,KAAZ,CAAkBO,EAAnB,GAAwB2C,MAAM,CAAC0F;QAAjC,CAAjB,GAA8D,EADvD;MANf;IAUD,CAzGsB;;IA4GvB,IAAI+Q,aAAa,GAAG,IAAI3G,OAAJ,CAAYR,OAAO,CAAC/U,GAApB,EAAyB;MAC3CmL,OAAO,EAAE4J,OAAO,CAAC5J,OAD0B;MAE3CqD,QAAQ,EAAEuG,OAAO,CAACvG,QAFyB;MAG3CjC,MAAM,EAAEwI,OAAO,CAACxI;IAH2B,CAAzB,CAApB;IAKA,IAAI0P,OAAO,GAAG,MAAMN,aAAa,CAACO,aAAD,EAAgBtY,OAAhB,EAAyBgX,cAAzB,CAAjC;IAEA,OAAAxZ,QAAA,KACK6a,OADL,EAGMxW,MAAM,CAACsV,UAAP,GAAoB;MAAEA,UAAU,EAAEtV,MAAM,CAACsV;IAArB,CAApB,GAAwD,EAH9D;MAIE1J,UAAU,EAAE;QACV,CAACoE,WAAW,CAAClT,KAAZ,CAAkBO,EAAnB,GAAwB2C,MAAM,CAACsF;OALnC;MAOEkQ,aAAa,EACP7Z,QAAA,KAAAqE,MAAM,CAAC0F,OAAP,GAAiB;QAAE,CAACsK,WAAW,CAAClT,KAAZ,CAAkBO,EAAnB,GAAwB2C,MAAM,CAAC0F;MAAjC,CAAjB,GAA8D,EADvD;IAPf;EAWD;EAED,eAAewQ,aAAfA,CACE5G,OADF,EAEEnR,OAFF,EAGEgX,cAHF,EAIEa,UAJF,EAKEnG,kBALF,EAKgC;IAQ9B,IAAIyG,cAAc,GAAGN,UAAU,IAAI,IAAnC,CAR8B;;IAW9B,IAAIM,cAAc,IAAI,EAACN,UAAD,YAACA,UAAU,CAAElZ,KAAZ,CAAkBsO,MAAnB,CAAtB,EAAiD;MAC/C,MAAMJ,sBAAsB,CAAC,GAAD,EAAM;QAChCmF,MAAM,EAAEb,OAAO,CAACa,MADgB;QAEhCtY,QAAQ,EAAE,IAAIS,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,EAAqB1C,QAFC;QAGhCuY,OAAO,EAAE4F,UAAF,oBAAEA,UAAU,CAAElZ,KAAZ,CAAkBO;MAHK,CAAN,CAA5B;IAKD;IAED,IAAI6U,cAAc,GAAG8D,UAAU,GAC3B,CAACA,UAAD,CAD2B,GAE3BU,6BAA6B,CAC3BvY,OAD2B,EAE3B0D,MAAM,CAACuM,IAAP,CAAYyB,kBAAkB,IAAI,EAAlC,EAAsC,CAAtC,CAF2B,CAFjC;IAMA,IAAIe,aAAa,GAAGsB,cAAc,CAAClR,MAAf,CAAuBmK,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQsO,MAArC,CAApB,CAzB8B;;IA4B9B,IAAIwF,aAAa,CAAC5Z,MAAd,KAAyB,CAA7B,EAAgC;MAC9B,OAAO;QACLmH,OADK;QAEL;QACAwN,UAAU,EAAExN,OAAO,CAAC8C,MAAR,CACV,CAAC8F,GAAD,EAAMoE,CAAN,KAAYtJ,MAAM,CAACrF,MAAP,CAAcuK,GAAd,EAAmB;UAAE,CAACoE,CAAC,CAACrO,KAAF,CAAQO,EAAT,GAAc;SAAnC,CADF,EAEV,EAFU,CAHP;QAOLwO,MAAM,EAAEgE,kBAAkB,IAAI,IAPzB;QAQLyF,UAAU,EAAE,GARP;QASLC,aAAa,EAAE,EATV;QAULzI,eAAe,EAAE;OAVnB;IAYD;IAED,IAAIoE,OAAO,GAAG,MAAM1K,OAAO,CAAC+M,GAAR,CAAY,CAC9B,GAAG3C,aAAa,CAACna,GAAd,CAAmBkL,KAAD,IACnB0O,kBAAkB,CAChB,QADgB,EAEhBf,OAFgB,EAGhB3N,KAHgB,EAIhBxD,OAJgB,EAKhBL,QALgB,EAMhB,IANgB,EAOhBwY,cAPgB,EAQhBnB,cARgB,CADjB,CAD2B,CAAZ,CAApB;IAeA,IAAI7F,OAAO,CAACxI,MAAR,CAAeW,OAAnB,EAA4B;MAC1B,IAAI0I,MAAM,GAAGmG,cAAc,GAAG,YAAH,GAAkB,OAA7C;MACA,MAAM,IAAIvb,KAAJ,CAAaoV,MAAb,GAAN;IACD,CA7D6B;;IAgE9B,IAAIrD,eAAe,GAAG,IAAIf,GAAJ,EAAtB;IACA,IAAIyK,OAAO,GAAGG,sBAAsB,CAClCxY,OADkC,EAElCyS,aAFkC,EAGlCM,OAHkC,EAIlCrB,kBAJkC,EAKlC/C,eALkC,CAApC,CAjE8B;;IA0E9B,IAAI8J,eAAe,GAAG,IAAIzZ,GAAJ,CACpByT,aAAa,CAACna,GAAd,CAAmBkL,KAAD,IAAWA,KAAK,CAAC7E,KAAN,CAAYO,EAAzC,CADoB,CAAtB;IAGAc,OAAO,CAACgB,OAAR,CAAiBwC,KAAD,IAAU;MACxB,IAAI,CAACiV,eAAe,CAACpZ,GAAhB,CAAoBmE,KAAK,CAAC7E,KAAN,CAAYO,EAAhC,CAAL,EAA0C;QACxCmZ,OAAO,CAAC7K,UAAR,CAAmBhK,KAAK,CAAC7E,KAAN,CAAYO,EAA/B,IAAqC,IAArC;MACD;KAHH;IAMA,OAAA1B,QAAA,KACK6a,OADL;MAEErY,OAFF;MAGE2O,eAAe,EACbA,eAAe,CAACzE,IAAhB,GAAuB,CAAvB,GACIxG,MAAM,CAACgV,WAAP,CAAmB/J,eAAe,CAACtW,OAAhB,EAAnB,CADJ,GAEI;IANR;EAQD;EAED,OAAO;IACL+T,UADK;IAEL0K,KAFK;IAGLU;GAHF;AAKD;AAID;AACA;AACA;;AAEA;;;AAGG;;SACamB,0BACd9Z,MAAA,EACAwZ,OAAA,EACAja,KAAA,EAAU;EAEV,IAAIwa,UAAU,GAAApb,QAAA,KACT6a,OADS;IAEZlB,UAAU,EAAE,GAFA;IAGZzJ,MAAM,EAAE;MACN,CAAC2K,OAAO,CAACQ,0BAAR,IAAsCha,MAAM,CAAC,CAAD,CAAN,CAAUK,EAAjD,GAAsDd;IADhD;GAHV;EAOA,OAAOwa,UAAP;AACD;AAED,SAASE,sBAATA,CACEzI,IADF,EAC6B;EAE3B,OAAOA,IAAI,IAAI,IAAR,IAAgB,cAAcA,IAArC;AACD;AAGD;;AACA,SAASE,wBAATA,CACEjX,EADF,EAEE+W,IAFF,EAGE0I,SAHF,EAGmB;EAAA,IAAjBA,SAAiB;IAAjBA,SAAiB,GAAL,KAAK;EAAA;EAMjB,IAAI1e,IAAI,GAAG,OAAOf,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BU,UAAU,CAACV,EAAD,CAAnD,CANiB;;EASjB,IAAI,CAAC+W,IAAD,IAAS,CAACyI,sBAAsB,CAACzI,IAAD,CAApC,EAA4C;IAC1C,OAAO;MAAEhW;KAAT;EACD;EAED,IAAIgW,IAAI,CAAC7E,UAAL,IAAmB,CAACyL,aAAa,CAAC5G,IAAI,CAAC7E,UAAN,CAArC,EAAwD;IACtD,OAAO;MACLnR,IADK;MAEL+D,KAAK,EAAEyO,sBAAsB,CAAC,GAAD,EAAM;QAAEmF,MAAM,EAAE3B,IAAI,CAAC7E;OAArB;KAF/B;EAID,CAlBgB;;EAqBjB,IAAI8E,UAAJ;EACA,IAAID,IAAI,CAAC1E,QAAT,EAAmB;IACjB2E,UAAU,GAAG;MACX9E,UAAU,EAAE6E,IAAI,CAAC7E,UAAL,IAAmB,KADpB;MAEXC,UAAU,EAAEuN,iBAAiB,CAAC3e,IAAD,CAFlB;MAGXqR,WAAW,EACR2E,IAAI,IAAIA,IAAI,CAAC3E,WAAd,IAA8B,mCAJrB;MAKXC,QAAQ,EAAE0E,IAAI,CAAC1E;KALjB;IAQA,IAAIoE,gBAAgB,CAACO,UAAU,CAAC9E,UAAZ,CAApB,EAA6C;MAC3C,OAAO;QAAEnR,IAAF;QAAQiW;OAAf;IACD;EACF,CAlCgB;;EAqCjB,IAAI5S,UAAU,GAAGpD,SAAS,CAACD,IAAD,CAA1B;EACA,IAAI4e,YAAY,GAAGC,6BAA6B,CAAC7I,IAAI,CAAC1E,QAAN,CAAhD,CAtCiB;EAwCjB;EACA;;EACA,IAAIoN,SAAS,IAAIrb,UAAU,CAACnD,MAAxB,IAAkC4e,kBAAkB,CAACzb,UAAU,CAACnD,MAAZ,CAAxD,EAA6E;IAC3E0e,YAAY,CAACG,MAAb,CAAoB,OAApB,EAA6B,EAA7B;EACD;EACD1b,UAAU,CAACnD,MAAX,SAAwB0e,YAAxB;EAEA,OAAO;IAAE5e,IAAI,EAAEL,UAAU,CAAC0D,UAAD,CAAlB;IAAgC4S;GAAvC;AACD;AAGD;;AACA,SAASiI,6BAATA,CACEvY,OADF,EAEEqZ,UAFF,EAEqB;EAEnB,IAAIC,eAAe,GAAGtZ,OAAtB;EACA,IAAIqZ,UAAJ,EAAgB;IACd,IAAI7gB,KAAK,GAAGwH,OAAO,CAACuZ,SAAR,CAAmBvM,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAema,UAAxC,CAAZ;IACA,IAAI7gB,KAAK,IAAI,CAAb,EAAgB;MACd8gB,eAAe,GAAGtZ,OAAO,CAACzD,KAAR,CAAc,CAAd,EAAiB/D,KAAjB,CAAlB;IACD;EACF;EACD,OAAO8gB,eAAP;AACD;AAED,SAAS3G,gBAATA,CACE1Y,OADF,EAEEvB,KAFF,EAGEsH,OAHF,EAIEsQ,UAJF,EAKE9W,QALF,EAME0U,sBANF,EAOEC,uBAPF,EAQEC,qBARF,EASEiD,iBATF,EAUEZ,YAVF,EAWE/B,gBAXF,EAWgD;EAE9C,IAAIyF,YAAY,GAAG1D,YAAY,GAC3B/M,MAAM,CAACiU,MAAP,CAAclH,YAAd,EAA4B,CAA5B,CAD2B,GAE3BY,iBAAiB,GACjB3N,MAAM,CAACiU,MAAP,CAActG,iBAAd,CAAiC,EAAjC,CADiB,GAEjB1Y,SAJJ;EAMA,IAAI6gB,UAAU,GAAGvf,OAAO,CAACC,SAAR,CAAkBxB,KAAK,CAACc,QAAxB,CAAjB;EACA,IAAIigB,OAAO,GAAGxf,OAAO,CAACC,SAAR,CAAkBV,QAAlB,CAAd;EAEA,IAAIkgB,uBAAuB;EAAA;EAEzBxL,sBAAsB;EAAA;EAEtBsL,UAAU,CAACpc,QAAX,OAA0Bqc,OAAO,CAACrc,QAAR,EAF1B;EAAA;EAIAoc,UAAU,CAACjf,MAAX,KAAsBkf,OAAO,CAAClf,MANhC,CAX8C;;EAoB9C,IAAI8e,UAAU,GAAG5I,YAAY,GAAG/M,MAAM,CAACuM,IAAP,CAAYQ,YAAZ,EAA0B,CAA1B,CAAH,GAAkC9X,SAA/D;EACA,IAAI2gB,eAAe,GAAGf,6BAA6B,CAACvY,OAAD,EAAUqZ,UAAV,CAAnD;EAEA,IAAIM,iBAAiB,GAAGL,eAAe,CAACzW,MAAhB,CAAuB,CAACW,KAAD,EAAQhL,KAAR,KAAiB;IAC9D,IAAIgL,KAAK,CAAC7E,KAAN,CAAYsO,MAAZ,IAAsB,IAA1B,EAAgC;MAC9B,OAAO,KAAP;IACD,CAH6D;;IAM9D,IACE2M,WAAW,CAAClhB,KAAK,CAAC8U,UAAP,EAAmB9U,KAAK,CAACsH,OAAN,CAAcxH,KAAd,CAAnB,EAAyCgL,KAAzC,CAAX,IACA2K,uBAAuB,CAACvL,IAAxB,CAA8B1D,EAAD,IAAQA,EAAE,KAAKsE,KAAK,CAAC7E,KAAN,CAAYO,EAAxD,CAFF,EAGE;MACA,OAAO,IAAP;IACD,CAX6D;IAc9D;IACA;IACA;;IACA,IAAI2a,iBAAiB,GAAGnhB,KAAK,CAACsH,OAAN,CAAcxH,KAAd,CAAxB;IACA,IAAIshB,cAAc,GAAGtW,KAArB;IAEA,OAAOuW,sBAAsB,CAACvW,KAAD,EAAAhG,QAAA;MAC3Bgc,UAD2B;MAE3BQ,aAAa,EAAEH,iBAAiB,CAAClW,MAFN;MAG3B8V,OAH2B;MAI3BQ,UAAU,EAAEH,cAAc,CAACnW;IAJA,GAKxB2M,UALwB;MAM3B6D,YAN2B;MAO3BuF,uBAAuB,EACrBA,uBAAuB,IACvBQ,kBAAkB,CAACL,iBAAD,EAAoBC,cAApB;KATtB;GApBsB,CAAxB,CAvB8C;;EAyD9C,IAAIpH,oBAAoB,GAA0B,EAAlD;EACAhE,gBAAgB,IACdA,gBAAgB,CAAC1N,OAAjB,CAAyB,CAACqU,CAAD,EAAI9b,GAAJ,KAAW;IAClC,IAAI,CAACyG,OAAO,CAAC4C,IAAR,CAAcoK,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAemW,CAAC,CAACpD,OAArC,CAAL,EAAoD;MAClD;MACA;MACA;KAHF,MAIO,IAAI7D,qBAAqB,CAAClN,QAAtB,CAA+B3H,GAA/B,CAAJ,EAAyC;MAC9C;MACAmZ,oBAAoB,CAACjY,IAArB,CAAA+C,QAAA;QAA4BjE;MAA5B,GAAoC8b,CAApC;IACD,CAHM,MAGA;MACL;MACA;MACA;MACA;MACA,IAAI8E,gBAAgB,GAAGJ,sBAAsB,CAAC1E,CAAC,CAAC7R,KAAH,EAAAhG,QAAA;QAC3Cgc,UAD2C;QAE3CQ,aAAa,EAAEthB,KAAK,CAACsH,OAAN,CAActH,KAAK,CAACsH,OAAN,CAAcnH,MAAd,GAAuB,CAArC,EAAwC8K,MAFZ;QAG3C8V,OAH2C;QAI3CQ,UAAU,EAAEja,OAAO,CAACA,OAAO,CAACnH,MAAR,GAAiB,CAAlB,CAAP,CAA4B8K;MAJG,GAKxC2M,UALwC;QAM3C6D,YAN2C;QAO3CuF;OAPF;MASA,IAAIS,gBAAJ,EAAsB;QACpBzH,oBAAoB,CAACjY,IAArB,CAAA+C,QAAA;UAA4BjE;QAA5B,GAAoC8b,CAApC;MACD;IACF;EACF,CA1BD,CADF;EA6BA,OAAO,CAACsE,iBAAD,EAAoBjH,oBAApB,CAAP;AACD;AAED,SAASkH,WAATA,CACEQ,iBADF,EAEEC,YAFF,EAGE7W,KAHF,EAG+B;EAE7B,IAAI8W,KAAK;EAAA;EAEP,CAACD,YAAD;EAAA;EAEA7W,KAAK,CAAC7E,KAAN,CAAYO,EAAZ,KAAmBmb,YAAY,CAAC1b,KAAb,CAAmBO,EAJxC,CAF6B;EAS7B;;EACA,IAAIqb,aAAa,GAAGH,iBAAiB,CAAC5W,KAAK,CAAC7E,KAAN,CAAYO,EAAb,CAAjB,KAAsCvG,SAA1D,CAV6B;;EAa7B,OAAO2hB,KAAK,IAAIC,aAAhB;AACD;AAED,SAASL,kBAATA,CACEG,YADF,EAEE7W,KAFF,EAE+B;EAE7B,IAAIgX,WAAW,GAAGH,YAAY,CAAC1b,KAAb,CAAmBtE,IAArC;EACA;IAAA;IAEEggB,YAAY,CAAC3gB,QAAb,KAA0B8J,KAAK,CAAC9J,QAAhC;IAAA;IAEA;IACC8gB,WAAW,IAAI,IAAf,IACCA,WAAW,CAAC9Y,QAAZ,CAAqB,GAArB,CADD,IAEC2Y,YAAY,CAAC1W,MAAb,CAAoB,GAApB,MAA6BH,KAAK,CAACG,MAAN,CAAa,GAAb;EAAA;AAElC;AAED,SAASoW,sBAATA,CACEU,WADF,EAEEC,GAFF,EAE8C;EAE5C,IAAID,WAAW,CAAC9b,KAAZ,CAAkBwb,gBAAtB,EAAwC;IACtC,IAAIQ,WAAW,GAAGF,WAAW,CAAC9b,KAAZ,CAAkBwb,gBAAlB,CAAmCO,GAAnC,CAAlB;IACA,IAAI,OAAOC,WAAP,KAAuB,SAA3B,EAAsC;MACpC,OAAOA,WAAP;IACD;EACF;EAED,OAAOD,GAAG,CAAChB,uBAAX;AACD;AAED,eAAexH,kBAAfA,CACEH,IADF,EAEEZ,OAFF,EAGE3N,KAHF,EAIExD,OAJF,EAKEL,QALF,EAMEib,eANF,EAOEzC,cAPF,EAQEnB,cARF,EAQ0B;EAAA,IAHxBrX,QAGwB;IAHxBA,QAGwB,GAHb,GAGa;EAAA;EAAA,IAFxBib,eAEwB;IAFxBA,eAEwB,GAFG,KAEH;EAAA;EAAA,IADxBzC,cACwB;IADxBA,cACwB,GADE,KACF;EAAA;EAExB,IAAI0C,UAAJ;EACA,IAAIhZ,MAAJ,CAHwB;;EAMxB,IAAIsG,MAAJ;EACA,IAAIC,YAAY,GAAG,IAAIC,OAAJ,CAAY,CAACrE,CAAD,EAAIsE,CAAJ,KAAWH,MAAM,GAAGG,CAAhC,CAAnB;EACA,IAAIwS,QAAQ,GAAGA,CAAA,KAAM3S,MAAM,EAA3B;EACAgJ,OAAO,CAACxI,MAAR,CAAepK,gBAAf,CAAgC,OAAhC,EAAyCuc,QAAzC;EAEA,IAAI;IACF,IAAIC,OAAO,GAAGvX,KAAK,CAAC7E,KAAN,CAAYoT,IAAZ,CAAd;IACAtV,SAAS,CACPse,OADO,0BAEehJ,IAFf,yBAEsCvO,KAAK,CAAC7E,KAAN,CAAYO,EAFlD,GAAT;IAKA2C,MAAM,GAAG,MAAMwG,OAAO,CAACW,IAAR,CAAa,CAC1B+R,OAAO,CAAC;MAAE5J,OAAF;MAAWxN,MAAM,EAAEH,KAAK,CAACG,MAAzB;MAAiC0U,OAAO,EAAErB;IAA1C,CAAD,CADmB,EAE1B5O,YAF0B,CAAb,CAAf;IAKA3L,SAAS,CACPoF,MAAM,KAAKlJ,SADJ,EAEP,cAAe,IAAAoZ,IAAI,KAAK,QAAT,GAAoB,WAApB,GAAkC,UAAjD,4BACMvO,KAAK,CAAC7E,KAAN,CAAYO,EADlB,iDACgE6S,IADhE,uDAFO,CAAT;GAZF,CAkBE,OAAO9U,CAAP,EAAU;IACV4d,UAAU,GAAGpc,UAAU,CAACL,KAAxB;IACAyD,MAAM,GAAG5E,CAAT;EACD,CArBD,SAqBU;IACRkU,OAAO,CAACxI,MAAR,CAAenK,mBAAf,CAAmC,OAAnC,EAA4Csc,QAA5C;EACD;EAED,IAAIvD,UAAU,CAAC1V,MAAD,CAAd,EAAwB;IACtB,IAAIyF,MAAM,GAAGzF,MAAM,CAACyF,MAApB,CADsB;;IAItB,IAAI+D,mBAAmB,CAAChM,GAApB,CAAwBiI,MAAxB,CAAJ,EAAqC;MACnC,IAAI9N,QAAQ,GAAGqI,MAAM,CAAC0F,OAAP,CAAe8B,GAAf,CAAmB,UAAnB,CAAf;MACA5M,SAAS,CACPjD,QADO,EAEP,4EAFO,CAAT;MAKA,IAAIwhB,UAAU,GAAG,+BAAgC,CAAAhY,IAAhC,CAAqCxJ,QAArC,CAAjB,CAPmC;;MAUnC,IAAI,CAACwhB,UAAL,EAAiB;QACf,IAAIC,aAAa,GAAGjb,OAAO,CAACzD,KAAR,CAAc,CAAd,EAAiByD,OAAO,CAAC1D,OAAR,CAAgBkH,KAAhB,IAAyB,CAA1C,CAApB;QACA,IAAI8C,cAAc,GAAGH,0BAA0B,CAAC8U,aAAD,CAA1B,CAA0C3iB,GAA1C,CAClBkL,KAAD,IAAWA,KAAK,CAACI,YADE,CAArB;QAGA,IAAIsX,gBAAgB,GAAG9U,SAAS,CAC9B5M,QAD8B,EAE9B8M,cAF8B,EAG9B,IAAInM,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,EAAqB1C,QAHS,CAAhC;QAKA+C,SAAS,CACPzC,UAAU,CAACkhB,gBAAD,CADH,EAEiC,0CAAA1hB,QAFjC,CAAT,CAVe;;QAgBf,IAAImG,QAAJ,EAAc;UACZ,IAAItF,IAAI,GAAG6gB,gBAAgB,CAACxhB,QAA5B;UACAwhB,gBAAgB,CAACxhB,QAAjB,GACEW,IAAI,KAAK,GAAT,GAAesF,QAAf,GAA0BgB,SAAS,CAAC,CAAChB,QAAD,EAAWtF,IAAX,CAAD,CADrC;QAED;QAEDb,QAAQ,GAAGQ,UAAU,CAACkhB,gBAAD,CAArB;MACD,CAvBD,MAuBO,IAAI,CAACN,eAAL,EAAsB;QAC3B;QACA;QACA;QACA,IAAIpB,UAAU,GAAG,IAAIrf,GAAJ,CAAQgX,OAAO,CAAC/U,GAAhB,CAAjB;QACA,IAAIA,GAAG,GAAG5C,QAAQ,CAACkH,UAAT,CAAoB,IAApB,CACN,OAAIvG,GAAJ,CAAQqf,UAAU,CAAC2B,QAAX,GAAsB3hB,QAA9B,CADM,GAEN,IAAIW,GAAJ,CAAQX,QAAR,CAFJ;QAGA,IAAI4C,GAAG,CAACkC,MAAJ,KAAekb,UAAU,CAAClb,MAA9B,EAAsC;UACpC9E,QAAQ,GAAG4C,GAAG,CAAC1C,QAAJ,GAAe0C,GAAG,CAAC7B,MAAnB,GAA4B6B,GAAG,CAAC5B,IAA3C;QACD;MACF,CA5CkC;MA+CnC;MACA;MACA;;MACA,IAAIogB,eAAJ,EAAqB;QACnB/Y,MAAM,CAAC0F,OAAP,CAAeE,GAAf,CAAmB,UAAnB,EAA+BjO,QAA/B;QACA,MAAMqI,MAAN;MACD;MAED,OAAO;QACLkQ,IAAI,EAAEtT,UAAU,CAACmM,QADZ;QAELtD,MAFK;QAGL9N,QAHK;QAILkX,UAAU,EAAE7O,MAAM,CAAC0F,OAAP,CAAe8B,GAAf,CAAmB,oBAAnB,CAA6C;OAJ3D;IAMD,CAjEqB;IAoEtB;IACA;;IACA,IAAI8O,cAAJ,EAAoB;MAClB;MACA,MAAM;QACJpG,IAAI,EAAE8I,UAAU,IAAIpc,UAAU,CAAC0I,IAD3B;QAEJ+Q,QAAQ,EAAErW;OAFZ;IAID;IAED,IAAIsF,IAAJ;IACA,IAAIiU,WAAW,GAAGvZ,MAAM,CAAC0F,OAAP,CAAe8B,GAAf,CAAmB,cAAnB,CAAlB,CA/EsB;IAiFtB;;IACA,IAAI+R,WAAW,IAAI,wBAAwBpY,IAAxB,CAA6BoY,WAA7B,CAAnB,EAA8D;MAC5DjU,IAAI,GAAG,MAAMtF,MAAM,CAACqF,IAAP,EAAb;IACD,CAFD,MAEO;MACLC,IAAI,GAAG,MAAMtF,MAAM,CAACwZ,IAAP,EAAb;IACD;IAED,IAAIR,UAAU,KAAKpc,UAAU,CAACL,KAA9B,EAAqC;MACnC,OAAO;QACL2T,IAAI,EAAE8I,UADD;QAELzc,KAAK,EAAE,IAAIyM,aAAJ,CAAkBvD,MAAlB,EAA0BzF,MAAM,CAACiJ,UAAjC,EAA6C3D,IAA7C,CAFF;QAGLI,OAAO,EAAE1F,MAAM,CAAC0F;OAHlB;IAKD;IAED,OAAO;MACLwK,IAAI,EAAEtT,UAAU,CAAC0I,IADZ;MAELA,IAFK;MAGLgQ,UAAU,EAAEtV,MAAM,CAACyF,MAHd;MAILC,OAAO,EAAE1F,MAAM,CAAC0F;KAJlB;EAMD;EAED,IAAIsT,UAAU,KAAKpc,UAAU,CAACL,KAA9B,EAAqC;IACnC,OAAO;MAAE2T,IAAI,EAAE8I,UAAR;MAAoBzc,KAAK,EAAEyD;KAAlC;EACD;EAED,IAAIA,MAAM,YAAY+F,YAAtB,EAAoC;IAClC,OAAO;MAAEmK,IAAI,EAAEtT,UAAU,CAAC6c,QAAnB;MAA6BjI,YAAY,EAAExR;KAAlD;EACD;EAED,OAAO;IAAEkQ,IAAI,EAAEtT,UAAU,CAAC0I,IAAnB;IAAyBA,IAAI,EAAEtF;GAAtC;AACD;AAGD;AACA;;AACA,SAASuP,uBAATA,CACEnX,OADF,EAEET,QAFF,EAGEmP,MAHF,EAIE2H,UAJF,EAIyB;EAEvB,IAAIlU,GAAG,GAAGnC,OAAO,CAACC,SAAR,CAAkB8e,iBAAiB,CAACxf,QAAD,CAAnC,CAA+C,CAAA4D,QAA/C,EAAV;EACA,IAAIgK,IAAI,GAAgB;IAAEuB;GAA1B;EAEA,IAAI2H,UAAU,IAAIP,gBAAgB,CAACO,UAAU,CAAC9E,UAAZ,CAAlC,EAA2D;IACzD,IAAI;MAAEA,UAAF;MAAcE,WAAd;MAA2BC;IAA3B,IAAwC2E,UAA5C;IACAlJ,IAAI,CAAC4K,MAAL,GAAcxG,UAAU,CAAC+P,WAAX,EAAd;IACAnU,IAAI,CAACoU,IAAL,GACE9P,WAAW,KAAK,mCAAhB,GACIwN,6BAA6B,CAACvN,QAAD,CADjC,GAEIA,QAHN;EAID,CAZsB;;EAevB,OAAO,IAAIgG,OAAJ,CAAYvV,GAAZ,EAAiBgL,IAAjB,CAAP;AACD;AAED,SAAS8R,6BAATA,CAAuCvN,QAAvC,EAAyD;EACvD,IAAIsN,YAAY,GAAG,IAAIwC,eAAJ,EAAnB;EAEA,KAAK,IAAI,CAACliB,GAAD,EAAMmD,KAAN,CAAT,IAAyBiP,QAAQ,CAACtT,OAAT,EAAzB,EAA6C;IAC3C;IACA4gB,YAAY,CAACG,MAAb,CAAoB7f,GAApB,EAAyBmD,KAAK,YAAYgf,IAAjB,GAAwBhf,KAAK,CAACif,IAA9B,GAAqCjf,KAA9D;EACD;EAED,OAAOuc,YAAP;AACD;AAED,SAAST,sBAATA,CACExY,OADF,EAEEyS,aAFF,EAGEM,OAHF,EAIEtC,YAJF,EAKE9B,eALF,EAK4C;EAO1C;EACA,IAAInB,UAAU,GAA8B,EAA5C;EACA,IAAIE,MAAM,GAAiC,IAA3C;EACA,IAAIyJ,UAAJ;EACA,IAAIyE,UAAU,GAAG,KAAjB;EACA,IAAIxE,aAAa,GAA4B,EAA7C,CAZ0C;;EAe1CrE,OAAO,CAAC/R,OAAR,CAAgB,CAACa,MAAD,EAASrJ,KAAT,KAAkB;IAChC,IAAI0G,EAAE,GAAGuT,aAAa,CAACja,KAAD,CAAb,CAAqBmG,KAArB,CAA2BO,EAApC;IACAzC,SAAS,CACP,CAAC0V,gBAAgB,CAACtQ,MAAD,CADV,EAEP,qDAFO,CAAT;IAIA,IAAIwQ,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIyQ,aAAa,GAAGhB,mBAAmB,CAACtR,OAAD,EAAUd,EAAV,CAAvC;MACA,IAAId,KAAK,GAAGyD,MAAM,CAACzD,KAAnB,CAJyB;MAMzB;MACA;;MACA,IAAIqS,YAAJ,EAAkB;QAChBrS,KAAK,GAAGsF,MAAM,CAACiU,MAAP,CAAclH,YAAd,EAA4B,CAA5B,CAAR;QACAA,YAAY,GAAG9X,SAAf;MACD;MAED+U,MAAM,GAAGA,MAAM,IAAI,EAAnB,CAbyB;;MAgBzB,IAAIA,MAAM,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,CAAN,IAAkC,IAAtC,EAA4C;QAC1CwO,MAAM,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,CAAN,GAAiCd,KAAjC;MACD,CAlBwB;;MAqBzBoP,UAAU,CAACtO,EAAD,CAAV,GAAiBvG,SAAjB,CArByB;MAwBzB;;MACA,IAAI,CAACijB,UAAL,EAAiB;QACfA,UAAU,GAAG,IAAb;QACAzE,UAAU,GAAGnM,oBAAoB,CAACnJ,MAAM,CAACzD,KAAR,CAApB,GACTyD,MAAM,CAACzD,KAAP,CAAakJ,MADJ,GAET,GAFJ;MAGD;MACD,IAAIzF,MAAM,CAAC0F,OAAX,EAAoB;QAClB6P,aAAa,CAAClY,EAAD,CAAb,GAAoB2C,MAAM,CAAC0F,OAA3B;MACD;IACF,CAlCD,MAkCO;MACL,IAAIgL,gBAAgB,CAAC1Q,MAAD,CAApB,EAA8B;QAC5B8M,eAAe,CAAClH,GAAhB,CAAoBvI,EAApB,EAAwB2C,MAAM,CAACwR,YAA/B;QACA7F,UAAU,CAACtO,EAAD,CAAV,GAAiB2C,MAAM,CAACwR,YAAP,CAAoBlM,IAArC;MACD,CAHD,MAGO;QACLqG,UAAU,CAACtO,EAAD,CAAV,GAAiB2C,MAAM,CAACsF,IAAxB;MACD,CANI;MASL;;MACA,IACEtF,MAAM,CAACsV,UAAP,IAAqB,IAArB,IACAtV,MAAM,CAACsV,UAAP,KAAsB,GADtB,IAEA,CAACyE,UAHH,EAIE;QACAzE,UAAU,GAAGtV,MAAM,CAACsV,UAApB;MACD;MACD,IAAItV,MAAM,CAAC0F,OAAX,EAAoB;QAClB6P,aAAa,CAAClY,EAAD,CAAb,GAAoB2C,MAAM,CAAC0F,OAA3B;MACD;IACF;EACF,CA7DD,EAf0C;EA+E1C;EACA;;EACA,IAAIkJ,YAAJ,EAAkB;IAChB/C,MAAM,GAAG+C,YAAT;IACAjD,UAAU,CAAC9J,MAAM,CAACuM,IAAP,CAAYQ,YAAZ,EAA0B,CAA1B,CAAD,CAAV,GAA2C9X,SAA3C;EACD;EAED,OAAO;IACL6U,UADK;IAELE,MAFK;IAGLyJ,UAAU,EAAEA,UAAU,IAAI,GAHrB;IAILC;GAJF;AAMD;AAED,SAAShE,iBAATA,CACE1a,KADF,EAEEsH,OAFF,EAGEyS,aAHF,EAIEM,OAJF,EAKEtC,YALF,EAMEiC,oBANF,EAOEO,cAPF,EAQEtE,eARF,EAQ4C;EAK1C,IAAI;IAAEnB,UAAF;IAAcE;EAAd,IAAyB8K,sBAAsB,CACjDxY,OADiD,EAEjDyS,aAFiD,EAGjDM,OAHiD,EAIjDtC,YAJiD,EAKjD9B,eALiD,CAAnD,CAL0C;;EAc1C,KAAK,IAAInW,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGka,oBAAoB,CAAC7Z,MAAjD,EAAyDL,KAAK,EAA9D,EAAkE;IAChE,IAAI;MAAEe,GAAF;MAAOiK;KAAU,GAAAkP,oBAAoB,CAACla,KAAD,CAAzC;IACAiE,SAAS,CACPwW,cAAc,KAAKta,SAAnB,IAAgCsa,cAAc,CAACza,KAAD,CAAd,KAA0BG,SADnD,EAEP,2CAFO,CAAT;IAIA,IAAIkJ,MAAM,GAAGoR,cAAc,CAACza,KAAD,CAA3B,CANgE;;IAShE,IAAI6Z,aAAa,CAACxQ,MAAD,CAAjB,EAA2B;MACzB,IAAIyQ,aAAa,GAAGhB,mBAAmB,CAAC5Y,KAAK,CAACsH,OAAP,EAAgBwD,KAAK,CAAC7E,KAAN,CAAYO,EAA5B,CAAvC;MACA,IAAI,EAAEwO,MAAM,IAAIA,MAAM,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,CAAlB,CAAJ,EAAiD;QAC/CwO,MAAM,GAAAlQ,QAAA,KACDkQ,MADC;UAEJ,CAAC4E,aAAa,CAAC3T,KAAd,CAAoBO,EAArB,GAA0B2C,MAAM,CAACzD;SAFnC;MAID;MACD1F,KAAK,CAACiV,QAAN,CAAepE,MAAf,CAAsBhQ,GAAtB;IACD,CATD,MASO,IAAI4Y,gBAAgB,CAACtQ,MAAD,CAApB,EAA8B;MACnC;MACA;MACApF,SAAS,CAAC,KAAD,EAAQ,yCAAR,CAAT;IACD,CAJM,MAIA,IAAI8V,gBAAgB,CAAC1Q,MAAD,CAApB,EAA8B;MACnC;MACA;MACApF,SAAS,CAAC,KAAD,EAAQ,iCAAR,CAAT;IACD,CAJM,MAIA;MACL,IAAIiY,WAAW,GAA0B;QACvChc,KAAK,EAAE,MADgC;QAEvCyO,IAAI,EAAEtF,MAAM,CAACsF,IAF0B;QAGvCqE,UAAU,EAAE7S,SAH2B;QAIvC8S,UAAU,EAAE9S,SAJ2B;QAKvC+S,WAAW,EAAE/S,SAL0B;QAMvCgT,QAAQ,EAAEhT,SAN6B;QAOvC,2BAA6B;OAP/B;MASAD,KAAK,CAACiV,QAAN,CAAelG,GAAf,CAAmBlO,GAAnB,EAAwBmb,WAAxB;IACD;EACF;EAED,OAAO;IAAElH,UAAF;IAAcE;GAArB;AACD;AAED,SAASwC,eAATA,CACE1C,UADF,EAEEqO,aAFF,EAGE7b,OAHF,EAIE0N,MAJF,EAIsC;EAEpC,IAAIoO,gBAAgB,GAAQte,QAAA,KAAAqe,aAAR,CAApB;EACA,KAAK,IAAIrY,KAAT,IAAkBxD,OAAlB,EAA2B;IACzB,IAAId,EAAE,GAAGsE,KAAK,CAAC7E,KAAN,CAAYO,EAArB;IACA,IAAI2c,aAAa,CAACE,cAAd,CAA6B7c,EAA7B,CAAJ,EAAsC;MACpC,IAAI2c,aAAa,CAAC3c,EAAD,CAAb,KAAsBvG,SAA1B,EAAqC;QACnCmjB,gBAAgB,CAAC5c,EAAD,CAAhB,GAAuB2c,aAAa,CAAC3c,EAAD,CAApC;MACD;KAHH,MAQO,IAAIsO,UAAU,CAACtO,EAAD,CAAV,KAAmBvG,SAAvB,EAAkC;MACvCmjB,gBAAgB,CAAC5c,EAAD,CAAhB,GAAuBsO,UAAU,CAACtO,EAAD,CAAjC;IACD;IAED,IAAIwO,MAAM,IAAIA,MAAM,CAACqO,cAAP,CAAsB7c,EAAtB,CAAd,EAAyC;MACvC;MACA;IACD;EACF;EACD,OAAO4c,gBAAP;AACD;AAGD;AACA;;AACA,SAASxK,mBAATA,CACEtR,OADF,EAEEiS,OAFF,EAEkB;EAEhB,IAAI+J,eAAe,GAAG/J,OAAO,GACzBjS,OAAO,CAACzD,KAAR,CAAc,CAAd,EAAiByD,OAAO,CAACuZ,SAAR,CAAmBvM,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAe+S,OAAxC,CAAmD,IAApE,CADyB,GAEzB,CAAC,GAAGjS,OAAJ,CAFJ;EAGA,OACEgc,eAAe,CAACC,OAAhB,GAA0BvE,IAA1B,CAAgC1K,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQud,gBAAR,KAA6B,IAAnE,KACAlc,OAAO,CAAC,CAAD,CAFT;AAID;AAED,SAAS8M,sBAATA,CAAgCjO,MAAhC,EAAiE;EAI/D;EACA,IAAIF,KAAK,GAAGE,MAAM,CAAC6Y,IAAP,CAAapP,CAAD,IAAOA,CAAC,CAAC9P,KAAF,IAAW,CAAC8P,CAAC,CAACjO,IAAd,IAAsBiO,CAAC,CAACjO,IAAF,KAAW,GAApD,CAA4D;IACtE6E,EAAE;GADJ;EAIA,OAAO;IACLc,OAAO,EAAE,CACP;MACE2D,MAAM,EAAE,EADV;MAEEjK,QAAQ,EAAE,EAFZ;MAGEkK,YAAY,EAAE,EAHhB;MAIEjF;IAJF,CADO,CADJ;IASLA;GATF;AAWD;AAED,SAASkO,sBAATA,CACEvF,MADF,EAYQ6U,MAAA;EAAA,IAVN;IACEziB,QADF;IAEEuY,OAFF;IAGED,MAHF;IAIED;EAJF,CAUM,GAAAoK,MAAA,cAAF,EAAE,GAAAA,MAAA;EAEN,IAAIrR,UAAU,GAAG,sBAAjB;EACA,IAAIsR,YAAY,GAAG,iCAAnB;EAEA,IAAI9U,MAAM,KAAK,GAAf,EAAoB;IAClBwD,UAAU,GAAG,aAAb;IACA,IAAIkH,MAAM,IAAItY,QAAV,IAAsBuY,OAA1B,EAAmC;MACjCmK,YAAY,GACV,aAAc,GAAApK,MAAd,sBAAoCtY,QAApC,4DAC2CuY,OAD3C,GADF;IAID,CALD,MAKO,IAAIF,IAAI,KAAK,cAAb,EAA6B;MAClCqK,YAAY,GAAG,qCAAf;IACD;EACF,CAVD,MAUO,IAAI9U,MAAM,KAAK,GAAf,EAAoB;IACzBwD,UAAU,GAAG,WAAb;IACAsR,YAAY,GAAa,aAAAnK,OAAb,GAA6C,6BAAAvY,QAA7C,GAAZ;EACD,CAHM,MAGA,IAAI4N,MAAM,KAAK,GAAf,EAAoB;IACzBwD,UAAU,GAAG,WAAb;IACAsR,YAAY,+BAA4B1iB,QAA5B,GAAZ;EACD,CAHM,MAGA,IAAI4N,MAAM,KAAK,GAAf,EAAoB;IACzBwD,UAAU,GAAG,oBAAb;IACA,IAAIkH,MAAM,IAAItY,QAAV,IAAsBuY,OAA1B,EAAmC;MACjCmK,YAAY,GACV,aAAc,GAAApK,MAAM,CAACuJ,WAAP,EAAd,GAAkD,mBAAA7hB,QAAlD,GAC4C,0DAAAuY,OAD5C,GADF;KADF,MAKO,IAAID,MAAJ,EAAY;MACjBoK,YAAY,GAA8B,8BAAApK,MAAM,CAACuJ,WAAP,EAA9B,GAAZ;IACD;EACF;EAED,OAAO,IAAI1Q,aAAJ,CACLvD,MAAM,IAAI,GADL,EAELwD,UAFK,EAGL,IAAIlO,KAAJ,CAAUwf,YAAV,CAHK,EAIL,IAJK,CAAP;AAMD;;AAGD,SAASjJ,YAATA,CAAsBJ,OAAtB,EAA2C;EACzC,KAAK,IAAI9S,CAAC,GAAG8S,OAAO,CAACla,MAAR,GAAiB,CAA9B,EAAiCoH,CAAC,IAAI,CAAtC,EAAyCA,CAAC,EAA1C,EAA8C;IAC5C,IAAI4B,MAAM,GAAGkR,OAAO,CAAC9S,CAAD,CAApB;IACA,IAAIkS,gBAAgB,CAACtQ,MAAD,CAApB,EAA8B;MAC5B,OAAOA,MAAP;IACD;EACF;AACF;AAED,SAASmX,iBAATA,CAA2B3e,IAA3B,EAAmC;EACjC,IAAIqD,UAAU,GAAG,OAAOrD,IAAP,KAAgB,QAAhB,GAA2BC,SAAS,CAACD,IAAD,CAApC,GAA6CA,IAA9D;EACA,OAAOL,UAAU,CAAAwD,QAAA,KAAME,UAAN;IAAkBlD,IAAI,EAAE;GAAzC;AACD;AAED,SAAS0W,gBAATA,CAA0BlP,CAA1B,EAAuCC,CAAvC,EAAkD;EAChD,OACED,CAAC,CAACtI,QAAF,KAAeuI,CAAC,CAACvI,QAAjB,IAA6BsI,CAAC,CAACzH,MAAF,KAAa0H,CAAC,CAAC1H,MAA5C,IAAsDyH,CAAC,CAACxH,IAAF,KAAWyH,CAAC,CAACzH,IADrE;AAGD;AAED,SAAS+X,gBAATA,CAA0B1Q,MAA1B,EAA4C;EAC1C,OAAOA,MAAM,CAACkQ,IAAP,KAAgBtT,UAAU,CAAC6c,QAAlC;AACD;AAED,SAASjJ,aAATA,CAAuBxQ,MAAvB,EAAyC;EACvC,OAAOA,MAAM,CAACkQ,IAAP,KAAgBtT,UAAU,CAACL,KAAlC;AACD;AAED,SAAS+T,gBAATA,CAA0BtQ,MAA1B,EAA6C;EAC3C,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACkQ,IAAlB,MAA4BtT,UAAU,CAACmM,QAA9C;AACD;AAED,SAAS2M,UAATA,CAAoB7a,KAApB,EAA8B;EAC5B,OACEA,KAAK,IAAI,IAAT,IACA,OAAOA,KAAK,CAAC4K,MAAb,KAAwB,QADxB,IAEA,OAAO5K,KAAK,CAACoO,UAAb,KAA4B,QAF5B,IAGA,OAAOpO,KAAK,CAAC6K,OAAb,KAAyB,QAHzB,IAIA,OAAO7K,KAAK,CAAC8e,IAAb,KAAsB,WALxB;AAOD;AAED,SAASvD,kBAATA,CAA4BpW,MAA5B,EAAuC;EACrC,IAAI,CAAC0V,UAAU,CAAC1V,MAAD,CAAf,EAAyB;IACvB,OAAO,KAAP;EACD;EAED,IAAIyF,MAAM,GAAGzF,MAAM,CAACyF,MAApB;EACA,IAAI9N,QAAQ,GAAGqI,MAAM,CAAC0F,OAAP,CAAe8B,GAAf,CAAmB,UAAnB,CAAf;EACA,OAAO/B,MAAM,IAAI,GAAV,IAAiBA,MAAM,IAAI,GAA3B,IAAkC9N,QAAQ,IAAI,IAArD;AACD;AAED,SAASwe,oBAATA,CAA8BqE,GAA9B,EAAsC;EACpC,OACEA,GAAG,IACH9E,UAAU,CAAC8E,GAAG,CAACnE,QAAL,CADV,KAECmE,GAAG,CAACtK,IAAJ,KAAatT,UAAU,CAAC0I,IAAxB,IAAgC1I,UAAU,CAACL,KAF5C,CADF;AAKD;AAED,SAAS6Y,aAATA,CAAuBjF,MAAvB,EAAqC;EACnC,OAAO5G,mBAAmB,CAAC/L,GAApB,CAAwB2S,MAAxB,CAAP;AACD;AAED,SAASjC,gBAATA,CAA0BiC,MAA1B,EAAyC;EACvC,OAAO9G,oBAAoB,CAAC7L,GAArB,CAAyB2S,MAAzB,CAAP;AACD;AAED,eAAesD,sBAAfA,CACEJ,cADF,EAEEzC,aAFF,EAGEM,OAHF,EAIEpK,MAJF,EAKEoQ,SALF,EAMEqB,iBANF,EAM+B;EAE7B,KAAK,IAAI5hB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGua,OAAO,CAACla,MAApC,EAA4CL,KAAK,EAAjD,EAAqD;IACnD,IAAIqJ,MAAM,GAAGkR,OAAO,CAACva,KAAD,CAApB;IACA,IAAIgL,KAAK,GAAGiP,aAAa,CAACja,KAAD,CAAzB;IACA,IAAI6hB,YAAY,GAAGnF,cAAc,CAACwC,IAAf,CAChB1K,CAAD,IAAOA,CAAC,CAACrO,KAAF,CAAQO,EAAR,KAAesE,KAAK,CAAC7E,KAAN,CAAYO,EADjB,CAAnB;IAGA,IAAIod,oBAAoB,GACtBjC,YAAY,IAAI,IAAhB,IACA,CAACH,kBAAkB,CAACG,YAAD,EAAe7W,KAAf,CADnB,IAEA,CAAC4W,iBAAiB,IAAIA,iBAAiB,CAAC5W,KAAK,CAAC7E,KAAN,CAAYO,EAAb,CAAvC,MAA6DvG,SAH/D;IAKA,IAAI4Z,gBAAgB,CAAC1Q,MAAD,CAAhB,KAA6BkX,SAAS,IAAIuD,oBAA1C,CAAJ,EAAqE;MACnE;MACA;MACA;MACA,MAAM3H,mBAAmB,CAAC9S,MAAD,EAAS8G,MAAT,EAAiBoQ,SAAjB,CAAnB,CAA+C9P,IAA/C,CAAqDpH,MAAD,IAAW;QACnE,IAAIA,MAAJ,EAAY;UACVkR,OAAO,CAACva,KAAD,CAAP,GAAiBqJ,MAAM,IAAIkR,OAAO,CAACva,KAAD,CAAlC;QACD;MACF,CAJK,CAAN;IAKD;EACF;AACF;AAED,eAAemc,mBAAfA,CACE9S,MADF,EAEE8G,MAFF,EAGE4T,MAHF,EAGgB;EAAA,IAAdA,MAAc;IAAdA,MAAc,GAAL,KAAK;EAAA;EAEd,IAAIjT,OAAO,GAAG,MAAMzH,MAAM,CAACwR,YAAP,CAAoBrJ,WAApB,CAAgCrB,MAAhC,CAApB;EACA,IAAIW,OAAJ,EAAa;IACX;EACD;EAED,IAAIiT,MAAJ,EAAY;IACV,IAAI;MACF,OAAO;QACLxK,IAAI,EAAEtT,UAAU,CAAC0I,IADZ;QAELA,IAAI,EAAEtF,MAAM,CAACwR,YAAP,CAAoBlJ;OAF5B;KADF,CAKE,OAAOlN,CAAP,EAAU;MACV;MACA,OAAO;QACL8U,IAAI,EAAEtT,UAAU,CAACL,KADZ;QAELA,KAAK,EAAEnB;OAFT;IAID;EACF;EAED,OAAO;IACL8U,IAAI,EAAEtT,UAAU,CAAC0I,IADZ;IAELA,IAAI,EAAEtF,MAAM,CAACwR,YAAP,CAAoBlM;GAF5B;AAID;AAED,SAASgS,kBAATA,CAA4B5e,MAA5B,EAA0C;EACxC,OAAO,IAAIkhB,eAAJ,CAAoBlhB,MAApB,EAA4BiiB,MAA5B,CAAmC,OAAnC,CAA4C,CAAA5Z,IAA5C,CAAkDkH,CAAD,IAAOA,CAAC,KAAK,EAA9D,CAAP;AACD;AAGD;;AACA,SAAS0M,qBAATA,CACEhT,KADF,EAEEgK,UAFF,EAEuB;EAErB,IAAI;IAAE7O,KAAF;IAASjF,QAAT;IAAmBiK;EAAnB,IAA8BH,KAAlC;EACA,OAAO;IACLtE,EAAE,EAAEP,KAAK,CAACO,EADL;IAELxF,QAFK;IAGLiK,MAHK;IAILwD,IAAI,EAAEqG,UAAU,CAAC7O,KAAK,CAACO,EAAP,CAJX;IAKLud,MAAM,EAAE9d,KAAK,CAAC8d;GALhB;AAOD;AAED,SAAS3K,cAATA,CACE9R,OADF,EAEExG,QAFF,EAE6B;EAE3B,IAAIe,MAAM,GACR,OAAOf,QAAP,KAAoB,QAApB,GAA+Bc,SAAS,CAACd,QAAD,CAAT,CAAoBe,MAAnD,GAA4Df,QAAQ,CAACe,MADvE;EAEA,IACEyF,OAAO,CAACA,OAAO,CAACnH,MAAR,GAAiB,CAAlB,CAAP,CAA4B8F,KAA5B,CAAkCnG,KAAlC,IACA2gB,kBAAkB,CAAC5e,MAAM,IAAI,EAAX,CAFpB,EAGE;IACA;IACA,OAAOyF,OAAO,CAACA,OAAO,CAACnH,MAAR,GAAiB,CAAlB,CAAd;EACD,CAV0B;EAY3B;;EACA,IAAI6jB,WAAW,GAAGvW,0BAA0B,CAACnG,OAAD,CAA5C;EACA,OAAO0c,WAAW,CAACA,WAAW,CAAC7jB,MAAZ,GAAqB,CAAtB,CAAlB;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}