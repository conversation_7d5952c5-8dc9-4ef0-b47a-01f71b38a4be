{"ast": null, "code": "export default function getDataOrAriaProps(props) {\n  return Object.keys(props).reduce(function (prev, key) {\n    if ((key.startsWith('data-') || key.startsWith('aria-') || key === 'role') && !key.startsWith('data-__')) {\n      prev[key] = props[key];\n    }\n    return prev;\n  }, {});\n}", "map": {"version": 3, "names": ["getDataOrAriaProps", "props", "Object", "keys", "reduce", "prev", "key", "startsWith"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/getDataOrAriaProps.js"], "sourcesContent": ["export default function getDataOrAriaProps(props) {\n  return Object.keys(props).reduce(function (prev, key) {\n    if ((key.startsWith('data-') || key.startsWith('aria-') || key === 'role') && !key.startsWith('data-__')) {\n      prev[key] = props[key];\n    }\n    return prev;\n  }, {});\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,KAAK,EAAE;EAChD,OAAOC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,MAAM,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACpD,IAAI,CAACA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,KAAK,MAAM,KAAK,CAACA,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE;MACxGF,IAAI,CAACC,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;IACxB;IACA,OAAOD,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module"}