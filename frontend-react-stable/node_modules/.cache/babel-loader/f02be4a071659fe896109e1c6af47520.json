{"ast": null, "code": "// This icon file is generated automatically.\nvar CaretLeftFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"0 0 1024 1024\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M689 165.1L308.2 493.5c-10.9 9.4-10.9 27.5 0 37L689 858.9c14.2 12.2 35 1.2 35-18.5V183.6c0-19.7-20.8-30.7-35-18.5z\"\n      }\n    }]\n  },\n  \"name\": \"caret-left\",\n  \"theme\": \"filled\"\n};\nexport default CaretLeftFilled;", "map": {"version": 3, "names": ["CaretLeftFilled"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/CaretLeftFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CaretLeftFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"0 0 1024 1024\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M689 165.1L308.2 493.5c-10.9 9.4-10.9 27.5 0 37L689 858.9c14.2 12.2 35 1.2 35-18.5V183.6c0-19.7-20.8-30.7-35-18.5z\" } }] }, \"name\": \"caret-left\", \"theme\": \"filled\" };\nexport default CaretLeftFilled;\n"], "mappings": "AAAA;AACA,IAAIA,eAAe,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAqH;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,YAAY;EAAE,OAAO,EAAE;AAAS,CAAC;AACnU,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}