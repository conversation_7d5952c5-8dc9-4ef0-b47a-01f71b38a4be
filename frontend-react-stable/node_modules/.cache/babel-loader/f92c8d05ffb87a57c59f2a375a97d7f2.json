{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    visible = props.visible,\n    maskProps = props.maskProps,\n    motionName = props.motionName;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    key: \"mask\",\n    visible: visible,\n    motionName: motionName,\n    leavedClassName: \"\".concat(prefixCls, \"-mask-hidden\")\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref,\n      style: _objectSpread(_objectSpread({}, motionStyle), style),\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionClassName)\n    }, maskProps));\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "React", "classNames", "CSSMotion", "Mask", "props", "prefixCls", "style", "visible", "maskProps", "motionName", "createElement", "key", "leavedClassName", "concat", "_ref", "ref", "motionClassName", "className", "motionStyle"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dialog/es/Dialog/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    visible = props.visible,\n    maskProps = props.maskProps,\n    motionName = props.motionName;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    key: \"mask\",\n    visible: visible,\n    motionName: motionName,\n    leavedClassName: \"\".concat(prefixCls, \"-mask-hidden\")\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref,\n      style: _objectSpread(_objectSpread({}, motionStyle), style),\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionClassName)\n    }, maskProps));\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,eAAe,SAASC,IAAIA,CAACC,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,UAAU,GAAGL,KAAK,CAACK,UAAU;EAC/B,OAAO,aAAaT,KAAK,CAACU,aAAa,CAACR,SAAS,EAAE;IACjDS,GAAG,EAAE,MAAM;IACXJ,OAAO,EAAEA,OAAO;IAChBE,UAAU,EAAEA,UAAU;IACtBG,eAAe,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,cAAc;EACtD,CAAC,EAAE,UAAUS,IAAI,EAAEC,GAAG,EAAE;IACtB,IAAIC,eAAe,GAAGF,IAAI,CAACG,SAAS;MAClCC,WAAW,GAAGJ,IAAI,CAACR,KAAK;IAC1B,OAAO,aAAaN,KAAK,CAACU,aAAa,CAAC,KAAK,EAAEZ,QAAQ,CAAC;MACtDiB,GAAG,EAAEA,GAAG;MACRT,KAAK,EAAEP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,WAAW,CAAC,EAAEZ,KAAK,CAAC;MAC3DW,SAAS,EAAEhB,UAAU,CAAC,EAAE,CAACY,MAAM,CAACR,SAAS,EAAE,OAAO,CAAC,EAAEW,eAAe;IACtE,CAAC,EAAER,SAAS,CAAC,CAAC;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}