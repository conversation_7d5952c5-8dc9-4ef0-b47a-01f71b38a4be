{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FlagFilledSvg from \"@ant-design/icons-svg/es/asn/FlagFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar FlagFilled = function FlagFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FlagFilledSvg\n  }));\n};\nFlagFilled.displayName = 'FlagFilled';\nexport default /*#__PURE__*/React.forwardRef(FlagFilled);", "map": {"version": 3, "names": ["_objectSpread", "React", "FlagFilledSvg", "AntdIcon", "FlagFilled", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/FlagFilled.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FlagFilledSvg from \"@ant-design/icons-svg/es/asn/FlagFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar FlagFilled = function FlagFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FlagFilledSvg\n  }));\n};\nFlagFilled.displayName = 'FlagFilled';\nexport default /*#__PURE__*/React.forwardRef(FlagFilled);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,UAAU,CAACK,WAAW,GAAG,YAAY;AACrC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}