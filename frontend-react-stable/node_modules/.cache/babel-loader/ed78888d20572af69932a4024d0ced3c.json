{"ast": null, "code": "import * as React from 'react';\nfunction Panel(_ref) {\n  var className = _ref.className,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, children);\n}\nexport default Panel;", "map": {"version": 3, "names": ["React", "Panel", "_ref", "className", "children", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Panel/index.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction Panel(_ref) {\n  var className = _ref.className,\n      children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, children);\n}\n\nexport default Panel;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC5B,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IAC7CF,SAAS,EAAEA;EACb,CAAC,EAAEC,QAAQ,CAAC;AACd;AAEA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}