{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = variance;\nfunction variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "variance", "values", "valueof", "count", "delta", "mean", "sum", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/variance.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = variance;\n\nfunction variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n\n  if (count > 1) return sum / (count - 1);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK;EACT,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,GAAG,GAAG,CAAC;EAEX,IAAIJ,OAAO,KAAKK,SAAS,EAAE;IACzB,KAAK,IAAIT,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAIH,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9CM,KAAK,GAAGN,KAAK,GAAGO,IAAI;QACpBA,IAAI,IAAID,KAAK,GAAG,EAAED,KAAK;QACvBG,GAAG,IAAIF,KAAK,IAAIN,KAAK,GAAGO,IAAI,CAAC;MAC/B;IACF;EACF,CAAC,MAAM;IACL,IAAIG,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIV,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEU,KAAK,EAAEP,MAAM,CAAC,KAAK,IAAI,IAAI,CAACH,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClFM,KAAK,GAAGN,KAAK,GAAGO,IAAI;QACpBA,IAAI,IAAID,KAAK,GAAG,EAAED,KAAK;QACvBG,GAAG,IAAIF,KAAK,IAAIN,KAAK,GAAGO,IAAI,CAAC;MAC/B;IACF;EACF;EAEA,IAAIF,KAAK,GAAG,CAAC,EAAE,OAAOG,GAAG,IAAIH,KAAK,GAAG,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}