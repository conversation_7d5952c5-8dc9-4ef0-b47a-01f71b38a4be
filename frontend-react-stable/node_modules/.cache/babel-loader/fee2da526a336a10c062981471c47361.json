{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _none = _interopRequireDefault(require(\"./none.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series) {\n  var peaks = series.map(peak);\n  return (0, _none.default)(series).sort(function (a, b) {\n    return peaks[a] - peaks[b];\n  });\n}\nfunction peak(series) {\n  var i = -1,\n    j = 0,\n    n = series.length,\n    vi,\n    vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_none", "_interopRequireDefault", "require", "obj", "__esModule", "series", "peaks", "map", "peak", "sort", "a", "b", "i", "j", "n", "length", "vi", "vj", "Infinity"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/order/appearance.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _none = _interopRequireDefault(require(\"./none.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series) {\n  var peaks = series.map(peak);\n  return (0, _none.default)(series).sort(function (a, b) {\n    return peaks[a] - peaks[b];\n  });\n}\n\nfunction peak(series) {\n  var i = -1,\n      j = 0,\n      n = series.length,\n      vi,\n      vj = -Infinity;\n\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n\n  return j;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,KAAK,GAAGC,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,MAAM,EAAE;EACxB,IAAIC,KAAK,GAAGD,MAAM,CAACE,GAAG,CAACC,IAAI,CAAC;EAC5B,OAAO,CAAC,CAAC,EAAER,KAAK,CAACF,OAAO,EAAEO,MAAM,CAAC,CAACI,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACrD,OAAOL,KAAK,CAACI,CAAC,CAAC,GAAGJ,KAAK,CAACK,CAAC,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASH,IAAIA,CAACH,MAAM,EAAE;EACpB,IAAIO,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAGT,MAAM,CAACU,MAAM;IACjBC,EAAE;IACFC,EAAE,GAAG,CAACC,QAAQ;EAElB,OAAO,EAAEN,CAAC,GAAGE,CAAC,EAAE,IAAI,CAACE,EAAE,GAAG,CAACX,MAAM,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,EAAEA,EAAE,GAAGD,EAAE,EAAEH,CAAC,GAAGD,CAAC;EAE7D,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}