{"ast": null, "code": "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context = ReactReduxContext) {\n  const useReduxContext =\n  // @ts-ignore\n  context === ReactReduxContext ? useDefaultReduxContext : () => useContext(context);\n  return function useStore() {\n    const {\n      store\n    } = useReduxContext(); // @ts-ignore\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useStore = /*#__PURE__*/createStoreHook();", "map": {"version": 3, "names": ["useContext", "ReactReduxContext", "useReduxContext", "useDefaultReduxContext", "createStoreHook", "context", "useStore", "store"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/hooks/useStore.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context = ReactReduxContext) {\n  const useReduxContext = // @ts-ignore\n  context === ReactReduxContext ? useDefaultReduxContext : () => useContext(context);\n  return function useStore() {\n    const {\n      store\n    } = useReduxContext(); // @ts-ignore\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useStore = /*#__PURE__*/createStoreHook();"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,IAAIC,sBAAsB,QAAQ,mBAAmB;AAC7E;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,OAAO,GAAGJ,iBAAiB,EAAE;EAC3D,MAAMC,eAAe;EAAG;EACxBG,OAAO,KAAKJ,iBAAiB,GAAGE,sBAAsB,GAAG,MAAMH,UAAU,CAACK,OAAO,CAAC;EAClF,OAAO,SAASC,QAAQA,CAAA,EAAG;IACzB,MAAM;MACJC;IACF,CAAC,GAAGL,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEvB,OAAOK,KAAK;EACd,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMD,QAAQ,GAAG,aAAaF,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}