{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nvar vendorPrefix;\nvar jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-'\n};\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  var style = document.createElement('p').style;\n  var testProp = 'Transform';\n  for (var key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\nfunction getTransitionName() {\n  return getVendorPrefix() ? \"\".concat(getVendorPrefix(), \"TransitionProperty\") : 'transitionProperty';\n}\nfunction getTransformName() {\n  return getVendorPrefix() ? \"\".concat(getVendorPrefix(), \"Transform\") : 'transform';\n}\nfunction setTransitionProperty(node, value) {\n  var name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\nfunction setTransform(node, value) {\n  var name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\nfunction getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\nfunction getTransformXY(node) {\n  var style = window.getComputedStyle(node, null);\n  var transform = style.getPropertyValue('transform') || style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    var matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0)\n    };\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n}\nvar matrix2d = /matrix\\((.*)\\)/;\nvar matrix3d = /matrix3d\\((.*)\\)/;\nfunction setTransformXY(node, xy) {\n  var style = window.getComputedStyle(node, null);\n  var transform = style.getPropertyValue('transform') || style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    var arr;\n    var match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(function (item) {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, \"matrix(\".concat(arr.join(','), \")\"));\n    } else {\n      var match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(function (item) {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, \"matrix3d(\".concat(arr.join(','), \")\"));\n    }\n  } else {\n    setTransform(node, \"translateX(\".concat(xy.x, \"px) translateY(\").concat(xy.y, \"px) translateZ(0)\"));\n  }\n}\nvar RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\nvar getComputedStyleX;\n\n// https://stackoverflow.com/a/3485654/3040605\nfunction forceRelayout(elem) {\n  var originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n  elem.style.display = originalStyle;\n}\nfunction css(el, name, v) {\n  var value = v;\n  if (_typeof(name) === 'object') {\n    for (var i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = \"\".concat(value, \"px\");\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\nfunction getClientPosition(elem) {\n  var box;\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top);\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\nfunction getScroll(w, top) {\n  var ret = w[\"page\".concat(top ? 'Y' : 'X', \"Offset\")];\n  var method = \"scroll\".concat(top ? 'Top' : 'Left');\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\nfunction getOffset(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\nfunction _getComputedStyle(elem, name, cs) {\n  var computedStyle = cs;\n  var val = '';\n  var d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n  return val;\n}\nvar _RE_NUM_NO_PX = new RegExp(\"^(\".concat(RE_NUM, \")(?!px)[a-z%]+$\"), 'i');\nvar RE_POS = /^(top|right|bottom|left)$/;\nvar CURRENT_STYLE = 'currentStyle';\nvar RUNTIME_STYLE = 'runtimeStyle';\nvar LEFT = 'left';\nvar PX = 'px';\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  var ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    var style = elem.style;\n    var left = style[LEFT];\n    var rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle ? _getComputedStyle : _getComputedStyleIE;\n}\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n}\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  var presetH = -999;\n  var presetV = -999;\n  var horizontalProperty = getOffsetDirection('left', option);\n  var verticalProperty = getOffsetDirection('top', option);\n  var oppositeHorizontalProperty = oppositeOffsetDirection(horizontalProperty);\n  var oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  var originalTransition = '';\n  var originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = \"\".concat(presetH, \"px\");\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = \"\".concat(presetV, \"px\");\n  }\n  // force relayout\n  forceRelayout(elem);\n  var old = getOffset(elem);\n  var originalStyle = {};\n  for (var key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      var dir = getOffsetDirection(key, option);\n      var preset = key === 'left' ? presetH : presetV;\n      var off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle);\n  // force relayout\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  var ret = {};\n  for (var _key in offset) {\n    if (offset.hasOwnProperty(_key)) {\n      var _dir = getOffsetDirection(_key, option);\n      var _off = offset[_key] - originalOffset[_key];\n      if (_key === _dir) {\n        ret[_dir] = originalStyle[_dir] + _off;\n      } else {\n        ret[_dir] = originalStyle[_dir] - _off;\n      }\n    }\n  }\n  css(elem, ret);\n}\nfunction setTransform$1(elem, offset) {\n  var originalOffset = getOffset(elem);\n  var originalXY = getTransformXY(elem);\n  var resultXY = {\n    x: originalXY.x,\n    y: originalXY.y\n  };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    var oriOffset = getOffset(elem);\n    var oLeft = oriOffset.left.toFixed(0);\n    var oTop = oriOffset.top.toFixed(0);\n    var tLeft = offset.left.toFixed(0);\n    var tTop = offset.top.toFixed(0);\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (option.useCssTransform && getTransformName() in document.body.style) {\n    setTransform$1(elem, offset);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\nfunction each(arr, fn) {\n  for (var i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\nvar BOX_MODELS = ['margin', 'border', 'padding'];\nvar CONTENT_INDEX = -1;\nvar PADDING_INDEX = 2;\nvar BORDER_INDEX = 1;\nvar MARGIN_INDEX = 0;\nfunction swap(elem, options, callback) {\n  var old = {};\n  var style = elem.style;\n  var name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\nfunction getPBMWidth(elem, props, which) {\n  var value = 0;\n  var prop;\n  var j;\n  var i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        var cssProp = void 0;\n        if (prop === 'border') {\n          cssProp = \"\".concat(prop).concat(which[i], \"Width\");\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\nvar domUtils = {\n  getParent: function getParent(element) {\n    var parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  }\n};\neach(['Width', 'Height'], function (name) {\n  domUtils[\"doc\".concat(name)] = function (refWin) {\n    var d = refWin.document;\n    return Math.max(\n    // firefox chrome documentElement.scrollHeight< body.scrollHeight\n    // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n    d.documentElement[\"scroll\".concat(name)],\n    // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n    d.body[\"scroll\".concat(name)], domUtils[\"viewport\".concat(name)](d));\n  };\n  domUtils[\"viewport\".concat(name)] = function (win) {\n    // pc browser includes scrollbar in window.innerWidth\n    var prop = \"client\".concat(name);\n    var doc = win.document;\n    var body = doc.body;\n    var documentElement = doc.documentElement;\n    var documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return doc.compatMode === 'CSS1Compat' && documentElementProp || body && body[prop] || documentElementProp;\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, ex) {\n  var extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width' ? domUtils.viewportWidth(elem) : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width' ? domUtils.docWidth(elem) : domUtils.docHeight(elem);\n  }\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  var borderBoxValue = name === 'width' ? Math.floor(elem.getBoundingClientRect().width) : Math.floor(elem.getBoundingClientRect().height);\n  var isBorderBox = isBorderBoxFn(elem);\n  var cssBoxValue = 0;\n  if (borderBoxValue === null || borderBoxValue === undefined || borderBoxValue <= 0) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (cssBoxValue === null || cssBoxValue === undefined || Number(cssBoxValue) < 0) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = Math.floor(parseFloat(cssBoxValue)) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  var borderBoxValueOrIsBorderBox = borderBoxValue !== undefined || isBorderBox;\n  var val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return val + (extra === PADDING_INDEX ? -getPBMWidth(elem, ['border'], which) : getPBMWidth(elem, ['margin'], which));\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\nvar cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block'\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay() {\n  for (var _len = arguments.length, args = new Array(_len), _key2 = 0; _key2 < _len; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  var val;\n  var elem = args[0];\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, function () {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\neach(['width', 'height'], function (name) {\n  var first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[\"outer\".concat(first)] = function (el, includeMargin) {\n    return el && getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX);\n  };\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  domUtils[name] = function (elem, v) {\n    var val = v;\n    if (val !== undefined) {\n      if (elem) {\n        var isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\nfunction mix(to, from) {\n  for (var i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\nvar utils = {\n  getWindow: function getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    var doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument: getDocument,\n  offset: function offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow: isWindow,\n  each: each,\n  css: css,\n  clone: function clone(obj) {\n    var i;\n    var ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    var overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix: mix,\n  getWindowScrollLeft: function getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop: function getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge: function merge() {\n    var ret = {};\n    for (var i = 0; i < arguments.length; i++) {\n      utils.mix(ret, i < 0 || arguments.length <= i ? undefined : arguments[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0\n};\nmix(utils, domUtils);\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\nvar getParent = utils.getParent;\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  }\n  // ie 这个也不是完全可行\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n  var doc = utils.getDocument(element);\n  var body = doc.body;\n  var parent;\n  var positionStyle = utils.css(element, 'position');\n  var skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html' ? null : getParent(element);\n  }\n  for (parent = getParent(element); parent && parent !== body && parent.nodeType !== 9; parent = getParent(parent)) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\nvar getParent$1 = utils.getParent;\nfunction isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n  var doc = utils.getDocument(element);\n  var body = doc.body;\n  var parent = null;\n  for (parent = getParent$1(element);\n  // 修复元素位于 document.documentElement 下导致崩溃问题\n  parent && parent !== body && parent !== doc; parent = getParent$1(parent)) {\n    var positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * 获得元素的显示部分的区域\n */\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  var visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity\n  };\n  var el = getOffsetParent(element);\n  var doc = utils.getDocument(element);\n  var win = doc.defaultView || doc.parentWindow;\n  var body = doc.body;\n  var documentElement = doc.documentElement;\n\n  // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if ((navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n    // body may have overflow set on it, yet we still get the entire\n    // viewport. In some browsers, el.offsetParent may be\n    // document.documentElement, so check for that too.\n    el !== body && el !== documentElement && utils.css(el, 'overflow') !== 'visible') {\n      var pos = utils.offset(el);\n      // add border\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(visibleRect.right,\n      // consider area without scrollBar\n      pos.left + el.clientWidth);\n      visibleRect.bottom = Math.min(visibleRect.bottom, pos.top + el.clientHeight);\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  }\n\n  // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n  var originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    var position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n  var scrollX = utils.getWindowScrollLeft(win);\n  var scrollY = utils.getWindowScrollTop(win);\n  var viewportWidth = utils.viewportWidth(win);\n  var viewportHeight = utils.viewportHeight(win);\n  var documentWidth = documentElement.scrollWidth;\n  var documentHeight = documentElement.scrollHeight;\n\n  // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n  var bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  }\n\n  // Reset element position after calculate the visible area\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    var maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n    var maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n  return visibleRect.top >= 0 && visibleRect.left >= 0 && visibleRect.bottom > visibleRect.top && visibleRect.right > visibleRect.left ? visibleRect : null;\n}\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  var pos = utils.clone(elFuturePos);\n  var size = {\n    width: elRegion.width,\n    height: elRegion.height\n  };\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  }\n\n  // Left edge inside and right edge outside viewport, try to resize it.\n  if (overflow.resizeWidth && pos.left >= visibleRect.left && pos.left + size.width > visibleRect.right) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  }\n\n  // Right edge outside viewport, try to move it.\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  }\n\n  // Top edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  }\n\n  // Top edge inside and bottom edge outside viewport, try to resize it.\n  if (overflow.resizeHeight && pos.top >= visibleRect.top && pos.top + size.height > visibleRect.bottom) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  }\n\n  // Bottom edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n  return utils.mix(pos, size);\n}\nfunction getRegion(node) {\n  var offset;\n  var w;\n  var h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    var win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win)\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\n/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\n\nfunction getAlignOffset(region, align) {\n  var V = align.charAt(0);\n  var H = align.charAt(1);\n  var w = region.width;\n  var h = region.height;\n  var x = region.left;\n  var y = region.top;\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n  return {\n    left: x,\n    top: y\n  };\n}\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  var p1 = getAlignOffset(refNodeRegion, points[1]);\n  var p2 = getAlignOffset(elRegion, points[0]);\n  var diff = [p2.left - p1.left, p2.top - p1.top];\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1])\n  };\n}\n\n/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\n// http://yiminghe.iteye.com/blog/1124720\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.left < visibleRect.left || elFuturePos.left + elRegion.width > visibleRect.right;\n}\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.top < visibleRect.top || elFuturePos.top + elRegion.height > visibleRect.bottom;\n}\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.left > visibleRect.right || elFuturePos.left + elRegion.width < visibleRect.left;\n}\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.top > visibleRect.bottom || elFuturePos.top + elRegion.height < visibleRect.top;\n}\nfunction flip(points, reg, map) {\n  var ret = [];\n  utils.each(points, function (p) {\n    ret.push(p.replace(reg, function (m) {\n      return map[m];\n    }));\n  });\n  return ret;\n}\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\nfunction convertOffset(str, offsetLen) {\n  var n;\n  if (/%$/.test(str)) {\n    n = parseInt(str.substring(0, str.length - 1), 10) / 100 * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  var points = align.points;\n  var offset = align.offset || [0, 0];\n  var targetOffset = align.targetOffset || [0, 0];\n  var overflow = align.overflow;\n  var source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  var newOverflowCfg = {};\n  var fail = 0;\n  var alwaysByViewport = !!(overflow && overflow.alwaysByViewport);\n  // 当前节点可以被放置的显示区域\n  var visibleRect = getVisibleRectForElement(source, alwaysByViewport);\n  // 当前节点所占的区域, left/top/width/height\n  var elRegion = getRegion(source);\n  // 将 offset 转换成数值，支持百分比\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion);\n  // 当前节点将要被放置的位置\n  var elFuturePos = getElFuturePos(elRegion, tgtRegion, points, offset, targetOffset);\n  // 当前节点将要所处的区域\n  var newElRegion = utils.merge(elRegion, elFuturePos);\n\n  // 如果可视区域不能完全放置当前节点时允许调整\n  if (visibleRect && (overflow.adjustX || overflow.adjustY) && isTgtRegionVisible) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        var newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l'\n        });\n        // 偏移量也反下\n        var newOffset = flipOffset(offset, 0);\n        var newTargetOffset = flipOffset(targetOffset, 0);\n        var newElFuturePos = getElFuturePos(elRegion, tgtRegion, newPoints, newOffset, newTargetOffset);\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        var _newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't'\n        });\n        // 偏移量也反下\n        var _newOffset = flipOffset(offset, 1);\n        var _newTargetOffset = flipOffset(targetOffset, 1);\n        var _newElFuturePos = getElFuturePos(elRegion, tgtRegion, _newPoints, _newOffset, _newTargetOffset);\n        if (!isCompleteFailY(_newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = _newPoints;\n          offset = _newOffset;\n          targetOffset = _newTargetOffset;\n        }\n      }\n    }\n\n    // 如果失败，重新计算当前节点将要被放置的位置\n    if (fail) {\n      elFuturePos = getElFuturePos(elRegion, tgtRegion, points, offset, targetOffset);\n      utils.mix(newElRegion, elFuturePos);\n    }\n    var isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    var isStillFailY = isFailY(elFuturePos, elRegion, visibleRect);\n    // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n    if (isStillFailX || isStillFailY) {\n      var _newPoints2 = points;\n\n      // 重置对应部分的翻转逻辑\n      if (isStillFailX) {\n        _newPoints2 = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l'\n        });\n      }\n      if (isStillFailY) {\n        _newPoints2 = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't'\n        });\n      }\n      points = _newPoints2;\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    }\n    // 2. 只有指定了可以调整当前方向才调整\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY;\n\n    // 确实要调整，甚至可能会调整高度宽度\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(elFuturePos, elRegion, visibleRect, newOverflowCfg);\n    }\n  }\n\n  // need judge to in case set fixed with in css on height auto element\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(source, 'width', utils.width(source) + newElRegion.width - elRegion.width);\n  }\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(source, 'height', utils.height(source) + newElRegion.height - elRegion.height);\n  }\n\n  // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n  utils.offset(source, {\n    left: newElRegion.left,\n    top: newElRegion.top\n  }, {\n    useCssRight: align.useCssRight,\n    useCssBottom: align.useCssBottom,\n    useCssTransform: align.useCssTransform,\n    ignoreShake: align.ignoreShake\n  });\n  return {\n    points: points,\n    offset: offset,\n    targetOffset: targetOffset,\n    overflow: newOverflowCfg\n  };\n}\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  var visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  var targetRegion = getRegion(target);\n  return !visibleRect || targetRegion.left + targetRegion.width <= visibleRect.left || targetRegion.top + targetRegion.height <= visibleRect.top || targetRegion.left >= visibleRect.right || targetRegion.top >= visibleRect.bottom;\n}\nfunction alignElement(el, refNode, align) {\n  var target = align.target || refNode;\n  var refNodeRegion = getRegion(target);\n  var isTargetNotOutOfVisible = !isOutOfVisibleRect(target, align.overflow && align.overflow.alwaysByViewport);\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\nalignElement.__getOffsetParent = getOffsetParent;\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  var pageX;\n  var pageY;\n  var doc = utils.getDocument(el);\n  var win = doc.defaultView || doc.parentWindow;\n  var scrollX = utils.getWindowScrollLeft(win);\n  var scrollY = utils.getWindowScrollTop(win);\n  var viewportWidth = utils.viewportWidth(win);\n  var viewportHeight = utils.viewportHeight(win);\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n  var tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0\n  };\n  var pointInView = pageX >= 0 && pageX <= scrollX + viewportWidth && pageY >= 0 && pageY <= scrollY + viewportHeight;\n\n  // Provide default target point\n  var points = [align.points[0], 'cc'];\n  return doAlign(el, tgtRegion, _objectSpread2(_objectSpread2({}, align), {}, {\n    points: points\n  }), pointInView);\n}\nexport default alignElement;\nexport { alignElement, alignPoint };", "map": {"version": 3, "names": ["vendorPrefix", "jsCssMap", "Webkit", "<PERSON><PERSON>", "ms", "O", "getVendorPrefix", "undefined", "style", "document", "createElement", "testProp", "key", "getTransitionName", "concat", "getTransformName", "setTransitionProperty", "node", "value", "name", "transitionProperty", "setTransform", "transform", "getTransitionProperty", "getTransformXY", "window", "getComputedStyle", "getPropertyValue", "matrix", "replace", "split", "x", "parseFloat", "y", "matrix2d", "matrix3d", "setTransformXY", "xy", "arr", "match2d", "match", "map", "item", "join", "match3d", "RE_NUM", "source", "getComputedStyleX", "forceRelayout", "elem", "originalStyle", "display", "offsetHeight", "css", "el", "v", "_typeof", "i", "hasOwnProperty", "getClientPosition", "box", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "Math", "floor", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "method", "d", "getScrollLeft", "getScrollTop", "getOffset", "pos", "defaultView", "parentWindow", "isWindow", "obj", "getDocument", "nodeType", "_getComputedStyle", "cs", "computedStyle", "val", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "rsLeft", "pixelLeft", "getOffsetDirection", "dir", "option", "useCssRight", "useCssBottom", "oppositeOffsetDirection", "setLeftTop", "offset", "position", "presetH", "presetV", "horizontalProperty", "verticalProperty", "oppositeHorizontalProperty", "oppositeVerticalProperty", "originalTransition", "originalOffset", "old", "preset", "off", "_key", "_dir", "_off", "setTransform$1", "originalXY", "resultXY", "setOffset", "ignoreShake", "oriOffset", "oLeft", "toFixed", "oTop", "tLeft", "tTop", "useCssTransform", "each", "fn", "length", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "call", "getPBMWidth", "props", "which", "prop", "j", "cssProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getParent", "element", "parent", "host", "parentNode", "refWin", "max", "win", "documentElementProp", "compatMode", "getWH", "ex", "extra", "viewportWidth", "viewportHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "width", "height", "isBorderBox", "cssBoxValue", "Number", "borderBoxValueOrIsBorderBox", "slice", "cssShow", "visibility", "getWHIgnoreDisplay", "_len", "arguments", "args", "Array", "_key2", "offsetWidth", "apply", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "mix", "to", "from", "utils", "getWindow", "setTimeout", "clone", "overflow", "getWindowScrollLeft", "getWindowScrollTop", "merge", "getOffsetParent", "positionStyle", "skipStatic", "nodeName", "toLowerCase", "getParent$1", "isAncestorFixed", "getVisibleRectForElement", "alwaysByViewport", "visibleRect", "right", "Infinity", "bottom", "navigator", "userAgent", "indexOf", "clientWidth", "min", "clientHeight", "originalPosition", "scrollX", "scrollY", "documentWidth", "scrollWidth", "documentHeight", "scrollHeight", "bodyStyle", "overflowX", "innerWidth", "overflowY", "innerHeight", "maxVisibleWidth", "maxVisibleHeight", "adjustForViewport", "elFuturePos", "elRegion", "size", "adjustX", "resizeWidth", "adjustY", "resizeHeight", "getRegion", "h", "outerWidth", "outerHeight", "getAlignOffset", "region", "align", "V", "H", "getElFuturePos", "refNodeRegion", "points", "targetOffset", "p1", "p2", "diff", "round", "isFailX", "isFailY", "isCompleteFailX", "isCompleteFailY", "flip", "reg", "p", "push", "m", "flipOffset", "index", "convertOffset", "str", "offsetLen", "n", "parseInt", "substring", "normalizeOffset", "doAlign", "tgtRegion", "isTgtRegionVisible", "newOverflowCfg", "fail", "newElRegion", "newPoints", "l", "r", "newOffset", "newTargetOffset", "newElFuturePos", "_newPoints", "t", "b", "_newOffset", "_newTargetOffset", "_newElFuturePos", "isStillFailX", "isStillFailY", "_newPoints2", "isOutOfVisibleRect", "target", "targetRegion", "alignElement", "refNode", "isTargetNotOutOfVisible", "__getOffsetParent", "__getVisibleRectForElement", "alignPoint", "tgtPoint", "pageX", "pageY", "clientX", "clientY", "pointInView", "_objectSpread2"], "sources": ["../../src/propertyUtils.js", "../../src/utils.js", "../../src/getOffsetParent.js", "../../src/isAncestorFixed.js", "../../src/getVisibleRectForElement.js", "../../src/adjustForViewport.js", "../../src/getRegion.js", "../../src/getAlignOffset.js", "../../src/getElFuturePos.js", "../../src/align/align.js", "../../src/align/alignElement.js", "../../src/align/alignPoint.js"], "sourcesContent": ["let vendorPrefix;\n\nconst jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-',\n};\n\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  const style = document.createElement('p').style;\n  const testProp = 'Transform';\n  for (const key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\n\nfunction getTransitionName() {\n  return getVendorPrefix()\n    ? `${getVendorPrefix()}TransitionProperty`\n    : 'transitionProperty';\n}\n\nexport function getTransformName() {\n  return getVendorPrefix() ? `${getVendorPrefix()}Transform` : 'transform';\n}\n\nexport function setTransitionProperty(node, value) {\n  const name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\n\nfunction setTransform(node, value) {\n  const name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\n\nexport function getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\n\nexport function getTransformXY(node) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    const matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0),\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n  };\n}\n\nconst matrix2d = /matrix\\((.*)\\)/;\nconst matrix3d = /matrix3d\\((.*)\\)/;\n\nexport function setTransformXY(node, xy) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    let arr;\n    let match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, `matrix(${arr.join(',')})`);\n    } else {\n      const match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, `matrix3d(${arr.join(',')})`);\n    }\n  } else {\n    setTransform(\n      node,\n      `translateX(${xy.x}px) translateY(${xy.y}px) translateZ(0)`,\n    );\n  }\n}\n", "import {\n  setTransitionProperty,\n  getTransitionProperty,\n  getTransformXY,\n  setTransformXY,\n  getTransformName,\n} from './propertyUtils';\n\nconst RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nlet getComputedStyleX;\n\n// https://stackoverflow.com/a/3485654/3040605\nfunction forceRelayout(elem) {\n  const originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n  elem.style.display = originalStyle;\n}\n\nfunction css(el, name, v) {\n  let value = v;\n  if (typeof name === 'object') {\n    for (const i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\n\nfunction getClientPosition(elem) {\n  let box;\n  let x;\n  let y;\n  const doc = elem.ownerDocument;\n  const body = doc.body;\n  const docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top);\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nfunction getScroll(w, top) {\n  let ret = w[`page${top ? 'Y' : 'X'}Offset`];\n  const method = `scroll${top ? 'Top' : 'Left'}`;\n  if (typeof ret !== 'number') {\n    const d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  const pos = getClientPosition(el);\n  const doc = el.ownerDocument;\n  const w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\n\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\n\nfunction _getComputedStyle(elem, name, cs) {\n  let computedStyle = cs;\n  let val = '';\n  const d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nconst _RE_NUM_NO_PX = new RegExp(`^(${RE_NUM})(?!px)[a-z%]+$`, 'i');\nconst RE_POS = /^(top|right|bottom|left)$/;\nconst CURRENT_STYLE = 'currentStyle';\nconst RUNTIME_STYLE = 'runtimeStyle';\nconst LEFT = 'left';\nconst PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  let ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    const style = elem.style;\n    const left = style[LEFT];\n    const rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\n\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle\n    ? _getComputedStyle\n    : _getComputedStyleIE;\n}\n\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\n\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n}\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  let presetH = -999;\n  let presetV = -999;\n  const horizontalProperty = getOffsetDirection('left', option);\n  const verticalProperty = getOffsetDirection('top', option);\n  const oppositeHorizontalProperty = oppositeOffsetDirection(\n    horizontalProperty,\n  );\n  const oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  let originalTransition = '';\n  const originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = `${presetH}px`;\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = `${presetV}px`;\n  }\n  // force relayout\n  forceRelayout(elem);\n  const old = getOffset(elem);\n  const originalStyle = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const preset = key === 'left' ? presetH : presetV;\n      const off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle);\n  // force relayout\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  const ret = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const off = offset[key] - originalOffset[key];\n      if (key === dir) {\n        ret[dir] = originalStyle[dir] + off;\n      } else {\n        ret[dir] = originalStyle[dir] - off;\n      }\n    }\n  }\n  css(elem, ret);\n}\n\nfunction setTransform(elem, offset) {\n  const originalOffset = getOffset(elem);\n  const originalXY = getTransformXY(elem);\n  const resultXY = { x: originalXY.x, y: originalXY.y };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\n\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    const oriOffset = getOffset(elem);\n\n    const oLeft = oriOffset.left.toFixed(0);\n    const oTop = oriOffset.top.toFixed(0);\n    const tLeft = offset.left.toFixed(0);\n    const tTop = offset.top.toFixed(0);\n\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (\n    option.useCssTransform &&\n    getTransformName() in document.body.style\n  ) {\n    setTransform(elem, offset, option);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\n\nfunction each(arr, fn) {\n  for (let i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nconst BOX_MODELS = ['margin', 'border', 'padding'];\nconst CONTENT_INDEX = -1;\nconst PADDING_INDEX = 2;\nconst BORDER_INDEX = 1;\nconst MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  const old = {};\n  const style = elem.style;\n  let name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  let value = 0;\n  let prop;\n  let j;\n  let i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        let cssProp;\n        if (prop === 'border') {\n          cssProp = `${prop}${which[i]}Width`;\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\n\nconst domUtils = {\n  getParent(element) {\n    let parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  },\n};\n\neach(['Width', 'Height'], name => {\n  domUtils[`doc${name}`] = refWin => {\n    const d = refWin.document;\n    return Math.max(\n      // firefox chrome documentElement.scrollHeight< body.scrollHeight\n      // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n      d.documentElement[`scroll${name}`],\n      // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n      d.body[`scroll${name}`],\n      domUtils[`viewport${name}`](d),\n    );\n  };\n\n  domUtils[`viewport${name}`] = win => {\n    // pc browser includes scrollbar in window.innerWidth\n    const prop = `client${name}`;\n    const doc = win.document;\n    const body = doc.body;\n    const documentElement = doc.documentElement;\n    const documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return (\n      (doc.compatMode === 'CSS1Compat' && documentElementProp) ||\n      (body && body[prop]) ||\n      documentElementProp\n    );\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, ex) {\n  let extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width'\n      ? domUtils.viewportWidth(elem)\n      : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width'\n      ? domUtils.docWidth(elem)\n      : domUtils.docHeight(elem);\n  }\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  let borderBoxValue =\n    name === 'width'\n      ? Math.floor(elem.getBoundingClientRect().width)\n      : Math.floor(elem.getBoundingClientRect().height);\n  const isBorderBox = isBorderBoxFn(elem);\n  let cssBoxValue = 0;\n  if (\n    borderBoxValue === null ||\n    borderBoxValue === undefined ||\n    borderBoxValue <= 0\n  ) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (\n      cssBoxValue === null ||\n      cssBoxValue === undefined ||\n      Number(cssBoxValue) < 0\n    ) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = Math.floor(parseFloat(cssBoxValue)) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  const borderBoxValueOrIsBorderBox =\n    borderBoxValue !== undefined || isBorderBox;\n  const val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return (\n      val +\n      (extra === PADDING_INDEX\n        ? -getPBMWidth(elem, ['border'], which)\n        : getPBMWidth(elem, ['margin'], which))\n    );\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\n\nconst cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block',\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay(...args) {\n  let val;\n  const elem = args[0];\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, () => {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\n\neach(['width', 'height'], name => {\n  const first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[`outer${first}`] = (el, includeMargin) => {\n    return (\n      el &&\n      getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX)\n    );\n  };\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = (elem, v) => {\n    let val = v;\n    if (val !== undefined) {\n      if (elem) {\n        const isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\n\nfunction mix(to, from) {\n  for (const i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\n\nconst utils = {\n  getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    const doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument,\n  offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow,\n  each,\n  css,\n  clone(obj) {\n    let i;\n    const ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    const overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix,\n  getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge(...args) {\n    const ret = {};\n    for (let i = 0; i < args.length; i++) {\n      utils.mix(ret, args[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0,\n};\n\nmix(utils, domUtils);\n\nexport default utils;\n", "import utils from './utils';\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\nconst { getParent } = utils;\n\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  }\n  // ie 这个也不是完全可行\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent;\n  let positionStyle = utils.css(element, 'position');\n  const skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html'\n      ? null\n      : getParent(element);\n  }\n\n  for (\n    parent = getParent(element);\n    parent && parent !== body && parent.nodeType !== 9;\n    parent = getParent(parent)\n  ) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\n\nexport default getOffsetParent;\n", "import utils from './utils';\n\nconst { getParent } = utils;\n\nexport default function isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent = null;\n  for (\n    parent = getParent(element);\n    // 修复元素位于 document.documentElement 下导致崩溃问题\n    parent && parent !== body && parent !== doc;\n    parent = getParent(parent)\n  ) {\n    const positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n", "import utils from './utils';\nimport getOffsetParent from './getOffsetParent';\nimport isAncestorFixed from './isAncestorFixed';\n\n/**\n * 获得元素的显示部分的区域\n */\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  const visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity,\n  };\n  let el = getOffsetParent(element);\n  const doc = utils.getDocument(element);\n  const win = doc.defaultView || doc.parentWindow;\n  const body = doc.body;\n  const documentElement = doc.documentElement;\n\n  // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if (\n      (navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n      // body may have overflow set on it, yet we still get the entire\n      // viewport. In some browsers, el.offsetParent may be\n      // document.documentElement, so check for that too.\n      (el !== body &&\n        el !== documentElement &&\n        utils.css(el, 'overflow') !== 'visible')\n    ) {\n      const pos = utils.offset(el);\n      // add border\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(\n        visibleRect.right,\n        // consider area without scrollBar\n        pos.left + el.clientWidth,\n      );\n      visibleRect.bottom = Math.min(\n        visibleRect.bottom,\n        pos.top + el.clientHeight,\n      );\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  }\n\n  // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n  let originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    const position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n  let documentWidth = documentElement.scrollWidth;\n  let documentHeight = documentElement.scrollHeight;\n\n  // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n  const bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  }\n\n  // Reset element position after calculate the visible area\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    const maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n\n    const maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n\n  return visibleRect.top >= 0 &&\n    visibleRect.left >= 0 &&\n    visibleRect.bottom > visibleRect.top &&\n    visibleRect.right > visibleRect.left\n    ? visibleRect\n    : null;\n}\n\nexport default getVisibleRectForElement;\n", "import utils from './utils';\n\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  const pos = utils.clone(elFuturePos);\n  const size = {\n    width: elRegion.width,\n    height: elRegion.height,\n  };\n\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  }\n\n  // Left edge inside and right edge outside viewport, try to resize it.\n  if (\n    overflow.resizeWidth &&\n    pos.left >= visibleRect.left &&\n    pos.left + size.width > visibleRect.right\n  ) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  }\n\n  // Right edge outside viewport, try to move it.\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  }\n\n  // Top edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  }\n\n  // Top edge inside and bottom edge outside viewport, try to resize it.\n  if (\n    overflow.resizeHeight &&\n    pos.top >= visibleRect.top &&\n    pos.top + size.height > visibleRect.bottom\n  ) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  }\n\n  // Bottom edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n\n  return utils.mix(pos, size);\n}\n\nexport default adjustForViewport;\n", "import utils from './utils';\n\nfunction getRegion(node) {\n  let offset;\n  let w;\n  let h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    const win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win),\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\nexport default getRegion;\n", "/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\n\nfunction getAlignOffset(region, align) {\n  const V = align.charAt(0);\n  const H = align.charAt(1);\n  const w = region.width;\n  const h = region.height;\n\n  let x = region.left;\n  let y = region.top;\n\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nexport default getAlignOffset;\n", "import getAlignOffset from './getAlignOffset';\n\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  const p1 = getAlignOffset(refNodeRegion, points[1]);\n  const p2 = getAlignOffset(elRegion, points[0]);\n  const diff = [p2.left - p1.left, p2.top - p1.top];\n\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1]),\n  };\n}\n\nexport default getElFuturePos;\n", "/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\nimport utils from '../utils';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport adjustForViewport from '../adjustForViewport';\nimport getRegion from '../getRegion';\nimport getElFuturePos from '../getElFuturePos';\n\n// http://yiminghe.iteye.com/blog/1124720\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left < visibleRect.left ||\n    elFuturePos.left + elRegion.width > visibleRect.right\n  );\n}\n\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top < visibleRect.top ||\n    elFuturePos.top + elRegion.height > visibleRect.bottom\n  );\n}\n\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left > visibleRect.right ||\n    elFuturePos.left + elRegion.width < visibleRect.left\n  );\n}\n\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top > visibleRect.bottom ||\n    elFuturePos.top + elRegion.height < visibleRect.top\n  );\n}\n\nfunction flip(points, reg, map) {\n  const ret = [];\n  utils.each(points, p => {\n    ret.push(\n      p.replace(reg, m => {\n        return map[m];\n      }),\n    );\n  });\n  return ret;\n}\n\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\n\nfunction convertOffset(str, offsetLen) {\n  let n;\n  if (/%$/.test(str)) {\n    n = (parseInt(str.substring(0, str.length - 1), 10) / 100) * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\n\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  let points = align.points;\n  let offset = align.offset || [0, 0];\n  let targetOffset = align.targetOffset || [0, 0];\n  let overflow = align.overflow;\n  const source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  const newOverflowCfg = {};\n  let fail = 0;\n  const alwaysByViewport = !!(overflow && overflow.alwaysByViewport);\n  // 当前节点可以被放置的显示区域\n  const visibleRect = getVisibleRectForElement(source, alwaysByViewport);\n  // 当前节点所占的区域, left/top/width/height\n  const elRegion = getRegion(source);\n  // 将 offset 转换成数值，支持百分比\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion);\n  // 当前节点将要被放置的位置\n  let elFuturePos = getElFuturePos(\n    elRegion,\n    tgtRegion,\n    points,\n    offset,\n    targetOffset,\n  );\n  // 当前节点将要所处的区域\n  let newElRegion = utils.merge(elRegion, elFuturePos);\n\n  // 如果可视区域不能完全放置当前节点时允许调整\n  if (\n    visibleRect &&\n    (overflow.adjustX || overflow.adjustY) &&\n    isTgtRegionVisible\n  ) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 0);\n        const newTargetOffset = flipOffset(targetOffset, 0);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 1);\n        const newTargetOffset = flipOffset(targetOffset, 1);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailY(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    // 如果失败，重新计算当前节点将要被放置的位置\n    if (fail) {\n      elFuturePos = getElFuturePos(\n        elRegion,\n        tgtRegion,\n        points,\n        offset,\n        targetOffset,\n      );\n      utils.mix(newElRegion, elFuturePos);\n    }\n    const isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    const isStillFailY = isFailY(elFuturePos, elRegion, visibleRect);\n    // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n    if (isStillFailX || isStillFailY) {\n      let newPoints = points;\n\n      // 重置对应部分的翻转逻辑\n      if (isStillFailX) {\n        newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n      }\n      if (isStillFailY) {\n        newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n      }\n\n      points = newPoints;\n\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    }\n    // 2. 只有指定了可以调整当前方向才调整\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY;\n\n    // 确实要调整，甚至可能会调整高度宽度\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(\n        elFuturePos,\n        elRegion,\n        visibleRect,\n        newOverflowCfg,\n      );\n    }\n  }\n\n  // need judge to in case set fixed with in css on height auto element\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(\n      source,\n      'width',\n      utils.width(source) + newElRegion.width - elRegion.width,\n    );\n  }\n\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(\n      source,\n      'height',\n      utils.height(source) + newElRegion.height - elRegion.height,\n    );\n  }\n\n  // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n  utils.offset(\n    source,\n    {\n      left: newElRegion.left,\n      top: newElRegion.top,\n    },\n    {\n      useCssRight: align.useCssRight,\n      useCssBottom: align.useCssBottom,\n      useCssTransform: align.useCssTransform,\n      ignoreShake: align.ignoreShake,\n    },\n  );\n\n  return {\n    points,\n    offset,\n    targetOffset,\n    overflow: newOverflowCfg,\n  };\n}\n\nexport default doAlign;\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n", "import doAlign from './align';\nimport getOffsetParent from '../getOffsetParent';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport getRegion from '../getRegion';\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  const visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  const targetRegion = getRegion(target);\n\n  return (\n    !visibleRect ||\n    targetRegion.left + targetRegion.width <= visibleRect.left ||\n    targetRegion.top + targetRegion.height <= visibleRect.top ||\n    targetRegion.left >= visibleRect.right ||\n    targetRegion.top >= visibleRect.bottom\n  );\n}\n\nfunction alignElement(el, refNode, align) {\n  const target = align.target || refNode;\n  const refNodeRegion = getRegion(target);\n\n  const isTargetNotOutOfVisible = !isOutOfVisibleRect(\n    target,\n    align.overflow && align.overflow.alwaysByViewport,\n  );\n\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\n\nalignElement.__getOffsetParent = getOffsetParent;\n\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\nexport default alignElement;\n", "import utils from '../utils';\nimport doAlign from './align';\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  let pageX;\n  let pageY;\n\n  const doc = utils.getDocument(el);\n  const win = doc.defaultView || doc.parentWindow;\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n\n  const tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0,\n  };\n\n  const pointInView =\n    pageX >= 0 &&\n    pageX <= scrollX + viewportWidth &&\n    (pageY >= 0 && pageY <= scrollY + viewportHeight);\n\n  // Provide default target point\n  const points = [align.points[0], 'cc'];\n\n  return doAlign(el, tgtRegion, { ...align, points }, pointInView);\n}\n\nexport default alignPoint;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,YAAY;AAEhB,IAAMC,QAAQ,GAAG;EACfC,MAAM,EAAE,UAAU;EAClBC,GAAG,EAAE,OAAO;;EAEZC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC;AAED,SAASC,eAAeA,CAAA,EAAG;EACzB,IAAIN,YAAY,KAAKO,SAAS,EAAE;IAC9B,OAAOP,YAAY;;EAErBA,YAAY,GAAG,EAAE;EACjB,IAAMQ,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAACF,KAAK;EAC/C,IAAMG,QAAQ,GAAG,WAAW;EAC5B,KAAK,IAAMC,GAAG,IAAIX,QAAQ,EAAE;IAC1B,IAAIW,GAAG,GAAGD,QAAQ,IAAIH,KAAK,EAAE;MAC3BR,YAAY,GAAGY,GAAG;;;EAGtB,OAAOZ,YAAY;AACrB;AAEA,SAASa,iBAAiBA,CAAA,EAAG;EAC3B,OAAOP,eAAe,EAAE,MAAAQ,MAAA,CACjBR,eAAe,EAAE,0BACpB,oBAAoB;AAC1B;AAEO,SAASS,gBAAgBA,CAAA,EAAG;EACjC,OAAOT,eAAe,EAAE,MAAAQ,MAAA,CAAMR,eAAe,EAAE,iBAAc,WAAW;AAC1E;AAEO,SAASU,qBAAqBA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACjD,IAAMC,IAAI,GAAGN,iBAAiB,EAAE;EAChC,IAAIM,IAAI,EAAE;IACRF,IAAI,CAACT,KAAK,CAACW,IAAI,CAAC,GAAGD,KAAK;IACxB,IAAIC,IAAI,KAAK,oBAAoB,EAAE;MACjCF,IAAI,CAACT,KAAK,CAACY,kBAAkB,GAAGF,KAAK;;;AAG3C;AAEA,SAASG,YAAYA,CAACJ,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAMC,IAAI,GAAGJ,gBAAgB,EAAE;EAC/B,IAAII,IAAI,EAAE;IACRF,IAAI,CAACT,KAAK,CAACW,IAAI,CAAC,GAAGD,KAAK;IACxB,IAAIC,IAAI,KAAK,WAAW,EAAE;MACxBF,IAAI,CAACT,KAAK,CAACc,SAAS,GAAGJ,KAAK;;;AAGlC;AAEO,SAASK,qBAAqBA,CAACN,IAAI,EAAE;EAC1C,OAAOA,IAAI,CAACT,KAAK,CAACY,kBAAkB,IAAIH,IAAI,CAACT,KAAK,CAACK,iBAAiB,EAAE,CAAC;AACzE;AAEO,SAASW,cAAcA,CAACP,IAAI,EAAE;EACnC,IAAMT,KAAK,GAAGiB,MAAM,CAACC,gBAAgB,CAACT,IAAI,EAAE,IAAI,CAAC;EACjD,IAAMK,SAAS,GACbd,KAAK,CAACmB,gBAAgB,CAAC,WAAW,CAAC,IACnCnB,KAAK,CAACmB,gBAAgB,CAACZ,gBAAgB,EAAE,CAAC;EAC5C,IAAIO,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACrC,IAAMM,MAAM,GAAGN,SAAS,CAACO,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9D,OAAO;MACLC,CAAC,EAAEC,UAAU,CAACJ,MAAM,CAAC,EAAE,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACzCK,CAAC,EAAED,UAAU,CAACJ,MAAM,CAAC,EAAE,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;KACzC;;EAEH,OAAO;IACLG,CAAC,EAAE,CAAC;IACJE,CAAC,EAAE;GACJ;AACH;AAEA,IAAMC,QAAQ,GAAG,gBAAgB;AACjC,IAAMC,QAAQ,GAAG,kBAAkB;AAE5B,SAASC,cAAcA,CAACnB,IAAI,EAAEoB,EAAE,EAAE;EACvC,IAAM7B,KAAK,GAAGiB,MAAM,CAACC,gBAAgB,CAACT,IAAI,EAAE,IAAI,CAAC;EACjD,IAAMK,SAAS,GACbd,KAAK,CAACmB,gBAAgB,CAAC,WAAW,CAAC,IACnCnB,KAAK,CAACmB,gBAAgB,CAACZ,gBAAgB,EAAE,CAAC;EAC5C,IAAIO,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACrC,IAAIgB,GAAG;IACP,IAAIC,OAAO,GAAGjB,SAAS,CAACkB,KAAK,CAACN,QAAQ,CAAC;IACvC,IAAIK,OAAO,EAAE;MACXA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;MACpBD,GAAG,GAAGC,OAAO,CAACT,KAAK,CAAC,GAAG,CAAC,CAACW,GAAG,CAAC,UAAAC,IAAI,EAAI;QACnC,OAAOV,UAAU,CAACU,IAAI,EAAE,EAAE,CAAC;OAC5B,CAAC;MACFJ,GAAG,CAAC,CAAC,CAAC,GAAGD,EAAE,CAACN,CAAC;MACbO,GAAG,CAAC,CAAC,CAAC,GAAGD,EAAE,CAACJ,CAAC;MACbZ,YAAY,CAACJ,IAAI,YAAAH,MAAA,CAAYwB,GAAG,CAACK,IAAI,CAAC,GAAG,CAAC,OAAI;KAC/C,MAAM;MACL,IAAMC,OAAO,GAAGtB,SAAS,CAACkB,KAAK,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5CG,GAAG,GAAGM,OAAO,CAACd,KAAK,CAAC,GAAG,CAAC,CAACW,GAAG,CAAC,UAAAC,IAAI,EAAI;QACnC,OAAOV,UAAU,CAACU,IAAI,EAAE,EAAE,CAAC;OAC5B,CAAC;MACFJ,GAAG,CAAC,EAAE,CAAC,GAAGD,EAAE,CAACN,CAAC;MACdO,GAAG,CAAC,EAAE,CAAC,GAAGD,EAAE,CAACJ,CAAC;MACdZ,YAAY,CAACJ,IAAI,cAAAH,MAAA,CAAcwB,GAAG,CAACK,IAAI,CAAC,GAAG,CAAC,OAAI;;GAEnD,MAAM;IACLtB,YAAY,CACVJ,IAAI,gBAAAH,MAAA,CACUuB,EAAE,CAACN,CAAC,qBAAAjB,MAAA,CAAkBuB,EAAE,CAACJ,CAAC,uBACzC;;AAEL;ACvGA,IAAMY,MAAM,GAAG,uCAAuC,CAACC,MAAM;AAE7D,IAAIC,iBAAiB;;AAErB;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAMC,aAAa,GAAGD,IAAI,CAACzC,KAAK,CAAC2C,OAAO;EACxCF,IAAI,CAACzC,KAAK,CAAC2C,OAAO,GAAG,MAAM;EAC3BF,IAAI,CAACG,YAAY,CAAC;EAClBH,IAAI,CAACzC,KAAK,CAAC2C,OAAO,GAAGD,aAAa;AACpC;AAEA,SAASG,GAAGA,CAACC,EAAE,EAAEnC,IAAI,EAAEoC,CAAC,EAAE;EACxB,IAAIrC,KAAK,GAAGqC,CAAC;EACb,IAAIC,OAAA,CAAOrC,IAAI,MAAK,QAAQ,EAAE;IAC5B,KAAK,IAAMsC,CAAC,IAAItC,IAAI,EAAE;MACpB,IAAIA,IAAI,CAACuC,cAAc,CAACD,CAAC,CAAC,EAAE;QAC1BJ,GAAG,CAACC,EAAE,EAAEG,CAAC,EAAEtC,IAAI,CAACsC,CAAC,CAAC,CAAC;;;IAGvB,OAAOlD,SAAS;;EAElB,IAAI,OAAOW,KAAK,KAAK,WAAW,EAAE;IAChC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,MAAAJ,MAAA,CAAMI,KAAK,OAAI;;IAEtBoC,EAAE,CAAC9C,KAAK,CAACW,IAAI,CAAC,GAAGD,KAAK;IACtB,OAAOX,SAAS;;EAElB,OAAOwC,iBAAiB,CAACO,EAAE,EAAEnC,IAAI,CAAC;AACpC;AAEA,SAASwC,iBAAiBA,CAACV,IAAI,EAAE;EAC/B,IAAIW,GAAG;EACP,IAAI7B,CAAC;EACL,IAAIE,CAAC;EACL,IAAM4B,GAAG,GAAGZ,IAAI,CAACa,aAAa;EAC9B,IAAMC,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,IAAMC,OAAO,GAAGH,GAAG,IAAIA,GAAG,CAACI,eAAe;;EAE1CL,GAAG,GAAGX,IAAI,CAACiB,qBAAqB,EAAE;;;;;;EAMlCnC,CAAC,GAAGoC,IAAI,CAACC,KAAK,CAACR,GAAG,CAACS,IAAI,CAAC;EACxBpC,CAAC,GAAGkC,IAAI,CAACC,KAAK,CAACR,GAAG,CAACU,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;EAsBvBvC,CAAC,IAAIiC,OAAO,CAACO,UAAU,IAAIR,IAAI,CAACQ,UAAU,IAAI,CAAC;EAC/CtC,CAAC,IAAI+B,OAAO,CAACQ,SAAS,IAAIT,IAAI,CAACS,SAAS,IAAI,CAAC;EAE7C,OAAO;IACLH,IAAI,EAAEtC,CAAC;IACPuC,GAAG,EAAErC;GACN;AACH;AAEA,SAASwC,SAASA,CAACC,CAAC,EAAEJ,GAAG,EAAE;EACzB,IAAIK,GAAG,GAAGD,CAAC,QAAA5D,MAAA,CAAQwD,GAAG,GAAG,GAAG,GAAG,GAAG,YAAS;EAC3C,IAAMM,MAAM,YAAA9D,MAAA,CAAYwD,GAAG,GAAG,KAAK,GAAG,MAAM,CAAE;EAC9C,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAME,CAAC,GAAGH,CAAC,CAACjE,QAAQ;;IAEpBkE,GAAG,GAAGE,CAAC,CAACZ,eAAe,CAACW,MAAM,CAAC;IAC/B,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;;MAE3BA,GAAG,GAAGE,CAAC,CAACd,IAAI,CAACa,MAAM,CAAC;;;EAGxB,OAAOD,GAAG;AACZ;AAEA,SAASG,aAAaA,CAACJ,CAAC,EAAE;EACxB,OAAOD,SAAS,CAACC,CAAC,CAAC;AACrB;AAEA,SAASK,YAAYA,CAACL,CAAC,EAAE;EACvB,OAAOD,SAAS,CAACC,CAAC,EAAE,IAAI,CAAC;AAC3B;AAEA,SAASM,SAASA,CAAC1B,EAAE,EAAE;EACrB,IAAM2B,GAAG,GAAGtB,iBAAiB,CAACL,EAAE,CAAC;EACjC,IAAMO,GAAG,GAAGP,EAAE,CAACQ,aAAa;EAC5B,IAAMY,CAAC,GAAGb,GAAG,CAACqB,WAAW,IAAIrB,GAAG,CAACsB,YAAY;EAC7CF,GAAG,CAACZ,IAAI,IAAIS,aAAa,CAACJ,CAAC,CAAC;EAC5BO,GAAG,CAACX,GAAG,IAAIS,YAAY,CAACL,CAAC,CAAC;EAC1B,OAAOO,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,GAAG,EAAE;;;EAGrB,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK9E,SAAS,IAAI8E,GAAG,IAAIA,GAAG,CAAC5D,MAAM;AAC/D;AAEA,SAAS6D,WAAWA,CAACrE,IAAI,EAAE;EACzB,IAAImE,QAAQ,CAACnE,IAAI,CAAC,EAAE;IAClB,OAAOA,IAAI,CAACR,QAAQ;;EAEtB,IAAIQ,IAAI,CAACsE,QAAQ,KAAK,CAAC,EAAE;IACvB,OAAOtE,IAAI;;EAEb,OAAOA,IAAI,CAAC6C,aAAa;AAC3B;AAEA,SAAS0B,iBAAiBA,CAACvC,IAAI,EAAE9B,IAAI,EAAEsE,EAAE,EAAE;EACzC,IAAIC,aAAa,GAAGD,EAAE;EACtB,IAAIE,GAAG,GAAG,EAAE;EACZ,IAAMd,CAAC,GAAGS,WAAW,CAACrC,IAAI,CAAC;EAC3ByC,aAAa,GAAGA,aAAa,IAAIb,CAAC,CAACK,WAAW,CAACxD,gBAAgB,CAACuB,IAAI,EAAE,IAAI,CAAC;;;EAG3E,IAAIyC,aAAa,EAAE;IACjBC,GAAG,GAAGD,aAAa,CAAC/D,gBAAgB,CAACR,IAAI,CAAC,IAAIuE,aAAa,CAACvE,IAAI,CAAC;;EAGnE,OAAOwE,GAAG;AACZ;AAEA,IAAMC,aAAa,GAAG,IAAIC,MAAM,MAAA/E,MAAA,CAAM+B,MAAM,sBAAmB,GAAG,CAAC;AACnE,IAAMiD,MAAM,GAAG,2BAA2B;AAC1C,IAAMC,aAAa,GAAG,cAAc;AACpC,IAAMC,aAAa,GAAG,cAAc;AACpC,IAAMC,IAAI,GAAG,MAAM;AACnB,IAAMC,EAAE,GAAG,IAAI;AAEf,SAASC,mBAAmBA,CAAClD,IAAI,EAAE9B,IAAI,EAAE;;;EAGvC,IAAIwD,GAAG,GAAG1B,IAAI,CAAC8C,aAAa,CAAC,IAAI9C,IAAI,CAAC8C,aAAa,CAAC,CAAC5E,IAAI,CAAC;;;;;;;;;;;;EAY1D,IAAIyE,aAAa,CAACQ,IAAI,CAACzB,GAAG,CAAC,IAAI,CAACmB,MAAM,CAACM,IAAI,CAACjF,IAAI,CAAC,EAAE;;IAEjD,IAAMX,KAAK,GAAGyC,IAAI,CAACzC,KAAK;IACxB,IAAM6D,IAAI,GAAG7D,KAAK,CAACyF,IAAI,CAAC;IACxB,IAAMI,MAAM,GAAGpD,IAAI,CAAC+C,aAAa,CAAC,CAACC,IAAI,CAAC;;;IAGxChD,IAAI,CAAC+C,aAAa,CAAC,CAACC,IAAI,CAAC,GAAGhD,IAAI,CAAC8C,aAAa,CAAC,CAACE,IAAI,CAAC;;;IAGrDzF,KAAK,CAACyF,IAAI,CAAC,GAAG9E,IAAI,KAAK,UAAU,GAAG,KAAK,GAAGwD,GAAG,IAAI,CAAC;IACpDA,GAAG,GAAGnE,KAAK,CAAC8F,SAAS,GAAGJ,EAAE;;;IAG1B1F,KAAK,CAACyF,IAAI,CAAC,GAAG5B,IAAI;IAElBpB,IAAI,CAAC+C,aAAa,CAAC,CAACC,IAAI,CAAC,GAAGI,MAAM;;EAEpC,OAAO1B,GAAG,KAAK,EAAE,GAAG,MAAM,GAAGA,GAAG;AAClC;AAEA,IAAI,OAAOlD,MAAM,KAAK,WAAW,EAAE;EACjCsB,iBAAiB,GAAGtB,MAAM,CAACC,gBAAgB,GACvC8D,iBAAiB,GACjBW,mBAAmB;AACzB;AAEA,SAASI,kBAAkBA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACvC,IAAID,GAAG,KAAK,MAAM,EAAE;IAClB,OAAOC,MAAM,CAACC,WAAW,GAAG,OAAO,GAAGF,GAAG;;EAE3C,OAAOC,MAAM,CAACE,YAAY,GAAG,QAAQ,GAAGH,GAAG;AAC7C;AAEA,SAASI,uBAAuBA,CAACJ,GAAG,EAAE;EACpC,IAAIA,GAAG,KAAK,MAAM,EAAE;IAClB,OAAO,OAAO;GACf,MAAM,IAAIA,GAAG,KAAK,OAAO,EAAE;IAC1B,OAAO,MAAM;GACd,MAAM,IAAIA,GAAG,KAAK,KAAK,EAAE;IACxB,OAAO,QAAQ;GAChB,MAAM,IAAIA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,KAAK;;AAEhB;;AAEA;AACA,SAASK,UAAUA,CAAC5D,IAAI,EAAE6D,MAAM,EAAEL,MAAM,EAAE;;EAExC,IAAIpD,GAAG,CAACJ,IAAI,EAAE,UAAU,CAAC,KAAK,QAAQ,EAAE;IACtCA,IAAI,CAACzC,KAAK,CAACuG,QAAQ,GAAG,UAAU;;EAElC,IAAIC,OAAO,GAAG,CAAC,GAAG;EAClB,IAAIC,OAAO,GAAG,CAAC,GAAG;EAClB,IAAMC,kBAAkB,GAAGX,kBAAkB,CAAC,MAAM,EAAEE,MAAM,CAAC;EAC7D,IAAMU,gBAAgB,GAAGZ,kBAAkB,CAAC,KAAK,EAAEE,MAAM,CAAC;EAC1D,IAAMW,0BAA0B,GAAGR,uBAAuB,CACxDM,kBAAkB,CACnB;EACD,IAAMG,wBAAwB,GAAGT,uBAAuB,CAACO,gBAAgB,CAAC;EAE1E,IAAID,kBAAkB,KAAK,MAAM,EAAE;IACjCF,OAAO,GAAG,GAAG;;EAGf,IAAIG,gBAAgB,KAAK,KAAK,EAAE;IAC9BF,OAAO,GAAG,GAAG;;EAEf,IAAIK,kBAAkB,GAAG,EAAE;EAC3B,IAAMC,cAAc,GAAGvC,SAAS,CAAC/B,IAAI,CAAC;EACtC,IAAI,MAAM,IAAI6D,MAAM,IAAI,KAAK,IAAIA,MAAM,EAAE;IACvCQ,kBAAkB,GAAG/F,qBAAqB,CAAC0B,IAAI,CAAC,IAAI,EAAE;IACtDjC,qBAAqB,CAACiC,IAAI,EAAE,MAAM,CAAC;;EAErC,IAAI,MAAM,IAAI6D,MAAM,EAAE;IACpB7D,IAAI,CAACzC,KAAK,CAAC4G,0BAA0B,CAAC,GAAG,EAAE;IAC3CnE,IAAI,CAACzC,KAAK,CAAC0G,kBAAkB,CAAC,MAAApG,MAAA,CAAMkG,OAAO,OAAI;;EAEjD,IAAI,KAAK,IAAIF,MAAM,EAAE;IACnB7D,IAAI,CAACzC,KAAK,CAAC6G,wBAAwB,CAAC,GAAG,EAAE;IACzCpE,IAAI,CAACzC,KAAK,CAAC2G,gBAAgB,CAAC,MAAArG,MAAA,CAAMmG,OAAO,OAAI;;;EAG/CjE,aAAa,CAACC,IAAI,CAAC;EACnB,IAAMuE,GAAG,GAAGxC,SAAS,CAAC/B,IAAI,CAAC;EAC3B,IAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAMtC,GAAG,IAAIkG,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACpD,cAAc,CAAC9C,GAAG,CAAC,EAAE;MAC9B,IAAM4F,GAAG,GAAGD,kBAAkB,CAAC3F,GAAG,EAAE6F,MAAM,CAAC;MAC3C,IAAMgB,MAAM,GAAG7G,GAAG,KAAK,MAAM,GAAGoG,OAAO,GAAGC,OAAO;MACjD,IAAMS,GAAG,GAAGH,cAAc,CAAC3G,GAAG,CAAC,GAAG4G,GAAG,CAAC5G,GAAG,CAAC;MAC1C,IAAI4F,GAAG,KAAK5F,GAAG,EAAE;QACfsC,aAAa,CAACsD,GAAG,CAAC,GAAGiB,MAAM,GAAGC,GAAG;OAClC,MAAM;QACLxE,aAAa,CAACsD,GAAG,CAAC,GAAGiB,MAAM,GAAGC,GAAG;;;;EAIvCrE,GAAG,CAACJ,IAAI,EAAEC,aAAa,CAAC;;EAExBF,aAAa,CAACC,IAAI,CAAC;EACnB,IAAI,MAAM,IAAI6D,MAAM,IAAI,KAAK,IAAIA,MAAM,EAAE;IACvC9F,qBAAqB,CAACiC,IAAI,EAAEqE,kBAAkB,CAAC;;EAEjD,IAAM3C,GAAG,GAAG,EAAE;EACd,KAAK,IAAMgD,IAAG,IAAIb,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACpD,cAAc,CAACiE,IAAG,CAAC,EAAE;MAC9B,IAAMC,IAAG,GAAGrB,kBAAkB,CAACoB,IAAG,EAAElB,MAAM,CAAC;MAC3C,IAAMoB,IAAG,GAAGf,MAAM,CAACa,IAAG,CAAC,GAAGJ,cAAc,CAACI,IAAG,CAAC;MAC7C,IAAIA,IAAG,KAAKC,IAAG,EAAE;QACfjD,GAAG,CAACiD,IAAG,CAAC,GAAG1E,aAAa,CAAC0E,IAAG,CAAC,GAAGC,IAAG;OACpC,MAAM;QACLlD,GAAG,CAACiD,IAAG,CAAC,GAAG1E,aAAa,CAAC0E,IAAG,CAAC,GAAGC,IAAG;;;;EAIzCxE,GAAG,CAACJ,IAAI,EAAE0B,GAAG,CAAC;AAChB;AAEA,SAASmD,cAAYzG,CAAC4B,IAAI,EAAE6D,MAAM,EAAE;EAClC,IAAMS,cAAc,GAAGvC,SAAS,CAAC/B,IAAI,CAAC;EACtC,IAAM8E,UAAU,GAAGvG,cAAc,CAACyB,IAAI,CAAC;EACvC,IAAM+E,QAAQ,GAAG;IAAEjG,CAAC,EAAEgG,UAAU,CAAChG,CAAC;IAAEE,CAAC,EAAE8F,UAAU,CAAC9F;GAAG;EACrD,IAAI,MAAM,IAAI6E,MAAM,EAAE;IACpBkB,QAAQ,CAACjG,CAAC,GAAGgG,UAAU,CAAChG,CAAC,GAAG+E,MAAM,CAACzC,IAAI,GAAGkD,cAAc,CAAClD,IAAI;;EAE/D,IAAI,KAAK,IAAIyC,MAAM,EAAE;IACnBkB,QAAQ,CAAC/F,CAAC,GAAG8F,UAAU,CAAC9F,CAAC,GAAG6E,MAAM,CAACxC,GAAG,GAAGiD,cAAc,CAACjD,GAAG;;EAE7DlC,cAAc,CAACa,IAAI,EAAE+E,QAAQ,CAAC;AAChC;AAEA,SAASC,SAASA,CAAChF,IAAI,EAAE6D,MAAM,EAAEL,MAAM,EAAE;EACvC,IAAIA,MAAM,CAACyB,WAAW,EAAE;IACtB,IAAMC,SAAS,GAAGnD,SAAS,CAAC/B,IAAI,CAAC;IAEjC,IAAMmF,KAAK,GAAGD,SAAS,CAAC9D,IAAI,CAACgE,OAAO,CAAC,CAAC,CAAC;IACvC,IAAMC,IAAI,GAAGH,SAAS,CAAC7D,GAAG,CAAC+D,OAAO,CAAC,CAAC,CAAC;IACrC,IAAME,KAAK,GAAGzB,MAAM,CAACzC,IAAI,CAACgE,OAAO,CAAC,CAAC,CAAC;IACpC,IAAMG,IAAI,GAAG1B,MAAM,CAACxC,GAAG,CAAC+D,OAAO,CAAC,CAAC,CAAC;IAElC,IAAID,KAAK,KAAKG,KAAK,IAAID,IAAI,KAAKE,IAAI,EAAE;MACpC;;;EAIJ,IAAI/B,MAAM,CAACC,WAAW,IAAID,MAAM,CAACE,YAAY,EAAE;IAC7CE,UAAU,CAAC5D,IAAI,EAAE6D,MAAM,EAAEL,MAAM,CAAC;GACjC,MAAM,IACLA,MAAM,CAACgC,eAAe,IACtB1H,gBAAgB,EAAE,IAAIN,QAAQ,CAACsD,IAAI,CAACvD,KAAK,EACzC;IACAsH,cAAY,CAAC7E,IAAI,EAAE6D,MAAM,CAAS;GACnC,MAAM;IACLD,UAAU,CAAC5D,IAAI,EAAE6D,MAAM,EAAEL,MAAM,CAAC;;AAEpC;AAEA,SAASiC,IAAIA,CAACpG,GAAG,EAAEqG,EAAE,EAAE;EACrB,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,GAAG,CAACsG,MAAM,EAAEnF,CAAC,EAAE,EAAE;IACnCkF,EAAE,CAACrG,GAAG,CAACmB,CAAC,CAAC,CAAC;;AAEd;AAEA,SAASoF,aAAaA,CAAC5F,IAAI,EAAE;EAC3B,OAAOF,iBAAiB,CAACE,IAAI,EAAE,WAAW,CAAC,KAAK,YAAY;AAC9D;AAEA,IAAM6F,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;AAClD,IAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,IAAMC,aAAa,GAAG,CAAC;AACvB,IAAMC,YAAY,GAAG,CAAC;AACtB,IAAMC,YAAY,GAAG,CAAC;AAEtB,SAASC,IAAIA,CAAClG,IAAI,EAAEmG,OAAO,EAAEC,QAAQ,EAAE;EACrC,IAAM7B,GAAG,GAAG,EAAE;EACd,IAAMhH,KAAK,GAAGyC,IAAI,CAACzC,KAAK;EACxB,IAAIW,IAAI;;;EAGR,KAAKA,IAAI,IAAIiI,OAAO,EAAE;IACpB,IAAIA,OAAO,CAAC1F,cAAc,CAACvC,IAAI,CAAC,EAAE;MAChCqG,GAAG,CAACrG,IAAI,CAAC,GAAGX,KAAK,CAACW,IAAI,CAAC;MACvBX,KAAK,CAACW,IAAI,CAAC,GAAGiI,OAAO,CAACjI,IAAI,CAAC;;;EAI/BkI,QAAQ,CAACC,IAAI,CAACrG,IAAI,CAAC;;;EAGnB,KAAK9B,IAAI,IAAIiI,OAAO,EAAE;IACpB,IAAIA,OAAO,CAAC1F,cAAc,CAACvC,IAAI,CAAC,EAAE;MAChCX,KAAK,CAACW,IAAI,CAAC,GAAGqG,GAAG,CAACrG,IAAI,CAAC;;;AAG7B;AAEA,SAASoI,WAAWA,CAACtG,IAAI,EAAEuG,KAAK,EAAEC,KAAK,EAAE;EACvC,IAAIvI,KAAK,GAAG,CAAC;EACb,IAAIwI,IAAI;EACR,IAAIC,CAAC;EACL,IAAIlG,CAAC;EACL,KAAKkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACZ,MAAM,EAAEe,CAAC,EAAE,EAAE;IACjCD,IAAI,GAAGF,KAAK,CAACG,CAAC,CAAC;IACf,IAAID,IAAI,EAAE;MACR,KAAKjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,KAAK,CAACb,MAAM,EAAEnF,CAAC,EAAE,EAAE;QACjC,IAAImG,OAAO;QACX,IAAIF,IAAI,KAAK,QAAQ,EAAE;UACrBE,OAAO,MAAA9I,MAAA,CAAM4I,IAAI,EAAA5I,MAAA,CAAG2I,KAAK,CAAChG,CAAC,CAAC,UAAO;SACpC,MAAM;UACLmG,OAAO,GAAGF,IAAI,GAAGD,KAAK,CAAChG,CAAC,CAAC;;QAE3BvC,KAAK,IAAIc,UAAU,CAACe,iBAAiB,CAACE,IAAI,EAAE2G,OAAO,CAAC,CAAC,IAAI,CAAC;;;;EAIhE,OAAO1I,KAAK;AACd;AAEA,IAAM2I,QAAQ,GAAG;EACfC,SAAS,WAAAA,UAACC,OAAO,EAAE;IACjB,IAAIC,MAAM,GAAGD,OAAO;IACpB,GAAG;MACD,IAAIC,MAAM,CAACzE,QAAQ,KAAK,EAAE,IAAIyE,MAAM,CAACC,IAAI,EAAE;QACzCD,MAAM,GAAGA,MAAM,CAACC,IAAI;OACrB,MAAM;QACLD,MAAM,GAAGA,MAAM,CAACE,UAAU;;KAE7B,QAAQF,MAAM,IAAIA,MAAM,CAACzE,QAAQ,KAAK,CAAC,IAAIyE,MAAM,CAACzE,QAAQ,KAAK,CAAC;IACjE,OAAOyE,MAAM;;AAEjB,CAAC;AAEDtB,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,UAAAvH,IAAI,EAAI;EAChC0I,QAAQ,OAAA/I,MAAA,CAAOK,IAAI,EAAG,GAAG,UAAAgJ,MAAM,EAAI;IACjC,IAAMtF,CAAC,GAAGsF,MAAM,CAAC1J,QAAQ;IACzB,OAAO0D,IAAI,CAACiG,GAAG;;;IAGbvF,CAAC,CAACZ,eAAe,UAAAnD,MAAA,CAAUK,IAAI,EAAG;;IAElC0D,CAAC,CAACd,IAAI,UAAAjD,MAAA,CAAUK,IAAI,EAAG,EACvB0I,QAAQ,YAAA/I,MAAA,CAAYK,IAAI,EAAG,CAAC0D,CAAC,CAAC,CAC/B;GACF;EAEDgF,QAAQ,YAAA/I,MAAA,CAAYK,IAAI,EAAG,GAAG,UAAAkJ,GAAG,EAAI;;IAEnC,IAAMX,IAAI,YAAA5I,MAAA,CAAYK,IAAI,CAAE;IAC5B,IAAM0C,GAAG,GAAGwG,GAAG,CAAC5J,QAAQ;IACxB,IAAMsD,IAAI,GAAGF,GAAG,CAACE,IAAI;IACrB,IAAME,eAAe,GAAGJ,GAAG,CAACI,eAAe;IAC3C,IAAMqG,mBAAmB,GAAGrG,eAAe,CAACyF,IAAI,CAAC;;;IAGjD,OACG7F,GAAG,CAAC0G,UAAU,KAAK,YAAY,IAAID,mBAAmB,IACtDvG,IAAI,IAAIA,IAAI,CAAC2F,IAAI,CAAE,IACpBY,mBAAmB;GAEtB;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACvH,IAAI,EAAE9B,IAAI,EAAEsJ,EAAE,EAAE;EAC7B,IAAIC,KAAK,GAAGD,EAAE;EACd,IAAIrF,QAAQ,CAACnC,IAAI,CAAC,EAAE;IAClB,OAAO9B,IAAI,KAAK,OAAO,GACnB0I,QAAQ,CAACc,aAAa,CAAC1H,IAAI,CAAC,GAC5B4G,QAAQ,CAACe,cAAc,CAAC3H,IAAI,CAAC;GAClC,MAAM,IAAIA,IAAI,CAACsC,QAAQ,KAAK,CAAC,EAAE;IAC9B,OAAOpE,IAAI,KAAK,OAAO,GACnB0I,QAAQ,CAACgB,QAAQ,CAAC5H,IAAI,CAAC,GACvB4G,QAAQ,CAACiB,SAAS,CAAC7H,IAAI,CAAC;;EAE9B,IAAMwG,KAAK,GAAGtI,IAAI,KAAK,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EACtE,IAAI4J,cAAc,GAChB5J,IAAI,KAAK,OAAO,GACZgD,IAAI,CAACC,KAAK,CAACnB,IAAI,CAACiB,qBAAqB,EAAE,CAAC8G,KAAK,CAAC,GAC9C7G,IAAI,CAACC,KAAK,CAACnB,IAAI,CAACiB,qBAAqB,EAAE,CAAC+G,MAAM,CAAC;EACrD,IAAMC,WAAW,GAAGrC,aAAa,CAAC5F,IAAI,CAAC;EACvC,IAAIkI,WAAW,GAAG,CAAC;EACnB,IACEJ,cAAc,KAAK,IAAI,IACvBA,cAAc,KAAKxK,SAAS,IAC5BwK,cAAc,IAAI,CAAC,EACnB;IACAA,cAAc,GAAGxK,SAAS;;IAE1B4K,WAAW,GAAGpI,iBAAiB,CAACE,IAAI,EAAE9B,IAAI,CAAC;IAC3C,IACEgK,WAAW,KAAK,IAAI,IACpBA,WAAW,KAAK5K,SAAS,IACzB6K,MAAM,CAACD,WAAW,CAAC,GAAG,CAAC,EACvB;MACAA,WAAW,GAAGlI,IAAI,CAACzC,KAAK,CAACW,IAAI,CAAC,IAAI,CAAC;;;IAGrCgK,WAAW,GAAGhH,IAAI,CAACC,KAAK,CAACpC,UAAU,CAACmJ,WAAW,CAAC,CAAC,IAAI,CAAC;;EAExD,IAAIT,KAAK,KAAKnK,SAAS,EAAE;IACvBmK,KAAK,GAAGQ,WAAW,GAAGjC,YAAY,GAAGF,aAAa;;EAEpD,IAAMsC,2BAA2B,GAC/BN,cAAc,KAAKxK,SAAS,IAAI2K,WAAW;EAC7C,IAAMvF,GAAG,GAAGoF,cAAc,IAAII,WAAW;EACzC,IAAIT,KAAK,KAAK3B,aAAa,EAAE;IAC3B,IAAIsC,2BAA2B,EAAE;MAC/B,OAAO1F,GAAG,GAAG4D,WAAW,CAACtG,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAEwG,KAAK,CAAC;;IAE9D,OAAO0B,WAAW;GACnB,MAAM,IAAIE,2BAA2B,EAAE;IACtC,IAAIX,KAAK,KAAKzB,YAAY,EAAE;MAC1B,OAAOtD,GAAG;;IAEZ,OACEA,GAAG,IACF+E,KAAK,KAAK1B,aAAa,GACpB,CAACO,WAAW,CAACtG,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAEwG,KAAK,CAAC,GACrCF,WAAW,CAACtG,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAEwG,KAAK,CAAC,CAAC;;EAG7C,OAAO0B,WAAW,GAAG5B,WAAW,CAACtG,IAAI,EAAE6F,UAAU,CAACwC,KAAK,CAACZ,KAAK,CAAC,EAAEjB,KAAK,CAAC;AACxE;AAEA,IAAM8B,OAAO,GAAG;EACdxE,QAAQ,EAAE,UAAU;EACpByE,UAAU,EAAE,QAAQ;EACpBrI,OAAO,EAAE;AACX,CAAC;;AAED;AACA,SAASsI,kBAAkBA,CAAA,EAAU;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA/C,MAAA,EAANgD,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,KAAA,MAAAA,KAAA,GAAAJ,IAAA,EAAAI,KAAA;IAAJF,IAAI,CAAAE,KAAA,IAAAH,SAAA,CAAAG,KAAA;;EACjC,IAAInG,GAAG;EACP,IAAM1C,IAAI,GAAG2I,IAAI,CAAC,CAAC,CAAC;;;EAGpB,IAAI3I,IAAI,CAAC8I,WAAW,KAAK,CAAC,EAAE;IAC1BpG,GAAG,GAAG6E,KAAK,CAACwB,KAAK,CAACzL,SAAS,EAAEqL,IAAI,CAAC;GACnC,MAAM;IACLzC,IAAI,CAAClG,IAAI,EAAEsI,OAAO,EAAE,YAAM;MACxB5F,GAAG,GAAG6E,KAAK,CAACwB,KAAK,CAACzL,SAAS,EAAEqL,IAAI,CAAC;KACnC,CAAC;;EAEJ,OAAOjG,GAAG;AACZ;AAEA+C,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,UAAAvH,IAAI,EAAI;EAChC,IAAM8K,KAAK,GAAG9K,IAAI,CAAC+K,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGhL,IAAI,CAACmK,KAAK,CAAC,CAAC,CAAC;EAC1DzB,QAAQ,SAAA/I,MAAA,CAASmL,KAAK,EAAG,GAAG,UAAC3I,EAAE,EAAE8I,aAAa,EAAK;IACjD,OACE9I,EAAE,IACFmI,kBAAkB,CAACnI,EAAE,EAAEnC,IAAI,EAAEiL,aAAa,GAAGlD,YAAY,GAAGD,YAAY,CAAC;GAE5E;EACD,IAAMQ,KAAK,GAAGtI,IAAI,KAAK,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EAEtE0I,QAAQ,CAAC1I,IAAI,CAAC,GAAG,UAAC8B,IAAI,EAAEM,CAAC,EAAK;IAC5B,IAAIoC,GAAG,GAAGpC,CAAC;IACX,IAAIoC,GAAG,KAAKpF,SAAS,EAAE;MACrB,IAAI0C,IAAI,EAAE;QACR,IAAMiI,WAAW,GAAGrC,aAAa,CAAC5F,IAAI,CAAC;QACvC,IAAIiI,WAAW,EAAE;UACfvF,GAAG,IAAI4D,WAAW,CAACtG,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAEwG,KAAK,CAAC;;QAExD,OAAOpG,GAAG,CAACJ,IAAI,EAAE9B,IAAI,EAAEwE,GAAG,CAAC;;MAE7B,OAAOpF,SAAS;;IAElB,OAAO0C,IAAI,IAAIwI,kBAAkB,CAACxI,IAAI,EAAE9B,IAAI,EAAE4H,aAAa,CAAC;GAC7D;AACH,CAAC,CAAC;AAEF,SAASsD,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACrB,KAAK,IAAM9I,CAAC,IAAI8I,IAAI,EAAE;IACpB,IAAIA,IAAI,CAAC7I,cAAc,CAACD,CAAC,CAAC,EAAE;MAC1B6I,EAAE,CAAC7I,CAAC,CAAC,GAAG8I,IAAI,CAAC9I,CAAC,CAAC;;;EAGnB,OAAO6I,EAAE;AACX;AAEA,IAAME,KAAK,GAAG;EACZC,SAAS,WAAAA,UAACxL,IAAI,EAAE;IACd,IAAIA,IAAI,IAAIA,IAAI,CAACR,QAAQ,IAAIQ,IAAI,CAACyL,UAAU,EAAE;MAC5C,OAAOzL,IAAI;;IAEb,IAAM4C,GAAG,GAAG5C,IAAI,CAAC6C,aAAa,IAAI7C,IAAI;IACtC,OAAO4C,GAAG,CAACqB,WAAW,IAAIrB,GAAG,CAACsB,YAAY;GAC3C;EACDG,WAAW,EAAXA,WAAW;EACXwB,MAAM,WAAAA,OAACxD,EAAE,EAAEpC,KAAK,EAAEuF,MAAM,EAAE;IACxB,IAAI,OAAOvF,KAAK,KAAK,WAAW,EAAE;MAChC+G,SAAS,CAAC3E,EAAE,EAAEpC,KAAK,EAAEuF,MAAM,IAAI,EAAE,CAAC;KACnC,MAAM;MACL,OAAOzB,SAAS,CAAC1B,EAAE,CAAC;;GAEvB;EACD8B,QAAQ,EAARA,QAAQ;EACRsD,IAAI,EAAJA,IAAI;EACJrF,GAAG,EAAHA,GAAG;EACHsJ,KAAK,WAAAA,MAACtH,GAAG,EAAE;IACT,IAAI5B,CAAC;IACL,IAAMkB,GAAG,GAAG,EAAE;IACd,KAAKlB,CAAC,IAAI4B,GAAG,EAAE;MACb,IAAIA,GAAG,CAAC3B,cAAc,CAACD,CAAC,CAAC,EAAE;QACzBkB,GAAG,CAAClB,CAAC,CAAC,GAAG4B,GAAG,CAAC5B,CAAC,CAAC;;;IAGnB,IAAMmJ,QAAQ,GAAGvH,GAAG,CAACuH,QAAQ;IAC7B,IAAIA,QAAQ,EAAE;MACZ,KAAKnJ,CAAC,IAAI4B,GAAG,EAAE;QACb,IAAIA,GAAG,CAAC3B,cAAc,CAACD,CAAC,CAAC,EAAE;UACzBkB,GAAG,CAACiI,QAAQ,CAACnJ,CAAC,CAAC,GAAG4B,GAAG,CAACuH,QAAQ,CAACnJ,CAAC,CAAC;;;;IAIvC,OAAOkB,GAAG;GACX;EACD0H,GAAG,EAAHA,GAAG;EACHQ,mBAAmB,WAAAA,oBAACnI,CAAC,EAAE;IACrB,OAAOI,aAAa,CAACJ,CAAC,CAAC;GACxB;EACDoI,kBAAkB,WAAAA,mBAACpI,CAAC,EAAE;IACpB,OAAOK,YAAY,CAACL,CAAC,CAAC;GACvB;EACDqI,KAAK,WAAAA,MAAA,EAAU;IACb,IAAMpI,GAAG,GAAG,EAAE;IACd,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,SAAA,CAAK/C,MAAM,EAAEnF,CAAC,EAAE,EAAE;MACpC+I,KAAK,CAACH,GAAG,CAAC1H,GAAG,EAAOlB,CAAC,QAAAkI,SAAA,CAAA/C,MAAA,IAADnF,CAAC,GAAAlD,SAAA,GAAAoL,SAAA,CAADlI,CAAC,EAAE;;IAEzB,OAAOkB,GAAG;GACX;EACDgG,aAAa,EAAE,CAAC;EAChBC,cAAc,EAAE;AAClB,CAAC;AAEDyB,GAAG,CAACG,KAAK,EAAE3C,QAAQ,CAAC;;ACxmBpB;AACA;AACA;AACA,IAAQC,SAAS,GAAK0C,KAAK,CAAnB1C,SAAS;AAEjB,SAASkD,eAAeA,CAACjD,OAAO,EAAE;EAChC,IAAIyC,KAAK,CAACpH,QAAQ,CAAC2E,OAAO,CAAC,IAAIA,OAAO,CAACxE,QAAQ,KAAK,CAAC,EAAE;IACrD,OAAO,IAAI;;;;AAIf;AACA;AACA;AACA;AACA;AACA;;;;;;;;EAQE,IAAM1B,GAAG,GAAG2I,KAAK,CAAClH,WAAW,CAACyE,OAAO,CAAC;EACtC,IAAMhG,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,IAAIiG,MAAM;EACV,IAAIiD,aAAa,GAAGT,KAAK,CAACnJ,GAAG,CAAC0G,OAAO,EAAE,UAAU,CAAC;EAClD,IAAMmD,UAAU,GAAGD,aAAa,KAAK,OAAO,IAAIA,aAAa,KAAK,UAAU;EAE5E,IAAI,CAACC,UAAU,EAAE;IACf,OAAOnD,OAAO,CAACoD,QAAQ,CAACC,WAAW,EAAE,KAAK,MAAM,GAC5C,IAAI,GACJtD,SAAS,CAACC,OAAO,CAAC;;EAGxB,KACEC,MAAM,GAAGF,SAAS,CAACC,OAAO,CAAC,EAC3BC,MAAM,IAAIA,MAAM,KAAKjG,IAAI,IAAIiG,MAAM,CAACzE,QAAQ,KAAK,CAAC,EAClDyE,MAAM,GAAGF,SAAS,CAACE,MAAM,CAAC,EAC1B;IACAiD,aAAa,GAAGT,KAAK,CAACnJ,GAAG,CAAC2G,MAAM,EAAE,UAAU,CAAC;IAC7C,IAAIiD,aAAa,KAAK,QAAQ,EAAE;MAC9B,OAAOjD,MAAM;;;EAGjB,OAAO,IAAI;AACb;AC/CA,IAAQqD,WAAS,GAAKb,KAAK,CAAnB1C,SAAS;AAEjB,SAAwBwD,eAAeA,CAACvD,OAAO,EAAE;EAC/C,IAAIyC,KAAK,CAACpH,QAAQ,CAAC2E,OAAO,CAAC,IAAIA,OAAO,CAACxE,QAAQ,KAAK,CAAC,EAAE;IACrD,OAAO,KAAK;;EAGd,IAAM1B,GAAG,GAAG2I,KAAK,CAAClH,WAAW,CAACyE,OAAO,CAAC;EACtC,IAAMhG,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,IAAIiG,MAAM,GAAG,IAAI;EACjB,KACEA,MAAM,GAAGqD,WAAS,CAACtD,OAAO,CAAC;;EAE3BC,MAAM,IAAIA,MAAM,KAAKjG,IAAI,IAAIiG,MAAM,KAAKnG,GAAG,EAC3CmG,MAAM,GAAGqD,WAAS,CAACrD,MAAM,CAAC,EAC1B;IACA,IAAMiD,aAAa,GAAGT,KAAK,CAACnJ,GAAG,CAAC2G,MAAM,EAAE,UAAU,CAAC;IACnD,IAAIiD,aAAa,KAAK,OAAO,EAAE;MAC7B,OAAO,IAAI;;;EAGf,OAAO,KAAK;AACd;;ACpBA;AACA;AACA;AACA,SAASM,wBAAwBA,CAACxD,OAAO,EAAEyD,gBAAgB,EAAE;EAC3D,IAAMC,WAAW,GAAG;IAClBpJ,IAAI,EAAE,CAAC;IACPqJ,KAAK,EAAEC,QAAQ;IACfrJ,GAAG,EAAE,CAAC;IACNsJ,MAAM,EAAED;GACT;EACD,IAAIrK,EAAE,GAAG0J,eAAe,CAACjD,OAAO,CAAC;EACjC,IAAMlG,GAAG,GAAG2I,KAAK,CAAClH,WAAW,CAACyE,OAAO,CAAC;EACtC,IAAMM,GAAG,GAAGxG,GAAG,CAACqB,WAAW,IAAIrB,GAAG,CAACsB,YAAY;EAC/C,IAAMpB,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,IAAME,eAAe,GAAGJ,GAAG,CAACI,eAAe;;;;EAI3C,OAAOX,EAAE,EAAE;;IAET,IACE,CAACuK,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIzK,EAAE,CAAC0K,WAAW,KAAK,CAAC;;;;IAIlE1K,EAAE,KAAKS,IAAI,IACVT,EAAE,KAAKW,eAAe,IACtBuI,KAAK,CAACnJ,GAAG,CAACC,EAAE,EAAE,UAAU,CAAC,KAAK,SAAU,EAC1C;MACA,IAAM2B,GAAG,GAAGuH,KAAK,CAAC1F,MAAM,CAACxD,EAAE,CAAC;;MAE5B2B,GAAG,CAACZ,IAAI,IAAIf,EAAE,CAACiB,UAAU;MACzBU,GAAG,CAACX,GAAG,IAAIhB,EAAE,CAACkB,SAAS;MACvBiJ,WAAW,CAACnJ,GAAG,GAAGH,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACnJ,GAAG,EAAEW,GAAG,CAACX,GAAG,CAAC;MACpDmJ,WAAW,CAACC,KAAK,GAAGvJ,IAAI,CAAC8J,GAAG,CAC1BR,WAAW,CAACC,KAAK;;MAEjBzI,GAAG,CAACZ,IAAI,GAAGf,EAAE,CAAC0K,WAAW,CAC1B;MACDP,WAAW,CAACG,MAAM,GAAGzJ,IAAI,CAAC8J,GAAG,CAC3BR,WAAW,CAACG,MAAM,EAClB3I,GAAG,CAACX,GAAG,GAAGhB,EAAE,CAAC4K,YAAY,CAC1B;MACDT,WAAW,CAACpJ,IAAI,GAAGF,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACpJ,IAAI,EAAEY,GAAG,CAACZ,IAAI,CAAC;KACxD,MAAM,IAAIf,EAAE,KAAKS,IAAI,IAAIT,EAAE,KAAKW,eAAe,EAAE;MAChD;;IAEFX,EAAE,GAAG0J,eAAe,CAAC1J,EAAE,CAAC;;;;;;EAM1B,IAAI6K,gBAAgB,GAAG,IAAI;EAC3B,IAAI,CAAC3B,KAAK,CAACpH,QAAQ,CAAC2E,OAAO,CAAC,IAAIA,OAAO,CAACxE,QAAQ,KAAK,CAAC,EAAE;IACtD4I,gBAAgB,GAAGpE,OAAO,CAACvJ,KAAK,CAACuG,QAAQ;IACzC,IAAMA,QAAQ,GAAGyF,KAAK,CAACnJ,GAAG,CAAC0G,OAAO,EAAE,UAAU,CAAC;IAC/C,IAAIhD,QAAQ,KAAK,UAAU,EAAE;MAC3BgD,OAAO,CAACvJ,KAAK,CAACuG,QAAQ,GAAG,OAAO;;;EAIpC,IAAMqH,OAAO,GAAG5B,KAAK,CAACK,mBAAmB,CAACxC,GAAG,CAAC;EAC9C,IAAMgE,OAAO,GAAG7B,KAAK,CAACM,kBAAkB,CAACzC,GAAG,CAAC;EAC7C,IAAMM,aAAa,GAAG6B,KAAK,CAAC7B,aAAa,CAACN,GAAG,CAAC;EAC9C,IAAMO,cAAc,GAAG4B,KAAK,CAAC5B,cAAc,CAACP,GAAG,CAAC;EAChD,IAAIiE,aAAa,GAAGrK,eAAe,CAACsK,WAAW;EAC/C,IAAIC,cAAc,GAAGvK,eAAe,CAACwK,YAAY;;;;EAIjD,IAAMC,SAAS,GAAGjN,MAAM,CAACC,gBAAgB,CAACqC,IAAI,CAAC;EAC/C,IAAI2K,SAAS,CAACC,SAAS,KAAK,QAAQ,EAAE;IACpCL,aAAa,GAAGjE,GAAG,CAACuE,UAAU;;EAEhC,IAAIF,SAAS,CAACG,SAAS,KAAK,QAAQ,EAAE;IACpCL,cAAc,GAAGnE,GAAG,CAACyE,WAAW;;;;EAIlC,IAAI/E,OAAO,CAACvJ,KAAK,EAAE;IACjBuJ,OAAO,CAACvJ,KAAK,CAACuG,QAAQ,GAAGoH,gBAAgB;;EAG3C,IAAIX,gBAAgB,IAAIF,eAAe,CAACvD,OAAO,CAAC,EAAE;;IAEhD0D,WAAW,CAACpJ,IAAI,GAAGF,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACpJ,IAAI,EAAE+J,OAAO,CAAC;IACtDX,WAAW,CAACnJ,GAAG,GAAGH,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACnJ,GAAG,EAAE+J,OAAO,CAAC;IACpDZ,WAAW,CAACC,KAAK,GAAGvJ,IAAI,CAAC8J,GAAG,CAACR,WAAW,CAACC,KAAK,EAAEU,OAAO,GAAGzD,aAAa,CAAC;IACxE8C,WAAW,CAACG,MAAM,GAAGzJ,IAAI,CAAC8J,GAAG,CAACR,WAAW,CAACG,MAAM,EAAES,OAAO,GAAGzD,cAAc,CAAC;GAC5E,MAAM;;IAEL,IAAMmE,eAAe,GAAG5K,IAAI,CAACiG,GAAG,CAACkE,aAAa,EAAEF,OAAO,GAAGzD,aAAa,CAAC;IACxE8C,WAAW,CAACC,KAAK,GAAGvJ,IAAI,CAAC8J,GAAG,CAACR,WAAW,CAACC,KAAK,EAAEqB,eAAe,CAAC;IAEhE,IAAMC,gBAAgB,GAAG7K,IAAI,CAACiG,GAAG,CAACoE,cAAc,EAAEH,OAAO,GAAGzD,cAAc,CAAC;IAC3E6C,WAAW,CAACG,MAAM,GAAGzJ,IAAI,CAAC8J,GAAG,CAACR,WAAW,CAACG,MAAM,EAAEoB,gBAAgB,CAAC;;EAGrE,OAAOvB,WAAW,CAACnJ,GAAG,IAAI,CAAC,IACzBmJ,WAAW,CAACpJ,IAAI,IAAI,CAAC,IACrBoJ,WAAW,CAACG,MAAM,GAAGH,WAAW,CAACnJ,GAAG,IACpCmJ,WAAW,CAACC,KAAK,GAAGD,WAAW,CAACpJ,IAAI,GAClCoJ,WAAW,GACX,IAAI;AACV;AC3GA,SAASwB,iBAAiBA,CAACC,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,EAAEb,QAAQ,EAAE;EACvE,IAAM3H,GAAG,GAAGuH,KAAK,CAACG,KAAK,CAACuC,WAAW,CAAC;EACpC,IAAME,IAAI,GAAG;IACXpE,KAAK,EAAEmE,QAAQ,CAACnE,KAAK;IACrBC,MAAM,EAAEkE,QAAQ,CAAClE;GAClB;EAED,IAAI2B,QAAQ,CAACyC,OAAO,IAAIpK,GAAG,CAACZ,IAAI,GAAGoJ,WAAW,CAACpJ,IAAI,EAAE;IACnDY,GAAG,CAACZ,IAAI,GAAGoJ,WAAW,CAACpJ,IAAI;;;;EAI7B,IACEuI,QAAQ,CAAC0C,WAAW,IACpBrK,GAAG,CAACZ,IAAI,IAAIoJ,WAAW,CAACpJ,IAAI,IAC5BY,GAAG,CAACZ,IAAI,GAAG+K,IAAI,CAACpE,KAAK,GAAGyC,WAAW,CAACC,KAAK,EACzC;IACA0B,IAAI,CAACpE,KAAK,IAAI/F,GAAG,CAACZ,IAAI,GAAG+K,IAAI,CAACpE,KAAK,GAAGyC,WAAW,CAACC,KAAK;;;;EAIzD,IAAId,QAAQ,CAACyC,OAAO,IAAIpK,GAAG,CAACZ,IAAI,GAAG+K,IAAI,CAACpE,KAAK,GAAGyC,WAAW,CAACC,KAAK,EAAE;;IAEjEzI,GAAG,CAACZ,IAAI,GAAGF,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACC,KAAK,GAAG0B,IAAI,CAACpE,KAAK,EAAEyC,WAAW,CAACpJ,IAAI,CAAC;;;;EAIvE,IAAIuI,QAAQ,CAAC2C,OAAO,IAAItK,GAAG,CAACX,GAAG,GAAGmJ,WAAW,CAACnJ,GAAG,EAAE;IACjDW,GAAG,CAACX,GAAG,GAAGmJ,WAAW,CAACnJ,GAAG;;;;EAI3B,IACEsI,QAAQ,CAAC4C,YAAY,IACrBvK,GAAG,CAACX,GAAG,IAAImJ,WAAW,CAACnJ,GAAG,IAC1BW,GAAG,CAACX,GAAG,GAAG8K,IAAI,CAACnE,MAAM,GAAGwC,WAAW,CAACG,MAAM,EAC1C;IACAwB,IAAI,CAACnE,MAAM,IAAIhG,GAAG,CAACX,GAAG,GAAG8K,IAAI,CAACnE,MAAM,GAAGwC,WAAW,CAACG,MAAM;;;;EAI3D,IAAIhB,QAAQ,CAAC2C,OAAO,IAAItK,GAAG,CAACX,GAAG,GAAG8K,IAAI,CAACnE,MAAM,GAAGwC,WAAW,CAACG,MAAM,EAAE;;IAElE3I,GAAG,CAACX,GAAG,GAAGH,IAAI,CAACiG,GAAG,CAACqD,WAAW,CAACG,MAAM,GAAGwB,IAAI,CAACnE,MAAM,EAAEwC,WAAW,CAACnJ,GAAG,CAAC;;EAGvE,OAAOkI,KAAK,CAACH,GAAG,CAACpH,GAAG,EAAEmK,IAAI,CAAC;AAC7B;AC/CA,SAASK,SAASA,CAACxO,IAAI,EAAE;EACvB,IAAI6F,MAAM;EACV,IAAIpC,CAAC;EACL,IAAIgL,CAAC;EACL,IAAI,CAAClD,KAAK,CAACpH,QAAQ,CAACnE,IAAI,CAAC,IAAIA,IAAI,CAACsE,QAAQ,KAAK,CAAC,EAAE;IAChDuB,MAAM,GAAG0F,KAAK,CAAC1F,MAAM,CAAC7F,IAAI,CAAC;IAC3ByD,CAAC,GAAG8H,KAAK,CAACmD,UAAU,CAAC1O,IAAI,CAAC;IAC1ByO,CAAC,GAAGlD,KAAK,CAACoD,WAAW,CAAC3O,IAAI,CAAC;GAC5B,MAAM;IACL,IAAMoJ,GAAG,GAAGmC,KAAK,CAACC,SAAS,CAACxL,IAAI,CAAC;IACjC6F,MAAM,GAAG;MACPzC,IAAI,EAAEmI,KAAK,CAACK,mBAAmB,CAACxC,GAAG,CAAC;MACpC/F,GAAG,EAAEkI,KAAK,CAACM,kBAAkB,CAACzC,GAAG;KAClC;IACD3F,CAAC,GAAG8H,KAAK,CAAC7B,aAAa,CAACN,GAAG,CAAC;IAC5BqF,CAAC,GAAGlD,KAAK,CAAC5B,cAAc,CAACP,GAAG,CAAC;;EAE/BvD,MAAM,CAACkE,KAAK,GAAGtG,CAAC;EAChBoC,MAAM,CAACmE,MAAM,GAAGyE,CAAC;EACjB,OAAO5I,MAAM;AACf;;ACtBA;AACA;AACA;;AAEA,SAAS+I,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAMC,CAAC,GAAGD,KAAK,CAAC7D,MAAM,CAAC,CAAC,CAAC;EACzB,IAAM+D,CAAC,GAAGF,KAAK,CAAC7D,MAAM,CAAC,CAAC,CAAC;EACzB,IAAMxH,CAAC,GAAGoL,MAAM,CAAC9E,KAAK;EACtB,IAAM0E,CAAC,GAAGI,MAAM,CAAC7E,MAAM;EAEvB,IAAIlJ,CAAC,GAAG+N,MAAM,CAACzL,IAAI;EACnB,IAAIpC,CAAC,GAAG6N,MAAM,CAACxL,GAAG;EAElB,IAAI0L,CAAC,KAAK,GAAG,EAAE;IACb/N,CAAC,IAAIyN,CAAC,GAAG,CAAC;GACX,MAAM,IAAIM,CAAC,KAAK,GAAG,EAAE;IACpB/N,CAAC,IAAIyN,CAAC;;EAGR,IAAIO,CAAC,KAAK,GAAG,EAAE;IACblO,CAAC,IAAI2C,CAAC,GAAG,CAAC;GACX,MAAM,IAAIuL,CAAC,KAAK,GAAG,EAAE;IACpBlO,CAAC,IAAI2C,CAAC;;EAGR,OAAO;IACLL,IAAI,EAAEtC,CAAC;IACPuC,GAAG,EAAErC;GACN;AACH;AC3BA,SAASiO,cAAcA,CAACf,QAAQ,EAAEgB,aAAa,EAAEC,MAAM,EAAEtJ,MAAM,EAAEuJ,YAAY,EAAE;EAC7E,IAAMC,EAAE,GAAGT,cAAc,CAACM,aAAa,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;EACnD,IAAMG,EAAE,GAAGV,cAAc,CAACV,QAAQ,EAAEiB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAMI,IAAI,GAAG,CAACD,EAAE,CAAClM,IAAI,GAAGiM,EAAE,CAACjM,IAAI,EAAEkM,EAAE,CAACjM,GAAG,GAAGgM,EAAE,CAAChM,GAAG,CAAC;EAEjD,OAAO;IACLD,IAAI,EAAEF,IAAI,CAACsM,KAAK,CAACtB,QAAQ,CAAC9K,IAAI,GAAGmM,IAAI,CAAC,CAAC,CAAC,GAAG1J,MAAM,CAAC,CAAC,CAAC,GAAGuJ,YAAY,CAAC,CAAC,CAAC,CAAC;IACvE/L,GAAG,EAAEH,IAAI,CAACsM,KAAK,CAACtB,QAAQ,CAAC7K,GAAG,GAAGkM,IAAI,CAAC,CAAC,CAAC,GAAG1J,MAAM,CAAC,CAAC,CAAC,GAAGuJ,YAAY,CAAC,CAAC,CAAC;GACrE;AACH;;ACXA;AACA;AACA;AACA;;AAQA;;AAEA,SAASK,OAAOA,CAACxB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,EAAE;EACnD,OACEyB,WAAW,CAAC7K,IAAI,GAAGoJ,WAAW,CAACpJ,IAAI,IACnC6K,WAAW,CAAC7K,IAAI,GAAG8K,QAAQ,CAACnE,KAAK,GAAGyC,WAAW,CAACC,KAAK;AAEzD;AAEA,SAASiD,OAAOA,CAACzB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,EAAE;EACnD,OACEyB,WAAW,CAAC5K,GAAG,GAAGmJ,WAAW,CAACnJ,GAAG,IACjC4K,WAAW,CAAC5K,GAAG,GAAG6K,QAAQ,CAAClE,MAAM,GAAGwC,WAAW,CAACG,MAAM;AAE1D;AAEA,SAASgD,eAAeA,CAAC1B,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,EAAE;EAC3D,OACEyB,WAAW,CAAC7K,IAAI,GAAGoJ,WAAW,CAACC,KAAK,IACpCwB,WAAW,CAAC7K,IAAI,GAAG8K,QAAQ,CAACnE,KAAK,GAAGyC,WAAW,CAACpJ,IAAI;AAExD;AAEA,SAASwM,eAAeA,CAAC3B,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,EAAE;EAC3D,OACEyB,WAAW,CAAC5K,GAAG,GAAGmJ,WAAW,CAACG,MAAM,IACpCsB,WAAW,CAAC5K,GAAG,GAAG6K,QAAQ,CAAClE,MAAM,GAAGwC,WAAW,CAACnJ,GAAG;AAEvD;AAEA,SAASwM,IAAIA,CAACV,MAAM,EAAEW,GAAG,EAAEtO,GAAG,EAAE;EAC9B,IAAMkC,GAAG,GAAG,EAAE;EACd6H,KAAK,CAAC9D,IAAI,CAAC0H,MAAM,EAAE,UAAAY,CAAC,EAAI;IACtBrM,GAAG,CAACsM,IAAI,CACND,CAAC,CAACnP,OAAO,CAACkP,GAAG,EAAE,UAAAG,CAAC,EAAI;MAClB,OAAOzO,GAAG,CAACyO,CAAC,CAAC;KACd,CAAC,CACH;GACF,CAAC;EACF,OAAOvM,GAAG;AACZ;AAEA,SAASwM,UAAUA,CAACrK,MAAM,EAAEsK,KAAK,EAAE;EACjCtK,MAAM,CAACsK,KAAK,CAAC,GAAG,CAACtK,MAAM,CAACsK,KAAK,CAAC;EAC9B,OAAOtK,MAAM;AACf;AAEA,SAASuK,aAAaA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACrC,IAAIC,CAAC;EACL,IAAI,IAAI,CAACpL,IAAI,CAACkL,GAAG,CAAC,EAAE;IAClBE,CAAC,GAAIC,QAAQ,CAACH,GAAG,CAACI,SAAS,CAAC,CAAC,EAAEJ,GAAG,CAAC1I,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAI2I,SAAS;GACvE,MAAM;IACLC,CAAC,GAAGC,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC;;EAEvB,OAAOE,CAAC,IAAI,CAAC;AACf;AAEA,SAASG,eAAeA,CAAC7K,MAAM,EAAExD,EAAE,EAAE;EACnCwD,MAAM,CAAC,CAAC,CAAC,GAAGuK,aAAa,CAACvK,MAAM,CAAC,CAAC,CAAC,EAAExD,EAAE,CAAC0H,KAAK,CAAC;EAC9ClE,MAAM,CAAC,CAAC,CAAC,GAAGuK,aAAa,CAACvK,MAAM,CAAC,CAAC,CAAC,EAAExD,EAAE,CAAC2H,MAAM,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS2G,OAAOA,CAACtO,EAAE,EAAEuO,SAAS,EAAE9B,KAAK,EAAE+B,kBAAkB,EAAE;EACzD,IAAI1B,MAAM,GAAGL,KAAK,CAACK,MAAM;EACzB,IAAItJ,MAAM,GAAGiJ,KAAK,CAACjJ,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACnC,IAAIuJ,YAAY,GAAGN,KAAK,CAACM,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,IAAIzD,QAAQ,GAAGmD,KAAK,CAACnD,QAAQ;EAC7B,IAAM9J,MAAM,GAAGiN,KAAK,CAACjN,MAAM,IAAIQ,EAAE;EACjCwD,MAAM,GAAG,EAAE,CAAChG,MAAM,CAACgG,MAAM,CAAC;EAC1BuJ,YAAY,GAAG,EAAE,CAACvP,MAAM,CAACuP,YAAY,CAAC;EACtCzD,QAAQ,GAAGA,QAAQ,IAAI,EAAE;EACzB,IAAMmF,cAAc,GAAG,EAAE;EACzB,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAMxE,gBAAgB,GAAG,CAAC,EAAEZ,QAAQ,IAAIA,QAAQ,CAACY,gBAAgB,CAAC;;EAElE,IAAMC,WAAW,GAAGF,wBAAwB,CAACzK,MAAM,EAAE0K,gBAAgB,CAAC;;EAEtE,IAAM2B,QAAQ,GAAGM,SAAS,CAAC3M,MAAM,CAAC;;EAElC6O,eAAe,CAAC7K,MAAM,EAAEqI,QAAQ,CAAC;EACjCwC,eAAe,CAACtB,YAAY,EAAEwB,SAAS,CAAC;;EAExC,IAAI3C,WAAW,GAAGgB,cAAc,CAC9Bf,QAAQ,EACR0C,SAAS,EACTzB,MAAM,EACNtJ,MAAM,EACNuJ,YAAY,CACb;;EAED,IAAI4B,WAAW,GAAGzF,KAAK,CAACO,KAAK,CAACoC,QAAQ,EAAED,WAAW,CAAC;;;EAGpD,IACEzB,WAAW,KACVb,QAAQ,CAACyC,OAAO,IAAIzC,QAAQ,CAAC2C,OAAO,CAAC,IACtCuC,kBAAkB,EAClB;IACA,IAAIlF,QAAQ,CAACyC,OAAO,EAAE;;MAEpB,IAAIqB,OAAO,CAACxB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,CAAC,EAAE;;QAE/C,IAAMyE,SAAS,GAAGpB,IAAI,CAACV,MAAM,EAAE,QAAQ,EAAE;UACvC+B,CAAC,EAAE,GAAG;UACNC,CAAC,EAAE;SACJ,CAAC;;QAEF,IAAMC,SAAS,GAAGlB,UAAU,CAACrK,MAAM,EAAE,CAAC,CAAC;QACvC,IAAMwL,eAAe,GAAGnB,UAAU,CAACd,YAAY,EAAE,CAAC,CAAC;QACnD,IAAMkC,cAAc,GAAGrC,cAAc,CACnCf,QAAQ,EACR0C,SAAS,EACTK,SAAS,EACTG,SAAS,EACTC,eAAe,CAChB;QAED,IAAI,CAAC1B,eAAe,CAAC2B,cAAc,EAAEpD,QAAQ,EAAE1B,WAAW,CAAC,EAAE;UAC3DuE,IAAI,GAAG,CAAC;UACR5B,MAAM,GAAG8B,SAAS;UAClBpL,MAAM,GAAGuL,SAAS;UAClBhC,YAAY,GAAGiC,eAAe;;;;IAKpC,IAAI1F,QAAQ,CAAC2C,OAAO,EAAE;;MAEpB,IAAIoB,OAAO,CAACzB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,CAAC,EAAE;;QAE/C,IAAM+E,UAAS,GAAG1B,IAAI,CAACV,MAAM,EAAE,QAAQ,EAAE;UACvCqC,CAAC,EAAE,GAAG;UACNC,CAAC,EAAE;SACJ,CAAC;;QAEF,IAAMC,UAAS,GAAGxB,UAAU,CAACrK,MAAM,EAAE,CAAC,CAAC;QACvC,IAAM8L,gBAAe,GAAGzB,UAAU,CAACd,YAAY,EAAE,CAAC,CAAC;QACnD,IAAMwC,eAAc,GAAG3C,cAAc,CACnCf,QAAQ,EACR0C,SAAS,EACTW,UAAS,EACTG,UAAS,EACTC,gBAAe,CAChB;QAED,IAAI,CAAC/B,eAAe,CAACgC,eAAc,EAAE1D,QAAQ,EAAE1B,WAAW,CAAC,EAAE;UAC3DuE,IAAI,GAAG,CAAC;UACR5B,MAAM,GAAGoC,UAAS;UAClB1L,MAAM,GAAG6L,UAAS;UAClBtC,YAAY,GAAGuC,gBAAe;;;;;;IAMpC,IAAIZ,IAAI,EAAE;MACR9C,WAAW,GAAGgB,cAAc,CAC1Bf,QAAQ,EACR0C,SAAS,EACTzB,MAAM,EACNtJ,MAAM,EACNuJ,YAAY,CACb;MACD7D,KAAK,CAACH,GAAG,CAAC4F,WAAW,EAAE/C,WAAW,CAAC;;IAErC,IAAM4D,YAAY,GAAGpC,OAAO,CAACxB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,CAAC;IAChE,IAAMsF,YAAY,GAAGpC,OAAO,CAACzB,WAAW,EAAEC,QAAQ,EAAE1B,WAAW,CAAC;;;IAGhE,IAAIqF,YAAY,IAAIC,YAAY,EAAE;MAChC,IAAIC,WAAS,GAAG5C,MAAM;;;MAGtB,IAAI0C,YAAY,EAAE;QAChBE,WAAS,GAAGlC,IAAI,CAACV,MAAM,EAAE,QAAQ,EAAE;UACjC+B,CAAC,EAAE,GAAG;UACNC,CAAC,EAAE;SACJ,CAAC;;MAEJ,IAAIW,YAAY,EAAE;QAChBC,WAAS,GAAGlC,IAAI,CAACV,MAAM,EAAE,QAAQ,EAAE;UACjCqC,CAAC,EAAE,GAAG;UACNC,CAAC,EAAE;SACJ,CAAC;;MAGJtC,MAAM,GAAG4C,WAAS;MAElBlM,MAAM,GAAGiJ,KAAK,CAACjJ,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/BuJ,YAAY,GAAGN,KAAK,CAACM,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;;IAG7C0B,cAAc,CAAC1C,OAAO,GAAGzC,QAAQ,CAACyC,OAAO,IAAIyD,YAAY;IACzDf,cAAc,CAACxC,OAAO,GAAG3C,QAAQ,CAAC2C,OAAO,IAAIwD,YAAY;;;IAGzD,IAAIhB,cAAc,CAAC1C,OAAO,IAAI0C,cAAc,CAACxC,OAAO,EAAE;MACpD0C,WAAW,GAAGhD,iBAAiB,CAC7BC,WAAW,EACXC,QAAQ,EACR1B,WAAW,EACXsE,cAAc,CACf;;;;;EAKL,IAAIE,WAAW,CAACjH,KAAK,KAAKmE,QAAQ,CAACnE,KAAK,EAAE;IACxCwB,KAAK,CAACnJ,GAAG,CACPP,MAAM,EACN,OAAO,EACP0J,KAAK,CAACxB,KAAK,CAAClI,MAAM,CAAC,GAAGmP,WAAW,CAACjH,KAAK,GAAGmE,QAAQ,CAACnE,KAAK,CACzD;;EAGH,IAAIiH,WAAW,CAAChH,MAAM,KAAKkE,QAAQ,CAAClE,MAAM,EAAE;IAC1CuB,KAAK,CAACnJ,GAAG,CACPP,MAAM,EACN,QAAQ,EACR0J,KAAK,CAACvB,MAAM,CAACnI,MAAM,CAAC,GAAGmP,WAAW,CAAChH,MAAM,GAAGkE,QAAQ,CAAClE,MAAM,CAC5D;;;;;;EAMHuB,KAAK,CAAC1F,MAAM,CACVhE,MAAM,EACN;IACEuB,IAAI,EAAE4N,WAAW,CAAC5N,IAAI;IACtBC,GAAG,EAAE2N,WAAW,CAAC3N;GAClB,EACD;IACEoC,WAAW,EAAEqJ,KAAK,CAACrJ,WAAW;IAC9BC,YAAY,EAAEoJ,KAAK,CAACpJ,YAAY;IAChC8B,eAAe,EAAEsH,KAAK,CAACtH,eAAe;IACtCP,WAAW,EAAE6H,KAAK,CAAC7H;GACpB,CACF;EAED,OAAO;IACLkI,MAAM,EAANA,MAAM;IACNtJ,MAAM,EAANA,MAAM;IACNuJ,YAAY,EAAZA,YAAY;IACZzD,QAAQ,EAAEmF;GACX;AACH;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3QA,SAASkB,kBAAkBA,CAACC,MAAM,EAAE1F,gBAAgB,EAAE;EACpD,IAAMC,WAAW,GAAGF,wBAAwB,CAAC2F,MAAM,EAAE1F,gBAAgB,CAAC;EACtE,IAAM2F,YAAY,GAAG1D,SAAS,CAACyD,MAAM,CAAC;EAEtC,OACE,CAACzF,WAAW,IACZ0F,YAAY,CAAC9O,IAAI,GAAG8O,YAAY,CAACnI,KAAK,IAAIyC,WAAW,CAACpJ,IAAI,IAC1D8O,YAAY,CAAC7O,GAAG,GAAG6O,YAAY,CAAClI,MAAM,IAAIwC,WAAW,CAACnJ,GAAG,IACzD6O,YAAY,CAAC9O,IAAI,IAAIoJ,WAAW,CAACC,KAAK,IACtCyF,YAAY,CAAC7O,GAAG,IAAImJ,WAAW,CAACG,MAAM;AAE1C;AAEA,SAASwF,YAAYA,CAAC9P,EAAE,EAAE+P,OAAO,EAAEtD,KAAK,EAAE;EACxC,IAAMmD,MAAM,GAAGnD,KAAK,CAACmD,MAAM,IAAIG,OAAO;EACtC,IAAMlD,aAAa,GAAGV,SAAS,CAACyD,MAAM,CAAC;EAEvC,IAAMI,uBAAuB,GAAG,CAACL,kBAAkB,CACjDC,MAAM,EACNnD,KAAK,CAACnD,QAAQ,IAAImD,KAAK,CAACnD,QAAQ,CAACY,gBAAgB,CAClD;EAED,OAAOoE,OAAO,CAACtO,EAAE,EAAE6M,aAAa,EAAEJ,KAAK,EAAEuD,uBAAuB,CAAC;AACnE;AAEAF,YAAY,CAACG,iBAAiB,GAAGvG,eAAe;AAEhDoG,YAAY,CAACI,0BAA0B,GAAGjG,wBAAwB;;AC7BlE;AACA;AACA;AACA;;AAEA,SAASkG,UAAUA,CAACnQ,EAAE,EAAEoQ,QAAQ,EAAE3D,KAAK,EAAE;EACvC,IAAI4D,KAAK;EACT,IAAIC,KAAK;EAET,IAAM/P,GAAG,GAAG2I,KAAK,CAAClH,WAAW,CAAChC,EAAE,CAAC;EACjC,IAAM+G,GAAG,GAAGxG,GAAG,CAACqB,WAAW,IAAIrB,GAAG,CAACsB,YAAY;EAE/C,IAAMiJ,OAAO,GAAG5B,KAAK,CAACK,mBAAmB,CAACxC,GAAG,CAAC;EAC9C,IAAMgE,OAAO,GAAG7B,KAAK,CAACM,kBAAkB,CAACzC,GAAG,CAAC;EAC7C,IAAMM,aAAa,GAAG6B,KAAK,CAAC7B,aAAa,CAACN,GAAG,CAAC;EAC9C,IAAMO,cAAc,GAAG4B,KAAK,CAAC5B,cAAc,CAACP,GAAG,CAAC;EAEhD,IAAI,OAAO,IAAIqJ,QAAQ,EAAE;IACvBC,KAAK,GAAGD,QAAQ,CAACC,KAAK;GACvB,MAAM;IACLA,KAAK,GAAGvF,OAAO,GAAGsF,QAAQ,CAACG,OAAO;;EAGpC,IAAI,OAAO,IAAIH,QAAQ,EAAE;IACvBE,KAAK,GAAGF,QAAQ,CAACE,KAAK;GACvB,MAAM;IACLA,KAAK,GAAGvF,OAAO,GAAGqF,QAAQ,CAACI,OAAO;;EAGpC,IAAMjC,SAAS,GAAG;IAChBxN,IAAI,EAAEsP,KAAK;IACXrP,GAAG,EAAEsP,KAAK;IACV5I,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;GACT;EAED,IAAM8I,WAAW,GACfJ,KAAK,IAAI,CAAC,IACVA,KAAK,IAAIvF,OAAO,GAAGzD,aAAa,IAC/BiJ,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIvF,OAAO,GAAGzD,cAAe;;;EAGnD,IAAMwF,MAAM,GAAG,CAACL,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAEtC,OAAOwB,OAAO,CAACtO,EAAE,EAAEuO,SAAS,EAAAmC,cAAA,CAAAA,cAAA,KAAOjE,KAAK;IAAEK,MAAM,EAANA;MAAU2D,WAAW,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}