{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, BarChartOutlined, ExperimentOutlined, AimOutlined, DatabaseOutlined, FileTextOutlined, SearchOutlined, UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst MainLayout = ({\n  children\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n  // 处理侧边栏拖拽调整宽度\n  const handleMouseDown = e => {\n    e.preventDefault();\n    setIsResizing(true);\n    const startX = e.clientX;\n    const startWidth = siderWidth;\n    const handleMouseMove = e => {\n      const newWidth = startWidth + (e.clientX - startX);\n      if (newWidth >= 150 && newWidth <= 400) {\n        setSiderWidth(newWidth);\n      }\n    };\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n  const menuItems = [{\n    key: '/data-cleaning',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this),\n    label: '流量分析'\n  }, {\n    key: '/model-training',\n    icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this),\n    label: '模型训练'\n  }, {\n    key: '/model-prediction',\n    icon: /*#__PURE__*/_jsxDEV(AimOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    label: '模型预测'\n  }, {\n    key: '/model-registry',\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this),\n    label: '模型仓库'\n  }, {\n    key: '/clean-template',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this),\n    label: '清洗模板'\n  }, {\n    key: '/data-query',\n    icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    label: '数据查询'\n  }, {\n    key: '/user-management',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 13\n    }, this),\n    label: '用户管理'\n  }];\n  const handleMenuClick = key => {\n    navigate(key);\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this),\n    label: '个人信息'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this),\n    label: '设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    className: isResizing ? 'resizing' : '',\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: sidebarCollapsed,\n      width: sidebarCollapsed ? 80 : siderWidth,\n      style: {\n        background: '#fff',\n        boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n        position: 'fixed',\n        left: 0,\n        top: 0,\n        bottom: 0,\n        zIndex: 1000,\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px 8px',\n          textAlign: 'center',\n          borderBottom: '1px solid #f0f0f0',\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: !sidebarCollapsed ? /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          className: \"system-title\",\n          style: {\n            margin: 0,\n            color: '#1890ff',\n            fontSize: '16px',\n            whiteSpace: 'nowrap',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            lineHeight: '1.2'\n          },\n          children: \"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          className: \"system-title\",\n          style: {\n            margin: 0,\n            color: '#1890ff',\n            fontSize: '18px',\n            lineHeight: '1.2'\n          },\n          children: \"AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: ({\n          key\n        }) => handleMenuClick(key),\n        style: {\n          border: 'none'\n        },\n        className: sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), !sidebarCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          right: 0,\n          top: 0,\n          bottom: 0,\n          width: '4px',\n          cursor: 'col-resize',\n          backgroundColor: isResizing ? '#1890ff' : 'transparent',\n          transition: 'background-color 0.2s'\n        },\n        onMouseDown: handleMouseDown,\n        onMouseEnter: e => {\n          e.currentTarget.style.backgroundColor = '#1890ff';\n        },\n        onMouseLeave: e => {\n          if (!isResizing) {\n            e.currentTarget.style.backgroundColor = 'transparent';\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        marginLeft: sidebarCollapsed ? 80 : siderWidth,\n        transition: 'margin-left 0.2s'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: sidebarCollapsed ? 80 : siderWidth,\n          zIndex: 999,\n          transition: 'left 0.2s'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: sidebarCollapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 63\n          }, this),\n          onClick: () => dispatch(toggleSidebar()),\n          style: {\n            fontSize: '16px',\n            width: 64,\n            height: 64\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user === null || user === void 0 ? void 0 : user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '88px 16px 24px 16px',\n          // 顶部留出Header的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"mmzqcQVSMbOYssWaIw2AtJ2TNCY=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useEffect", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "Dropdown", "Space", "Typography", "MenuFoldOutlined", "MenuUnfoldOutlined", "BarChartOutlined", "ExperimentOutlined", "AimOutlined", "DatabaseOutlined", "FileTextOutlined", "SearchOutlined", "UserOutlined", "LogoutOutlined", "SettingOutlined", "useNavigate", "useLocation", "useSelector", "useDispatch", "logout", "restoreAuth", "toggleSidebar", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "MainLayout", "children", "_s", "navigate", "location", "dispatch", "user", "state", "auth", "sidebarCollapsed", "ui", "handleMouseDown", "e", "preventDefault", "setIsResizing", "startX", "clientX", "startWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleMouseMove", "newWidth", "set<PERSON>iderWidth", "handleMouseUp", "document", "removeEventListener", "addEventListener", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "style", "minHeight", "className", "isResizing", "trigger", "collapsible", "collapsed", "width", "background", "boxShadow", "position", "left", "top", "bottom", "zIndex", "overflow", "padding", "textAlign", "borderBottom", "height", "display", "alignItems", "justifyContent", "level", "margin", "color", "fontSize", "whiteSpace", "textOverflow", "lineHeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "border", "right", "cursor", "backgroundColor", "transition", "onMouseDown", "onMouseEnter", "currentTarget", "onMouseLeave", "marginLeft", "menu", "placement", "arrow", "username", "borderRadius", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  Bar<PERSON>hartOutlined,\n  ExperimentOutlined,\n  AimOutlined,\n  DatabaseOutlined,\n  FileTextOutlined,\n  SearchOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const { user } = useSelector((state: RootState) => state.auth);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n\n\n\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n  // 处理侧边栏拖拽调整宽度\n  const handleMouseDown = (e: React.MouseEvent) => {\n    e.preventDefault();\n    setIsResizing(true);\n\n    const startX = e.clientX;\n    const startWidth = siderWidth;\n\n    const handleMouseMove = (e: MouseEvent) => {\n      const newWidth = startWidth + (e.clientX - startX);\n      if (newWidth >= 150 && newWidth <= 400) {\n        setSiderWidth(newWidth);\n      }\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  const menuItems = [\n    {\n      key: '/data-cleaning',\n      icon: <BarChartOutlined />,\n      label: '流量分析',\n    },\n    {\n      key: '/model-training',\n      icon: <ExperimentOutlined />,\n      label: '模型训练',\n    },\n    {\n      key: '/model-prediction',\n      icon: <AimOutlined />,\n      label: '模型预测',\n    },\n    {\n      key: '/model-registry',\n      icon: <DatabaseOutlined />,\n      label: '模型仓库',\n    },\n    {\n      key: '/clean-template',\n      icon: <FileTextOutlined />,\n      label: '清洗模板',\n    },\n    {\n      key: '/data-query',\n      icon: <SearchOutlined />,\n      label: '数据查询',\n    },\n    {\n      key: '/user-management',\n      icon: <UserOutlined />,\n      label: '用户管理',\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }} className={isResizing ? 'resizing' : ''}>\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={sidebarCollapsed ? 80 : siderWidth}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          left: 0,\n          top: 0,\n          bottom: 0,\n          zIndex: 1000,\n          overflow: 'hidden',\n        }}\n      >\n        <div style={{\n          padding: '16px 8px',\n          textAlign: 'center',\n          borderBottom: '1px solid #f0f0f0',\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n        }}>\n          {!sidebarCollapsed ? (\n            <Title level={4} className=\"system-title\" style={{\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '16px',\n              whiteSpace: 'nowrap',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              lineHeight: '1.2',\n            }}>\n              AI智能清洗策略系统\n            </Title>\n          ) : (\n            <Title level={4} className=\"system-title\" style={{\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '18px',\n              lineHeight: '1.2',\n            }}>\n              AI\n            </Title>\n          )}\n        </div>\n\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          style={{\n            border: 'none',\n          }}\n          className={sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'}\n        />\n\n        {/* 拖拽调整宽度的手柄 */}\n        {!sidebarCollapsed && (\n          <div\n            style={{\n              position: 'absolute',\n              right: 0,\n              top: 0,\n              bottom: 0,\n              width: '4px',\n              cursor: 'col-resize',\n              backgroundColor: isResizing ? '#1890ff' : 'transparent',\n              transition: 'background-color 0.2s',\n            }}\n            onMouseDown={handleMouseDown}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = '#1890ff';\n            }}\n            onMouseLeave={(e) => {\n              if (!isResizing) {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              }\n            }}\n          />\n        )}\n      </Sider>\n\n      <Layout style={{\n        marginLeft: sidebarCollapsed ? 80 : siderWidth,\n        transition: 'margin-left 0.2s',\n      }}>\n        <Header style={{\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: sidebarCollapsed ? 80 : siderWidth,\n          zIndex: 999,\n          transition: 'left 0.2s',\n        }}>\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => dispatch(toggleSidebar())}\n            style={{\n              fontSize: '16px',\n              width: 64,\n              height: 64,\n            }}\n          />\n\n          <Space>\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>{user?.username}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: '88px 16px 24px 16px', // 顶部留出Header的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAkB,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAChF,OAAO,kBAAkB;AACzB,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,WAAW,EACXC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,MAAM,EAAEC,WAAW,QAAQ,8BAA8B;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG7B,MAAM;AACzC,MAAM;EAAE8B;AAAM,CAAC,GAAGxB,UAAU;AAM5B,MAAMyB,UAAqC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEgB;EAAK,CAAC,GAAGjB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC9D,MAAM;IAAEC;EAAiB,CAAC,GAAGpB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EAIxE1C,SAAS,CAAC,MAAM;IACd;IACAqC,QAAQ,CAACb,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACa,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMM,eAAe,GAAIC,CAAmB,IAAK;IAC/CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBC,aAAa,CAAC,IAAI,CAAC;IAEnB,MAAMC,MAAM,GAAGH,CAAC,CAACI,OAAO;IACxB,MAAMC,UAAU,GAAGC,UAAU;IAE7B,MAAMC,eAAe,GAAIP,CAAa,IAAK;MACzC,MAAMQ,QAAQ,GAAGH,UAAU,IAAIL,CAAC,CAACI,OAAO,GAAGD,MAAM,CAAC;MAClD,IAAIK,QAAQ,IAAI,GAAG,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACtCC,aAAa,CAACD,QAAQ,CAAC;MACzB;IACF,CAAC;IAED,MAAME,aAAa,GAAGA,CAAA,KAAM;MAC1BR,aAAa,CAAC,KAAK,CAAC;MACpBS,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEL,eAAe,CAAC;MAC1DI,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;IACxD,CAAC;IAEDC,QAAQ,CAACE,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IACvDI,QAAQ,CAACE,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;EACrD,CAAC;EAED,MAAMI,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,gBAAgB;IACrBC,IAAI,eAAEjC,OAAA,CAACjB,gBAAgB;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAEjC,OAAA,CAAChB,kBAAkB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAEjC,OAAA,CAACf,WAAW;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAEjC,OAAA,CAACd,gBAAgB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAEjC,OAAA,CAACb,gBAAgB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAEjC,OAAA,CAACZ,cAAc;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAEjC,OAAA,CAACX,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIP,GAAW,IAAK;IACvCxB,QAAQ,CAACwB,GAAG,CAAC;EACf,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB9B,QAAQ,CAACd,MAAM,CAAC,CAAC,CAAC;IAClBY,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMiC,aAAa,GAAG,CACpB;IACET,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEjC,OAAA,CAACX,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEjC,OAAA,CAACT,eAAe;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,IAAI,EAAE;EACR,CAAC,EACD;IACEV,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEjC,OAAA,CAACV,cAAc;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbK,OAAO,EAAEH;EACX,CAAC,CACF;EAED,oBACExC,OAAA,CAAC1B,MAAM;IAACsE,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAACC,SAAS,EAAEC,UAAU,GAAG,UAAU,GAAG,EAAG;IAAAzC,QAAA,gBAC7EN,OAAA,CAACE,KAAK;MACJ8C,OAAO,EAAE,IAAK;MACdC,WAAW;MACXC,SAAS,EAAEpC,gBAAiB;MAC5BqC,KAAK,EAAErC,gBAAgB,GAAG,EAAE,GAAGS,UAAW;MAC1CqB,KAAK,EAAE;QACLQ,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE;MACZ,CAAE;MAAArD,QAAA,gBAEFN,OAAA;QAAK4C,KAAK,EAAE;UACVgB,OAAO,EAAE,UAAU;UACnBC,SAAS,EAAE,QAAQ;UACnBC,YAAY,EAAE,mBAAmB;UACjCC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAA5D,QAAA,EACC,CAACQ,gBAAgB,gBAChBd,OAAA,CAACI,KAAK;UAAC+D,KAAK,EAAE,CAAE;UAACrB,SAAS,EAAC,cAAc;UAACF,KAAK,EAAE;YAC/CwB,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,QAAQ;YACpBZ,QAAQ,EAAE,QAAQ;YAClBa,YAAY,EAAE,UAAU;YACxBC,UAAU,EAAE;UACd,CAAE;UAAAnE,QAAA,EAAC;QAEH;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERrC,OAAA,CAACI,KAAK;UAAC+D,KAAK,EAAE,CAAE;UAACrB,SAAS,EAAC,cAAc;UAACF,KAAK,EAAE;YAC/CwB,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBG,UAAU,EAAE;UACd,CAAE;UAAAnE,QAAA,EAAC;QAEH;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENrC,OAAA,CAACzB,IAAI;QACHmG,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAAClE,QAAQ,CAACmE,QAAQ,CAAE;QAClCC,KAAK,EAAE9C,SAAU;QACjBY,OAAO,EAAEA,CAAC;UAAEX;QAAI,CAAC,KAAKO,eAAe,CAACP,GAAG,CAAE;QAC3CY,KAAK,EAAE;UACLkC,MAAM,EAAE;QACV,CAAE;QACFhC,SAAS,EAAEhC,gBAAgB,GAAG,gBAAgB,GAAG;MAAgB;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,EAGD,CAACvB,gBAAgB,iBAChBd,OAAA;QACE4C,KAAK,EAAE;UACLU,QAAQ,EAAE,UAAU;UACpByB,KAAK,EAAE,CAAC;UACRvB,GAAG,EAAE,CAAC;UACNC,MAAM,EAAE,CAAC;UACTN,KAAK,EAAE,KAAK;UACZ6B,MAAM,EAAE,YAAY;UACpBC,eAAe,EAAElC,UAAU,GAAG,SAAS,GAAG,aAAa;UACvDmC,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAEnE,eAAgB;QAC7BoE,YAAY,EAAGnE,CAAC,IAAK;UACnBA,CAAC,CAACoE,aAAa,CAACzC,KAAK,CAACqC,eAAe,GAAG,SAAS;QACnD,CAAE;QACFK,YAAY,EAAGrE,CAAC,IAAK;UACnB,IAAI,CAAC8B,UAAU,EAAE;YACf9B,CAAC,CAACoE,aAAa,CAACzC,KAAK,CAACqC,eAAe,GAAG,aAAa;UACvD;QACF;MAAE;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERrC,OAAA,CAAC1B,MAAM;MAACsE,KAAK,EAAE;QACb2C,UAAU,EAAEzE,gBAAgB,GAAG,EAAE,GAAGS,UAAU;QAC9C2D,UAAU,EAAE;MACd,CAAE;MAAA5E,QAAA,gBACAN,OAAA,CAACC,MAAM;QAAC2C,KAAK,EAAE;UACbgB,OAAO,EAAE,QAAQ;UACjBR,UAAU,EAAE,MAAM;UAClBY,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/Bb,SAAS,EAAE,2BAA2B;UACtCC,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,CAAC;UACNuB,KAAK,EAAE,CAAC;UACRxB,IAAI,EAAEzC,gBAAgB,GAAG,EAAE,GAAGS,UAAU;UACxCmC,MAAM,EAAE,GAAG;UACXwB,UAAU,EAAE;QACd,CAAE;QAAA5E,QAAA,gBACAN,OAAA,CAACxB,MAAM;UACLkE,IAAI,EAAC,MAAM;UACXT,IAAI,EAAEnB,gBAAgB,gBAAGd,OAAA,CAAClB,kBAAkB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACnB,gBAAgB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvEM,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAACZ,aAAa,CAAC,CAAC,CAAE;UACzC8C,KAAK,EAAE;YACL0B,QAAQ,EAAE,MAAM;YAChBnB,KAAK,EAAE,EAAE;YACTY,MAAM,EAAE;UACV;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFrC,OAAA,CAACrB,KAAK;UAAA2B,QAAA,eACJN,OAAA,CAACtB,QAAQ;YACP8G,IAAI,EAAE;cAAEX,KAAK,EAAEpC;YAAc,CAAE;YAC/BgD,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAApF,QAAA,eAELN,OAAA,CAACrB,KAAK;cAACiE,KAAK,EAAE;gBAAEoC,MAAM,EAAE;cAAU,CAAE;cAAA1E,QAAA,gBAClCN,OAAA,CAACvB,MAAM;gBAACwD,IAAI,eAAEjC,OAAA,CAACX,YAAY;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCrC,OAAA;gBAAAM,QAAA,EAAOK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF;cAAQ;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETrC,OAAA,CAACG,OAAO;QAACyC,KAAK,EAAE;UACdwB,MAAM,EAAE,qBAAqB;UAAE;UAC/BR,OAAO,EAAE,EAAE;UACXR,UAAU,EAAE,MAAM;UAClBwC,YAAY,EAAE,KAAK;UACnB/C,SAAS,EAAE;QACb,CAAE;QAAAvC,QAAA,EACCA;MAAQ;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAAC9B,EAAA,CA5PIF,UAAqC;EAAA,QACxBb,WAAW,EACXC,WAAW,EACXE,WAAW,EAEXD,WAAW,EACCA,WAAW;AAAA;AAAAmG,EAAA,GANpCxF,UAAqC;AA8P3C,eAAeA,UAAU;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}