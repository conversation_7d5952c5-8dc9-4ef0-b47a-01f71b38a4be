{"ast": null, "code": "var baseEach = require('./_baseEach'),\n  isArrayLike = require('./isArrayLike');\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n    result = isArrayLike(collection) ? Array(collection.length) : [];\n  baseEach(collection, function (value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\nmodule.exports = baseMap;", "map": {"version": 3, "names": ["baseEach", "require", "isArrayLike", "baseMap", "collection", "iteratee", "index", "result", "Array", "length", "value", "key", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseMap.js"], "sourcesContent": ["var baseEach = require('./_baseEach'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\n\nmodule.exports = baseMap;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EACrC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,WAAW,CAACE,UAAU,CAAC,GAAGI,KAAK,CAACJ,UAAU,CAACK,MAAM,CAAC,GAAG,EAAE;EAEpET,QAAQ,CAACI,UAAU,EAAE,UAASM,KAAK,EAAEC,GAAG,EAAEP,UAAU,EAAE;IACpDG,MAAM,CAAC,EAAED,KAAK,CAAC,GAAGD,QAAQ,CAACK,KAAK,EAAEC,GAAG,EAAEP,UAAU,CAAC;EACpD,CAAC,CAAC;EACF,OAAOG,MAAM;AACf;AAEAK,MAAM,CAACC,OAAO,GAAGV,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}