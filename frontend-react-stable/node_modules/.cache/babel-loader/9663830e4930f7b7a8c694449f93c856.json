{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pow;\nexports.powish = powish;\nexports.sqrt = sqrt;\nvar _linear = require(\"./linear.js\");\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nfunction transformPow(exponent) {\n  return function (x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\nfunction powish(transform) {\n  var scale = transform(_continuous.identity, _continuous.identity),\n    exponent = 1;\n  function rescale() {\n    return exponent === 1 ? transform(_continuous.identity, _continuous.identity) : exponent === 0.5 ? transform(transformSqrt, transformSquare) : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n  scale.exponent = function (_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n  return (0, _linear.linearish)(scale);\n}\nfunction pow() {\n  var scale = powish((0, _continuous.transformer)());\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, pow()).exponent(scale.exponent());\n  };\n  _init.initRange.apply(scale, arguments);\n  return scale;\n}\nfunction sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "pow", "powish", "sqrt", "_linear", "require", "_continuous", "_init", "transformPow", "exponent", "x", "Math", "transformSqrt", "transformSquare", "transform", "scale", "identity", "rescale", "_", "arguments", "length", "linearish", "transformer", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/pow.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pow;\nexports.powish = powish;\nexports.sqrt = sqrt;\n\nvar _linear = require(\"./linear.js\");\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction transformPow(exponent) {\n  return function (x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nfunction powish(transform) {\n  var scale = transform(_continuous.identity, _continuous.identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(_continuous.identity, _continuous.identity) : exponent === 0.5 ? transform(transformSqrt, transformSquare) : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function (_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return (0, _linear.linearish)(scale);\n}\n\nfunction pow() {\n  var scale = powish((0, _continuous.transformer)());\n\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, pow()).exponent(scale.exponent());\n  };\n\n  _init.initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nfunction sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AACrBH,OAAO,CAACI,MAAM,GAAGA,MAAM;AACvBJ,OAAO,CAACK,IAAI,GAAGA,IAAI;AAEnB,IAAIC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASG,YAAYA,CAACC,QAAQ,EAAE;EAC9B,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACC,IAAI,CAACV,GAAG,CAAC,CAACS,CAAC,EAAED,QAAQ,CAAC,GAAGE,IAAI,CAACV,GAAG,CAACS,CAAC,EAAED,QAAQ,CAAC;EAChE,CAAC;AACH;AAEA,SAASG,aAAaA,CAACF,CAAC,EAAE;EACxB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACC,IAAI,CAACR,IAAI,CAAC,CAACO,CAAC,CAAC,GAAGC,IAAI,CAACR,IAAI,CAACO,CAAC,CAAC;AAC9C;AAEA,SAASG,eAAeA,CAACH,CAAC,EAAE;EAC1B,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAC/B;AAEA,SAASR,MAAMA,CAACY,SAAS,EAAE;EACzB,IAAIC,KAAK,GAAGD,SAAS,CAACR,WAAW,CAACU,QAAQ,EAAEV,WAAW,CAACU,QAAQ,CAAC;IAC7DP,QAAQ,GAAG,CAAC;EAEhB,SAASQ,OAAOA,CAAA,EAAG;IACjB,OAAOR,QAAQ,KAAK,CAAC,GAAGK,SAAS,CAACR,WAAW,CAACU,QAAQ,EAAEV,WAAW,CAACU,QAAQ,CAAC,GAAGP,QAAQ,KAAK,GAAG,GAAGK,SAAS,CAACF,aAAa,EAAEC,eAAe,CAAC,GAAGC,SAAS,CAACN,YAAY,CAACC,QAAQ,CAAC,EAAED,YAAY,CAAC,CAAC,GAAGC,QAAQ,CAAC,CAAC;EAC9M;EAEAM,KAAK,CAACN,QAAQ,GAAG,UAAUS,CAAC,EAAE;IAC5B,OAAOC,SAAS,CAACC,MAAM,IAAIX,QAAQ,GAAG,CAACS,CAAC,EAAED,OAAO,CAAC,CAAC,IAAIR,QAAQ;EACjE,CAAC;EAED,OAAO,CAAC,CAAC,EAAEL,OAAO,CAACiB,SAAS,EAAEN,KAAK,CAAC;AACtC;AAEA,SAASd,GAAGA,CAAA,EAAG;EACb,IAAIc,KAAK,GAAGb,MAAM,CAAC,CAAC,CAAC,EAAEI,WAAW,CAACgB,WAAW,EAAE,CAAC,CAAC;EAElDP,KAAK,CAACQ,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEjB,WAAW,CAACiB,IAAI,EAAER,KAAK,EAAEd,GAAG,CAAC,CAAC,CAAC,CAACQ,QAAQ,CAACM,KAAK,CAACN,QAAQ,CAAC,CAAC,CAAC;EACvE,CAAC;EAEDF,KAAK,CAACiB,SAAS,CAACC,KAAK,CAACV,KAAK,EAAEI,SAAS,CAAC;EAEvC,OAAOJ,KAAK;AACd;AAEA,SAASZ,IAAIA,CAAA,EAAG;EACd,OAAOF,GAAG,CAACwB,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC,CAACV,QAAQ,CAAC,GAAG,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}