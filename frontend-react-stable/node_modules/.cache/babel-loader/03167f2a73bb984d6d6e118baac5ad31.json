{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from '../_util/warning';\nfunction filter(items) {\n  return items.filter(function (item) {\n    return item;\n  });\n}\nexport default function useLegacyItems(items, children) {\n  if (items) {\n    return items;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!children, 'Steps', 'Step is deprecated. Please use `items` directly.') : void 0;\n  var childrenItems = toArray(children).map(function (node) {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      var props = node.props;\n      var item = _extends({}, props);\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "map": {"version": 3, "names": ["_extends", "React", "toArray", "warning", "filter", "items", "item", "useLegacyItems", "children", "process", "env", "NODE_ENV", "childrenItems", "map", "node", "isValidElement", "props"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/steps/useLegacyItems.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from '../_util/warning';\nfunction filter(items) {\n  return items.filter(function (item) {\n    return item;\n  });\n}\nexport default function useLegacyItems(items, children) {\n  if (items) {\n    return items;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!children, 'Steps', 'Step is deprecated. Please use `items` directly.') : void 0;\n  var childrenItems = toArray(children).map(function (node) {\n    if ( /*#__PURE__*/React.isValidElement(node)) {\n      var props = node.props;\n      var item = _extends({}, props);\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,CAAC,UAAUE,IAAI,EAAE;IAClC,OAAOA,IAAI;EACb,CAAC,CAAC;AACJ;AACA,eAAe,SAASC,cAAcA,CAACF,KAAK,EAAEG,QAAQ,EAAE;EACtD,IAAIH,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EACAI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,OAAO,CAAC,CAACK,QAAQ,EAAE,OAAO,EAAE,kDAAkD,CAAC,GAAG,KAAK,CAAC;EAChI,IAAII,aAAa,GAAGV,OAAO,CAACM,QAAQ,CAAC,CAACK,GAAG,CAAC,UAAUC,IAAI,EAAE;IACxD,IAAK,aAAab,KAAK,CAACc,cAAc,CAACD,IAAI,CAAC,EAAE;MAC5C,IAAIE,KAAK,GAAGF,IAAI,CAACE,KAAK;MACtB,IAAIV,IAAI,GAAGN,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,CAAC;MAC9B,OAAOV,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF,OAAOF,MAAM,CAACQ,aAAa,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}