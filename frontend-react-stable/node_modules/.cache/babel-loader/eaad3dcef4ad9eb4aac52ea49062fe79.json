{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(<PERSON><PERSON>rap<PERSON>, _React$Component);\n  var _super = _createSuper(<PERSON>Wrapper);\n  function DomWrapper() {\n    _classCallCheck(this, <PERSON>Wrapper);\n    return _super.apply(this, arguments);\n  }\n  _createClass(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(React.Component);\nexport default DomWrapper;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "DomWrapper", "_React$Component", "_super", "apply", "arguments", "key", "value", "render", "props", "children", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/DomWrapper.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(<PERSON><PERSON>rap<PERSON>, _React$Component);\n  var _super = _createSuper(<PERSON>Wrapper);\n  function DomWrapper() {\n    _classCallCheck(this, <PERSON>Wrapper);\n    return _super.apply(this, arguments);\n  }\n  _createClass(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(React.Component);\nexport default DomWrapper;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACxDJ,SAAS,CAACG,UAAU,EAAEC,gBAAgB,CAAC;EACvC,IAAIC,MAAM,GAAGJ,YAAY,CAACE,UAAU,CAAC;EACrC,SAASA,UAAUA,CAAA,EAAG;IACpBL,eAAe,CAAC,IAAI,EAAEK,UAAU,CAAC;IACjC,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EACAR,YAAY,CAACI,UAAU,EAAE,CAAC;IACxBK,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ;IAC5B;EACF,CAAC,CAAC,CAAC;EACH,OAAOT,UAAU;AACnB,CAAC,CAACD,KAAK,CAACW,SAAS,CAAC;AAClB,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}