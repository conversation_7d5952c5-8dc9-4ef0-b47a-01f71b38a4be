{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = maxIndex;\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "maxIndex", "values", "valueof", "max", "index", "undefined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/maxIndex.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = maxIndex;\n\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n\n  return maxIndex;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjC,IAAIC,GAAG;EACP,IAAIH,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAII,KAAK,GAAG,CAAC,CAAC;EAEd,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,MAAMP,KAAK,IAAIG,MAAM,EAAE;MAC1B,EAAEG,KAAK;MAEP,IAAIN,KAAK,IAAI,IAAI,KAAKK,GAAG,GAAGL,KAAK,IAAIK,GAAG,KAAKE,SAAS,IAAIP,KAAK,IAAIA,KAAK,CAAC,EAAE;QACzEK,GAAG,GAAGL,KAAK,EAAEE,QAAQ,GAAGI,KAAK;MAC/B;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIN,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,KAAK,IAAI,KAAKE,GAAG,GAAGL,KAAK,IAAIK,GAAG,KAAKE,SAAS,IAAIP,KAAK,IAAIA,KAAK,CAAC,EAAE;QAC7GK,GAAG,GAAGL,KAAK,EAAEE,QAAQ,GAAGI,KAAK;MAC/B;IACF;EACF;EAEA,OAAOJ,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}