{"ast": null, "code": "import InternalCheckbox from './Checkbox';\nimport Group from './Group';\nvar Checkbox = InternalCheckbox;\nCheckbox.Group = Group;\nCheckbox.__ANT_CHECKBOX = true;\nexport default Checkbox;", "map": {"version": 3, "names": ["InternalCheckbox", "Group", "Checkbox", "__ANT_CHECKBOX"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/checkbox/index.js"], "sourcesContent": ["import InternalCheckbox from './Checkbox';\nimport Group from './Group';\nvar Checkbox = InternalCheckbox;\nCheckbox.Group = Group;\nCheckbox.__ANT_CHECKBOX = true;\nexport default Checkbox;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,YAAY;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,QAAQ,GAAGF,gBAAgB;AAC/BE,QAAQ,CAACD,KAAK,GAAGA,KAAK;AACtBC,QAAQ,CAACC,cAAc,GAAG,IAAI;AAC9B,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}