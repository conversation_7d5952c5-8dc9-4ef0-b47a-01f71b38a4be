{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _math = require(\"../math.js\");\nvar _default = {\n  draw(context, size) {\n    const w = (0, _math.sqrt)(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_math", "require", "_default", "draw", "context", "size", "w", "sqrt", "x", "rect"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/symbol/square.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _math = require(\"../math.js\");\n\nvar _default = {\n  draw(context, size) {\n    const w = (0, _math.sqrt)(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,IAAIC,QAAQ,GAAG;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACO,IAAI,EAAEF,IAAI,CAAC;IAC/B,MAAMG,CAAC,GAAG,CAACF,CAAC,GAAG,CAAC;IAChBF,OAAO,CAACK,IAAI,CAACD,CAAC,EAAEA,CAAC,EAAEF,CAAC,EAAEA,CAAC,CAAC;EAC1B;AAEF,CAAC;AACDT,OAAO,CAACE,OAAO,GAAGG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}