{"ast": null, "code": "'use strict';\n\n/** @type {import('./min')} */\nmodule.exports = Math.min;", "map": {"version": 3, "names": ["module", "exports", "Math", "min"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/math-intrinsics/min.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./min')} */\nmodule.exports = Math.min;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script"}