{"ast": null, "code": "/**\n * @fileOverview Bar Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Bar } from '../cartesian/Bar';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var BarChart = generateCategoricalChart({\n  chartName: 'BarChart',\n  GraphicalChild: Bar,\n  defaultTooltipEventType: 'axis',\n  validateTooltipEventTypes: ['axis', 'item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "Bar", "XAxis", "YA<PERSON>s", "formatAxisMap", "<PERSON><PERSON><PERSON>", "chartName", "GraphicalChild", "defaultTooltipEventType", "validateTooltipEventTypes", "axisComponents", "axisType", "AxisComp"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/chart/BarChart.js"], "sourcesContent": ["/**\n * @fileOverview Bar Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Bar } from '../cartesian/Bar';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var BarChart = generateCategoricalChart({\n  chartName: 'BarChart',\n  GraphicalChild: Bar,\n  defaultTooltipEventType: 'axis',\n  validateTooltipEventTypes: ['axis', 'item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAO,IAAIC,QAAQ,GAAGL,wBAAwB,CAAC;EAC7CM,SAAS,EAAE,UAAU;EACrBC,cAAc,EAAEN,GAAG;EACnBO,uBAAuB,EAAE,MAAM;EAC/BC,yBAAyB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC3CC,cAAc,EAAE,CAAC;IACfC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAEV;EACZ,CAAC,EAAE;IACDS,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAET;EACZ,CAAC,CAAC;EACFC,aAAa,EAAEA;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}