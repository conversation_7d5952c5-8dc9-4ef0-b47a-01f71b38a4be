{"ast": null, "code": "var conversions = {\n  // length\n  'px': {\n    'px': 1,\n    'cm': 96.0 / 2.54,\n    'mm': 96.0 / 25.4,\n    'in': 96,\n    'pt': 96.0 / 72.0,\n    'pc': 16\n  },\n  'cm': {\n    'px': 2.54 / 96.0,\n    'cm': 1,\n    'mm': 0.1,\n    'in': 2.54,\n    'pt': 2.54 / 72.0,\n    'pc': 2.54 / 6.0\n  },\n  'mm': {\n    'px': 25.4 / 96.0,\n    'cm': 10,\n    'mm': 1,\n    'in': 25.4,\n    'pt': 25.4 / 72.0,\n    'pc': 25.4 / 6.0\n  },\n  'in': {\n    'px': 1.0 / 96.0,\n    'cm': 1.0 / 2.54,\n    'mm': 1.0 / 25.4,\n    'in': 1,\n    'pt': 1.0 / 72.0,\n    'pc': 1.0 / 6.0\n  },\n  'pt': {\n    'px': 0.75,\n    'cm': 72.0 / 2.54,\n    'mm': 72.0 / 25.4,\n    'in': 72,\n    'pt': 1,\n    'pc': 12\n  },\n  'pc': {\n    'px': 6.0 / 96.0,\n    'cm': 6.0 / 2.54,\n    'mm': 6.0 / 25.4,\n    'in': 6,\n    'pt': 6.0 / 72.0,\n    'pc': 1\n  },\n  // angle\n  'deg': {\n    'deg': 1,\n    'grad': 0.9,\n    'rad': 180 / Math.PI,\n    'turn': 360\n  },\n  'grad': {\n    'deg': 400 / 360,\n    'grad': 1,\n    'rad': 200 / Math.PI,\n    'turn': 400\n  },\n  'rad': {\n    'deg': Math.PI / 180,\n    'grad': Math.PI / 200,\n    'rad': 1,\n    'turn': Math.PI * 2\n  },\n  'turn': {\n    'deg': 1 / 360,\n    'grad': 1 / 400,\n    'rad': 0.5 / Math.PI,\n    'turn': 1\n  },\n  // time\n  's': {\n    's': 1,\n    'ms': 1 / 1000\n  },\n  'ms': {\n    's': 1000,\n    'ms': 1\n  },\n  // frequency\n  'Hz': {\n    'Hz': 1,\n    'kHz': 1000\n  },\n  'kHz': {\n    'Hz': 1 / 1000,\n    'kHz': 1\n  },\n  // resolution\n  'dpi': {\n    'dpi': 1,\n    'dpcm': 1.0 / 2.54,\n    'dppx': 1 / 96\n  },\n  'dpcm': {\n    'dpi': 2.54,\n    'dpcm': 1,\n    'dppx': 2.54 / 96.0\n  },\n  'dppx': {\n    'dpi': 96,\n    'dpcm': 96.0 / 2.54,\n    'dppx': 1\n  }\n};\nmodule.exports = function (value, sourceUnit, targetUnit, precision) {\n  if (!conversions.hasOwnProperty(targetUnit)) throw new Error(\"Cannot convert to \" + targetUnit);\n  if (!conversions[targetUnit].hasOwnProperty(sourceUnit)) throw new Error(\"Cannot convert from \" + sourceUnit + \" to \" + targetUnit);\n  var converted = conversions[targetUnit][sourceUnit] * value;\n  if (precision !== false) {\n    precision = Math.pow(10, parseInt(precision) || 5);\n    return Math.round(converted * precision) / precision;\n  }\n  return converted;\n};", "map": {"version": 3, "names": ["conversions", "Math", "PI", "module", "exports", "value", "sourceUnit", "targetUnit", "precision", "hasOwnProperty", "Error", "converted", "pow", "parseInt", "round"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/css-unit-converter/index.js"], "sourcesContent": ["var conversions = {\r\n    // length\r\n    'px': {\r\n        'px': 1,\r\n        'cm': 96.0/2.54,\r\n        'mm': 96.0/25.4,\r\n        'in': 96,\r\n        'pt': 96.0/72.0,\r\n        'pc': 16\r\n    },\r\n    'cm': {\r\n        'px': 2.54/96.0,\r\n        'cm': 1,\r\n        'mm': 0.1,\r\n        'in': 2.54,\r\n        'pt': 2.54/72.0,\r\n        'pc': 2.54/6.0\r\n    },\r\n    'mm': {\r\n        'px': 25.4/96.0,\r\n        'cm': 10,\r\n        'mm': 1,\r\n        'in': 25.4,\r\n        'pt': 25.4/72.0,\r\n        'pc': 25.4/6.0\r\n    },\r\n    'in': {\r\n        'px': 1.0/96.0,\r\n        'cm': 1.0/2.54,\r\n        'mm': 1.0/25.4,\r\n        'in': 1,\r\n        'pt': 1.0/72.0,\r\n        'pc': 1.0/6.0\r\n    },\r\n    'pt': {\r\n        'px': 0.75,\r\n        'cm': 72.0/2.54,\r\n        'mm': 72.0/25.4,\r\n        'in': 72,\r\n        'pt': 1,\r\n        'pc': 12\r\n    },\r\n    'pc': {\r\n        'px': 6.0/96.0,\r\n        'cm': 6.0/2.54,\r\n        'mm': 6.0/25.4,\r\n        'in': 6,\r\n        'pt': 6.0/72.0,\r\n        'pc': 1\r\n    },\r\n    // angle\r\n    'deg': {\r\n        'deg': 1,\r\n        'grad': 0.9,\r\n        'rad': 180/Math.PI,\r\n        'turn': 360\r\n    },\r\n    'grad': {\r\n        'deg': 400/360,\r\n        'grad': 1,\r\n        'rad': 200/Math.PI,\r\n        'turn': 400\r\n    },\r\n    'rad': {\r\n        'deg': Math.PI/180,\r\n        'grad': Math.PI/200,\r\n        'rad': 1,\r\n        'turn': Math.PI*2\r\n    },\r\n    'turn': {\r\n        'deg': 1/360,\r\n        'grad': 1/400,\r\n        'rad': 0.5/Math.PI,\r\n        'turn': 1\r\n    },\r\n    // time\r\n    's': {\r\n        's': 1,\r\n        'ms': 1/1000\r\n    },\r\n    'ms': {\r\n        's': 1000,\r\n        'ms': 1\r\n    },\r\n    // frequency\r\n    'Hz': {\r\n        'Hz': 1,\r\n        'kHz': 1000\r\n    },\r\n    'kHz': {\r\n        'Hz': 1/1000,\r\n        'kHz': 1\r\n    },\r\n    // resolution\r\n    'dpi': {\r\n        'dpi': 1,\r\n        'dpcm': 1.0/2.54,\r\n        'dppx': 1/96\r\n    },\r\n    'dpcm': {\r\n        'dpi': 2.54,\r\n        'dpcm': 1,\r\n        'dppx': 2.54/96.0\r\n    },\r\n    'dppx': {\r\n        'dpi': 96,\r\n        'dpcm': 96.0/2.54,\r\n        'dppx': 1\r\n    }\r\n};\r\n\r\nmodule.exports = function (value, sourceUnit, targetUnit, precision) {\r\n    if (!conversions.hasOwnProperty(targetUnit))\r\n        throw new Error(\"Cannot convert to \" + targetUnit);\r\n\r\n    if (!conversions[targetUnit].hasOwnProperty(sourceUnit))\r\n        throw new Error(\"Cannot convert from \" + sourceUnit + \" to \" + targetUnit);\r\n    \r\n    var converted = conversions[targetUnit][sourceUnit] * value;\r\n    \r\n    if (precision !== false) {\r\n        precision = Math.pow(10, parseInt(precision) || 5);\r\n        return Math.round(converted * precision) / precision;\r\n    }\r\n    \r\n    return converted;\r\n};\r\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG;EACd;EACA,IAAI,EAAE;IACF,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE;EACV,CAAC;EACD,IAAI,EAAE;IACF,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,IAAI,GAAC;EACf,CAAC;EACD,IAAI,EAAE;IACF,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,IAAI,GAAC;EACf,CAAC;EACD,IAAI,EAAE;IACF,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,GAAG,GAAC;EACd,CAAC;EACD,IAAI,EAAE;IACF,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,IAAI,GAAC,IAAI;IACf,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE;EACV,CAAC;EACD,IAAI,EAAE;IACF,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,GAAG,GAAC,IAAI;IACd,IAAI,EAAE;EACV,CAAC;EACD;EACA,KAAK,EAAE;IACH,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG,GAACC,IAAI,CAACC,EAAE;IAClB,MAAM,EAAE;EACZ,CAAC;EACD,MAAM,EAAE;IACJ,KAAK,EAAE,GAAG,GAAC,GAAG;IACd,MAAM,EAAE,CAAC;IACT,KAAK,EAAE,GAAG,GAACD,IAAI,CAACC,EAAE;IAClB,MAAM,EAAE;EACZ,CAAC;EACD,KAAK,EAAE;IACH,KAAK,EAAED,IAAI,CAACC,EAAE,GAAC,GAAG;IAClB,MAAM,EAAED,IAAI,CAACC,EAAE,GAAC,GAAG;IACnB,KAAK,EAAE,CAAC;IACR,MAAM,EAAED,IAAI,CAACC,EAAE,GAAC;EACpB,CAAC;EACD,MAAM,EAAE;IACJ,KAAK,EAAE,CAAC,GAAC,GAAG;IACZ,MAAM,EAAE,CAAC,GAAC,GAAG;IACb,KAAK,EAAE,GAAG,GAACD,IAAI,CAACC,EAAE;IAClB,MAAM,EAAE;EACZ,CAAC;EACD;EACA,GAAG,EAAE;IACD,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC,GAAC;EACZ,CAAC;EACD,IAAI,EAAE;IACF,GAAG,EAAE,IAAI;IACT,IAAI,EAAE;EACV,CAAC;EACD;EACA,IAAI,EAAE;IACF,IAAI,EAAE,CAAC;IACP,KAAK,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACH,IAAI,EAAE,CAAC,GAAC,IAAI;IACZ,KAAK,EAAE;EACX,CAAC;EACD;EACA,KAAK,EAAE;IACH,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,GAAG,GAAC,IAAI;IAChB,MAAM,EAAE,CAAC,GAAC;EACd,CAAC;EACD,MAAM,EAAE;IACJ,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,IAAI,GAAC;EACjB,CAAC;EACD,MAAM,EAAE;IACJ,KAAK,EAAE,EAAE;IACT,MAAM,EAAE,IAAI,GAAC,IAAI;IACjB,MAAM,EAAE;EACZ;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACjE,IAAI,CAACR,WAAW,CAACS,cAAc,CAACF,UAAU,CAAC,EACvC,MAAM,IAAIG,KAAK,CAAC,oBAAoB,GAAGH,UAAU,CAAC;EAEtD,IAAI,CAACP,WAAW,CAACO,UAAU,CAAC,CAACE,cAAc,CAACH,UAAU,CAAC,EACnD,MAAM,IAAII,KAAK,CAAC,sBAAsB,GAAGJ,UAAU,GAAG,MAAM,GAAGC,UAAU,CAAC;EAE9E,IAAII,SAAS,GAAGX,WAAW,CAACO,UAAU,CAAC,CAACD,UAAU,CAAC,GAAGD,KAAK;EAE3D,IAAIG,SAAS,KAAK,KAAK,EAAE;IACrBA,SAAS,GAAGP,IAAI,CAACW,GAAG,CAAC,EAAE,EAAEC,QAAQ,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;IAClD,OAAOP,IAAI,CAACa,KAAK,CAACH,SAAS,GAAGH,SAAS,CAAC,GAAGA,SAAS;EACxD;EAEA,OAAOG,SAAS;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}