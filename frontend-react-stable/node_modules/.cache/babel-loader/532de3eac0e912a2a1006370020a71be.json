{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.cubehelixLong = void 0;\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\nvar _color = _interopRequireWildcard(require(\"./color.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction cubehelix(hue) {\n  return function cubehelixGamma(y) {\n    y = +y;\n    function cubehelix(start, end) {\n      var h = hue((start = (0, _index.cubehelix)(start)).h, (end = (0, _index.cubehelix)(end)).h),\n        s = (0, _color.default)(start.s, end.s),\n        l = (0, _color.default)(start.l, end.l),\n        opacity = (0, _color.default)(start.opacity, end.opacity);\n      return function (t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n    cubehelix.gamma = cubehelixGamma;\n    return cubehelix;\n  }(1);\n}\nvar _default = cubehelix(_color.hue);\nexports.default = _default;\nvar cubehelixLong = cubehelix(_color.default);\nexports.cubehelixLong = cubehelixLong;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "cubehelixLong", "_index", "require", "_color", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "cubehelix", "hue", "cubehelix<PERSON>ma", "y", "start", "end", "h", "s", "l", "opacity", "t", "Math", "pow", "gamma", "_default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/cubehelix.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.cubehelixLong = void 0;\n\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\n\nvar _color = _interopRequireWildcard(require(\"./color.js\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction cubehelix(hue) {\n  return function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = (0, _index.cubehelix)(start)).h, (end = (0, _index.cubehelix)(end)).h),\n          s = (0, _color.default)(start.s, end.s),\n          l = (0, _color.default)(start.l, end.l),\n          opacity = (0, _color.default)(start.opacity, end.opacity);\n      return function (t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n    return cubehelix;\n  }(1);\n}\n\nvar _default = cubehelix(_color.hue);\n\nexports.default = _default;\nvar cubehelixLong = cubehelix(_color.default);\nexports.cubehelixLong = cubehelixLong;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,aAAa,GAAG,KAAK,CAAC;AAEhD,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,MAAM,GAAGC,uBAAuB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAE3D,SAASG,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASF,uBAAuBA,CAACM,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEX,OAAO,EAAEW;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGrB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACsB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIvB,MAAM,CAACwB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGrB,MAAM,CAACsB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE5B,MAAM,CAACC,cAAc,CAACmB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAChB,OAAO,GAAGW,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASS,SAASA,CAACC,GAAG,EAAE;EACtB,OAAO,SAASC,cAAcA,CAACC,CAAC,EAAE;IAChCA,CAAC,GAAG,CAACA,CAAC;IAEN,SAASH,SAASA,CAACI,KAAK,EAAEC,GAAG,EAAE;MAC7B,IAAIC,CAAC,GAAGL,GAAG,CAAC,CAACG,KAAK,GAAG,CAAC,CAAC,EAAE3B,MAAM,CAACuB,SAAS,EAAEI,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAACuB,SAAS,EAAEK,GAAG,CAAC,EAAEC,CAAC,CAAC;QACvFC,CAAC,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAACJ,OAAO,EAAE6B,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;QACvCC,CAAC,GAAG,CAAC,CAAC,EAAE7B,MAAM,CAACJ,OAAO,EAAE6B,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;QACvCC,OAAO,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAACJ,OAAO,EAAE6B,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;MAC7D,OAAO,UAAUC,CAAC,EAAE;QAClBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;QACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;QACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACC,GAAG,CAACF,CAAC,EAAEP,CAAC,CAAC,CAAC;QAC3BC,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;QAC1B,OAAON,KAAK,GAAG,EAAE;MACnB,CAAC;IACH;IAEAJ,SAAS,CAACa,KAAK,GAAGX,cAAc;IAChC,OAAOF,SAAS;EAClB,CAAC,CAAC,CAAC,CAAC;AACN;AAEA,IAAIc,QAAQ,GAAGd,SAAS,CAACrB,MAAM,CAACsB,GAAG,CAAC;AAEpC5B,OAAO,CAACE,OAAO,GAAGuC,QAAQ;AAC1B,IAAItC,aAAa,GAAGwB,SAAS,CAACrB,MAAM,CAACJ,OAAO,CAAC;AAC7CF,OAAO,CAACG,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}