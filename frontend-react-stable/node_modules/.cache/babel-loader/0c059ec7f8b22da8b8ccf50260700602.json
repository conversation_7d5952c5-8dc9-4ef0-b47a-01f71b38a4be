{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Typography, Card, Alert, Tabs, Button, Select, Input, Form, message, Table, Space, Modal, Row, Col, Divider } from 'antd';\nimport { FileTextOutlined, SettingOutlined, SendOutlined, ReloadOutlined, EyeOutlined, DownloadOutlined, EditOutlined, FolderOpenOutlined, CloudUploadOutlined } from '@ant-design/icons';\nimport { cleanTemplateAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\nconst CleanTemplatePage = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const [loading, setLoading] = useState(false);\n  const [generateForm] = Form.useForm();\n  const [sendForm] = Form.useForm();\n  const [updateForm] = Form.useForm();\n\n  // 模板生成相关状态\n  const [resultFiles, setResultFiles] = useState([]);\n  const [resultDir, setResultDir] = useState('');\n  const [lastGeneratedTemplate, setLastGeneratedTemplate] = useState(null);\n\n  // 模板管理相关状态\n  const [templates, setTemplates] = useState([]);\n  const [templateDir, setTemplateDir] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [templateContent, setTemplateContent] = useState('');\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n\n  // 模板发送功能的状态\n  const [sendTemplateDir, setSendTemplateDir] = useState('');\n  const [sendTemplates, setSendTemplates] = useState([]);\n  const [sendTemplatesLoading, setSendTemplatesLoading] = useState(false);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async (showMessage = true) => {\n    if (!resultDir.trim()) {\n      setResultFiles([]);\n      return;\n    }\n    try {\n      const response = await cleanTemplateAPI.listResultFiles(resultDir);\n      if (response.data.files) {\n        setResultFiles(response.data.files || []);\n        if (showMessage && response.data.files.length > 0) {\n          message.success(`📁 找到 ${response.data.files.length} 个结果文件`);\n        } else if (showMessage && response.data.files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        }\n      }\n    } catch (error) {\n      console.error('获取结果文件失败:', error);\n      if (showMessage) {\n        var _error$response, _error$response$data;\n        message.error(`❌ 获取结果文件失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n      }\n      setResultFiles([]);\n    }\n  }, [resultDir]);\n\n  // 获取模板列表\n  const fetchTemplates = useCallback(async (showMessage = true) => {\n    if (!templateDir.trim()) {\n      setTemplates([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(templateDir);\n      console.log('模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${templateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              var _item$filename;\n              // 如果已经是对象格式，直接使用\n              return {\n                template_name: item.template_name || ((_item$filename = item.filename) === null || _item$filename === void 0 ? void 0 : _item$filename.replace('_cleantemplate.json', '')) || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${templateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n        console.log('处理后的模板数据:', processedTemplates); // 调试信息\n        setTemplates(processedTemplates);\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error) {\n      console.error('获取模板列表失败:', error);\n      if (showMessage) {\n        var _error$response2, _error$response2$data;\n        message.error(`❌ 获取模板列表失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n      }\n      setTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [templateDir]);\n\n  // 获取发送模板列表\n  const fetchSendTemplates = useCallback(async (showMessage = true) => {\n    if (!sendTemplateDir.trim()) {\n      setSendTemplates([]);\n      return;\n    }\n    setSendTemplatesLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(sendTemplateDir);\n      console.log('发送模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('发送模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${sendTemplateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              var _item$filename2;\n              // 如果已经是对象格式，确保所有必需字段存在\n              return {\n                template_name: item.template_name || ((_item$filename2 = item.filename) === null || _item$filename2 === void 0 ? void 0 : _item$filename2.replace('_cleantemplate.json', '')) || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${sendTemplateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n        console.log('处理后的发送模板数据:', processedTemplates); // 调试信息\n        setSendTemplates(processedTemplates);\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个可发送的模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error) {\n      console.error('获取发送模板列表失败:', error);\n      if (showMessage) {\n        var _error$response3, _error$response3$data;\n        message.error(`❌ 获取发送模板列表失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n      }\n      setSendTemplates([]);\n    } finally {\n      setSendTemplatesLoading(false);\n    }\n  }, [sendTemplateDir]);\n\n  // 获取模板内容\n  const fetchTemplateContent = async templatePath => {\n    try {\n      const response = await cleanTemplateAPI.getTemplateContent(templatePath);\n      if (response.data && response.data.content) {\n        setTemplateContent(JSON.stringify(response.data.content, null, 2));\n        return response.data.content;\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('获取模板内容失败:', error);\n      message.error(`❌ 获取模板内容失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n    }\n    return null;\n  };\n\n  // 生成清洗模板\n  const generateTemplate = async values => {\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('results_file', `${resultDir}/${values.selected_result_file}`);\n      formData.append('output_folder', values.output_dir);\n      if (values.template_name) {\n        formData.append('template_name', values.template_name);\n      }\n      const response = await cleanTemplateAPI.generateTemplate(formData);\n      if (response.data) {\n        const {\n          template_path,\n          updated_thresholds\n        } = response.data;\n\n        // 显示详细的成功信息\n        message.success({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                marginBottom: 4\n              },\n              children: \"\\uD83C\\uDF89 \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u6210\\u529F\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: [\"\\uD83D\\uDCC1 \\u6587\\u4EF6\\u8DEF\\u5F84: \", template_path, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 41\n              }, this), \"\\uD83D\\uDD27 \\u66F4\\u65B0\\u9608\\u503C: \", updated_thresholds, \" \\u4E2A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this),\n          duration: 6 // 显示6秒\n        });\n\n        // 刷新模板列表\n        if (templateDir) {\n          fetchTemplates(true); // 显示刷新消息\n        }\n\n        // 设置最近生成的模板信息\n        setLastGeneratedTemplate({\n          path: template_path,\n          name: template_path.split('/').pop() || '未知模板',\n          time: new Date().toLocaleString()\n        });\n\n        // 重置表单\n        generateForm.resetFields();\n        setResultFiles([]); // 清空结果文件列表\n        setResultDir(''); // 清空目录输入\n      }\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('生成模板失败:', error);\n      message.error(`❌ 生成模板失败: ${((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新模板内容\n  const updateTemplate = async values => {\n    try {\n      const formData = new FormData();\n      formData.append('template_path', selectedTemplate);\n      formData.append('template_content', values.template_content);\n      const response = await cleanTemplateAPI.updateTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success('✅ 模板内容更新成功');\n        setEditModalVisible(false);\n        fetchTemplates(true); // 刷新模板列表\n      }\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('更新模板失败:', error);\n      message.error(`❌ 更新模板失败: ${((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || error.message}`);\n    }\n  };\n\n  // 发送模板\n  const sendTemplate = async values => {\n    console.log('发送模板参数:', values); // 调试信息\n\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('template_path', values.template_path);\n      formData.append('target_host', values.target_host);\n      formData.append('target_username', values.target_username);\n      formData.append('target_password', values.target_password);\n      formData.append('target_port', values.target_port.toString());\n      formData.append('target_path', values.target_path);\n      const response = await cleanTemplateAPI.sendTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                marginBottom: 4\n              },\n              children: \"\\uD83C\\uDF89 \\u6A21\\u677F\\u53D1\\u9001\\u6210\\u529F\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: [\"\\uD83D\\uDCC1 \\u6A21\\u677F: \", values.template_path.split('/').pop(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 63\n              }, this), \"\\uD83D\\uDDA5\\uFE0F \\u76EE\\u6807: \", values.target_host, \":\", values.target_port, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 66\n              }, this), \"\\uD83D\\uDCC2 \\u8DEF\\u5F84: \", values.target_path]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this),\n          duration: 6\n        });\n\n        // 重置表单，但保留选择模式\n        const currentMode = sendForm.getFieldValue('template_selection_mode');\n        sendForm.resetFields();\n        sendForm.setFieldsValue({\n          template_selection_mode: currentMode\n        });\n      }\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('发送模板失败:', error);\n      message.error(`❌ 发送模板失败: ${((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载模板\n  const downloadTemplate = async (templatePath, templateName) => {\n    console.log('下载模板，路径:', templatePath, '名称:', templateName); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    try {\n      const response = await cleanTemplateAPI.downloadTemplate(templatePath);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', templateName || 'template.json');\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n      message.success('✅ 模板下载成功');\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('下载模板失败:', error);\n      message.error(`❌ 下载模板失败: ${((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || error.message}`);\n    }\n  };\n\n  // 查看模板详情\n  const viewTemplate = async templatePath => {\n    console.log('查看模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setViewModalVisible(true);\n    }\n  };\n\n  // 编辑模板\n  const editTemplate = async templatePath => {\n    console.log('编辑模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setSelectedTemplate(templatePath);\n      updateForm.setFieldsValue({\n        template_content: templateContent\n      });\n      setEditModalVisible(true);\n    }\n  };\n\n  // 防抖hook\n  const useDebounce = (value, delay) => {\n    _s();\n    const [debouncedValue, setDebouncedValue] = useState(value);\n    useEffect(() => {\n      const handler = setTimeout(() => {\n        setDebouncedValue(value);\n      }, delay);\n      return () => {\n        clearTimeout(handler);\n      };\n    }, [value, delay]);\n    return debouncedValue;\n  };\n\n  // 防抖处理路径变化 - 增加延迟时间减少频繁请求\n  _s(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");\n  const debouncedResultDir = useDebounce(resultDir, 1500);\n  const debouncedTemplateDir = useDebounce(templateDir, 1500);\n  const debouncedSendTemplateDir = useDebounce(sendTemplateDir, 1500);\n\n  // 页面初始化时不自动获取数据，等待用户手动刷新\n  // useEffect(() => {\n  //   fetchResultFiles();\n  //   fetchTemplates();\n  // }, []);\n\n  // 防抖后的路径变化时静默获取数据（不显示消息）\n  useEffect(() => {\n    if (debouncedResultDir && debouncedResultDir.trim() !== '') {\n      fetchResultFiles(false); // 静默获取，不显示消息\n    } else {\n      setResultFiles([]); // 清空列表\n    }\n  }, [debouncedResultDir, fetchResultFiles]);\n  useEffect(() => {\n    if (debouncedTemplateDir && debouncedTemplateDir.trim() !== '') {\n      fetchTemplates(false); // 静默获取，不显示消息\n    } else {\n      setTemplates([]); // 清空列表\n    }\n  }, [debouncedTemplateDir, fetchTemplates]);\n  useEffect(() => {\n    if (debouncedSendTemplateDir && debouncedSendTemplateDir.trim() !== '') {\n      fetchSendTemplates(false); // 静默获取，不显示消息\n    } else {\n      setSendTemplates([]); // 清空列表\n    }\n  }, [debouncedSendTemplateDir, fetchSendTemplates]);\n\n  // 模板列表表格列定义\n  const templateColumns = [{\n    title: '模板名称',\n    dataIndex: 'template_name',\n    key: 'template_name',\n    render: (name, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.filename\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => {\n      console.log('表格行数据:', record); // 调试信息\n      return /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 21\n          }, this),\n          onClick: () => viewTemplate(record.template_path),\n          children: \"\\u67E5\\u770B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 21\n          }, this),\n          onClick: () => editTemplate(record.template_path),\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 21\n          }, this),\n          onClick: () => downloadTemplate(record.template_path, record.filename),\n          children: \"\\u4E0B\\u8F7D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u57FA\\u4E8E\\u6A21\\u578B\\u8BAD\\u7EC3\\u6216\\u9884\\u6D4B\\u7ED3\\u679C\\uFF0C\\u751F\\u6210\\u7279\\u5B9A\\u5BA2\\u6237\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u6A21\\u677F\\u914D\\u7F6E\\u6587\\u4EF6\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"1\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 29\n          }, this), \"\\u6A21\\u677F\\u751F\\u6210\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 23\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Form, {\n            form: generateForm,\n            layout: \"vertical\",\n            onFinish: generateTemplate,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7ED3\\u679C\\u6587\\u4EF6\\u76EE\\u5F55\",\n                  name: \"result_dir\",\n                  rules: [{\n                    required: true,\n                    message: '请输入结果文件目录'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(FolderOpenOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 31\n                    }, this),\n                    placeholder: \"\\u4F8B\\u5982: /data/output\",\n                    value: resultDir,\n                    onChange: e => setResultDir(e.target.value),\n                    addonAfter: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"small\",\n                      icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 33\n                      }, this),\n                      onClick: () => fetchResultFiles(true),\n                      children: \"\\u5237\\u65B0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u9009\\u62E9\\u7ED3\\u679C\\u6587\\u4EF6\",\n                  name: \"selected_result_file\",\n                  rules: [{\n                    required: true,\n                    message: '请选择结果文件'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: resultFiles.length === 0 ? \"请先输入目录路径并点击刷新\" : \"选择要生成模板的结果文件\",\n                    showSearch: true,\n                    notFoundContent: resultFiles.length === 0 ? \"请先输入目录路径并点击刷新获取文件列表\" : \"未找到匹配的文件\",\n                    filterOption: (input, option) => {\n                      var _option$children;\n                      return option === null || option === void 0 ? void 0 : (_option$children = option.children) === null || _option$children === void 0 ? void 0 : _option$children.toLowerCase().includes(input.toLowerCase());\n                    },\n                    children: resultFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                      value: file,\n                      children: file\n                    }, file, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u6A21\\u677F\\u8F93\\u51FA\\u76EE\\u5F55\",\n                  name: \"output_dir\",\n                  rules: [{\n                    required: true,\n                    message: '请输入输出目录'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u4F8B\\u5982: /data/output\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u6A21\\u677F\\u540D\\u79F0\\uFF08\\u53EF\\u9009\\uFF09\",\n                  name: \"template_name\",\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u4F8B\\u5982: customer_name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 25\n                }, this),\n                children: \"\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), lastGeneratedTemplate && /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              marginTop: 16,\n              borderColor: '#52c41a'\n            },\n            title: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#52c41a'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 21\n              }, this), \" \\u6700\\u8FD1\\u751F\\u6210\\u7684\\u6A21\\u677F\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 19\n            }, this),\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              type: \"text\",\n              onClick: () => setLastGeneratedTemplate(null),\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 19\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6A21\\u677F\\u540D\\u79F0\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  copyable: true,\n                  children: lastGeneratedTemplate.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u751F\\u6210\\u65F6\\u95F4\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: lastGeneratedTemplate.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                span: 24,\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6587\\u4EF6\\u8DEF\\u5F84\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  copyable: true,\n                  style: {\n                    fontSize: '12px',\n                    color: '#666'\n                  },\n                  children: lastGeneratedTemplate.path\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)\n      }, \"1\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 29\n          }, this), \"\\u6A21\\u677F\\u7BA1\\u7406\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 23\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6A21\\u677F\\u5217\\u8868\",\n          size: \"small\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\",\n              value: templateDir,\n              onChange: e => setTemplateDir(e.target.value),\n              style: {\n                width: 200\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 25\n              }, this),\n              onClick: () => fetchTemplates(true),\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this),\n          children: templates.length === 0 && !loading ? /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6682\\u65E0\\u6A21\\u677F\",\n            description: \"\\uD83D\\uDCC1 \\u8BF7\\u5148\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\\u5E76\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\\u83B7\\u53D6\\u6A21\\u677F\\u5217\\u8868\\uFF0C\\u6216\\u8005\\u5728\\u6A21\\u677F\\u751F\\u6210\\u9875\\u9762\\u521B\\u5EFA\\u65B0\\u6A21\\u677F\\u3002\",\n            type: \"info\",\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Table, {\n            columns: templateColumns,\n            dataSource: templates,\n            rowKey: \"template_path\",\n            loading: loading,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showTotal: total => `共 ${total} 个模板`\n            },\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)\n      }, \"2\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 29\n          }, this), \"\\u6A21\\u677F\\u53D1\\u9001\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 23\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u53D1\\u9001\\u6A21\\u677F\\u5230\\u76EE\\u6807\\u670D\\u52A1\\u5668\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: sendForm,\n            layout: \"vertical\",\n            onFinish: sendTemplate,\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u6A21\\u677F\\u76EE\\u5F55\",\n              name: \"send_template_directory\",\n              rules: [{\n                required: true,\n                message: '请输入模板目录路径'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF0C\\u4F8B\\u5982: /data/output\",\n                value: sendTemplateDir,\n                onChange: e => setSendTemplateDir(e.target.value),\n                suffix: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"text\",\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 29\n                  }, this),\n                  loading: sendTemplatesLoading,\n                  onClick: () => fetchSendTemplates(true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u9009\\u62E9\\u6A21\\u677F\",\n              name: \"template_path\",\n              rules: [{\n                required: true,\n                message: '请选择要发送的模板'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u5148\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\uFF0C\\u7136\\u540E\\u9009\\u62E9\\u8981\\u53D1\\u9001\\u7684\\u6A21\\u677F\\u6587\\u4EF6\",\n                showSearch: true,\n                loading: sendTemplatesLoading,\n                filterOption: (input, option) => {\n                  var _option$children2;\n                  return option === null || option === void 0 ? void 0 : (_option$children2 = option.children) === null || _option$children2 === void 0 ? void 0 : _option$children2.toLowerCase().includes(input.toLowerCase());\n                },\n                notFoundContent: sendTemplatesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center',\n                    padding: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: \"\\uD83D\\uDD04 \\u6B63\\u5728\\u52A0\\u8F7D\\u6A21\\u677F\\u5217\\u8868...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 23\n                }, this) : sendTemplates.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center',\n                    padding: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\uD83D\\uDCC1 \\u6682\\u65E0\\u53EF\\u7528\\u6A21\\u677F\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 36\n                    }, this), \"\\u8BF7\\u68C0\\u67E5\\u76EE\\u5F55\\u8DEF\\u5F84\\u6216\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 23\n                }, this) : null,\n                children: sendTemplates.map(template => /*#__PURE__*/_jsxDEV(Option, {\n                  value: template.template_path,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: template.template_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 767,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      style: {\n                        fontSize: '12px'\n                      },\n                      children: template.filename\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 768,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 23\n                  }, this)\n                }, template.template_path, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u76EE\\u6807\\u670D\\u52A1\\u5668\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u76EE\\u6807\\u4E3B\\u673A\",\n                  name: \"target_host\",\n                  rules: [{\n                    required: true,\n                    message: '请输入目标主机地址'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u4F8B\\u5982: *************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7AEF\\u53E3\",\n                  name: \"target_port\",\n                  initialValue: 22,\n                  rules: [{\n                    required: true,\n                    message: '请输入端口号'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    type: \"number\",\n                    placeholder: \"22\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7528\\u6237\\u540D\",\n                  name: \"target_username\",\n                  rules: [{\n                    required: true,\n                    message: '请输入用户名'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u4F8B\\u5982: root\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u5BC6\\u7801\",\n                  name: \"target_password\",\n                  rules: [{\n                    required: true,\n                    message: '请输入密码'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u76EE\\u6807\\u8DEF\\u5F84\",\n              name: \"target_path\",\n              rules: [{\n                required: true,\n                message: '请输入目标路径'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u4F8B\\u5982: /etc/cleantemplate/\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(CloudUploadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 25\n                }, this),\n                children: \"\\u53D1\\u9001\\u6A21\\u677F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)\n      }, \"3\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u6A21\\u677F\\u5185\\u5BB9\",\n      open: viewModalVisible,\n      onCancel: () => setViewModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setViewModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(TextArea, {\n        value: templateContent,\n        rows: 20,\n        readOnly: true,\n        style: {\n          fontFamily: 'monospace'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 857,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 846,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u7F16\\u8F91\\u6A21\\u677F\\u5185\\u5BB9\",\n      open: editModalVisible,\n      onCancel: () => setEditModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setEditModalVisible(false),\n        children: \"\\u53D6\\u6D88\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => updateForm.submit(),\n        children: \"\\u4FDD\\u5B58\"\n      }, \"save\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: updateForm,\n        onFinish: updateTemplate,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"template_content\",\n          rules: [{\n            required: true,\n            message: '模板内容不能为空'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 20,\n            style: {\n              fontFamily: 'monospace'\n            },\n            placeholder: \"\\u8BF7\\u8F93\\u5165JSON\\u683C\\u5F0F\\u7684\\u6A21\\u677F\\u5185\\u5BB9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 884,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n};\n_s2(CleanTemplatePage, \"9epss/hOV5z+iUzNXmIBWrFEzKQ=\", true, function () {\n  return [Form.useForm, Form.useForm, Form.useForm];\n});\n_c = CleanTemplatePage;\nexport default CleanTemplatePage;\nvar _c;\n$RefreshReg$(_c, \"CleanTemplatePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "<PERSON><PERSON>", "Tabs", "<PERSON><PERSON>", "Select", "Input", "Form", "message", "Table", "Space", "Modal", "Row", "Col", "Divider", "FileTextOutlined", "SettingOutlined", "SendOutlined", "ReloadOutlined", "EyeOutlined", "DownloadOutlined", "EditOutlined", "FolderOpenOutlined", "CloudUploadOutlined", "cleanTemplateAPI", "TextArea", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "Option", "CleanTemplatePage", "_s2", "_s", "$RefreshSig$", "loading", "setLoading", "generateForm", "useForm", "sendForm", "updateForm", "resultFiles", "setResultFiles", "resultDir", "setResultDir", "lastGeneratedTemplate", "setLastGeneratedTemplate", "templates", "setTemplates", "templateDir", "setTemplateDir", "selectedTemplate", "setSelectedTemplate", "templateContent", "setTemplateContent", "editModalVisible", "setEditModalVisible", "viewModalVisible", "setViewModalVisible", "sendTemplateDir", "setSendTemplateDir", "sendTemplates", "setSendTemplates", "sendTemplatesLoading", "setSendTemplatesLoading", "fetchResultFiles", "showMessage", "trim", "response", "listResultFiles", "data", "files", "length", "success", "info", "error", "console", "_error$response", "_error$response$data", "detail", "fetchTemplates", "listTemplates", "log", "templatesData", "processedTemplates", "Array", "isArray", "map", "item", "index", "template_name", "replace", "filename", "template_path", "file_size", "created_time", "modified_time", "_item$filename", "_error$response2", "_error$response2$data", "fetchSendTemplates", "_item$filename2", "_error$response3", "_error$response3$data", "fetchTemplateContent", "templatePath", "getTemplateContent", "content", "JSON", "stringify", "_error$response4", "_error$response4$data", "generateTemplate", "values", "formData", "FormData", "append", "selected_result_file", "output_dir", "updated_thresholds", "children", "style", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "duration", "path", "name", "split", "pop", "time", "Date", "toLocaleString", "resetFields", "_error$response5", "_error$response5$data", "updateTemplate", "template_content", "_error$response6", "_error$response6$data", "sendTemplate", "target_host", "target_username", "target_password", "target_port", "toString", "target_path", "currentMode", "getFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template_selection_mode", "_error$response7", "_error$response7$data", "downloadTemplate", "templateName", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "_error$response8", "_error$response8$data", "viewTemplate", "editTemplate", "useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "setTimeout", "clearTimeout", "debouncedResultDir", "debouncedTemplateDir", "debouncedSendTemplateDir", "templateColumns", "title", "dataIndex", "key", "render", "record", "strong", "type", "_", "size", "icon", "onClick", "level", "defaultActiveKey", "marginTop", "tab", "form", "layout", "onFinish", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "onChange", "e", "target", "addonAfter", "showSearch", "notFoundContent", "filterOption", "input", "option", "_option$children", "toLowerCase", "includes", "file", "htmlType", "borderColor", "extra", "copyable", "width", "description", "showIcon", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total", "suffix", "_option$children2", "textAlign", "padding", "template", "initialValue", "Password", "open", "onCancel", "footer", "rows", "readOnly", "fontFamily", "submit", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typo<PERSON>,\n  <PERSON>,\n  Alert,\n  Tabs,\n  Button,\n  Select,\n  Input,\n  Form,\n  message,\n  Table,\n  Space,\n  Modal,\n  Row,\n  Col,\n  Divider\n} from 'antd';\nimport {\n  FileTextOutlined,\n  SettingOutlined,\n  SendOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  DownloadOutlined,\n  EditOutlined,\n  FolderOpenOutlined,\n  CloudUploadOutlined\n} from '@ant-design/icons';\nimport { cleanTemplateAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { Option } = Select;\n\ninterface TemplateInfo {\n  template_name: string;\n  filename: string;\n  template_path: string;\n  file_size: number;\n  created_time: number;\n  modified_time: number;\n}\n\nconst CleanTemplatePage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [generateForm] = Form.useForm();\n  const [sendForm] = Form.useForm();\n  const [updateForm] = Form.useForm();\n\n  // 模板生成相关状态\n  const [resultFiles, setResultFiles] = useState<string[]>([]);\n  const [resultDir, setResultDir] = useState('');\n  const [lastGeneratedTemplate, setLastGeneratedTemplate] = useState<{\n    path: string;\n    name: string;\n    time: string;\n  } | null>(null);\n\n  // 模板管理相关状态\n  const [templates, setTemplates] = useState<TemplateInfo[]>([]);\n  const [templateDir, setTemplateDir] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('');\n  const [templateContent, setTemplateContent] = useState('');\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n\n  // 模板发送功能的状态\n  const [sendTemplateDir, setSendTemplateDir] = useState<string>('');\n  const [sendTemplates, setSendTemplates] = useState<TemplateInfo[]>([]);\n  const [sendTemplatesLoading, setSendTemplatesLoading] = useState(false);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async (showMessage = true) => {\n    if (!resultDir.trim()) {\n      setResultFiles([]);\n      return;\n    }\n\n    try {\n      const response = await cleanTemplateAPI.listResultFiles(resultDir);\n      if (response.data.files) {\n        setResultFiles(response.data.files || []);\n        if (showMessage && response.data.files.length > 0) {\n          message.success(`📁 找到 ${response.data.files.length} 个结果文件`);\n        } else if (showMessage && response.data.files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取结果文件失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取结果文件失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setResultFiles([]);\n    }\n  }, [resultDir]);\n\n  // 获取模板列表\n  const fetchTemplates = useCallback(async (showMessage = true) => {\n    if (!templateDir.trim()) {\n      setTemplates([]);\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(templateDir);\n      console.log('模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${templateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，直接使用\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${templateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的模板数据:', processedTemplates); // 调试信息\n        setTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [templateDir]);\n\n  // 获取发送模板列表\n  const fetchSendTemplates = useCallback(async (showMessage = true) => {\n    if (!sendTemplateDir.trim()) {\n      setSendTemplates([]);\n      return;\n    }\n\n    setSendTemplatesLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(sendTemplateDir);\n      console.log('发送模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('发送模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${sendTemplateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，确保所有必需字段存在\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${sendTemplateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的发送模板数据:', processedTemplates); // 调试信息\n        setSendTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个可发送的模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取发送模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取发送模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setSendTemplates([]);\n    } finally {\n      setSendTemplatesLoading(false);\n    }\n  }, [sendTemplateDir]);\n\n  // 获取模板内容\n  const fetchTemplateContent = async (templatePath: string) => {\n    try {\n      const response = await cleanTemplateAPI.getTemplateContent(templatePath);\n      if (response.data && response.data.content) {\n        setTemplateContent(JSON.stringify(response.data.content, null, 2));\n        return response.data.content;\n      }\n    } catch (error: any) {\n      console.error('获取模板内容失败:', error);\n      message.error(`❌ 获取模板内容失败: ${error.response?.data?.detail || error.message}`);\n    }\n    return null;\n  };\n\n  // 生成清洗模板\n  const generateTemplate = async (values: any) => {\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('results_file', `${resultDir}/${values.selected_result_file}`);\n      formData.append('output_folder', values.output_dir);\n      if (values.template_name) {\n        formData.append('template_name', values.template_name);\n      }\n\n      const response = await cleanTemplateAPI.generateTemplate(formData);\n      if (response.data) {\n        const { template_path, updated_thresholds } = response.data;\n\n        // 显示详细的成功信息\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 清洗模板生成成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 文件路径: {template_path}<br/>\n                🔧 更新阈值: {updated_thresholds} 个\n              </div>\n            </div>\n          ),\n          duration: 6, // 显示6秒\n        });\n\n        // 刷新模板列表\n        if (templateDir) {\n          fetchTemplates(true); // 显示刷新消息\n        }\n\n        // 设置最近生成的模板信息\n        setLastGeneratedTemplate({\n          path: template_path,\n          name: template_path.split('/').pop() || '未知模板',\n          time: new Date().toLocaleString()\n        });\n\n        // 重置表单\n        generateForm.resetFields();\n        setResultFiles([]); // 清空结果文件列表\n        setResultDir(''); // 清空目录输入\n      }\n    } catch (error: any) {\n      console.error('生成模板失败:', error);\n      message.error(`❌ 生成模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新模板内容\n  const updateTemplate = async (values: any) => {\n    try {\n      const formData = new FormData();\n      formData.append('template_path', selectedTemplate);\n      formData.append('template_content', values.template_content);\n\n      const response = await cleanTemplateAPI.updateTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success('✅ 模板内容更新成功');\n        setEditModalVisible(false);\n        fetchTemplates(true); // 刷新模板列表\n      }\n    } catch (error: any) {\n      console.error('更新模板失败:', error);\n      message.error(`❌ 更新模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 发送模板\n  const sendTemplate = async (values: any) => {\n    console.log('发送模板参数:', values); // 调试信息\n\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('template_path', values.template_path);\n      formData.append('target_host', values.target_host);\n      formData.append('target_username', values.target_username);\n      formData.append('target_password', values.target_password);\n      formData.append('target_port', values.target_port.toString());\n      formData.append('target_path', values.target_path);\n\n      const response = await cleanTemplateAPI.sendTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 模板发送成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 模板: {values.template_path.split('/').pop()}<br/>\n                🖥️ 目标: {values.target_host}:{values.target_port}<br/>\n                📂 路径: {values.target_path}\n              </div>\n            </div>\n          ),\n          duration: 6,\n        });\n\n        // 重置表单，但保留选择模式\n        const currentMode = sendForm.getFieldValue('template_selection_mode');\n        sendForm.resetFields();\n        sendForm.setFieldsValue({ template_selection_mode: currentMode });\n      }\n    } catch (error: any) {\n      console.error('发送模板失败:', error);\n      message.error(`❌ 发送模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载模板\n  const downloadTemplate = async (templatePath: string, templateName: string) => {\n    console.log('下载模板，路径:', templatePath, '名称:', templateName); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    try {\n      const response = await cleanTemplateAPI.downloadTemplate(templatePath);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', templateName || 'template.json');\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success('✅ 模板下载成功');\n    } catch (error: any) {\n      console.error('下载模板失败:', error);\n      message.error(`❌ 下载模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 查看模板详情\n  const viewTemplate = async (templatePath: string) => {\n    console.log('查看模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setViewModalVisible(true);\n    }\n  };\n\n  // 编辑模板\n  const editTemplate = async (templatePath: string) => {\n    console.log('编辑模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setSelectedTemplate(templatePath);\n      updateForm.setFieldsValue({ template_content: templateContent });\n      setEditModalVisible(true);\n    }\n  };\n\n  // 防抖hook\n  const useDebounce = (value: string, delay: number) => {\n    const [debouncedValue, setDebouncedValue] = useState(value);\n\n    useEffect(() => {\n      const handler = setTimeout(() => {\n        setDebouncedValue(value);\n      }, delay);\n\n      return () => {\n        clearTimeout(handler);\n      };\n    }, [value, delay]);\n\n    return debouncedValue;\n  };\n\n  // 防抖处理路径变化 - 增加延迟时间减少频繁请求\n  const debouncedResultDir = useDebounce(resultDir, 1500);\n  const debouncedTemplateDir = useDebounce(templateDir, 1500);\n  const debouncedSendTemplateDir = useDebounce(sendTemplateDir, 1500);\n\n  // 页面初始化时不自动获取数据，等待用户手动刷新\n  // useEffect(() => {\n  //   fetchResultFiles();\n  //   fetchTemplates();\n  // }, []);\n\n  // 防抖后的路径变化时静默获取数据（不显示消息）\n  useEffect(() => {\n    if (debouncedResultDir && debouncedResultDir.trim() !== '') {\n      fetchResultFiles(false); // 静默获取，不显示消息\n    } else {\n      setResultFiles([]); // 清空列表\n    }\n  }, [debouncedResultDir, fetchResultFiles]);\n\n  useEffect(() => {\n    if (debouncedTemplateDir && debouncedTemplateDir.trim() !== '') {\n      fetchTemplates(false); // 静默获取，不显示消息\n    } else {\n      setTemplates([]); // 清空列表\n    }\n  }, [debouncedTemplateDir, fetchTemplates]);\n\n  useEffect(() => {\n    if (debouncedSendTemplateDir && debouncedSendTemplateDir.trim() !== '') {\n      fetchSendTemplates(false); // 静默获取，不显示消息\n    } else {\n      setSendTemplates([]); // 清空列表\n    }\n  }, [debouncedSendTemplateDir, fetchSendTemplates]);\n\n  // 模板列表表格列定义\n  const templateColumns = [\n    {\n      title: '模板名称',\n      dataIndex: 'template_name',\n      key: 'template_name',\n      render: (name: string, record: TemplateInfo) => (\n        <div>\n          <Text strong>{name}</Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.filename}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: TemplateInfo) => {\n        console.log('表格行数据:', record); // 调试信息\n        return (\n          <Space>\n            <Button\n              type=\"primary\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => viewTemplate(record.template_path)}\n            >\n              查看\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => editTemplate(record.template_path)}\n            >\n              编辑\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<DownloadOutlined />}\n              onClick={() => downloadTemplate(record.template_path, record.filename)}\n            >\n              下载\n            </Button>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>清洗模板生成</Title>\n      <Text type=\"secondary\">\n        基于模型训练或预测结果，生成特定客户的流量清洗模板配置文件。\n      </Text>\n\n      <Tabs defaultActiveKey=\"1\" style={{ marginTop: 24 }}>\n        <TabPane tab={<span><FileTextOutlined />模板生成</span>} key=\"1\">\n          <Card title=\"生成清洗模板\" size=\"small\">\n            <Form\n              form={generateForm}\n              layout=\"vertical\"\n              onFinish={generateTemplate}\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"结果文件目录\"\n                    name=\"result_dir\"\n                    rules={[{ required: true, message: '请输入结果文件目录' }]}\n                  >\n                    <Input\n                      prefix={<FolderOpenOutlined />}\n                      placeholder=\"例如: /data/output\"\n                      value={resultDir}\n                      onChange={(e) => setResultDir(e.target.value)}\n                      addonAfter={\n                        <Button\n                          size=\"small\"\n                          icon={<ReloadOutlined />}\n                          onClick={() => fetchResultFiles(true)}\n                        >\n                          刷新\n                        </Button>\n                      }\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"选择结果文件\"\n                    name=\"selected_result_file\"\n                    rules={[{ required: true, message: '请选择结果文件' }]}\n                  >\n                    <Select\n                      placeholder={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新\" : \"选择要生成模板的结果文件\"}\n                      showSearch\n                      notFoundContent={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新获取文件列表\" : \"未找到匹配的文件\"}\n                      filterOption={(input, option) =>\n                        (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                      }\n                    >\n                      {resultFiles.map(file => (\n                        <Option key={file} value={file}>\n                          {file}\n                        </Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板输出目录\"\n                    name=\"output_dir\"\n                    rules={[{ required: true, message: '请输入输出目录' }]}\n                  >\n                    <Input placeholder=\"例如: /data/output\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板名称（可选）\"\n                    name=\"template_name\"\n                  >\n                    <Input placeholder=\"例如: customer_name\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<FileTextOutlined />}\n                >\n                  生成清洗模板\n                </Button>\n              </Form.Item>\n            </Form>\n\n            {/* 显示最近生成的模板信息 */}\n            {lastGeneratedTemplate && (\n              <Card\n                size=\"small\"\n                style={{ marginTop: 16, borderColor: '#52c41a' }}\n                title={\n                  <span style={{ color: '#52c41a' }}>\n                    <FileTextOutlined /> 最近生成的模板\n                  </span>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    type=\"text\"\n                    onClick={() => setLastGeneratedTemplate(null)}\n                  >\n                    ×\n                  </Button>\n                }\n              >\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Text strong>模板名称：</Text>\n                    <Text copyable>{lastGeneratedTemplate.name}</Text>\n                  </Col>\n                  <Col span={12}>\n                    <Text strong>生成时间：</Text>\n                    <Text>{lastGeneratedTemplate.time}</Text>\n                  </Col>\n                </Row>\n                <Row style={{ marginTop: 8 }}>\n                  <Col span={24}>\n                    <Text strong>文件路径：</Text>\n                    <Text copyable style={{ fontSize: '12px', color: '#666' }}>\n                      {lastGeneratedTemplate.path}\n                    </Text>\n                  </Col>\n                </Row>\n              </Card>\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SettingOutlined />模板管理</span>} key=\"2\">\n          <Card\n            title=\"模板列表\"\n            size=\"small\"\n            extra={\n              <Space>\n                <Input\n                  placeholder=\"模板目录路径\"\n                  value={templateDir}\n                  onChange={(e) => setTemplateDir(e.target.value)}\n                  style={{ width: 200 }}\n                />\n                <Button\n                  icon={<ReloadOutlined />}\n                  onClick={() => fetchTemplates(true)}\n                  loading={loading}\n                >\n                  刷新\n                </Button>\n              </Space>\n            }\n          >\n            {templates.length === 0 && !loading ? (\n              <Alert\n                message=\"暂无模板\"\n                description=\"📁 请先输入模板目录路径并点击刷新按钮获取模板列表，或者在模板生成页面创建新模板。\"\n                type=\"info\"\n                showIcon\n              />\n            ) : (\n              <Table\n                columns={templateColumns}\n                dataSource={templates}\n                rowKey=\"template_path\"\n                loading={loading}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: (total) => `共 ${total} 个模板`,\n                }}\n                size=\"small\"\n              />\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SendOutlined />模板发送</span>} key=\"3\">\n          <Card title=\"发送模板到目标服务器\" size=\"small\">\n            <Form\n              form={sendForm}\n              layout=\"vertical\"\n              onFinish={sendTemplate}\n            >\n              <Form.Item\n                label=\"模板目录\"\n                name=\"send_template_directory\"\n                rules={[{ required: true, message: '请输入模板目录路径' }]}\n              >\n                <Input\n                  placeholder=\"请输入模板目录路径，例如: /data/output\"\n                  value={sendTemplateDir}\n                  onChange={(e) => setSendTemplateDir(e.target.value)}\n                  suffix={\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      loading={sendTemplatesLoading}\n                      onClick={() => fetchSendTemplates(true)}\n                    />\n                  }\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"选择模板\"\n                name=\"template_path\"\n                rules={[{ required: true, message: '请选择要发送的模板' }]}\n              >\n                <Select\n                  placeholder=\"请先输入模板目录，然后选择要发送的模板文件\"\n                  showSearch\n                  loading={sendTemplatesLoading}\n                  filterOption={(input, option) =>\n                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                  }\n                  notFoundContent={\n                    sendTemplatesLoading ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">🔄 正在加载模板列表...</Text>\n                      </div>\n                    ) : sendTemplates.length === 0 ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">\n                          📁 暂无可用模板<br/>\n                          请检查目录路径或点击刷新按钮\n                        </Text>\n                      </div>\n                    ) : null\n                  }\n                >\n                  {sendTemplates.map(template => (\n                    <Option key={template.template_path} value={template.template_path}>\n                      <div>\n                        <Text strong>{template.template_name}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {template.filename}\n                        </Text>\n                      </div>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Divider>目标服务器配置</Divider>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"目标主机\"\n                    name=\"target_host\"\n                    rules={[{ required: true, message: '请输入目标主机地址' }]}\n                  >\n                    <Input placeholder=\"例如: *************\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"端口\"\n                    name=\"target_port\"\n                    initialValue={22}\n                    rules={[{ required: true, message: '请输入端口号' }]}\n                  >\n                    <Input type=\"number\" placeholder=\"22\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"用户名\"\n                    name=\"target_username\"\n                    rules={[{ required: true, message: '请输入用户名' }]}\n                  >\n                    <Input placeholder=\"例如: root\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"密码\"\n                    name=\"target_password\"\n                    rules={[{ required: true, message: '请输入密码' }]}\n                  >\n                    <Input.Password placeholder=\"请输入密码\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item\n                label=\"目标路径\"\n                name=\"target_path\"\n                rules={[{ required: true, message: '请输入目标路径' }]}\n              >\n                <Input placeholder=\"例如: /etc/cleantemplate/\" />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<CloudUploadOutlined />}\n                >\n                  发送模板\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </TabPane>\n      </Tabs>\n\n      {/* 查看模板内容弹窗 */}\n      <Modal\n        title=\"模板内容\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        <TextArea\n          value={templateContent}\n          rows={20}\n          readOnly\n          style={{ fontFamily: 'monospace' }}\n        />\n      </Modal>\n\n      {/* 编辑模板内容弹窗 */}\n      <Modal\n        title=\"编辑模板内容\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setEditModalVisible(false)}>\n            取消\n          </Button>,\n          <Button key=\"save\" type=\"primary\" onClick={() => updateForm.submit()}>\n            保存\n          </Button>\n        ]}\n        width={800}\n      >\n        <Form\n          form={updateForm}\n          onFinish={updateTemplate}\n        >\n          <Form.Item\n            name=\"template_content\"\n            rules={[{ required: true, message: '模板内容不能为空' }]}\n          >\n            <TextArea\n              rows={20}\n              style={{ fontFamily: 'monospace' }}\n              placeholder=\"请输入JSON格式的模板内容\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CleanTemplatePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,OAAO,QACF,MAAM;AACb,SACEC,gBAAgB,EAChBC,eAAe,EACfC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,gBAAgB,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,QACd,mBAAmB;AAC1B,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG7B,UAAU;AAClC,MAAM;EAAE8B;AAAQ,CAAC,GAAG3B,IAAI;AACxB,MAAM;EAAE4B;AAAO,CAAC,GAAG1B,MAAM;AAWzB,MAAM2B,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,YAAY,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EACrC,MAAM,CAACC,QAAQ,CAAC,GAAGjC,IAAI,CAACgC,OAAO,CAAC,CAAC;EACjC,MAAM,CAACE,UAAU,CAAC,GAAGlC,IAAI,CAACgC,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlD,QAAQ,CAIxD,IAAI,CAAC;;EAEf;EACA,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAiB,EAAE,CAAC;EAC9D,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACmE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAMqE,gBAAgB,GAAGnE,WAAW,CAAC,OAAOoE,WAAW,GAAG,IAAI,KAAK;IACjE,IAAI,CAACvB,SAAS,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrBzB,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEA,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAM7C,gBAAgB,CAAC8C,eAAe,CAAC1B,SAAS,CAAC;MAClE,IAAIyB,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAE;QACvB7B,cAAc,CAAC0B,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;QACzC,IAAIL,WAAW,IAAIE,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;UACjDjE,OAAO,CAACkE,OAAO,CAAC,SAASL,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,MAAM,QAAQ,CAAC;QAC9D,CAAC,MAAM,IAAIN,WAAW,IAAIE,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;UAC1DjE,OAAO,CAACmE,IAAI,CAAC,eAAe,CAAC;QAC/B;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIT,WAAW,EAAE;QAAA,IAAAW,eAAA,EAAAC,oBAAA;QACfvE,OAAO,CAACoE,KAAK,CAAC,eAAe,EAAAE,eAAA,GAAAF,KAAK,CAACP,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;MAC/E;MACAmC,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,EAAE,CAACC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMqC,cAAc,GAAGlF,WAAW,CAAC,OAAOoE,WAAW,GAAG,IAAI,KAAK;IAC/D,IAAI,CAACjB,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;MACvBnB,YAAY,CAAC,EAAE,CAAC;MAChB;IACF;IAEAZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAM7C,gBAAgB,CAAC0D,aAAa,CAAChC,WAAW,CAAC;MAClE2B,OAAO,CAACM,GAAG,CAAC,YAAY,EAAEd,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;;MAE1C,IAAIF,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACvB,SAAS,EAAE;QAC5C,MAAMoC,aAAa,GAAGf,QAAQ,CAACE,IAAI,CAACvB,SAAS;QAC7C6B,OAAO,CAACM,GAAG,CAAC,OAAO,EAAEC,aAAa,CAAC,CAAC,CAAC;;QAErC;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAC3B,IAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,EAAE;UAChCC,kBAAkB,GAAGD,aAAa,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACtD,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;cAC5B;cACA,OAAO;gBACLE,aAAa,EAAEF,IAAI,CAACG,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;gBACtDC,QAAQ,EAAEJ,IAAI;gBACdK,aAAa,EAAE,GAAG5C,WAAW,IAAIuC,IAAI,EAAE;gBACvCM,SAAS,EAAE,CAAC;gBACZC,YAAY,EAAE,CAAC;gBACfC,aAAa,EAAE;cACjB,CAAC;YACH,CAAC,MAAM,IAAI,OAAOR,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;cAAA,IAAAS,cAAA;cACpD;cACA,OAAO;gBACLP,aAAa,EAAEF,IAAI,CAACE,aAAa,MAAAO,cAAA,GAAIT,IAAI,CAACI,QAAQ,cAAAK,cAAA,uBAAbA,cAAA,CAAeN,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,KAAI,KAAKF,KAAK,GAAG,CAAC,EAAE;gBAC1GG,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACE,aAAa,IAAI,WAAWD,KAAK,GAAG,CAAC,OAAO;gBAC5EI,aAAa,EAAEL,IAAI,CAACK,aAAa,IAAI,GAAG5C,WAAW,IAAIuC,IAAI,CAACI,QAAQ,EAAE;gBACtEE,SAAS,EAAEN,IAAI,CAACM,SAAS,IAAI,CAAC;gBAC9BC,YAAY,EAAEP,IAAI,CAACO,YAAY,IAAI,CAAC;gBACpCC,aAAa,EAAER,IAAI,CAACQ,aAAa,IAAI;cACvC,CAAC;YACH;YACA,OAAOR,IAAI;UACb,CAAC,CAAC;QACJ;QAEAZ,OAAO,CAACM,GAAG,CAAC,WAAW,EAAEE,kBAAkB,CAAC,CAAC,CAAC;QAC9CpC,YAAY,CAACoC,kBAAkB,CAAC;QAEhC,IAAIlB,WAAW,IAAIkB,kBAAkB,CAACZ,MAAM,GAAG,CAAC,EAAE;UAChDjE,OAAO,CAACkE,OAAO,CAAC,SAASW,kBAAkB,CAACZ,MAAM,QAAQ,CAAC;QAC7D,CAAC,MAAM,IAAIN,WAAW,IAAIkB,kBAAkB,CAACZ,MAAM,KAAK,CAAC,EAAE;UACzDjE,OAAO,CAACmE,IAAI,CAAC,eAAe,CAAC;QAC/B;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIT,WAAW,EAAE;QAAA,IAAAgC,gBAAA,EAAAC,qBAAA;QACf5F,OAAO,CAACoE,KAAK,CAAC,eAAe,EAAAuB,gBAAA,GAAAvB,KAAK,CAACP,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBpB,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;MAC/E;MACAyC,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACa,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMmD,kBAAkB,GAAGtG,WAAW,CAAC,OAAOoE,WAAW,GAAG,IAAI,KAAK;IACnE,IAAI,CAACP,eAAe,CAACQ,IAAI,CAAC,CAAC,EAAE;MAC3BL,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM7C,gBAAgB,CAAC0D,aAAa,CAACtB,eAAe,CAAC;MACtEiB,OAAO,CAACM,GAAG,CAAC,cAAc,EAAEd,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;;MAE5C,IAAIF,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACvB,SAAS,EAAE;QAC5C,MAAMoC,aAAa,GAAGf,QAAQ,CAACE,IAAI,CAACvB,SAAS;QAC7C6B,OAAO,CAACM,GAAG,CAAC,SAAS,EAAEC,aAAa,CAAC,CAAC,CAAC;;QAEvC;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAC3B,IAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,EAAE;UAChCC,kBAAkB,GAAGD,aAAa,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACtD,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;cAC5B;cACA,OAAO;gBACLE,aAAa,EAAEF,IAAI,CAACG,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;gBACtDC,QAAQ,EAAEJ,IAAI;gBACdK,aAAa,EAAE,GAAGlC,eAAe,IAAI6B,IAAI,EAAE;gBAC3CM,SAAS,EAAE,CAAC;gBACZC,YAAY,EAAE,CAAC;gBACfC,aAAa,EAAE;cACjB,CAAC;YACH,CAAC,MAAM,IAAI,OAAOR,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;cAAA,IAAAa,eAAA;cACpD;cACA,OAAO;gBACLX,aAAa,EAAEF,IAAI,CAACE,aAAa,MAAAW,eAAA,GAAIb,IAAI,CAACI,QAAQ,cAAAS,eAAA,uBAAbA,eAAA,CAAeV,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,KAAI,KAAKF,KAAK,GAAG,CAAC,EAAE;gBAC1GG,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACE,aAAa,IAAI,WAAWD,KAAK,GAAG,CAAC,OAAO;gBAC5EI,aAAa,EAAEL,IAAI,CAACK,aAAa,IAAI,GAAGlC,eAAe,IAAI6B,IAAI,CAACI,QAAQ,EAAE;gBAC1EE,SAAS,EAAEN,IAAI,CAACM,SAAS,IAAI,CAAC;gBAC9BC,YAAY,EAAEP,IAAI,CAACO,YAAY,IAAI,CAAC;gBACpCC,aAAa,EAAER,IAAI,CAACQ,aAAa,IAAI;cACvC,CAAC;YACH;YACA,OAAOR,IAAI;UACb,CAAC,CAAC;QACJ;QAEAZ,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEE,kBAAkB,CAAC,CAAC,CAAC;QAChDtB,gBAAgB,CAACsB,kBAAkB,CAAC;QAEpC,IAAIlB,WAAW,IAAIkB,kBAAkB,CAACZ,MAAM,GAAG,CAAC,EAAE;UAChDjE,OAAO,CAACkE,OAAO,CAAC,SAASW,kBAAkB,CAACZ,MAAM,YAAY,CAAC;QACjE,CAAC,MAAM,IAAIN,WAAW,IAAIkB,kBAAkB,CAACZ,MAAM,KAAK,CAAC,EAAE;UACzDjE,OAAO,CAACmE,IAAI,CAAC,eAAe,CAAC;QAC/B;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,IAAIT,WAAW,EAAE;QAAA,IAAAoC,gBAAA,EAAAC,qBAAA;QACfhG,OAAO,CAACoE,KAAK,CAAC,iBAAiB,EAAA2B,gBAAA,GAAA3B,KAAK,CAACP,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBxB,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;MACjF;MACAuD,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRE,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC,EAAE,CAACL,eAAe,CAAC,CAAC;;EAErB;EACA,MAAM6C,oBAAoB,GAAG,MAAOC,YAAoB,IAAK;IAC3D,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAM7C,gBAAgB,CAACmF,kBAAkB,CAACD,YAAY,CAAC;MACxE,IAAIrC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACqC,OAAO,EAAE;QAC1CrD,kBAAkB,CAACsD,IAAI,CAACC,SAAS,CAACzC,QAAQ,CAACE,IAAI,CAACqC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAClE,OAAOvC,QAAQ,CAACE,IAAI,CAACqC,OAAO;MAC9B;IACF,CAAC,CAAC,OAAOhC,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnBnC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpE,OAAO,CAACoE,KAAK,CAAC,eAAe,EAAAmC,gBAAA,GAAAnC,KAAK,CAACP,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBhC,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;IAC/E;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMyG,gBAAgB,GAAG,MAAOC,MAAW,IAAK;IAC9C7E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8E,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE,GAAGzE,SAAS,IAAIsE,MAAM,CAACI,oBAAoB,EAAE,CAAC;MAC9EH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,MAAM,CAACK,UAAU,CAAC;MACnD,IAAIL,MAAM,CAACvB,aAAa,EAAE;QACxBwB,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,MAAM,CAACvB,aAAa,CAAC;MACxD;MAEA,MAAMtB,QAAQ,GAAG,MAAM7C,gBAAgB,CAACyF,gBAAgB,CAACE,QAAQ,CAAC;MAClE,IAAI9C,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAM;UAAEuB,aAAa;UAAE0B;QAAmB,CAAC,GAAGnD,QAAQ,CAACE,IAAI;;QAE3D;QACA/D,OAAO,CAACkE,OAAO,CAAC;UACdkC,OAAO,eACLjF,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAErD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrG,OAAA;cAAK+F,KAAK,EAAE;gBAAEO,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,yCACtC,EAAC3B,aAAa,eAACnE,OAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,2CACpB,EAACR,kBAAkB,EAAC,SAC/B;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDG,QAAQ,EAAE,CAAC,CAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIjF,WAAW,EAAE;UACf+B,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QACxB;;QAEA;QACAlC,wBAAwB,CAAC;UACvBqF,IAAI,EAAEtC,aAAa;UACnBuC,IAAI,EAAEvC,aAAa,CAACwC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAI,MAAM;UAC9CC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC;QAClC,CAAC,CAAC;;QAEF;QACApG,YAAY,CAACqG,WAAW,CAAC,CAAC;QAC1BhG,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;QACpBE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAO+B,KAAU,EAAE;MAAA,IAAAgE,gBAAA,EAAAC,qBAAA;MACnBhE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,aAAa,EAAAgE,gBAAA,GAAAhE,KAAK,CAACP,QAAQ,cAAAuE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrE,IAAI,cAAAsE,qBAAA,uBAApBA,qBAAA,CAAsB7D,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACR6B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyG,cAAc,GAAG,MAAO5B,MAAW,IAAK;IAC5C,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEjE,gBAAgB,CAAC;MAClD+D,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAEH,MAAM,CAAC6B,gBAAgB,CAAC;MAE5D,MAAM1E,QAAQ,GAAG,MAAM7C,gBAAgB,CAACsH,cAAc,CAAC3B,QAAQ,CAAC;MAChE,IAAI9C,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC/D,OAAO,EAAE;QAC1CA,OAAO,CAACkE,OAAO,CAAC,YAAY,CAAC;QAC7BjB,mBAAmB,CAAC,KAAK,CAAC;QAC1BwB,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;MACxB;IACF,CAAC,CAAC,OAAOL,KAAU,EAAE;MAAA,IAAAoE,gBAAA,EAAAC,qBAAA;MACnBpE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,aAAa,EAAAoE,gBAAA,GAAApE,KAAK,CAACP,QAAQ,cAAA2E,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzE,IAAI,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBjE,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;IAC7E;EACF,CAAC;;EAED;EACA,MAAM0I,YAAY,GAAG,MAAOhC,MAAW,IAAK;IAC1CrC,OAAO,CAACM,GAAG,CAAC,SAAS,EAAE+B,MAAM,CAAC,CAAC,CAAC;;IAEhC7E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8E,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,MAAM,CAACpB,aAAa,CAAC;MACtDqB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,MAAM,CAACiC,WAAW,CAAC;MAClDhC,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEH,MAAM,CAACkC,eAAe,CAAC;MAC1DjC,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEH,MAAM,CAACmC,eAAe,CAAC;MAC1DlC,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,MAAM,CAACoC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC7DpC,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,MAAM,CAACsC,WAAW,CAAC;MAElD,MAAMnF,QAAQ,GAAG,MAAM7C,gBAAgB,CAAC0H,YAAY,CAAC/B,QAAQ,CAAC;MAC9D,IAAI9C,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC/D,OAAO,EAAE;QAC1CA,OAAO,CAACkE,OAAO,CAAC;UACdkC,OAAO,eACLjF,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAErD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrG,OAAA;cAAK+F,KAAK,EAAE;gBAAEO,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,6BACxC,EAACP,MAAM,CAACpB,aAAa,CAACwC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,eAAC5G,OAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qCAC3C,EAACd,MAAM,CAACiC,WAAW,EAAC,GAAC,EAACjC,MAAM,CAACoC,WAAW,eAAC3H,OAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,+BAC/C,EAACd,MAAM,CAACsC,WAAW;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDG,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACA,MAAMsB,WAAW,GAAGjH,QAAQ,CAACkH,aAAa,CAAC,yBAAyB,CAAC;QACrElH,QAAQ,CAACmG,WAAW,CAAC,CAAC;QACtBnG,QAAQ,CAACmH,cAAc,CAAC;UAAEC,uBAAuB,EAAEH;QAAY,CAAC,CAAC;MACnE;IACF,CAAC,CAAC,OAAO7E,KAAU,EAAE;MAAA,IAAAiF,gBAAA,EAAAC,qBAAA;MACnBjF,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,aAAa,EAAAiF,gBAAA,GAAAjF,KAAK,CAACP,QAAQ,cAAAwF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtF,IAAI,cAAAuF,qBAAA,uBAApBA,qBAAA,CAAsB9E,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACR6B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0H,gBAAgB,GAAG,MAAAA,CAAOrD,YAAoB,EAAEsD,YAAoB,KAAK;IAC7EnF,OAAO,CAACM,GAAG,CAAC,UAAU,EAAEuB,YAAY,EAAE,KAAK,EAAEsD,YAAY,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACtD,YAAY,IAAIA,YAAY,KAAK,WAAW,EAAE;MACjDlG,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IACA,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAM7C,gBAAgB,CAACuI,gBAAgB,CAACrD,YAAY,CAAC;;MAEtE;MACA,MAAMuD,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAChG,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjE,MAAM+F,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAEV,YAAY,IAAI,eAAe,CAAC;MAC9DO,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZP,IAAI,CAACQ,MAAM,CAAC,CAAC;MACbZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC;MAE/BzJ,OAAO,CAACkE,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAU,EAAE;MAAA,IAAAoG,gBAAA,EAAAC,qBAAA;MACnBpG,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,aAAa,EAAAoG,gBAAA,GAAApG,KAAK,CAACP,QAAQ,cAAA2G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzG,IAAI,cAAA0G,qBAAA,uBAApBA,qBAAA,CAAsBjG,MAAM,KAAIJ,KAAK,CAACpE,OAAO,EAAE,CAAC;IAC7E;EACF,CAAC;;EAED;EACA,MAAM0K,YAAY,GAAG,MAAOxE,YAAoB,IAAK;IACnD7B,OAAO,CAACM,GAAG,CAAC,UAAU,EAAEuB,YAAY,CAAC,CAAC,CAAC;IACvC,IAAI,CAACA,YAAY,IAAIA,YAAY,KAAK,WAAW,EAAE;MACjDlG,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IACA,MAAMgC,OAAO,GAAG,MAAMH,oBAAoB,CAACC,YAAY,CAAC;IACxD,IAAIE,OAAO,EAAE;MACXjD,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMwH,YAAY,GAAG,MAAOzE,YAAoB,IAAK;IACnD7B,OAAO,CAACM,GAAG,CAAC,UAAU,EAAEuB,YAAY,CAAC,CAAC,CAAC;IACvC,IAAI,CAACA,YAAY,IAAIA,YAAY,KAAK,WAAW,EAAE;MACjDlG,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IACA,MAAMgC,OAAO,GAAG,MAAMH,oBAAoB,CAACC,YAAY,CAAC;IACxD,IAAIE,OAAO,EAAE;MACXvD,mBAAmB,CAACqD,YAAY,CAAC;MACjCjE,UAAU,CAACkH,cAAc,CAAC;QAAEZ,gBAAgB,EAAEzF;MAAgB,CAAC,CAAC;MAChEG,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM2H,WAAW,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAAK;IAAApJ,EAAA;IACpD,MAAM,CAACqJ,cAAc,EAAEC,iBAAiB,CAAC,GAAG3L,QAAQ,CAACwL,KAAK,CAAC;IAE3DvL,SAAS,CAAC,MAAM;MACd,MAAM2L,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/BF,iBAAiB,CAACH,KAAK,CAAC;MAC1B,CAAC,EAAEC,KAAK,CAAC;MAET,OAAO,MAAM;QACXK,YAAY,CAACF,OAAO,CAAC;MACvB,CAAC;IACH,CAAC,EAAE,CAACJ,KAAK,EAAEC,KAAK,CAAC,CAAC;IAElB,OAAOC,cAAc;EACvB,CAAC;;EAED;EAAArJ,EAAA,CAhBMkJ,WAAW;EAiBjB,MAAMQ,kBAAkB,GAAGR,WAAW,CAACxI,SAAS,EAAE,IAAI,CAAC;EACvD,MAAMiJ,oBAAoB,GAAGT,WAAW,CAAClI,WAAW,EAAE,IAAI,CAAC;EAC3D,MAAM4I,wBAAwB,GAAGV,WAAW,CAACxH,eAAe,EAAE,IAAI,CAAC;;EAEnE;EACA;EACA;EACA;EACA;;EAEA;EACA9D,SAAS,CAAC,MAAM;IACd,IAAI8L,kBAAkB,IAAIA,kBAAkB,CAACxH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC1DF,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLvB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACiJ,kBAAkB,EAAE1H,gBAAgB,CAAC,CAAC;EAE1CpE,SAAS,CAAC,MAAM;IACd,IAAI+L,oBAAoB,IAAIA,oBAAoB,CAACzH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9Da,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACLhC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC4I,oBAAoB,EAAE5G,cAAc,CAAC,CAAC;EAE1CnF,SAAS,CAAC,MAAM;IACd,IAAIgM,wBAAwB,IAAIA,wBAAwB,CAAC1H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACtEiC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLtC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC+H,wBAAwB,EAAEzF,kBAAkB,CAAC,CAAC;;EAElD;EACA,MAAM0F,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAAC9D,IAAY,EAAE+D,MAAoB,kBACzCzK,OAAA;MAAA8F,QAAA,gBACE9F,OAAA,CAACE,IAAI;QAACwK,MAAM;QAAA5E,QAAA,EAAEY;MAAI;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BrG,OAAA;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNrG,OAAA,CAACE,IAAI;QAACyK,IAAI,EAAC,WAAW;QAAC5E,KAAK,EAAE;UAAEO,QAAQ,EAAE;QAAO,CAAE;QAAAR,QAAA,EAChD2E,MAAM,CAACvG;MAAQ;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEgE,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACI,CAAM,EAAEH,MAAoB,KAAK;MACxCvH,OAAO,CAACM,GAAG,CAAC,QAAQ,EAAEiH,MAAM,CAAC,CAAC,CAAC;MAC/B,oBACEzK,OAAA,CAACjB,KAAK;QAAA+G,QAAA,gBACJ9F,OAAA,CAACvB,MAAM;UACLkM,IAAI,EAAC,SAAS;UACdE,IAAI,EAAC,OAAO;UACZC,IAAI,eAAE9K,OAAA,CAACR,WAAW;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB0E,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAACkB,MAAM,CAACtG,aAAa,CAAE;UAAA2B,QAAA,EACnD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA,CAACvB,MAAM;UACLoM,IAAI,EAAC,OAAO;UACZC,IAAI,eAAE9K,OAAA,CAACN,YAAY;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB0E,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAACiB,MAAM,CAACtG,aAAa,CAAE;UAAA2B,QAAA,EACnD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA,CAACvB,MAAM;UACLoM,IAAI,EAAC,OAAO;UACZC,IAAI,eAAE9K,OAAA,CAACP,gBAAgB;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B0E,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAACqC,MAAM,CAACtG,aAAa,EAAEsG,MAAM,CAACvG,QAAQ,CAAE;UAAA4B,QAAA,EACxE;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;EACF,CAAC,CACF;EAED,oBACErG,OAAA;IAAA8F,QAAA,gBACE9F,OAAA,CAACC,KAAK;MAAC+K,KAAK,EAAE,CAAE;MAACjF,KAAK,EAAE;QAAEO,QAAQ,EAAE,MAAM;QAAEN,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClGrG,OAAA,CAACE,IAAI;MAACyK,IAAI,EAAC,WAAW;MAAA7E,QAAA,EAAC;IAEvB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPrG,OAAA,CAACxB,IAAI;MAACyM,gBAAgB,EAAC,GAAG;MAAClF,KAAK,EAAE;QAAEmF,SAAS,EAAE;MAAG,CAAE;MAAApF,QAAA,gBAClD9F,OAAA,CAACG,OAAO;QAACgL,GAAG,eAAEnL,OAAA;UAAA8F,QAAA,gBAAM9F,OAAA,CAACZ,gBAAgB;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAP,QAAA,eAClD9F,OAAA,CAAC1B,IAAI;UAAC+L,KAAK,EAAC,sCAAQ;UAACQ,IAAI,EAAC,OAAO;UAAA/E,QAAA,gBAC/B9F,OAAA,CAACpB,IAAI;YACHwM,IAAI,EAAEzK,YAAa;YACnB0K,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAEhG,gBAAiB;YAAAQ,QAAA,gBAE3B9F,OAAA,CAACf,GAAG;cAACsM,MAAM,EAAE,EAAG;cAAAzF,QAAA,gBACd9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,sCAAQ;kBACdhF,IAAI,EAAC,YAAY;kBACjBiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAiH,QAAA,eAElD9F,OAAA,CAACrB,KAAK;oBACJkN,MAAM,eAAE7L,OAAA,CAACL,kBAAkB;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC/ByF,WAAW,EAAC,4BAAkB;oBAC9BpC,KAAK,EAAEzI,SAAU;oBACjB8K,QAAQ,EAAGC,CAAC,IAAK9K,YAAY,CAAC8K,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;oBAC9CwC,UAAU,eACRlM,OAAA,CAACvB,MAAM;sBACLoM,IAAI,EAAC,OAAO;sBACZC,IAAI,eAAE9K,OAAA,CAACT,cAAc;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzB0E,OAAO,EAAEA,CAAA,KAAMxI,gBAAgB,CAAC,IAAI,CAAE;sBAAAuD,QAAA,EACvC;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBACT;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNrG,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,sCAAQ;kBACdhF,IAAI,EAAC,sBAAsB;kBAC3BiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAiH,QAAA,eAEhD9F,OAAA,CAACtB,MAAM;oBACLoN,WAAW,EAAE/K,WAAW,CAAC+B,MAAM,KAAK,CAAC,GAAG,eAAe,GAAG,cAAe;oBACzEqJ,UAAU;oBACVC,eAAe,EAAErL,WAAW,CAAC+B,MAAM,KAAK,CAAC,GAAG,qBAAqB,GAAG,UAAW;oBAC/EuJ,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;sBAAA,IAAAC,gBAAA;sBAAA,OACzBD,MAAM,aAANA,MAAM,wBAAAC,gBAAA,GAAND,MAAM,CAAEzG,QAAQ,cAAA0G,gBAAA,uBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC;oBAAA,CACrF;oBAAA3G,QAAA,EAEA/E,WAAW,CAAC8C,GAAG,CAAC8I,IAAI,iBACnB3M,OAAA,CAACI,MAAM;sBAAYsJ,KAAK,EAAEiD,IAAK;sBAAA7G,QAAA,EAC5B6G;oBAAI,GADMA,IAAI;sBAAAzG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrG,OAAA,CAACf,GAAG;cAACsM,MAAM,EAAE,EAAG;cAAAzF,QAAA,gBACd9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,sCAAQ;kBACdhF,IAAI,EAAC,YAAY;kBACjBiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAiH,QAAA,eAEhD9F,OAAA,CAACrB,KAAK;oBAACmN,WAAW,EAAC;kBAAkB;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNrG,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,kDAAU;kBAChBhF,IAAI,EAAC,eAAe;kBAAAZ,QAAA,eAEpB9F,OAAA,CAACrB,KAAK;oBAACmN,WAAW,EAAC;kBAAmB;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrG,OAAA,CAACpB,IAAI,CAAC6M,IAAI;cAAA3F,QAAA,eACR9F,OAAA,CAACvB,MAAM;gBACLkM,IAAI,EAAC,SAAS;gBACdiC,QAAQ,EAAC,QAAQ;gBACjBnM,OAAO,EAAEA,OAAQ;gBACjBqK,IAAI,eAAE9K,OAAA,CAACZ,gBAAgB;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAP,QAAA,EAC5B;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EAGNlF,qBAAqB,iBACpBnB,OAAA,CAAC1B,IAAI;YACHuM,IAAI,EAAC,OAAO;YACZ9E,KAAK,EAAE;cAAEmF,SAAS,EAAE,EAAE;cAAE2B,WAAW,EAAE;YAAU,CAAE;YACjDxC,KAAK,eACHrK,OAAA;cAAM+F,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU,CAAE;cAAAT,QAAA,gBAChC9F,OAAA,CAACZ,gBAAgB;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,+CACtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YACDyG,KAAK,eACH9M,OAAA,CAACvB,MAAM;cACLoM,IAAI,EAAC,OAAO;cACZF,IAAI,EAAC,MAAM;cACXI,OAAO,EAAEA,CAAA,KAAM3J,wBAAwB,CAAC,IAAI,CAAE;cAAA0E,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;YAAAP,QAAA,gBAED9F,OAAA,CAACf,GAAG;cAACsM,MAAM,EAAE,EAAG;cAAAzF,QAAA,gBACd9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,gBACZ9F,OAAA,CAACE,IAAI;kBAACwK,MAAM;kBAAA5E,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBrG,OAAA,CAACE,IAAI;kBAAC6M,QAAQ;kBAAAjH,QAAA,EAAE3E,qBAAqB,CAACuF;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNrG,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,gBACZ9F,OAAA,CAACE,IAAI;kBAACwK,MAAM;kBAAA5E,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBrG,OAAA,CAACE,IAAI;kBAAA4F,QAAA,EAAE3E,qBAAqB,CAAC0F;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrG,OAAA,CAACf,GAAG;cAAC8G,KAAK,EAAE;gBAAEmF,SAAS,EAAE;cAAE,CAAE;cAAApF,QAAA,eAC3B9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,gBACZ9F,OAAA,CAACE,IAAI;kBAACwK,MAAM;kBAAA5E,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBrG,OAAA,CAACE,IAAI;kBAAC6M,QAAQ;kBAAChH,KAAK,EAAE;oBAAEO,QAAQ,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAT,QAAA,EACvD3E,qBAAqB,CAACsF;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GA/HgD,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgInD,CAAC,eAEVrG,OAAA,CAACG,OAAO;QAACgL,GAAG,eAAEnL,OAAA;UAAA8F,QAAA,gBAAM9F,OAAA,CAACX,eAAe;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAP,QAAA,eACjD9F,OAAA,CAAC1B,IAAI;UACH+L,KAAK,EAAC,0BAAM;UACZQ,IAAI,EAAC,OAAO;UACZiC,KAAK,eACH9M,OAAA,CAACjB,KAAK;YAAA+G,QAAA,gBACJ9F,OAAA,CAACrB,KAAK;cACJmN,WAAW,EAAC,sCAAQ;cACpBpC,KAAK,EAAEnI,WAAY;cACnBwK,QAAQ,EAAGC,CAAC,IAAKxK,cAAc,CAACwK,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cAChD3D,KAAK,EAAE;gBAAEiH,KAAK,EAAE;cAAI;YAAE;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFrG,OAAA,CAACvB,MAAM;cACLqM,IAAI,eAAE9K,OAAA,CAACT,cAAc;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB0E,OAAO,EAAEA,CAAA,KAAMzH,cAAc,CAAC,IAAI,CAAE;cACpC7C,OAAO,EAAEA,OAAQ;cAAAqF,QAAA,EAClB;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAP,QAAA,EAEAzE,SAAS,CAACyB,MAAM,KAAK,CAAC,IAAI,CAACrC,OAAO,gBACjCT,OAAA,CAACzB,KAAK;YACJM,OAAO,EAAC,0BAAM;YACdoO,WAAW,EAAC,yPAA4C;YACxDtC,IAAI,EAAC,MAAM;YACXuC,QAAQ;UAAA;YAAAhH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,gBAEFrG,OAAA,CAAClB,KAAK;YACJqO,OAAO,EAAE/C,eAAgB;YACzBgD,UAAU,EAAE/L,SAAU;YACtBgM,MAAM,EAAC,eAAe;YACtB5M,OAAO,EAAEA,OAAQ;YACjB6M,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACF7C,IAAI,EAAC;UAAO;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GA3C+C,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4ClD,CAAC,eAEVrG,OAAA,CAACG,OAAO;QAACgL,GAAG,eAAEnL,OAAA;UAAA8F,QAAA,gBAAM9F,OAAA,CAACV,YAAY;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAP,QAAA,eAC9C9F,OAAA,CAAC1B,IAAI;UAAC+L,KAAK,EAAC,8DAAY;UAACQ,IAAI,EAAC,OAAO;UAAA/E,QAAA,eACnC9F,OAAA,CAACpB,IAAI;YACHwM,IAAI,EAAEvK,QAAS;YACfwK,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE/D,YAAa;YAAAzB,QAAA,gBAEvB9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZhF,IAAI,EAAC,yBAAyB;cAC9BiF,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/M,OAAO,EAAE;cAAY,CAAC,CAAE;cAAAiH,QAAA,eAElD9F,OAAA,CAACrB,KAAK;gBACJmN,WAAW,EAAC,wFAA4B;gBACxCpC,KAAK,EAAEzH,eAAgB;gBACvB8J,QAAQ,EAAGC,CAAC,IAAK9J,kBAAkB,CAAC8J,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;gBACpDiE,MAAM,eACJ3N,OAAA,CAACvB,MAAM;kBACLkM,IAAI,EAAC,MAAM;kBACXE,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAE9K,OAAA,CAACT,cAAc;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB5F,OAAO,EAAE4B,oBAAqB;kBAC9B0I,OAAO,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,IAAI;gBAAE;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrG,OAAA,CAACpB,IAAI,CAAC6M,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZhF,IAAI,EAAC,eAAe;cACpBiF,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/M,OAAO,EAAE;cAAY,CAAC,CAAE;cAAAiH,QAAA,eAElD9F,OAAA,CAACtB,MAAM;gBACLoN,WAAW,EAAC,gIAAuB;gBACnCK,UAAU;gBACV1L,OAAO,EAAE4B,oBAAqB;gBAC9BgK,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;kBAAA,IAAAqB,iBAAA;kBAAA,OACzBrB,MAAM,aAANA,MAAM,wBAAAqB,iBAAA,GAANrB,MAAM,CAAEzG,QAAQ,cAAA8H,iBAAA,uBAAjBA,iBAAA,CAAyCnB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC;gBAAA,CACrF;gBACDL,eAAe,EACb/J,oBAAoB,gBAClBrC,OAAA;kBAAK+F,KAAK,EAAE;oBAAE8H,SAAS,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAhI,QAAA,eACnD9F,OAAA,CAACE,IAAI;oBAACyK,IAAI,EAAC,WAAW;oBAAA7E,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,GACJlE,aAAa,CAACW,MAAM,KAAK,CAAC,gBAC5B9C,OAAA;kBAAK+F,KAAK,EAAE;oBAAE8H,SAAS,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAhI,QAAA,eACnD9F,OAAA,CAACE,IAAI;oBAACyK,IAAI,EAAC,WAAW;oBAAA7E,QAAA,GAAC,mDACZ,eAAA9F,OAAA;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,wFAEhB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,GACJ,IACL;gBAAAP,QAAA,EAEA3D,aAAa,CAAC0B,GAAG,CAACkK,QAAQ,iBACzB/N,OAAA,CAACI,MAAM;kBAA8BsJ,KAAK,EAAEqE,QAAQ,CAAC5J,aAAc;kBAAA2B,QAAA,eACjE9F,OAAA;oBAAA8F,QAAA,gBACE9F,OAAA,CAACE,IAAI;sBAACwK,MAAM;sBAAA5E,QAAA,EAAEiI,QAAQ,CAAC/J;oBAAa;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5CrG,OAAA;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrG,OAAA,CAACE,IAAI;sBAACyK,IAAI,EAAC,WAAW;sBAAC5E,KAAK,EAAE;wBAAEO,QAAQ,EAAE;sBAAO,CAAE;sBAAAR,QAAA,EAChDiI,QAAQ,CAAC7J;oBAAQ;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAPK0H,QAAQ,CAAC5J,aAAa;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQ3B,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZrG,OAAA,CAACb,OAAO;cAAA2G,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAE1BrG,OAAA,CAACf,GAAG;cAACsM,MAAM,EAAE,EAAG;cAAAzF,QAAA,gBACd9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZhF,IAAI,EAAC,aAAa;kBAClBiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAiH,QAAA,eAElD9F,OAAA,CAACrB,KAAK;oBAACmN,WAAW,EAAC;kBAAmB;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNrG,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,cAAI;kBACVhF,IAAI,EAAC,aAAa;kBAClBsH,YAAY,EAAE,EAAG;kBACjBrC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAAiH,QAAA,eAE/C9F,OAAA,CAACrB,KAAK;oBAACgM,IAAI,EAAC,QAAQ;oBAACmB,WAAW,EAAC;kBAAI;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrG,OAAA,CAACf,GAAG;cAACsM,MAAM,EAAE,EAAG;cAAAzF,QAAA,gBACd9F,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,oBAAK;kBACXhF,IAAI,EAAC,iBAAiB;kBACtBiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAAiH,QAAA,eAE/C9F,OAAA,CAACrB,KAAK;oBAACmN,WAAW,EAAC;kBAAU;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNrG,OAAA,CAACd,GAAG;gBAACsM,IAAI,EAAE,EAAG;gBAAA1F,QAAA,eACZ9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;kBACRC,KAAK,EAAC,cAAI;kBACVhF,IAAI,EAAC,iBAAiB;kBACtBiF,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE/M,OAAO,EAAE;kBAAQ,CAAC,CAAE;kBAAAiH,QAAA,eAE9C9F,OAAA,CAACrB,KAAK,CAACsP,QAAQ;oBAACnC,WAAW,EAAC;kBAAO;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrG,OAAA,CAACpB,IAAI,CAAC6M,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZhF,IAAI,EAAC,aAAa;cAClBiF,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/M,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhD9F,OAAA,CAACrB,KAAK;gBAACmN,WAAW,EAAC;cAAyB;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAEZrG,OAAA,CAACpB,IAAI,CAAC6M,IAAI;cAAA3F,QAAA,eACR9F,OAAA,CAACvB,MAAM;gBACLkM,IAAI,EAAC,SAAS;gBACdiC,QAAQ,EAAC,QAAQ;gBACjBnM,OAAO,EAAEA,OAAQ;gBACjBqK,IAAI,eAAE9K,OAAA,CAACJ,mBAAmB;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAP,QAAA,EAC/B;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GArI4C,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsI/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGPrG,OAAA,CAAChB,KAAK;MACJqL,KAAK,EAAC,0BAAM;MACZ6D,IAAI,EAAEnM,gBAAiB;MACvBoM,QAAQ,EAAEA,CAAA,KAAMnM,mBAAmB,CAAC,KAAK,CAAE;MAC3CoM,MAAM,EAAE,cACNpO,OAAA,CAACvB,MAAM;QAAasM,OAAO,EAAEA,CAAA,KAAM/I,mBAAmB,CAAC,KAAK,CAAE;QAAA8D,QAAA,EAAC;MAE/D,GAFY,OAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACF2G,KAAK,EAAE,GAAI;MAAAlH,QAAA,eAEX9F,OAAA,CAACF,QAAQ;QACP4J,KAAK,EAAE/H,eAAgB;QACvB0M,IAAI,EAAE,EAAG;QACTC,QAAQ;QACRvI,KAAK,EAAE;UAAEwI,UAAU,EAAE;QAAY;MAAE;QAAArI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRrG,OAAA,CAAChB,KAAK;MACJqL,KAAK,EAAC,sCAAQ;MACd6D,IAAI,EAAErM,gBAAiB;MACvBsM,QAAQ,EAAEA,CAAA,KAAMrM,mBAAmB,CAAC,KAAK,CAAE;MAC3CsM,MAAM,EAAE,cACNpO,OAAA,CAACvB,MAAM;QAAcsM,OAAO,EAAEA,CAAA,KAAMjJ,mBAAmB,CAAC,KAAK,CAAE;QAAAgE,QAAA,EAAC;MAEhE,GAFY,QAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,eACTrG,OAAA,CAACvB,MAAM;QAAYkM,IAAI,EAAC,SAAS;QAACI,OAAO,EAAEA,CAAA,KAAMjK,UAAU,CAAC0N,MAAM,CAAC,CAAE;QAAA1I,QAAA,EAAC;MAEtE,GAFY,MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC,CACT;MACF2G,KAAK,EAAE,GAAI;MAAAlH,QAAA,eAEX9F,OAAA,CAACpB,IAAI;QACHwM,IAAI,EAAEtK,UAAW;QACjBwK,QAAQ,EAAEnE,cAAe;QAAArB,QAAA,eAEzB9F,OAAA,CAACpB,IAAI,CAAC6M,IAAI;UACR/E,IAAI,EAAC,kBAAkB;UACvBiF,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/M,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAiH,QAAA,eAEjD9F,OAAA,CAACF,QAAQ;YACPuO,IAAI,EAAE,EAAG;YACTtI,KAAK,EAAE;cAAEwI,UAAU,EAAE;YAAY,CAAE;YACnCzC,WAAW,EAAC;UAAgB;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/F,GAAA,CAp1BID,iBAA2B;EAAA,QAERzB,IAAI,CAACgC,OAAO,EAChBhC,IAAI,CAACgC,OAAO,EACVhC,IAAI,CAACgC,OAAO;AAAA;AAAA6N,EAAA,GAJ7BpO,iBAA2B;AAs1BjC,eAAeA,iBAAiB;AAAC,IAAAoO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}