{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport defaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport { FormItemInputContext } from '../form/context';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport warning from '../_util/warning';\nimport List from './list';\nimport Operation from './operation';\nimport Search from './search';\nvar Transfer = /*#__PURE__*/function (_React$Component) {\n  _inherits(Transfer, _React$Component);\n  var _super = _createSuper(Transfer);\n  function Transfer(props) {\n    var _this;\n    _classCallCheck(this, Transfer);\n    _this = _super.call(this, props);\n    _this.separatedDataSource = null;\n    _this.setStateKeys = function (direction, keys) {\n      if (direction === 'left') {\n        _this.setState(function (_ref) {\n          var sourceSelectedKeys = _ref.sourceSelectedKeys;\n          return {\n            sourceSelectedKeys: typeof keys === 'function' ? keys(sourceSelectedKeys || []) : keys\n          };\n        });\n      } else {\n        _this.setState(function (_ref2) {\n          var targetSelectedKeys = _ref2.targetSelectedKeys;\n          return {\n            targetSelectedKeys: typeof keys === 'function' ? keys(targetSelectedKeys || []) : keys\n          };\n        });\n      }\n    };\n    _this.getLocale = function (transferLocale, renderEmpty) {\n      var _this$props$locale = _this.props.locale,\n        locale = _this$props$locale === void 0 ? {} : _this$props$locale;\n      return _extends(_extends(_extends({}, transferLocale), {\n        notFoundContent: renderEmpty('Transfer')\n      }), locale);\n    };\n    _this.moveTo = function (direction) {\n      var _this$props = _this.props,\n        _this$props$targetKey = _this$props.targetKeys,\n        targetKeys = _this$props$targetKey === void 0 ? [] : _this$props$targetKey,\n        _this$props$dataSourc = _this$props.dataSource,\n        dataSource = _this$props$dataSourc === void 0 ? [] : _this$props$dataSourc,\n        onChange = _this$props.onChange;\n      var _this$state = _this.state,\n        sourceSelectedKeys = _this$state.sourceSelectedKeys,\n        targetSelectedKeys = _this$state.targetSelectedKeys;\n      var moveKeys = direction === 'right' ? sourceSelectedKeys : targetSelectedKeys;\n      // filter the disabled options\n      var newMoveKeys = moveKeys.filter(function (key) {\n        return !dataSource.some(function (data) {\n          return !!(key === data.key && data.disabled);\n        });\n      });\n      // move items to target box\n      var newTargetKeys = direction === 'right' ? newMoveKeys.concat(targetKeys) : targetKeys.filter(function (targetKey) {\n        return !newMoveKeys.includes(targetKey);\n      });\n      // empty checked keys\n      var oppositeDirection = direction === 'right' ? 'left' : 'right';\n      _this.setStateKeys(oppositeDirection, []);\n      _this.handleSelectChange(oppositeDirection, []);\n      onChange === null || onChange === void 0 ? void 0 : onChange(newTargetKeys, direction, newMoveKeys);\n    };\n    _this.moveToLeft = function () {\n      return _this.moveTo('left');\n    };\n    _this.moveToRight = function () {\n      return _this.moveTo('right');\n    };\n    _this.onItemSelectAll = function (direction, selectedKeys, checkAll) {\n      _this.setStateKeys(direction, function (prevKeys) {\n        var mergedCheckedKeys = [];\n        if (checkAll) {\n          // Merge current keys with origin key\n          mergedCheckedKeys = Array.from(new Set([].concat(_toConsumableArray(prevKeys), _toConsumableArray(selectedKeys))));\n        } else {\n          // Remove current keys from origin keys\n          mergedCheckedKeys = prevKeys.filter(function (key) {\n            return !selectedKeys.includes(key);\n          });\n        }\n        _this.handleSelectChange(direction, mergedCheckedKeys);\n        return mergedCheckedKeys;\n      });\n    };\n    _this.onLeftItemSelectAll = function (selectedKeys, checkAll) {\n      return _this.onItemSelectAll('left', selectedKeys, checkAll);\n    };\n    _this.onRightItemSelectAll = function (selectedKeys, checkAll) {\n      return _this.onItemSelectAll('right', selectedKeys, checkAll);\n    };\n    _this.handleFilter = function (direction, e) {\n      var onSearch = _this.props.onSearch;\n      var value = e.target.value;\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(direction, value);\n    };\n    _this.handleLeftFilter = function (e) {\n      return _this.handleFilter('left', e);\n    };\n    _this.handleRightFilter = function (e) {\n      return _this.handleFilter('right', e);\n    };\n    _this.handleClear = function (direction) {\n      var onSearch = _this.props.onSearch;\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(direction, '');\n    };\n    _this.handleLeftClear = function () {\n      return _this.handleClear('left');\n    };\n    _this.handleRightClear = function () {\n      return _this.handleClear('right');\n    };\n    _this.onItemSelect = function (direction, selectedKey, checked) {\n      var _this$state2 = _this.state,\n        sourceSelectedKeys = _this$state2.sourceSelectedKeys,\n        targetSelectedKeys = _this$state2.targetSelectedKeys;\n      var holder = direction === 'left' ? _toConsumableArray(sourceSelectedKeys) : _toConsumableArray(targetSelectedKeys);\n      var index = holder.indexOf(selectedKey);\n      if (index > -1) {\n        holder.splice(index, 1);\n      }\n      if (checked) {\n        holder.push(selectedKey);\n      }\n      _this.handleSelectChange(direction, holder);\n      if (!_this.props.selectedKeys) {\n        _this.setStateKeys(direction, holder);\n      }\n    };\n    _this.onLeftItemSelect = function (selectedKey, checked) {\n      return _this.onItemSelect('left', selectedKey, checked);\n    };\n    _this.onRightItemSelect = function (selectedKey, checked) {\n      return _this.onItemSelect('right', selectedKey, checked);\n    };\n    _this.onRightItemRemove = function (selectedKeys) {\n      var _this$props2 = _this.props,\n        _this$props2$targetKe = _this$props2.targetKeys,\n        targetKeys = _this$props2$targetKe === void 0 ? [] : _this$props2$targetKe,\n        onChange = _this$props2.onChange;\n      _this.setStateKeys('right', []);\n      onChange === null || onChange === void 0 ? void 0 : onChange(targetKeys.filter(function (key) {\n        return !selectedKeys.includes(key);\n      }), 'left', _toConsumableArray(selectedKeys));\n    };\n    _this.handleScroll = function (direction, e) {\n      var onScroll = _this.props.onScroll;\n      onScroll === null || onScroll === void 0 ? void 0 : onScroll(direction, e);\n    };\n    _this.handleLeftScroll = function (e) {\n      return _this.handleScroll('left', e);\n    };\n    _this.handleRightScroll = function (e) {\n      return _this.handleScroll('right', e);\n    };\n    // eslint-disable-next-line class-methods-use-this\n    _this.handleListStyle = function (listStyle, direction) {\n      if (typeof listStyle === 'function') {\n        return listStyle({\n          direction: direction\n        });\n      }\n      return listStyle;\n    };\n    var _props$selectedKeys = props.selectedKeys,\n      selectedKeys = _props$selectedKeys === void 0 ? [] : _props$selectedKeys,\n      _props$targetKeys = props.targetKeys,\n      targetKeys = _props$targetKeys === void 0 ? [] : _props$targetKeys;\n    _this.state = {\n      sourceSelectedKeys: selectedKeys.filter(function (key) {\n        return !targetKeys.includes(key);\n      }),\n      targetSelectedKeys: selectedKeys.filter(function (key) {\n        return targetKeys.includes(key);\n      })\n    };\n    return _this;\n  }\n  _createClass(Transfer, [{\n    key: \"getTitles\",\n    value: function getTitles(transferLocale) {\n      var _a, _b;\n      return (_b = (_a = this.props.titles) !== null && _a !== void 0 ? _a : transferLocale.titles) !== null && _b !== void 0 ? _b : [];\n    }\n  }, {\n    key: \"handleSelectChange\",\n    value: function handleSelectChange(direction, holder) {\n      var _this$state3 = this.state,\n        sourceSelectedKeys = _this$state3.sourceSelectedKeys,\n        targetSelectedKeys = _this$state3.targetSelectedKeys;\n      var onSelectChange = this.props.onSelectChange;\n      if (!onSelectChange) {\n        return;\n      }\n      if (direction === 'left') {\n        onSelectChange(holder, targetSelectedKeys);\n      } else {\n        onSelectChange(sourceSelectedKeys, holder);\n      }\n    }\n  }, {\n    key: \"separateDataSource\",\n    value: function separateDataSource() {\n      var _this$props3 = this.props,\n        _this$props3$dataSour = _this$props3.dataSource,\n        dataSource = _this$props3$dataSour === void 0 ? [] : _this$props3$dataSour,\n        rowKey = _this$props3.rowKey,\n        _this$props3$targetKe = _this$props3.targetKeys,\n        targetKeys = _this$props3$targetKe === void 0 ? [] : _this$props3$targetKe;\n      var leftDataSource = [];\n      var rightDataSource = new Array(targetKeys.length);\n      dataSource.forEach(function (record) {\n        if (rowKey) {\n          record = _extends(_extends({}, record), {\n            key: rowKey(record)\n          });\n        }\n        // rightDataSource should be ordered by targetKeys\n        // leftDataSource should be ordered by dataSource\n        var indexOfKey = targetKeys.indexOf(record.key);\n        if (indexOfKey !== -1) {\n          rightDataSource[indexOfKey] = record;\n        } else {\n          leftDataSource.push(record);\n        }\n      });\n      return {\n        leftDataSource: leftDataSource,\n        rightDataSource: rightDataSource\n      };\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      return /*#__PURE__*/React.createElement(LocaleReceiver, {\n        componentName: \"Transfer\",\n        defaultLocale: defaultLocale.Transfer\n      }, function (contextLocale) {\n        return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref3) {\n          var getPrefixCls = _ref3.getPrefixCls,\n            renderEmpty = _ref3.renderEmpty,\n            direction = _ref3.direction;\n          return /*#__PURE__*/React.createElement(FormItemInputContext.Consumer, null, function (_ref4) {\n            var _classNames;\n            var hasFeedback = _ref4.hasFeedback,\n              contextStatus = _ref4.status;\n            var _this2$props = _this2.props,\n              customizePrefixCls = _this2$props.prefixCls,\n              className = _this2$props.className,\n              disabled = _this2$props.disabled,\n              _this2$props$operatio = _this2$props.operations,\n              operations = _this2$props$operatio === void 0 ? [] : _this2$props$operatio,\n              _this2$props$showSear = _this2$props.showSearch,\n              showSearch = _this2$props$showSear === void 0 ? false : _this2$props$showSear,\n              footer = _this2$props.footer,\n              style = _this2$props.style,\n              _this2$props$listStyl = _this2$props.listStyle,\n              listStyle = _this2$props$listStyl === void 0 ? {} : _this2$props$listStyl,\n              operationStyle = _this2$props.operationStyle,\n              filterOption = _this2$props.filterOption,\n              render = _this2$props.render,\n              children = _this2$props.children,\n              showSelectAll = _this2$props.showSelectAll,\n              oneWay = _this2$props.oneWay,\n              pagination = _this2$props.pagination,\n              customStatus = _this2$props.status;\n            var prefixCls = getPrefixCls('transfer', customizePrefixCls);\n            var locale = _this2.getLocale(contextLocale, renderEmpty || defaultRenderEmpty);\n            var _this2$state = _this2.state,\n              sourceSelectedKeys = _this2$state.sourceSelectedKeys,\n              targetSelectedKeys = _this2$state.targetSelectedKeys;\n            var mergedStatus = getMergedStatus(contextStatus, customStatus);\n            var mergedPagination = !children && pagination;\n            var _this2$separateDataSo = _this2.separateDataSource(),\n              leftDataSource = _this2$separateDataSo.leftDataSource,\n              rightDataSource = _this2$separateDataSo.rightDataSource;\n            var leftActive = targetSelectedKeys.length > 0;\n            var rightActive = sourceSelectedKeys.length > 0;\n            var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-customize-list\"), !!children), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), getStatusClassNames(prefixCls, mergedStatus, hasFeedback), className);\n            var titles = _this2.getTitles(locale);\n            var selectAllLabels = _this2.props.selectAllLabels || [];\n            return /*#__PURE__*/React.createElement(\"div\", {\n              className: cls,\n              style: style\n            }, /*#__PURE__*/React.createElement(List, _extends({\n              prefixCls: \"\".concat(prefixCls, \"-list\"),\n              titleText: titles === null || titles === void 0 ? void 0 : titles[0],\n              dataSource: leftDataSource,\n              filterOption: filterOption,\n              style: _this2.handleListStyle(listStyle, 'left'),\n              checkedKeys: sourceSelectedKeys,\n              handleFilter: _this2.handleLeftFilter,\n              handleClear: _this2.handleLeftClear,\n              onItemSelect: _this2.onLeftItemSelect,\n              onItemSelectAll: _this2.onLeftItemSelectAll,\n              render: render,\n              showSearch: showSearch,\n              renderList: children,\n              footer: footer,\n              onScroll: _this2.handleLeftScroll,\n              disabled: disabled,\n              direction: direction === 'rtl' ? 'right' : 'left',\n              showSelectAll: showSelectAll,\n              selectAllLabel: selectAllLabels[0],\n              pagination: mergedPagination\n            }, locale)), /*#__PURE__*/React.createElement(Operation, {\n              className: \"\".concat(prefixCls, \"-operation\"),\n              rightActive: rightActive,\n              rightArrowText: operations[0],\n              moveToRight: _this2.moveToRight,\n              leftActive: leftActive,\n              leftArrowText: operations[1],\n              moveToLeft: _this2.moveToLeft,\n              style: operationStyle,\n              disabled: disabled,\n              direction: direction,\n              oneWay: oneWay\n            }), /*#__PURE__*/React.createElement(List, _extends({\n              prefixCls: \"\".concat(prefixCls, \"-list\"),\n              titleText: titles === null || titles === void 0 ? void 0 : titles[1],\n              dataSource: rightDataSource,\n              filterOption: filterOption,\n              style: _this2.handleListStyle(listStyle, 'right'),\n              checkedKeys: targetSelectedKeys,\n              handleFilter: _this2.handleRightFilter,\n              handleClear: _this2.handleRightClear,\n              onItemSelect: _this2.onRightItemSelect,\n              onItemSelectAll: _this2.onRightItemSelectAll,\n              onItemRemove: _this2.onRightItemRemove,\n              render: render,\n              showSearch: showSearch,\n              renderList: children,\n              footer: footer,\n              onScroll: _this2.handleRightScroll,\n              disabled: disabled,\n              direction: direction === 'rtl' ? 'left' : 'right',\n              showSelectAll: showSelectAll,\n              selectAllLabel: selectAllLabels[1],\n              showRemove: oneWay,\n              pagination: mergedPagination\n            }, locale)));\n          });\n        });\n      });\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(_ref5) {\n      var selectedKeys = _ref5.selectedKeys,\n        targetKeys = _ref5.targetKeys,\n        pagination = _ref5.pagination,\n        children = _ref5.children;\n      if (selectedKeys) {\n        var mergedTargetKeys = targetKeys || [];\n        return {\n          sourceSelectedKeys: selectedKeys.filter(function (key) {\n            return !mergedTargetKeys.includes(key);\n          }),\n          targetSelectedKeys: selectedKeys.filter(function (key) {\n            return mergedTargetKeys.includes(key);\n          })\n        };\n      }\n      process.env.NODE_ENV !== \"production\" ? warning(!pagination || !children, 'Transfer', '`pagination` not support customize render list.') : void 0;\n      return null;\n    }\n  }]);\n  return Transfer;\n}(React.Component); // For high-level customized Transfer @dqaria\nTransfer.List = List;\nTransfer.Operation = Operation;\nTransfer.Search = Search;\nexport default Transfer;", "map": {"version": 3, "names": ["_defineProperty", "_toConsumableArray", "_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "classNames", "React", "ConfigConsumer", "defaultRenderEmpty", "FormItemInputContext", "LocaleReceiver", "defaultLocale", "getMergedStatus", "getStatusClassNames", "warning", "List", "Operation", "Search", "Transfer", "_React$Component", "_super", "props", "_this", "call", "separatedDataSource", "setStateKeys", "direction", "keys", "setState", "_ref", "sourceSelectedKeys", "_ref2", "targetSelectedKeys", "getLocale", "transferLocale", "renderEmpty", "_this$props$locale", "locale", "notFoundContent", "moveTo", "_this$props", "_this$props$targetKey", "targetKeys", "_this$props$dataSourc", "dataSource", "onChange", "_this$state", "state", "move<PERSON>eys", "newMoveKeys", "filter", "key", "some", "data", "disabled", "newTargetKeys", "concat", "<PERSON><PERSON><PERSON>", "includes", "oppositeDirection", "handleSelectChange", "moveToLeft", "moveToRight", "onItemSelectAll", "<PERSON><PERSON><PERSON><PERSON>", "checkAll", "prevKeys", "mergedCheckedKeys", "Array", "from", "Set", "onLeftItemSelectAll", "onRightItemSelectAll", "handleFilter", "e", "onSearch", "value", "target", "handleLeftFilter", "handleRightFilter", "handleClear", "handleLeftClear", "handleRightClear", "onItemSelect", "<PERSON><PERSON><PERSON>", "checked", "_this$state2", "holder", "index", "indexOf", "splice", "push", "onLeftItemSelect", "onRightItemSelect", "onRightItemRemove", "_this$props2", "_this$props2$targetKe", "handleScroll", "onScroll", "handleLeftScroll", "handleRightScroll", "handleListStyle", "listStyle", "_props$selectedKeys", "_props$targetKeys", "get<PERSON>itles", "_a", "_b", "titles", "_this$state3", "onSelectChange", "separateDataSource", "_this$props3", "_this$props3$dataSour", "<PERSON><PERSON><PERSON>", "_this$props3$targetKe", "leftDataSource", "rightDataSource", "length", "for<PERSON>ach", "record", "indexOfKey", "render", "_this2", "createElement", "componentName", "contextLocale", "_ref3", "getPrefixCls", "Consumer", "_ref4", "_classNames", "hasFeedback", "contextStatus", "status", "_this2$props", "customizePrefixCls", "prefixCls", "className", "_this2$props$operatio", "operations", "_this2$props$showSear", "showSearch", "footer", "style", "_this2$props$listStyl", "operationStyle", "filterOption", "children", "showSelectAll", "oneWay", "pagination", "customStatus", "_this2$state", "mergedStatus", "mergedPagination", "_this2$separateDataSo", "leftActive", "rightActive", "cls", "selectAllLabels", "titleText", "checked<PERSON>eys", "renderList", "selectAllLabel", "rightArrowText", "leftArrowText", "onItemRemove", "showRemove", "getDerivedStateFromProps", "_ref5", "mergedTargetKeys", "process", "env", "NODE_ENV", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/transfer/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport defaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport { FormItemInputContext } from '../form/context';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport warning from '../_util/warning';\nimport List from './list';\nimport Operation from './operation';\nimport Search from './search';\nvar Transfer = /*#__PURE__*/function (_React$Component) {\n  _inherits(Transfer, _React$Component);\n  var _super = _createSuper(Transfer);\n  function Transfer(props) {\n    var _this;\n    _classCallCheck(this, Transfer);\n    _this = _super.call(this, props);\n    _this.separatedDataSource = null;\n    _this.setStateKeys = function (direction, keys) {\n      if (direction === 'left') {\n        _this.setState(function (_ref) {\n          var sourceSelectedKeys = _ref.sourceSelectedKeys;\n          return {\n            sourceSelectedKeys: typeof keys === 'function' ? keys(sourceSelectedKeys || []) : keys\n          };\n        });\n      } else {\n        _this.setState(function (_ref2) {\n          var targetSelectedKeys = _ref2.targetSelectedKeys;\n          return {\n            targetSelectedKeys: typeof keys === 'function' ? keys(targetSelectedKeys || []) : keys\n          };\n        });\n      }\n    };\n    _this.getLocale = function (transferLocale, renderEmpty) {\n      var _this$props$locale = _this.props.locale,\n        locale = _this$props$locale === void 0 ? {} : _this$props$locale;\n      return _extends(_extends(_extends({}, transferLocale), {\n        notFoundContent: renderEmpty('Transfer')\n      }), locale);\n    };\n    _this.moveTo = function (direction) {\n      var _this$props = _this.props,\n        _this$props$targetKey = _this$props.targetKeys,\n        targetKeys = _this$props$targetKey === void 0 ? [] : _this$props$targetKey,\n        _this$props$dataSourc = _this$props.dataSource,\n        dataSource = _this$props$dataSourc === void 0 ? [] : _this$props$dataSourc,\n        onChange = _this$props.onChange;\n      var _this$state = _this.state,\n        sourceSelectedKeys = _this$state.sourceSelectedKeys,\n        targetSelectedKeys = _this$state.targetSelectedKeys;\n      var moveKeys = direction === 'right' ? sourceSelectedKeys : targetSelectedKeys;\n      // filter the disabled options\n      var newMoveKeys = moveKeys.filter(function (key) {\n        return !dataSource.some(function (data) {\n          return !!(key === data.key && data.disabled);\n        });\n      });\n      // move items to target box\n      var newTargetKeys = direction === 'right' ? newMoveKeys.concat(targetKeys) : targetKeys.filter(function (targetKey) {\n        return !newMoveKeys.includes(targetKey);\n      });\n      // empty checked keys\n      var oppositeDirection = direction === 'right' ? 'left' : 'right';\n      _this.setStateKeys(oppositeDirection, []);\n      _this.handleSelectChange(oppositeDirection, []);\n      onChange === null || onChange === void 0 ? void 0 : onChange(newTargetKeys, direction, newMoveKeys);\n    };\n    _this.moveToLeft = function () {\n      return _this.moveTo('left');\n    };\n    _this.moveToRight = function () {\n      return _this.moveTo('right');\n    };\n    _this.onItemSelectAll = function (direction, selectedKeys, checkAll) {\n      _this.setStateKeys(direction, function (prevKeys) {\n        var mergedCheckedKeys = [];\n        if (checkAll) {\n          // Merge current keys with origin key\n          mergedCheckedKeys = Array.from(new Set([].concat(_toConsumableArray(prevKeys), _toConsumableArray(selectedKeys))));\n        } else {\n          // Remove current keys from origin keys\n          mergedCheckedKeys = prevKeys.filter(function (key) {\n            return !selectedKeys.includes(key);\n          });\n        }\n        _this.handleSelectChange(direction, mergedCheckedKeys);\n        return mergedCheckedKeys;\n      });\n    };\n    _this.onLeftItemSelectAll = function (selectedKeys, checkAll) {\n      return _this.onItemSelectAll('left', selectedKeys, checkAll);\n    };\n    _this.onRightItemSelectAll = function (selectedKeys, checkAll) {\n      return _this.onItemSelectAll('right', selectedKeys, checkAll);\n    };\n    _this.handleFilter = function (direction, e) {\n      var onSearch = _this.props.onSearch;\n      var value = e.target.value;\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(direction, value);\n    };\n    _this.handleLeftFilter = function (e) {\n      return _this.handleFilter('left', e);\n    };\n    _this.handleRightFilter = function (e) {\n      return _this.handleFilter('right', e);\n    };\n    _this.handleClear = function (direction) {\n      var onSearch = _this.props.onSearch;\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(direction, '');\n    };\n    _this.handleLeftClear = function () {\n      return _this.handleClear('left');\n    };\n    _this.handleRightClear = function () {\n      return _this.handleClear('right');\n    };\n    _this.onItemSelect = function (direction, selectedKey, checked) {\n      var _this$state2 = _this.state,\n        sourceSelectedKeys = _this$state2.sourceSelectedKeys,\n        targetSelectedKeys = _this$state2.targetSelectedKeys;\n      var holder = direction === 'left' ? _toConsumableArray(sourceSelectedKeys) : _toConsumableArray(targetSelectedKeys);\n      var index = holder.indexOf(selectedKey);\n      if (index > -1) {\n        holder.splice(index, 1);\n      }\n      if (checked) {\n        holder.push(selectedKey);\n      }\n      _this.handleSelectChange(direction, holder);\n      if (!_this.props.selectedKeys) {\n        _this.setStateKeys(direction, holder);\n      }\n    };\n    _this.onLeftItemSelect = function (selectedKey, checked) {\n      return _this.onItemSelect('left', selectedKey, checked);\n    };\n    _this.onRightItemSelect = function (selectedKey, checked) {\n      return _this.onItemSelect('right', selectedKey, checked);\n    };\n    _this.onRightItemRemove = function (selectedKeys) {\n      var _this$props2 = _this.props,\n        _this$props2$targetKe = _this$props2.targetKeys,\n        targetKeys = _this$props2$targetKe === void 0 ? [] : _this$props2$targetKe,\n        onChange = _this$props2.onChange;\n      _this.setStateKeys('right', []);\n      onChange === null || onChange === void 0 ? void 0 : onChange(targetKeys.filter(function (key) {\n        return !selectedKeys.includes(key);\n      }), 'left', _toConsumableArray(selectedKeys));\n    };\n    _this.handleScroll = function (direction, e) {\n      var onScroll = _this.props.onScroll;\n      onScroll === null || onScroll === void 0 ? void 0 : onScroll(direction, e);\n    };\n    _this.handleLeftScroll = function (e) {\n      return _this.handleScroll('left', e);\n    };\n    _this.handleRightScroll = function (e) {\n      return _this.handleScroll('right', e);\n    };\n    // eslint-disable-next-line class-methods-use-this\n    _this.handleListStyle = function (listStyle, direction) {\n      if (typeof listStyle === 'function') {\n        return listStyle({\n          direction: direction\n        });\n      }\n      return listStyle;\n    };\n    var _props$selectedKeys = props.selectedKeys,\n      selectedKeys = _props$selectedKeys === void 0 ? [] : _props$selectedKeys,\n      _props$targetKeys = props.targetKeys,\n      targetKeys = _props$targetKeys === void 0 ? [] : _props$targetKeys;\n    _this.state = {\n      sourceSelectedKeys: selectedKeys.filter(function (key) {\n        return !targetKeys.includes(key);\n      }),\n      targetSelectedKeys: selectedKeys.filter(function (key) {\n        return targetKeys.includes(key);\n      })\n    };\n    return _this;\n  }\n  _createClass(Transfer, [{\n    key: \"getTitles\",\n    value: function getTitles(transferLocale) {\n      var _a, _b;\n      return (_b = (_a = this.props.titles) !== null && _a !== void 0 ? _a : transferLocale.titles) !== null && _b !== void 0 ? _b : [];\n    }\n  }, {\n    key: \"handleSelectChange\",\n    value: function handleSelectChange(direction, holder) {\n      var _this$state3 = this.state,\n        sourceSelectedKeys = _this$state3.sourceSelectedKeys,\n        targetSelectedKeys = _this$state3.targetSelectedKeys;\n      var onSelectChange = this.props.onSelectChange;\n      if (!onSelectChange) {\n        return;\n      }\n      if (direction === 'left') {\n        onSelectChange(holder, targetSelectedKeys);\n      } else {\n        onSelectChange(sourceSelectedKeys, holder);\n      }\n    }\n  }, {\n    key: \"separateDataSource\",\n    value: function separateDataSource() {\n      var _this$props3 = this.props,\n        _this$props3$dataSour = _this$props3.dataSource,\n        dataSource = _this$props3$dataSour === void 0 ? [] : _this$props3$dataSour,\n        rowKey = _this$props3.rowKey,\n        _this$props3$targetKe = _this$props3.targetKeys,\n        targetKeys = _this$props3$targetKe === void 0 ? [] : _this$props3$targetKe;\n      var leftDataSource = [];\n      var rightDataSource = new Array(targetKeys.length);\n      dataSource.forEach(function (record) {\n        if (rowKey) {\n          record = _extends(_extends({}, record), {\n            key: rowKey(record)\n          });\n        }\n        // rightDataSource should be ordered by targetKeys\n        // leftDataSource should be ordered by dataSource\n        var indexOfKey = targetKeys.indexOf(record.key);\n        if (indexOfKey !== -1) {\n          rightDataSource[indexOfKey] = record;\n        } else {\n          leftDataSource.push(record);\n        }\n      });\n      return {\n        leftDataSource: leftDataSource,\n        rightDataSource: rightDataSource\n      };\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      return /*#__PURE__*/React.createElement(LocaleReceiver, {\n        componentName: \"Transfer\",\n        defaultLocale: defaultLocale.Transfer\n      }, function (contextLocale) {\n        return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref3) {\n          var getPrefixCls = _ref3.getPrefixCls,\n            renderEmpty = _ref3.renderEmpty,\n            direction = _ref3.direction;\n          return /*#__PURE__*/React.createElement(FormItemInputContext.Consumer, null, function (_ref4) {\n            var _classNames;\n            var hasFeedback = _ref4.hasFeedback,\n              contextStatus = _ref4.status;\n            var _this2$props = _this2.props,\n              customizePrefixCls = _this2$props.prefixCls,\n              className = _this2$props.className,\n              disabled = _this2$props.disabled,\n              _this2$props$operatio = _this2$props.operations,\n              operations = _this2$props$operatio === void 0 ? [] : _this2$props$operatio,\n              _this2$props$showSear = _this2$props.showSearch,\n              showSearch = _this2$props$showSear === void 0 ? false : _this2$props$showSear,\n              footer = _this2$props.footer,\n              style = _this2$props.style,\n              _this2$props$listStyl = _this2$props.listStyle,\n              listStyle = _this2$props$listStyl === void 0 ? {} : _this2$props$listStyl,\n              operationStyle = _this2$props.operationStyle,\n              filterOption = _this2$props.filterOption,\n              render = _this2$props.render,\n              children = _this2$props.children,\n              showSelectAll = _this2$props.showSelectAll,\n              oneWay = _this2$props.oneWay,\n              pagination = _this2$props.pagination,\n              customStatus = _this2$props.status;\n            var prefixCls = getPrefixCls('transfer', customizePrefixCls);\n            var locale = _this2.getLocale(contextLocale, renderEmpty || defaultRenderEmpty);\n            var _this2$state = _this2.state,\n              sourceSelectedKeys = _this2$state.sourceSelectedKeys,\n              targetSelectedKeys = _this2$state.targetSelectedKeys;\n            var mergedStatus = getMergedStatus(contextStatus, customStatus);\n            var mergedPagination = !children && pagination;\n            var _this2$separateDataSo = _this2.separateDataSource(),\n              leftDataSource = _this2$separateDataSo.leftDataSource,\n              rightDataSource = _this2$separateDataSo.rightDataSource;\n            var leftActive = targetSelectedKeys.length > 0;\n            var rightActive = sourceSelectedKeys.length > 0;\n            var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-customize-list\"), !!children), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), getStatusClassNames(prefixCls, mergedStatus, hasFeedback), className);\n            var titles = _this2.getTitles(locale);\n            var selectAllLabels = _this2.props.selectAllLabels || [];\n            return /*#__PURE__*/React.createElement(\"div\", {\n              className: cls,\n              style: style\n            }, /*#__PURE__*/React.createElement(List, _extends({\n              prefixCls: \"\".concat(prefixCls, \"-list\"),\n              titleText: titles === null || titles === void 0 ? void 0 : titles[0],\n              dataSource: leftDataSource,\n              filterOption: filterOption,\n              style: _this2.handleListStyle(listStyle, 'left'),\n              checkedKeys: sourceSelectedKeys,\n              handleFilter: _this2.handleLeftFilter,\n              handleClear: _this2.handleLeftClear,\n              onItemSelect: _this2.onLeftItemSelect,\n              onItemSelectAll: _this2.onLeftItemSelectAll,\n              render: render,\n              showSearch: showSearch,\n              renderList: children,\n              footer: footer,\n              onScroll: _this2.handleLeftScroll,\n              disabled: disabled,\n              direction: direction === 'rtl' ? 'right' : 'left',\n              showSelectAll: showSelectAll,\n              selectAllLabel: selectAllLabels[0],\n              pagination: mergedPagination\n            }, locale)), /*#__PURE__*/React.createElement(Operation, {\n              className: \"\".concat(prefixCls, \"-operation\"),\n              rightActive: rightActive,\n              rightArrowText: operations[0],\n              moveToRight: _this2.moveToRight,\n              leftActive: leftActive,\n              leftArrowText: operations[1],\n              moveToLeft: _this2.moveToLeft,\n              style: operationStyle,\n              disabled: disabled,\n              direction: direction,\n              oneWay: oneWay\n            }), /*#__PURE__*/React.createElement(List, _extends({\n              prefixCls: \"\".concat(prefixCls, \"-list\"),\n              titleText: titles === null || titles === void 0 ? void 0 : titles[1],\n              dataSource: rightDataSource,\n              filterOption: filterOption,\n              style: _this2.handleListStyle(listStyle, 'right'),\n              checkedKeys: targetSelectedKeys,\n              handleFilter: _this2.handleRightFilter,\n              handleClear: _this2.handleRightClear,\n              onItemSelect: _this2.onRightItemSelect,\n              onItemSelectAll: _this2.onRightItemSelectAll,\n              onItemRemove: _this2.onRightItemRemove,\n              render: render,\n              showSearch: showSearch,\n              renderList: children,\n              footer: footer,\n              onScroll: _this2.handleRightScroll,\n              disabled: disabled,\n              direction: direction === 'rtl' ? 'left' : 'right',\n              showSelectAll: showSelectAll,\n              selectAllLabel: selectAllLabels[1],\n              showRemove: oneWay,\n              pagination: mergedPagination\n            }, locale)));\n          });\n        });\n      });\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(_ref5) {\n      var selectedKeys = _ref5.selectedKeys,\n        targetKeys = _ref5.targetKeys,\n        pagination = _ref5.pagination,\n        children = _ref5.children;\n      if (selectedKeys) {\n        var mergedTargetKeys = targetKeys || [];\n        return {\n          sourceSelectedKeys: selectedKeys.filter(function (key) {\n            return !mergedTargetKeys.includes(key);\n          }),\n          targetSelectedKeys: selectedKeys.filter(function (key) {\n            return mergedTargetKeys.includes(key);\n          })\n        };\n      }\n      process.env.NODE_ENV !== \"production\" ? warning(!pagination || !children, 'Transfer', '`pagination` not support customize render list.') : void 0;\n      return null;\n    }\n  }]);\n  return Transfer;\n}(React.Component); // For high-level customized Transfer @dqaria\nTransfer.List = List;\nTransfer.Operation = Operation;\nTransfer.Search = Search;\nexport default Transfer;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC3E,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,MAAM,MAAM,UAAU;AAC7B,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtDhB,SAAS,CAACe,QAAQ,EAAEC,gBAAgB,CAAC;EACrC,IAAIC,MAAM,GAAGhB,YAAY,CAACc,QAAQ,CAAC;EACnC,SAASA,QAAQA,CAACG,KAAK,EAAE;IACvB,IAAIC,KAAK;IACTrB,eAAe,CAAC,IAAI,EAAEiB,QAAQ,CAAC;IAC/BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,mBAAmB,GAAG,IAAI;IAChCF,KAAK,CAACG,YAAY,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAE;MAC9C,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBJ,KAAK,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;UAC7B,IAAIC,kBAAkB,GAAGD,IAAI,CAACC,kBAAkB;UAChD,OAAO;YACLA,kBAAkB,EAAE,OAAOH,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACG,kBAAkB,IAAI,EAAE,CAAC,GAAGH;UACpF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACLL,KAAK,CAACM,QAAQ,CAAC,UAAUG,KAAK,EAAE;UAC9B,IAAIC,kBAAkB,GAAGD,KAAK,CAACC,kBAAkB;UACjD,OAAO;YACLA,kBAAkB,EAAE,OAAOL,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACK,kBAAkB,IAAI,EAAE,CAAC,GAAGL;UACpF,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC;IACDL,KAAK,CAACW,SAAS,GAAG,UAAUC,cAAc,EAAEC,WAAW,EAAE;MACvD,IAAIC,kBAAkB,GAAGd,KAAK,CAACD,KAAK,CAACgB,MAAM;QACzCA,MAAM,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;MAClE,OAAOpC,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAAC,EAAE;QACrDI,eAAe,EAAEH,WAAW,CAAC,UAAU;MACzC,CAAC,CAAC,EAAEE,MAAM,CAAC;IACb,CAAC;IACDf,KAAK,CAACiB,MAAM,GAAG,UAAUb,SAAS,EAAE;MAClC,IAAIc,WAAW,GAAGlB,KAAK,CAACD,KAAK;QAC3BoB,qBAAqB,GAAGD,WAAW,CAACE,UAAU;QAC9CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC1EE,qBAAqB,GAAGH,WAAW,CAACI,UAAU;QAC9CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC1EE,QAAQ,GAAGL,WAAW,CAACK,QAAQ;MACjC,IAAIC,WAAW,GAAGxB,KAAK,CAACyB,KAAK;QAC3BjB,kBAAkB,GAAGgB,WAAW,CAAChB,kBAAkB;QACnDE,kBAAkB,GAAGc,WAAW,CAACd,kBAAkB;MACrD,IAAIgB,QAAQ,GAAGtB,SAAS,KAAK,OAAO,GAAGI,kBAAkB,GAAGE,kBAAkB;MAC9E;MACA,IAAIiB,WAAW,GAAGD,QAAQ,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAE;QAC/C,OAAO,CAACP,UAAU,CAACQ,IAAI,CAAC,UAAUC,IAAI,EAAE;UACtC,OAAO,CAAC,EAAEF,GAAG,KAAKE,IAAI,CAACF,GAAG,IAAIE,IAAI,CAACC,QAAQ,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;MACA,IAAIC,aAAa,GAAG7B,SAAS,KAAK,OAAO,GAAGuB,WAAW,CAACO,MAAM,CAACd,UAAU,CAAC,GAAGA,UAAU,CAACQ,MAAM,CAAC,UAAUO,SAAS,EAAE;QAClH,OAAO,CAACR,WAAW,CAACS,QAAQ,CAACD,SAAS,CAAC;MACzC,CAAC,CAAC;MACF;MACA,IAAIE,iBAAiB,GAAGjC,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MAChEJ,KAAK,CAACG,YAAY,CAACkC,iBAAiB,EAAE,EAAE,CAAC;MACzCrC,KAAK,CAACsC,kBAAkB,CAACD,iBAAiB,EAAE,EAAE,CAAC;MAC/Cd,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,aAAa,EAAE7B,SAAS,EAAEuB,WAAW,CAAC;IACrG,CAAC;IACD3B,KAAK,CAACuC,UAAU,GAAG,YAAY;MAC7B,OAAOvC,KAAK,CAACiB,MAAM,CAAC,MAAM,CAAC;IAC7B,CAAC;IACDjB,KAAK,CAACwC,WAAW,GAAG,YAAY;MAC9B,OAAOxC,KAAK,CAACiB,MAAM,CAAC,OAAO,CAAC;IAC9B,CAAC;IACDjB,KAAK,CAACyC,eAAe,GAAG,UAAUrC,SAAS,EAAEsC,YAAY,EAAEC,QAAQ,EAAE;MACnE3C,KAAK,CAACG,YAAY,CAACC,SAAS,EAAE,UAAUwC,QAAQ,EAAE;QAChD,IAAIC,iBAAiB,GAAG,EAAE;QAC1B,IAAIF,QAAQ,EAAE;UACZ;UACAE,iBAAiB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,EAAE,CAACd,MAAM,CAACzD,kBAAkB,CAACmE,QAAQ,CAAC,EAAEnE,kBAAkB,CAACiE,YAAY,CAAC,CAAC,CAAC,CAAC;QACpH,CAAC,MAAM;UACL;UACAG,iBAAiB,GAAGD,QAAQ,CAAChB,MAAM,CAAC,UAAUC,GAAG,EAAE;YACjD,OAAO,CAACa,YAAY,CAACN,QAAQ,CAACP,GAAG,CAAC;UACpC,CAAC,CAAC;QACJ;QACA7B,KAAK,CAACsC,kBAAkB,CAAClC,SAAS,EAAEyC,iBAAiB,CAAC;QACtD,OAAOA,iBAAiB;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD7C,KAAK,CAACiD,mBAAmB,GAAG,UAAUP,YAAY,EAAEC,QAAQ,EAAE;MAC5D,OAAO3C,KAAK,CAACyC,eAAe,CAAC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,CAAC;IAC9D,CAAC;IACD3C,KAAK,CAACkD,oBAAoB,GAAG,UAAUR,YAAY,EAAEC,QAAQ,EAAE;MAC7D,OAAO3C,KAAK,CAACyC,eAAe,CAAC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,CAAC;IAC/D,CAAC;IACD3C,KAAK,CAACmD,YAAY,GAAG,UAAU/C,SAAS,EAAEgD,CAAC,EAAE;MAC3C,IAAIC,QAAQ,GAAGrD,KAAK,CAACD,KAAK,CAACsD,QAAQ;MACnC,IAAIC,KAAK,GAAGF,CAAC,CAACG,MAAM,CAACD,KAAK;MAC1BD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACjD,SAAS,EAAEkD,KAAK,CAAC;IAChF,CAAC;IACDtD,KAAK,CAACwD,gBAAgB,GAAG,UAAUJ,CAAC,EAAE;MACpC,OAAOpD,KAAK,CAACmD,YAAY,CAAC,MAAM,EAAEC,CAAC,CAAC;IACtC,CAAC;IACDpD,KAAK,CAACyD,iBAAiB,GAAG,UAAUL,CAAC,EAAE;MACrC,OAAOpD,KAAK,CAACmD,YAAY,CAAC,OAAO,EAAEC,CAAC,CAAC;IACvC,CAAC;IACDpD,KAAK,CAAC0D,WAAW,GAAG,UAAUtD,SAAS,EAAE;MACvC,IAAIiD,QAAQ,GAAGrD,KAAK,CAACD,KAAK,CAACsD,QAAQ;MACnCA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACjD,SAAS,EAAE,EAAE,CAAC;IAC7E,CAAC;IACDJ,KAAK,CAAC2D,eAAe,GAAG,YAAY;MAClC,OAAO3D,KAAK,CAAC0D,WAAW,CAAC,MAAM,CAAC;IAClC,CAAC;IACD1D,KAAK,CAAC4D,gBAAgB,GAAG,YAAY;MACnC,OAAO5D,KAAK,CAAC0D,WAAW,CAAC,OAAO,CAAC;IACnC,CAAC;IACD1D,KAAK,CAAC6D,YAAY,GAAG,UAAUzD,SAAS,EAAE0D,WAAW,EAAEC,OAAO,EAAE;MAC9D,IAAIC,YAAY,GAAGhE,KAAK,CAACyB,KAAK;QAC5BjB,kBAAkB,GAAGwD,YAAY,CAACxD,kBAAkB;QACpDE,kBAAkB,GAAGsD,YAAY,CAACtD,kBAAkB;MACtD,IAAIuD,MAAM,GAAG7D,SAAS,KAAK,MAAM,GAAG3B,kBAAkB,CAAC+B,kBAAkB,CAAC,GAAG/B,kBAAkB,CAACiC,kBAAkB,CAAC;MACnH,IAAIwD,KAAK,GAAGD,MAAM,CAACE,OAAO,CAACL,WAAW,CAAC;MACvC,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACdD,MAAM,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACzB;MACA,IAAIH,OAAO,EAAE;QACXE,MAAM,CAACI,IAAI,CAACP,WAAW,CAAC;MAC1B;MACA9D,KAAK,CAACsC,kBAAkB,CAAClC,SAAS,EAAE6D,MAAM,CAAC;MAC3C,IAAI,CAACjE,KAAK,CAACD,KAAK,CAAC2C,YAAY,EAAE;QAC7B1C,KAAK,CAACG,YAAY,CAACC,SAAS,EAAE6D,MAAM,CAAC;MACvC;IACF,CAAC;IACDjE,KAAK,CAACsE,gBAAgB,GAAG,UAAUR,WAAW,EAAEC,OAAO,EAAE;MACvD,OAAO/D,KAAK,CAAC6D,YAAY,CAAC,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC;IACzD,CAAC;IACD/D,KAAK,CAACuE,iBAAiB,GAAG,UAAUT,WAAW,EAAEC,OAAO,EAAE;MACxD,OAAO/D,KAAK,CAAC6D,YAAY,CAAC,OAAO,EAAEC,WAAW,EAAEC,OAAO,CAAC;IAC1D,CAAC;IACD/D,KAAK,CAACwE,iBAAiB,GAAG,UAAU9B,YAAY,EAAE;MAChD,IAAI+B,YAAY,GAAGzE,KAAK,CAACD,KAAK;QAC5B2E,qBAAqB,GAAGD,YAAY,CAACrD,UAAU;QAC/CA,UAAU,GAAGsD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC1EnD,QAAQ,GAAGkD,YAAY,CAAClD,QAAQ;MAClCvB,KAAK,CAACG,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;MAC/BoB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACH,UAAU,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAE;QAC5F,OAAO,CAACa,YAAY,CAACN,QAAQ,CAACP,GAAG,CAAC;MACpC,CAAC,CAAC,EAAE,MAAM,EAAEpD,kBAAkB,CAACiE,YAAY,CAAC,CAAC;IAC/C,CAAC;IACD1C,KAAK,CAAC2E,YAAY,GAAG,UAAUvE,SAAS,EAAEgD,CAAC,EAAE;MAC3C,IAAIwB,QAAQ,GAAG5E,KAAK,CAACD,KAAK,CAAC6E,QAAQ;MACnCA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACxE,SAAS,EAAEgD,CAAC,CAAC;IAC5E,CAAC;IACDpD,KAAK,CAAC6E,gBAAgB,GAAG,UAAUzB,CAAC,EAAE;MACpC,OAAOpD,KAAK,CAAC2E,YAAY,CAAC,MAAM,EAAEvB,CAAC,CAAC;IACtC,CAAC;IACDpD,KAAK,CAAC8E,iBAAiB,GAAG,UAAU1B,CAAC,EAAE;MACrC,OAAOpD,KAAK,CAAC2E,YAAY,CAAC,OAAO,EAAEvB,CAAC,CAAC;IACvC,CAAC;IACD;IACApD,KAAK,CAAC+E,eAAe,GAAG,UAAUC,SAAS,EAAE5E,SAAS,EAAE;MACtD,IAAI,OAAO4E,SAAS,KAAK,UAAU,EAAE;QACnC,OAAOA,SAAS,CAAC;UACf5E,SAAS,EAAEA;QACb,CAAC,CAAC;MACJ;MACA,OAAO4E,SAAS;IAClB,CAAC;IACD,IAAIC,mBAAmB,GAAGlF,KAAK,CAAC2C,YAAY;MAC1CA,YAAY,GAAGuC,mBAAmB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,mBAAmB;MACxEC,iBAAiB,GAAGnF,KAAK,CAACqB,UAAU;MACpCA,UAAU,GAAG8D,iBAAiB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,iBAAiB;IACpElF,KAAK,CAACyB,KAAK,GAAG;MACZjB,kBAAkB,EAAEkC,YAAY,CAACd,MAAM,CAAC,UAAUC,GAAG,EAAE;QACrD,OAAO,CAACT,UAAU,CAACgB,QAAQ,CAACP,GAAG,CAAC;MAClC,CAAC,CAAC;MACFnB,kBAAkB,EAAEgC,YAAY,CAACd,MAAM,CAAC,UAAUC,GAAG,EAAE;QACrD,OAAOT,UAAU,CAACgB,QAAQ,CAACP,GAAG,CAAC;MACjC,CAAC;IACH,CAAC;IACD,OAAO7B,KAAK;EACd;EACApB,YAAY,CAACgB,QAAQ,EAAE,CAAC;IACtBiC,GAAG,EAAE,WAAW;IAChByB,KAAK,EAAE,SAAS6B,SAASA,CAACvE,cAAc,EAAE;MACxC,IAAIwE,EAAE,EAAEC,EAAE;MACV,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACrF,KAAK,CAACuF,MAAM,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGxE,cAAc,CAAC0E,MAAM,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IACnI;EACF,CAAC,EAAE;IACDxD,GAAG,EAAE,oBAAoB;IACzByB,KAAK,EAAE,SAAShB,kBAAkBA,CAAClC,SAAS,EAAE6D,MAAM,EAAE;MACpD,IAAIsB,YAAY,GAAG,IAAI,CAAC9D,KAAK;QAC3BjB,kBAAkB,GAAG+E,YAAY,CAAC/E,kBAAkB;QACpDE,kBAAkB,GAAG6E,YAAY,CAAC7E,kBAAkB;MACtD,IAAI8E,cAAc,GAAG,IAAI,CAACzF,KAAK,CAACyF,cAAc;MAC9C,IAAI,CAACA,cAAc,EAAE;QACnB;MACF;MACA,IAAIpF,SAAS,KAAK,MAAM,EAAE;QACxBoF,cAAc,CAACvB,MAAM,EAAEvD,kBAAkB,CAAC;MAC5C,CAAC,MAAM;QACL8E,cAAc,CAAChF,kBAAkB,EAAEyD,MAAM,CAAC;MAC5C;IACF;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,oBAAoB;IACzByB,KAAK,EAAE,SAASmC,kBAAkBA,CAAA,EAAG;MACnC,IAAIC,YAAY,GAAG,IAAI,CAAC3F,KAAK;QAC3B4F,qBAAqB,GAAGD,YAAY,CAACpE,UAAU;QAC/CA,UAAU,GAAGqE,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC1EC,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5BC,qBAAqB,GAAGH,YAAY,CAACtE,UAAU;QAC/CA,UAAU,GAAGyE,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;MAC5E,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,eAAe,GAAG,IAAIjD,KAAK,CAAC1B,UAAU,CAAC4E,MAAM,CAAC;MAClD1E,UAAU,CAAC2E,OAAO,CAAC,UAAUC,MAAM,EAAE;QACnC,IAAIN,MAAM,EAAE;UACVM,MAAM,GAAGxH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwH,MAAM,CAAC,EAAE;YACtCrE,GAAG,EAAE+D,MAAM,CAACM,MAAM;UACpB,CAAC,CAAC;QACJ;QACA;QACA;QACA,IAAIC,UAAU,GAAG/E,UAAU,CAAC+C,OAAO,CAAC+B,MAAM,CAACrE,GAAG,CAAC;QAC/C,IAAIsE,UAAU,KAAK,CAAC,CAAC,EAAE;UACrBJ,eAAe,CAACI,UAAU,CAAC,GAAGD,MAAM;QACtC,CAAC,MAAM;UACLJ,cAAc,CAACzB,IAAI,CAAC6B,MAAM,CAAC;QAC7B;MACF,CAAC,CAAC;MACF,OAAO;QACLJ,cAAc,EAAEA,cAAc;QAC9BC,eAAe,EAAEA;MACnB,CAAC;IACH;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,QAAQ;IACbyB,KAAK,EAAE,SAAS8C,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,OAAO,aAAarH,KAAK,CAACsH,aAAa,CAAClH,cAAc,EAAE;QACtDmH,aAAa,EAAE,UAAU;QACzBlH,aAAa,EAAEA,aAAa,CAACO;MAC/B,CAAC,EAAE,UAAU4G,aAAa,EAAE;QAC1B,OAAO,aAAaxH,KAAK,CAACsH,aAAa,CAACrH,cAAc,EAAE,IAAI,EAAE,UAAUwH,KAAK,EAAE;UAC7E,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;YACnC7F,WAAW,GAAG4F,KAAK,CAAC5F,WAAW;YAC/BT,SAAS,GAAGqG,KAAK,CAACrG,SAAS;UAC7B,OAAO,aAAapB,KAAK,CAACsH,aAAa,CAACnH,oBAAoB,CAACwH,QAAQ,EAAE,IAAI,EAAE,UAAUC,KAAK,EAAE;YAC5F,IAAIC,WAAW;YACf,IAAIC,WAAW,GAAGF,KAAK,CAACE,WAAW;cACjCC,aAAa,GAAGH,KAAK,CAACI,MAAM;YAC9B,IAAIC,YAAY,GAAGZ,MAAM,CAACtG,KAAK;cAC7BmH,kBAAkB,GAAGD,YAAY,CAACE,SAAS;cAC3CC,SAAS,GAAGH,YAAY,CAACG,SAAS;cAClCpF,QAAQ,GAAGiF,YAAY,CAACjF,QAAQ;cAChCqF,qBAAqB,GAAGJ,YAAY,CAACK,UAAU;cAC/CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;cAC1EE,qBAAqB,GAAGN,YAAY,CAACO,UAAU;cAC/CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;cAC7EE,MAAM,GAAGR,YAAY,CAACQ,MAAM;cAC5BC,KAAK,GAAGT,YAAY,CAACS,KAAK;cAC1BC,qBAAqB,GAAGV,YAAY,CAACjC,SAAS;cAC9CA,SAAS,GAAG2C,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;cACzEC,cAAc,GAAGX,YAAY,CAACW,cAAc;cAC5CC,YAAY,GAAGZ,YAAY,CAACY,YAAY;cACxCzB,MAAM,GAAGa,YAAY,CAACb,MAAM;cAC5B0B,QAAQ,GAAGb,YAAY,CAACa,QAAQ;cAChCC,aAAa,GAAGd,YAAY,CAACc,aAAa;cAC1CC,MAAM,GAAGf,YAAY,CAACe,MAAM;cAC5BC,UAAU,GAAGhB,YAAY,CAACgB,UAAU;cACpCC,YAAY,GAAGjB,YAAY,CAACD,MAAM;YACpC,IAAIG,SAAS,GAAGT,YAAY,CAAC,UAAU,EAAEQ,kBAAkB,CAAC;YAC5D,IAAInG,MAAM,GAAGsF,MAAM,CAAC1F,SAAS,CAAC6F,aAAa,EAAE3F,WAAW,IAAI3B,kBAAkB,CAAC;YAC/E,IAAIiJ,YAAY,GAAG9B,MAAM,CAAC5E,KAAK;cAC7BjB,kBAAkB,GAAG2H,YAAY,CAAC3H,kBAAkB;cACpDE,kBAAkB,GAAGyH,YAAY,CAACzH,kBAAkB;YACtD,IAAI0H,YAAY,GAAG9I,eAAe,CAACyH,aAAa,EAAEmB,YAAY,CAAC;YAC/D,IAAIG,gBAAgB,GAAG,CAACP,QAAQ,IAAIG,UAAU;YAC9C,IAAIK,qBAAqB,GAAGjC,MAAM,CAACZ,kBAAkB,CAAC,CAAC;cACrDK,cAAc,GAAGwC,qBAAqB,CAACxC,cAAc;cACrDC,eAAe,GAAGuC,qBAAqB,CAACvC,eAAe;YACzD,IAAIwC,UAAU,GAAG7H,kBAAkB,CAACsF,MAAM,GAAG,CAAC;YAC9C,IAAIwC,WAAW,GAAGhI,kBAAkB,CAACwF,MAAM,GAAG,CAAC;YAC/C,IAAIyC,GAAG,GAAG1J,UAAU,CAACoI,SAAS,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAErI,eAAe,CAACqI,WAAW,EAAE,EAAE,CAAC3E,MAAM,CAACiF,SAAS,EAAE,WAAW,CAAC,EAAEnF,QAAQ,CAAC,EAAExD,eAAe,CAACqI,WAAW,EAAE,EAAE,CAAC3E,MAAM,CAACiF,SAAS,EAAE,iBAAiB,CAAC,EAAE,CAAC,CAACW,QAAQ,CAAC,EAAEtJ,eAAe,CAACqI,WAAW,EAAE,EAAE,CAAC3E,MAAM,CAACiF,SAAS,EAAE,MAAM,CAAC,EAAE/G,SAAS,KAAK,KAAK,CAAC,EAAEyG,WAAW,GAAGtH,mBAAmB,CAAC4H,SAAS,EAAEiB,YAAY,EAAEtB,WAAW,CAAC,EAAEM,SAAS,CAAC;YACrX,IAAI9B,MAAM,GAAGe,MAAM,CAAClB,SAAS,CAACpE,MAAM,CAAC;YACrC,IAAI2H,eAAe,GAAGrC,MAAM,CAACtG,KAAK,CAAC2I,eAAe,IAAI,EAAE;YACxD,OAAO,aAAa1J,KAAK,CAACsH,aAAa,CAAC,KAAK,EAAE;cAC7Cc,SAAS,EAAEqB,GAAG;cACdf,KAAK,EAAEA;YACT,CAAC,EAAE,aAAa1I,KAAK,CAACsH,aAAa,CAAC7G,IAAI,EAAEf,QAAQ,CAAC;cACjDyI,SAAS,EAAE,EAAE,CAACjF,MAAM,CAACiF,SAAS,EAAE,OAAO,CAAC;cACxCwB,SAAS,EAAErD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;cACpEhE,UAAU,EAAEwE,cAAc;cAC1B+B,YAAY,EAAEA,YAAY;cAC1BH,KAAK,EAAErB,MAAM,CAACtB,eAAe,CAACC,SAAS,EAAE,MAAM,CAAC;cAChD4D,WAAW,EAAEpI,kBAAkB;cAC/B2C,YAAY,EAAEkD,MAAM,CAAC7C,gBAAgB;cACrCE,WAAW,EAAE2C,MAAM,CAAC1C,eAAe;cACnCE,YAAY,EAAEwC,MAAM,CAAC/B,gBAAgB;cACrC7B,eAAe,EAAE4D,MAAM,CAACpD,mBAAmB;cAC3CmD,MAAM,EAAEA,MAAM;cACdoB,UAAU,EAAEA,UAAU;cACtBqB,UAAU,EAAEf,QAAQ;cACpBL,MAAM,EAAEA,MAAM;cACd7C,QAAQ,EAAEyB,MAAM,CAACxB,gBAAgB;cACjC7C,QAAQ,EAAEA,QAAQ;cAClB5B,SAAS,EAAEA,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;cACjD2H,aAAa,EAAEA,aAAa;cAC5Be,cAAc,EAAEJ,eAAe,CAAC,CAAC,CAAC;cAClCT,UAAU,EAAEI;YACd,CAAC,EAAEtH,MAAM,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACsH,aAAa,CAAC5G,SAAS,EAAE;cACvD0H,SAAS,EAAE,EAAE,CAAClF,MAAM,CAACiF,SAAS,EAAE,YAAY,CAAC;cAC7CqB,WAAW,EAAEA,WAAW;cACxBO,cAAc,EAAEzB,UAAU,CAAC,CAAC,CAAC;cAC7B9E,WAAW,EAAE6D,MAAM,CAAC7D,WAAW;cAC/B+F,UAAU,EAAEA,UAAU;cACtBS,aAAa,EAAE1B,UAAU,CAAC,CAAC,CAAC;cAC5B/E,UAAU,EAAE8D,MAAM,CAAC9D,UAAU;cAC7BmF,KAAK,EAAEE,cAAc;cACrB5F,QAAQ,EAAEA,QAAQ;cAClB5B,SAAS,EAAEA,SAAS;cACpB4H,MAAM,EAAEA;YACV,CAAC,CAAC,EAAE,aAAahJ,KAAK,CAACsH,aAAa,CAAC7G,IAAI,EAAEf,QAAQ,CAAC;cAClDyI,SAAS,EAAE,EAAE,CAACjF,MAAM,CAACiF,SAAS,EAAE,OAAO,CAAC;cACxCwB,SAAS,EAAErD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;cACpEhE,UAAU,EAAEyE,eAAe;cAC3B8B,YAAY,EAAEA,YAAY;cAC1BH,KAAK,EAAErB,MAAM,CAACtB,eAAe,CAACC,SAAS,EAAE,OAAO,CAAC;cACjD4D,WAAW,EAAElI,kBAAkB;cAC/ByC,YAAY,EAAEkD,MAAM,CAAC5C,iBAAiB;cACtCC,WAAW,EAAE2C,MAAM,CAACzC,gBAAgB;cACpCC,YAAY,EAAEwC,MAAM,CAAC9B,iBAAiB;cACtC9B,eAAe,EAAE4D,MAAM,CAACnD,oBAAoB;cAC5C+F,YAAY,EAAE5C,MAAM,CAAC7B,iBAAiB;cACtC4B,MAAM,EAAEA,MAAM;cACdoB,UAAU,EAAEA,UAAU;cACtBqB,UAAU,EAAEf,QAAQ;cACpBL,MAAM,EAAEA,MAAM;cACd7C,QAAQ,EAAEyB,MAAM,CAACvB,iBAAiB;cAClC9C,QAAQ,EAAEA,QAAQ;cAClB5B,SAAS,EAAEA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;cACjD2H,aAAa,EAAEA,aAAa;cAC5Be,cAAc,EAAEJ,eAAe,CAAC,CAAC,CAAC;cAClCQ,UAAU,EAAElB,MAAM;cAClBC,UAAU,EAAEI;YACd,CAAC,EAAEtH,MAAM,CAAC,CAAC,CAAC;UACd,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,CAAC;IACHc,GAAG,EAAE,0BAA0B;IAC/ByB,KAAK,EAAE,SAAS6F,wBAAwBA,CAACC,KAAK,EAAE;MAC9C,IAAI1G,YAAY,GAAG0G,KAAK,CAAC1G,YAAY;QACnCtB,UAAU,GAAGgI,KAAK,CAAChI,UAAU;QAC7B6G,UAAU,GAAGmB,KAAK,CAACnB,UAAU;QAC7BH,QAAQ,GAAGsB,KAAK,CAACtB,QAAQ;MAC3B,IAAIpF,YAAY,EAAE;QAChB,IAAI2G,gBAAgB,GAAGjI,UAAU,IAAI,EAAE;QACvC,OAAO;UACLZ,kBAAkB,EAAEkC,YAAY,CAACd,MAAM,CAAC,UAAUC,GAAG,EAAE;YACrD,OAAO,CAACwH,gBAAgB,CAACjH,QAAQ,CAACP,GAAG,CAAC;UACxC,CAAC,CAAC;UACFnB,kBAAkB,EAAEgC,YAAY,CAACd,MAAM,CAAC,UAAUC,GAAG,EAAE;YACrD,OAAOwH,gBAAgB,CAACjH,QAAQ,CAACP,GAAG,CAAC;UACvC,CAAC;QACH,CAAC;MACH;MACAyH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhK,OAAO,CAAC,CAACyI,UAAU,IAAI,CAACH,QAAQ,EAAE,UAAU,EAAE,iDAAiD,CAAC,GAAG,KAAK,CAAC;MACjJ,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOlI,QAAQ;AACjB,CAAC,CAACZ,KAAK,CAACyK,SAAS,CAAC,CAAC,CAAC;AACpB7J,QAAQ,CAACH,IAAI,GAAGA,IAAI;AACpBG,QAAQ,CAACF,SAAS,GAAGA,SAAS;AAC9BE,QAAQ,CAACD,MAAM,GAAGA,MAAM;AACxB,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}