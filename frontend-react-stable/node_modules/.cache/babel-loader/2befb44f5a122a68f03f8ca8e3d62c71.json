{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcMonths = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar utcMonth = (0, _interval.default)(function (date) {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, function (start, end) {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, function (date) {\n  return date.getUTCMonth();\n});\nvar _default = utcMonth;\nexports.default = _default;\nvar utcMonths = utcMonth.range;\nexports.utcMonths = utcMonths;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcMonths", "default", "_interval", "_interopRequireDefault", "require", "obj", "__esModule", "utcMonth", "date", "setUTCDate", "setUTCHours", "step", "setUTCMonth", "getUTCMonth", "start", "end", "getUTCFullYear", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/utcMonth.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcMonths = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar utcMonth = (0, _interval.default)(function (date) {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, function (start, end) {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, function (date) {\n  return date.getUTCMonth();\n});\nvar _default = utcMonth;\nexports.default = _default;\nvar utcMonths = utcMonth.range;\nexports.utcMonths = utcMonths;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE5C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIE,QAAQ,GAAG,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;EACpDA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;EAClBD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;EACvBH,IAAI,CAACI,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC7C,CAAC,EAAE,UAAUG,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACF,WAAW,CAAC,CAAC,GAAGC,KAAK,CAACD,WAAW,CAAC,CAAC,GAAG,CAACE,GAAG,CAACC,cAAc,CAAC,CAAC,GAAGF,KAAK,CAACE,cAAc,CAAC,CAAC,IAAI,EAAE;AACvG,CAAC,EAAE,UAAUR,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACK,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC;AACF,IAAII,QAAQ,GAAGV,QAAQ;AACvBT,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,SAAS,GAAGO,QAAQ,CAACW,KAAK;AAC9BpB,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}