{"ast": null, "code": "export function getPlaceholder(picker, locale, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.yearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.quarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.monthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.weekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.placeholder;\n  }\n  return locale.lang.placeholder;\n}\nexport function getRangePlaceholder(picker, locale, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.rangeYearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.rangeQuarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.rangeMonthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.rangeWeekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.rangePlaceholder;\n  }\n  return locale.lang.rangePlaceholder;\n}\nexport function transPlacement2DropdownAlign(direction, placement) {\n  var overflow = {\n    adjustX: 1,\n    adjustY: 1\n  };\n  switch (placement) {\n    case 'bottomLeft':\n      {\n        return {\n          points: ['tl', 'bl'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n    case 'bottomRight':\n      {\n        return {\n          points: ['tr', 'br'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n    case 'topLeft':\n      {\n        return {\n          points: ['bl', 'tl'],\n          offset: [0, -4],\n          overflow: overflow\n        };\n      }\n    case 'topRight':\n      {\n        return {\n          points: ['br', 'tr'],\n          offset: [0, -4],\n          overflow: overflow\n        };\n      }\n    default:\n      {\n        return {\n          points: direction === 'rtl' ? ['tr', 'br'] : ['tl', 'bl'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n  }\n}", "map": {"version": 3, "names": ["getPlaceholder", "picker", "locale", "customizePlaceholder", "undefined", "lang", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "timePickerLocale", "placeholder", "getRangePlaceholder", "rangeYearPlaceholder", "rangeQuarterPlaceholder", "rangeMonthPlaceholder", "rangeWeekPlaceholder", "rangePlaceholder", "transPlacement2DropdownAlign", "direction", "placement", "overflow", "adjustX", "adjustY", "points", "offset"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/date-picker/util.js"], "sourcesContent": ["export function getPlaceholder(picker, locale, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.yearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.quarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.monthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.weekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.placeholder;\n  }\n  return locale.lang.placeholder;\n}\nexport function getRangePlaceholder(picker, locale, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.rangeYearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.rangeQuarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.rangeMonthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.rangeWeekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.rangePlaceholder;\n  }\n  return locale.lang.rangePlaceholder;\n}\nexport function transPlacement2DropdownAlign(direction, placement) {\n  var overflow = {\n    adjustX: 1,\n    adjustY: 1\n  };\n  switch (placement) {\n    case 'bottomLeft':\n      {\n        return {\n          points: ['tl', 'bl'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n    case 'bottomRight':\n      {\n        return {\n          points: ['tr', 'br'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n    case 'topLeft':\n      {\n        return {\n          points: ['bl', 'tl'],\n          offset: [0, -4],\n          overflow: overflow\n        };\n      }\n    case 'topRight':\n      {\n        return {\n          points: ['br', 'tr'],\n          offset: [0, -4],\n          overflow: overflow\n        };\n      }\n    default:\n      {\n        return {\n          points: direction === 'rtl' ? ['tr', 'br'] : ['tl', 'bl'],\n          offset: [0, 4],\n          overflow: overflow\n        };\n      }\n  }\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,oBAAoB,EAAE;EACnE,IAAIA,oBAAoB,KAAKC,SAAS,EAAE;IACtC,OAAOD,oBAAoB;EAC7B;EACA,IAAIF,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACG,IAAI,CAACC,eAAe,EAAE;IACpD,OAAOJ,MAAM,CAACG,IAAI,CAACC,eAAe;EACpC;EACA,IAAIL,MAAM,KAAK,SAAS,IAAIC,MAAM,CAACG,IAAI,CAACE,kBAAkB,EAAE;IAC1D,OAAOL,MAAM,CAACG,IAAI,CAACE,kBAAkB;EACvC;EACA,IAAIN,MAAM,KAAK,OAAO,IAAIC,MAAM,CAACG,IAAI,CAACG,gBAAgB,EAAE;IACtD,OAAON,MAAM,CAACG,IAAI,CAACG,gBAAgB;EACrC;EACA,IAAIP,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACG,IAAI,CAACI,eAAe,EAAE;IACpD,OAAOP,MAAM,CAACG,IAAI,CAACI,eAAe;EACpC;EACA,IAAIR,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACQ,gBAAgB,CAACC,WAAW,EAAE;IAC5D,OAAOT,MAAM,CAACQ,gBAAgB,CAACC,WAAW;EAC5C;EACA,OAAOT,MAAM,CAACG,IAAI,CAACM,WAAW;AAChC;AACA,OAAO,SAASC,mBAAmBA,CAACX,MAAM,EAAEC,MAAM,EAAEC,oBAAoB,EAAE;EACxE,IAAIA,oBAAoB,KAAKC,SAAS,EAAE;IACtC,OAAOD,oBAAoB;EAC7B;EACA,IAAIF,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACG,IAAI,CAACC,eAAe,EAAE;IACpD,OAAOJ,MAAM,CAACG,IAAI,CAACQ,oBAAoB;EACzC;EACA,IAAIZ,MAAM,KAAK,SAAS,IAAIC,MAAM,CAACG,IAAI,CAACE,kBAAkB,EAAE;IAC1D,OAAOL,MAAM,CAACG,IAAI,CAACS,uBAAuB;EAC5C;EACA,IAAIb,MAAM,KAAK,OAAO,IAAIC,MAAM,CAACG,IAAI,CAACG,gBAAgB,EAAE;IACtD,OAAON,MAAM,CAACG,IAAI,CAACU,qBAAqB;EAC1C;EACA,IAAId,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACG,IAAI,CAACI,eAAe,EAAE;IACpD,OAAOP,MAAM,CAACG,IAAI,CAACW,oBAAoB;EACzC;EACA,IAAIf,MAAM,KAAK,MAAM,IAAIC,MAAM,CAACQ,gBAAgB,CAACC,WAAW,EAAE;IAC5D,OAAOT,MAAM,CAACQ,gBAAgB,CAACO,gBAAgB;EACjD;EACA,OAAOf,MAAM,CAACG,IAAI,CAACY,gBAAgB;AACrC;AACA,OAAO,SAASC,4BAA4BA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACjE,IAAIC,QAAQ,GAAG;IACbC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC;EACD,QAAQH,SAAS;IACf,KAAK,YAAY;MACf;QACE,OAAO;UACLI,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;UACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACdJ,QAAQ,EAAEA;QACZ,CAAC;MACH;IACF,KAAK,aAAa;MAChB;QACE,OAAO;UACLG,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;UACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACdJ,QAAQ,EAAEA;QACZ,CAAC;MACH;IACF,KAAK,SAAS;MACZ;QACE,OAAO;UACLG,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;UACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACfJ,QAAQ,EAAEA;QACZ,CAAC;MACH;IACF,KAAK,UAAU;MACb;QACE,OAAO;UACLG,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;UACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACfJ,QAAQ,EAAEA;QACZ,CAAC;MACH;IACF;MACE;QACE,OAAO;UACLG,MAAM,EAAEL,SAAS,KAAK,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;UACzDM,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACdJ,QAAQ,EAAEA;QACZ,CAAC;MACH;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}