{"ast": null, "code": "import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Simple = function Simple() {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('empty-img-simple');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    fillRule: \"nonzero\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    className: \"\".concat(prefixCls, \"-path\")\n  }))));\n};\nexport default Simple;", "map": {"version": 3, "names": ["React", "ConfigContext", "Simple", "_React$useContext", "useContext", "getPrefixCls", "prefixCls", "createElement", "className", "width", "height", "viewBox", "xmlns", "transform", "fill", "fillRule", "concat", "cx", "cy", "rx", "ry", "d"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/empty/simple.js"], "sourcesContent": ["import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Simple = function Simple() {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('empty-img-simple');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    fillRule: \"nonzero\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    className: \"\".concat(prefixCls, \"-path\")\n  }))));\n};\nexport default Simple;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAACH,aAAa,CAAC;IACrDI,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIC,SAAS,GAAGD,YAAY,CAAC,kBAAkB,CAAC;EAChD,OAAO,aAAaL,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEF,SAAS;IACpBG,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC,EAAE,aAAaZ,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE;IACvCM,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAaf,KAAK,CAACO,aAAa,CAAC,SAAS,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC;IAC3CW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE;IACxCC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,IAAI,CAAC;IACrCS,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAaf,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC1Cc,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3Cc,CAAC,EAAE,+OAA+O;IAClPb,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,OAAO;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}