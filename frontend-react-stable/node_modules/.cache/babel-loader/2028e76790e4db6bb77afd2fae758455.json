{"ast": null, "code": "var INTERNAL_KEY_PREFIX = 'RC_TABLE_KEY';\nfunction toArray(arr) {\n  if (arr === undefined || arr === null) {\n    return [];\n  }\n  return Array.isArray(arr) ? arr : [arr];\n}\nexport function getPathValue(record, path) {\n  // Skip if path is empty\n  if (!path && typeof path !== 'number') {\n    return record;\n  }\n  var pathList = toArray(path);\n  var current = record;\n  for (var i = 0; i < pathList.length; i += 1) {\n    if (!current) {\n      return null;\n    }\n    var prop = pathList[i];\n    current = current[prop];\n  }\n  return current;\n}\nexport function getColumnsKey(columns) {\n  var columnKeys = [];\n  var keys = {};\n  columns.forEach(function (column) {\n    var _ref = column || {},\n      key = _ref.key,\n      dataIndex = _ref.dataIndex;\n    var mergedKey = key || toArray(dataIndex).join('-') || INTERNAL_KEY_PREFIX;\n    while (keys[mergedKey]) {\n      mergedKey = \"\".concat(mergedKey, \"_next\");\n    }\n    keys[mergedKey] = true;\n    columnKeys.push(mergedKey);\n  });\n  return columnKeys;\n}\nexport function validateValue(val) {\n  return val !== null && val !== undefined;\n}", "map": {"version": 3, "names": ["INTERNAL_KEY_PREFIX", "toArray", "arr", "undefined", "Array", "isArray", "getPathValue", "record", "path", "pathList", "current", "i", "length", "prop", "getColumnsKey", "columns", "columnKeys", "keys", "for<PERSON>ach", "column", "_ref", "key", "dataIndex", "mergedKey", "join", "concat", "push", "validate<PERSON><PERSON>ue", "val"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/utils/valueUtil.js"], "sourcesContent": ["var INTERNAL_KEY_PREFIX = 'RC_TABLE_KEY';\n\nfunction toArray(arr) {\n  if (arr === undefined || arr === null) {\n    return [];\n  }\n\n  return Array.isArray(arr) ? arr : [arr];\n}\n\nexport function getPathValue(record, path) {\n  // Skip if path is empty\n  if (!path && typeof path !== 'number') {\n    return record;\n  }\n\n  var pathList = toArray(path);\n  var current = record;\n\n  for (var i = 0; i < pathList.length; i += 1) {\n    if (!current) {\n      return null;\n    }\n\n    var prop = pathList[i];\n    current = current[prop];\n  }\n\n  return current;\n}\nexport function getColumnsKey(columns) {\n  var columnKeys = [];\n  var keys = {};\n  columns.forEach(function (column) {\n    var _ref = column || {},\n        key = _ref.key,\n        dataIndex = _ref.dataIndex;\n\n    var mergedKey = key || toArray(dataIndex).join('-') || INTERNAL_KEY_PREFIX;\n\n    while (keys[mergedKey]) {\n      mergedKey = \"\".concat(mergedKey, \"_next\");\n    }\n\n    keys[mergedKey] = true;\n    columnKeys.push(mergedKey);\n  });\n  return columnKeys;\n}\nexport function validateValue(val) {\n  return val !== null && val !== undefined;\n}"], "mappings": "AAAA,IAAIA,mBAAmB,GAAG,cAAc;AAExC,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;IACrC,OAAO,EAAE;EACX;EAEA,OAAOE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AAEA,OAAO,SAASI,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACzC;EACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAOD,MAAM;EACf;EAEA,IAAIE,QAAQ,GAAGR,OAAO,CAACO,IAAI,CAAC;EAC5B,IAAIE,OAAO,GAAGH,MAAM;EAEpB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAI,CAACD,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;IAEA,IAAIG,IAAI,GAAGJ,QAAQ,CAACE,CAAC,CAAC;IACtBD,OAAO,GAAGA,OAAO,CAACG,IAAI,CAAC;EACzB;EAEA,OAAOH,OAAO;AAChB;AACA,OAAO,SAASI,aAAaA,CAACC,OAAO,EAAE;EACrC,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,IAAI,GAAG,CAAC,CAAC;EACbF,OAAO,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAE;IAChC,IAAIC,IAAI,GAAGD,MAAM,IAAI,CAAC,CAAC;MACnBE,GAAG,GAAGD,IAAI,CAACC,GAAG;MACdC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAE9B,IAAIC,SAAS,GAAGF,GAAG,IAAIpB,OAAO,CAACqB,SAAS,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,IAAIxB,mBAAmB;IAE1E,OAAOiB,IAAI,CAACM,SAAS,CAAC,EAAE;MACtBA,SAAS,GAAG,EAAE,CAACE,MAAM,CAACF,SAAS,EAAE,OAAO,CAAC;IAC3C;IAEAN,IAAI,CAACM,SAAS,CAAC,GAAG,IAAI;IACtBP,UAAU,CAACU,IAAI,CAACH,SAAS,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOP,UAAU;AACnB;AACA,OAAO,SAASW,aAAaA,CAACC,GAAG,EAAE;EACjC,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKzB,SAAS;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}