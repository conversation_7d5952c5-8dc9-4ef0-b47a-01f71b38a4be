{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = greatestIndex;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _maxIndex = _interopRequireDefault(require(\"./maxIndex.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction greatestIndex(values, compare = _ascending.default) {\n  if (compare.length === 1) return (0, _maxIndex.default)(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "greatestIndex", "_ascending", "_interopRequireDefault", "require", "_maxIndex", "obj", "__esModule", "values", "compare", "length", "maxValue", "max", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/greatestIndex.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = greatestIndex;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _maxIndex = _interopRequireDefault(require(\"./maxIndex.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction greatestIndex(values, compare = _ascending.default) {\n  if (compare.length === 1) return (0, _maxIndex.default)(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n\n  for (const value of values) {\n    ++index;\n\n    if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n\n  return max;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,aAAa;AAE/B,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,SAAS,GAAGF,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,aAAaA,CAACO,MAAM,EAAEC,OAAO,GAAGP,UAAU,CAACF,OAAO,EAAE;EAC3D,IAAIS,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAEL,SAAS,CAACL,OAAO,EAAEQ,MAAM,EAAEC,OAAO,CAAC;EACxE,IAAIE,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,MAAMd,KAAK,IAAIS,MAAM,EAAE;IAC1B,EAAEK,KAAK;IAEP,IAAID,GAAG,GAAG,CAAC,GAAGH,OAAO,CAACV,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAAGU,OAAO,CAACV,KAAK,EAAEY,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxEA,QAAQ,GAAGZ,KAAK;MAChBa,GAAG,GAAGC,KAAK;IACb;EACF;EAEA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}