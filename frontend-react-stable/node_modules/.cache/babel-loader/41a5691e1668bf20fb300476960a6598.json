{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport ReactIcon from './IconBase';\nimport { getTwoToneColor, setTwoToneColor } from './twoTonePrimaryColor';\nimport { normalizeTwoToneColors } from '../utils';\n// Initial setting\n// should move it to antd main repo?\nsetTwoToneColor('#1890ff');\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var className = props.className,\n    icon = props.icon,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    twoToneColor = props.twoToneColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), _defineProperty(_classNames, \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), _classNames), className);\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "_defineProperty", "_objectWithoutProperties", "_excluded", "React", "classNames", "Context", "ReactIcon", "getTwoToneColor", "setTwoToneColor", "normalizeTwoToneColors", "Icon", "forwardRef", "props", "ref", "_classNames", "className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor", "restProps", "_React$useContext", "useContext", "_React$useContext$pre", "prefixCls", "rootClassName", "classString", "concat", "name", "iconTabIndex", "undefined", "svgStyle", "msTransform", "transform", "_normalizeTwoToneColo", "_normalizeTwoToneColo2", "primaryColor", "secondaryColor", "createElement", "role", "style", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/components/AntdIcon.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport ReactIcon from './IconBase';\nimport { getTwoToneColor, setTwoToneColor } from './twoTonePrimaryColor';\nimport { normalizeTwoToneColors } from '../utils';\n// Initial setting\n// should move it to antd main repo?\nsetTwoToneColor('#1890ff');\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var className = props.className,\n    icon = props.icon,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    twoToneColor = props.twoToneColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), _defineProperty(_classNames, \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), _classNames), className);\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC;AAC9F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,EAAEC,eAAe,QAAQ,uBAAuB;AACxE,SAASC,sBAAsB,QAAQ,UAAU;AACjD;AACA;AACAD,eAAe,CAAC,SAAS,CAAC;AAC1B,IAAIE,IAAI,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC7BC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,IAAI,GAAGL,KAAK,CAACK,IAAI;IACjBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,SAAS,GAAGrB,wBAAwB,CAACW,KAAK,EAAEV,SAAS,CAAC;EACxD,IAAIqB,iBAAiB,GAAGpB,KAAK,CAACqB,UAAU,CAACnB,OAAO,CAAC;IAC/CoB,qBAAqB,GAAGF,iBAAiB,CAACG,SAAS;IACnDA,SAAS,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,qBAAqB;IAChFE,aAAa,GAAGJ,iBAAiB,CAACI,aAAa;EACjD,IAAIC,WAAW,GAAGxB,UAAU,CAACuB,aAAa,EAAED,SAAS,GAAGZ,WAAW,GAAG,CAAC,CAAC,EAAEd,eAAe,CAACc,WAAW,EAAE,EAAE,CAACe,MAAM,CAACH,SAAS,EAAE,GAAG,CAAC,CAACG,MAAM,CAACb,IAAI,CAACc,IAAI,CAAC,EAAE,CAAC,CAACd,IAAI,CAACc,IAAI,CAAC,EAAE9B,eAAe,CAACc,WAAW,EAAE,EAAE,CAACe,MAAM,CAACH,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAACT,IAAI,IAAID,IAAI,CAACc,IAAI,KAAK,SAAS,CAAC,EAAEhB,WAAW,GAAGC,SAAS,CAAC;EAC1R,IAAIgB,YAAY,GAAGZ,QAAQ;EAC3B,IAAIY,YAAY,KAAKC,SAAS,IAAIZ,OAAO,EAAE;IACzCW,YAAY,GAAG,CAAC,CAAC;EACnB;EACA,IAAIE,QAAQ,GAAGf,MAAM,GAAG;IACtBgB,WAAW,EAAE,SAAS,CAACL,MAAM,CAACX,MAAM,EAAE,MAAM,CAAC;IAC7CiB,SAAS,EAAE,SAAS,CAACN,MAAM,CAACX,MAAM,EAAE,MAAM;EAC5C,CAAC,GAAGc,SAAS;EACb,IAAII,qBAAqB,GAAG3B,sBAAsB,CAACY,YAAY,CAAC;IAC9DgB,sBAAsB,GAAGtC,cAAc,CAACqC,qBAAqB,EAAE,CAAC,CAAC;IACjEE,YAAY,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACxCE,cAAc,GAAGF,sBAAsB,CAAC,CAAC,CAAC;EAC5C,OAAO,aAAalC,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAE1C,aAAa,CAACA,aAAa,CAAC;IAC1E2C,IAAI,EAAE,KAAK;IACX,YAAY,EAAEzB,IAAI,CAACc;EACrB,CAAC,EAAER,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;IACjBT,GAAG,EAAEA,GAAG;IACRM,QAAQ,EAAEY,YAAY;IACtBX,OAAO,EAAEA,OAAO;IAChBL,SAAS,EAAEa;EACb,CAAC,CAAC,EAAE,aAAazB,KAAK,CAACqC,aAAa,CAAClC,SAAS,EAAE;IAC9CU,IAAI,EAAEA,IAAI;IACVsB,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA,cAAc;IAC9BG,KAAK,EAAET;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFvB,IAAI,CAACiC,WAAW,GAAG,UAAU;AAC7BjC,IAAI,CAACH,eAAe,GAAGA,eAAe;AACtCG,IAAI,CAACF,eAAe,GAAGA,eAAe;AACtC,eAAeE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}