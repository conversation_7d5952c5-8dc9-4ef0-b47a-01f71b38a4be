{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"title\", \"eventKey\", \"children\"],\n  _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { parseChildren } from './utils/nodeUtil';\nimport { MenuContext } from './context/MenuContext';\nimport { useFullPath, useMeasure } from './context/PathContext';\nvar InternalMenuItemGroup = function InternalMenuItemGroup(_ref) {\n  var className = _ref.className,\n    title = _ref.title,\n    eventKey = _ref.eventKey,\n    children = _ref.children,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var groupPrefixCls = \"\".concat(prefixCls, \"-item-group\");\n  return /*#__PURE__*/React.createElement(\"li\", _extends({}, restProps, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    className: classNames(groupPrefixCls, className)\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(groupPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(groupPrefixCls, \"-list\")\n  }, children));\n};\nexport default function MenuItemGroup(_ref2) {\n  var children = _ref2.children,\n    props = _objectWithoutProperties(_ref2, _excluded2);\n  var connectedKeyPath = useFullPath(props.eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n  var measure = useMeasure();\n  if (measure) {\n    return childList;\n  }\n  return /*#__PURE__*/React.createElement(InternalMenuItemGroup, omit(props, ['warnKey']), childList);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "classNames", "omit", "parse<PERSON><PERSON><PERSON>n", "MenuContext", "useFullPath", "useMeasure", "InternalMenuItemGroup", "_ref", "className", "title", "eventKey", "children", "restProps", "_React$useContext", "useContext", "prefixCls", "groupPrefixCls", "concat", "createElement", "onClick", "e", "stopPropagation", "undefined", "MenuItemGroup", "_ref2", "props", "connectedKeyPath", "childList", "measure"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/MenuItemGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"title\", \"eventKey\", \"children\"],\n    _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { parseChildren } from './utils/nodeUtil';\nimport { MenuContext } from './context/MenuContext';\nimport { useFullPath, useMeasure } from './context/PathContext';\n\nvar InternalMenuItemGroup = function InternalMenuItemGroup(_ref) {\n  var className = _ref.className,\n      title = _ref.title,\n      eventKey = _ref.eventKey,\n      children = _ref.children,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var _React$useContext = React.useContext(MenuContext),\n      prefixCls = _React$useContext.prefixCls;\n\n  var groupPrefixCls = \"\".concat(prefixCls, \"-item-group\");\n  return /*#__PURE__*/React.createElement(\"li\", _extends({}, restProps, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    className: classNames(groupPrefixCls, className)\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(groupPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(groupPrefixCls, \"-list\")\n  }, children));\n};\n\nexport default function MenuItemGroup(_ref2) {\n  var children = _ref2.children,\n      props = _objectWithoutProperties(_ref2, _excluded2);\n\n  var connectedKeyPath = useFullPath(props.eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n  var measure = useMeasure();\n\n  if (measure) {\n    return childList;\n  }\n\n  return /*#__PURE__*/React.createElement(InternalMenuItemGroup, omit(props, ['warnKey']), childList);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;EAC1DC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,WAAW,EAAEC,UAAU,QAAQ,uBAAuB;AAE/D,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EAC/D,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,SAAS,GAAGhB,wBAAwB,CAACW,IAAI,EAAEV,SAAS,CAAC;EAEzD,IAAIgB,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACX,WAAW,CAAC;IACjDY,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACF,SAAS,EAAE,aAAa,CAAC;EACxD,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEiB,SAAS,EAAE;IACpEO,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;IAC5B,CAAC;IACDb,SAAS,EAAER,UAAU,CAACgB,cAAc,EAAER,SAAS;EACjD,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC1CV,SAAS,EAAE,EAAE,CAACS,MAAM,CAACD,cAAc,EAAE,QAAQ,CAAC;IAC9CP,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGa;EAC7C,CAAC,EAAEb,KAAK,CAAC,EAAE,aAAaV,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;IAChDV,SAAS,EAAE,EAAE,CAACS,MAAM,CAACD,cAAc,EAAE,OAAO;EAC9C,CAAC,EAAEL,QAAQ,CAAC,CAAC;AACf,CAAC;AAED,eAAe,SAASY,aAAaA,CAACC,KAAK,EAAE;EAC3C,IAAIb,QAAQ,GAAGa,KAAK,CAACb,QAAQ;IACzBc,KAAK,GAAG7B,wBAAwB,CAAC4B,KAAK,EAAE1B,UAAU,CAAC;EAEvD,IAAI4B,gBAAgB,GAAGtB,WAAW,CAACqB,KAAK,CAACf,QAAQ,CAAC;EAClD,IAAIiB,SAAS,GAAGzB,aAAa,CAACS,QAAQ,EAAEe,gBAAgB,CAAC;EACzD,IAAIE,OAAO,GAAGvB,UAAU,CAAC,CAAC;EAE1B,IAAIuB,OAAO,EAAE;IACX,OAAOD,SAAS;EAClB;EAEA,OAAO,aAAa5B,KAAK,CAACmB,aAAa,CAACZ,qBAAqB,EAAEL,IAAI,CAACwB,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAEE,SAAS,CAAC;AACrG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}