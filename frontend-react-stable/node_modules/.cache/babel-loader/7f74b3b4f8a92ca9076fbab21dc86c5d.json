{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport * as React from 'react';\nimport { ConfigConsumer } from '../../config-provider';\nexport default function createUseNotification(getNotificationInstance, getRCNoticeProps) {\n  var useNotification = function useNotification() {\n    // We can only get content by render\n    var getPrefixCls;\n    // We create a proxy to handle delay created instance\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = useRCNotification(proxy),\n      _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('notification', customizePrefixCls);\n      getNotificationInstance(_extends(_extends({}, args), {\n        prefixCls: mergedPrefixCls\n      }), function (_ref) {\n        var prefixCls = _ref.prefixCls,\n          instance = _ref.instance;\n        innerInstance = instance;\n        hookNotify(getRCNoticeProps(args, prefixCls));\n      });\n    }\n    // Fill functions\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    ['success', 'info', 'warning', 'error'].forEach(function (type) {\n      hookApiRef.current[type] = function (args) {\n        return hookApiRef.current.open(_extends(_extends({}, args), {\n          type: type\n        }));\n      };\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      return holder;\n    })];\n  };\n  return useNotification;\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "useRCNotification", "React", "ConfigConsumer", "createUseNotification", "getNotificationInstance", "getRCNoticeProps", "useNotification", "getPrefixCls", "innerInstance", "proxy", "add", "noticeProps", "<PERSON><PERSON><PERSON><PERSON>", "component", "_useRCNotification", "_useRCNotification2", "hookNotify", "holder", "notify", "args", "customizePrefixCls", "prefixCls", "mergedPrefixCls", "_ref", "instance", "hookApiRef", "useRef", "current", "open", "for<PERSON>ach", "type", "createElement", "key", "context"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/notification/hooks/useNotification.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport * as React from 'react';\nimport { ConfigConsumer } from '../../config-provider';\nexport default function createUseNotification(getNotificationInstance, getRCNoticeProps) {\n  var useNotification = function useNotification() {\n    // We can only get content by render\n    var getPrefixCls;\n    // We create a proxy to handle delay created instance\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = useRCNotification(proxy),\n      _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('notification', customizePrefixCls);\n      getNotificationInstance(_extends(_extends({}, args), {\n        prefixCls: mergedPrefixCls\n      }), function (_ref) {\n        var prefixCls = _ref.prefixCls,\n          instance = _ref.instance;\n        innerInstance = instance;\n        hookNotify(getRCNoticeProps(args, prefixCls));\n      });\n    }\n    // Fill functions\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    ['success', 'info', 'warning', 'error'].forEach(function (type) {\n      hookApiRef.current[type] = function (args) {\n        return hookApiRef.current.open(_extends(_extends({}, args), {\n          type: type\n        }));\n      };\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      return holder;\n    })];\n  };\n  return useNotification;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,uBAAuB;AACtD,eAAe,SAASC,qBAAqBA,CAACC,uBAAuB,EAAEC,gBAAgB,EAAE;EACvF,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C;IACA,IAAIC,YAAY;IAChB;IACA,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,KAAK,GAAG;MACVC,GAAG,EAAE,SAASA,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAE;QAC7CJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,SAAS,CAACH,GAAG,CAACC,WAAW,EAAEC,cAAc,CAAC;MACxH;IACF,CAAC;IACD,IAAIE,kBAAkB,GAAGd,iBAAiB,CAACS,KAAK,CAAC;MAC/CM,mBAAmB,GAAGhB,cAAc,CAACe,kBAAkB,EAAE,CAAC,CAAC;MAC3DE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;MACnCE,MAAM,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IACjC,SAASG,MAAMA,CAACC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAGD,IAAI,CAACE,SAAS;MACvC,IAAIC,eAAe,GAAGf,YAAY,CAAC,cAAc,EAAEa,kBAAkB,CAAC;MACtEhB,uBAAuB,CAACN,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqB,IAAI,CAAC,EAAE;QACnDE,SAAS,EAAEC;MACb,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;QAClB,IAAIF,SAAS,GAAGE,IAAI,CAACF,SAAS;UAC5BG,QAAQ,GAAGD,IAAI,CAACC,QAAQ;QAC1BhB,aAAa,GAAGgB,QAAQ;QACxBR,UAAU,CAACX,gBAAgB,CAACc,IAAI,EAAEE,SAAS,CAAC,CAAC;MAC/C,CAAC,CAAC;IACJ;IACA;IACA,IAAII,UAAU,GAAGxB,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC,CAAC;IACjCD,UAAU,CAACE,OAAO,CAACC,IAAI,GAAGV,MAAM;IAChC,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAACW,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC9DL,UAAU,CAACE,OAAO,CAACG,IAAI,CAAC,GAAG,UAAUX,IAAI,EAAE;QACzC,OAAOM,UAAU,CAACE,OAAO,CAACC,IAAI,CAAC9B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqB,IAAI,CAAC,EAAE;UAC1DW,IAAI,EAAEA;QACR,CAAC,CAAC,CAAC;MACL,CAAC;IACH,CAAC,CAAC;IACF,OAAO,CAACL,UAAU,CAACE,OAAO,EAAE,aAAa1B,KAAK,CAAC8B,aAAa,CAAC7B,cAAc,EAAE;MAC3E8B,GAAG,EAAE;IACP,CAAC,EAAE,UAAUC,OAAO,EAAE;MACpB1B,YAAY,GAAG0B,OAAO,CAAC1B,YAAY;MACnC,OAAOU,MAAM;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAOX,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}