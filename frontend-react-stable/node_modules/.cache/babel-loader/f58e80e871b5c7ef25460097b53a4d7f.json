{"ast": null, "code": "import * as React from 'react';\nimport Field from './Field';\nimport List from './List';\nimport useForm from './useForm';\nimport FieldForm from './Form';\nimport { FormProvider } from './FormContext';\nimport FieldContext from './FieldContext';\nimport ListContext from './ListContext';\nimport useWatch from './useWatch';\nvar InternalForm = /*#__PURE__*/React.forwardRef(FieldForm);\nvar RefForm = InternalForm;\nRefForm.FormProvider = FormProvider;\nRefForm.Field = Field;\nRefForm.List = List;\nRefForm.useForm = useForm;\nRefForm.useWatch = useWatch;\nexport { Field, List, useForm, FormProvider, FieldContext, ListContext, useWatch };\nexport default RefForm;", "map": {"version": 3, "names": ["React", "Field", "List", "useForm", "FieldForm", "FormProvider", "FieldContext", "ListContext", "useWatch", "InternalForm", "forwardRef", "RefForm"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/index.js"], "sourcesContent": ["import * as React from 'react';\nimport Field from './Field';\nimport List from './List';\nimport useForm from './useForm';\nimport FieldForm from './Form';\nimport { FormProvider } from './FormContext';\nimport FieldContext from './FieldContext';\nimport ListContext from './ListContext';\nimport useWatch from './useWatch';\nvar InternalForm = /*#__PURE__*/React.forwardRef(FieldForm);\nvar RefForm = InternalForm;\nRefForm.FormProvider = FormProvider;\nRefForm.Field = Field;\nRefForm.List = List;\nRefForm.useForm = useForm;\nRefForm.useWatch = useWatch;\nexport { Field, List, useForm, FormProvider, FieldContext, ListContext, useWatch };\nexport default RefForm;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,QAAQ;AAC9B,SAASC,YAAY,QAAQ,eAAe;AAC5C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,IAAIC,YAAY,GAAG,aAAaT,KAAK,CAACU,UAAU,CAACN,SAAS,CAAC;AAC3D,IAAIO,OAAO,GAAGF,YAAY;AAC1BE,OAAO,CAACN,YAAY,GAAGA,YAAY;AACnCM,OAAO,CAACV,KAAK,GAAGA,KAAK;AACrBU,OAAO,CAACT,IAAI,GAAGA,IAAI;AACnBS,OAAO,CAACR,OAAO,GAAGA,OAAO;AACzBQ,OAAO,CAACH,QAAQ,GAAGA,QAAQ;AAC3B,SAASP,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEE,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,QAAQ;AAChF,eAAeG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}