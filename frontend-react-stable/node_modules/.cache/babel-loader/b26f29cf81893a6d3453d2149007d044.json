{"ast": null, "code": "/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar TreeNode = function TreeNode() {\n  return null;\n};\nexport default TreeNode;", "map": {"version": 3, "names": ["TreeNode"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/TreeNode.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar TreeNode = function TreeNode() {\n  return null;\n};\n\nexport default TreeNode;"], "mappings": "AAAA;;AAEA;AACA,IAAIA,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO,IAAI;AACb,CAAC;AAED,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}