{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.sum = sum;\nvar _none = _interopRequireDefault(require(\"./none.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series) {\n  var sums = series.map(sum);\n  return (0, _none.default)(series).sort(function (a, b) {\n    return sums[a] - sums[b];\n  });\n}\nfunction sum(series) {\n  var s = 0,\n    i = -1,\n    n = series.length,\n    v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "sum", "_none", "_interopRequireDefault", "require", "obj", "__esModule", "series", "sums", "map", "sort", "a", "b", "s", "i", "n", "length", "v"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/order/ascending.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.sum = sum;\n\nvar _none = _interopRequireDefault(require(\"./none.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series) {\n  var sums = series.map(sum);\n  return (0, _none.default)(series).sort(function (a, b) {\n    return sums[a] - sums[b];\n  });\n}\n\nfunction sum(series) {\n  var s = 0,\n      i = -1,\n      n = series.length,\n      v;\n\n  while (++i < n) if (v = +series[i][1]) s += v;\n\n  return s;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,GAAG,GAAGA,GAAG;AAEjB,IAAIC,KAAK,GAAGC,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,QAAQA,CAACO,MAAM,EAAE;EACxB,IAAIC,IAAI,GAAGD,MAAM,CAACE,GAAG,CAACR,GAAG,CAAC;EAC1B,OAAO,CAAC,CAAC,EAAEC,KAAK,CAACH,OAAO,EAAEQ,MAAM,CAAC,CAACG,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACrD,OAAOJ,IAAI,CAACG,CAAC,CAAC,GAAGH,IAAI,CAACI,CAAC,CAAC;EAC1B,CAAC,CAAC;AACJ;AAEA,SAASX,GAAGA,CAACM,MAAM,EAAE;EACnB,IAAIM,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAGR,MAAM,CAACS,MAAM;IACjBC,CAAC;EAEL,OAAO,EAAEH,CAAC,GAAGC,CAAC,EAAE,IAAIE,CAAC,GAAG,CAACV,MAAM,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,IAAII,CAAC;EAE7C,OAAOJ,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}