{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ordinal;\nexports.implicit = void 0;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _init = require(\"./init.js\");\nconst implicit = Symbol(\"implicit\");\nexports.implicit = implicit;\nfunction ordinal() {\n  var index = new _index.InternMap(),\n    domain = [],\n    range = [],\n    unknown = implicit;\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new _index.InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return ordinal(domain, range).unknown(unknown);\n  };\n  _init.initRange.apply(scale, arguments);\n  return scale;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ordinal", "implicit", "_index", "require", "_init", "Symbol", "index", "InternMap", "domain", "range", "unknown", "scale", "d", "i", "get", "undefined", "set", "push", "length", "_", "arguments", "slice", "has", "Array", "from", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/ordinal.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ordinal;\nexports.implicit = void 0;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _init = require(\"./init.js\");\n\nconst implicit = Symbol(\"implicit\");\nexports.implicit = implicit;\n\nfunction ordinal() {\n  var index = new _index.InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n\n    return range[i % range.length];\n  }\n\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new _index.InternMap();\n\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n\n    return scale;\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function () {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  _init.initRange.apply(scale, arguments);\n\n  return scale;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,OAAO;AACzBH,OAAO,CAACI,QAAQ,GAAG,KAAK,CAAC;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,MAAMF,QAAQ,GAAGI,MAAM,CAAC,UAAU,CAAC;AACnCR,OAAO,CAACI,QAAQ,GAAGA,QAAQ;AAE3B,SAASD,OAAOA,CAAA,EAAG;EACjB,IAAIM,KAAK,GAAG,IAAIJ,MAAM,CAACK,SAAS,CAAC,CAAC;IAC9BC,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,EAAE;IACVC,OAAO,GAAGT,QAAQ;EAEtB,SAASU,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIC,CAAC,GAAGP,KAAK,CAACQ,GAAG,CAACF,CAAC,CAAC;IAEpB,IAAIC,CAAC,KAAKE,SAAS,EAAE;MACnB,IAAIL,OAAO,KAAKT,QAAQ,EAAE,OAAOS,OAAO;MACxCJ,KAAK,CAACU,GAAG,CAACJ,CAAC,EAAEC,CAAC,GAAGL,MAAM,CAACS,IAAI,CAACL,CAAC,CAAC,GAAG,CAAC,CAAC;IACtC;IAEA,OAAOH,KAAK,CAACI,CAAC,GAAGJ,KAAK,CAACS,MAAM,CAAC;EAChC;EAEAP,KAAK,CAACH,MAAM,GAAG,UAAUW,CAAC,EAAE;IAC1B,IAAI,CAACC,SAAS,CAACF,MAAM,EAAE,OAAOV,MAAM,CAACa,KAAK,CAAC,CAAC;IAC5Cb,MAAM,GAAG,EAAE,EAAEF,KAAK,GAAG,IAAIJ,MAAM,CAACK,SAAS,CAAC,CAAC;IAE3C,KAAK,MAAMT,KAAK,IAAIqB,CAAC,EAAE;MACrB,IAAIb,KAAK,CAACgB,GAAG,CAACxB,KAAK,CAAC,EAAE;MACtBQ,KAAK,CAACU,GAAG,CAAClB,KAAK,EAAEU,MAAM,CAACS,IAAI,CAACnB,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1C;IAEA,OAAOa,KAAK;EACd,CAAC;EAEDA,KAAK,CAACF,KAAK,GAAG,UAAUU,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACF,MAAM,IAAIT,KAAK,GAAGc,KAAK,CAACC,IAAI,CAACL,CAAC,CAAC,EAAER,KAAK,IAAIF,KAAK,CAACY,KAAK,CAAC,CAAC;EAC1E,CAAC;EAEDV,KAAK,CAACD,OAAO,GAAG,UAAUS,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACF,MAAM,IAAIR,OAAO,GAAGS,CAAC,EAAER,KAAK,IAAID,OAAO;EAC1D,CAAC;EAEDC,KAAK,CAACc,IAAI,GAAG,YAAY;IACvB,OAAOzB,OAAO,CAACQ,MAAM,EAAEC,KAAK,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EAChD,CAAC;EAEDN,KAAK,CAACsB,SAAS,CAACC,KAAK,CAAChB,KAAK,EAAES,SAAS,CAAC;EAEvC,OAAOT,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}