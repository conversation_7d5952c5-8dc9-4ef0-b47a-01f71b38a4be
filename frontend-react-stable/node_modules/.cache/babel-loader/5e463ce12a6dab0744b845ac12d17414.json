{"ast": null, "code": "'use strict';\n\nvar inspect = require('object-inspect');\nvar $TypeError = require('es-errors/type');\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n  /** @type {typeof list | NonNullable<(typeof list)['next']>} */\n  var prev = list;\n  /** @type {(typeof list)['next']} */\n  var curr;\n  // eslint-disable-next-line eqeqeq\n  for (; (curr = prev.next) != null; prev = curr) {\n    if (curr.key === key) {\n      prev.next = curr.next;\n      if (!isDelete) {\n        // eslint-disable-next-line no-extra-parens\n        curr.next = /** @type {NonNullable<typeof list.next>} */list.next;\n        list.next = curr; // eslint-disable-line no-param-reassign\n      }\n      return curr;\n    }\n  }\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n  if (!objects) {\n    return void undefined;\n  }\n  var node = listGetNode(objects, key);\n  return node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n  var node = listGetNode(objects, key);\n  if (node) {\n    node.value = value;\n  } else {\n    // Prepend the new node to the beginning of the list\n    objects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */{\n      // eslint-disable-line no-param-reassign, no-extra-parens\n      key: key,\n      next: objects.next,\n      value: value\n    };\n  }\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n  if (!objects) {\n    return false;\n  }\n  return !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n  if (objects) {\n    return listGetNode(objects, key, true);\n  }\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n  /** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n  /** @typedef {Parameters<Channel['get']>[0]} K */\n  /** @typedef {Parameters<Channel['set']>[1]} V */\n\n  /** @type {import('./list.d.ts').RootNode<V, K> | undefined} */var $o;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      var root = $o && $o.next;\n      var deletedNode = listDelete($o, key);\n      if (deletedNode && root && root === deletedNode) {\n        $o = void undefined;\n      }\n      return !!deletedNode;\n    },\n    get: function (key) {\n      return listGet($o, key);\n    },\n    has: function (key) {\n      return listHas($o, key);\n    },\n    set: function (key, value) {\n      if (!$o) {\n        // Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n        $o = {\n          next: void undefined\n        };\n      }\n      // eslint-disable-next-line no-extra-parens\n      listSet(/** @type {NonNullable<typeof $o>} */$o, key, value);\n    }\n  };\n  // @ts-expect-error TODO: figure out why this is erroring\n  return channel;\n};", "map": {"version": 3, "names": ["inspect", "require", "$TypeError", "listGetNode", "list", "key", "isDelete", "prev", "curr", "next", "listGet", "objects", "undefined", "node", "value", "listSet", "listHas", "listDelete", "module", "exports", "getSideChannelList", "$o", "channel", "assert", "has", "delete", "root", "deletedNode", "get", "set"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/side-channel-list/index.js"], "sourcesContent": ["'use strict';\n\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAEvC,IAAIC,UAAU,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG,SAAAA,CAAUC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAChD;EACA,IAAIC,IAAI,GAAGH,IAAI;EACf;EACA,IAAII,IAAI;EACR;EACA,OAAO,CAACA,IAAI,GAAGD,IAAI,CAACE,IAAI,KAAK,IAAI,EAAEF,IAAI,GAAGC,IAAI,EAAE;IAC/C,IAAIA,IAAI,CAACH,GAAG,KAAKA,GAAG,EAAE;MACrBE,IAAI,CAACE,IAAI,GAAGD,IAAI,CAACC,IAAI;MACrB,IAAI,CAACH,QAAQ,EAAE;QACd;QACAE,IAAI,CAACC,IAAI,GAAG,4CAA8CL,IAAI,CAACK,IAAK;QACpEL,IAAI,CAACK,IAAI,GAAGD,IAAI,CAAC,CAAC;MACnB;MACA,OAAOA,IAAI;IACZ;EACD;AACD,CAAC;;AAED;AACA,IAAIE,OAAO,GAAG,SAAAA,CAAUC,OAAO,EAAEN,GAAG,EAAE;EACrC,IAAI,CAACM,OAAO,EAAE;IACb,OAAO,KAAKC,SAAS;EACtB;EACA,IAAIC,IAAI,GAAGV,WAAW,CAACQ,OAAO,EAAEN,GAAG,CAAC;EACpC,OAAOQ,IAAI,IAAIA,IAAI,CAACC,KAAK;AAC1B,CAAC;AACD;AACA,IAAIC,OAAO,GAAG,SAAAA,CAAUJ,OAAO,EAAEN,GAAG,EAAES,KAAK,EAAE;EAC5C,IAAID,IAAI,GAAGV,WAAW,CAACQ,OAAO,EAAEN,GAAG,CAAC;EACpC,IAAIQ,IAAI,EAAE;IACTA,IAAI,CAACC,KAAK,GAAGA,KAAK;EACnB,CAAC,MAAM;IACN;IACAH,OAAO,CAACF,IAAI,GAAG,uEAAyE;MAAE;MACzFJ,GAAG,EAAEA,GAAG;MACRI,IAAI,EAAEE,OAAO,CAACF,IAAI;MAClBK,KAAK,EAAEA;IACR,CAAE;EACH;AACD,CAAC;AACD;AACA,IAAIE,OAAO,GAAG,SAAAA,CAAUL,OAAO,EAAEN,GAAG,EAAE;EACrC,IAAI,CAACM,OAAO,EAAE;IACb,OAAO,KAAK;EACb;EACA,OAAO,CAAC,CAACR,WAAW,CAACQ,OAAO,EAAEN,GAAG,CAAC;AACnC,CAAC;AACD;AACA;AACA,IAAIY,UAAU,GAAG,SAAAA,CAAUN,OAAO,EAAEN,GAAG,EAAE;EACxC,IAAIM,OAAO,EAAE;IACZ,OAAOR,WAAW,CAACQ,OAAO,EAAEN,GAAG,EAAE,IAAI,CAAC;EACvC;AACD,CAAC;;AAED;AACAa,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAAA,EAAG;EAC9C;EACA;EACA;;EAEA,+DAAgE,IAAIC,EAAE;;EAEtE;EACA,IAAIC,OAAO,GAAG;IACbC,MAAM,EAAE,SAAAA,CAAUlB,GAAG,EAAE;MACtB,IAAI,CAACiB,OAAO,CAACE,GAAG,CAACnB,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIH,UAAU,CAAC,gCAAgC,GAAGF,OAAO,CAACK,GAAG,CAAC,CAAC;MACtE;IACD,CAAC;IACD,QAAQ,EAAE,SAAAoB,CAAUpB,GAAG,EAAE;MACxB,IAAIqB,IAAI,GAAGL,EAAE,IAAIA,EAAE,CAACZ,IAAI;MACxB,IAAIkB,WAAW,GAAGV,UAAU,CAACI,EAAE,EAAEhB,GAAG,CAAC;MACrC,IAAIsB,WAAW,IAAID,IAAI,IAAIA,IAAI,KAAKC,WAAW,EAAE;QAChDN,EAAE,GAAG,KAAKT,SAAS;MACpB;MACA,OAAO,CAAC,CAACe,WAAW;IACrB,CAAC;IACDC,GAAG,EAAE,SAAAA,CAAUvB,GAAG,EAAE;MACnB,OAAOK,OAAO,CAACW,EAAE,EAAEhB,GAAG,CAAC;IACxB,CAAC;IACDmB,GAAG,EAAE,SAAAA,CAAUnB,GAAG,EAAE;MACnB,OAAOW,OAAO,CAACK,EAAE,EAAEhB,GAAG,CAAC;IACxB,CAAC;IACDwB,GAAG,EAAE,SAAAA,CAAUxB,GAAG,EAAES,KAAK,EAAE;MAC1B,IAAI,CAACO,EAAE,EAAE;QACR;QACAA,EAAE,GAAG;UACJZ,IAAI,EAAE,KAAKG;QACZ,CAAC;MACF;MACA;MACAG,OAAO,CAAC,qCAAuCM,EAAE,EAAGhB,GAAG,EAAES,KAAK,CAAC;IAChE;EACD,CAAC;EACD;EACA,OAAOQ,OAAO;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}