{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = threshold;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _init = require(\"./init.js\");\nfunction threshold() {\n  var domain = [0.5],\n    range = [0, 1],\n    unknown,\n    n = 1;\n  function scale(x) {\n    return x != null && x <= x ? range[(0, _index.bisect)(domain, x, 0, n)] : unknown;\n  }\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return threshold().domain(domain).range(range).unknown(unknown);\n  };\n  return _init.initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "threshold", "_index", "require", "_init", "domain", "range", "unknown", "n", "scale", "x", "bisect", "_", "arguments", "length", "Array", "from", "Math", "min", "slice", "invertExtent", "y", "i", "indexOf", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/threshold.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = threshold;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0, _index.bisect)(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function () {\n    return threshold().domain(domain).range(range).unknown(unknown);\n  };\n\n  return _init.initRange.apply(scale, arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASF,SAASA,CAAA,EAAG;EACnB,IAAII,MAAM,GAAG,CAAC,GAAG,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,OAAO;IACPC,CAAC,GAAG,CAAC;EAET,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAACS,MAAM,EAAEN,MAAM,EAAEK,CAAC,EAAE,CAAC,EAAEF,CAAC,CAAC,CAAC,GAAGD,OAAO;EACnF;EAEAE,KAAK,CAACJ,MAAM,GAAG,UAAUO,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIT,MAAM,GAAGU,KAAK,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAEJ,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACb,MAAM,CAACS,MAAM,EAAER,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAEL,KAAK,IAAIJ,MAAM,CAACc,KAAK,CAAC,CAAC;EAC3H,CAAC;EAEDV,KAAK,CAACH,KAAK,GAAG,UAAUM,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIR,KAAK,GAAGS,KAAK,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAEJ,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACb,MAAM,CAACS,MAAM,EAAER,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAEL,KAAK,IAAIH,KAAK,CAACa,KAAK,CAAC,CAAC;EACzH,CAAC;EAEDV,KAAK,CAACW,YAAY,GAAG,UAAUC,CAAC,EAAE;IAChC,IAAIC,CAAC,GAAGhB,KAAK,CAACiB,OAAO,CAACF,CAAC,CAAC;IACxB,OAAO,CAAChB,MAAM,CAACiB,CAAC,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAACiB,CAAC,CAAC,CAAC;EACnC,CAAC;EAEDb,KAAK,CAACF,OAAO,GAAG,UAAUK,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIP,OAAO,GAAGK,CAAC,EAAEH,KAAK,IAAIF,OAAO;EAC1D,CAAC;EAEDE,KAAK,CAACe,IAAI,GAAG,YAAY;IACvB,OAAOvB,SAAS,CAAC,CAAC,CAACI,MAAM,CAACA,MAAM,CAAC,CAACC,KAAK,CAACA,KAAK,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EACjE,CAAC;EAED,OAAOH,KAAK,CAACqB,SAAS,CAACC,KAAK,CAACjB,KAAK,EAAEI,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}