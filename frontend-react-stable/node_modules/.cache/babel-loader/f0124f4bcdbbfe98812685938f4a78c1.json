{"ast": null, "code": "'use strict';\n\n/** @type {import('./range')} */\nmodule.exports = RangeError;", "map": {"version": 3, "names": ["module", "exports", "RangeError"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/es-errors/range.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./range')} */\nmodule.exports = RangeError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}