{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport default function useOffset(min, max, step, markList, allowCross, pushable) {\n  var formatRangeValue = React.useCallback(function (val) {\n    var formatNextValue = isFinite(val) ? val : min;\n    formatNextValue = Math.min(max, val);\n    formatNextValue = Math.max(min, formatNextValue);\n    return formatNextValue;\n  }, [min, max]);\n  var formatStepValue = React.useCallback(function (val) {\n    if (step !== null) {\n      var stepValue = min + Math.round((formatRangeValue(val) - min) / step) * step; // Cut number in case to be like 0.30000000000000004\n\n      var getDecimal = function getDecimal(num) {\n        return (String(num).split('.')[1] || '').length;\n      };\n      var maxDecimal = Math.max(getDecimal(step), getDecimal(max), getDecimal(min));\n      var fixedValue = Number(stepValue.toFixed(maxDecimal));\n      return min <= fixedValue && fixedValue <= max ? fixedValue : null;\n    }\n    return null;\n  }, [step, min, max, formatRangeValue]);\n  var formatValue = React.useCallback(function (val) {\n    var formatNextValue = formatRangeValue(val); // List align values\n\n    var alignValues = markList.map(function (mark) {\n      return mark.value;\n    });\n    if (step !== null) {\n      alignValues.push(formatStepValue(val));\n    } // min & max\n\n    alignValues.push(min, max); // Align with marks\n\n    var closeValue = alignValues[0];\n    var closeDist = max - min;\n    alignValues.forEach(function (alignValue) {\n      var dist = Math.abs(formatNextValue - alignValue);\n      if (dist <= closeDist) {\n        closeValue = alignValue;\n        closeDist = dist;\n      }\n    });\n    return closeValue;\n  }, [min, max, markList, step, formatRangeValue, formatStepValue]); // ========================== Offset ==========================\n  // Single Value\n\n  var offsetValue = function offsetValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    if (typeof offset === 'number') {\n      var nextValue;\n      var originValue = values[valueIndex]; // Only used for `dist` mode\n\n      var targetDistValue = originValue + offset; // Compare next step value & mark value which is best match\n\n      var potentialValues = [];\n      markList.forEach(function (mark) {\n        potentialValues.push(mark.value);\n      }); // Min & Max\n\n      potentialValues.push(min, max); // In case origin value is align with mark but not with step\n\n      potentialValues.push(formatStepValue(originValue)); // Put offset step value also\n\n      var sign = offset > 0 ? 1 : -1;\n      if (mode === 'unit') {\n        potentialValues.push(formatStepValue(originValue + sign * step));\n      } else {\n        potentialValues.push(formatStepValue(targetDistValue));\n      } // Find close one\n\n      potentialValues = potentialValues.filter(function (val) {\n        return val !== null;\n      }) // Remove reverse value\n      .filter(function (val) {\n        return offset < 0 ? val <= originValue : val >= originValue;\n      });\n      if (mode === 'unit') {\n        // `unit` mode can not contain itself\n        potentialValues = potentialValues.filter(function (val) {\n          return val !== originValue;\n        });\n      }\n      var compareValue = mode === 'unit' ? originValue : targetDistValue;\n      nextValue = potentialValues[0];\n      var valueDist = Math.abs(nextValue - compareValue);\n      potentialValues.forEach(function (potentialValue) {\n        var dist = Math.abs(potentialValue - compareValue);\n        if (dist < valueDist) {\n          nextValue = potentialValue;\n          valueDist = dist;\n        }\n      }); // Out of range will back to range\n\n      if (nextValue === undefined) {\n        return offset < 0 ? min : max;\n      } // `dist` mode\n\n      if (mode === 'dist') {\n        return nextValue;\n      } // `unit` mode may need another round\n\n      if (Math.abs(offset) > 1) {\n        var cloneValues = _toConsumableArray(values);\n        cloneValues[valueIndex] = nextValue;\n        return offsetValue(cloneValues, offset - sign, valueIndex, mode);\n      }\n      return nextValue;\n    } else if (offset === 'min') {\n      return min;\n    } else if (offset === 'max') {\n      return max;\n    }\n  };\n  /** Same as `offsetValue` but return `changed` mark to tell value changed */\n\n  var offsetChangedValue = function offsetChangedValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var originValue = values[valueIndex];\n    var nextValue = offsetValue(values, offset, valueIndex, mode);\n    return {\n      value: nextValue,\n      changed: nextValue !== originValue\n    };\n  };\n  var needPush = function needPush(dist) {\n    return pushable === null && dist === 0 || typeof pushable === 'number' && dist < pushable;\n  }; // Values\n\n  var offsetValues = function offsetValues(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var nextValues = values.map(formatValue);\n    var originValue = nextValues[valueIndex];\n    var nextValue = offsetValue(nextValues, offset, valueIndex, mode);\n    nextValues[valueIndex] = nextValue;\n    if (allowCross === false) {\n      // >>>>> Allow Cross\n      var pushNum = pushable || 0; // ============ AllowCross ===============\n\n      if (valueIndex > 0 && nextValues[valueIndex - 1] !== originValue) {\n        nextValues[valueIndex] = Math.max(nextValues[valueIndex], nextValues[valueIndex - 1] + pushNum);\n      }\n      if (valueIndex < nextValues.length - 1 && nextValues[valueIndex + 1] !== originValue) {\n        nextValues[valueIndex] = Math.min(nextValues[valueIndex], nextValues[valueIndex + 1] - pushNum);\n      }\n    } else if (typeof pushable === 'number' || pushable === null) {\n      // >>>>> Pushable\n      // =============== Push ==================\n      // >>>>>> Basic push\n      // End values\n      for (var i = valueIndex + 1; i < nextValues.length; i += 1) {\n        var changed = true;\n        while (needPush(nextValues[i] - nextValues[i - 1]) && changed) {\n          var _offsetChangedValue = offsetChangedValue(nextValues, 1, i);\n          nextValues[i] = _offsetChangedValue.value;\n          changed = _offsetChangedValue.changed;\n        }\n      } // Start values\n\n      for (var _i = valueIndex; _i > 0; _i -= 1) {\n        var _changed = true;\n        while (needPush(nextValues[_i] - nextValues[_i - 1]) && _changed) {\n          var _offsetChangedValue2 = offsetChangedValue(nextValues, -1, _i - 1);\n          nextValues[_i - 1] = _offsetChangedValue2.value;\n          _changed = _offsetChangedValue2.changed;\n        }\n      } // >>>>> Revert back to safe push range\n      // End to Start\n\n      for (var _i2 = nextValues.length - 1; _i2 > 0; _i2 -= 1) {\n        var _changed2 = true;\n        while (needPush(nextValues[_i2] - nextValues[_i2 - 1]) && _changed2) {\n          var _offsetChangedValue3 = offsetChangedValue(nextValues, -1, _i2 - 1);\n          nextValues[_i2 - 1] = _offsetChangedValue3.value;\n          _changed2 = _offsetChangedValue3.changed;\n        }\n      } // Start to End\n\n      for (var _i3 = 0; _i3 < nextValues.length - 1; _i3 += 1) {\n        var _changed3 = true;\n        while (needPush(nextValues[_i3 + 1] - nextValues[_i3]) && _changed3) {\n          var _offsetChangedValue4 = offsetChangedValue(nextValues, 1, _i3 + 1);\n          nextValues[_i3 + 1] = _offsetChangedValue4.value;\n          _changed3 = _offsetChangedValue4.changed;\n        }\n      }\n    }\n    return {\n      value: nextValues[valueIndex],\n      values: nextValues\n    };\n  };\n  return [formatValue, offsetValues];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "useOffset", "min", "max", "step", "markList", "allowCross", "pushable", "formatRangeValue", "useCallback", "val", "formatNextValue", "isFinite", "Math", "formatStepValue", "<PERSON><PERSON><PERSON><PERSON>", "round", "getDecimal", "num", "String", "split", "length", "maxDecimal", "fixedValue", "Number", "toFixed", "formatValue", "align<PERSON><PERSON><PERSON>", "map", "mark", "value", "push", "closeValue", "closeDist", "for<PERSON>ach", "alignValue", "dist", "abs", "offsetValue", "values", "offset", "valueIndex", "mode", "arguments", "undefined", "nextValue", "originValue", "targetDistValue", "potential<PERSON><PERSON><PERSON>", "sign", "filter", "compareValue", "valueDist", "potentialValue", "clone<PERSON><PERSON>ues", "offsetChangedValue", "changed", "needPush", "offsetValues", "nextV<PERSON>ues", "pushNum", "i", "_offsetChangedValue", "_i", "_changed", "_offsetChangedValue2", "_i2", "_changed2", "_offsetChangedValue3", "_i3", "_changed3", "_offsetChangedValue4"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/hooks/useOffset.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport default function useOffset(min, max, step, markList, allowCross, pushable) {\n  var formatRangeValue = React.useCallback(function (val) {\n    var formatNextValue = isFinite(val) ? val : min;\n    formatNextValue = Math.min(max, val);\n    formatNextValue = Math.max(min, formatNextValue);\n    return formatNextValue;\n  }, [min, max]);\n  var formatStepValue = React.useCallback(function (val) {\n    if (step !== null) {\n      var stepValue = min + Math.round((formatRangeValue(val) - min) / step) * step; // Cut number in case to be like 0.30000000000000004\n\n      var getDecimal = function getDecimal(num) {\n        return (String(num).split('.')[1] || '').length;\n      };\n\n      var maxDecimal = Math.max(getDecimal(step), getDecimal(max), getDecimal(min));\n      var fixedValue = Number(stepValue.toFixed(maxDecimal));\n      return min <= fixedValue && fixedValue <= max ? fixedValue : null;\n    }\n\n    return null;\n  }, [step, min, max, formatRangeValue]);\n  var formatValue = React.useCallback(function (val) {\n    var formatNextValue = formatRangeValue(val); // List align values\n\n    var alignValues = markList.map(function (mark) {\n      return mark.value;\n    });\n\n    if (step !== null) {\n      alignValues.push(formatStepValue(val));\n    } // min & max\n\n\n    alignValues.push(min, max); // Align with marks\n\n    var closeValue = alignValues[0];\n    var closeDist = max - min;\n    alignValues.forEach(function (alignValue) {\n      var dist = Math.abs(formatNextValue - alignValue);\n\n      if (dist <= closeDist) {\n        closeValue = alignValue;\n        closeDist = dist;\n      }\n    });\n    return closeValue;\n  }, [min, max, markList, step, formatRangeValue, formatStepValue]); // ========================== Offset ==========================\n  // Single Value\n\n  var offsetValue = function offsetValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n\n    if (typeof offset === 'number') {\n      var nextValue;\n      var originValue = values[valueIndex]; // Only used for `dist` mode\n\n      var targetDistValue = originValue + offset; // Compare next step value & mark value which is best match\n\n      var potentialValues = [];\n      markList.forEach(function (mark) {\n        potentialValues.push(mark.value);\n      }); // Min & Max\n\n      potentialValues.push(min, max); // In case origin value is align with mark but not with step\n\n      potentialValues.push(formatStepValue(originValue)); // Put offset step value also\n\n      var sign = offset > 0 ? 1 : -1;\n\n      if (mode === 'unit') {\n        potentialValues.push(formatStepValue(originValue + sign * step));\n      } else {\n        potentialValues.push(formatStepValue(targetDistValue));\n      } // Find close one\n\n\n      potentialValues = potentialValues.filter(function (val) {\n        return val !== null;\n      }) // Remove reverse value\n      .filter(function (val) {\n        return offset < 0 ? val <= originValue : val >= originValue;\n      });\n\n      if (mode === 'unit') {\n        // `unit` mode can not contain itself\n        potentialValues = potentialValues.filter(function (val) {\n          return val !== originValue;\n        });\n      }\n\n      var compareValue = mode === 'unit' ? originValue : targetDistValue;\n      nextValue = potentialValues[0];\n      var valueDist = Math.abs(nextValue - compareValue);\n      potentialValues.forEach(function (potentialValue) {\n        var dist = Math.abs(potentialValue - compareValue);\n\n        if (dist < valueDist) {\n          nextValue = potentialValue;\n          valueDist = dist;\n        }\n      }); // Out of range will back to range\n\n      if (nextValue === undefined) {\n        return offset < 0 ? min : max;\n      } // `dist` mode\n\n\n      if (mode === 'dist') {\n        return nextValue;\n      } // `unit` mode may need another round\n\n\n      if (Math.abs(offset) > 1) {\n        var cloneValues = _toConsumableArray(values);\n\n        cloneValues[valueIndex] = nextValue;\n        return offsetValue(cloneValues, offset - sign, valueIndex, mode);\n      }\n\n      return nextValue;\n    } else if (offset === 'min') {\n      return min;\n    } else if (offset === 'max') {\n      return max;\n    }\n  };\n  /** Same as `offsetValue` but return `changed` mark to tell value changed */\n\n\n  var offsetChangedValue = function offsetChangedValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var originValue = values[valueIndex];\n    var nextValue = offsetValue(values, offset, valueIndex, mode);\n    return {\n      value: nextValue,\n      changed: nextValue !== originValue\n    };\n  };\n\n  var needPush = function needPush(dist) {\n    return pushable === null && dist === 0 || typeof pushable === 'number' && dist < pushable;\n  }; // Values\n\n\n  var offsetValues = function offsetValues(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var nextValues = values.map(formatValue);\n    var originValue = nextValues[valueIndex];\n    var nextValue = offsetValue(nextValues, offset, valueIndex, mode);\n    nextValues[valueIndex] = nextValue;\n\n    if (allowCross === false) {\n      // >>>>> Allow Cross\n      var pushNum = pushable || 0; // ============ AllowCross ===============\n\n      if (valueIndex > 0 && nextValues[valueIndex - 1] !== originValue) {\n        nextValues[valueIndex] = Math.max(nextValues[valueIndex], nextValues[valueIndex - 1] + pushNum);\n      }\n\n      if (valueIndex < nextValues.length - 1 && nextValues[valueIndex + 1] !== originValue) {\n        nextValues[valueIndex] = Math.min(nextValues[valueIndex], nextValues[valueIndex + 1] - pushNum);\n      }\n    } else if (typeof pushable === 'number' || pushable === null) {\n      // >>>>> Pushable\n      // =============== Push ==================\n      // >>>>>> Basic push\n      // End values\n      for (var i = valueIndex + 1; i < nextValues.length; i += 1) {\n        var changed = true;\n\n        while (needPush(nextValues[i] - nextValues[i - 1]) && changed) {\n          var _offsetChangedValue = offsetChangedValue(nextValues, 1, i);\n\n          nextValues[i] = _offsetChangedValue.value;\n          changed = _offsetChangedValue.changed;\n        }\n      } // Start values\n\n\n      for (var _i = valueIndex; _i > 0; _i -= 1) {\n        var _changed = true;\n\n        while (needPush(nextValues[_i] - nextValues[_i - 1]) && _changed) {\n          var _offsetChangedValue2 = offsetChangedValue(nextValues, -1, _i - 1);\n\n          nextValues[_i - 1] = _offsetChangedValue2.value;\n          _changed = _offsetChangedValue2.changed;\n        }\n      } // >>>>> Revert back to safe push range\n      // End to Start\n\n\n      for (var _i2 = nextValues.length - 1; _i2 > 0; _i2 -= 1) {\n        var _changed2 = true;\n\n        while (needPush(nextValues[_i2] - nextValues[_i2 - 1]) && _changed2) {\n          var _offsetChangedValue3 = offsetChangedValue(nextValues, -1, _i2 - 1);\n\n          nextValues[_i2 - 1] = _offsetChangedValue3.value;\n          _changed2 = _offsetChangedValue3.changed;\n        }\n      } // Start to End\n\n\n      for (var _i3 = 0; _i3 < nextValues.length - 1; _i3 += 1) {\n        var _changed3 = true;\n\n        while (needPush(nextValues[_i3 + 1] - nextValues[_i3]) && _changed3) {\n          var _offsetChangedValue4 = offsetChangedValue(nextValues, 1, _i3 + 1);\n\n          nextValues[_i3 + 1] = _offsetChangedValue4.value;\n          _changed3 = _offsetChangedValue4.changed;\n        }\n      }\n    }\n\n    return {\n      value: nextValues[valueIndex],\n      values: nextValues\n    };\n  };\n\n  return [formatValue, offsetValues];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EAChF,IAAIC,gBAAgB,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACtD,IAAIC,eAAe,GAAGC,QAAQ,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAGR,GAAG;IAC/CS,eAAe,GAAGE,IAAI,CAACX,GAAG,CAACC,GAAG,EAAEO,GAAG,CAAC;IACpCC,eAAe,GAAGE,IAAI,CAACV,GAAG,CAACD,GAAG,EAAES,eAAe,CAAC;IAChD,OAAOA,eAAe;EACxB,CAAC,EAAE,CAACT,GAAG,EAAEC,GAAG,CAAC,CAAC;EACd,IAAIW,eAAe,GAAGd,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACrD,IAAIN,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIW,SAAS,GAAGb,GAAG,GAAGW,IAAI,CAACG,KAAK,CAAC,CAACR,gBAAgB,CAACE,GAAG,CAAC,GAAGR,GAAG,IAAIE,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC;;MAE/E,IAAIa,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAE;QACxC,OAAO,CAACC,MAAM,CAACD,GAAG,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;MACjD,CAAC;MAED,IAAIC,UAAU,GAAGT,IAAI,CAACV,GAAG,CAACc,UAAU,CAACb,IAAI,CAAC,EAAEa,UAAU,CAACd,GAAG,CAAC,EAAEc,UAAU,CAACf,GAAG,CAAC,CAAC;MAC7E,IAAIqB,UAAU,GAAGC,MAAM,CAACT,SAAS,CAACU,OAAO,CAACH,UAAU,CAAC,CAAC;MACtD,OAAOpB,GAAG,IAAIqB,UAAU,IAAIA,UAAU,IAAIpB,GAAG,GAAGoB,UAAU,GAAG,IAAI;IACnE;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAACnB,IAAI,EAAEF,GAAG,EAAEC,GAAG,EAAEK,gBAAgB,CAAC,CAAC;EACtC,IAAIkB,WAAW,GAAG1B,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACjD,IAAIC,eAAe,GAAGH,gBAAgB,CAACE,GAAG,CAAC,CAAC,CAAC;;IAE7C,IAAIiB,WAAW,GAAGtB,QAAQ,CAACuB,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC7C,OAAOA,IAAI,CAACC,KAAK;IACnB,CAAC,CAAC;IAEF,IAAI1B,IAAI,KAAK,IAAI,EAAE;MACjBuB,WAAW,CAACI,IAAI,CAACjB,eAAe,CAACJ,GAAG,CAAC,CAAC;IACxC,CAAC,CAAC;;IAGFiB,WAAW,CAACI,IAAI,CAAC7B,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;;IAE5B,IAAI6B,UAAU,GAAGL,WAAW,CAAC,CAAC,CAAC;IAC/B,IAAIM,SAAS,GAAG9B,GAAG,GAAGD,GAAG;IACzByB,WAAW,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAE;MACxC,IAAIC,IAAI,GAAGvB,IAAI,CAACwB,GAAG,CAAC1B,eAAe,GAAGwB,UAAU,CAAC;MAEjD,IAAIC,IAAI,IAAIH,SAAS,EAAE;QACrBD,UAAU,GAAGG,UAAU;QACvBF,SAAS,GAAGG,IAAI;MAClB;IACF,CAAC,CAAC;IACF,OAAOJ,UAAU;EACnB,CAAC,EAAE,CAAC9B,GAAG,EAAEC,GAAG,EAAEE,QAAQ,EAAED,IAAI,EAAEI,gBAAgB,EAAEM,eAAe,CAAC,CAAC,CAAC,CAAC;EACnE;;EAEA,IAAIwB,WAAW,GAAG,SAASA,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACjE,IAAIC,IAAI,GAAGC,SAAS,CAACtB,MAAM,GAAG,CAAC,IAAIsB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IAErF,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAIK,SAAS;MACb,IAAIC,WAAW,GAAGP,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC;;MAEtC,IAAIM,eAAe,GAAGD,WAAW,GAAGN,MAAM,CAAC,CAAC;;MAE5C,IAAIQ,eAAe,GAAG,EAAE;MACxB3C,QAAQ,CAAC6B,OAAO,CAAC,UAAUL,IAAI,EAAE;QAC/BmB,eAAe,CAACjB,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC;;MAEJkB,eAAe,CAACjB,IAAI,CAAC7B,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;;MAEhC6C,eAAe,CAACjB,IAAI,CAACjB,eAAe,CAACgC,WAAW,CAAC,CAAC,CAAC,CAAC;;MAEpD,IAAIG,IAAI,GAAGT,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAE9B,IAAIE,IAAI,KAAK,MAAM,EAAE;QACnBM,eAAe,CAACjB,IAAI,CAACjB,eAAe,CAACgC,WAAW,GAAGG,IAAI,GAAG7C,IAAI,CAAC,CAAC;MAClE,CAAC,MAAM;QACL4C,eAAe,CAACjB,IAAI,CAACjB,eAAe,CAACiC,eAAe,CAAC,CAAC;MACxD,CAAC,CAAC;;MAGFC,eAAe,GAAGA,eAAe,CAACE,MAAM,CAAC,UAAUxC,GAAG,EAAE;QACtD,OAAOA,GAAG,KAAK,IAAI;MACrB,CAAC,CAAC,CAAC;MAAA,CACFwC,MAAM,CAAC,UAAUxC,GAAG,EAAE;QACrB,OAAO8B,MAAM,GAAG,CAAC,GAAG9B,GAAG,IAAIoC,WAAW,GAAGpC,GAAG,IAAIoC,WAAW;MAC7D,CAAC,CAAC;MAEF,IAAIJ,IAAI,KAAK,MAAM,EAAE;QACnB;QACAM,eAAe,GAAGA,eAAe,CAACE,MAAM,CAAC,UAAUxC,GAAG,EAAE;UACtD,OAAOA,GAAG,KAAKoC,WAAW;QAC5B,CAAC,CAAC;MACJ;MAEA,IAAIK,YAAY,GAAGT,IAAI,KAAK,MAAM,GAAGI,WAAW,GAAGC,eAAe;MAClEF,SAAS,GAAGG,eAAe,CAAC,CAAC,CAAC;MAC9B,IAAII,SAAS,GAAGvC,IAAI,CAACwB,GAAG,CAACQ,SAAS,GAAGM,YAAY,CAAC;MAClDH,eAAe,CAACd,OAAO,CAAC,UAAUmB,cAAc,EAAE;QAChD,IAAIjB,IAAI,GAAGvB,IAAI,CAACwB,GAAG,CAACgB,cAAc,GAAGF,YAAY,CAAC;QAElD,IAAIf,IAAI,GAAGgB,SAAS,EAAE;UACpBP,SAAS,GAAGQ,cAAc;UAC1BD,SAAS,GAAGhB,IAAI;QAClB;MACF,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIS,SAAS,KAAKD,SAAS,EAAE;QAC3B,OAAOJ,MAAM,GAAG,CAAC,GAAGtC,GAAG,GAAGC,GAAG;MAC/B,CAAC,CAAC;;MAGF,IAAIuC,IAAI,KAAK,MAAM,EAAE;QACnB,OAAOG,SAAS;MAClB,CAAC,CAAC;;MAGF,IAAIhC,IAAI,CAACwB,GAAG,CAACG,MAAM,CAAC,GAAG,CAAC,EAAE;QACxB,IAAIc,WAAW,GAAGvD,kBAAkB,CAACwC,MAAM,CAAC;QAE5Ce,WAAW,CAACb,UAAU,CAAC,GAAGI,SAAS;QACnC,OAAOP,WAAW,CAACgB,WAAW,EAAEd,MAAM,GAAGS,IAAI,EAAER,UAAU,EAAEC,IAAI,CAAC;MAClE;MAEA,OAAOG,SAAS;IAClB,CAAC,MAAM,IAAIL,MAAM,KAAK,KAAK,EAAE;MAC3B,OAAOtC,GAAG;IACZ,CAAC,MAAM,IAAIsC,MAAM,KAAK,KAAK,EAAE;MAC3B,OAAOrC,GAAG;IACZ;EACF,CAAC;EACD;;EAGA,IAAIoD,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAC/E,IAAIC,IAAI,GAAGC,SAAS,CAACtB,MAAM,GAAG,CAAC,IAAIsB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAIG,WAAW,GAAGP,MAAM,CAACE,UAAU,CAAC;IACpC,IAAII,SAAS,GAAGP,WAAW,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC7D,OAAO;MACLZ,KAAK,EAAEe,SAAS;MAChBW,OAAO,EAAEX,SAAS,KAAKC;IACzB,CAAC;EACH,CAAC;EAED,IAAIW,QAAQ,GAAG,SAASA,QAAQA,CAACrB,IAAI,EAAE;IACrC,OAAO7B,QAAQ,KAAK,IAAI,IAAI6B,IAAI,KAAK,CAAC,IAAI,OAAO7B,QAAQ,KAAK,QAAQ,IAAI6B,IAAI,GAAG7B,QAAQ;EAC3F,CAAC,CAAC,CAAC;;EAGH,IAAImD,YAAY,GAAG,SAASA,YAAYA,CAACnB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACnE,IAAIC,IAAI,GAAGC,SAAS,CAACtB,MAAM,GAAG,CAAC,IAAIsB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAIgB,UAAU,GAAGpB,MAAM,CAACX,GAAG,CAACF,WAAW,CAAC;IACxC,IAAIoB,WAAW,GAAGa,UAAU,CAAClB,UAAU,CAAC;IACxC,IAAII,SAAS,GAAGP,WAAW,CAACqB,UAAU,EAAEnB,MAAM,EAAEC,UAAU,EAAEC,IAAI,CAAC;IACjEiB,UAAU,CAAClB,UAAU,CAAC,GAAGI,SAAS;IAElC,IAAIvC,UAAU,KAAK,KAAK,EAAE;MACxB;MACA,IAAIsD,OAAO,GAAGrD,QAAQ,IAAI,CAAC,CAAC,CAAC;;MAE7B,IAAIkC,UAAU,GAAG,CAAC,IAAIkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,KAAKK,WAAW,EAAE;QAChEa,UAAU,CAAClB,UAAU,CAAC,GAAG5B,IAAI,CAACV,GAAG,CAACwD,UAAU,CAAClB,UAAU,CAAC,EAAEkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,GAAGmB,OAAO,CAAC;MACjG;MAEA,IAAInB,UAAU,GAAGkB,UAAU,CAACtC,MAAM,GAAG,CAAC,IAAIsC,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,KAAKK,WAAW,EAAE;QACpFa,UAAU,CAAClB,UAAU,CAAC,GAAG5B,IAAI,CAACX,GAAG,CAACyD,UAAU,CAAClB,UAAU,CAAC,EAAEkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,GAAGmB,OAAO,CAAC;MACjG;IACF,CAAC,MAAM,IAAI,OAAOrD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MAC5D;MACA;MACA;MACA;MACA,KAAK,IAAIsD,CAAC,GAAGpB,UAAU,GAAG,CAAC,EAAEoB,CAAC,GAAGF,UAAU,CAACtC,MAAM,EAAEwC,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAIL,OAAO,GAAG,IAAI;QAElB,OAAOC,QAAQ,CAACE,UAAU,CAACE,CAAC,CAAC,GAAGF,UAAU,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIL,OAAO,EAAE;UAC7D,IAAIM,mBAAmB,GAAGP,kBAAkB,CAACI,UAAU,EAAE,CAAC,EAAEE,CAAC,CAAC;UAE9DF,UAAU,CAACE,CAAC,CAAC,GAAGC,mBAAmB,CAAChC,KAAK;UACzC0B,OAAO,GAAGM,mBAAmB,CAACN,OAAO;QACvC;MACF,CAAC,CAAC;;MAGF,KAAK,IAAIO,EAAE,GAAGtB,UAAU,EAAEsB,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;QACzC,IAAIC,QAAQ,GAAG,IAAI;QAEnB,OAAOP,QAAQ,CAACE,UAAU,CAACI,EAAE,CAAC,GAAGJ,UAAU,CAACI,EAAE,GAAG,CAAC,CAAC,CAAC,IAAIC,QAAQ,EAAE;UAChE,IAAIC,oBAAoB,GAAGV,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC,EAAEI,EAAE,GAAG,CAAC,CAAC;UAErEJ,UAAU,CAACI,EAAE,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACnC,KAAK;UAC/CkC,QAAQ,GAAGC,oBAAoB,CAACT,OAAO;QACzC;MACF,CAAC,CAAC;MACF;;MAGA,KAAK,IAAIU,GAAG,GAAGP,UAAU,CAACtC,MAAM,GAAG,CAAC,EAAE6C,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;QACvD,IAAIC,SAAS,GAAG,IAAI;QAEpB,OAAOV,QAAQ,CAACE,UAAU,CAACO,GAAG,CAAC,GAAGP,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC,CAAC,IAAIC,SAAS,EAAE;UACnE,IAAIC,oBAAoB,GAAGb,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC,EAAEO,GAAG,GAAG,CAAC,CAAC;UAEtEP,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACtC,KAAK;UAChDqC,SAAS,GAAGC,oBAAoB,CAACZ,OAAO;QAC1C;MACF,CAAC,CAAC;;MAGF,KAAK,IAAIa,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGV,UAAU,CAACtC,MAAM,GAAG,CAAC,EAAEgD,GAAG,IAAI,CAAC,EAAE;QACvD,IAAIC,SAAS,GAAG,IAAI;QAEpB,OAAOb,QAAQ,CAACE,UAAU,CAACU,GAAG,GAAG,CAAC,CAAC,GAAGV,UAAU,CAACU,GAAG,CAAC,CAAC,IAAIC,SAAS,EAAE;UACnE,IAAIC,oBAAoB,GAAGhB,kBAAkB,CAACI,UAAU,EAAE,CAAC,EAAEU,GAAG,GAAG,CAAC,CAAC;UAErEV,UAAU,CAACU,GAAG,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACzC,KAAK;UAChDwC,SAAS,GAAGC,oBAAoB,CAACf,OAAO;QAC1C;MACF;IACF;IAEA,OAAO;MACL1B,KAAK,EAAE6B,UAAU,CAAClB,UAAU,CAAC;MAC7BF,MAAM,EAAEoB;IACV,CAAC;EACH,CAAC;EAED,OAAO,CAACjC,WAAW,EAAEgC,YAAY,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}