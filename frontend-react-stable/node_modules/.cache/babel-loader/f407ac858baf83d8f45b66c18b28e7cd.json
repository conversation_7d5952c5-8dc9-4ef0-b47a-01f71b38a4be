{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isoSpecifier = exports.default = void 0;\nvar _defaultLocale = require(\"./defaultLocale.js\");\nvar isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\nexports.isoSpecifier = isoSpecifier;\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\nvar formatIso = Date.prototype.toISOString ? formatIsoNative : (0, _defaultLocale.utcFormat)(isoSpecifier);\nvar _default = formatIso;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isoSpecifier", "default", "_defaultLocale", "require", "formatIsoNative", "date", "toISOString", "formatIso", "Date", "prototype", "utcFormat", "_default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time-format/src/isoFormat.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isoSpecifier = exports.default = void 0;\n\nvar _defaultLocale = require(\"./defaultLocale.js\");\n\nvar isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\nexports.isoSpecifier = isoSpecifier;\n\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\n\nvar formatIso = Date.prototype.toISOString ? formatIsoNative : (0, _defaultLocale.utcFormat)(isoSpecifier);\nvar _default = formatIso;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE/C,IAAIC,cAAc,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAElD,IAAIH,YAAY,GAAG,uBAAuB;AAC1CF,OAAO,CAACE,YAAY,GAAGA,YAAY;AAEnC,SAASI,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC;AAC3B;AAEA,IAAIC,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACH,WAAW,GAAGF,eAAe,GAAG,CAAC,CAAC,EAAEF,cAAc,CAACQ,SAAS,EAAEV,YAAY,CAAC;AAC1G,IAAIW,QAAQ,GAAGJ,SAAS;AACxBT,OAAO,CAACG,OAAO,GAAGU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}