{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.extend = extend;\nfunction _default(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\nfunction extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "extend", "constructor", "factory", "prototype", "parent", "definition", "create", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-color/src/define.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.extend = extend;\n\nfunction _default(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nfunction extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n\n  for (var key in definition) prototype[key] = definition[key];\n\n  return prototype;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,MAAM,GAAGA,MAAM;AAEvB,SAASD,QAAQA,CAACE,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAE;EACjDF,WAAW,CAACE,SAAS,GAAGD,OAAO,CAACC,SAAS,GAAGA,SAAS;EACrDA,SAAS,CAACF,WAAW,GAAGA,WAAW;AACrC;AAEA,SAASD,MAAMA,CAACI,MAAM,EAAEC,UAAU,EAAE;EAClC,IAAIF,SAAS,GAAGT,MAAM,CAACY,MAAM,CAACF,MAAM,CAACD,SAAS,CAAC;EAE/C,KAAK,IAAII,GAAG,IAAIF,UAAU,EAAEF,SAAS,CAACI,GAAG,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;EAE5D,OAAOJ,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}