{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Dropdown from '../dropdown/dropdown';\nvar BreadcrumbItem = function BreadcrumbItem(props) {\n  var customizePrefixCls = props.prefixCls,\n    _props$separator = props.separator,\n    separator = _props$separator === void 0 ? '/' : _props$separator,\n    children = props.children,\n    menu = props.menu,\n    overlay = props.overlay,\n    dropdownProps = props.dropdownProps,\n    restProps = __rest(props, [\"prefixCls\", \"separator\", \"children\", \"menu\", \"overlay\", \"dropdownProps\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!('overlay' in props), 'Breadcrumb.Item', '`overlay` is deprecated. Please use `menu` instead.') : void 0;\n  }\n  /** If overlay is have Wrap a Dropdown */\n  var renderBreadcrumbNode = function renderBreadcrumbNode(breadcrumbItem) {\n    if (menu || overlay) {\n      return /*#__PURE__*/React.createElement(Dropdown, _extends({\n        menu: menu,\n        overlay: overlay,\n        placement: \"bottom\"\n      }, dropdownProps), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-overlay-link\")\n      }, breadcrumbItem, /*#__PURE__*/React.createElement(DownOutlined, null)));\n    }\n    return breadcrumbItem;\n  };\n  var link;\n  if ('href' in restProps) {\n    link = /*#__PURE__*/React.createElement(\"a\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } else {\n    link = /*#__PURE__*/React.createElement(\"span\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  }\n  // wrap to dropDown\n  link = renderBreadcrumbNode(link);\n  if (children !== undefined && children !== null) {\n    return /*#__PURE__*/React.createElement(\"li\", null, link, separator && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-separator\")\n    }, separator));\n  }\n  return null;\n};\nBreadcrumbItem.__ANT_BREADCRUMB_ITEM = true;\nexport default BreadcrumbItem;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "DownOutlined", "React", "warning", "ConfigContext", "Dropdown", "BreadcrumbItem", "props", "customizePrefixCls", "prefixCls", "_props$separator", "separator", "children", "menu", "overlay", "dropdownProps", "restProps", "_React$useContext", "useContext", "getPrefixCls", "process", "env", "NODE_ENV", "renderBreadcrumbNode", "breadcrumbItem", "createElement", "placement", "className", "concat", "link", "undefined", "__ANT_BREADCRUMB_ITEM"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/breadcrumb/BreadcrumbItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Dropdown from '../dropdown/dropdown';\nvar BreadcrumbItem = function BreadcrumbItem(props) {\n  var customizePrefixCls = props.prefixCls,\n    _props$separator = props.separator,\n    separator = _props$separator === void 0 ? '/' : _props$separator,\n    children = props.children,\n    menu = props.menu,\n    overlay = props.overlay,\n    dropdownProps = props.dropdownProps,\n    restProps = __rest(props, [\"prefixCls\", \"separator\", \"children\", \"menu\", \"overlay\", \"dropdownProps\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!('overlay' in props), 'Breadcrumb.Item', '`overlay` is deprecated. Please use `menu` instead.') : void 0;\n  }\n  /** If overlay is have Wrap a Dropdown */\n  var renderBreadcrumbNode = function renderBreadcrumbNode(breadcrumbItem) {\n    if (menu || overlay) {\n      return /*#__PURE__*/React.createElement(Dropdown, _extends({\n        menu: menu,\n        overlay: overlay,\n        placement: \"bottom\"\n      }, dropdownProps), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-overlay-link\")\n      }, breadcrumbItem, /*#__PURE__*/React.createElement(DownOutlined, null)));\n    }\n    return breadcrumbItem;\n  };\n  var link;\n  if ('href' in restProps) {\n    link = /*#__PURE__*/React.createElement(\"a\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } else {\n    link = /*#__PURE__*/React.createElement(\"span\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  }\n  // wrap to dropDown\n  link = renderBreadcrumbNode(link);\n  if (children !== undefined && children !== null) {\n    return /*#__PURE__*/React.createElement(\"li\", null, link, separator && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-separator\")\n    }, separator));\n  }\n  return null;\n};\nBreadcrumbItem.__ANT_BREADCRUMB_ITEM = true;\nexport default BreadcrumbItem;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,YAAY,MAAM,yCAAyC;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,gBAAgB;IAChEE,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,SAAS,GAAG7B,MAAM,CAACoB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;EACvG,IAAIU,iBAAiB,GAAGf,KAAK,CAACgB,UAAU,CAACd,aAAa,CAAC;IACrDe,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIV,SAAS,GAAGU,YAAY,CAAC,YAAY,EAAEX,kBAAkB,CAAC;EAC9D;EACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,OAAO,CAAC,EAAE,SAAS,IAAII,KAAK,CAAC,EAAE,iBAAiB,EAAE,qDAAqD,CAAC,GAAG,KAAK,CAAC;EAC3J;EACA;EACA,IAAIgB,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAE;IACvE,IAAIX,IAAI,IAAIC,OAAO,EAAE;MACnB,OAAO,aAAaZ,KAAK,CAACuB,aAAa,CAACpB,QAAQ,EAAEnB,QAAQ,CAAC;QACzD2B,IAAI,EAAEA,IAAI;QACVC,OAAO,EAAEA,OAAO;QAChBY,SAAS,EAAE;MACb,CAAC,EAAEX,aAAa,CAAC,EAAE,aAAab,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;QAC1DE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACnB,SAAS,EAAE,eAAe;MACjD,CAAC,EAAEe,cAAc,EAAE,aAAatB,KAAK,CAACuB,aAAa,CAACxB,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3E;IACA,OAAOuB,cAAc;EACvB,CAAC;EACD,IAAIK,IAAI;EACR,IAAI,MAAM,IAAIb,SAAS,EAAE;IACvBa,IAAI,GAAG,aAAa3B,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAEvC,QAAQ,CAAC;MACpDyC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACnB,SAAS,EAAE,OAAO;IACzC,CAAC,EAAEO,SAAS,CAAC,EAAEJ,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACLiB,IAAI,GAAG,aAAa3B,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAEvC,QAAQ,CAAC;MACvDyC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACnB,SAAS,EAAE,OAAO;IACzC,CAAC,EAAEO,SAAS,CAAC,EAAEJ,QAAQ,CAAC;EAC1B;EACA;EACAiB,IAAI,GAAGN,oBAAoB,CAACM,IAAI,CAAC;EACjC,IAAIjB,QAAQ,KAAKkB,SAAS,IAAIlB,QAAQ,KAAK,IAAI,EAAE;IAC/C,OAAO,aAAaV,KAAK,CAACuB,aAAa,CAAC,IAAI,EAAE,IAAI,EAAEI,IAAI,EAAElB,SAAS,IAAI,aAAaT,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC9GE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACnB,SAAS,EAAE,YAAY;IAC9C,CAAC,EAAEE,SAAS,CAAC,CAAC;EAChB;EACA,OAAO,IAAI;AACb,CAAC;AACDL,cAAc,CAACyB,qBAAqB,GAAG,IAAI;AAC3C,eAAezB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}