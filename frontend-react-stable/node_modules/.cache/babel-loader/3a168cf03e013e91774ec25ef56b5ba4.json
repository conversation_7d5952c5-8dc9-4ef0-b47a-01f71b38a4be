{"ast": null, "code": "var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\nexport default isNumeric;", "map": {"version": 3, "names": ["isNumeric", "value", "isNaN", "parseFloat", "isFinite"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/isNumeric.js"], "sourcesContent": ["var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\nexport default isNumeric;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC;AACrD,CAAC;AACD,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}