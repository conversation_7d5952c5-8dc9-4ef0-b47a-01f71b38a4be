{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport { useRef, useImperativeHandle, forwardRef } from 'react';\nimport Trigger from 'rc-trigger';\nimport { placements } from './placements';\nimport Popup from './Popup';\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    _props$mouseEnterDela = props.mouseEnterDelay,\n    mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n    _props$mouseLeaveDela = props.mouseLeaveDelay,\n    mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n    overlayStyle = props.overlayStyle,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    afterVisibleChange = props.afterVisibleChange,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    motion = props.motion,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$align = props.align,\n    align = _props$align === void 0 ? {} : _props$align,\n    _props$destroyTooltip = props.destroyTooltipOnHide,\n    destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n    defaultVisible = props.defaultVisible,\n    getTooltipContainer = props.getTooltipContainer,\n    overlayInnerStyle = props.overlayInnerStyle,\n    arrowContent = props.arrowContent,\n    overlay = props.overlay,\n    id = props.id,\n    showArrow = props.showArrow,\n    restProps = _objectWithoutProperties(props, [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\", \"arrowContent\", \"overlay\", \"id\", \"showArrow\"]);\n  var domRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return domRef.current;\n  });\n  var extraProps = _objectSpread({}, restProps);\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n  var getPopupElement = function getPopupElement() {\n    return /*#__PURE__*/React.createElement(Popup, {\n      showArrow: showArrow,\n      arrowContent: arrowContent,\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: id,\n      overlayInnerStyle: overlayInnerStyle\n    }, overlay);\n  };\n  var destroyTooltip = false;\n  var autoDestroy = false;\n  if (typeof destroyTooltipOnHide === 'boolean') {\n    destroyTooltip = destroyTooltipOnHide;\n  } else if (destroyTooltipOnHide && _typeof(destroyTooltipOnHide) === 'object') {\n    var keepParent = destroyTooltipOnHide.keepParent;\n    destroyTooltip = keepParent === true;\n    autoDestroy = keepParent === false;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: overlayClassName,\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: domRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    destroyPopupOnHide: destroyTooltip,\n    autoDestroy: autoDestroy,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: overlayStyle,\n    mouseEnterDelay: mouseEnterDelay\n  }, extraProps), children);\n};\nexport default /*#__PURE__*/forwardRef(Tooltip);", "map": {"version": 3, "names": ["_extends", "_typeof", "_objectSpread", "_objectWithoutProperties", "React", "useRef", "useImperativeHandle", "forwardRef", "<PERSON><PERSON>", "placements", "Popup", "<PERSON><PERSON><PERSON>", "props", "ref", "overlayClassName", "_props$trigger", "trigger", "_props$mouseEnterDela", "mouseEnterDelay", "_props$mouseLeaveDela", "mouseLeaveDelay", "overlayStyle", "_props$prefixCls", "prefixCls", "children", "onVisibleChange", "afterVisibleChange", "transitionName", "animation", "motion", "_props$placement", "placement", "_props$align", "align", "_props$destroyTooltip", "destroyTooltipOnHide", "defaultVisible", "getTooltipContainer", "overlayInnerStyle", "arrow<PERSON>ontent", "overlay", "id", "showArrow", "restProps", "domRef", "current", "extraProps", "popupVisible", "visible", "getPopupElement", "createElement", "key", "destroyTooltip", "autoDestroy", "keepParent", "popupClassName", "popup", "action", "builtinPlacements", "popupPlacement", "popupAlign", "getPopupContainer", "onPopupVisibleChange", "afterPopupVisibleChange", "popupTransitionName", "popupAnimation", "popupMotion", "defaultPopupVisible", "destroyPopupOnHide", "popupStyle"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tooltip/es/Tooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport { useRef, useImperativeHandle, forwardRef } from 'react';\nimport Trigger from 'rc-trigger';\nimport { placements } from './placements';\nimport Popup from './Popup';\n\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n      _props$trigger = props.trigger,\n      trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n      _props$mouseEnterDela = props.mouseEnterDelay,\n      mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      overlayStyle = props.overlayStyle,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n      children = props.children,\n      onVisibleChange = props.onVisibleChange,\n      afterVisibleChange = props.afterVisibleChange,\n      transitionName = props.transitionName,\n      animation = props.animation,\n      motion = props.motion,\n      _props$placement = props.placement,\n      placement = _props$placement === void 0 ? 'right' : _props$placement,\n      _props$align = props.align,\n      align = _props$align === void 0 ? {} : _props$align,\n      _props$destroyTooltip = props.destroyTooltipOnHide,\n      destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n      defaultVisible = props.defaultVisible,\n      getTooltipContainer = props.getTooltipContainer,\n      overlayInnerStyle = props.overlayInnerStyle,\n      arrowContent = props.arrowContent,\n      overlay = props.overlay,\n      id = props.id,\n      showArrow = props.showArrow,\n      restProps = _objectWithoutProperties(props, [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\", \"arrowContent\", \"overlay\", \"id\", \"showArrow\"]);\n\n  var domRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return domRef.current;\n  });\n\n  var extraProps = _objectSpread({}, restProps);\n\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n\n  var getPopupElement = function getPopupElement() {\n    return /*#__PURE__*/React.createElement(Popup, {\n      showArrow: showArrow,\n      arrowContent: arrowContent,\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: id,\n      overlayInnerStyle: overlayInnerStyle\n    }, overlay);\n  };\n\n  var destroyTooltip = false;\n  var autoDestroy = false;\n\n  if (typeof destroyTooltipOnHide === 'boolean') {\n    destroyTooltip = destroyTooltipOnHide;\n  } else if (destroyTooltipOnHide && _typeof(destroyTooltipOnHide) === 'object') {\n    var keepParent = destroyTooltipOnHide.keepParent;\n    destroyTooltip = keepParent === true;\n    autoDestroy = keepParent === false;\n  }\n\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: overlayClassName,\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: domRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    destroyPopupOnHide: destroyTooltip,\n    autoDestroy: autoDestroy,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: overlayStyle,\n    mouseEnterDelay: mouseEnterDelay\n  }, extraProps), children);\n};\n\nexport default /*#__PURE__*/forwardRef(Tooltip);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,OAAO;AAC/D,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,KAAK,MAAM,SAAS;AAE3B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzC,IAAIC,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB;IACzCC,cAAc,GAAGH,KAAK,CAACI,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,cAAc;IAChEE,qBAAqB,GAAGL,KAAK,CAACM,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAC9EE,qBAAqB,GAAGP,KAAK,CAACQ,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IAChFE,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,gBAAgB,GAAGV,KAAK,CAACW,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,eAAe,GAAGb,KAAK,CAACa,eAAe;IACvCC,kBAAkB,GAAGd,KAAK,CAACc,kBAAkB;IAC7CC,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,gBAAgB;IACpEE,YAAY,GAAGpB,KAAK,CAACqB,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,qBAAqB,GAAGtB,KAAK,CAACuB,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACvFE,cAAc,GAAGxB,KAAK,CAACwB,cAAc;IACrCC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,iBAAiB,GAAG1B,KAAK,CAAC0B,iBAAiB;IAC3CC,YAAY,GAAG3B,KAAK,CAAC2B,YAAY;IACjCC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;IACvBC,EAAE,GAAG7B,KAAK,CAAC6B,EAAE;IACbC,SAAS,GAAG9B,KAAK,CAAC8B,SAAS;IAC3BC,SAAS,GAAGxC,wBAAwB,CAACS,KAAK,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;EAE3Y,IAAIgC,MAAM,GAAGvC,MAAM,CAAC,IAAI,CAAC;EACzBC,mBAAmB,CAACO,GAAG,EAAE,YAAY;IACnC,OAAO+B,MAAM,CAACC,OAAO;EACvB,CAAC,CAAC;EAEF,IAAIC,UAAU,GAAG5C,aAAa,CAAC,CAAC,CAAC,EAAEyC,SAAS,CAAC;EAE7C,IAAI,SAAS,IAAI/B,KAAK,EAAE;IACtBkC,UAAU,CAACC,YAAY,GAAGnC,KAAK,CAACoC,OAAO;EACzC;EAEA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,OAAO,aAAa7C,KAAK,CAAC8C,aAAa,CAACxC,KAAK,EAAE;MAC7CgC,SAAS,EAAEA,SAAS;MACpBH,YAAY,EAAEA,YAAY;MAC1BY,GAAG,EAAE,SAAS;MACd5B,SAAS,EAAEA,SAAS;MACpBkB,EAAE,EAAEA,EAAE;MACNH,iBAAiB,EAAEA;IACrB,CAAC,EAAEE,OAAO,CAAC;EACb,CAAC;EAED,IAAIY,cAAc,GAAG,KAAK;EAC1B,IAAIC,WAAW,GAAG,KAAK;EAEvB,IAAI,OAAOlB,oBAAoB,KAAK,SAAS,EAAE;IAC7CiB,cAAc,GAAGjB,oBAAoB;EACvC,CAAC,MAAM,IAAIA,oBAAoB,IAAIlC,OAAO,CAACkC,oBAAoB,CAAC,KAAK,QAAQ,EAAE;IAC7E,IAAImB,UAAU,GAAGnB,oBAAoB,CAACmB,UAAU;IAChDF,cAAc,GAAGE,UAAU,KAAK,IAAI;IACpCD,WAAW,GAAGC,UAAU,KAAK,KAAK;EACpC;EAEA,OAAO,aAAalD,KAAK,CAAC8C,aAAa,CAAC1C,OAAO,EAAER,QAAQ,CAAC;IACxDuD,cAAc,EAAEzC,gBAAgB;IAChCS,SAAS,EAAEA,SAAS;IACpBiC,KAAK,EAAEP,eAAe;IACtBQ,MAAM,EAAEzC,OAAO;IACf0C,iBAAiB,EAAEjD,UAAU;IAC7BkD,cAAc,EAAE5B,SAAS;IACzBlB,GAAG,EAAE+B,MAAM;IACXgB,UAAU,EAAE3B,KAAK;IACjB4B,iBAAiB,EAAExB,mBAAmB;IACtCyB,oBAAoB,EAAErC,eAAe;IACrCsC,uBAAuB,EAAErC,kBAAkB;IAC3CsC,mBAAmB,EAAErC,cAAc;IACnCsC,cAAc,EAAErC,SAAS;IACzBsC,WAAW,EAAErC,MAAM;IACnBsC,mBAAmB,EAAE/B,cAAc;IACnCgC,kBAAkB,EAAEhB,cAAc;IAClCC,WAAW,EAAEA,WAAW;IACxBjC,eAAe,EAAEA,eAAe;IAChCiD,UAAU,EAAEhD,YAAY;IACxBH,eAAe,EAAEA;EACnB,CAAC,EAAE4B,UAAU,CAAC,EAAEtB,QAAQ,CAAC;AAC3B,CAAC;AAED,eAAe,aAAajB,UAAU,CAACI,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}