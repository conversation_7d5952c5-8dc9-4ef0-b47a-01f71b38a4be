{"ast": null, "code": "/**\n * @fileOverview Z Axis\n */\n\nexport var ZAxis = function ZAxis() {\n  return null;\n};\nZAxis.displayName = 'ZAxis';\nZAxis.defaultProps = {\n  zAxisId: 0,\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number'\n};", "map": {"version": 3, "names": ["ZAxis", "displayName", "defaultProps", "zAxisId", "range", "scale", "type"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/ZAxis.js"], "sourcesContent": ["/**\n * @fileOverview Z Axis\n */\n\nexport var ZAxis = function ZAxis() {\n  return null;\n};\nZAxis.displayName = 'ZAxis';\nZAxis.defaultProps = {\n  zAxisId: 0,\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number'\n};"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,IAAIA,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAClC,OAAO,IAAI;AACb,CAAC;AACDA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,YAAY,GAAG;EACnBC,OAAO,EAAE,CAAC;EACVC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}