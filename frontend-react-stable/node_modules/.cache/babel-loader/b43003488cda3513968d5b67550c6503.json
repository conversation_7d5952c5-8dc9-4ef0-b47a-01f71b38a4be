{"ast": null, "code": "// This icon file is generated automatically.\nvar HolderOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z\"\n      }\n    }]\n  },\n  \"name\": \"holder\",\n  \"theme\": \"outlined\"\n};\nexport default HolderOutlined;", "map": {"version": 3, "names": ["Holder<PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/HolderOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar HolderOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z\" } }] }, \"name\": \"holder\", \"theme\": \"outlined\" };\nexport default HolderOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,cAAc,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA+P;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,QAAQ;EAAE,OAAO,EAAE;AAAW,CAAC;AAC1c,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}