{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport classNames from 'classnames';\nimport List from 'rc-virtual-list';\nimport TransBtn from \"./TransBtn\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport SelectContext from \"./SelectContext\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    if (listRef.current) {\n      listRef.current.scrollTo(typeof args === 'number' ? {\n        index: args\n      } : args);\n    }\n  };\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _memoFlattenOptions$c = memoFlattenOptions[current],\n        group = _memoFlattenOptions$c.group,\n        data = _memoFlattenOptions$c.data;\n      if (!group && !data.disabled) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    return rawValues.has(value) && mode !== 'combobox';\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        var index = memoFlattenOptions.findIndex(function (_ref) {\n          var data = _ref.data;\n          return data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !item.data.disabled) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) return null;\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index,\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index),\n      \"aria-selected\": isSelected(value)\n    }), value) : null;\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"listbox\",\n    id: \"\".concat(id, \"_list\"),\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }, renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual\n  }, function (item, itemIndex) {\n    var _classNames;\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\")),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-selected\"), selected), _classNames));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), {\n      \"aria-selected\": selected,\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || disabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!disabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;", "map": {"version": 3, "names": ["_defineProperty", "_objectWithoutProperties", "_extends", "_toConsumableArray", "_slicedToArray", "_excluded", "React", "useEffect", "KeyCode", "omit", "pickAttrs", "useMemo", "classNames", "List", "TransBtn", "isPlatformMac", "useBaseProps", "SelectContext", "isTitleType", "content", "OptionList", "_", "ref", "_useBaseProps", "prefixCls", "id", "open", "multiple", "mode", "searchValue", "toggle<PERSON><PERSON>", "notFoundContent", "onPopupScroll", "_React$useContext", "useContext", "flattenOptions", "onActiveValue", "defaultActiveFirstOption", "onSelect", "menuItemSelectedIcon", "rawValues", "fieldNames", "virtual", "listHeight", "listItemHeight", "itemPrefixCls", "concat", "memoFlattenOptions", "prev", "next", "listRef", "useRef", "onListMouseDown", "event", "preventDefault", "scrollIntoView", "args", "current", "scrollTo", "index", "getEnabledActiveIndex", "offset", "arguments", "length", "undefined", "len", "i", "_memoFlattenOptions$c", "group", "data", "disabled", "_React$useState", "useState", "_React$useState2", "activeIndex", "setActiveIndex", "setActive", "fromKeyboard", "info", "source", "flattenItem", "value", "isSelected", "useCallback", "has", "toString", "size", "timeoutId", "setTimeout", "Array", "from", "findIndex", "_ref", "_listRef$current", "clearTimeout", "onSelectValue", "selected", "useImperativeHandle", "onKeyDown", "which", "ctrl<PERSON>ey", "N", "P", "UP", "DOWN", "nextActiveIndex", "ENTER", "item", "ESC", "stopPropagation", "onKeyUp", "createElement", "role", "className", "onMouseDown", "omitFieldNameList", "Object", "keys", "map", "key", "get<PERSON><PERSON><PERSON>", "label", "renderItem", "itemData", "attrs", "mergedLabel", "Fragment", "style", "height", "width", "overflow", "itemKey", "itemHeight", "fullHeight", "onScroll", "itemIndex", "_classNames", "groupOption", "_data$title", "groupTitle", "title", "children", "otherProps", "passedProps", "optionPrefixCls", "optionClassName", "iconVisible", "optionTitle", "onMouseMove", "onClick", "isValidElement", "customizeIcon", "customizeIconProps", "RefOptionList", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/OptionList.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport classNames from 'classnames';\nimport List from 'rc-virtual-list';\nimport TransBtn from \"./TransBtn\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport SelectContext from \"./SelectContext\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    if (listRef.current) {\n      listRef.current.scrollTo(typeof args === 'number' ? {\n        index: args\n      } : args);\n    }\n  };\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _memoFlattenOptions$c = memoFlattenOptions[current],\n        group = _memoFlattenOptions$c.group,\n        data = _memoFlattenOptions$c.data;\n      if (!group && !data.disabled) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    return rawValues.has(value) && mode !== 'combobox';\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        var index = memoFlattenOptions.findIndex(function (_ref) {\n          var data = _ref.data;\n          return data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !item.data.disabled) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) return null;\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index,\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index),\n      \"aria-selected\": isSelected(value)\n    }), value) : null;\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"listbox\",\n    id: \"\".concat(id, \"_list\"),\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }, renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual\n  }, function (item, itemIndex) {\n    var _classNames;\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\")),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-selected\"), selected), _classNames));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), {\n      \"aria-selected\": selected,\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || disabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!disabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;;AAE3C;;AAEA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,OAAO,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ;AACnE;;AAEA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC3C,IAAIC,aAAa,GAAGP,YAAY,CAAC,CAAC;IAChCQ,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,EAAE,GAAGF,aAAa,CAACE,EAAE;IACrBC,IAAI,GAAGH,aAAa,CAACG,IAAI;IACzBC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;IACjCC,IAAI,GAAGL,aAAa,CAACK,IAAI;IACzBC,WAAW,GAAGN,aAAa,CAACM,WAAW;IACvCC,UAAU,GAAGP,aAAa,CAACO,UAAU;IACrCC,eAAe,GAAGR,aAAa,CAACQ,eAAe;IAC/CC,aAAa,GAAGT,aAAa,CAACS,aAAa;EAC7C,IAAIC,iBAAiB,GAAG3B,KAAK,CAAC4B,UAAU,CAACjB,aAAa,CAAC;IACrDkB,cAAc,GAAGF,iBAAiB,CAACE,cAAc;IACjDC,aAAa,GAAGH,iBAAiB,CAACG,aAAa;IAC/CC,wBAAwB,GAAGJ,iBAAiB,CAACI,wBAAwB;IACrEC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,oBAAoB,GAAGN,iBAAiB,CAACM,oBAAoB;IAC7DC,SAAS,GAAGP,iBAAiB,CAACO,SAAS;IACvCC,UAAU,GAAGR,iBAAiB,CAACQ,UAAU;IACzCC,OAAO,GAAGT,iBAAiB,CAACS,OAAO;IACnCC,UAAU,GAAGV,iBAAiB,CAACU,UAAU;IACzCC,cAAc,GAAGX,iBAAiB,CAACW,cAAc;EACnD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIuB,kBAAkB,GAAGpC,OAAO,CAAC,YAAY;IAC3C,OAAOwB,cAAc;EACvB,CAAC,EAAE,CAACT,IAAI,EAAES,cAAc,CAAC,EAAE,UAAUa,IAAI,EAAEC,IAAI,EAAE;IAC/C,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,OAAO,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpDA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;IACjD,IAAIN,OAAO,CAACO,OAAO,EAAE;MACnBP,OAAO,CAACO,OAAO,CAACC,QAAQ,CAAC,OAAOF,IAAI,KAAK,QAAQ,GAAG;QAClDG,KAAK,EAAEH;MACT,CAAC,GAAGA,IAAI,CAAC;IACX;EACF,CAAC;;EAED;EACA,IAAII,qBAAqB,GAAG,SAASA,qBAAqBA,CAACD,KAAK,EAAE;IAChE,IAAIE,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIG,GAAG,GAAGlB,kBAAkB,CAACgB,MAAM;IACnC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIT,OAAO,GAAG,CAACE,KAAK,GAAGO,CAAC,GAAGL,MAAM,GAAGI,GAAG,IAAIA,GAAG;MAC9C,IAAIE,qBAAqB,GAAGpB,kBAAkB,CAACU,OAAO,CAAC;QACrDW,KAAK,GAAGD,qBAAqB,CAACC,KAAK;QACnCC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;MACnC,IAAI,CAACD,KAAK,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;QAC5B,OAAOb,OAAO;MAChB;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,IAAIc,eAAe,GAAGjE,KAAK,CAACkE,QAAQ,CAAC,YAAY;MAC7C,OAAOZ,qBAAqB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC;IACFa,gBAAgB,GAAGrE,cAAc,CAACmE,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACjB,KAAK,EAAE;IACxC,IAAIkB,YAAY,GAAGf,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5Fa,cAAc,CAAChB,KAAK,CAAC;IACrB,IAAImB,IAAI,GAAG;MACTC,MAAM,EAAEF,YAAY,GAAG,UAAU,GAAG;IACtC,CAAC;;IAED;IACA,IAAIG,WAAW,GAAGjC,kBAAkB,CAACY,KAAK,CAAC;IAC3C,IAAI,CAACqB,WAAW,EAAE;MAChB5C,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE0C,IAAI,CAAC;MAC7B;IACF;IACA1C,aAAa,CAAC4C,WAAW,CAACC,KAAK,EAAEtB,KAAK,EAAEmB,IAAI,CAAC;EAC/C,CAAC;;EAED;EACAvE,SAAS,CAAC,YAAY;IACpBqE,SAAS,CAACvC,wBAAwB,KAAK,KAAK,GAAGuB,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACb,kBAAkB,CAACgB,MAAM,EAAElC,WAAW,CAAC,CAAC;;EAE5C;EACA,IAAIqD,UAAU,GAAG5E,KAAK,CAAC6E,WAAW,CAAC,UAAUF,KAAK,EAAE;IAClD,OAAOzC,SAAS,CAAC4C,GAAG,CAACH,KAAK,CAAC,IAAIrD,IAAI,KAAK,UAAU;EACpD,CAAC,EAAE,CAACA,IAAI,EAAEzB,kBAAkB,CAACqC,SAAS,CAAC,CAAC6C,QAAQ,CAAC,CAAC,EAAE7C,SAAS,CAAC8C,IAAI,CAAC,CAAC;;EAEpE;EACA/E,SAAS,CAAC,YAAY;IACpB;AACJ;AACA;AACA;AACA;IACI,IAAIgF,SAAS,GAAGC,UAAU,CAAC,YAAY;MACrC,IAAI,CAAC7D,QAAQ,IAAID,IAAI,IAAIc,SAAS,CAAC8C,IAAI,KAAK,CAAC,EAAE;QAC7C,IAAIL,KAAK,GAAGQ,KAAK,CAACC,IAAI,CAAClD,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC,IAAImB,KAAK,GAAGZ,kBAAkB,CAAC4C,SAAS,CAAC,UAAUC,IAAI,EAAE;UACvD,IAAIvB,IAAI,GAAGuB,IAAI,CAACvB,IAAI;UACpB,OAAOA,IAAI,CAACY,KAAK,KAAKA,KAAK;QAC7B,CAAC,CAAC;QACF,IAAItB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBiB,SAAS,CAACjB,KAAK,CAAC;UAChBJ,cAAc,CAACI,KAAK,CAAC;QACvB;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIjC,IAAI,EAAE;MACR,IAAImE,gBAAgB;MACpB,CAACA,gBAAgB,GAAG3C,OAAO,CAACO,OAAO,MAAM,IAAI,IAAIoC,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACnC,QAAQ,CAACM,SAAS,CAAC;IAC9H;IACA,OAAO,YAAY;MACjB,OAAO8B,YAAY,CAACP,SAAS,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAAC7D,IAAI,EAAEG,WAAW,CAAC,CAAC;;EAEvB;EACA,IAAIkE,aAAa,GAAG,SAASA,aAAaA,CAACd,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAKjB,SAAS,EAAE;MACvB1B,QAAQ,CAAC2C,KAAK,EAAE;QACde,QAAQ,EAAE,CAACxD,SAAS,CAAC4C,GAAG,CAACH,KAAK;MAChC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACtD,QAAQ,EAAE;MACbG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAxB,KAAK,CAAC2F,mBAAmB,CAAC3E,GAAG,EAAE,YAAY;IACzC,OAAO;MACL4E,SAAS,EAAE,SAASA,SAASA,CAAC7C,KAAK,EAAE;QACnC,IAAI8C,KAAK,GAAG9C,KAAK,CAAC8C,KAAK;UACrBC,OAAO,GAAG/C,KAAK,CAAC+C,OAAO;QACzB,QAAQD,KAAK;UACX;UACA,KAAK3F,OAAO,CAAC6F,CAAC;UACd,KAAK7F,OAAO,CAAC8F,CAAC;UACd,KAAK9F,OAAO,CAAC+F,EAAE;UACf,KAAK/F,OAAO,CAACgG,IAAI;YACf;cACE,IAAI3C,MAAM,GAAG,CAAC;cACd,IAAIsC,KAAK,KAAK3F,OAAO,CAAC+F,EAAE,EAAE;gBACxB1C,MAAM,GAAG,CAAC,CAAC;cACb,CAAC,MAAM,IAAIsC,KAAK,KAAK3F,OAAO,CAACgG,IAAI,EAAE;gBACjC3C,MAAM,GAAG,CAAC;cACZ,CAAC,MAAM,IAAI9C,aAAa,CAAC,CAAC,IAAIqF,OAAO,EAAE;gBACrC,IAAID,KAAK,KAAK3F,OAAO,CAAC6F,CAAC,EAAE;kBACvBxC,MAAM,GAAG,CAAC;gBACZ,CAAC,MAAM,IAAIsC,KAAK,KAAK3F,OAAO,CAAC8F,CAAC,EAAE;kBAC9BzC,MAAM,GAAG,CAAC,CAAC;gBACb;cACF;cACA,IAAIA,MAAM,KAAK,CAAC,EAAE;gBAChB,IAAI4C,eAAe,GAAG7C,qBAAqB,CAACc,WAAW,GAAGb,MAAM,EAAEA,MAAM,CAAC;gBACzEN,cAAc,CAACkD,eAAe,CAAC;gBAC/B7B,SAAS,CAAC6B,eAAe,EAAE,IAAI,CAAC;cAClC;cACA;YACF;;UAEF;UACA,KAAKjG,OAAO,CAACkG,KAAK;YAChB;cACE;cACA,IAAIC,IAAI,GAAG5D,kBAAkB,CAAC2B,WAAW,CAAC;cAC1C,IAAIiC,IAAI,IAAI,CAACA,IAAI,CAACtC,IAAI,CAACC,QAAQ,EAAE;gBAC/ByB,aAAa,CAACY,IAAI,CAAC1B,KAAK,CAAC;cAC3B,CAAC,MAAM;gBACLc,aAAa,CAAC/B,SAAS,CAAC;cAC1B;cACA,IAAItC,IAAI,EAAE;gBACR2B,KAAK,CAACC,cAAc,CAAC,CAAC;cACxB;cACA;YACF;;UAEF;UACA,KAAK9C,OAAO,CAACoG,GAAG;YACd;cACE9E,UAAU,CAAC,KAAK,CAAC;cACjB,IAAIJ,IAAI,EAAE;gBACR2B,KAAK,CAACwD,eAAe,CAAC,CAAC;cACzB;YACF;QACJ;MACF,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;MAC9BpD,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;QACjCJ,cAAc,CAACI,KAAK,CAAC;MACvB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIZ,kBAAkB,CAACgB,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO,aAAazD,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE;MAC7CC,IAAI,EAAE,SAAS;MACfvF,EAAE,EAAE,EAAE,CAACqB,MAAM,CAACrB,EAAE,EAAE,OAAO,CAAC;MAC1BwF,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC;MAC7CqE,WAAW,EAAE9D;IACf,CAAC,EAAErB,eAAe,CAAC;EACrB;EACA,IAAIoF,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAAC5E,UAAU,CAAC,CAAC6E,GAAG,CAAC,UAAUC,GAAG,EAAE;IACjE,OAAO9E,UAAU,CAAC8E,GAAG,CAAC;EACxB,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACb,IAAI,EAAE;IACrC,OAAOA,IAAI,CAACc,KAAK;EACnB,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAC/D,KAAK,EAAE;IAC1C,IAAIgD,IAAI,GAAG5D,kBAAkB,CAACY,KAAK,CAAC;IACpC,IAAI,CAACgD,IAAI,EAAE,OAAO,IAAI;IACtB,IAAIgB,QAAQ,GAAGhB,IAAI,CAACtC,IAAI,IAAI,CAAC,CAAC;IAC9B,IAAIY,KAAK,GAAG0C,QAAQ,CAAC1C,KAAK;IAC1B,IAAIb,KAAK,GAAGuC,IAAI,CAACvC,KAAK;IACtB,IAAIwD,KAAK,GAAGlH,SAAS,CAACiH,QAAQ,EAAE,IAAI,CAAC;IACrC,IAAIE,WAAW,GAAGL,QAAQ,CAACb,IAAI,CAAC;IAChC,OAAOA,IAAI,GAAG,aAAarG,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE7G,QAAQ,CAAC;MAC7D,YAAY,EAAE,OAAO2H,WAAW,KAAK,QAAQ,IAAI,CAACzD,KAAK,GAAGyD,WAAW,GAAG;IAC1E,CAAC,EAAED,KAAK,EAAE;MACRL,GAAG,EAAE5D,KAAK;MACVqD,IAAI,EAAE5C,KAAK,GAAG,cAAc,GAAG,QAAQ;MACvC3C,EAAE,EAAE,EAAE,CAACqB,MAAM,CAACrB,EAAE,EAAE,QAAQ,CAAC,CAACqB,MAAM,CAACa,KAAK,CAAC;MACzC,eAAe,EAAEuB,UAAU,CAACD,KAAK;IACnC,CAAC,CAAC,EAAEA,KAAK,CAAC,GAAG,IAAI;EACnB,CAAC;EACD,OAAO,aAAa3E,KAAK,CAACyG,aAAa,CAACzG,KAAK,CAACwH,QAAQ,EAAE,IAAI,EAAE,aAAaxH,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE;IACpGC,IAAI,EAAE,SAAS;IACfvF,EAAE,EAAE,EAAE,CAACqB,MAAM,CAACrB,EAAE,EAAE,OAAO,CAAC;IAC1BsG,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAER,UAAU,CAAChD,WAAW,GAAG,CAAC,CAAC,EAAEgD,UAAU,CAAChD,WAAW,CAAC,EAAEgD,UAAU,CAAChD,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,aAAapE,KAAK,CAACyG,aAAa,CAAClG,IAAI,EAAE;IAC5HsH,OAAO,EAAE,KAAK;IACd7G,GAAG,EAAE4B,OAAO;IACZmB,IAAI,EAAEtB,kBAAkB;IACxBiF,MAAM,EAAErF,UAAU;IAClByF,UAAU,EAAExF,cAAc;IAC1ByF,UAAU,EAAE,KAAK;IACjBnB,WAAW,EAAE9D,eAAe;IAC5BkF,QAAQ,EAAEtG,aAAa;IACvBU,OAAO,EAAEA;EACX,CAAC,EAAE,UAAUiE,IAAI,EAAE4B,SAAS,EAAE;IAC5B,IAAIC,WAAW;IACf,IAAIpE,KAAK,GAAGuC,IAAI,CAACvC,KAAK;MACpBqE,WAAW,GAAG9B,IAAI,CAAC8B,WAAW;MAC9BpE,IAAI,GAAGsC,IAAI,CAACtC,IAAI;MAChBoD,KAAK,GAAGd,IAAI,CAACc,KAAK;MAClBxC,KAAK,GAAG0B,IAAI,CAAC1B,KAAK;IACpB,IAAIsC,GAAG,GAAGlD,IAAI,CAACkD,GAAG;;IAElB;IACA,IAAInD,KAAK,EAAE;MACT,IAAIsE,WAAW;MACf,IAAIC,UAAU,GAAG,CAACD,WAAW,GAAGrE,IAAI,CAACuE,KAAK,MAAM,IAAI,IAAIF,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGxH,WAAW,CAACuG,KAAK,CAAC,GAAGA,KAAK,CAACpC,QAAQ,CAAC,CAAC,GAAGrB,SAAS;MAChJ,OAAO,aAAa1D,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE;QAC7CE,SAAS,EAAErG,UAAU,CAACiC,aAAa,EAAE,EAAE,CAACC,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC,CAAC;QACxE+F,KAAK,EAAED;MACT,CAAC,EAAElB,KAAK,KAAKzD,SAAS,GAAGyD,KAAK,GAAGF,GAAG,CAAC;IACvC;IACA,IAAIjD,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC1BsE,KAAK,GAAGvE,IAAI,CAACuE,KAAK;MAClBC,QAAQ,GAAGxE,IAAI,CAACwE,QAAQ;MACxBd,KAAK,GAAG1D,IAAI,CAAC0D,KAAK;MAClBd,SAAS,GAAG5C,IAAI,CAAC4C,SAAS;MAC1B6B,UAAU,GAAG7I,wBAAwB,CAACoE,IAAI,EAAEhE,SAAS,CAAC;IACxD,IAAI0I,WAAW,GAAGtI,IAAI,CAACqI,UAAU,EAAE3B,iBAAiB,CAAC;;IAErD;IACA,IAAInB,QAAQ,GAAGd,UAAU,CAACD,KAAK,CAAC;IAChC,IAAI+D,eAAe,GAAG,EAAE,CAAClG,MAAM,CAACD,aAAa,EAAE,SAAS,CAAC;IACzD,IAAIoG,eAAe,GAAGrI,UAAU,CAACiC,aAAa,EAAEmG,eAAe,EAAE/B,SAAS,GAAGuB,WAAW,GAAG,CAAC,CAAC,EAAExI,eAAe,CAACwI,WAAW,EAAE,EAAE,CAAC1F,MAAM,CAACkG,eAAe,EAAE,UAAU,CAAC,EAAEP,WAAW,CAAC,EAAEzI,eAAe,CAACwI,WAAW,EAAE,EAAE,CAAC1F,MAAM,CAACkG,eAAe,EAAE,SAAS,CAAC,EAAEtE,WAAW,KAAK6D,SAAS,IAAI,CAACjE,QAAQ,CAAC,EAAEtE,eAAe,CAACwI,WAAW,EAAE,EAAE,CAAC1F,MAAM,CAACkG,eAAe,EAAE,WAAW,CAAC,EAAE1E,QAAQ,CAAC,EAAEtE,eAAe,CAACwI,WAAW,EAAE,EAAE,CAAC1F,MAAM,CAACkG,eAAe,EAAE,WAAW,CAAC,EAAEhD,QAAQ,CAAC,EAAEwC,WAAW,CAAC,CAAC;IAC9c,IAAIX,WAAW,GAAGL,QAAQ,CAACb,IAAI,CAAC;IAChC,IAAIuC,WAAW,GAAG,CAAC3G,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,UAAU,IAAIyD,QAAQ;;IAEjG;IACA,IAAI7E,OAAO,GAAG,OAAO0G,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,IAAI5C,KAAK;IAClF;IACA,IAAIkE,WAAW,GAAGjI,WAAW,CAACC,OAAO,CAAC,GAAGA,OAAO,CAACkE,QAAQ,CAAC,CAAC,GAAGrB,SAAS;IACvE,IAAI4E,KAAK,KAAK5E,SAAS,EAAE;MACvBmF,WAAW,GAAGP,KAAK;IACrB;IACA,OAAO,aAAatI,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE7G,QAAQ,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAACqI,WAAW,CAAC,EAAE;MAClF,eAAe,EAAE/C,QAAQ;MACzBiB,SAAS,EAAEgC,eAAe;MAC1BL,KAAK,EAAEO,WAAW;MAClBC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,IAAI1E,WAAW,KAAK6D,SAAS,IAAIjE,QAAQ,EAAE;UACzC;QACF;QACAM,SAAS,CAAC2D,SAAS,CAAC;MACtB,CAAC;MACDc,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAI,CAAC/E,QAAQ,EAAE;UACbyB,aAAa,CAACd,KAAK,CAAC;QACtB;MACF,CAAC;MACD8C,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE,aAAazH,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE;MAC1CE,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACkG,eAAe,EAAE,UAAU;IAClD,CAAC,EAAE7H,OAAO,CAAC,EAAE,aAAab,KAAK,CAACgJ,cAAc,CAAC/G,oBAAoB,CAAC,IAAIyD,QAAQ,EAAEkD,WAAW,IAAI,aAAa5I,KAAK,CAACyG,aAAa,CAACjG,QAAQ,EAAE;MAC1ImG,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACD,aAAa,EAAE,eAAe,CAAC;MACpD0G,aAAa,EAAEhH,oBAAoB;MACnCiH,kBAAkB,EAAE;QAClBtE,UAAU,EAAEc;MACd;IACF,CAAC,EAAEA,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIyD,aAAa,GAAG,aAAanJ,KAAK,CAACoJ,UAAU,CAACtI,UAAU,CAAC;AAC7DqI,aAAa,CAACE,WAAW,GAAG,YAAY;AACxC,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}