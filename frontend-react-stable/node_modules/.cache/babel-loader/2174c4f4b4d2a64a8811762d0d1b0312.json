{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nvar EllipsisTooltip = function EllipsisTooltip(_ref) {\n  var enabledEllipsis = _ref.enabledEllipsis,\n    isEllipsis = _ref.isEllipsis,\n    children = _ref.children,\n    tooltipProps = _ref.tooltipProps;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON>", "EllipsisTooltip", "_ref", "enabledEllipsis", "isEllipsis", "children", "tooltipProps", "title", "createElement", "open", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Base/EllipsisTooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nvar EllipsisTooltip = function EllipsisTooltip(_ref) {\n  var enabledEllipsis = _ref.enabledEllipsis,\n    isEllipsis = _ref.isEllipsis,\n    children = _ref.children,\n    tooltipProps = _ref.tooltipProps;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AACnC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EACnD,IAAIC,eAAe,GAAGD,IAAI,CAACC,eAAe;IACxCC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,YAAY,GAAGJ,IAAI,CAACI,YAAY;EAClC,IAAI,EAAEA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACC,KAAK,CAAC,IAAI,CAACJ,eAAe,EAAE;IACzG,OAAOE,QAAQ;EACjB;EACA,OAAO,aAAaN,KAAK,CAACS,aAAa,CAACR,OAAO,EAAEF,QAAQ,CAAC;IACxDW,IAAI,EAAEL,UAAU,GAAGM,SAAS,GAAG;EACjC,CAAC,EAAEJ,YAAY,CAAC,EAAED,QAAQ,CAAC;AAC7B,CAAC;AACD,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCZ,eAAe,CAACa,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAeb,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}