{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = union;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction union(...others) {\n  const set = new _index.InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "union", "_index", "require", "others", "set", "InternSet", "other", "o", "add"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/union.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = union;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction union(...others) {\n  const set = new _index.InternSet();\n\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n\n  return set;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,KAAKA,CAAC,GAAGG,MAAM,EAAE;EACxB,MAAMC,GAAG,GAAG,IAAIH,MAAM,CAACI,SAAS,CAAC,CAAC;EAElC,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMI,CAAC,IAAID,KAAK,EAAE;MACrBF,GAAG,CAACI,GAAG,CAACD,CAAC,CAAC;IACZ;EACF;EAEA,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}