{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from './MeasureCell';\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    columnsKey = _ref.columnsKey,\n    onColumnResize = _ref.onColumnResize;\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    }\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      infoList.forEach(function (_ref2) {\n        var columnKey = _ref2.data,\n          size = _ref2.size;\n        onColumnResize(columnKey, size.offsetWidth);\n      });\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "MeasureCell", "MeasureRow", "_ref", "prefixCls", "columnsKey", "onColumnResize", "createElement", "className", "concat", "style", "height", "fontSize", "Collection", "onBatchResize", "infoList", "for<PERSON>ach", "_ref2", "column<PERSON>ey", "data", "size", "offsetWidth", "map", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Body/MeasureRow.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from './MeasureCell';\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n      columnsKey = _ref.columnsKey,\n      onColumnResize = _ref.onColumnResize;\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    }\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      infoList.forEach(function (_ref2) {\n        var columnKey = _ref2.data,\n            size = _ref2.size;\n        onColumnResize(columnKey, size.offsetWidth);\n      });\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAE;EACvC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACxC,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAAC,IAAI,EAAE;IAC5C,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,cAAc,CAAC;IAC/CM,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,aAAab,KAAK,CAACQ,aAAa,CAACP,cAAc,CAACa,UAAU,EAAE;IAC7DC,aAAa,EAAE,SAASA,aAAaA,CAACC,QAAQ,EAAE;MAC9CA,QAAQ,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;QAChC,IAAIC,SAAS,GAAGD,KAAK,CAACE,IAAI;UACtBC,IAAI,GAAGH,KAAK,CAACG,IAAI;QACrBd,cAAc,CAACY,SAAS,EAAEE,IAAI,CAACC,WAAW,CAAC;MAC7C,CAAC,CAAC;IACJ;EACF,CAAC,EAAEhB,UAAU,CAACiB,GAAG,CAAC,UAAUJ,SAAS,EAAE;IACrC,OAAO,aAAanB,KAAK,CAACQ,aAAa,CAACN,WAAW,EAAE;MACnDsB,GAAG,EAAEL,SAAS;MACdA,SAAS,EAAEA,SAAS;MACpBZ,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module"}