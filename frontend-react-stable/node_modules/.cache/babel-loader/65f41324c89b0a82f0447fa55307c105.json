{"ast": null, "code": "/**\n * Cut input selection into 2 part and return text before selection start\n */\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\n\nexport function getLastMeasureIndex(text, prefix) {\n  return prefix.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  } // Reuse rest text as it can\n\n  var restText = text;\n  var targetTextLen = targetText.length;\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\n\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n    prefix = measureConfig.prefix,\n    targetText = measureConfig.targetText,\n    selectionStart = measureConfig.selectionStart,\n    split = measureConfig.split; // Before text will append one space if have other text\n\n  var beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  } // Cut duplicate string with current targetText\n\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, props) {\n  var split = props.split;\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n    value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}", "map": {"version": 3, "names": ["getBeforeSelectionText", "input", "selectionStart", "value", "slice", "getLastMeasureIndex", "text", "prefix", "reduce", "lastMatch", "prefixStr", "lastIndex", "lastIndexOf", "location", "lower", "char", "toLowerCase", "reduceText", "targetText", "split", "firstChar", "restText", "targetTextLen", "length", "i", "replaceWithMeasure", "measureConfig", "measureLocation", "beforeMeasureText", "concat", "connectedStartText", "selectionLocation", "setInputSelection", "setSelectionRange", "blur", "focus", "validateSearch", "props", "indexOf", "filterOption", "_ref", "_ref$value", "lowerCase"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-mentions/es/util.js"], "sourcesContent": ["/**\n * Cut input selection into 2 part and return text before selection start\n */\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\n\nexport function getLastMeasureIndex(text, prefix) {\n  return prefix.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\n\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\n\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n\n  if (!firstChar || firstChar === split) {\n    return text;\n  } // Reuse rest text as it can\n\n\n  var restText = text;\n  var targetTextLen = targetText.length;\n\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n\n  return restText;\n}\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\n\n\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n      prefix = measureConfig.prefix,\n      targetText = measureConfig.targetText,\n      selectionStart = measureConfig.selectionStart,\n      split = measureConfig.split; // Before text will append one space if have other text\n\n  var beforeMeasureText = text.slice(0, measureLocation);\n\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  } // Cut duplicate string with current targetText\n\n\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, props) {\n  var split = props.split;\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n      value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;EACzC,OAAOD,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;AAC7C;AACA;AACA;AACA;;AAEA,OAAO,SAASG,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChD,OAAOA,MAAM,CAACC,MAAM,CAAC,UAAUC,SAAS,EAAEC,SAAS,EAAE;IACnD,IAAIC,SAAS,GAAGL,IAAI,CAACM,WAAW,CAACF,SAAS,CAAC;IAE3C,IAAIC,SAAS,GAAGF,SAAS,CAACI,QAAQ,EAAE;MAClC,OAAO;QACLA,QAAQ,EAAEF,SAAS;QACnBJ,MAAM,EAAEG;MACV,CAAC;IACH;IAEA,OAAOD,SAAS;EAClB,CAAC,EAAE;IACDI,QAAQ,EAAE,CAAC,CAAC;IACZN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,SAASO,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;AACnC;AAEA,SAASC,UAAUA,CAACX,IAAI,EAAEY,UAAU,EAAEC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGd,IAAI,CAAC,CAAC,CAAC;EAEvB,IAAI,CAACc,SAAS,IAAIA,SAAS,KAAKD,KAAK,EAAE;IACrC,OAAOb,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIe,QAAQ,GAAGf,IAAI;EACnB,IAAIgB,aAAa,GAAGJ,UAAU,CAACK,MAAM;EAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIV,KAAK,CAACO,QAAQ,CAACG,CAAC,CAAC,CAAC,KAAKV,KAAK,CAACI,UAAU,CAACM,CAAC,CAAC,CAAC,EAAE;MAC/CH,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACoB,CAAC,CAAC;MAC5B;IACF,CAAC,MAAM,IAAIA,CAAC,KAAKF,aAAa,GAAG,CAAC,EAAE;MAClCD,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACkB,aAAa,CAAC;IAC1C;EACF;EAEA,OAAOD,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASI,kBAAkBA,CAACnB,IAAI,EAAEoB,aAAa,EAAE;EACtD,IAAIC,eAAe,GAAGD,aAAa,CAACC,eAAe;IAC/CpB,MAAM,GAAGmB,aAAa,CAACnB,MAAM;IAC7BW,UAAU,GAAGQ,aAAa,CAACR,UAAU;IACrChB,cAAc,GAAGwB,aAAa,CAACxB,cAAc;IAC7CiB,KAAK,GAAGO,aAAa,CAACP,KAAK,CAAC,CAAC;;EAEjC,IAAIS,iBAAiB,GAAGtB,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEuB,eAAe,CAAC;EAEtD,IAAIC,iBAAiB,CAACA,iBAAiB,CAACL,MAAM,GAAGJ,KAAK,CAACI,MAAM,CAAC,KAAKJ,KAAK,EAAE;IACxES,iBAAiB,GAAGA,iBAAiB,CAACxB,KAAK,CAAC,CAAC,EAAEwB,iBAAiB,CAACL,MAAM,GAAGJ,KAAK,CAACI,MAAM,CAAC;EACzF;EAEA,IAAIK,iBAAiB,EAAE;IACrBA,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACV,KAAK,CAAC;EAChE,CAAC,CAAC;;EAGF,IAAIE,QAAQ,GAAGJ,UAAU,CAACX,IAAI,CAACF,KAAK,CAACF,cAAc,CAAC,EAAEgB,UAAU,CAACd,KAAK,CAACF,cAAc,GAAGyB,eAAe,GAAGpB,MAAM,CAACgB,MAAM,CAAC,EAAEJ,KAAK,CAAC;EAEhI,IAAIE,QAAQ,CAACjB,KAAK,CAAC,CAAC,EAAEe,KAAK,CAACI,MAAM,CAAC,KAAKJ,KAAK,EAAE;IAC7CE,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACe,KAAK,CAACI,MAAM,CAAC;EACzC;EAEA,IAAIO,kBAAkB,GAAG,EAAE,CAACD,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACtB,MAAM,CAAC,CAACsB,MAAM,CAACX,UAAU,CAAC,CAACW,MAAM,CAACV,KAAK,CAAC;EACrG,OAAO;IACLb,IAAI,EAAE,EAAE,CAACuB,MAAM,CAACC,kBAAkB,CAAC,CAACD,MAAM,CAACR,QAAQ,CAAC;IACpDU,iBAAiB,EAAED,kBAAkB,CAACP;EACxC,CAAC;AACH;AACA,OAAO,SAASS,iBAAiBA,CAAC/B,KAAK,EAAEY,QAAQ,EAAE;EACjDZ,KAAK,CAACgC,iBAAiB,CAACpB,QAAQ,EAAEA,QAAQ,CAAC;EAC3C;AACF;AACA;AACA;;EAEEZ,KAAK,CAACiC,IAAI,CAAC,CAAC;EACZjC,KAAK,CAACkC,KAAK,CAAC,CAAC;AACf;AACA,OAAO,SAASC,cAAcA,CAAC9B,IAAI,EAAE+B,KAAK,EAAE;EAC1C,IAAIlB,KAAK,GAAGkB,KAAK,CAAClB,KAAK;EACvB,OAAO,CAACA,KAAK,IAAIb,IAAI,CAACgC,OAAO,CAACnB,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,OAAO,SAASoB,YAAYA,CAACtC,KAAK,EAAEuC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGD,IAAI,CAACrC,KAAK;IACvBA,KAAK,GAAGsC,UAAU,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,UAAU;EACnD,IAAIC,SAAS,GAAGzC,KAAK,CAACe,WAAW,CAAC,CAAC;EACnC,OAAOb,KAAK,CAACa,WAAW,CAAC,CAAC,CAACsB,OAAO,CAACI,SAAS,CAAC,KAAK,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}