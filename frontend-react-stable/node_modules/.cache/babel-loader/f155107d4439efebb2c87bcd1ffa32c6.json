{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"showSearch\", \"tagRender\", \"direction\", \"omitDomProps\", \"displayValues\", \"onDisplayValuesChange\", \"emptyOptions\", \"notFoundContent\", \"onClear\", \"mode\", \"disabled\", \"loading\", \"getInputElement\", \"getRawInputElement\", \"open\", \"defaultOpen\", \"onDropdownVisibleChange\", \"activeValue\", \"onActiveValueChange\", \"activeDescendantId\", \"searchValue\", \"autoClearSearchValue\", \"onSearch\", \"onSearchSplit\", \"tokenSeparators\", \"allowClear\", \"showArrow\", \"inputIcon\", \"clearIcon\", \"OptionList\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"placement\", \"getPopupContainer\", \"showAction\", \"onFocus\", \"onBlur\", \"onKeyUp\", \"onKeyDown\", \"onMouseDown\"];\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { BaseSelectContext } from \"./hooks/useBaseProps\";\nimport useDelayReset from \"./hooks/useDelayReset\";\nimport useLock from \"./hooks/useLock\";\nimport useSelectTriggerControl from \"./hooks/useSelectTriggerControl\";\nimport Selector from \"./Selector\";\nimport SelectTrigger from \"./SelectTrigger\";\nimport TransBtn from \"./TransBtn\";\nimport { getSeparatedContent } from \"./utils/valueUtil\";\nvar DEFAULT_OMIT_PROPS = ['value', 'onChange', 'removeIcon', 'placeholder', 'autoFocus', 'maxTagCount', 'maxTagTextLength', 'maxTagPlaceholder', 'choiceTransitionName', 'onInputKeyDown', 'onPopupScroll', 'tabIndex'];\nexport function isMultiple(mode) {\n  return mode === 'tags' || mode === 'multiple';\n}\nvar BaseSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _customizeRawInputEle, _classNames2;\n  var id = props.id,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    showSearch = props.showSearch,\n    tagRender = props.tagRender,\n    direction = props.direction,\n    omitDomProps = props.omitDomProps,\n    displayValues = props.displayValues,\n    onDisplayValuesChange = props.onDisplayValuesChange,\n    emptyOptions = props.emptyOptions,\n    _props$notFoundConten = props.notFoundContent,\n    notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n    onClear = props.onClear,\n    mode = props.mode,\n    disabled = props.disabled,\n    loading = props.loading,\n    getInputElement = props.getInputElement,\n    getRawInputElement = props.getRawInputElement,\n    open = props.open,\n    defaultOpen = props.defaultOpen,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    activeValue = props.activeValue,\n    onActiveValueChange = props.onActiveValueChange,\n    activeDescendantId = props.activeDescendantId,\n    searchValue = props.searchValue,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSplit = props.onSearchSplit,\n    tokenSeparators = props.tokenSeparators,\n    allowClear = props.allowClear,\n    showArrow = props.showArrow,\n    inputIcon = props.inputIcon,\n    clearIcon = props.clearIcon,\n    OptionList = props.OptionList,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    placement = props.placement,\n    getPopupContainer = props.getPopupContainer,\n    _props$showAction = props.showAction,\n    showAction = _props$showAction === void 0 ? [] : _props$showAction,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyUp = props.onKeyUp,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // ============================== MISC ==============================\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = (showSearch !== undefined ? showSearch : multiple) || mode === 'combobox';\n  var domProps = _objectSpread({}, restProps);\n  DEFAULT_OMIT_PROPS.forEach(function (propName) {\n    delete domProps[propName];\n  });\n  omitDomProps === null || omitDomProps === void 0 ? void 0 : omitDomProps.forEach(function (propName) {\n    delete domProps[propName];\n  });\n\n  // ============================= Mobile =============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mobile = _React$useState2[0],\n    setMobile = _React$useState2[1];\n  React.useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ============================== Refs ==============================\n  var containerRef = React.useRef(null);\n  var selectorDomRef = React.useRef(null);\n  var triggerRef = React.useRef(null);\n  var selectorRef = React.useRef(null);\n  var listRef = React.useRef(null);\n\n  /** Used for component focused management */\n  var _useDelayReset = useDelayReset(),\n    _useDelayReset2 = _slicedToArray(_useDelayReset, 3),\n    mockFocused = _useDelayReset2[0],\n    setMockFocused = _useDelayReset2[1],\n    cancelSetMockFocused = _useDelayReset2[2];\n\n  // =========================== Imperative ===========================\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current, _selectorRef$current2;\n    return {\n      focus: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.focus,\n      blur: (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 ? void 0 : _selectorRef$current2.blur,\n      scrollTo: function scrollTo(arg) {\n        var _listRef$current;\n        return (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(arg);\n      }\n    };\n  });\n\n  // ========================== Search Value ==========================\n  var mergedSearchValue = React.useMemo(function () {\n    var _displayValues$;\n    if (mode !== 'combobox') {\n      return searchValue;\n    }\n    var val = (_displayValues$ = displayValues[0]) === null || _displayValues$ === void 0 ? void 0 : _displayValues$.value;\n    return typeof val === 'string' || typeof val === 'number' ? String(val) : '';\n  }, [searchValue, mode, displayValues]);\n\n  // ========================== Custom Input ==========================\n  // Only works in `combobox`\n  var customizeInputElement = mode === 'combobox' && typeof getInputElement === 'function' && getInputElement() || null;\n\n  // Used for customize replacement for `rc-cascader`\n  var customizeRawInputElement = typeof getRawInputElement === 'function' && getRawInputElement();\n  var customizeRawInputRef = useComposeRef(selectorDomRef, customizeRawInputElement === null || customizeRawInputElement === void 0 ? void 0 : (_customizeRawInputEle = customizeRawInputElement.props) === null || _customizeRawInputEle === void 0 ? void 0 : _customizeRawInputEle.ref);\n\n  // ============================== Open ==============================\n  var _useMergedState = useMergedState(undefined, {\n      defaultValue: defaultOpen,\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    innerOpen = _useMergedState2[0],\n    setInnerOpen = _useMergedState2[1];\n  var mergedOpen = innerOpen;\n\n  // Not trigger `open` in `combobox` when `notFoundContent` is empty\n  var emptyListContent = !notFoundContent && emptyOptions;\n  if (disabled || emptyListContent && mergedOpen && mode === 'combobox') {\n    mergedOpen = false;\n  }\n  var triggerOpen = emptyListContent ? false : mergedOpen;\n  var onToggleOpen = React.useCallback(function (newOpen) {\n    var nextOpen = newOpen !== undefined ? newOpen : !mergedOpen;\n    if (!disabled) {\n      setInnerOpen(nextOpen);\n      if (mergedOpen !== nextOpen) {\n        onDropdownVisibleChange === null || onDropdownVisibleChange === void 0 ? void 0 : onDropdownVisibleChange(nextOpen);\n      }\n    }\n  }, [disabled, mergedOpen, setInnerOpen, onDropdownVisibleChange]);\n\n  // ============================= Search =============================\n  var tokenWithEnter = React.useMemo(function () {\n    return (tokenSeparators || []).some(function (tokenSeparator) {\n      return ['\\n', '\\r\\n'].includes(tokenSeparator);\n    });\n  }, [tokenSeparators]);\n  var onInternalSearch = function onInternalSearch(searchText, fromTyping, isCompositing) {\n    var ret = true;\n    var newSearchText = searchText;\n    onActiveValueChange === null || onActiveValueChange === void 0 ? void 0 : onActiveValueChange(null);\n\n    // Check if match the `tokenSeparators`\n    var patchLabels = isCompositing ? null : getSeparatedContent(searchText, tokenSeparators);\n\n    // Ignore combobox since it's not split-able\n    if (mode !== 'combobox' && patchLabels) {\n      newSearchText = '';\n      onSearchSplit === null || onSearchSplit === void 0 ? void 0 : onSearchSplit(patchLabels);\n\n      // Should close when paste finish\n      onToggleOpen(false);\n\n      // Tell Selector that break next actions\n      ret = false;\n    }\n    if (onSearch && mergedSearchValue !== newSearchText) {\n      onSearch(newSearchText, {\n        source: fromTyping ? 'typing' : 'effect'\n      });\n    }\n    return ret;\n  };\n\n  // Only triggered when menu is closed & mode is tags\n  // If menu is open, OptionList will take charge\n  // If mode isn't tags, press enter is not meaningful when you can't see any option\n  var onInternalSearchSubmit = function onInternalSearchSubmit(searchText) {\n    // prevent empty tags from appearing when you click the Enter button\n    if (!searchText || !searchText.trim()) {\n      return;\n    }\n    onSearch(searchText, {\n      source: 'submit'\n    });\n  };\n\n  // Close will clean up single mode search text\n  React.useEffect(function () {\n    if (!mergedOpen && !multiple && mode !== 'combobox') {\n      onInternalSearch('', false, false);\n    }\n  }, [mergedOpen]);\n\n  // ============================ Disabled ============================\n  // Close dropdown & remove focus state when disabled change\n  React.useEffect(function () {\n    if (innerOpen && disabled) {\n      setInnerOpen(false);\n    }\n    if (disabled) {\n      setMockFocused(false);\n    }\n  }, [disabled]);\n\n  // ============================ Keyboard ============================\n  /**\n   * We record input value here to check if can press to clean up by backspace\n   * - null: Key is not down, this is reset by key up\n   * - true: Search text is empty when first time backspace down\n   * - false: Search text is not empty when first time backspace down\n   */\n  var _useLock = useLock(),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getClearLock = _useLock2[0],\n    setClearLock = _useLock2[1];\n\n  // KeyDown\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var clearLock = getClearLock();\n    var which = event.which;\n    if (which === KeyCode.ENTER) {\n      // Do not submit form when type in the input\n      if (mode !== 'combobox') {\n        event.preventDefault();\n      }\n\n      // We only manage open state here, close logic should handle by list component\n      if (!mergedOpen) {\n        onToggleOpen(true);\n      }\n    }\n    setClearLock(!!mergedSearchValue);\n\n    // Remove value by `backspace`\n    if (which === KeyCode.BACKSPACE && !clearLock && multiple && !mergedSearchValue && displayValues.length) {\n      var cloneDisplayValues = _toConsumableArray(displayValues);\n      var removedDisplayValue = null;\n      for (var i = cloneDisplayValues.length - 1; i >= 0; i -= 1) {\n        var current = cloneDisplayValues[i];\n        if (!current.disabled) {\n          cloneDisplayValues.splice(i, 1);\n          removedDisplayValue = current;\n          break;\n        }\n      }\n      if (removedDisplayValue) {\n        onDisplayValuesChange(cloneDisplayValues, {\n          type: 'remove',\n          values: [removedDisplayValue]\n        });\n      }\n    }\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current).onKeyDown.apply(_listRef$current2, [event].concat(rest));\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown.apply(void 0, [event].concat(rest));\n  };\n\n  // KeyUp\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      rest[_key2 - 1] = arguments[_key2];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current3;\n      (_listRef$current3 = listRef.current).onKeyUp.apply(_listRef$current3, [event].concat(rest));\n    }\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp.apply(void 0, [event].concat(rest));\n  };\n\n  // ============================ Selector ============================\n  var onSelectorRemove = function onSelectorRemove(val) {\n    var newValues = displayValues.filter(function (i) {\n      return i !== val;\n    });\n    onDisplayValuesChange(newValues, {\n      type: 'remove',\n      values: [val]\n    });\n  };\n\n  // ========================== Focus / Blur ==========================\n  /** Record real focus status */\n  var focusRef = React.useRef(false);\n  var onContainerFocus = function onContainerFocus() {\n    setMockFocused(true);\n    if (!disabled) {\n      if (onFocus && !focusRef.current) {\n        onFocus.apply(void 0, arguments);\n      }\n\n      // `showAction` should handle `focus` if set\n      if (showAction.includes('focus')) {\n        onToggleOpen(true);\n      }\n    }\n    focusRef.current = true;\n  };\n  var onContainerBlur = function onContainerBlur() {\n    setMockFocused(false, function () {\n      focusRef.current = false;\n      onToggleOpen(false);\n    });\n    if (disabled) {\n      return;\n    }\n    if (mergedSearchValue) {\n      // `tags` mode should move `searchValue` into values\n      if (mode === 'tags') {\n        onSearch(mergedSearchValue, {\n          source: 'submit'\n        });\n      } else if (mode === 'multiple') {\n        // `multiple` mode only clean the search value but not trigger event\n        onSearch('', {\n          source: 'blur'\n        });\n      }\n    }\n    if (onBlur) {\n      onBlur.apply(void 0, arguments);\n    }\n  };\n\n  // Give focus back of Select\n  var activeTimeoutIds = [];\n  React.useEffect(function () {\n    return function () {\n      activeTimeoutIds.forEach(function (timeoutId) {\n        return clearTimeout(timeoutId);\n      });\n      activeTimeoutIds.splice(0, activeTimeoutIds.length);\n    };\n  }, []);\n  var onInternalMouseDown = function onInternalMouseDown(event) {\n    var _triggerRef$current;\n    var target = event.target;\n    var popupElement = (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.getPopupElement();\n\n    // We should give focus back to selector if clicked item is not focusable\n    if (popupElement && popupElement.contains(target)) {\n      var timeoutId = setTimeout(function () {\n        var index = activeTimeoutIds.indexOf(timeoutId);\n        if (index !== -1) {\n          activeTimeoutIds.splice(index, 1);\n        }\n        cancelSetMockFocused();\n        if (!mobile && !popupElement.contains(document.activeElement)) {\n          var _selectorRef$current3;\n          (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 ? void 0 : _selectorRef$current3.focus();\n        }\n      });\n      activeTimeoutIds.push(timeoutId);\n    }\n    for (var _len3 = arguments.length, restArgs = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      restArgs[_key3 - 1] = arguments[_key3];\n    }\n    onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown.apply(void 0, [event].concat(restArgs));\n  };\n\n  // ============================ Dropdown ============================\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    containerWidth = _React$useState4[0],\n    setContainerWidth = _React$useState4[1];\n  var _React$useState5 = React.useState({}),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forceUpdate = _React$useState6[1];\n  // We need force update here since popup dom is render async\n  function onPopupMouseEnter() {\n    forceUpdate({});\n  }\n  useLayoutEffect(function () {\n    if (triggerOpen) {\n      var _containerRef$current;\n      var newWidth = Math.ceil((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.offsetWidth);\n      if (containerWidth !== newWidth && !Number.isNaN(newWidth)) {\n        setContainerWidth(newWidth);\n      }\n    }\n  }, [triggerOpen]);\n\n  // Used for raw custom input trigger\n  var onTriggerVisibleChange;\n  if (customizeRawInputElement) {\n    onTriggerVisibleChange = function onTriggerVisibleChange(newOpen) {\n      onToggleOpen(newOpen);\n    };\n  }\n\n  // Close when click on non-select element\n  useSelectTriggerControl(function () {\n    var _triggerRef$current2;\n    return [containerRef.current, (_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : _triggerRef$current2.getPopupElement()];\n  }, triggerOpen, onToggleOpen, !!customizeRawInputElement);\n\n  // ============================ Context =============================\n  var baseSelectContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, props), {}, {\n      notFoundContent: notFoundContent,\n      open: mergedOpen,\n      triggerOpen: triggerOpen,\n      id: id,\n      showSearch: mergedShowSearch,\n      multiple: multiple,\n      toggleOpen: onToggleOpen\n    });\n  }, [props, notFoundContent, triggerOpen, mergedOpen, id, mergedShowSearch, multiple, onToggleOpen]);\n\n  // ==================================================================\n  // ==                            Render                            ==\n  // ==================================================================\n\n  // ============================= Arrow ==============================\n  var mergedShowArrow = showArrow !== undefined ? showArrow : loading || !multiple && mode !== 'combobox';\n  var arrowNode;\n  if (mergedShowArrow) {\n    arrowNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: classNames(\"\".concat(prefixCls, \"-arrow\"), _defineProperty({}, \"\".concat(prefixCls, \"-arrow-loading\"), loading)),\n      customizeIcon: inputIcon,\n      customizeIconProps: {\n        loading: loading,\n        searchValue: mergedSearchValue,\n        open: mergedOpen,\n        focused: mockFocused,\n        showSearch: mergedShowSearch\n      }\n    });\n  }\n\n  // ============================= Clear ==============================\n  var clearNode;\n  var onClearMouseDown = function onClearMouseDown() {\n    var _selectorRef$current4;\n    onClear === null || onClear === void 0 ? void 0 : onClear();\n    (_selectorRef$current4 = selectorRef.current) === null || _selectorRef$current4 === void 0 ? void 0 : _selectorRef$current4.focus();\n    onDisplayValuesChange([], {\n      type: 'clear',\n      values: displayValues\n    });\n    onInternalSearch('', false, false);\n  };\n  if (!disabled && allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n    clearNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: clearIcon\n    }, \"\\xD7\");\n  }\n\n  // =========================== OptionList ===========================\n  var optionList = /*#__PURE__*/React.createElement(OptionList, {\n    ref: listRef\n  });\n\n  // ============================= Select =============================\n  var mergedClassName = classNames(prefixCls, className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mockFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-multiple\"), multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-single\"), !multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-allow-clear\"), allowClear), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-arrow\"), mergedShowArrow), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-open\"), mergedOpen), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-customize-input\"), customizeInputElement), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-search\"), mergedShowSearch), _classNames2));\n\n  // >>> Selector\n  var selectorNode = /*#__PURE__*/React.createElement(SelectTrigger, {\n    ref: triggerRef,\n    disabled: disabled,\n    prefixCls: prefixCls,\n    visible: triggerOpen,\n    popupElement: optionList,\n    containerWidth: containerWidth,\n    animation: animation,\n    transitionName: transitionName,\n    dropdownStyle: dropdownStyle,\n    dropdownClassName: dropdownClassName,\n    direction: direction,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n    dropdownRender: dropdownRender,\n    dropdownAlign: dropdownAlign,\n    placement: placement,\n    getPopupContainer: getPopupContainer,\n    empty: emptyOptions,\n    getTriggerDOMNode: function getTriggerDOMNode() {\n      return selectorDomRef.current;\n    },\n    onPopupVisibleChange: onTriggerVisibleChange,\n    onPopupMouseEnter: onPopupMouseEnter\n  }, customizeRawInputElement ? /*#__PURE__*/React.cloneElement(customizeRawInputElement, {\n    ref: customizeRawInputRef\n  }) : /*#__PURE__*/React.createElement(Selector, _extends({}, props, {\n    domRef: selectorDomRef,\n    prefixCls: prefixCls,\n    inputElement: customizeInputElement,\n    ref: selectorRef,\n    id: id,\n    showSearch: mergedShowSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    mode: mode,\n    activeDescendantId: activeDescendantId,\n    tagRender: tagRender,\n    values: displayValues,\n    open: mergedOpen,\n    onToggleOpen: onToggleOpen,\n    activeValue: activeValue,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSubmit: onInternalSearchSubmit,\n    onRemove: onSelectorRemove,\n    tokenWithEnter: tokenWithEnter\n  })));\n\n  // >>> Render\n  var renderNode;\n\n  // Render raw\n  if (customizeRawInputElement) {\n    renderNode = selectorNode;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: mergedClassName\n    }, domProps, {\n      ref: containerRef,\n      onMouseDown: onInternalMouseDown,\n      onKeyDown: onInternalKeyDown,\n      onKeyUp: onInternalKeyUp,\n      onFocus: onContainerFocus,\n      onBlur: onContainerBlur\n    }), mockFocused && !mergedOpen && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        width: 0,\n        height: 0,\n        position: 'absolute',\n        overflow: 'hidden',\n        opacity: 0\n      },\n      \"aria-live\": \"polite\"\n    }, \"\".concat(displayValues.map(function (_ref) {\n      var label = _ref.label,\n        value = _ref.value;\n      return ['number', 'string'].includes(_typeof(label)) ? label : value;\n    }).join(', '))), selectorNode, arrowNode, clearNode);\n  }\n  return /*#__PURE__*/React.createElement(BaseSelectContext.Provider, {\n    value: baseSelectContext\n  }, renderNode);\n});\n\n// Set display name for dev\nif (process.env.NODE_ENV !== 'production') {\n  BaseSelect.displayName = 'BaseSelect';\n}\nexport default BaseSelect;", "map": {"version": 3, "names": ["_typeof", "_extends", "_defineProperty", "_toConsumableArray", "_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "classNames", "useLayoutEffect", "useMergedState", "isMobile", "KeyCode", "useComposeRef", "React", "BaseSelectContext", "useDelayReset", "useLock", "useSelectTriggerControl", "Selector", "SelectTrigger", "TransBtn", "getSeparatedContent", "DEFAULT_OMIT_PROPS", "isMultiple", "mode", "BaseSelect", "forwardRef", "props", "ref", "_customizeRawInputEle", "_classNames2", "id", "prefixCls", "className", "showSearch", "tagRender", "direction", "omitDomProps", "displayValues", "onDisplayValuesChange", "emptyOptions", "_props$notFoundConten", "notFoundContent", "onClear", "disabled", "loading", "getInputElement", "getRawInputElement", "open", "defaultOpen", "onDropdownVisibleChange", "activeValue", "onActiveValueChange", "activeDescendantId", "searchValue", "autoClearSearchValue", "onSearch", "onSearchSplit", "tokenSeparators", "allowClear", "showArrow", "inputIcon", "clearIcon", "OptionList", "animation", "transitionName", "dropdownStyle", "dropdownClassName", "dropdownMatchSelectWidth", "dropdownRender", "dropdownAlign", "placement", "getPopupContainer", "_props$showAction", "showAction", "onFocus", "onBlur", "onKeyUp", "onKeyDown", "onMouseDown", "restProps", "multiple", "mergedShowSearch", "undefined", "domProps", "for<PERSON>ach", "propName", "_React$useState", "useState", "_React$useState2", "mobile", "setMobile", "useEffect", "containerRef", "useRef", "selectorDomRef", "triggerRef", "selectorRef", "listRef", "_useDelayReset", "_useDelayReset2", "mockFocused", "setMockFocused", "cancelSetMockFocused", "useImperativeHandle", "_selectorRef$current", "_selectorRef$current2", "focus", "current", "blur", "scrollTo", "arg", "_listRef$current", "mergedSearchValue", "useMemo", "_displayValues$", "val", "value", "String", "customizeInputElement", "customizeRawInputElement", "customizeRawInputRef", "_useMergedState", "defaultValue", "_useMergedState2", "innerOpen", "setInnerOpen", "mergedOpen", "emptyListContent", "triggerOpen", "onToggleOpen", "useCallback", "newOpen", "nextOpen", "tokenWithEnter", "some", "tokenSeparator", "includes", "onInternalSearch", "searchText", "fromTyping", "isCompositing", "ret", "newSearchText", "patchLabels", "source", "onInternalSearchSubmit", "trim", "_useLock", "_useLock2", "getClearLock", "setClearLock", "onInternalKeyDown", "event", "clearLock", "which", "ENTER", "preventDefault", "BACKSPACE", "length", "cloneDisplayValues", "removedDisplayValue", "i", "splice", "type", "values", "_len", "arguments", "rest", "Array", "_key", "_listRef$current2", "apply", "concat", "onInternalKeyUp", "_len2", "_key2", "_listRef$current3", "onSelectorRemove", "newValues", "filter", "focusRef", "onContainerFocus", "onContainerBlur", "activeTimeoutIds", "timeoutId", "clearTimeout", "onInternalMouseDown", "_triggerRef$current", "target", "popupElement", "getPopupElement", "contains", "setTimeout", "index", "indexOf", "document", "activeElement", "_selectorRef$current3", "push", "_len3", "restArgs", "_key3", "_React$useState3", "_React$useState4", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$useState5", "_React$useState6", "forceUpdate", "onPopupMouseEnter", "_containerRef$current", "newWidth", "Math", "ceil", "offsetWidth", "Number", "isNaN", "onTriggerVisibleChange", "_triggerRef$current2", "baseSelectContext", "toggle<PERSON><PERSON>", "mergedShowArrow", "arrowNode", "createElement", "customizeIcon", "customizeIconProps", "focused", "clearNode", "onClearMouseDown", "_selectorRef$current4", "optionList", "mergedClassName", "selectorNode", "visible", "empty", "getTriggerDOMNode", "onPopupVisibleChange", "cloneElement", "domRef", "inputElement", "onSearchSubmit", "onRemove", "renderNode", "style", "width", "height", "position", "overflow", "opacity", "map", "_ref", "label", "join", "Provider", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/BaseSelect.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"showSearch\", \"tagRender\", \"direction\", \"omitDomProps\", \"displayValues\", \"onDisplayValuesChange\", \"emptyOptions\", \"notFoundContent\", \"onClear\", \"mode\", \"disabled\", \"loading\", \"getInputElement\", \"getRawInputElement\", \"open\", \"defaultOpen\", \"onDropdownVisibleChange\", \"activeValue\", \"onActiveValueChange\", \"activeDescendantId\", \"searchValue\", \"autoClearSearchValue\", \"onSearch\", \"onSearchSplit\", \"tokenSeparators\", \"allowClear\", \"showArrow\", \"inputIcon\", \"clearIcon\", \"OptionList\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"placement\", \"getPopupContainer\", \"showAction\", \"onFocus\", \"onBlur\", \"onKeyUp\", \"onKeyDown\", \"onMouseDown\"];\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { BaseSelectContext } from \"./hooks/useBaseProps\";\nimport useDelayReset from \"./hooks/useDelayReset\";\nimport useLock from \"./hooks/useLock\";\nimport useSelectTriggerControl from \"./hooks/useSelectTriggerControl\";\nimport Selector from \"./Selector\";\nimport SelectTrigger from \"./SelectTrigger\";\nimport TransBtn from \"./TransBtn\";\nimport { getSeparatedContent } from \"./utils/valueUtil\";\nvar DEFAULT_OMIT_PROPS = ['value', 'onChange', 'removeIcon', 'placeholder', 'autoFocus', 'maxTagCount', 'maxTagTextLength', 'maxTagPlaceholder', 'choiceTransitionName', 'onInputKeyDown', 'onPopupScroll', 'tabIndex'];\nexport function isMultiple(mode) {\n  return mode === 'tags' || mode === 'multiple';\n}\nvar BaseSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _customizeRawInputEle, _classNames2;\n  var id = props.id,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    showSearch = props.showSearch,\n    tagRender = props.tagRender,\n    direction = props.direction,\n    omitDomProps = props.omitDomProps,\n    displayValues = props.displayValues,\n    onDisplayValuesChange = props.onDisplayValuesChange,\n    emptyOptions = props.emptyOptions,\n    _props$notFoundConten = props.notFoundContent,\n    notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n    onClear = props.onClear,\n    mode = props.mode,\n    disabled = props.disabled,\n    loading = props.loading,\n    getInputElement = props.getInputElement,\n    getRawInputElement = props.getRawInputElement,\n    open = props.open,\n    defaultOpen = props.defaultOpen,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    activeValue = props.activeValue,\n    onActiveValueChange = props.onActiveValueChange,\n    activeDescendantId = props.activeDescendantId,\n    searchValue = props.searchValue,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSplit = props.onSearchSplit,\n    tokenSeparators = props.tokenSeparators,\n    allowClear = props.allowClear,\n    showArrow = props.showArrow,\n    inputIcon = props.inputIcon,\n    clearIcon = props.clearIcon,\n    OptionList = props.OptionList,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    placement = props.placement,\n    getPopupContainer = props.getPopupContainer,\n    _props$showAction = props.showAction,\n    showAction = _props$showAction === void 0 ? [] : _props$showAction,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyUp = props.onKeyUp,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // ============================== MISC ==============================\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = (showSearch !== undefined ? showSearch : multiple) || mode === 'combobox';\n  var domProps = _objectSpread({}, restProps);\n  DEFAULT_OMIT_PROPS.forEach(function (propName) {\n    delete domProps[propName];\n  });\n  omitDomProps === null || omitDomProps === void 0 ? void 0 : omitDomProps.forEach(function (propName) {\n    delete domProps[propName];\n  });\n\n  // ============================= Mobile =============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mobile = _React$useState2[0],\n    setMobile = _React$useState2[1];\n  React.useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ============================== Refs ==============================\n  var containerRef = React.useRef(null);\n  var selectorDomRef = React.useRef(null);\n  var triggerRef = React.useRef(null);\n  var selectorRef = React.useRef(null);\n  var listRef = React.useRef(null);\n\n  /** Used for component focused management */\n  var _useDelayReset = useDelayReset(),\n    _useDelayReset2 = _slicedToArray(_useDelayReset, 3),\n    mockFocused = _useDelayReset2[0],\n    setMockFocused = _useDelayReset2[1],\n    cancelSetMockFocused = _useDelayReset2[2];\n\n  // =========================== Imperative ===========================\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current, _selectorRef$current2;\n    return {\n      focus: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.focus,\n      blur: (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 ? void 0 : _selectorRef$current2.blur,\n      scrollTo: function scrollTo(arg) {\n        var _listRef$current;\n        return (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(arg);\n      }\n    };\n  });\n\n  // ========================== Search Value ==========================\n  var mergedSearchValue = React.useMemo(function () {\n    var _displayValues$;\n    if (mode !== 'combobox') {\n      return searchValue;\n    }\n    var val = (_displayValues$ = displayValues[0]) === null || _displayValues$ === void 0 ? void 0 : _displayValues$.value;\n    return typeof val === 'string' || typeof val === 'number' ? String(val) : '';\n  }, [searchValue, mode, displayValues]);\n\n  // ========================== Custom Input ==========================\n  // Only works in `combobox`\n  var customizeInputElement = mode === 'combobox' && typeof getInputElement === 'function' && getInputElement() || null;\n\n  // Used for customize replacement for `rc-cascader`\n  var customizeRawInputElement = typeof getRawInputElement === 'function' && getRawInputElement();\n  var customizeRawInputRef = useComposeRef(selectorDomRef, customizeRawInputElement === null || customizeRawInputElement === void 0 ? void 0 : (_customizeRawInputEle = customizeRawInputElement.props) === null || _customizeRawInputEle === void 0 ? void 0 : _customizeRawInputEle.ref);\n\n  // ============================== Open ==============================\n  var _useMergedState = useMergedState(undefined, {\n      defaultValue: defaultOpen,\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    innerOpen = _useMergedState2[0],\n    setInnerOpen = _useMergedState2[1];\n  var mergedOpen = innerOpen;\n\n  // Not trigger `open` in `combobox` when `notFoundContent` is empty\n  var emptyListContent = !notFoundContent && emptyOptions;\n  if (disabled || emptyListContent && mergedOpen && mode === 'combobox') {\n    mergedOpen = false;\n  }\n  var triggerOpen = emptyListContent ? false : mergedOpen;\n  var onToggleOpen = React.useCallback(function (newOpen) {\n    var nextOpen = newOpen !== undefined ? newOpen : !mergedOpen;\n    if (!disabled) {\n      setInnerOpen(nextOpen);\n      if (mergedOpen !== nextOpen) {\n        onDropdownVisibleChange === null || onDropdownVisibleChange === void 0 ? void 0 : onDropdownVisibleChange(nextOpen);\n      }\n    }\n  }, [disabled, mergedOpen, setInnerOpen, onDropdownVisibleChange]);\n\n  // ============================= Search =============================\n  var tokenWithEnter = React.useMemo(function () {\n    return (tokenSeparators || []).some(function (tokenSeparator) {\n      return ['\\n', '\\r\\n'].includes(tokenSeparator);\n    });\n  }, [tokenSeparators]);\n  var onInternalSearch = function onInternalSearch(searchText, fromTyping, isCompositing) {\n    var ret = true;\n    var newSearchText = searchText;\n    onActiveValueChange === null || onActiveValueChange === void 0 ? void 0 : onActiveValueChange(null);\n\n    // Check if match the `tokenSeparators`\n    var patchLabels = isCompositing ? null : getSeparatedContent(searchText, tokenSeparators);\n\n    // Ignore combobox since it's not split-able\n    if (mode !== 'combobox' && patchLabels) {\n      newSearchText = '';\n      onSearchSplit === null || onSearchSplit === void 0 ? void 0 : onSearchSplit(patchLabels);\n\n      // Should close when paste finish\n      onToggleOpen(false);\n\n      // Tell Selector that break next actions\n      ret = false;\n    }\n    if (onSearch && mergedSearchValue !== newSearchText) {\n      onSearch(newSearchText, {\n        source: fromTyping ? 'typing' : 'effect'\n      });\n    }\n    return ret;\n  };\n\n  // Only triggered when menu is closed & mode is tags\n  // If menu is open, OptionList will take charge\n  // If mode isn't tags, press enter is not meaningful when you can't see any option\n  var onInternalSearchSubmit = function onInternalSearchSubmit(searchText) {\n    // prevent empty tags from appearing when you click the Enter button\n    if (!searchText || !searchText.trim()) {\n      return;\n    }\n    onSearch(searchText, {\n      source: 'submit'\n    });\n  };\n\n  // Close will clean up single mode search text\n  React.useEffect(function () {\n    if (!mergedOpen && !multiple && mode !== 'combobox') {\n      onInternalSearch('', false, false);\n    }\n  }, [mergedOpen]);\n\n  // ============================ Disabled ============================\n  // Close dropdown & remove focus state when disabled change\n  React.useEffect(function () {\n    if (innerOpen && disabled) {\n      setInnerOpen(false);\n    }\n    if (disabled) {\n      setMockFocused(false);\n    }\n  }, [disabled]);\n\n  // ============================ Keyboard ============================\n  /**\n   * We record input value here to check if can press to clean up by backspace\n   * - null: Key is not down, this is reset by key up\n   * - true: Search text is empty when first time backspace down\n   * - false: Search text is not empty when first time backspace down\n   */\n  var _useLock = useLock(),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getClearLock = _useLock2[0],\n    setClearLock = _useLock2[1];\n\n  // KeyDown\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var clearLock = getClearLock();\n    var which = event.which;\n    if (which === KeyCode.ENTER) {\n      // Do not submit form when type in the input\n      if (mode !== 'combobox') {\n        event.preventDefault();\n      }\n\n      // We only manage open state here, close logic should handle by list component\n      if (!mergedOpen) {\n        onToggleOpen(true);\n      }\n    }\n    setClearLock(!!mergedSearchValue);\n\n    // Remove value by `backspace`\n    if (which === KeyCode.BACKSPACE && !clearLock && multiple && !mergedSearchValue && displayValues.length) {\n      var cloneDisplayValues = _toConsumableArray(displayValues);\n      var removedDisplayValue = null;\n      for (var i = cloneDisplayValues.length - 1; i >= 0; i -= 1) {\n        var current = cloneDisplayValues[i];\n        if (!current.disabled) {\n          cloneDisplayValues.splice(i, 1);\n          removedDisplayValue = current;\n          break;\n        }\n      }\n      if (removedDisplayValue) {\n        onDisplayValuesChange(cloneDisplayValues, {\n          type: 'remove',\n          values: [removedDisplayValue]\n        });\n      }\n    }\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current).onKeyDown.apply(_listRef$current2, [event].concat(rest));\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown.apply(void 0, [event].concat(rest));\n  };\n\n  // KeyUp\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      rest[_key2 - 1] = arguments[_key2];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current3;\n      (_listRef$current3 = listRef.current).onKeyUp.apply(_listRef$current3, [event].concat(rest));\n    }\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp.apply(void 0, [event].concat(rest));\n  };\n\n  // ============================ Selector ============================\n  var onSelectorRemove = function onSelectorRemove(val) {\n    var newValues = displayValues.filter(function (i) {\n      return i !== val;\n    });\n    onDisplayValuesChange(newValues, {\n      type: 'remove',\n      values: [val]\n    });\n  };\n\n  // ========================== Focus / Blur ==========================\n  /** Record real focus status */\n  var focusRef = React.useRef(false);\n  var onContainerFocus = function onContainerFocus() {\n    setMockFocused(true);\n    if (!disabled) {\n      if (onFocus && !focusRef.current) {\n        onFocus.apply(void 0, arguments);\n      }\n\n      // `showAction` should handle `focus` if set\n      if (showAction.includes('focus')) {\n        onToggleOpen(true);\n      }\n    }\n    focusRef.current = true;\n  };\n  var onContainerBlur = function onContainerBlur() {\n    setMockFocused(false, function () {\n      focusRef.current = false;\n      onToggleOpen(false);\n    });\n    if (disabled) {\n      return;\n    }\n    if (mergedSearchValue) {\n      // `tags` mode should move `searchValue` into values\n      if (mode === 'tags') {\n        onSearch(mergedSearchValue, {\n          source: 'submit'\n        });\n      } else if (mode === 'multiple') {\n        // `multiple` mode only clean the search value but not trigger event\n        onSearch('', {\n          source: 'blur'\n        });\n      }\n    }\n    if (onBlur) {\n      onBlur.apply(void 0, arguments);\n    }\n  };\n\n  // Give focus back of Select\n  var activeTimeoutIds = [];\n  React.useEffect(function () {\n    return function () {\n      activeTimeoutIds.forEach(function (timeoutId) {\n        return clearTimeout(timeoutId);\n      });\n      activeTimeoutIds.splice(0, activeTimeoutIds.length);\n    };\n  }, []);\n  var onInternalMouseDown = function onInternalMouseDown(event) {\n    var _triggerRef$current;\n    var target = event.target;\n    var popupElement = (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.getPopupElement();\n\n    // We should give focus back to selector if clicked item is not focusable\n    if (popupElement && popupElement.contains(target)) {\n      var timeoutId = setTimeout(function () {\n        var index = activeTimeoutIds.indexOf(timeoutId);\n        if (index !== -1) {\n          activeTimeoutIds.splice(index, 1);\n        }\n        cancelSetMockFocused();\n        if (!mobile && !popupElement.contains(document.activeElement)) {\n          var _selectorRef$current3;\n          (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 ? void 0 : _selectorRef$current3.focus();\n        }\n      });\n      activeTimeoutIds.push(timeoutId);\n    }\n    for (var _len3 = arguments.length, restArgs = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      restArgs[_key3 - 1] = arguments[_key3];\n    }\n    onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown.apply(void 0, [event].concat(restArgs));\n  };\n\n  // ============================ Dropdown ============================\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    containerWidth = _React$useState4[0],\n    setContainerWidth = _React$useState4[1];\n  var _React$useState5 = React.useState({}),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forceUpdate = _React$useState6[1];\n  // We need force update here since popup dom is render async\n  function onPopupMouseEnter() {\n    forceUpdate({});\n  }\n  useLayoutEffect(function () {\n    if (triggerOpen) {\n      var _containerRef$current;\n      var newWidth = Math.ceil((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.offsetWidth);\n      if (containerWidth !== newWidth && !Number.isNaN(newWidth)) {\n        setContainerWidth(newWidth);\n      }\n    }\n  }, [triggerOpen]);\n\n  // Used for raw custom input trigger\n  var onTriggerVisibleChange;\n  if (customizeRawInputElement) {\n    onTriggerVisibleChange = function onTriggerVisibleChange(newOpen) {\n      onToggleOpen(newOpen);\n    };\n  }\n\n  // Close when click on non-select element\n  useSelectTriggerControl(function () {\n    var _triggerRef$current2;\n    return [containerRef.current, (_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : _triggerRef$current2.getPopupElement()];\n  }, triggerOpen, onToggleOpen, !!customizeRawInputElement);\n\n  // ============================ Context =============================\n  var baseSelectContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, props), {}, {\n      notFoundContent: notFoundContent,\n      open: mergedOpen,\n      triggerOpen: triggerOpen,\n      id: id,\n      showSearch: mergedShowSearch,\n      multiple: multiple,\n      toggleOpen: onToggleOpen\n    });\n  }, [props, notFoundContent, triggerOpen, mergedOpen, id, mergedShowSearch, multiple, onToggleOpen]);\n\n  // ==================================================================\n  // ==                            Render                            ==\n  // ==================================================================\n\n  // ============================= Arrow ==============================\n  var mergedShowArrow = showArrow !== undefined ? showArrow : loading || !multiple && mode !== 'combobox';\n  var arrowNode;\n  if (mergedShowArrow) {\n    arrowNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: classNames(\"\".concat(prefixCls, \"-arrow\"), _defineProperty({}, \"\".concat(prefixCls, \"-arrow-loading\"), loading)),\n      customizeIcon: inputIcon,\n      customizeIconProps: {\n        loading: loading,\n        searchValue: mergedSearchValue,\n        open: mergedOpen,\n        focused: mockFocused,\n        showSearch: mergedShowSearch\n      }\n    });\n  }\n\n  // ============================= Clear ==============================\n  var clearNode;\n  var onClearMouseDown = function onClearMouseDown() {\n    var _selectorRef$current4;\n    onClear === null || onClear === void 0 ? void 0 : onClear();\n    (_selectorRef$current4 = selectorRef.current) === null || _selectorRef$current4 === void 0 ? void 0 : _selectorRef$current4.focus();\n    onDisplayValuesChange([], {\n      type: 'clear',\n      values: displayValues\n    });\n    onInternalSearch('', false, false);\n  };\n  if (!disabled && allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n    clearNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: clearIcon\n    }, \"\\xD7\");\n  }\n\n  // =========================== OptionList ===========================\n  var optionList = /*#__PURE__*/React.createElement(OptionList, {\n    ref: listRef\n  });\n\n  // ============================= Select =============================\n  var mergedClassName = classNames(prefixCls, className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mockFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-multiple\"), multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-single\"), !multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-allow-clear\"), allowClear), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-arrow\"), mergedShowArrow), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-open\"), mergedOpen), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-customize-input\"), customizeInputElement), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-search\"), mergedShowSearch), _classNames2));\n\n  // >>> Selector\n  var selectorNode = /*#__PURE__*/React.createElement(SelectTrigger, {\n    ref: triggerRef,\n    disabled: disabled,\n    prefixCls: prefixCls,\n    visible: triggerOpen,\n    popupElement: optionList,\n    containerWidth: containerWidth,\n    animation: animation,\n    transitionName: transitionName,\n    dropdownStyle: dropdownStyle,\n    dropdownClassName: dropdownClassName,\n    direction: direction,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n    dropdownRender: dropdownRender,\n    dropdownAlign: dropdownAlign,\n    placement: placement,\n    getPopupContainer: getPopupContainer,\n    empty: emptyOptions,\n    getTriggerDOMNode: function getTriggerDOMNode() {\n      return selectorDomRef.current;\n    },\n    onPopupVisibleChange: onTriggerVisibleChange,\n    onPopupMouseEnter: onPopupMouseEnter\n  }, customizeRawInputElement ? /*#__PURE__*/React.cloneElement(customizeRawInputElement, {\n    ref: customizeRawInputRef\n  }) : /*#__PURE__*/React.createElement(Selector, _extends({}, props, {\n    domRef: selectorDomRef,\n    prefixCls: prefixCls,\n    inputElement: customizeInputElement,\n    ref: selectorRef,\n    id: id,\n    showSearch: mergedShowSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    mode: mode,\n    activeDescendantId: activeDescendantId,\n    tagRender: tagRender,\n    values: displayValues,\n    open: mergedOpen,\n    onToggleOpen: onToggleOpen,\n    activeValue: activeValue,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSubmit: onInternalSearchSubmit,\n    onRemove: onSelectorRemove,\n    tokenWithEnter: tokenWithEnter\n  })));\n\n  // >>> Render\n  var renderNode;\n\n  // Render raw\n  if (customizeRawInputElement) {\n    renderNode = selectorNode;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: mergedClassName\n    }, domProps, {\n      ref: containerRef,\n      onMouseDown: onInternalMouseDown,\n      onKeyDown: onInternalKeyDown,\n      onKeyUp: onInternalKeyUp,\n      onFocus: onContainerFocus,\n      onBlur: onContainerBlur\n    }), mockFocused && !mergedOpen && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        width: 0,\n        height: 0,\n        position: 'absolute',\n        overflow: 'hidden',\n        opacity: 0\n      },\n      \"aria-live\": \"polite\"\n    }, \"\".concat(displayValues.map(function (_ref) {\n      var label = _ref.label,\n        value = _ref.value;\n      return ['number', 'string'].includes(_typeof(label)) ? label : value;\n    }).join(', '))), selectorNode, arrowNode, clearNode);\n  }\n  return /*#__PURE__*/React.createElement(BaseSelectContext.Provider, {\n    value: baseSelectContext\n  }, renderNode);\n});\n\n// Set display name for dev\nif (process.env.NODE_ENV !== 'production') {\n  BaseSelect.displayName = 'BaseSelect';\n}\nexport default BaseSelect;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,yBAAyB,EAAE,aAAa,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,aAAa,EAAE,sBAAsB,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC;AAChwB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,IAAIC,kBAAkB,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,eAAe,EAAE,UAAU,CAAC;AACvN,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU;AAC/C;AACA,IAAIC,UAAU,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,qBAAqB,EAAEC,YAAY;EACvC,IAAIC,EAAE,GAAGJ,KAAK,CAACI,EAAE;IACfC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,YAAY,GAAGV,KAAK,CAACU,YAAY;IACjCC,aAAa,GAAGX,KAAK,CAACW,aAAa;IACnCC,qBAAqB,GAAGZ,KAAK,CAACY,qBAAqB;IACnDC,YAAY,GAAGb,KAAK,CAACa,YAAY;IACjCC,qBAAqB,GAAGd,KAAK,CAACe,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,qBAAqB;IACxFE,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBnB,IAAI,GAAGG,KAAK,CAACH,IAAI;IACjBoB,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,eAAe,GAAGnB,KAAK,CAACmB,eAAe;IACvCC,kBAAkB,GAAGpB,KAAK,CAACoB,kBAAkB;IAC7CC,IAAI,GAAGrB,KAAK,CAACqB,IAAI;IACjBC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,uBAAuB,GAAGvB,KAAK,CAACuB,uBAAuB;IACvDC,WAAW,GAAGxB,KAAK,CAACwB,WAAW;IAC/BC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,kBAAkB,GAAG1B,KAAK,CAAC0B,kBAAkB;IAC7CC,WAAW,GAAG3B,KAAK,CAAC2B,WAAW;IAC/BC,oBAAoB,GAAG5B,KAAK,CAAC4B,oBAAoB;IACjDC,QAAQ,GAAG7B,KAAK,CAAC6B,QAAQ;IACzBC,aAAa,GAAG9B,KAAK,CAAC8B,aAAa;IACnCC,eAAe,GAAG/B,KAAK,CAAC+B,eAAe;IACvCC,UAAU,GAAGhC,KAAK,CAACgC,UAAU;IAC7BC,SAAS,GAAGjC,KAAK,CAACiC,SAAS;IAC3BC,SAAS,GAAGlC,KAAK,CAACkC,SAAS;IAC3BC,SAAS,GAAGnC,KAAK,CAACmC,SAAS;IAC3BC,UAAU,GAAGpC,KAAK,CAACoC,UAAU;IAC7BC,SAAS,GAAGrC,KAAK,CAACqC,SAAS;IAC3BC,cAAc,GAAGtC,KAAK,CAACsC,cAAc;IACrCC,aAAa,GAAGvC,KAAK,CAACuC,aAAa;IACnCC,iBAAiB,GAAGxC,KAAK,CAACwC,iBAAiB;IAC3CC,wBAAwB,GAAGzC,KAAK,CAACyC,wBAAwB;IACzDC,cAAc,GAAG1C,KAAK,CAAC0C,cAAc;IACrCC,aAAa,GAAG3C,KAAK,CAAC2C,aAAa;IACnCC,SAAS,GAAG5C,KAAK,CAAC4C,SAAS;IAC3BC,iBAAiB,GAAG7C,KAAK,CAAC6C,iBAAiB;IAC3CC,iBAAiB,GAAG9C,KAAK,CAAC+C,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,iBAAiB;IAClEE,OAAO,GAAGhD,KAAK,CAACgD,OAAO;IACvBC,MAAM,GAAGjD,KAAK,CAACiD,MAAM;IACrBC,OAAO,GAAGlD,KAAK,CAACkD,OAAO;IACvBC,SAAS,GAAGnD,KAAK,CAACmD,SAAS;IAC3BC,WAAW,GAAGpD,KAAK,CAACoD,WAAW;IAC/BC,SAAS,GAAG3E,wBAAwB,CAACsB,KAAK,EAAErB,SAAS,CAAC;;EAExD;EACA,IAAI2E,QAAQ,GAAG1D,UAAU,CAACC,IAAI,CAAC;EAC/B,IAAI0D,gBAAgB,GAAG,CAAChD,UAAU,KAAKiD,SAAS,GAAGjD,UAAU,GAAG+C,QAAQ,KAAKzD,IAAI,KAAK,UAAU;EAChG,IAAI4D,QAAQ,GAAGhF,aAAa,CAAC,CAAC,CAAC,EAAE4E,SAAS,CAAC;EAC3C1D,kBAAkB,CAAC+D,OAAO,CAAC,UAAUC,QAAQ,EAAE;IAC7C,OAAOF,QAAQ,CAACE,QAAQ,CAAC;EAC3B,CAAC,CAAC;EACFjD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACgD,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACnG,OAAOF,QAAQ,CAACE,QAAQ,CAAC;EAC3B,CAAC,CAAC;;EAEF;EACA,IAAIC,eAAe,GAAG1E,KAAK,CAAC2E,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGtF,cAAc,CAACoF,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC5E,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1B;IACAD,SAAS,CAACjF,QAAQ,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAImF,YAAY,GAAGhF,KAAK,CAACiF,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIC,cAAc,GAAGlF,KAAK,CAACiF,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIE,UAAU,GAAGnF,KAAK,CAACiF,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIG,WAAW,GAAGpF,KAAK,CAACiF,MAAM,CAAC,IAAI,CAAC;EACpC,IAAII,OAAO,GAAGrF,KAAK,CAACiF,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,IAAIK,cAAc,GAAGpF,aAAa,CAAC,CAAC;IAClCqF,eAAe,GAAGjG,cAAc,CAACgG,cAAc,EAAE,CAAC,CAAC;IACnDE,WAAW,GAAGD,eAAe,CAAC,CAAC,CAAC;IAChCE,cAAc,GAAGF,eAAe,CAAC,CAAC,CAAC;IACnCG,oBAAoB,GAAGH,eAAe,CAAC,CAAC,CAAC;;EAE3C;EACAvF,KAAK,CAAC2F,mBAAmB,CAAC5E,GAAG,EAAE,YAAY;IACzC,IAAI6E,oBAAoB,EAAEC,qBAAqB;IAC/C,OAAO;MACLC,KAAK,EAAE,CAACF,oBAAoB,GAAGR,WAAW,CAACW,OAAO,MAAM,IAAI,IAAIH,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACE,KAAK;MACrIE,IAAI,EAAE,CAACH,qBAAqB,GAAGT,WAAW,CAACW,OAAO,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,IAAI;MACtIC,QAAQ,EAAE,SAASA,QAAQA,CAACC,GAAG,EAAE;QAC/B,IAAIC,gBAAgB;QACpB,OAAO,CAACA,gBAAgB,GAAGd,OAAO,CAACU,OAAO,MAAM,IAAI,IAAII,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACF,QAAQ,CAACC,GAAG,CAAC;MAC/H;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIE,iBAAiB,GAAGpG,KAAK,CAACqG,OAAO,CAAC,YAAY;IAChD,IAAIC,eAAe;IACnB,IAAI3F,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO8B,WAAW;IACpB;IACA,IAAI8D,GAAG,GAAG,CAACD,eAAe,GAAG7E,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI6E,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,KAAK;IACtH,OAAO,OAAOD,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAGE,MAAM,CAACF,GAAG,CAAC,GAAG,EAAE;EAC9E,CAAC,EAAE,CAAC9D,WAAW,EAAE9B,IAAI,EAAEc,aAAa,CAAC,CAAC;;EAEtC;EACA;EACA,IAAIiF,qBAAqB,GAAG/F,IAAI,KAAK,UAAU,IAAI,OAAOsB,eAAe,KAAK,UAAU,IAAIA,eAAe,CAAC,CAAC,IAAI,IAAI;;EAErH;EACA,IAAI0E,wBAAwB,GAAG,OAAOzE,kBAAkB,KAAK,UAAU,IAAIA,kBAAkB,CAAC,CAAC;EAC/F,IAAI0E,oBAAoB,GAAG7G,aAAa,CAACmF,cAAc,EAAEyB,wBAAwB,KAAK,IAAI,IAAIA,wBAAwB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC3F,qBAAqB,GAAG2F,wBAAwB,CAAC7F,KAAK,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,GAAG,CAAC;;EAExR;EACA,IAAI8F,eAAe,GAAGjH,cAAc,CAAC0E,SAAS,EAAE;MAC5CwC,YAAY,EAAE1E,WAAW;MACzBoE,KAAK,EAAErE;IACT,CAAC,CAAC;IACF4E,gBAAgB,GAAGzH,cAAc,CAACuH,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,UAAU,GAAGF,SAAS;;EAE1B;EACA,IAAIG,gBAAgB,GAAG,CAACtF,eAAe,IAAIF,YAAY;EACvD,IAAII,QAAQ,IAAIoF,gBAAgB,IAAID,UAAU,IAAIvG,IAAI,KAAK,UAAU,EAAE;IACrEuG,UAAU,GAAG,KAAK;EACpB;EACA,IAAIE,WAAW,GAAGD,gBAAgB,GAAG,KAAK,GAAGD,UAAU;EACvD,IAAIG,YAAY,GAAGrH,KAAK,CAACsH,WAAW,CAAC,UAAUC,OAAO,EAAE;IACtD,IAAIC,QAAQ,GAAGD,OAAO,KAAKjD,SAAS,GAAGiD,OAAO,GAAG,CAACL,UAAU;IAC5D,IAAI,CAACnF,QAAQ,EAAE;MACbkF,YAAY,CAACO,QAAQ,CAAC;MACtB,IAAIN,UAAU,KAAKM,QAAQ,EAAE;QAC3BnF,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACmF,QAAQ,CAAC;MACrH;IACF;EACF,CAAC,EAAE,CAACzF,QAAQ,EAAEmF,UAAU,EAAED,YAAY,EAAE5E,uBAAuB,CAAC,CAAC;;EAEjE;EACA,IAAIoF,cAAc,GAAGzH,KAAK,CAACqG,OAAO,CAAC,YAAY;IAC7C,OAAO,CAACxD,eAAe,IAAI,EAAE,EAAE6E,IAAI,CAAC,UAAUC,cAAc,EAAE;MAC5D,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,cAAc,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9E,eAAe,CAAC,CAAC;EACrB,IAAIgF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAE;IACtF,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,aAAa,GAAGJ,UAAU;IAC9BvF,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC,IAAI,CAAC;;IAEnG;IACA,IAAI4F,WAAW,GAAGH,aAAa,GAAG,IAAI,GAAGxH,mBAAmB,CAACsH,UAAU,EAAEjF,eAAe,CAAC;;IAEzF;IACA,IAAIlC,IAAI,KAAK,UAAU,IAAIwH,WAAW,EAAE;MACtCD,aAAa,GAAG,EAAE;MAClBtF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACuF,WAAW,CAAC;;MAExF;MACAd,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACAY,GAAG,GAAG,KAAK;IACb;IACA,IAAItF,QAAQ,IAAIyD,iBAAiB,KAAK8B,aAAa,EAAE;MACnDvF,QAAQ,CAACuF,aAAa,EAAE;QACtBE,MAAM,EAAEL,UAAU,GAAG,QAAQ,GAAG;MAClC,CAAC,CAAC;IACJ;IACA,OAAOE,GAAG;EACZ,CAAC;;EAED;EACA;EACA;EACA,IAAII,sBAAsB,GAAG,SAASA,sBAAsBA,CAACP,UAAU,EAAE;IACvE;IACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE;MACrC;IACF;IACA3F,QAAQ,CAACmF,UAAU,EAAE;MACnBM,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACApI,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACmC,UAAU,IAAI,CAAC9C,QAAQ,IAAIzD,IAAI,KAAK,UAAU,EAAE;MACnDkH,gBAAgB,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC;;EAEhB;EACA;EACAlH,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1B,IAAIiC,SAAS,IAAIjF,QAAQ,EAAE;MACzBkF,YAAY,CAAC,KAAK,CAAC;IACrB;IACA,IAAIlF,QAAQ,EAAE;MACZ0D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAAC1D,QAAQ,CAAC,CAAC;;EAEd;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIwG,QAAQ,GAAGpI,OAAO,CAAC,CAAC;IACtBqI,SAAS,GAAGlJ,cAAc,CAACiJ,QAAQ,EAAE,CAAC,CAAC;IACvCE,YAAY,GAAGD,SAAS,CAAC,CAAC,CAAC;IAC3BE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;;EAE7B;EACA,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAIC,SAAS,GAAGJ,YAAY,CAAC,CAAC;IAC9B,IAAIK,KAAK,GAAGF,KAAK,CAACE,KAAK;IACvB,IAAIA,KAAK,KAAKhJ,OAAO,CAACiJ,KAAK,EAAE;MAC3B;MACA,IAAIpI,IAAI,KAAK,UAAU,EAAE;QACvBiI,KAAK,CAACI,cAAc,CAAC,CAAC;MACxB;;MAEA;MACA,IAAI,CAAC9B,UAAU,EAAE;QACfG,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;IACAqB,YAAY,CAAC,CAAC,CAACtC,iBAAiB,CAAC;;IAEjC;IACA,IAAI0C,KAAK,KAAKhJ,OAAO,CAACmJ,SAAS,IAAI,CAACJ,SAAS,IAAIzE,QAAQ,IAAI,CAACgC,iBAAiB,IAAI3E,aAAa,CAACyH,MAAM,EAAE;MACvG,IAAIC,kBAAkB,GAAG9J,kBAAkB,CAACoC,aAAa,CAAC;MAC1D,IAAI2H,mBAAmB,GAAG,IAAI;MAC9B,KAAK,IAAIC,CAAC,GAAGF,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAItD,OAAO,GAAGoD,kBAAkB,CAACE,CAAC,CAAC;QACnC,IAAI,CAACtD,OAAO,CAAChE,QAAQ,EAAE;UACrBoH,kBAAkB,CAACG,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAC/BD,mBAAmB,GAAGrD,OAAO;UAC7B;QACF;MACF;MACA,IAAIqD,mBAAmB,EAAE;QACvB1H,qBAAqB,CAACyH,kBAAkB,EAAE;UACxCI,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,CAACJ,mBAAmB;QAC9B,CAAC,CAAC;MACJ;IACF;IACA,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACR,MAAM,EAAES,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAClC;IACA,IAAI3C,UAAU,IAAI7B,OAAO,CAACU,OAAO,EAAE;MACjC,IAAI+D,iBAAiB;MACrB,CAACA,iBAAiB,GAAGzE,OAAO,CAACU,OAAO,EAAE9B,SAAS,CAAC8F,KAAK,CAACD,iBAAiB,EAAE,CAAClB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;IAChG;IACA1F,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC8F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;EACrG,CAAC;;EAED;EACA,IAAIM,eAAe,GAAG,SAASA,eAAeA,CAACrB,KAAK,EAAE;IACpD,KAAK,IAAIsB,KAAK,GAAGR,SAAS,CAACR,MAAM,EAAES,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MACjHR,IAAI,CAACQ,KAAK,GAAG,CAAC,CAAC,GAAGT,SAAS,CAACS,KAAK,CAAC;IACpC;IACA,IAAIjD,UAAU,IAAI7B,OAAO,CAACU,OAAO,EAAE;MACjC,IAAIqE,iBAAiB;MACrB,CAACA,iBAAiB,GAAG/E,OAAO,CAACU,OAAO,EAAE/B,OAAO,CAAC+F,KAAK,CAACK,iBAAiB,EAAE,CAACxB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;IAC9F;IACA3F,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC+F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;EAC/F,CAAC;;EAED;EACA,IAAIU,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC9D,GAAG,EAAE;IACpD,IAAI+D,SAAS,GAAG7I,aAAa,CAAC8I,MAAM,CAAC,UAAUlB,CAAC,EAAE;MAChD,OAAOA,CAAC,KAAK9C,GAAG;IAClB,CAAC,CAAC;IACF7E,qBAAqB,CAAC4I,SAAS,EAAE;MAC/Bf,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,CAACjD,GAAG;IACd,CAAC,CAAC;EACJ,CAAC;;EAED;EACA;EACA,IAAIiE,QAAQ,GAAGxK,KAAK,CAACiF,MAAM,CAAC,KAAK,CAAC;EAClC,IAAIwF,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDhF,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI,CAAC1D,QAAQ,EAAE;MACb,IAAI+B,OAAO,IAAI,CAAC0G,QAAQ,CAACzE,OAAO,EAAE;QAChCjC,OAAO,CAACiG,KAAK,CAAC,KAAK,CAAC,EAAEL,SAAS,CAAC;MAClC;;MAEA;MACA,IAAI7F,UAAU,CAAC+D,QAAQ,CAAC,OAAO,CAAC,EAAE;QAChCP,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;IACAmD,QAAQ,CAACzE,OAAO,GAAG,IAAI;EACzB,CAAC;EACD,IAAI2E,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CjF,cAAc,CAAC,KAAK,EAAE,YAAY;MAChC+E,QAAQ,CAACzE,OAAO,GAAG,KAAK;MACxBsB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;IACF,IAAItF,QAAQ,EAAE;MACZ;IACF;IACA,IAAIqE,iBAAiB,EAAE;MACrB;MACA,IAAIzF,IAAI,KAAK,MAAM,EAAE;QACnBgC,QAAQ,CAACyD,iBAAiB,EAAE;UAC1BgC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIzH,IAAI,KAAK,UAAU,EAAE;QAC9B;QACAgC,QAAQ,CAAC,EAAE,EAAE;UACXyF,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;IACA,IAAIrE,MAAM,EAAE;MACVA,MAAM,CAACgG,KAAK,CAAC,KAAK,CAAC,EAAEL,SAAS,CAAC;IACjC;EACF,CAAC;;EAED;EACA,IAAIiB,gBAAgB,GAAG,EAAE;EACzB3K,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB4F,gBAAgB,CAACnG,OAAO,CAAC,UAAUoG,SAAS,EAAE;QAC5C,OAAOC,YAAY,CAACD,SAAS,CAAC;MAChC,CAAC,CAAC;MACFD,gBAAgB,CAACrB,MAAM,CAAC,CAAC,EAAEqB,gBAAgB,CAACzB,MAAM,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAI4B,mBAAmB,GAAG,SAASA,mBAAmBA,CAAClC,KAAK,EAAE;IAC5D,IAAImC,mBAAmB;IACvB,IAAIC,MAAM,GAAGpC,KAAK,CAACoC,MAAM;IACzB,IAAIC,YAAY,GAAG,CAACF,mBAAmB,GAAG5F,UAAU,CAACY,OAAO,MAAM,IAAI,IAAIgF,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACG,eAAe,CAAC,CAAC;;IAEzJ;IACA,IAAID,YAAY,IAAIA,YAAY,CAACE,QAAQ,CAACH,MAAM,CAAC,EAAE;MACjD,IAAIJ,SAAS,GAAGQ,UAAU,CAAC,YAAY;QACrC,IAAIC,KAAK,GAAGV,gBAAgB,CAACW,OAAO,CAACV,SAAS,CAAC;QAC/C,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBV,gBAAgB,CAACrB,MAAM,CAAC+B,KAAK,EAAE,CAAC,CAAC;QACnC;QACA3F,oBAAoB,CAAC,CAAC;QACtB,IAAI,CAACb,MAAM,IAAI,CAACoG,YAAY,CAACE,QAAQ,CAACI,QAAQ,CAACC,aAAa,CAAC,EAAE;UAC7D,IAAIC,qBAAqB;UACzB,CAACA,qBAAqB,GAAGrG,WAAW,CAACW,OAAO,MAAM,IAAI,IAAI0F,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC3F,KAAK,CAAC,CAAC;QACrI;MACF,CAAC,CAAC;MACF6E,gBAAgB,CAACe,IAAI,CAACd,SAAS,CAAC;IAClC;IACA,KAAK,IAAIe,KAAK,GAAGjC,SAAS,CAACR,MAAM,EAAE0C,QAAQ,GAAG,IAAIhC,KAAK,CAAC+B,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;MACrHD,QAAQ,CAACC,KAAK,GAAG,CAAC,CAAC,GAAGnC,SAAS,CAACmC,KAAK,CAAC;IACxC;IACA3H,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAAC4B,QAAQ,CAAC,CAAC;EAC/G,CAAC;;EAED;EACA,IAAIE,gBAAgB,GAAG9L,KAAK,CAAC2E,QAAQ,CAAC,IAAI,CAAC;IACzCoH,gBAAgB,GAAGzM,cAAc,CAACwM,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,gBAAgB,GAAGlM,KAAK,CAAC2E,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCwH,gBAAgB,GAAG7M,cAAc,CAAC4M,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC;EACA,SAASE,iBAAiBA,CAAA,EAAG;IAC3BD,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB;EACAzM,eAAe,CAAC,YAAY;IAC1B,IAAIyH,WAAW,EAAE;MACf,IAAIkF,qBAAqB;MACzB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACH,qBAAqB,GAAGtH,YAAY,CAACe,OAAO,MAAM,IAAI,IAAIuG,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,WAAW,CAAC;MAClK,IAAIV,cAAc,KAAKO,QAAQ,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,QAAQ,CAAC,EAAE;QAC1DN,iBAAiB,CAACM,QAAQ,CAAC;MAC7B;IACF;EACF,CAAC,EAAE,CAACnF,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIyF,sBAAsB;EAC1B,IAAIlG,wBAAwB,EAAE;IAC5BkG,sBAAsB,GAAG,SAASA,sBAAsBA,CAACtF,OAAO,EAAE;MAChEF,YAAY,CAACE,OAAO,CAAC;IACvB,CAAC;EACH;;EAEA;EACAnH,uBAAuB,CAAC,YAAY;IAClC,IAAI0M,oBAAoB;IACxB,OAAO,CAAC9H,YAAY,CAACe,OAAO,EAAE,CAAC+G,oBAAoB,GAAG3H,UAAU,CAACY,OAAO,MAAM,IAAI,IAAI+G,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC5B,eAAe,CAAC,CAAC,CAAC;EAC1K,CAAC,EAAE9D,WAAW,EAAEC,YAAY,EAAE,CAAC,CAACV,wBAAwB,CAAC;;EAEzD;EACA,IAAIoG,iBAAiB,GAAG/M,KAAK,CAACqG,OAAO,CAAC,YAAY;IAChD,OAAO9G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDe,eAAe,EAAEA,eAAe;MAChCM,IAAI,EAAE+E,UAAU;MAChBE,WAAW,EAAEA,WAAW;MACxBlG,EAAE,EAAEA,EAAE;MACNG,UAAU,EAAEgD,gBAAgB;MAC5BD,QAAQ,EAAEA,QAAQ;MAClB4I,UAAU,EAAE3F;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvG,KAAK,EAAEe,eAAe,EAAEuF,WAAW,EAAEF,UAAU,EAAEhG,EAAE,EAAEmD,gBAAgB,EAAED,QAAQ,EAAEiD,YAAY,CAAC,CAAC;;EAEnG;EACA;EACA;;EAEA;EACA,IAAI4F,eAAe,GAAGlK,SAAS,KAAKuB,SAAS,GAAGvB,SAAS,GAAGf,OAAO,IAAI,CAACoC,QAAQ,IAAIzD,IAAI,KAAK,UAAU;EACvG,IAAIuM,SAAS;EACb,IAAID,eAAe,EAAE;IACnBC,SAAS,GAAG,aAAalN,KAAK,CAACmN,aAAa,CAAC5M,QAAQ,EAAE;MACrDa,SAAS,EAAE1B,UAAU,CAAC,EAAE,CAACsK,MAAM,CAAC7I,SAAS,EAAE,QAAQ,CAAC,EAAE/B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4K,MAAM,CAAC7I,SAAS,EAAE,gBAAgB,CAAC,EAAEa,OAAO,CAAC,CAAC;MAC3HoL,aAAa,EAAEpK,SAAS;MACxBqK,kBAAkB,EAAE;QAClBrL,OAAO,EAAEA,OAAO;QAChBS,WAAW,EAAE2D,iBAAiB;QAC9BjE,IAAI,EAAE+E,UAAU;QAChBoG,OAAO,EAAE9H,WAAW;QACpBnE,UAAU,EAAEgD;MACd;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIkJ,SAAS;EACb,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,qBAAqB;IACzB3L,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC3D,CAAC2L,qBAAqB,GAAGrI,WAAW,CAACW,OAAO,MAAM,IAAI,IAAI0H,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC3H,KAAK,CAAC,CAAC;IACnIpE,qBAAqB,CAAC,EAAE,EAAE;MACxB6H,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE/H;IACV,CAAC,CAAC;IACFoG,gBAAgB,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;EACpC,CAAC;EACD,IAAI,CAAC9F,QAAQ,IAAIe,UAAU,KAAKrB,aAAa,CAACyH,MAAM,IAAI9C,iBAAiB,CAAC,IAAI,EAAEzF,IAAI,KAAK,UAAU,IAAIyF,iBAAiB,KAAK,EAAE,CAAC,EAAE;IAChImH,SAAS,GAAG,aAAavN,KAAK,CAACmN,aAAa,CAAC5M,QAAQ,EAAE;MACrDa,SAAS,EAAE,EAAE,CAAC4I,MAAM,CAAC7I,SAAS,EAAE,QAAQ,CAAC;MACzC+C,WAAW,EAAEsJ,gBAAgB;MAC7BJ,aAAa,EAAEnK;IACjB,CAAC,EAAE,MAAM,CAAC;EACZ;;EAEA;EACA,IAAIyK,UAAU,GAAG,aAAa1N,KAAK,CAACmN,aAAa,CAACjK,UAAU,EAAE;IAC5DnC,GAAG,EAAEsE;EACP,CAAC,CAAC;;EAEF;EACA,IAAIsI,eAAe,GAAGjO,UAAU,CAACyB,SAAS,EAAEC,SAAS,GAAGH,YAAY,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,UAAU,CAAC,EAAEqE,WAAW,CAAC,EAAEpG,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,WAAW,CAAC,EAAEiD,QAAQ,CAAC,EAAEhF,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,SAAS,CAAC,EAAE,CAACiD,QAAQ,CAAC,EAAEhF,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,cAAc,CAAC,EAAE2B,UAAU,CAAC,EAAE1D,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,aAAa,CAAC,EAAE8L,eAAe,CAAC,EAAE7N,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,WAAW,CAAC,EAAEY,QAAQ,CAAC,EAAE3C,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,UAAU,CAAC,EAAEa,OAAO,CAAC,EAAE5C,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,OAAO,CAAC,EAAE+F,UAAU,CAAC,EAAE9H,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,kBAAkB,CAAC,EAAEuF,qBAAqB,CAAC,EAAEtH,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC+I,MAAM,CAAC7I,SAAS,EAAE,cAAc,CAAC,EAAEkD,gBAAgB,CAAC,EAAEpD,YAAY,CAAC,CAAC;;EAE33B;EACA,IAAI2M,YAAY,GAAG,aAAa5N,KAAK,CAACmN,aAAa,CAAC7M,aAAa,EAAE;IACjES,GAAG,EAAEoE,UAAU;IACfpD,QAAQ,EAAEA,QAAQ;IAClBZ,SAAS,EAAEA,SAAS;IACpB0M,OAAO,EAAEzG,WAAW;IACpB6D,YAAY,EAAEyC,UAAU;IACxB1B,cAAc,EAAEA,cAAc;IAC9B7I,SAAS,EAAEA,SAAS;IACpBC,cAAc,EAAEA,cAAc;IAC9BC,aAAa,EAAEA,aAAa;IAC5BC,iBAAiB,EAAEA,iBAAiB;IACpC/B,SAAS,EAAEA,SAAS;IACpBgC,wBAAwB,EAAEA,wBAAwB;IAClDC,cAAc,EAAEA,cAAc;IAC9BC,aAAa,EAAEA,aAAa;IAC5BC,SAAS,EAAEA,SAAS;IACpBC,iBAAiB,EAAEA,iBAAiB;IACpCmK,KAAK,EAAEnM,YAAY;IACnBoM,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG;MAC9C,OAAO7I,cAAc,CAACa,OAAO;IAC/B,CAAC;IACDiI,oBAAoB,EAAEnB,sBAAsB;IAC5CR,iBAAiB,EAAEA;EACrB,CAAC,EAAE1F,wBAAwB,GAAG,aAAa3G,KAAK,CAACiO,YAAY,CAACtH,wBAAwB,EAAE;IACtF5F,GAAG,EAAE6F;EACP,CAAC,CAAC,GAAG,aAAa5G,KAAK,CAACmN,aAAa,CAAC9M,QAAQ,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IAClEoN,MAAM,EAAEhJ,cAAc;IACtB/D,SAAS,EAAEA,SAAS;IACpBgN,YAAY,EAAEzH,qBAAqB;IACnC3F,GAAG,EAAEqE,WAAW;IAChBlE,EAAE,EAAEA,EAAE;IACNG,UAAU,EAAEgD,gBAAgB;IAC5B3B,oBAAoB,EAAEA,oBAAoB;IAC1C/B,IAAI,EAAEA,IAAI;IACV6B,kBAAkB,EAAEA,kBAAkB;IACtClB,SAAS,EAAEA,SAAS;IACpBkI,MAAM,EAAE/H,aAAa;IACrBU,IAAI,EAAE+E,UAAU;IAChBG,YAAY,EAAEA,YAAY;IAC1B/E,WAAW,EAAEA,WAAW;IACxBG,WAAW,EAAE2D,iBAAiB;IAC9BzD,QAAQ,EAAEkF,gBAAgB;IAC1BuG,cAAc,EAAE/F,sBAAsB;IACtCgG,QAAQ,EAAEhE,gBAAgB;IAC1B5C,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC;;EAEJ;EACA,IAAI6G,UAAU;;EAEd;EACA,IAAI3H,wBAAwB,EAAE;IAC5B2H,UAAU,GAAGV,YAAY;EAC3B,CAAC,MAAM;IACLU,UAAU,GAAG,aAAatO,KAAK,CAACmN,aAAa,CAAC,KAAK,EAAEhO,QAAQ,CAAC;MAC5DiC,SAAS,EAAEuM;IACb,CAAC,EAAEpJ,QAAQ,EAAE;MACXxD,GAAG,EAAEiE,YAAY;MACjBd,WAAW,EAAE4G,mBAAmB;MAChC7G,SAAS,EAAE0E,iBAAiB;MAC5B3E,OAAO,EAAEiG,eAAe;MACxBnG,OAAO,EAAE2G,gBAAgB;MACzB1G,MAAM,EAAE2G;IACV,CAAC,CAAC,EAAElF,WAAW,IAAI,CAAC0B,UAAU,IAAI,aAAalH,KAAK,CAACmN,aAAa,CAAC,MAAM,EAAE;MACzEoB,KAAK,EAAE;QACLC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE;MACX,CAAC;MACD,WAAW,EAAE;IACf,CAAC,EAAE,EAAE,CAAC5E,MAAM,CAACvI,aAAa,CAACoN,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC7C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QACpBvI,KAAK,GAAGsI,IAAI,CAACtI,KAAK;MACpB,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACoB,QAAQ,CAAC1I,OAAO,CAAC6P,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAGvI,KAAK;IACtE,CAAC,CAAC,CAACwI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEpB,YAAY,EAAEV,SAAS,EAAEK,SAAS,CAAC;EACtD;EACA,OAAO,aAAavN,KAAK,CAACmN,aAAa,CAAClN,iBAAiB,CAACgP,QAAQ,EAAE;IAClEzI,KAAK,EAAEuG;EACT,CAAC,EAAEuB,UAAU,CAAC;AAChB,CAAC,CAAC;;AAEF;AACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCxO,UAAU,CAACyO,WAAW,GAAG,YAAY;AACvC;AACA,eAAezO,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}