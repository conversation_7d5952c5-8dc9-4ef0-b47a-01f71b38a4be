{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.milliseconds = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar millisecond = (0, _interval.default)(function () {// noop\n}, function (date, step) {\n  date.setTime(+date + step);\n}, function (start, end) {\n  return end - start;\n}); // An optimized implementation for this simple case.\n\nmillisecond.every = function (k) {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return (0, _interval.default)(function (date) {\n    date.setTime(Math.floor(date / k) * k);\n  }, function (date, step) {\n    date.setTime(+date + step * k);\n  }, function (start, end) {\n    return (end - start) / k;\n  });\n};\nvar _default = millisecond;\nexports.default = _default;\nvar milliseconds = millisecond.range;\nexports.milliseconds = milliseconds;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "milliseconds", "default", "_interval", "_interopRequireDefault", "require", "obj", "__esModule", "millisecond", "date", "step", "setTime", "start", "end", "every", "k", "Math", "floor", "isFinite", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/millisecond.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.milliseconds = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar millisecond = (0, _interval.default)(function () {// noop\n}, function (date, step) {\n  date.setTime(+date + step);\n}, function (start, end) {\n  return end - start;\n}); // An optimized implementation for this simple case.\n\nmillisecond.every = function (k) {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return (0, _interval.default)(function (date) {\n    date.setTime(Math.floor(date / k) * k);\n  }, function (date, step) {\n    date.setTime(+date + step * k);\n  }, function (start, end) {\n    return (end - start) / k;\n  });\n};\n\nvar _default = millisecond;\nexports.default = _default;\nvar milliseconds = millisecond.range;\nexports.milliseconds = milliseconds;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE/C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,YAAY,CAAC;AAAA,CACrD,EAAE,UAAUO,IAAI,EAAEC,IAAI,EAAE;EACvBD,IAAI,CAACE,OAAO,CAAC,CAACF,IAAI,GAAGC,IAAI,CAAC;AAC5B,CAAC,EAAE,UAAUE,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAOA,GAAG,GAAGD,KAAK;AACpB,CAAC,CAAC,CAAC,CAAC;;AAEJJ,WAAW,CAACM,KAAK,GAAG,UAAUC,CAAC,EAAE;EAC/BA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACjB,IAAI,CAACG,QAAQ,CAACH,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;EACzC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,OAAOP,WAAW;EAChC,OAAO,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;IAC5CA,IAAI,CAACE,OAAO,CAACK,IAAI,CAACC,KAAK,CAACR,IAAI,GAAGM,CAAC,CAAC,GAAGA,CAAC,CAAC;EACxC,CAAC,EAAE,UAAUN,IAAI,EAAEC,IAAI,EAAE;IACvBD,IAAI,CAACE,OAAO,CAAC,CAACF,IAAI,GAAGC,IAAI,GAAGK,CAAC,CAAC;EAChC,CAAC,EAAE,UAAUH,KAAK,EAAEC,GAAG,EAAE;IACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIG,CAAC;EAC1B,CAAC,CAAC;AACJ,CAAC;AAED,IAAII,QAAQ,GAAGX,WAAW;AAC1BT,OAAO,CAACG,OAAO,GAAGiB,QAAQ;AAC1B,IAAIlB,YAAY,GAAGO,WAAW,CAACY,KAAK;AACpCrB,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}