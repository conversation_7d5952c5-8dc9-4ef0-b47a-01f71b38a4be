{"ast": null, "code": "import momentGenerateConfig from \"rc-picker/es/generate/moment\";\nimport generatePicker from './generatePicker';\nvar DatePicker = generatePicker(momentGenerateConfig);\nexport default DatePicker;", "map": {"version": 3, "names": ["momentGenerateConfig", "generatePicker", "DatePicker"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/date-picker/index.js"], "sourcesContent": ["import momentGenerateConfig from \"rc-picker/es/generate/moment\";\nimport generatePicker from './generatePicker';\nvar DatePicker = generatePicker(momentGenerateConfig);\nexport default DatePicker;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,IAAIC,UAAU,GAAGD,cAAc,CAACD,oBAAoB,CAAC;AACrD,eAAeE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}