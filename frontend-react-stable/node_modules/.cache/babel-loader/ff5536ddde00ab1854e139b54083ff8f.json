{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport unauthorized from './unauthorized';\nexport var IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport var ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n};\n// ExceptionImageMap keys\nvar ExceptionStatus = Object.keys(ExceptionMap);\nvar Icon = function Icon(_ref) {\n  var prefixCls = _ref.prefixCls,\n    icon = _ref.icon,\n    status = _ref.status;\n  var className = classNames(\"\".concat(prefixCls, \"-icon\"));\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'Result', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\")) : void 0;\n  if (ExceptionStatus.includes(\"\".concat(status))) {\n    var SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(className, \" \").concat(prefixCls, \"-image\")\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  var iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nvar Extra = function Extra(_ref2) {\n  var prefixCls = _ref2.prefixCls,\n    extra = _ref2.extra;\n  if (!extra) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra);\n};\nvar Result = function Result(_ref3) {\n  var customizePrefixCls = _ref3.prefixCls,\n    customizeClassName = _ref3.className,\n    subTitle = _ref3.subTitle,\n    title = _ref3.title,\n    style = _ref3.style,\n    children = _ref3.children,\n    _ref3$status = _ref3.status,\n    status = _ref3$status === void 0 ? 'info' : _ref3$status,\n    icon = _ref3.icon,\n    extra = _ref3.extra;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('result', customizePrefixCls);\n  var className = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(status), customizeClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Icon, {\n    prefixCls: prefixCls,\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-subtitle\")\n  }, subTitle), /*#__PURE__*/React.createElement(Extra, {\n    prefixCls: prefixCls,\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nexport default Result;", "map": {"version": 3, "names": ["_defineProperty", "CheckCircleFilled", "CloseCircleFilled", "ExclamationCircleFilled", "WarningFilled", "classNames", "React", "ConfigContext", "warning", "noFound", "serverError", "unauthorized", "IconMap", "success", "error", "info", "ExceptionMap", "ExceptionStatus", "Object", "keys", "Icon", "_ref", "prefixCls", "icon", "status", "className", "concat", "process", "env", "NODE_ENV", "length", "includes", "SVGComponent", "createElement", "iconNode", "Extra", "_ref2", "extra", "Result", "_ref3", "customizePrefixCls", "customizeClassName", "subTitle", "title", "style", "children", "_ref3$status", "_React$useContext", "useContext", "getPrefixCls", "direction", "PRESENTED_IMAGE_403", "PRESENTED_IMAGE_404", "PRESENTED_IMAGE_500"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/result/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport unauthorized from './unauthorized';\nexport var IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport var ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n};\n// ExceptionImageMap keys\nvar ExceptionStatus = Object.keys(ExceptionMap);\nvar Icon = function Icon(_ref) {\n  var prefixCls = _ref.prefixCls,\n    icon = _ref.icon,\n    status = _ref.status;\n  var className = classNames(\"\".concat(prefixCls, \"-icon\"));\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'Result', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\")) : void 0;\n  if (ExceptionStatus.includes(\"\".concat(status))) {\n    var SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(className, \" \").concat(prefixCls, \"-image\")\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  var iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nvar Extra = function Extra(_ref2) {\n  var prefixCls = _ref2.prefixCls,\n    extra = _ref2.extra;\n  if (!extra) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra);\n};\nvar Result = function Result(_ref3) {\n  var customizePrefixCls = _ref3.prefixCls,\n    customizeClassName = _ref3.className,\n    subTitle = _ref3.subTitle,\n    title = _ref3.title,\n    style = _ref3.style,\n    children = _ref3.children,\n    _ref3$status = _ref3.status,\n    status = _ref3$status === void 0 ? 'info' : _ref3$status,\n    icon = _ref3.icon,\n    extra = _ref3.extra;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('result', customizePrefixCls);\n  var className = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(status), customizeClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Icon, {\n    prefixCls: prefixCls,\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-subtitle\")\n  }, subTitle), /*#__PURE__*/React.createElement(Extra, {\n    prefixCls: prefixCls,\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nexport default Result;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,uBAAuB,MAAM,oDAAoD;AACxF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,IAAIC,OAAO,GAAG;EACnBC,OAAO,EAAEZ,iBAAiB;EAC1Ba,KAAK,EAAEZ,iBAAiB;EACxBa,IAAI,EAAEZ,uBAAuB;EAC7BK,OAAO,EAAEJ;AACX,CAAC;AACD,OAAO,IAAIY,YAAY,GAAG;EACxB,KAAK,EAAEP,OAAO;EACd,KAAK,EAAEC,WAAW;EAClB,KAAK,EAAEC;AACT,CAAC;AACD;AACA,IAAIM,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC;AAC/C,IAAII,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAE;EAC7B,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,SAAS,GAAGpB,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,CAAC;EACzDK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,OAAO,CAAC,EAAE,OAAOe,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,0EAA0E,CAACJ,MAAM,CAACH,IAAI,EAAE,yCAAyC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtP,IAAIN,eAAe,CAACc,QAAQ,CAAC,EAAE,CAACL,MAAM,CAACF,MAAM,CAAC,CAAC,EAAE;IAC/C,IAAIQ,YAAY,GAAGhB,YAAY,CAACQ,MAAM,CAAC;IACvC,OAAO,aAAalB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MAC7CR,SAAS,EAAE,EAAE,CAACC,MAAM,CAACD,SAAS,EAAE,GAAG,CAAC,CAACC,MAAM,CAACJ,SAAS,EAAE,QAAQ;IACjE,CAAC,EAAE,aAAahB,KAAK,CAAC2B,aAAa,CAACD,YAAY,EAAE,IAAI,CAAC,CAAC;EAC1D;EACA,IAAIE,QAAQ,GAAG,aAAa5B,KAAK,CAAC2B,aAAa,CAACrB,OAAO,CAACY,MAAM,CAAC,CAAC;EAChE,OAAO,aAAalB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC7CR,SAAS,EAAEA;EACb,CAAC,EAAEF,IAAI,IAAIW,QAAQ,CAAC;AACtB,CAAC;AACD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAId,SAAS,GAAGc,KAAK,CAACd,SAAS;IAC7Be,KAAK,GAAGD,KAAK,CAACC,KAAK;EACrB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,OAAO,aAAa/B,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC7CR,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEe,KAAK,CAAC;AACX,CAAC;AACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,kBAAkB,GAAGD,KAAK,CAACjB,SAAS;IACtCmB,kBAAkB,GAAGF,KAAK,CAACd,SAAS;IACpCiB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,YAAY,GAAGP,KAAK,CAACf,MAAM;IAC3BA,MAAM,GAAGsB,YAAY,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,YAAY;IACxDvB,IAAI,GAAGgB,KAAK,CAAChB,IAAI;IACjBc,KAAK,GAAGE,KAAK,CAACF,KAAK;EACrB,IAAIU,iBAAiB,GAAGzC,KAAK,CAAC0C,UAAU,CAACzC,aAAa,CAAC;IACrD0C,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAI5B,SAAS,GAAG2B,YAAY,CAAC,QAAQ,EAAET,kBAAkB,CAAC;EAC1D,IAAIf,SAAS,GAAGpB,UAAU,CAACiB,SAAS,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,GAAG,CAAC,CAACI,MAAM,CAACF,MAAM,CAAC,EAAEiB,kBAAkB,EAAEzC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0B,MAAM,CAACJ,SAAS,EAAE,MAAM,CAAC,EAAE4B,SAAS,KAAK,KAAK,CAAC,CAAC;EAC3K,OAAO,aAAa5C,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC7CR,SAAS,EAAEA,SAAS;IACpBmB,KAAK,EAAEA;EACT,CAAC,EAAE,aAAatC,KAAK,CAAC2B,aAAa,CAACb,IAAI,EAAE;IACxCE,SAAS,EAAEA,SAAS;IACpBE,MAAM,EAAEA,MAAM;IACdD,IAAI,EAAEA;EACR,CAAC,CAAC,EAAE,aAAajB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC1CR,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEqB,KAAK,CAAC,EAAED,QAAQ,IAAI,aAAapC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC7DR,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,WAAW;EAC7C,CAAC,EAAEoB,QAAQ,CAAC,EAAE,aAAapC,KAAK,CAAC2B,aAAa,CAACE,KAAK,EAAE;IACpDb,SAAS,EAAEA,SAAS;IACpBe,KAAK,EAAEA;EACT,CAAC,CAAC,EAAEQ,QAAQ,IAAI,aAAavC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACtDR,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEuB,QAAQ,CAAC,CAAC;AACf,CAAC;AACDP,MAAM,CAACa,mBAAmB,GAAGnC,YAAY,CAAC,KAAK,CAAC;AAChDsB,MAAM,CAACc,mBAAmB,GAAGpC,YAAY,CAAC,KAAK,CAAC;AAChDsB,MAAM,CAACe,mBAAmB,GAAGrC,YAAY,CAAC,KAAK,CAAC;AAChD,eAAesB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}