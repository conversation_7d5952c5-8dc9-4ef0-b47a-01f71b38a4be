{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\n/**\n * Execute code before next frame but async\n */\n\nexport function useLayoutState(defaultState) {\n  var stateRef = useRef(defaultState);\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    forceUpdate = _useState2[1];\n  var lastPromiseRef = useRef(null);\n  var updateBatchRef = useRef([]);\n  function setFrameState(updater) {\n    updateBatchRef.current.push(updater);\n    var promise = Promise.resolve();\n    lastPromiseRef.current = promise;\n    promise.then(function () {\n      if (lastPromiseRef.current === promise) {\n        var prevBatch = updateBatchRef.current;\n        var prevState = stateRef.current;\n        updateBatchRef.current = [];\n        prevBatch.forEach(function (batchUpdater) {\n          stateRef.current = batchUpdater(stateRef.current);\n        });\n        lastPromiseRef.current = null;\n        if (prevState !== stateRef.current) {\n          forceUpdate({});\n        }\n      }\n    });\n  }\n  useEffect(function () {\n    return function () {\n      lastPromiseRef.current = null;\n    };\n  }, []);\n  return [stateRef.current, setFrameState];\n}\n/** Lock frame, when frame pass reset the lock. */\n\nexport function useTimeoutLock(defaultState) {\n  var frameRef = useRef(defaultState || null);\n  var timeoutRef = useRef();\n  function cleanUp() {\n    window.clearTimeout(timeoutRef.current);\n  }\n  function setState(newState) {\n    frameRef.current = newState;\n    cleanUp();\n    timeoutRef.current = window.setTimeout(function () {\n      frameRef.current = null;\n      timeoutRef.current = undefined;\n    }, 100);\n  }\n  function getState() {\n    return frameRef.current;\n  }\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return [setState, getState];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useRef", "useState", "useEffect", "useLayoutState", "defaultState", "stateRef", "_useState", "_useState2", "forceUpdate", "lastPromiseRef", "updateBatchRef", "setFrameState", "updater", "current", "push", "promise", "Promise", "resolve", "then", "prevBatch", "prevState", "for<PERSON>ach", "batchUpdater", "useTimeoutLock", "frameRef", "timeoutRef", "cleanUp", "window", "clearTimeout", "setState", "newState", "setTimeout", "undefined", "getState"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/hooks/useFrame.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\n/**\n * Execute code before next frame but async\n */\n\nexport function useLayoutState(defaultState) {\n  var stateRef = useRef(defaultState);\n\n  var _useState = useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      forceUpdate = _useState2[1];\n\n  var lastPromiseRef = useRef(null);\n  var updateBatchRef = useRef([]);\n\n  function setFrameState(updater) {\n    updateBatchRef.current.push(updater);\n    var promise = Promise.resolve();\n    lastPromiseRef.current = promise;\n    promise.then(function () {\n      if (lastPromiseRef.current === promise) {\n        var prevBatch = updateBatchRef.current;\n        var prevState = stateRef.current;\n        updateBatchRef.current = [];\n        prevBatch.forEach(function (batchUpdater) {\n          stateRef.current = batchUpdater(stateRef.current);\n        });\n        lastPromiseRef.current = null;\n\n        if (prevState !== stateRef.current) {\n          forceUpdate({});\n        }\n      }\n    });\n  }\n\n  useEffect(function () {\n    return function () {\n      lastPromiseRef.current = null;\n    };\n  }, []);\n  return [stateRef.current, setFrameState];\n}\n/** Lock frame, when frame pass reset the lock. */\n\nexport function useTimeoutLock(defaultState) {\n  var frameRef = useRef(defaultState || null);\n  var timeoutRef = useRef();\n\n  function cleanUp() {\n    window.clearTimeout(timeoutRef.current);\n  }\n\n  function setState(newState) {\n    frameRef.current = newState;\n    cleanUp();\n    timeoutRef.current = window.setTimeout(function () {\n      frameRef.current = null;\n      timeoutRef.current = undefined;\n    }, 100);\n  }\n\n  function getState() {\n    return frameRef.current;\n  }\n\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return [setState, getState];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACnD;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,YAAY,EAAE;EAC3C,IAAIC,QAAQ,GAAGL,MAAM,CAACI,YAAY,CAAC;EAEnC,IAAIE,SAAS,GAAGL,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxBM,UAAU,GAAGR,cAAc,CAACO,SAAS,EAAE,CAAC,CAAC;IACzCE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE/B,IAAIE,cAAc,GAAGT,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIU,cAAc,GAAGV,MAAM,CAAC,EAAE,CAAC;EAE/B,SAASW,aAAaA,CAACC,OAAO,EAAE;IAC9BF,cAAc,CAACG,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACpC,IAAIG,OAAO,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC/BR,cAAc,CAACI,OAAO,GAAGE,OAAO;IAChCA,OAAO,CAACG,IAAI,CAAC,YAAY;MACvB,IAAIT,cAAc,CAACI,OAAO,KAAKE,OAAO,EAAE;QACtC,IAAII,SAAS,GAAGT,cAAc,CAACG,OAAO;QACtC,IAAIO,SAAS,GAAGf,QAAQ,CAACQ,OAAO;QAChCH,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3BM,SAAS,CAACE,OAAO,CAAC,UAAUC,YAAY,EAAE;UACxCjB,QAAQ,CAACQ,OAAO,GAAGS,YAAY,CAACjB,QAAQ,CAACQ,OAAO,CAAC;QACnD,CAAC,CAAC;QACFJ,cAAc,CAACI,OAAO,GAAG,IAAI;QAE7B,IAAIO,SAAS,KAAKf,QAAQ,CAACQ,OAAO,EAAE;UAClCL,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;IACF,CAAC,CAAC;EACJ;EAEAN,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBO,cAAc,CAACI,OAAO,GAAG,IAAI;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACR,QAAQ,CAACQ,OAAO,EAAEF,aAAa,CAAC;AAC1C;AACA;;AAEA,OAAO,SAASY,cAAcA,CAACnB,YAAY,EAAE;EAC3C,IAAIoB,QAAQ,GAAGxB,MAAM,CAACI,YAAY,IAAI,IAAI,CAAC;EAC3C,IAAIqB,UAAU,GAAGzB,MAAM,CAAC,CAAC;EAEzB,SAAS0B,OAAOA,CAAA,EAAG;IACjBC,MAAM,CAACC,YAAY,CAACH,UAAU,CAACZ,OAAO,CAAC;EACzC;EAEA,SAASgB,QAAQA,CAACC,QAAQ,EAAE;IAC1BN,QAAQ,CAACX,OAAO,GAAGiB,QAAQ;IAC3BJ,OAAO,CAAC,CAAC;IACTD,UAAU,CAACZ,OAAO,GAAGc,MAAM,CAACI,UAAU,CAAC,YAAY;MACjDP,QAAQ,CAACX,OAAO,GAAG,IAAI;MACvBY,UAAU,CAACZ,OAAO,GAAGmB,SAAS;IAChC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA,SAASC,QAAQA,CAAA,EAAG;IAClB,OAAOT,QAAQ,CAACX,OAAO;EACzB;EAEAX,SAAS,CAAC,YAAY;IACpB,OAAOwB,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACG,QAAQ,EAAEI,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}