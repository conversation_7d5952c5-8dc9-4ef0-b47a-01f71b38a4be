{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\nimport * as React from 'react';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport warning from \"rc-util/es/warning\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport ColumnGroup from './sugar/ColumnGroup';\nimport Column from './sugar/Column';\nimport Header from './Header/Header';\nimport TableContext from './context/TableContext';\nimport BodyContext from './context/BodyContext';\nimport Body from './Body';\nimport useColumns from './hooks/useColumns';\nimport { useLayoutState, useTimeoutLock } from './hooks/useFrame';\nimport { getPathValue, validateValue, getColumnsKey } from './utils/valueUtil';\nimport ResizeContext from './context/ResizeContext';\nimport useStickyOffsets from './hooks/useStickyOffsets';\nimport ColGroup from './ColGroup';\nimport { getExpandableProps } from './utils/legacyUtil';\nimport Panel from './Panel';\nimport Footer, { FooterComponents } from './Footer';\nimport { findAllChildrenKeys, renderExpandIcon } from './utils/expandUtil';\nimport { getCellFixedInfo } from './utils/fixUtil';\nimport StickyScrollBar from './stickyScrollBar';\nimport useSticky from './hooks/useSticky';\nimport FixedHolder from './FixedHolder';\nimport Summary from './Footer/Summary';\nimport StickyContext from './context/StickyContext';\nimport ExpandedRowContext from './context/ExpandedRowContext';\nimport { EXPAND_COLUMN } from './constant'; // Used for conditions cache\n\nvar EMPTY_DATA = []; // Used for customize scroll\n\nvar EMPTY_SCROLL_TARGET = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';\nvar MemoTableContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  if (!shallowEqual(prev.props, next.props)) {\n    return false;\n  } // No additional render when pinged status change.\n  // This is not a bug.\n\n  return prev.pingLeft !== next.pingLeft || prev.pingRight !== next.pingRight;\n});\nfunction Table(props) {\n  var _classNames;\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length; // ===================== Warning ======================\n\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  } // ==================== Customize =====================\n\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getPathValue(components || {}, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]); // ====================== Expand ======================\n\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n    expandedRowKeys = expandableConfig.expandedRowKeys,\n    defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n    defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n    expandedRowRender = expandableConfig.expandedRowRender,\n    columnTitle = expandableConfig.columnTitle,\n    onExpand = expandableConfig.onExpand,\n    onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n    expandRowByClick = expandableConfig.expandRowByClick,\n    rowExpandable = expandableConfig.rowExpandable,\n    expandIconColumnIndex = expandableConfig.expandIconColumnIndex,\n    expandedRowClassName = expandableConfig.expandedRowClassName,\n    childrenColumnName = expandableConfig.childrenColumnName,\n    indentSize = expandableConfig.indentSize;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n\n    if (props.expandable && internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n  var _React$useState = React.useState(function () {\n      if (defaultExpandedRowKeys) {\n        return defaultExpandedRowKeys;\n      }\n      if (defaultExpandAllRows) {\n        return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n      }\n      return [];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerExpandedKeys = _React$useState2[0],\n    setInnerExpandedKeys = _React$useState2[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n    setInnerExpandedKeys(newExpandedKeys);\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]); // Warning if use `expandedRowRender` and nest children in the same time\n\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  } // ====================== Column ======================\n\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    componentWidth = _React$useState4[0],\n    setComponentWidth = _React$useState4[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandedRowRender,\n      columnTitle: columnTitle,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandIconColumnIndex,\n      direction: direction\n    }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 2),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1];\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]); // ====================== Scroll ======================\n\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedLeft = _React$useState6[0],\n    setPingedLeft = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    pingedRight = _React$useState8[0],\n    setPingedRight = _React$useState8[1];\n  var _useLayoutState = useLayoutState(new Map()),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    colsWidths = _useLayoutState2[0],\n    updateColsWidths = _useLayoutState2[1]; // Convert map to number width\n\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref2) {\n    var fixed = _ref2.fixed;\n    return fixed;\n  }); // Sticky\n\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container; // Footer (Fix footer must fixed header)\n\n  var summaryNode = summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed; // Scroll\n\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    }; // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n        return widths;\n      });\n    }\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      // eslint-disable-next-line no-param-reassign\n      target.scrollLeft = scrollLeft;\n    }\n  }\n  var onScroll = function onScroll(_ref3) {\n    var currentTarget = _ref3.currentTarget,\n      scrollLeft = _ref3.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n        clientWidth = currentTarget.clientWidth; // There is no space to scroll\n\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  };\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref4) {\n    var width = _ref4.width;\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  }; // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []); // ===================== Effects ======================\n\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    scrollbarSize = _React$useState10[0],\n    setScrollbarSize = _React$useState10[1];\n  var _React$useState11 = React.useState(true),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    supportSticky = _React$useState12[0],\n    setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n  React.useEffect(function () {\n    if (scrollBodyRef.current instanceof Element) {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    } else {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n    }\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []); // ================== INTERNAL HOOKS ==================\n\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  }); // ====================== Render ======================\n\n  var TableComponent = getComponent(['table'], 'table'); // Table layout\n\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    } // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref5) {\n      var ellipsis = _ref5.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode; // Header props\n\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  }; // Empty\n\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]); // Body\n\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref6) {\n      var width = _ref6.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var customizeScrollBody = getComponent(['body']);\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref7, index) {\n        var width = _ref7.width;\n        var colWidth = index === columns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        warning(false, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, {\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    } // Fixed holder share the props\n\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n    }), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n    }), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, {\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n  var ariaProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, ariaProps), /*#__PURE__*/React.createElement(MemoTableContent, {\n    pingLeft: pingedLeft,\n    pingRight: pingedRight,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      stickyOffsets: stickyOffsets,\n      mergedExpandedKeys: mergedExpandedKeys\n    })\n  }, title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData))));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var TableContextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: flattenColumns.map(function (_, colIndex) {\n        return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n      }),\n      isSticky: isSticky\n    };\n  }, [prefixCls, getComponent, scrollbarSize, direction, flattenColumns, stickyOffsets, isSticky]);\n  var BodyContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, columnContext), {}, {\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandRowByClick,\n      expandedRowRender: expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandIconColumnIndex,\n      indentSize: indentSize,\n      allColumnsFixedLeft: columnContext.flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      })\n    });\n  }, [columnContext, mergedTableLayout, rowClassName, expandedRowClassName, mergedExpandIcon, expandableType, expandRowByClick, expandedRowRender, onTriggerExpand, expandIconColumnIndex, indentSize]);\n  var ExpandedRowContextValue = React.useMemo(function () {\n    return {\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll\n    };\n  }, [componentWidth, fixHeader, fixColumn, horizonScroll]);\n  var ResizeContextValue = React.useMemo(function () {\n    return {\n      onColumnResize: onColumnResize\n    };\n  }, [onColumnResize]);\n  return /*#__PURE__*/React.createElement(StickyContext.Provider, {\n    value: supportSticky\n  }, /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, /*#__PURE__*/React.createElement(BodyContext.Provider, {\n    value: BodyContextValue\n  }, /*#__PURE__*/React.createElement(ExpandedRowContext.Provider, {\n    value: ExpandedRowContextValue\n  }, /*#__PURE__*/React.createElement(ResizeContext.Provider, {\n    value: ResizeContextValue\n  }, fullTable)))));\n}\nTable.EXPAND_COLUMN = EXPAND_COLUMN;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = FooterComponents;\nTable.defaultProps = {\n  rowKey: 'key',\n  prefixCls: 'rc-table',\n  emptyText: function emptyText() {\n    return 'No Data';\n  }\n};\nexport default Table;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectSpread", "_toConsumableArray", "_slicedToArray", "_typeof", "React", "isVisible", "pickAttrs", "isStyleSupport", "classNames", "shallowEqual", "warning", "ResizeObserver", "getTargetScrollBarSize", "ColumnGroup", "Column", "Header", "TableContext", "BodyContext", "Body", "useColumns", "useLayoutState", "useTimeoutLock", "getPathValue", "validate<PERSON><PERSON>ue", "getColumnsKey", "ResizeContext", "useStickyOffsets", "ColGroup", "getExpandableProps", "Panel", "Footer", "FooterComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderExpandIcon", "getCellFixedInfo", "StickyScrollBar", "useSticky", "FixedHolder", "Summary", "StickyContext", "ExpandedRowContext", "EXPAND_COLUMN", "EMPTY_DATA", "EMPTY_SCROLL_TARGET", "INTERNAL_HOOKS", "MemoTableContent", "memo", "_ref", "children", "prev", "next", "props", "pingLeft", "pingRight", "Table", "_classNames", "prefixCls", "className", "rowClassName", "style", "data", "<PERSON><PERSON><PERSON>", "scroll", "tableLayout", "direction", "title", "footer", "summary", "id", "showHeader", "components", "emptyText", "onRow", "onHeaderRow", "internalHooks", "transformColumns", "internalRefs", "sticky", "mergedData", "hasData", "length", "process", "env", "NODE_ENV", "for<PERSON>ach", "name", "undefined", "concat", "getComponent", "useCallback", "path", "defaultComponent", "getRowKey", "useMemo", "record", "key", "expandableConfig", "expandIcon", "expandedRowKeys", "defaultExpandedRowKeys", "defaultExpandAllRows", "expandedRowRender", "columnTitle", "onExpand", "onExpandedRowsChange", "expandRowByClick", "rowExpandable", "expandIconColumnIndex", "expandedRowClassName", "childrenColumnName", "indentSize", "mergedExpandIcon", "mergedChildrenColumnName", "expandableType", "expandable", "__PARENT_RENDER_ICON__", "some", "_React$useState", "useState", "_React$useState2", "innerExpandedKeys", "setInnerExpandedKeys", "mergedExpandedKeys", "Set", "onTriggerExpand", "indexOf", "newExpandedKeys", "<PERSON><PERSON><PERSON>", "has", "delete", "Array", "isArray", "_React$useState3", "_React$useState4", "componentWidth", "setComponentWidth", "_useColumns", "expandedKeys", "_useColumns2", "columns", "flattenColumns", "columnContext", "fullTableRef", "useRef", "scrollHeaderRef", "scrollBodyRef", "scrollBodyContainerRef", "scrollSummaryRef", "_React$useState5", "_React$useState6", "pingedLeft", "setPingedLeft", "_React$useState7", "_React$useState8", "pingedRight", "setPingedRight", "_useLayoutState", "Map", "_useLayoutState2", "colsWidths", "updateColsWidths", "colsKeys", "pureColWidths", "map", "column<PERSON>ey", "get", "col<PERSON><PERSON><PERSON>", "join", "stickyOffsets", "fixHeader", "y", "horizonScroll", "x", "Boolean", "fixed", "fixColumn", "_ref2", "stickyRef", "_useSticky", "isSticky", "offsetHeader", "offsetSummary", "offsetScroll", "stickyClassName", "container", "summaryNode", "fixFooter", "isValidElement", "type", "scrollXStyle", "scrollYStyle", "scrollTableStyle", "overflowY", "maxHeight", "overflowX", "width", "min<PERSON><PERSON><PERSON>", "onColumnResize", "current", "widths", "newWidths", "set", "_useTimeoutLock", "_useTimeoutLock2", "setScrollTarget", "getScrollTarget", "forceScroll", "scrollLeft", "target", "onScroll", "_ref3", "currentTarget", "isRTL", "mergedScrollLeft", "compareTarget", "_stickyRef$current", "setScrollLeft", "scrollWidth", "clientWidth", "triggerOnScroll", "onFullTableResize", "_ref4", "offsetWidth", "mounted", "useEffect", "_React$useState9", "_React$useState10", "scrollbarSize", "setScrollbarSize", "_React$useState11", "_React$useState12", "supportSticky", "setSupportSticky", "Element", "body", "TableComponent", "mergedTableLayout", "_ref5", "ellipsis", "groupTableNode", "headerProps", "columCount", "emptyNode", "bodyTable", "createElement", "measureColumnWidth", "bodyColGroup", "_ref6", "customizeScrollBody", "bodyContent", "ref", "_ref7", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "fixedHolderProps", "noData", "maxContentScroll", "Fragment", "stickyTopOffset", "fixedHolderPassProps", "stickyBottomOffset", "ariaProps", "aria", "fullTable", "onResize", "TableContextValue", "fixedInfoList", "_", "colIndex", "BodyContextValue", "allColumnsFixedLeft", "every", "col", "ExpandedRowContextValue", "ResizeContextValue", "Provider", "value", "defaultProps"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Table.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\nimport * as React from 'react';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport warning from \"rc-util/es/warning\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport ColumnGroup from './sugar/ColumnGroup';\nimport Column from './sugar/Column';\nimport Header from './Header/Header';\nimport TableContext from './context/TableContext';\nimport BodyContext from './context/BodyContext';\nimport Body from './Body';\nimport useColumns from './hooks/useColumns';\nimport { useLayoutState, useTimeoutLock } from './hooks/useFrame';\nimport { getPathValue, validateValue, getColumnsKey } from './utils/valueUtil';\nimport ResizeContext from './context/ResizeContext';\nimport useStickyOffsets from './hooks/useStickyOffsets';\nimport ColGroup from './ColGroup';\nimport { getExpandableProps } from './utils/legacyUtil';\nimport Panel from './Panel';\nimport Footer, { FooterComponents } from './Footer';\nimport { findAllChildrenKeys, renderExpandIcon } from './utils/expandUtil';\nimport { getCellFixedInfo } from './utils/fixUtil';\nimport StickyScrollBar from './stickyScrollBar';\nimport useSticky from './hooks/useSticky';\nimport FixedHolder from './FixedHolder';\nimport Summary from './Footer/Summary';\nimport StickyContext from './context/StickyContext';\nimport ExpandedRowContext from './context/ExpandedRowContext';\nimport { EXPAND_COLUMN } from './constant'; // Used for conditions cache\n\nvar EMPTY_DATA = []; // Used for customize scroll\n\nvar EMPTY_SCROLL_TARGET = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';\nvar MemoTableContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  if (!shallowEqual(prev.props, next.props)) {\n    return false;\n  } // No additional render when pinged status change.\n  // This is not a bug.\n\n\n  return prev.pingLeft !== next.pingLeft || prev.pingRight !== next.pingRight;\n});\n\nfunction Table(props) {\n  var _classNames;\n\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      rowClassName = props.rowClassName,\n      style = props.style,\n      data = props.data,\n      rowKey = props.rowKey,\n      scroll = props.scroll,\n      tableLayout = props.tableLayout,\n      direction = props.direction,\n      title = props.title,\n      footer = props.footer,\n      summary = props.summary,\n      id = props.id,\n      showHeader = props.showHeader,\n      components = props.components,\n      emptyText = props.emptyText,\n      onRow = props.onRow,\n      onHeaderRow = props.onHeaderRow,\n      internalHooks = props.internalHooks,\n      transformColumns = props.transformColumns,\n      internalRefs = props.internalRefs,\n      sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length; // ===================== Warning ======================\n\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  } // ==================== Customize =====================\n\n\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getPathValue(components || {}, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n\n    return function (record) {\n      var key = record && record[rowKey];\n\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n\n      return key;\n    };\n  }, [rowKey]); // ====================== Expand ======================\n\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n      expandedRowKeys = expandableConfig.expandedRowKeys,\n      defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n      defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n      expandedRowRender = expandableConfig.expandedRowRender,\n      columnTitle = expandableConfig.columnTitle,\n      onExpand = expandableConfig.onExpand,\n      onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n      expandRowByClick = expandableConfig.expandRowByClick,\n      rowExpandable = expandableConfig.rowExpandable,\n      expandIconColumnIndex = expandableConfig.expandIconColumnIndex,\n      expandedRowClassName = expandableConfig.expandedRowClassName,\n      childrenColumnName = expandableConfig.childrenColumnName,\n      indentSize = expandableConfig.indentSize;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n\n\n    if (props.expandable && internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n\n\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n\n  var _React$useState = React.useState(function () {\n    if (defaultExpandedRowKeys) {\n      return defaultExpandedRowKeys;\n    }\n\n    if (defaultExpandAllRows) {\n      return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n    }\n\n    return [];\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      innerExpandedKeys = _React$useState2[0],\n      setInnerExpandedKeys = _React$useState2[1];\n\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n\n    setInnerExpandedKeys(newExpandedKeys);\n\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]); // Warning if use `expandedRowRender` and nest children in the same time\n\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  } // ====================== Column ======================\n\n\n  var _React$useState3 = React.useState(0),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      componentWidth = _React$useState4[0],\n      setComponentWidth = _React$useState4[1];\n\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n    expandable: !!expandedRowRender,\n    columnTitle: columnTitle,\n    expandedKeys: mergedExpandedKeys,\n    getRowKey: getRowKey,\n    // https://github.com/ant-design/ant-design/issues/23894\n    onTriggerExpand: onTriggerExpand,\n    expandIcon: mergedExpandIcon,\n    expandIconColumnIndex: expandIconColumnIndex,\n    direction: direction\n  }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n      _useColumns2 = _slicedToArray(_useColumns, 2),\n      columns = _useColumns2[0],\n      flattenColumns = _useColumns2[1];\n\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]); // ====================== Scroll ======================\n\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n\n  var _React$useState5 = React.useState(false),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      pingedLeft = _React$useState6[0],\n      setPingedLeft = _React$useState6[1];\n\n  var _React$useState7 = React.useState(false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      pingedRight = _React$useState8[0],\n      setPingedRight = _React$useState8[1];\n\n  var _useLayoutState = useLayoutState(new Map()),\n      _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n      colsWidths = _useLayoutState2[0],\n      updateColsWidths = _useLayoutState2[1]; // Convert map to number width\n\n\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref2) {\n    var fixed = _ref2.fixed;\n    return fixed;\n  }); // Sticky\n\n  var stickyRef = React.useRef();\n\n  var _useSticky = useSticky(sticky, prefixCls),\n      isSticky = _useSticky.isSticky,\n      offsetHeader = _useSticky.offsetHeader,\n      offsetSummary = _useSticky.offsetSummary,\n      offsetScroll = _useSticky.offsetScroll,\n      stickyClassName = _useSticky.stickyClassName,\n      container = _useSticky.container; // Footer (Fix footer must fixed header)\n\n\n  var summaryNode = summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed; // Scroll\n\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    }; // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n\n        return widths;\n      });\n    }\n  }, []);\n\n  var _useTimeoutLock = useTimeoutLock(null),\n      _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n      setScrollTarget = _useTimeoutLock2[0],\n      getScrollTarget = _useTimeoutLock2[1];\n\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      // eslint-disable-next-line no-param-reassign\n      target.scrollLeft = scrollLeft;\n    }\n  }\n\n  var onScroll = function onScroll(_ref3) {\n    var currentTarget = _ref3.currentTarget,\n        scrollLeft = _ref3.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n          clientWidth = currentTarget.clientWidth; // There is no space to scroll\n\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  };\n\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n\n  var onFullTableResize = function onFullTableResize(_ref4) {\n    var width = _ref4.width;\n\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  }; // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n\n\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []); // ===================== Effects ======================\n\n  var _React$useState9 = React.useState(0),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      scrollbarSize = _React$useState10[0],\n      setScrollbarSize = _React$useState10[1];\n\n  var _React$useState11 = React.useState(true),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      supportSticky = _React$useState12[0],\n      setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n\n  React.useEffect(function () {\n    if (scrollBodyRef.current instanceof Element) {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    } else {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n    }\n\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []); // ================== INTERNAL HOOKS ==================\n\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  }); // ====================== Render ======================\n\n  var TableComponent = getComponent(['table'], 'table'); // Table layout\n\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    } // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n\n\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref5) {\n      var ellipsis = _ref5.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode; // Header props\n\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  }; // Empty\n\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n\n    return emptyText;\n  }, [hasData, emptyText]); // Body\n\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref6) {\n      var width = _ref6.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var customizeScrollBody = getComponent(['body']);\n\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref7, index) {\n        var width = _ref7.width;\n        var colWidth = index === columns.length - 1 ? width - scrollbarSize : width;\n\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n\n        warning(false, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, {\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    } // Fixed holder share the props\n\n\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n    }), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n    }), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, {\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n\n  var ariaProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, ariaProps), /*#__PURE__*/React.createElement(MemoTableContent, {\n    pingLeft: pingedLeft,\n    pingRight: pingedRight,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      stickyOffsets: stickyOffsets,\n      mergedExpandedKeys: mergedExpandedKeys\n    })\n  }, title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData))));\n\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n\n  var TableContextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: flattenColumns.map(function (_, colIndex) {\n        return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n      }),\n      isSticky: isSticky\n    };\n  }, [prefixCls, getComponent, scrollbarSize, direction, flattenColumns, stickyOffsets, isSticky]);\n  var BodyContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, columnContext), {}, {\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandRowByClick,\n      expandedRowRender: expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandIconColumnIndex,\n      indentSize: indentSize,\n      allColumnsFixedLeft: columnContext.flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      })\n    });\n  }, [columnContext, mergedTableLayout, rowClassName, expandedRowClassName, mergedExpandIcon, expandableType, expandRowByClick, expandedRowRender, onTriggerExpand, expandIconColumnIndex, indentSize]);\n  var ExpandedRowContextValue = React.useMemo(function () {\n    return {\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll\n    };\n  }, [componentWidth, fixHeader, fixColumn, horizonScroll]);\n  var ResizeContextValue = React.useMemo(function () {\n    return {\n      onColumnResize: onColumnResize\n    };\n  }, [onColumnResize]);\n  return /*#__PURE__*/React.createElement(StickyContext.Provider, {\n    value: supportSticky\n  }, /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, /*#__PURE__*/React.createElement(BodyContext.Provider, {\n    value: BodyContextValue\n  }, /*#__PURE__*/React.createElement(ExpandedRowContext.Provider, {\n    value: ExpandedRowContextValue\n  }, /*#__PURE__*/React.createElement(ResizeContext.Provider, {\n    value: ResizeContextValue\n  }, fullTable)))));\n}\n\nTable.EXPAND_COLUMN = EXPAND_COLUMN;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = FooterComponents;\nTable.defaultProps = {\n  rowKey: 'key',\n  prefixCls: 'rc-table',\n  emptyText: function emptyText() {\n    return 'No Data';\n  }\n};\nexport default Table;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAkB;AACjE,SAASC,YAAY,EAAEC,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AAC9E,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,IAAIC,gBAAgB,QAAQ,UAAU;AACnD,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC1E,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,aAAa,QAAQ,YAAY,CAAC,CAAC;;AAE5C,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;;AAErB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,OAAO,IAAIC,cAAc,GAAG,wBAAwB;AACpD,IAAIC,gBAAgB,GAAG,aAAazC,KAAK,CAAC0C,IAAI,CAAC,UAAUC,IAAI,EAAE;EAC7D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,IAAI,EAAEC,IAAI,EAAE;EACvB,IAAI,CAACzC,YAAY,CAACwC,IAAI,CAACE,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC,EAAE;IACzC,OAAO,KAAK;EACd,CAAC,CAAC;EACF;;EAGA,OAAOF,IAAI,CAACG,QAAQ,KAAKF,IAAI,CAACE,QAAQ,IAAIH,IAAI,CAACI,SAAS,KAAKH,IAAI,CAACG,SAAS;AAC7E,CAAC,CAAC;AAEF,SAASC,KAAKA,CAACH,KAAK,EAAE;EACpB,IAAII,WAAW;EAEf,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,KAAK,GAAGd,KAAK,CAACc,KAAK;IACnBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,EAAE,GAAGjB,KAAK,CAACiB,EAAE;IACbC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IAC3BC,KAAK,GAAGrB,KAAK,CAACqB,KAAK;IACnBC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,aAAa,GAAGvB,KAAK,CAACuB,aAAa;IACnCC,gBAAgB,GAAGxB,KAAK,CAACwB,gBAAgB;IACzCC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;EACzB,IAAIC,UAAU,GAAGlB,IAAI,IAAIlB,UAAU;EACnC,IAAIqC,OAAO,GAAG,CAAC,CAACD,UAAU,CAACE,MAAM,CAAC,CAAC;;EAEnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACnH3E,OAAO,CAACyC,KAAK,CAACkC,IAAI,CAAC,KAAKC,SAAS,EAAE,GAAG,CAACC,MAAM,CAACF,IAAI,EAAE,2CAA2C,CAAC,CAAC;IACnG,CAAC,CAAC;IACF3E,OAAO,CAAC,EAAE,gBAAgB,IAAIyC,KAAK,CAAC,EAAE,yEAAyE,CAAC;EAClH,CAAC,CAAC;;EAGF,IAAIqC,YAAY,GAAGpF,KAAK,CAACqF,WAAW,CAAC,UAAUC,IAAI,EAAEC,gBAAgB,EAAE;IACrE,OAAOrE,YAAY,CAACgD,UAAU,IAAI,CAAC,CAAC,EAAEoB,IAAI,CAAC,IAAIC,gBAAgB;EACjE,CAAC,EAAE,CAACrB,UAAU,CAAC,CAAC;EAChB,IAAIsB,SAAS,GAAGxF,KAAK,CAACyF,OAAO,CAAC,YAAY;IACxC,IAAI,OAAOhC,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IAEA,OAAO,UAAUiC,MAAM,EAAE;MACvB,IAAIC,GAAG,GAAGD,MAAM,IAAIA,MAAM,CAACjC,MAAM,CAAC;MAElC,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzE,OAAO,CAACqF,GAAG,KAAKT,SAAS,EAAE,iGAAiG,CAAC;MAC/H;MAEA,OAAOS,GAAG;IACZ,CAAC;EACH,CAAC,EAAE,CAAClC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEd,IAAImC,gBAAgB,GAAGpE,kBAAkB,CAACuB,KAAK,CAAC;EAChD,IAAI8C,UAAU,GAAGD,gBAAgB,CAACC,UAAU;IACxCC,eAAe,GAAGF,gBAAgB,CAACE,eAAe;IAClDC,sBAAsB,GAAGH,gBAAgB,CAACG,sBAAsB;IAChEC,oBAAoB,GAAGJ,gBAAgB,CAACI,oBAAoB;IAC5DC,iBAAiB,GAAGL,gBAAgB,CAACK,iBAAiB;IACtDC,WAAW,GAAGN,gBAAgB,CAACM,WAAW;IAC1CC,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ;IACpCC,oBAAoB,GAAGR,gBAAgB,CAACQ,oBAAoB;IAC5DC,gBAAgB,GAAGT,gBAAgB,CAACS,gBAAgB;IACpDC,aAAa,GAAGV,gBAAgB,CAACU,aAAa;IAC9CC,qBAAqB,GAAGX,gBAAgB,CAACW,qBAAqB;IAC9DC,oBAAoB,GAAGZ,gBAAgB,CAACY,oBAAoB;IAC5DC,kBAAkB,GAAGb,gBAAgB,CAACa,kBAAkB;IACxDC,UAAU,GAAGd,gBAAgB,CAACc,UAAU;EAC5C,IAAIC,gBAAgB,GAAGd,UAAU,IAAIhE,gBAAgB;EACrD,IAAI+E,wBAAwB,GAAGH,kBAAkB,IAAI,UAAU;EAC/D,IAAII,cAAc,GAAG7G,KAAK,CAACyF,OAAO,CAAC,YAAY;IAC7C,IAAIQ,iBAAiB,EAAE;MACrB,OAAO,KAAK;IACd;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI,IAAIlD,KAAK,CAAC+D,UAAU,IAAIxC,aAAa,KAAK9B,cAAc,IAAIO,KAAK,CAAC+D,UAAU,CAACC,sBAAsB,IAAIrC,UAAU,CAACsC,IAAI,CAAC,UAAUtB,MAAM,EAAE;MACvI,OAAOA,MAAM,IAAI3F,OAAO,CAAC2F,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACkB,wBAAwB,CAAC;IACnF,CAAC,CAAC,EAAE;MACF,OAAO,MAAM;IACf;IACA;;IAGA,OAAO,KAAK;EACd,CAAC,EAAE,CAAC,CAAC,CAACX,iBAAiB,EAAEvB,UAAU,CAAC,CAAC;EAErC,IAAIuC,eAAe,GAAGjH,KAAK,CAACkH,QAAQ,CAAC,YAAY;MAC/C,IAAInB,sBAAsB,EAAE;QAC1B,OAAOA,sBAAsB;MAC/B;MAEA,IAAIC,oBAAoB,EAAE;QACxB,OAAOpE,mBAAmB,CAAC8C,UAAU,EAAEc,SAAS,EAAEoB,wBAAwB,CAAC;MAC7E;MAEA,OAAO,EAAE;IACX,CAAC,CAAC;IACEO,gBAAgB,GAAGrH,cAAc,CAACmH,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE9C,IAAIG,kBAAkB,GAAGtH,KAAK,CAACyF,OAAO,CAAC,YAAY;IACjD,OAAO,IAAI8B,GAAG,CAACzB,eAAe,IAAIsB,iBAAiB,IAAI,EAAE,CAAC;EAC5D,CAAC,EAAE,CAACtB,eAAe,EAAEsB,iBAAiB,CAAC,CAAC;EACxC,IAAII,eAAe,GAAGxH,KAAK,CAACqF,WAAW,CAAC,UAAUK,MAAM,EAAE;IACxD,IAAIC,GAAG,GAAGH,SAAS,CAACE,MAAM,EAAEhB,UAAU,CAAC+C,OAAO,CAAC/B,MAAM,CAAC,CAAC;IACvD,IAAIgC,eAAe;IACnB,IAAIC,MAAM,GAAGL,kBAAkB,CAACM,GAAG,CAACjC,GAAG,CAAC;IAExC,IAAIgC,MAAM,EAAE;MACVL,kBAAkB,CAACO,MAAM,CAAClC,GAAG,CAAC;MAC9B+B,eAAe,GAAG7H,kBAAkB,CAACyH,kBAAkB,CAAC;IAC1D,CAAC,MAAM;MACLI,eAAe,GAAG,EAAE,CAACvC,MAAM,CAACtF,kBAAkB,CAACyH,kBAAkB,CAAC,EAAE,CAAC3B,GAAG,CAAC,CAAC;IAC5E;IAEA0B,oBAAoB,CAACK,eAAe,CAAC;IAErC,IAAIvB,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAACwB,MAAM,EAAEjC,MAAM,CAAC;IAC3B;IAEA,IAAIU,oBAAoB,EAAE;MACxBA,oBAAoB,CAACsB,eAAe,CAAC;IACvC;EACF,CAAC,EAAE,CAAClC,SAAS,EAAE8B,kBAAkB,EAAE5C,UAAU,EAAEyB,QAAQ,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEjF,IAAIvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIkB,iBAAiB,IAAIvB,UAAU,CAACsC,IAAI,CAAC,UAAUtB,MAAM,EAAE;IAClG,OAAOoC,KAAK,CAACC,OAAO,CAACrC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACkB,wBAAwB,CAAC,CAAC;EACxG,CAAC,CAAC,EAAE;IACFtG,OAAO,CAAC,KAAK,EAAE,sDAAsD,CAAC;EACxE,CAAC,CAAC;;EAGF,IAAI0H,gBAAgB,GAAGhI,KAAK,CAACkH,QAAQ,CAAC,CAAC,CAAC;IACpCe,gBAAgB,GAAGnI,cAAc,CAACkI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,WAAW,GAAGrH,UAAU,CAACnB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAAC,EAAE6C,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MACxGkB,UAAU,EAAE,CAAC,CAACb,iBAAiB;MAC/BC,WAAW,EAAEA,WAAW;MACxBmC,YAAY,EAAEf,kBAAkB;MAChC9B,SAAS,EAAEA,SAAS;MACpB;MACAgC,eAAe,EAAEA,eAAe;MAChC3B,UAAU,EAAEc,gBAAgB;MAC5BJ,qBAAqB,EAAEA,qBAAqB;MAC5C3C,SAAS,EAAEA;IACb,CAAC,CAAC,EAAEU,aAAa,KAAK9B,cAAc,GAAG+B,gBAAgB,GAAG,IAAI,CAAC;IAC3D+D,YAAY,GAAGxI,cAAc,CAACsI,WAAW,EAAE,CAAC,CAAC;IAC7CG,OAAO,GAAGD,YAAY,CAAC,CAAC,CAAC;IACzBE,cAAc,GAAGF,YAAY,CAAC,CAAC,CAAC;EAEpC,IAAIG,aAAa,GAAGzI,KAAK,CAACyF,OAAO,CAAC,YAAY;IAC5C,OAAO;MACL8C,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,OAAO,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIE,YAAY,GAAG1I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAG5I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EACpC,IAAIE,aAAa,GAAG7I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EAClC,IAAIG,sBAAsB,GAAG9I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EAC3C,IAAII,gBAAgB,GAAG/I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EAErC,IAAIK,gBAAgB,GAAGhJ,KAAK,CAACkH,QAAQ,CAAC,KAAK,CAAC;IACxC+B,gBAAgB,GAAGnJ,cAAc,CAACkJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,IAAIG,gBAAgB,GAAGpJ,KAAK,CAACkH,QAAQ,CAAC,KAAK,CAAC;IACxCmC,gBAAgB,GAAGvJ,cAAc,CAACsJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,eAAe,GAAGxI,cAAc,CAAC,IAAIyI,GAAG,CAAC,CAAC,CAAC;IAC3CC,gBAAgB,GAAG5J,cAAc,CAAC0J,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,QAAQ,GAAGzI,aAAa,CAACoH,cAAc,CAAC;EAC5C,IAAIsB,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAAC,UAAUC,SAAS,EAAE;IACpD,OAAOL,UAAU,CAACM,GAAG,CAACD,SAAS,CAAC;EAClC,CAAC,CAAC;EACF,IAAIE,SAAS,GAAGlK,KAAK,CAACyF,OAAO,CAAC,YAAY;IACxC,OAAOqE,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAG9I,gBAAgB,CAAC4I,SAAS,EAAE1B,cAAc,CAAC5D,MAAM,EAAEhB,SAAS,CAAC;EACjF,IAAIyG,SAAS,GAAG3G,MAAM,IAAIvC,aAAa,CAACuC,MAAM,CAAC4G,CAAC,CAAC;EACjD,IAAIC,aAAa,GAAG7G,MAAM,IAAIvC,aAAa,CAACuC,MAAM,CAAC8G,CAAC,CAAC,IAAIC,OAAO,CAAC7E,gBAAgB,CAAC8E,KAAK,CAAC;EACxF,IAAIC,SAAS,GAAGJ,aAAa,IAAI/B,cAAc,CAACxB,IAAI,CAAC,UAAU4D,KAAK,EAAE;IACpE,IAAIF,KAAK,GAAGE,KAAK,CAACF,KAAK;IACvB,OAAOA,KAAK;EACd,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIG,SAAS,GAAG7K,KAAK,CAAC2I,MAAM,CAAC,CAAC;EAE9B,IAAImC,UAAU,GAAG9I,SAAS,CAACyC,MAAM,EAAErB,SAAS,CAAC;IACzC2H,QAAQ,GAAGD,UAAU,CAACC,QAAQ;IAC9BC,YAAY,GAAGF,UAAU,CAACE,YAAY;IACtCC,aAAa,GAAGH,UAAU,CAACG,aAAa;IACxCC,YAAY,GAAGJ,UAAU,CAACI,YAAY;IACtCC,eAAe,GAAGL,UAAU,CAACK,eAAe;IAC5CC,SAAS,GAAGN,UAAU,CAACM,SAAS,CAAC,CAAC;;EAGtC,IAAIC,WAAW,GAAGtH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,UAAU,CAAC;EACvF,IAAI4G,SAAS,GAAG,CAACjB,SAAS,IAAIU,QAAQ,KAAK,aAAa/K,KAAK,CAACuL,cAAc,CAACF,WAAW,CAAC,IAAIA,WAAW,CAACG,IAAI,KAAKtJ,OAAO,IAAImJ,WAAW,CAACtI,KAAK,CAAC2H,KAAK,CAAC,CAAC;;EAEtJ,IAAIe,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,gBAAgB;EAEpB,IAAItB,SAAS,EAAE;IACbqB,YAAY,GAAG;MACbE,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAEnI,MAAM,CAAC4G;IACpB,CAAC;EACH;EAEA,IAAIC,aAAa,EAAE;IACjBkB,YAAY,GAAG;MACbK,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;IACH;IACA;;IAEA,IAAI,CAACzB,SAAS,EAAE;MACdqB,YAAY,GAAG;QACbE,SAAS,EAAE;MACb,CAAC;IACH;IAEAD,gBAAgB,GAAG;MACjBI,KAAK,EAAE,CAACrI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8G,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG9G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8G,CAAC;MAC9IwB,QAAQ,EAAE;IACZ,CAAC;EACH;EAEA,IAAIC,cAAc,GAAGjM,KAAK,CAACqF,WAAW,CAAC,UAAU2E,SAAS,EAAE+B,KAAK,EAAE;IACjE,IAAI9L,SAAS,CAACyI,YAAY,CAACwD,OAAO,CAAC,EAAE;MACnCtC,gBAAgB,CAAC,UAAUuC,MAAM,EAAE;QACjC,IAAIA,MAAM,CAAClC,GAAG,CAACD,SAAS,CAAC,KAAK+B,KAAK,EAAE;UACnC,IAAIK,SAAS,GAAG,IAAI3C,GAAG,CAAC0C,MAAM,CAAC;UAC/BC,SAAS,CAACC,GAAG,CAACrC,SAAS,EAAE+B,KAAK,CAAC;UAC/B,OAAOK,SAAS;QAClB;QAEA,OAAOD,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIG,eAAe,GAAGrL,cAAc,CAAC,IAAI,CAAC;IACtCsL,gBAAgB,GAAGzM,cAAc,CAACwM,eAAe,EAAE,CAAC,CAAC;IACrDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,SAASG,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACvC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAACD,UAAU,CAAC;IACpB,CAAC,MAAM,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;MAC3C;MACAC,MAAM,CAACD,UAAU,GAAGA,UAAU;IAChC;EACF;EAEA,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;MACnCJ,UAAU,GAAGG,KAAK,CAACH,UAAU;IACjC,IAAIK,KAAK,GAAGpJ,SAAS,KAAK,KAAK;IAC/B,IAAIqJ,gBAAgB,GAAG,OAAON,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGI,aAAa,CAACJ,UAAU;IAC7F,IAAIO,aAAa,GAAGH,aAAa,IAAIxK,mBAAmB;IAExD,IAAI,CAACkK,eAAe,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,KAAKS,aAAa,EAAE;MAC7D,IAAIC,kBAAkB;MAEtBX,eAAe,CAACU,aAAa,CAAC;MAC9BR,WAAW,CAACO,gBAAgB,EAAErE,eAAe,CAACsD,OAAO,CAAC;MACtDQ,WAAW,CAACO,gBAAgB,EAAEpE,aAAa,CAACqD,OAAO,CAAC;MACpDQ,WAAW,CAACO,gBAAgB,EAAElE,gBAAgB,CAACmD,OAAO,CAAC;MACvDQ,WAAW,CAACO,gBAAgB,EAAE,CAACE,kBAAkB,GAAGtC,SAAS,CAACqB,OAAO,MAAM,IAAI,IAAIiB,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,aAAa,CAAC;IAC/J;IAEA,IAAIL,aAAa,EAAE;MACjB,IAAIM,WAAW,GAAGN,aAAa,CAACM,WAAW;QACvCC,WAAW,GAAGP,aAAa,CAACO,WAAW,CAAC,CAAC;;MAE7C,IAAID,WAAW,KAAKC,WAAW,EAAE;QAC/BnE,aAAa,CAAC,KAAK,CAAC;QACpBI,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;MAEA,IAAIyD,KAAK,EAAE;QACT7D,aAAa,CAAC,CAAC8D,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;QAC5D/D,cAAc,CAAC,CAAC0D,gBAAgB,GAAG,CAAC,CAAC;MACvC,CAAC,MAAM;QACL9D,aAAa,CAAC8D,gBAAgB,GAAG,CAAC,CAAC;QACnC1D,cAAc,CAAC0D,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;MAC9D;IACF;EACF,CAAC;EAED,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIhD,aAAa,IAAI1B,aAAa,CAACqD,OAAO,EAAE;MAC1CW,QAAQ,CAAC;QACPE,aAAa,EAAElE,aAAa,CAACqD;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL/C,aAAa,CAAC,KAAK,CAAC;MACpBI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAIiE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAI1B,KAAK,GAAG0B,KAAK,CAAC1B,KAAK;IAEvB,IAAIA,KAAK,KAAK7D,cAAc,EAAE;MAC5BqF,eAAe,CAAC,CAAC;MACjBpF,iBAAiB,CAACO,YAAY,CAACwD,OAAO,GAAGxD,YAAY,CAACwD,OAAO,CAACwB,WAAW,GAAG3B,KAAK,CAAC;IACpF;EACF,CAAC,CAAC,CAAC;;EAGH,IAAI4B,OAAO,GAAG3N,KAAK,CAAC2I,MAAM,CAAC,KAAK,CAAC;EACjC3I,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1B;IACA;IACA,IAAID,OAAO,CAACzB,OAAO,EAAE;MACnBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChD,aAAa,EAAE/G,IAAI,EAAE+E,OAAO,CAAC3D,MAAM,CAAC,CAAC;EACzC5E,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1BD,OAAO,CAACzB,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAI2B,gBAAgB,GAAG7N,KAAK,CAACkH,QAAQ,CAAC,CAAC,CAAC;IACpC4G,iBAAiB,GAAGhO,cAAc,CAAC+N,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE3C,IAAIG,iBAAiB,GAAGjO,KAAK,CAACkH,QAAQ,CAAC,IAAI,CAAC;IACxCgH,iBAAiB,GAAGpO,cAAc,CAACmO,iBAAiB,EAAE,CAAC,CAAC;IACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG7ClO,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1B,IAAI/E,aAAa,CAACqD,OAAO,YAAYmC,OAAO,EAAE;MAC5CL,gBAAgB,CAACxN,sBAAsB,CAACqI,aAAa,CAACqD,OAAO,CAAC,CAACH,KAAK,CAAC;IACvE,CAAC,MAAM;MACLiC,gBAAgB,CAACxN,sBAAsB,CAACsI,sBAAsB,CAACoD,OAAO,CAAC,CAACH,KAAK,CAAC;IAChF;IAEAqC,gBAAgB,CAACjO,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERH,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1B,IAAItJ,aAAa,KAAK9B,cAAc,IAAIgC,YAAY,EAAE;MACpDA,YAAY,CAAC8J,IAAI,CAACpC,OAAO,GAAGrD,aAAa,CAACqD,OAAO;IACnD;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIqC,cAAc,GAAGnJ,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEvD,IAAIoJ,iBAAiB,GAAGxO,KAAK,CAACyF,OAAO,CAAC,YAAY;IAChD,IAAI9B,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF;IACA;;IAGA,IAAIgH,SAAS,EAAE;MACb,OAAO,CAACjH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8G,CAAC,MAAM,aAAa,GAAG,MAAM,GAAG,OAAO;IACxG;IAEA,IAAIH,SAAS,IAAIU,QAAQ,IAAIvC,cAAc,CAACxB,IAAI,CAAC,UAAUyH,KAAK,EAAE;MAChE,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EAAE;MACF,OAAO,OAAO;IAChB;IAEA,OAAO,MAAM;EACf,CAAC,EAAE,CAACrE,SAAS,EAAEM,SAAS,EAAEnC,cAAc,EAAE7E,WAAW,EAAEoH,QAAQ,CAAC,CAAC;EACjE,IAAI4D,cAAc,CAAC,CAAC;;EAEpB,IAAIC,WAAW,GAAG;IAChB1E,SAAS,EAAEA,SAAS;IACpB2E,UAAU,EAAErG,cAAc,CAAC5D,MAAM;IACjCwF,aAAa,EAAEA,aAAa;IAC5B/F,WAAW,EAAEA,WAAW;IACxBgG,SAAS,EAAEA,SAAS;IACpB3G,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;;EAEH,IAAIoL,SAAS,GAAG9O,KAAK,CAACyF,OAAO,CAAC,YAAY;IACxC,IAAId,OAAO,EAAE;MACX,OAAO,IAAI;IACb;IAEA,IAAI,OAAOR,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,CAAC,CAAC;IACpB;IAEA,OAAOA,SAAS;EAClB,CAAC,EAAE,CAACQ,OAAO,EAAER,SAAS,CAAC,CAAC,CAAC,CAAC;;EAE1B,IAAI4K,SAAS,GAAG,aAAa/O,KAAK,CAACgP,aAAa,CAAClO,IAAI,EAAE;IACrD0C,IAAI,EAAEkB,UAAU;IAChBuK,kBAAkB,EAAE5E,SAAS,IAAIE,aAAa,IAAIQ,QAAQ;IAC1D1C,YAAY,EAAEf,kBAAkB;IAChChB,aAAa,EAAEA,aAAa;IAC5Bd,SAAS,EAAEA,SAAS;IACpBpB,KAAK,EAAEA,KAAK;IACZ0K,SAAS,EAAEA,SAAS;IACpBrI,kBAAkB,EAAEG;EACtB,CAAC,CAAC;EACF,IAAIsI,YAAY,GAAG,aAAalP,KAAK,CAACgP,aAAa,CAACzN,QAAQ,EAAE;IAC5D2I,SAAS,EAAE1B,cAAc,CAACuB,GAAG,CAAC,UAAUoF,KAAK,EAAE;MAC7C,IAAIpD,KAAK,GAAGoD,KAAK,CAACpD,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACFxD,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,IAAI4G,mBAAmB,GAAGhK,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;EAEhD,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOqK,mBAAmB,KAAK,UAAU,IAAIzK,OAAO,IAAI,CAAC0F,SAAS,EAAE;IAC/G/J,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;EACnF;EAEA,IAAI+J,SAAS,IAAIU,QAAQ,EAAE;IACzB;IACA,IAAIsE,WAAW;IAEf,IAAI,OAAOD,mBAAmB,KAAK,UAAU,EAAE;MAC7CC,WAAW,GAAGD,mBAAmB,CAAC1K,UAAU,EAAE;QAC5CqJ,aAAa,EAAEA,aAAa;QAC5BuB,GAAG,EAAEzG,aAAa;QAClBgE,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF+B,WAAW,CAAC1E,SAAS,GAAG1B,cAAc,CAACuB,GAAG,CAAC,UAAUwF,KAAK,EAAEC,KAAK,EAAE;QACjE,IAAIzD,KAAK,GAAGwD,KAAK,CAACxD,KAAK;QACvB,IAAI0D,QAAQ,GAAGD,KAAK,KAAKjH,OAAO,CAAC3D,MAAM,GAAG,CAAC,GAAGmH,KAAK,GAAGgC,aAAa,GAAGhC,KAAK;QAE3E,IAAI,OAAO0D,QAAQ,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC,EAAE;UAC3D,OAAOA,QAAQ;QACjB;QAEAnP,OAAO,CAAC,KAAK,EAAE,8FAA8F,CAAC;QAC9G,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL+O,WAAW,GAAG,aAAarP,KAAK,CAACgP,aAAa,CAAC,KAAK,EAAE;QACpDzL,KAAK,EAAE3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6L,YAAY,CAAC,EAAEC,YAAY,CAAC;QACnEmB,QAAQ,EAAEA,QAAQ;QAClByC,GAAG,EAAEzG,aAAa;QAClBxF,SAAS,EAAEjD,UAAU,CAAC,EAAE,CAAC+E,MAAM,CAAC/B,SAAS,EAAE,OAAO,CAAC;MACrD,CAAC,EAAE,aAAapD,KAAK,CAACgP,aAAa,CAACT,cAAc,EAAE;QAClDhL,KAAK,EAAE3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+L,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DhI,WAAW,EAAE6K;QACf,CAAC;MACH,CAAC,EAAEU,YAAY,EAAEH,SAAS,EAAE,CAACzD,SAAS,IAAID,WAAW,IAAI,aAAarL,KAAK,CAACgP,aAAa,CAACtN,MAAM,EAAE;QAChG0I,aAAa,EAAEA,aAAa;QAC5B5B,cAAc,EAAEA;MAClB,CAAC,EAAE6C,WAAW,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC;;IAGF,IAAIuE,gBAAgB,GAAGhQ,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC/DiQ,MAAM,EAAE,CAACnL,UAAU,CAACE,MAAM;MAC1BkL,gBAAgB,EAAEvF,aAAa,IAAI7G,MAAM,CAAC8G,CAAC,KAAK;IAClD,CAAC,EAAEoE,WAAW,CAAC,EAAEnG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACnC7E,SAAS,EAAEA,SAAS;MACpBuH,eAAe,EAAEA,eAAe;MAChC0B,QAAQ,EAAEA;IACZ,CAAC,CAAC;IAEF8B,cAAc,GAAG,aAAa3O,KAAK,CAACgP,aAAa,CAAChP,KAAK,CAAC+P,QAAQ,EAAE,IAAI,EAAE9L,UAAU,KAAK,KAAK,IAAI,aAAajE,KAAK,CAACgP,aAAa,CAAC/M,WAAW,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEiQ,gBAAgB,EAAE;MAC3KI,eAAe,EAAEhF,YAAY;MAC7B3H,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,SAAS,CAAC;MAC1CkM,GAAG,EAAE1G;IACP,CAAC,CAAC,EAAE,UAAUqH,oBAAoB,EAAE;MAClC,OAAO,aAAajQ,KAAK,CAACgP,aAAa,CAAChP,KAAK,CAAC+P,QAAQ,EAAE,IAAI,EAAE,aAAa/P,KAAK,CAACgP,aAAa,CAACrO,MAAM,EAAEsP,oBAAoB,CAAC,EAAE3E,SAAS,KAAK,KAAK,IAAI,aAAatL,KAAK,CAACgP,aAAa,CAACtN,MAAM,EAAEuO,oBAAoB,EAAE5E,WAAW,CAAC,CAAC;IACnO,CAAC,CAAC,EAAEgE,WAAW,EAAE/D,SAAS,IAAIA,SAAS,KAAK,KAAK,IAAI,aAAatL,KAAK,CAACgP,aAAa,CAAC/M,WAAW,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEiQ,gBAAgB,EAAE;MAChIM,kBAAkB,EAAEjF,aAAa;MACjC5H,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC;MAC3CkM,GAAG,EAAEvG;IACP,CAAC,CAAC,EAAE,UAAUkH,oBAAoB,EAAE;MAClC,OAAO,aAAajQ,KAAK,CAACgP,aAAa,CAACtN,MAAM,EAAEuO,oBAAoB,EAAE5E,WAAW,CAAC;IACpF,CAAC,CAAC,EAAEN,QAAQ,IAAI,aAAa/K,KAAK,CAACgP,aAAa,CAACjN,eAAe,EAAE;MAChEuN,GAAG,EAAEzE,SAAS;MACdK,YAAY,EAAEA,YAAY;MAC1BrC,aAAa,EAAEA,aAAa;MAC5BgE,QAAQ,EAAEA,QAAQ;MAClBzB,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL;IACAuD,cAAc,GAAG,aAAa3O,KAAK,CAACgP,aAAa,CAAC,KAAK,EAAE;MACvDzL,KAAK,EAAE3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6L,YAAY,CAAC,EAAEC,YAAY,CAAC;MACnErI,SAAS,EAAEjD,UAAU,CAAC,EAAE,CAAC+E,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC,CAAC;MACvDyJ,QAAQ,EAAEA,QAAQ;MAClByC,GAAG,EAAEzG;IACP,CAAC,EAAE,aAAa7I,KAAK,CAACgP,aAAa,CAACT,cAAc,EAAE;MAClDhL,KAAK,EAAE3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+L,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5DhI,WAAW,EAAE6K;MACf,CAAC;IACH,CAAC,EAAEU,YAAY,EAAEjL,UAAU,KAAK,KAAK,IAAI,aAAajE,KAAK,CAACgP,aAAa,CAACrO,MAAM,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEiP,WAAW,EAAEnG,aAAa,CAAC,CAAC,EAAEsG,SAAS,EAAE1D,WAAW,IAAI,aAAarL,KAAK,CAACgP,aAAa,CAACtN,MAAM,EAAE;MAC9L0I,aAAa,EAAEA,aAAa;MAC5B5B,cAAc,EAAEA;IAClB,CAAC,EAAE6C,WAAW,CAAC,CAAC,CAAC;EACnB;EAEA,IAAI8E,SAAS,GAAGjQ,SAAS,CAAC6C,KAAK,EAAE;IAC/BqN,IAAI,EAAE,IAAI;IACV5M,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI6M,SAAS,GAAG,aAAarQ,KAAK,CAACgP,aAAa,CAAC,KAAK,EAAErP,QAAQ,CAAC;IAC/D0D,SAAS,EAAEjD,UAAU,CAACgD,SAAS,EAAEC,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAEzD,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,KAAK,KAAK,CAAC,EAAElE,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,YAAY,CAAC,EAAE8F,UAAU,CAAC,EAAExJ,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,aAAa,CAAC,EAAEkG,WAAW,CAAC,EAAE5J,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEO,WAAW,KAAK,OAAO,CAAC,EAAEjE,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEiH,SAAS,CAAC,EAAE3K,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEuH,SAAS,CAAC,EAAEjL,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,oBAAoB,CAAC,EAAEmH,aAAa,CAAC,EAAE7K,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEoF,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,CAACkC,KAAK,CAAC,EAAEhL,eAAe,CAACyD,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,gBAAgB,CAAC,EAAEoF,cAAc,CAACA,cAAc,CAAC5D,MAAM,GAAG,CAAC,CAAC,IAAI4D,cAAc,CAACA,cAAc,CAAC5D,MAAM,GAAG,CAAC,CAAC,CAAC8F,KAAK,KAAK,OAAO,CAAC,EAAEvH,WAAW,CAAC,CAAC;IACr7BI,KAAK,EAAEA,KAAK;IACZS,EAAE,EAAEA,EAAE;IACNsL,GAAG,EAAE5G;EACP,CAAC,EAAEyH,SAAS,CAAC,EAAE,aAAanQ,KAAK,CAACgP,aAAa,CAACvM,gBAAgB,EAAE;IAChEO,QAAQ,EAAEkG,UAAU;IACpBjG,SAAS,EAAEqG,WAAW;IACtBvG,KAAK,EAAEnD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDqH,aAAa,EAAEA,aAAa;MAC5B9C,kBAAkB,EAAEA;IACtB,CAAC;EACH,CAAC,EAAEzD,KAAK,IAAI,aAAa7D,KAAK,CAACgP,aAAa,CAACvN,KAAK,EAAE;IAClD4B,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAES,KAAK,CAACa,UAAU,CAAC,CAAC,EAAE,aAAa1E,KAAK,CAACgP,aAAa,CAAC,KAAK,EAAE;IAC7DM,GAAG,EAAExG,sBAAsB;IAC3BzF,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAEuL,cAAc,CAAC,EAAE7K,MAAM,IAAI,aAAa9D,KAAK,CAACgP,aAAa,CAACvN,KAAK,EAAE;IACpE4B,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEU,MAAM,CAACY,UAAU,CAAC,CAAC,CAAC,CAAC;EAExB,IAAI6F,aAAa,EAAE;IACjB8F,SAAS,GAAG,aAAarQ,KAAK,CAACgP,aAAa,CAACzO,cAAc,EAAE;MAC3D+P,QAAQ,EAAE9C;IACZ,CAAC,EAAE6C,SAAS,CAAC;EACf;EAEA,IAAIE,iBAAiB,GAAGvQ,KAAK,CAACyF,OAAO,CAAC,YAAY;IAChD,OAAO;MACLrC,SAAS,EAAEA,SAAS;MACpBgC,YAAY,EAAEA,YAAY;MAC1B2I,aAAa,EAAEA,aAAa;MAC5BnK,SAAS,EAAEA,SAAS;MACpB4M,aAAa,EAAEhI,cAAc,CAACuB,GAAG,CAAC,UAAU0G,CAAC,EAAEC,QAAQ,EAAE;QACvD,OAAO5O,gBAAgB,CAAC4O,QAAQ,EAAEA,QAAQ,EAAElI,cAAc,EAAE4B,aAAa,EAAExG,SAAS,CAAC;MACvF,CAAC,CAAC;MACFmH,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,EAAE,CAAC3H,SAAS,EAAEgC,YAAY,EAAE2I,aAAa,EAAEnK,SAAS,EAAE4E,cAAc,EAAE4B,aAAa,EAAEW,QAAQ,CAAC,CAAC;EAChG,IAAI4F,gBAAgB,GAAG3Q,KAAK,CAACyF,OAAO,CAAC,YAAY;IAC/C,OAAO7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6I,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzD9E,WAAW,EAAE6K,iBAAiB;MAC9BlL,YAAY,EAAEA,YAAY;MAC1BkD,oBAAoB,EAAEA,oBAAoB;MAC1CX,UAAU,EAAEc,gBAAgB;MAC5BE,cAAc,EAAEA,cAAc;MAC9BR,gBAAgB,EAAEA,gBAAgB;MAClCJ,iBAAiB,EAAEA,iBAAiB;MACpCuB,eAAe,EAAEA,eAAe;MAChCjB,qBAAqB,EAAEA,qBAAqB;MAC5CG,UAAU,EAAEA,UAAU;MACtBkK,mBAAmB,EAAEnI,aAAa,CAACD,cAAc,CAACqI,KAAK,CAAC,UAAUC,GAAG,EAAE;QACrE,OAAOA,GAAG,CAACpG,KAAK,KAAK,MAAM;MAC7B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,aAAa,EAAE+F,iBAAiB,EAAElL,YAAY,EAAEkD,oBAAoB,EAAEG,gBAAgB,EAAEE,cAAc,EAAER,gBAAgB,EAAEJ,iBAAiB,EAAEuB,eAAe,EAAEjB,qBAAqB,EAAEG,UAAU,CAAC,CAAC;EACrM,IAAIqK,uBAAuB,GAAG/Q,KAAK,CAACyF,OAAO,CAAC,YAAY;IACtD,OAAO;MACLyC,cAAc,EAAEA,cAAc;MAC9BmC,SAAS,EAAEA,SAAS;MACpBM,SAAS,EAAEA,SAAS;MACpBJ,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,EAAE,CAACrC,cAAc,EAAEmC,SAAS,EAAEM,SAAS,EAAEJ,aAAa,CAAC,CAAC;EACzD,IAAIyG,kBAAkB,GAAGhR,KAAK,CAACyF,OAAO,CAAC,YAAY;IACjD,OAAO;MACLwG,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,OAAO,aAAajM,KAAK,CAACgP,aAAa,CAAC7M,aAAa,CAAC8O,QAAQ,EAAE;IAC9DC,KAAK,EAAE/C;EACT,CAAC,EAAE,aAAanO,KAAK,CAACgP,aAAa,CAACpO,YAAY,CAACqQ,QAAQ,EAAE;IACzDC,KAAK,EAAEX;EACT,CAAC,EAAE,aAAavQ,KAAK,CAACgP,aAAa,CAACnO,WAAW,CAACoQ,QAAQ,EAAE;IACxDC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAa3Q,KAAK,CAACgP,aAAa,CAAC5M,kBAAkB,CAAC6O,QAAQ,EAAE;IAC/DC,KAAK,EAAEH;EACT,CAAC,EAAE,aAAa/Q,KAAK,CAACgP,aAAa,CAAC3N,aAAa,CAAC4P,QAAQ,EAAE;IAC1DC,KAAK,EAAEF;EACT,CAAC,EAAEX,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AAEAnN,KAAK,CAACb,aAAa,GAAGA,aAAa;AACnCa,KAAK,CAACxC,MAAM,GAAGA,MAAM;AACrBwC,KAAK,CAACzC,WAAW,GAAGA,WAAW;AAC/ByC,KAAK,CAAChB,OAAO,GAAGP,gBAAgB;AAChCuB,KAAK,CAACiO,YAAY,GAAG;EACnB1N,MAAM,EAAE,KAAK;EACbL,SAAS,EAAE,UAAU;EACrBe,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,SAAS;EAClB;AACF,CAAC;AACD,eAAejB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}