{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nimport { generate, getSecondaryColor, isIconDefinition, warning, useInsertStyles } from '../utils';\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n    secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\nfunction getTwoToneColors() {\n  return _objectSpread({}, twoToneColorPalette);\n}\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n    className = props.className,\n    onClick = props.onClick,\n    style = props.style,\n    primaryColor = props.primaryColor,\n    secondaryColor = props.secondaryColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var colors = twoToneColorPalette;\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)\n    };\n  }\n  useInsertStyles();\n  warning(isIconDefinition(icon), \"icon should be icon definiton, but got \".concat(icon));\n  if (!isIconDefinition(icon)) {\n    return null;\n  }\n  var target = icon;\n  if (target && typeof target.icon === 'function') {\n    target = _objectSpread(_objectSpread({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n  return generate(target.icon, \"svg-\".concat(target.name), _objectSpread({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps));\n};\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nexport default IconBase;", "map": {"version": 3, "names": ["_objectWithoutProperties", "_objectSpread", "_excluded", "generate", "getSecondaryColor", "isIconDefinition", "warning", "useInsertStyles", "twoToneColorPalette", "primaryColor", "secondaryColor", "calculated", "setTwoToneColors", "_ref", "getTwoToneColors", "IconBase", "props", "icon", "className", "onClick", "style", "restProps", "colors", "concat", "target", "name", "width", "height", "fill", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/components/IconBase.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nimport { generate, getSecondaryColor, isIconDefinition, warning, useInsertStyles } from '../utils';\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n    secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\nfunction getTwoToneColors() {\n  return _objectSpread({}, twoToneColorPalette);\n}\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n    className = props.className,\n    onClick = props.onClick,\n    style = props.style,\n    primaryColor = props.primaryColor,\n    secondaryColor = props.secondaryColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var colors = twoToneColorPalette;\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)\n    };\n  }\n  useInsertStyles();\n  warning(isIconDefinition(icon), \"icon should be icon definiton, but got \".concat(icon));\n  if (!isIconDefinition(icon)) {\n    return null;\n  }\n  var target = icon;\n  if (target && typeof target.icon === 'function') {\n    target = _objectSpread(_objectSpread({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n  return generate(target.icon, \"svg-\".concat(target.name), _objectSpread({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps));\n};\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nexport default IconBase;"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,CAAC;AAC3F,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,eAAe,QAAQ,UAAU;AAClG,IAAIC,mBAAmB,GAAG;EACxBC,YAAY,EAAE,MAAM;EACpBC,cAAc,EAAE,SAAS;EACzBC,UAAU,EAAE;AACd,CAAC;AACD,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAIJ,YAAY,GAAGI,IAAI,CAACJ,YAAY;IAClCC,cAAc,GAAGG,IAAI,CAACH,cAAc;EACtCF,mBAAmB,CAACC,YAAY,GAAGA,YAAY;EAC/CD,mBAAmB,CAACE,cAAc,GAAGA,cAAc,IAAIN,iBAAiB,CAACK,YAAY,CAAC;EACtFD,mBAAmB,CAACG,UAAU,GAAG,CAAC,CAACD,cAAc;AACnD;AACA,SAASI,gBAAgBA,CAAA,EAAG;EAC1B,OAAOb,aAAa,CAAC,CAAC,CAAC,EAAEO,mBAAmB,CAAC;AAC/C;AACA,IAAIO,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBX,YAAY,GAAGO,KAAK,CAACP,YAAY;IACjCC,cAAc,GAAGM,KAAK,CAACN,cAAc;IACrCW,SAAS,GAAGrB,wBAAwB,CAACgB,KAAK,EAAEd,SAAS,CAAC;EACxD,IAAIoB,MAAM,GAAGd,mBAAmB;EAChC,IAAIC,YAAY,EAAE;IAChBa,MAAM,GAAG;MACPb,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA,cAAc,IAAIN,iBAAiB,CAACK,YAAY;IAClE,CAAC;EACH;EACAF,eAAe,CAAC,CAAC;EACjBD,OAAO,CAACD,gBAAgB,CAACY,IAAI,CAAC,EAAE,yCAAyC,CAACM,MAAM,CAACN,IAAI,CAAC,CAAC;EACvF,IAAI,CAACZ,gBAAgB,CAACY,IAAI,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAIO,MAAM,GAAGP,IAAI;EACjB,IAAIO,MAAM,IAAI,OAAOA,MAAM,CAACP,IAAI,KAAK,UAAU,EAAE;IAC/CO,MAAM,GAAGvB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACpDP,IAAI,EAAEO,MAAM,CAACP,IAAI,CAACK,MAAM,CAACb,YAAY,EAAEa,MAAM,CAACZ,cAAc;IAC9D,CAAC,CAAC;EACJ;EACA,OAAOP,QAAQ,CAACqB,MAAM,CAACP,IAAI,EAAE,MAAM,CAACM,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,EAAExB,aAAa,CAAC;IACrEiB,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBC,KAAK,EAAEA,KAAK;IACZ,WAAW,EAAEI,MAAM,CAACC,IAAI;IACxBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE;EACjB,CAAC,EAAEP,SAAS,CAAC,CAAC;AAChB,CAAC;AACDN,QAAQ,CAACc,WAAW,GAAG,WAAW;AAClCd,QAAQ,CAACD,gBAAgB,GAAGA,gBAAgB;AAC5CC,QAAQ,CAACH,gBAAgB,GAAGA,gBAAgB;AAC5C,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}