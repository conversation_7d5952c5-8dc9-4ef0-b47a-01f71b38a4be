{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = nice;\nvar _ticks = require(\"./ticks.js\");\nfunction nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = (0, _ticks.tickIncrement)(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "nice", "_ticks", "require", "start", "stop", "count", "prestep", "step", "tickIncrement", "isFinite", "Math", "floor", "ceil"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/nice.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = nice;\n\nvar _ticks = require(\"./ticks.js\");\n\nfunction nice(start, stop, count) {\n  let prestep;\n\n  while (true) {\n    const step = (0, _ticks.tickIncrement)(start, stop, count);\n\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n\n    prestep = step;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AAElC,SAASF,IAAIA,CAACG,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAChC,IAAIC,OAAO;EAEX,OAAO,IAAI,EAAE;IACX,MAAMC,IAAI,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACO,aAAa,EAAEL,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IAE1D,IAAIE,IAAI,KAAKD,OAAO,IAAIC,IAAI,KAAK,CAAC,IAAI,CAACE,QAAQ,CAACF,IAAI,CAAC,EAAE;MACrD,OAAO,CAACJ,KAAK,EAAEC,IAAI,CAAC;IACtB,CAAC,MAAM,IAAIG,IAAI,GAAG,CAAC,EAAE;MACnBJ,KAAK,GAAGO,IAAI,CAACC,KAAK,CAACR,KAAK,GAAGI,IAAI,CAAC,GAAGA,IAAI;MACvCH,IAAI,GAAGM,IAAI,CAACE,IAAI,CAACR,IAAI,GAAGG,IAAI,CAAC,GAAGA,IAAI;IACtC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;MACnBJ,KAAK,GAAGO,IAAI,CAACE,IAAI,CAACT,KAAK,GAAGI,IAAI,CAAC,GAAGA,IAAI;MACtCH,IAAI,GAAGM,IAAI,CAACC,KAAK,CAACP,IAAI,GAAGG,IAAI,CAAC,GAAGA,IAAI;IACvC;IAEAD,OAAO,GAAGC,IAAI;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}