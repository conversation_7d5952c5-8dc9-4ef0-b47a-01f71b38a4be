{"ast": null, "code": "import * as React from 'react';\nvar AnchorContext = /*#__PURE__*/React.createContext(undefined);\nexport default AnchorContext;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createContext", "undefined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/anchor/context.js"], "sourcesContent": ["import * as React from 'react';\nvar AnchorContext = /*#__PURE__*/React.createContext(undefined);\nexport default AnchorContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC/D,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}