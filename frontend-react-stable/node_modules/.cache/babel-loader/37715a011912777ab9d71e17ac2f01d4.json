{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nfunction _default(series) {\n  var n = series.length,\n    o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "series", "n", "length", "o", "Array"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/order/none.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nfunction _default(series) {\n  var n = series.length,\n      o = new Array(n);\n\n  while (--n >= 0) o[n] = n;\n\n  return o;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;IACjBC,CAAC,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;EAEpB,OAAO,EAAEA,CAAC,IAAI,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,GAAGA,CAAC;EAEzB,OAAOE,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}