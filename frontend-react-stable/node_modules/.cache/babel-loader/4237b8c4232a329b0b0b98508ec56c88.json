{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport { SubMenu as RcSubMenu, useFullPath } from 'rc-menu';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from '../_util/reactNode';\nimport MenuContext from './MenuContext';\nfunction SubMenu(props) {\n  var _a;\n  var popupClassName = props.popupClassName,\n    icon = props.icon,\n    title = props.title,\n    theme = props.theme;\n  var context = React.useContext(MenuContext);\n  var prefixCls = context.prefixCls,\n    inlineCollapsed = context.inlineCollapsed,\n    antdMenuTheme = context.antdMenuTheme;\n  var parentPath = useFullPath();\n  var titleNode;\n  if (!icon) {\n    titleNode = inlineCollapsed && !parentPath.length && title && typeof title === 'string' ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-inline-collapsed-noicon\")\n    }, title.charAt(0)) : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-title-content\")\n    }, title);\n  } else {\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    var titleIsSpan = isValidElement(title) && title.type === 'span';\n    titleNode = /*#__PURE__*/React.createElement(React.Fragment, null, cloneElement(icon, {\n      className: classNames(isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', \"\".concat(prefixCls, \"-item-icon\"))\n    }), titleIsSpan ? title : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-title-content\")\n    }, title));\n  }\n  var contextValue = React.useMemo(function () {\n    return _extends(_extends({}, context), {\n      firstLevel: false\n    });\n  }, [context]);\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(RcSubMenu, _extends({}, omit(props, ['icon']), {\n    title: titleNode,\n    popupClassName: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(theme || antdMenuTheme), popupClassName)\n  })));\n}\nexport default SubMenu;", "map": {"version": 3, "names": ["_extends", "classNames", "SubMenu", "RcSubMenu", "useFullPath", "omit", "React", "cloneElement", "isValidElement", "MenuContext", "props", "_a", "popupClassName", "icon", "title", "theme", "context", "useContext", "prefixCls", "inlineCollapsed", "antdMenuTheme", "parentPath", "titleNode", "length", "createElement", "className", "concat", "char<PERSON>t", "titleIsSpan", "type", "Fragment", "contextValue", "useMemo", "firstLevel", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/menu/SubMenu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport { SubMenu as RcSubMenu, useFullPath } from 'rc-menu';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from '../_util/reactNode';\nimport MenuContext from './MenuContext';\nfunction SubMenu(props) {\n  var _a;\n  var popupClassName = props.popupClassName,\n    icon = props.icon,\n    title = props.title,\n    theme = props.theme;\n  var context = React.useContext(MenuContext);\n  var prefixCls = context.prefixCls,\n    inlineCollapsed = context.inlineCollapsed,\n    antdMenuTheme = context.antdMenuTheme;\n  var parentPath = useFullPath();\n  var titleNode;\n  if (!icon) {\n    titleNode = inlineCollapsed && !parentPath.length && title && typeof title === 'string' ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-inline-collapsed-noicon\")\n    }, title.charAt(0)) : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-title-content\")\n    }, title);\n  } else {\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    var titleIsSpan = isValidElement(title) && title.type === 'span';\n    titleNode = /*#__PURE__*/React.createElement(React.Fragment, null, cloneElement(icon, {\n      className: classNames(isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', \"\".concat(prefixCls, \"-item-icon\"))\n    }), titleIsSpan ? title : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-title-content\")\n    }, title));\n  }\n  var contextValue = React.useMemo(function () {\n    return _extends(_extends({}, context), {\n      firstLevel: false\n    });\n  }, [context]);\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(RcSubMenu, _extends({}, omit(props, ['icon']), {\n    title: titleNode,\n    popupClassName: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(theme || antdMenuTheme), popupClassName)\n  })));\n}\nexport default SubMenu;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,IAAIC,SAAS,EAAEC,WAAW,QAAQ,SAAS;AAC3D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AACjE,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASP,OAAOA,CAACQ,KAAK,EAAE;EACtB,IAAIC,EAAE;EACN,IAAIC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACvCC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;EACrB,IAAIC,OAAO,GAAGV,KAAK,CAACW,UAAU,CAACR,WAAW,CAAC;EAC3C,IAAIS,SAAS,GAAGF,OAAO,CAACE,SAAS;IAC/BC,eAAe,GAAGH,OAAO,CAACG,eAAe;IACzCC,aAAa,GAAGJ,OAAO,CAACI,aAAa;EACvC,IAAIC,UAAU,GAAGjB,WAAW,CAAC,CAAC;EAC9B,IAAIkB,SAAS;EACb,IAAI,CAACT,IAAI,EAAE;IACTS,SAAS,GAAGH,eAAe,IAAI,CAACE,UAAU,CAACE,MAAM,IAAIT,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAG,aAAaR,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;MAChIC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,0BAA0B;IAC5D,CAAC,EAAEJ,KAAK,CAACa,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,aAAarB,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;MAC7DC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,gBAAgB;IAClD,CAAC,EAAEJ,KAAK,CAAC;EACX,CAAC,MAAM;IACL;IACA;IACA,IAAIc,WAAW,GAAGpB,cAAc,CAACM,KAAK,CAAC,IAAIA,KAAK,CAACe,IAAI,KAAK,MAAM;IAChEP,SAAS,GAAG,aAAahB,KAAK,CAACkB,aAAa,CAAClB,KAAK,CAACwB,QAAQ,EAAE,IAAI,EAAEvB,YAAY,CAACM,IAAI,EAAE;MACpFY,SAAS,EAAExB,UAAU,CAACO,cAAc,CAACK,IAAI,CAAC,GAAG,CAACF,EAAE,GAAGE,IAAI,CAACH,KAAK,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,SAAS,GAAG,EAAE,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,YAAY,CAAC;IAC3J,CAAC,CAAC,EAAEU,WAAW,GAAGd,KAAK,GAAG,aAAaR,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;MACjEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,gBAAgB;IAClD,CAAC,EAAEJ,KAAK,CAAC,CAAC;EACZ;EACA,IAAIiB,YAAY,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,YAAY;IAC3C,OAAOhC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgB,OAAO,CAAC,EAAE;MACrCiB,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;EACb,OAAO,aAAaV,KAAK,CAACkB,aAAa,CAACf,WAAW,CAACyB,QAAQ,EAAE;IAC5DC,KAAK,EAAEJ;EACT,CAAC,EAAE,aAAazB,KAAK,CAACkB,aAAa,CAACrB,SAAS,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,IAAI,CAACK,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;IACjFI,KAAK,EAAEQ,SAAS;IAChBV,cAAc,EAAEX,UAAU,CAACiB,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACR,SAAS,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACX,KAAK,IAAIK,aAAa,CAAC,EAAER,cAAc;EAChH,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAeV,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}