{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"mobile\"];\nimport * as React from 'react';\nimport { useState, useEffect } from 'react';\nimport isMobile from \"rc-util/es/isMobile\";\nimport Mask from \"./Mask\";\nimport PopupInner from \"./PopupInner\";\nimport MobilePopupInner from \"./MobilePopupInner\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var visible = _ref.visible,\n    mobile = _ref.mobile,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useState = useState(visible),\n    _useState2 = _slicedToArray(_useState, 2),\n    innerVisible = _useState2[0],\n    serInnerVisible = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    inMobile = _useState4[0],\n    setInMobile = _useState4[1];\n  var cloneProps = _objectSpread(_objectSpread({}, props), {}, {\n    visible: innerVisible\n  }); // We check mobile in visible changed here.\n  // And this also delay set `innerVisible` to avoid popup component render flash\n\n  useEffect(function () {\n    serInnerVisible(visible);\n    if (visible && mobile) {\n      setInMobile(isMobile());\n    }\n  }, [visible, mobile]);\n  var popupNode = inMobile ? /*#__PURE__*/React.createElement(MobilePopupInner, _extends({}, cloneProps, {\n    mobile: mobile,\n    ref: ref\n  })) : /*#__PURE__*/React.createElement(PopupInner, _extends({}, cloneProps, {\n    ref: ref\n  })); // We can use fragment directly but this may failed some selector usage. Keep as origin logic\n\n  return /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(Mask, cloneProps), popupNode);\n});\nPopup.displayName = 'Popup';\nexport default Popup;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useState", "useEffect", "isMobile", "Mask", "PopupInner", "MobilePopupInner", "Popup", "forwardRef", "_ref", "ref", "visible", "mobile", "props", "_useState", "_useState2", "innerVisible", "serInnerVisible", "_useState3", "_useState4", "inMobile", "setInMobile", "cloneProps", "popupNode", "createElement", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/Popup/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"mobile\"];\nimport * as React from 'react';\nimport { useState, useEffect } from 'react';\nimport isMobile from \"rc-util/es/isMobile\";\nimport Mask from \"./Mask\";\nimport PopupInner from \"./PopupInner\";\nimport MobilePopupInner from \"./MobilePopupInner\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var visible = _ref.visible,\n      mobile = _ref.mobile,\n      props = _objectWithoutProperties(_ref, _excluded);\n\n  var _useState = useState(visible),\n      _useState2 = _slicedToArray(_useState, 2),\n      innerVisible = _useState2[0],\n      serInnerVisible = _useState2[1];\n\n  var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      inMobile = _useState4[0],\n      setInMobile = _useState4[1];\n\n  var cloneProps = _objectSpread(_objectSpread({}, props), {}, {\n    visible: innerVisible\n  }); // We check mobile in visible changed here.\n  // And this also delay set `innerVisible` to avoid popup component render flash\n\n\n  useEffect(function () {\n    serInnerVisible(visible);\n\n    if (visible && mobile) {\n      setInMobile(isMobile());\n    }\n  }, [visible, mobile]);\n  var popupNode = inMobile ? /*#__PURE__*/React.createElement(MobilePopupInner, _extends({}, cloneProps, {\n    mobile: mobile,\n    ref: ref\n  })) : /*#__PURE__*/React.createElement(PopupInner, _extends({}, cloneProps, {\n    ref: ref\n  })); // We can use fragment directly but this may failed some selector usage. Keep as origin logic\n\n  return /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(Mask, cloneProps), popupNode);\n});\nPopup.displayName = 'Popup';\nexport default Popup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,IAAIC,KAAK,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EAC7D,IAAIC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,KAAK,GAAGf,wBAAwB,CAACW,IAAI,EAAEV,SAAS,CAAC;EAErD,IAAIe,SAAS,GAAGb,QAAQ,CAACU,OAAO,CAAC;IAC7BI,UAAU,GAAGlB,cAAc,CAACiB,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAGjB,QAAQ,CAAC,KAAK,CAAC;IAC5BkB,UAAU,GAAGtB,cAAc,CAACqB,UAAU,EAAE,CAAC,CAAC;IAC1CE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,WAAW,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE/B,IAAIG,UAAU,GAAG1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3DF,OAAO,EAAEK;EACX,CAAC,CAAC,CAAC,CAAC;EACJ;;EAGAd,SAAS,CAAC,YAAY;IACpBe,eAAe,CAACN,OAAO,CAAC;IAExB,IAAIA,OAAO,IAAIC,MAAM,EAAE;MACrBS,WAAW,CAAClB,QAAQ,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACQ,OAAO,EAAEC,MAAM,CAAC,CAAC;EACrB,IAAIW,SAAS,GAAGH,QAAQ,GAAG,aAAapB,KAAK,CAACwB,aAAa,CAAClB,gBAAgB,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAE2B,UAAU,EAAE;IACrGV,MAAM,EAAEA,MAAM;IACdF,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,GAAG,aAAaV,KAAK,CAACwB,aAAa,CAACnB,UAAU,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAE2B,UAAU,EAAE;IAC1EZ,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEL,OAAO,aAAaV,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,aAAaxB,KAAK,CAACwB,aAAa,CAACpB,IAAI,EAAEkB,UAAU,CAAC,EAAEC,SAAS,CAAC;AACrH,CAAC,CAAC;AACFhB,KAAK,CAACkB,WAAW,GAAG,OAAO;AAC3B,eAAelB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}