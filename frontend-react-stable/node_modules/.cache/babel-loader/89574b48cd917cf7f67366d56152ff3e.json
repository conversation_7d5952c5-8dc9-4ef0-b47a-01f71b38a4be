{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonButton = function SkeletonButton(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    _props$block = props.block,\n    block = _props$block === void 0 ? false : _props$block,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-button\"),\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonButton;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "omit", "React", "ConfigContext", "Element", "SkeletonButton", "props", "_classNames", "customizePrefixCls", "prefixCls", "className", "active", "_props$block", "block", "_props$size", "size", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Button.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonButton = function SkeletonButton(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    _props$block = props.block,\n    block = _props$block === void 0 ? false : _props$block,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-button\"),\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,WAAW;EACf,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACtCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,YAAY,GAAGN,KAAK,CAACO,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IACtDE,WAAW,GAAGR,KAAK,CAACS,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,WAAW;EACzD,IAAIE,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACd,aAAa,CAAC;IACrDe,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIT,SAAS,GAAGS,YAAY,CAAC,UAAU,EAAEV,kBAAkB,CAAC;EAC5D,IAAIW,UAAU,GAAGlB,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,IAAIc,GAAG,GAAGpB,UAAU,CAACS,SAAS,EAAE,EAAE,CAACY,MAAM,CAACZ,SAAS,EAAE,UAAU,CAAC,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACc,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAEZ,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACc,MAAM,CAACZ,SAAS,EAAE,QAAQ,CAAC,EAAEI,KAAK,CAAC,EAAEN,WAAW,GAAGG,SAAS,CAAC;EACzP,OAAO,aAAaR,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7CZ,SAAS,EAAEU;EACb,CAAC,EAAE,aAAalB,KAAK,CAACoB,aAAa,CAAClB,OAAO,EAAEN,QAAQ,CAAC;IACpDW,SAAS,EAAE,EAAE,CAACY,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC;IAC1CM,IAAI,EAAEA;EACR,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,eAAed,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}