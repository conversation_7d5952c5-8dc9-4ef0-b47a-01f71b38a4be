{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.prefixExponent = void 0;\nvar _formatDecimal = require(\"./formatDecimal.js\");\nvar prefixExponent;\nexports.prefixExponent = prefixExponent;\nfunction _default(x, p) {\n  var d = (0, _formatDecimal.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1],\n    i = exponent - (exports.prefixExponent = prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n    n = coefficient.length;\n  return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0, _formatDecimal.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "prefixExponent", "_formatDecimal", "require", "x", "p", "d", "formatDecimalParts", "coefficient", "exponent", "i", "Math", "max", "min", "floor", "n", "length", "Array", "join", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatPrefixAuto.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.prefixExponent = void 0;\n\nvar _formatDecimal = require(\"./formatDecimal.js\");\n\nvar prefixExponent;\nexports.prefixExponent = prefixExponent;\n\nfunction _default(x, p) {\n  var d = (0, _formatDecimal.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (exports.prefixExponent = prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0, _formatDecimal.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAE/B,IAAIC,cAAc,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAElD,IAAIF,cAAc;AAClBJ,OAAO,CAACI,cAAc,GAAGA,cAAc;AAEvC,SAASD,QAAQA,CAACI,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEJ,cAAc,CAACK,kBAAkB,EAAEH,CAAC,EAAEC,CAAC,CAAC;EACpD,IAAI,CAACC,CAAC,EAAE,OAAOF,CAAC,GAAG,EAAE;EACrB,IAAII,WAAW,GAAGF,CAAC,CAAC,CAAC,CAAC;IAClBG,QAAQ,GAAGH,CAAC,CAAC,CAAC,CAAC;IACfI,CAAC,GAAGD,QAAQ,IAAIZ,OAAO,CAACI,cAAc,GAAGA,cAAc,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,KAAK,CAACL,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACtHM,CAAC,GAAGP,WAAW,CAACQ,MAAM;EAC1B,OAAON,CAAC,KAAKK,CAAC,GAAGP,WAAW,GAAGE,CAAC,GAAGK,CAAC,GAAGP,WAAW,GAAG,IAAIS,KAAK,CAACP,CAAC,GAAGK,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGR,CAAC,GAAG,CAAC,GAAGF,WAAW,CAACW,KAAK,CAAC,CAAC,EAAET,CAAC,CAAC,GAAG,GAAG,GAAGF,WAAW,CAACW,KAAK,CAACT,CAAC,CAAC,GAAG,IAAI,GAAG,IAAIO,KAAK,CAAC,CAAC,GAAGP,CAAC,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEhB,cAAc,CAACK,kBAAkB,EAAEH,CAAC,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,CAAC,GAAGK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}