{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"reactReduxForwardedRef\"];\n\n/* eslint-disable valid-jsdoc, @typescript-eslint/no-unused-vars */\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport defaultSelectorFactory from '../connect/selectorFactory';\nimport { mapDispatchToPropsFactory } from '../connect/mapDispatchToProps';\nimport { mapStateToPropsFactory } from '../connect/mapStateToProps';\nimport { mergePropsFactory } from '../connect/mergeProps';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport shallowEqual from '../utils/shallowEqual';\nimport warning from '../utils/warning';\nimport { ReactReduxContext } from './Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStore = notInitialized;\nexport const initializeConnect = fn => {\n  useSyncExternalStore = fn;\n}; // Define some constant arrays just to avoid re-creating these\n\nconst EMPTY_ARRAY = [null, 0];\nconst NO_SUBSCRIPTION_ARRAY = [null, null]; // Attempts to stringify whatever not-really-a-component value we were given\n// for logging in an error message\n\nconst stringifyComponent = Comp => {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\n\n// This is \"just\" a `useLayoutEffect`, but with two modifications:\n// - we need to fall back to `useEffect` in SSR to avoid annoying warnings\n// - we extract this to a separate function to avoid closing over values\n//   and causing memory leaks\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies);\n} // Effect callback, extracted: assign the latest props values to refs for later usage\n\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps,\n// actualChildProps: unknown,\nchildPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n} // Effect callback, extracted: subscribe to the Redux store or nearest connected ancestor,\n// check for updates after dispatched actions, and trigger re-renders.\n\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs,\n// forceComponentUpdateDispatch: React.Dispatch<any>,\nadditionalSubscribeListener) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return () => {}; // Capture values for checking if and when this component unmounts\n\n  let didUnsubscribe = false;\n  let lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  const checkForUpdates = () => {\n    if (didUnsubscribe || !isMounted.current) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    } // TODO We're currently calling getState ourselves here, rather than letting `uSES` do it\n\n    const latestStoreState = store.getState();\n    let newChildProps, error;\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // TODO This is hacky and not how `uSES` is meant to be used\n      // Trigger the React `useSyncExternalStore` subscriber\n\n      additionalSubscribeListener();\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n  const unsubscribeWrapper = () => {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n  return unsubscribeWrapper;\n} // Reducer initial state creation for our update reducer\n\nconst initStateUpdates = () => EMPTY_ARRAY;\nfunction strictEqual(a, b) {\n  return a === b;\n}\n/**\r\n * Infers the type of props that a connector will inject into a component.\r\n */\n\nlet hasWarnedAboutDeprecatedPureOption = false;\n/**\r\n * Connects a React component to a Redux store.\r\n *\r\n * - Without arguments, just wraps the component, without changing the behavior / props\r\n *\r\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\r\n * is to override ownProps (as stated in the docs), so what remains is everything that's\r\n * not a state or dispatch prop\r\n *\r\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\r\n * should be valid component props, because it depends on mergeProps implementation.\r\n * As such, it is the user's responsibility to extend ownProps interface from state or\r\n * dispatch props or both when applicable\r\n *\r\n * @param mapStateToProps A function that extracts values from state\r\n * @param mapDispatchToProps Setup for dispatching actions\r\n * @param mergeProps Optional callback to merge state and dispatch props together\r\n * @param options Options for configuring the connection\r\n *\r\n */\n\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, {\n  // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n  // @ts-ignore\n  pure,\n  areStatesEqual = strictEqual,\n  areOwnPropsEqual = shallowEqual,\n  areStatePropsEqual = shallowEqual,\n  areMergedPropsEqual = shallowEqual,\n  // use React's forwardRef to expose a ref of the wrapped component\n  forwardRef = false,\n  // the context consumer to use\n  context = ReactReduxContext\n} = {}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (pure !== undefined && !hasWarnedAboutDeprecatedPureOption) {\n      hasWarnedAboutDeprecatedPureOption = true;\n      warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n    }\n  }\n  const Context = context;\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n  const initMergeProps = mergePropsFactory(mergeProps);\n  const shouldHandleStateChanges = Boolean(mapStateToProps);\n  const wrapWithConnect = WrappedComponent => {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n    }\n    const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    const displayName = `Connect(${wrappedComponentName})`;\n    const selectorFactoryOptions = {\n      shouldHandleStateChanges,\n      displayName,\n      wrappedComponentName,\n      WrappedComponent,\n      // @ts-ignore\n      initMapStateToProps,\n      // @ts-ignore\n      initMapDispatchToProps,\n      initMergeProps,\n      areStatesEqual,\n      areStatePropsEqual,\n      areOwnPropsEqual,\n      areMergedPropsEqual\n    };\n    function ConnectFunction(props) {\n      const [propsContext, reactReduxForwardedRef, wrapperProps] = useMemo(() => {\n        // Distinguish between actual \"data\" props that were passed to the wrapper component,\n        // and values needed to control behavior (forwarded refs, alternate context instances).\n        // To maintain the wrapperProps object reference, memoize this destructuring.\n        const {\n            reactReduxForwardedRef\n          } = props,\n          wrapperProps = _objectWithoutPropertiesLoose(props, _excluded);\n        return [props.context, reactReduxForwardedRef, wrapperProps];\n      }, [props]);\n      const ContextToUse = useMemo(() => {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer &&\n        // @ts-ignore\n        isContextConsumer(/*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      const contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(`Could not find \"store\" in the context of ` + `\"${displayName}\". Either wrap the root component in a <Provider>, ` + `or pass a custom React context provider to <Provider> and the corresponding ` + `React context consumer to ${displayName} in connect options.`);\n      } // Based on the previous check, one of these must be true\n\n      const store = didStoreComeFromProps ? props.store : contextValue.store;\n      const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n      const childPropsSelector = useMemo(() => {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return defaultSelectorFactory(store.dispatch, selectorFactoryOptions);\n      }, [store]);\n      const [subscription, notifyNestedSubs] = useMemo(() => {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n        const subscription = createSubscription(store, didStoreComeFromProps ? undefined : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n\n        const notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n        return [subscription, notifyNestedSubs];\n      }, [store, didStoreComeFromProps, contextValue]); // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n      const overriddenContextValue = useMemo(() => {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n        return _extends({}, contextValue, {\n          subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // Set up refs to coordinate values between the subscription effect and the render logic\n\n      const lastChildProps = useRef();\n      const lastWrapperProps = useRef(wrapperProps);\n      const childPropsFromStoreUpdate = useRef();\n      const renderIsScheduled = useRef(false);\n      const isProcessingDispatch = useRef(false);\n      const isMounted = useRef(false);\n      const latestSubscriptionCallbackError = useRef();\n      useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n          isMounted.current = false;\n        };\n      }, []);\n      const actualChildPropsSelector = useMemo(() => {\n        const selector = () => {\n          // Tricky logic here:\n          // - This render may have been triggered by a Redux store update that produced new child props\n          // - However, we may have gotten new wrapper props after that\n          // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n          // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n          // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n          if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n            return childPropsFromStoreUpdate.current;\n          } // TODO We're reading the store directly in render() here. Bad idea?\n          // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n          // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n          // to determine what the child props should be.\n\n          return childPropsSelector(store.getState(), wrapperProps);\n        };\n        return selector;\n      }, [store, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      const subscribeForReact = useMemo(() => {\n        const subscribe = reactListener => {\n          if (!subscription) {\n            return () => {};\n          }\n          return subscribeUpdates(shouldHandleStateChanges, store, subscription,\n          // @ts-ignore\n          childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n        };\n        return subscribe;\n      }, [subscription]);\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs]);\n      let actualChildProps;\n      try {\n        actualChildProps = useSyncExternalStore(\n        // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n        subscribeForReact,\n        // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n        // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n        actualChildPropsSelector, getServerState ? () => childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n      } catch (err) {\n        if (latestSubscriptionCallbackError.current) {\n          ;\n          err.message += `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`;\n        }\n        throw err;\n      }\n      useIsomorphicLayoutEffect(() => {\n        latestSubscriptionCallbackError.current = undefined;\n        childPropsFromStoreUpdate.current = undefined;\n        lastChildProps.current = actualChildProps;\n      }); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      const renderedWrappedComponent = useMemo(() => {\n        return (/*#__PURE__*/\n          // @ts-ignore\n          React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n            ref: reactReduxForwardedRef\n          }))\n        );\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      const renderedChild = useMemo(() => {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    }\n    const _Connect = React.memo(ConnectFunction);\n\n    // Add a hacky cast to get the right output type\n    const Connect = _Connect;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n    if (forwardRef) {\n      const _forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        // @ts-ignore\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n      const forwarded = _forwarded;\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n    return hoistStatics(Connect, WrappedComponent);\n  };\n  return wrapWithConnect;\n}\nexport default connect;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "hoistStatics", "React", "useContext", "useMemo", "useRef", "isValidElementType", "isContextConsumer", "defaultSelectorFactory", "mapDispatchToPropsFactory", "mapStateToPropsFactory", "mergePropsFactory", "createSubscription", "useIsomorphicLayoutEffect", "shallowEqual", "warning", "ReactReduxContext", "notInitialized", "useSyncExternalStore", "initializeConnect", "fn", "EMPTY_ARRAY", "NO_SUBSCRIPTION_ARRAY", "stringifyComponent", "Comp", "JSON", "stringify", "err", "String", "useIsomorphicLayoutEffectWithArgs", "effectFunc", "effectArgs", "dependencies", "captureWrapperProps", "lastWrapperProps", "lastChildProps", "renderIsScheduled", "wrapperProps", "childPropsFromStoreUpdate", "notifyNestedSubs", "current", "subscribeUpdates", "shouldHandleStateChanges", "store", "subscription", "childPropsSelector", "isMounted", "additionalSubscribeListener", "didUnsubscribe", "lastThrownError", "checkForUpdates", "latestStoreState", "getState", "newChildProps", "error", "e", "onStateChange", "trySubscribe", "unsubscribeWrapper", "tryUnsubscribe", "initStateUpdates", "strictEqual", "a", "b", "hasWarnedAboutDeprecatedPureOption", "connect", "mapStateToProps", "mapDispatchToProps", "mergeProps", "pure", "areStatesEqual", "areOwnPropsEqual", "areStatePropsEqual", "areMergedPropsEqual", "forwardRef", "context", "process", "env", "NODE_ENV", "undefined", "Context", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "Boolean", "wrapWithConnect", "WrappedComponent", "Error", "wrappedComponentName", "displayName", "name", "selectorFactoryOptions", "ConnectFunction", "props", "props<PERSON><PERSON><PERSON><PERSON>", "reactReduxForwardedRef", "ContextToUse", "Consumer", "createElement", "contextValue", "didStoreComeFromProps", "dispatch", "didStoreComeFromContext", "getServerState", "bind", "overriddenContextValue", "isProcessingDispatch", "latestSubscriptionCallbackError", "actualChildPropsSelector", "selector", "subscribeForReact", "subscribe", "reactListener", "actualChildProps", "message", "stack", "renderedWrappedComponent", "ref", "<PERSON><PERSON><PERSON><PERSON>", "Provider", "value", "_Connect", "memo", "Connect", "_forwarded", "forwardConnectRef", "forwarded"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/components/connect.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"reactReduxForwardedRef\"];\n\n/* eslint-disable valid-jsdoc, @typescript-eslint/no-unused-vars */\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport defaultSelectorFactory from '../connect/selectorFactory';\nimport { mapDispatchToPropsFactory } from '../connect/mapDispatchToProps';\nimport { mapStateToPropsFactory } from '../connect/mapStateToProps';\nimport { mergePropsFactory } from '../connect/mergeProps';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport shallowEqual from '../utils/shallowEqual';\nimport warning from '../utils/warning';\nimport { ReactReduxContext } from './Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStore = notInitialized;\nexport const initializeConnect = fn => {\n  useSyncExternalStore = fn;\n}; // Define some constant arrays just to avoid re-creating these\n\nconst EMPTY_ARRAY = [null, 0];\nconst NO_SUBSCRIPTION_ARRAY = [null, null]; // Attempts to stringify whatever not-really-a-component value we were given\n// for logging in an error message\n\nconst stringifyComponent = Comp => {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\n\n// This is \"just\" a `useLayoutEffect`, but with two modifications:\n// - we need to fall back to `useEffect` in SSR to avoid annoying warnings\n// - we extract this to a separate function to avoid closing over values\n//   and causing memory leaks\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies);\n} // Effect callback, extracted: assign the latest props values to refs for later usage\n\n\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, // actualChildProps: unknown,\nchildPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n} // Effect callback, extracted: subscribe to the Redux store or nearest connected ancestor,\n// check for updates after dispatched actions, and trigger re-renders.\n\n\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, // forceComponentUpdateDispatch: React.Dispatch<any>,\nadditionalSubscribeListener) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return () => {}; // Capture values for checking if and when this component unmounts\n\n  let didUnsubscribe = false;\n  let lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  const checkForUpdates = () => {\n    if (didUnsubscribe || !isMounted.current) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    } // TODO We're currently calling getState ourselves here, rather than letting `uSES` do it\n\n\n    const latestStoreState = store.getState();\n    let newChildProps, error;\n\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // TODO This is hacky and not how `uSES` is meant to be used\n      // Trigger the React `useSyncExternalStore` subscriber\n\n      additionalSubscribeListener();\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n\n  const unsubscribeWrapper = () => {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n\n  return unsubscribeWrapper;\n} // Reducer initial state creation for our update reducer\n\n\nconst initStateUpdates = () => EMPTY_ARRAY;\n\nfunction strictEqual(a, b) {\n  return a === b;\n}\n/**\r\n * Infers the type of props that a connector will inject into a component.\r\n */\n\n\nlet hasWarnedAboutDeprecatedPureOption = false;\n/**\r\n * Connects a React component to a Redux store.\r\n *\r\n * - Without arguments, just wraps the component, without changing the behavior / props\r\n *\r\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\r\n * is to override ownProps (as stated in the docs), so what remains is everything that's\r\n * not a state or dispatch prop\r\n *\r\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\r\n * should be valid component props, because it depends on mergeProps implementation.\r\n * As such, it is the user's responsibility to extend ownProps interface from state or\r\n * dispatch props or both when applicable\r\n *\r\n * @param mapStateToProps A function that extracts values from state\r\n * @param mapDispatchToProps Setup for dispatching actions\r\n * @param mergeProps Optional callback to merge state and dispatch props together\r\n * @param options Options for configuring the connection\r\n *\r\n */\n\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, {\n  // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n  // @ts-ignore\n  pure,\n  areStatesEqual = strictEqual,\n  areOwnPropsEqual = shallowEqual,\n  areStatePropsEqual = shallowEqual,\n  areMergedPropsEqual = shallowEqual,\n  // use React's forwardRef to expose a ref of the wrapped component\n  forwardRef = false,\n  // the context consumer to use\n  context = ReactReduxContext\n} = {}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (pure !== undefined && !hasWarnedAboutDeprecatedPureOption) {\n      hasWarnedAboutDeprecatedPureOption = true;\n      warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n    }\n  }\n\n  const Context = context;\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n  const initMergeProps = mergePropsFactory(mergeProps);\n  const shouldHandleStateChanges = Boolean(mapStateToProps);\n\n  const wrapWithConnect = WrappedComponent => {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n    }\n\n    const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    const displayName = `Connect(${wrappedComponentName})`;\n    const selectorFactoryOptions = {\n      shouldHandleStateChanges,\n      displayName,\n      wrappedComponentName,\n      WrappedComponent,\n      // @ts-ignore\n      initMapStateToProps,\n      // @ts-ignore\n      initMapDispatchToProps,\n      initMergeProps,\n      areStatesEqual,\n      areStatePropsEqual,\n      areOwnPropsEqual,\n      areMergedPropsEqual\n    };\n\n    function ConnectFunction(props) {\n      const [propsContext, reactReduxForwardedRef, wrapperProps] = useMemo(() => {\n        // Distinguish between actual \"data\" props that were passed to the wrapper component,\n        // and values needed to control behavior (forwarded refs, alternate context instances).\n        // To maintain the wrapperProps object reference, memoize this destructuring.\n        const {\n          reactReduxForwardedRef\n        } = props,\n              wrapperProps = _objectWithoutPropertiesLoose(props, _excluded);\n\n        return [props.context, reactReduxForwardedRef, wrapperProps];\n      }, [props]);\n      const ContextToUse = useMemo(() => {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer && // @ts-ignore\n        isContextConsumer( /*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      const contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(`Could not find \"store\" in the context of ` + `\"${displayName}\". Either wrap the root component in a <Provider>, ` + `or pass a custom React context provider to <Provider> and the corresponding ` + `React context consumer to ${displayName} in connect options.`);\n      } // Based on the previous check, one of these must be true\n\n\n      const store = didStoreComeFromProps ? props.store : contextValue.store;\n      const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n      const childPropsSelector = useMemo(() => {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return defaultSelectorFactory(store.dispatch, selectorFactoryOptions);\n      }, [store]);\n      const [subscription, notifyNestedSubs] = useMemo(() => {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n        const subscription = createSubscription(store, didStoreComeFromProps ? undefined : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n\n        const notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n        return [subscription, notifyNestedSubs];\n      }, [store, didStoreComeFromProps, contextValue]); // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n      const overriddenContextValue = useMemo(() => {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n\n        return _extends({}, contextValue, {\n          subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // Set up refs to coordinate values between the subscription effect and the render logic\n\n      const lastChildProps = useRef();\n      const lastWrapperProps = useRef(wrapperProps);\n      const childPropsFromStoreUpdate = useRef();\n      const renderIsScheduled = useRef(false);\n      const isProcessingDispatch = useRef(false);\n      const isMounted = useRef(false);\n      const latestSubscriptionCallbackError = useRef();\n      useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n          isMounted.current = false;\n        };\n      }, []);\n      const actualChildPropsSelector = useMemo(() => {\n        const selector = () => {\n          // Tricky logic here:\n          // - This render may have been triggered by a Redux store update that produced new child props\n          // - However, we may have gotten new wrapper props after that\n          // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n          // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n          // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n          if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n            return childPropsFromStoreUpdate.current;\n          } // TODO We're reading the store directly in render() here. Bad idea?\n          // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n          // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n          // to determine what the child props should be.\n\n\n          return childPropsSelector(store.getState(), wrapperProps);\n        };\n\n        return selector;\n      }, [store, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      const subscribeForReact = useMemo(() => {\n        const subscribe = reactListener => {\n          if (!subscription) {\n            return () => {};\n          }\n\n          return subscribeUpdates(shouldHandleStateChanges, store, subscription, // @ts-ignore\n          childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n        };\n\n        return subscribe;\n      }, [subscription]);\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs]);\n      let actualChildProps;\n\n      try {\n        actualChildProps = useSyncExternalStore( // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n        subscribeForReact, // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n        // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n        actualChildPropsSelector, getServerState ? () => childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n      } catch (err) {\n        if (latestSubscriptionCallbackError.current) {\n          ;\n          err.message += `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`;\n        }\n\n        throw err;\n      }\n\n      useIsomorphicLayoutEffect(() => {\n        latestSubscriptionCallbackError.current = undefined;\n        childPropsFromStoreUpdate.current = undefined;\n        lastChildProps.current = actualChildProps;\n      }); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      const renderedWrappedComponent = useMemo(() => {\n        return (\n          /*#__PURE__*/\n          // @ts-ignore\n          React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n            ref: reactReduxForwardedRef\n          }))\n        );\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      const renderedChild = useMemo(() => {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    }\n\n    const _Connect = React.memo(ConnectFunction);\n\n    // Add a hacky cast to get the right output type\n    const Connect = _Connect;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n\n    if (forwardRef) {\n      const _forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        // @ts-ignore\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n\n      const forwarded = _forwarded;\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n\n    return hoistStatics(Connect, WrappedComponent);\n  };\n\n  return wrapWithConnect;\n}\n\nexport default connect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,wBAAwB,CAAC;;AAE5C;AACA,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,UAAU;AAChE,OAAOC,sBAAsB,MAAM,4BAA4B;AAC/D,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,iBAAiB,QAAQ,WAAW;AAC7C,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,IAAIC,oBAAoB,GAAGD,cAAc;AACzC,OAAO,MAAME,iBAAiB,GAAGC,EAAE,IAAI;EACrCF,oBAAoB,GAAGE,EAAE;AAC3B,CAAC,CAAC,CAAC;;AAEH,MAAMC,WAAW,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7B,MAAMC,qBAAqB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5C;;AAEA,MAAMC,kBAAkB,GAAGC,IAAI,IAAI;EACjC,IAAI;IACF,OAAOC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,OAAOC,MAAM,CAACJ,IAAI,CAAC;EACrB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASK,iCAAiCA,CAACC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC/EnB,yBAAyB,CAAC,MAAMiB,UAAU,CAAC,GAAGC,UAAU,CAAC,EAAEC,YAAY,CAAC;AAC1E,CAAC,CAAC;;AAGF,SAASC,mBAAmBA,CAACC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY;AAAE;AAChGC,yBAAyB,EAAEC,gBAAgB,EAAE;EAC3C;EACAL,gBAAgB,CAACM,OAAO,GAAGH,YAAY;EACvCD,iBAAiB,CAACI,OAAO,GAAG,KAAK,CAAC,CAAC;;EAEnC,IAAIF,yBAAyB,CAACE,OAAO,EAAE;IACrCF,yBAAyB,CAACE,OAAO,GAAG,IAAI;IACxCD,gBAAgB,CAAC,CAAC;EACpB;AACF,CAAC,CAAC;AACF;;AAGA,SAASE,gBAAgBA,CAACC,wBAAwB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,kBAAkB,EAAEX,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEU,SAAS,EAAER,yBAAyB,EAAEC,gBAAgB;AAAE;AAC1MQ,2BAA2B,EAAE;EAC3B;EACA,IAAI,CAACL,wBAAwB,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAIM,cAAc,GAAG,KAAK;EAC1B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE5B,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIF,cAAc,IAAI,CAACF,SAAS,CAACN,OAAO,EAAE;MACxC;MACA;MACA;IACF,CAAC,CAAC;;IAGF,MAAMW,gBAAgB,GAAGR,KAAK,CAACS,QAAQ,CAAC,CAAC;IACzC,IAAIC,aAAa,EAAEC,KAAK;IAExB,IAAI;MACF;MACA;MACAD,aAAa,GAAGR,kBAAkB,CAACM,gBAAgB,EAAEjB,gBAAgB,CAACM,OAAO,CAAC;IAChF,CAAC,CAAC,OAAOe,CAAC,EAAE;MACVD,KAAK,GAAGC,CAAC;MACTN,eAAe,GAAGM,CAAC;IACrB;IAEA,IAAI,CAACD,KAAK,EAAE;MACVL,eAAe,GAAG,IAAI;IACxB,CAAC,CAAC;;IAGF,IAAII,aAAa,KAAKlB,cAAc,CAACK,OAAO,EAAE;MAC5C,IAAI,CAACJ,iBAAiB,CAACI,OAAO,EAAE;QAC9BD,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA;MACAJ,cAAc,CAACK,OAAO,GAAGa,aAAa;MACtCf,yBAAyB,CAACE,OAAO,GAAGa,aAAa;MACjDjB,iBAAiB,CAACI,OAAO,GAAG,IAAI,CAAC,CAAC;MAClC;;MAEAO,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC,CAAC;;EAGHH,YAAY,CAACY,aAAa,GAAGN,eAAe;EAC5CN,YAAY,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEAP,eAAe,CAAC,CAAC;EAEjB,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BV,cAAc,GAAG,IAAI;IACrBJ,YAAY,CAACe,cAAc,CAAC,CAAC;IAC7Bf,YAAY,CAACY,aAAa,GAAG,IAAI;IAEjC,IAAIP,eAAe,EAAE;MACnB;MACA;MACA;MACA;MACA;MACA,MAAMA,eAAe;IACvB;EACF,CAAC;EAED,OAAOS,kBAAkB;AAC3B,CAAC,CAAC;;AAGF,MAAME,gBAAgB,GAAGA,CAAA,KAAMvC,WAAW;AAE1C,SAASwC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOD,CAAC,KAAKC,CAAC;AAChB;AACA;AACA;AACA;;AAGA,IAAIC,kCAAkC,GAAG,KAAK;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAOA,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAE;EAChE;EACA;EACAC,IAAI;EACJC,cAAc,GAAGT,WAAW;EAC5BU,gBAAgB,GAAGzD,YAAY;EAC/B0D,kBAAkB,GAAG1D,YAAY;EACjC2D,mBAAmB,GAAG3D,YAAY;EAClC;EACA4D,UAAU,GAAG,KAAK;EAClB;EACAC,OAAO,GAAG3D;AACZ,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAI4D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIT,IAAI,KAAKU,SAAS,IAAI,CAACf,kCAAkC,EAAE;MAC7DA,kCAAkC,GAAG,IAAI;MACzCjD,OAAO,CAAC,yFAAyF,CAAC;IACpG;EACF;EAEA,MAAMiE,OAAO,GAAGL,OAAO;EACvB,MAAMM,mBAAmB,GAAGvE,sBAAsB,CAACwD,eAAe,CAAC;EACnE,MAAMgB,sBAAsB,GAAGzE,yBAAyB,CAAC0D,kBAAkB,CAAC;EAC5E,MAAMgB,cAAc,GAAGxE,iBAAiB,CAACyD,UAAU,CAAC;EACpD,MAAM1B,wBAAwB,GAAG0C,OAAO,CAAClB,eAAe,CAAC;EAEzD,MAAMmB,eAAe,GAAGC,gBAAgB,IAAI;IAC1C,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACxE,kBAAkB,CAACgF,gBAAgB,CAAC,EAAE;MAClF,MAAM,IAAIC,KAAK,CAAC,mFAAmFhE,kBAAkB,CAAC+D,gBAAgB,CAAC,EAAE,CAAC;IAC5I;IAEA,MAAME,oBAAoB,GAAGF,gBAAgB,CAACG,WAAW,IAAIH,gBAAgB,CAACI,IAAI,IAAI,WAAW;IACjG,MAAMD,WAAW,GAAG,WAAWD,oBAAoB,GAAG;IACtD,MAAMG,sBAAsB,GAAG;MAC7BjD,wBAAwB;MACxB+C,WAAW;MACXD,oBAAoB;MACpBF,gBAAgB;MAChB;MACAL,mBAAmB;MACnB;MACAC,sBAAsB;MACtBC,cAAc;MACdb,cAAc;MACdE,kBAAkB;MAClBD,gBAAgB;MAChBE;IACF,CAAC;IAED,SAASmB,eAAeA,CAACC,KAAK,EAAE;MAC9B,MAAM,CAACC,YAAY,EAAEC,sBAAsB,EAAE1D,YAAY,CAAC,GAAGjC,OAAO,CAAC,MAAM;QACzE;QACA;QACA;QACA,MAAM;YACJ2F;UACF,CAAC,GAAGF,KAAK;UACHxD,YAAY,GAAGtC,6BAA6B,CAAC8F,KAAK,EAAE7F,SAAS,CAAC;QAEpE,OAAO,CAAC6F,KAAK,CAAClB,OAAO,EAAEoB,sBAAsB,EAAE1D,YAAY,CAAC;MAC9D,CAAC,EAAE,CAACwD,KAAK,CAAC,CAAC;MACX,MAAMG,YAAY,GAAG5F,OAAO,CAAC,MAAM;QACjC;QACA;QACA,OAAO0F,YAAY,IAAIA,YAAY,CAACG,QAAQ;QAAI;QAChD1F,iBAAiB,CAAE,aAAaL,KAAK,CAACgG,aAAa,CAACJ,YAAY,CAACG,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAGH,YAAY,GAAGd,OAAO;MAC5G,CAAC,EAAE,CAACc,YAAY,EAAEd,OAAO,CAAC,CAAC,CAAC,CAAC;;MAE7B,MAAMmB,YAAY,GAAGhG,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAC/C;MACA;;MAEA,MAAMI,qBAAqB,GAAGhB,OAAO,CAACS,KAAK,CAAClD,KAAK,CAAC,IAAIyC,OAAO,CAACS,KAAK,CAAClD,KAAK,CAACS,QAAQ,CAAC,IAAIgC,OAAO,CAACS,KAAK,CAAClD,KAAK,CAAC0D,QAAQ,CAAC;MACpH,MAAMC,uBAAuB,GAAGlB,OAAO,CAACe,YAAY,CAAC,IAAIf,OAAO,CAACe,YAAY,CAACxD,KAAK,CAAC;MAEpF,IAAIiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACsB,qBAAqB,IAAI,CAACE,uBAAuB,EAAE;QAC/F,MAAM,IAAIf,KAAK,CAAC,2CAA2C,GAAG,IAAIE,WAAW,qDAAqD,GAAG,8EAA8E,GAAG,6BAA6BA,WAAW,sBAAsB,CAAC;MACvR,CAAC,CAAC;;MAGF,MAAM9C,KAAK,GAAGyD,qBAAqB,GAAGP,KAAK,CAAClD,KAAK,GAAGwD,YAAY,CAACxD,KAAK;MACtE,MAAM4D,cAAc,GAAGD,uBAAuB,GAAGH,YAAY,CAACI,cAAc,GAAG5D,KAAK,CAACS,QAAQ;MAC7F,MAAMP,kBAAkB,GAAGzC,OAAO,CAAC,MAAM;QACvC;QACA;QACA,OAAOI,sBAAsB,CAACmC,KAAK,CAAC0D,QAAQ,EAAEV,sBAAsB,CAAC;MACvE,CAAC,EAAE,CAAChD,KAAK,CAAC,CAAC;MACX,MAAM,CAACC,YAAY,EAAEL,gBAAgB,CAAC,GAAGnC,OAAO,CAAC,MAAM;QACrD,IAAI,CAACsC,wBAAwB,EAAE,OAAOpB,qBAAqB,CAAC,CAAC;QAC7D;;QAEA,MAAMsB,YAAY,GAAGhC,kBAAkB,CAAC+B,KAAK,EAAEyD,qBAAqB,GAAGrB,SAAS,GAAGoB,YAAY,CAACvD,YAAY,CAAC,CAAC,CAAC;QAC/G;QACA;QACA;;QAEA,MAAML,gBAAgB,GAAGK,YAAY,CAACL,gBAAgB,CAACiE,IAAI,CAAC5D,YAAY,CAAC;QACzE,OAAO,CAACA,YAAY,EAAEL,gBAAgB,CAAC;MACzC,CAAC,EAAE,CAACI,KAAK,EAAEyD,qBAAqB,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC;MAClD;;MAEA,MAAMM,sBAAsB,GAAGrG,OAAO,CAAC,MAAM;QAC3C,IAAIgG,qBAAqB,EAAE;UACzB;UACA;UACA;UACA,OAAOD,YAAY;QACrB,CAAC,CAAC;QACF;;QAGA,OAAOrG,QAAQ,CAAC,CAAC,CAAC,EAAEqG,YAAY,EAAE;UAChCvD;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,CAACwD,qBAAqB,EAAED,YAAY,EAAEvD,YAAY,CAAC,CAAC,CAAC,CAAC;;MAEzD,MAAMT,cAAc,GAAG9B,MAAM,CAAC,CAAC;MAC/B,MAAM6B,gBAAgB,GAAG7B,MAAM,CAACgC,YAAY,CAAC;MAC7C,MAAMC,yBAAyB,GAAGjC,MAAM,CAAC,CAAC;MAC1C,MAAM+B,iBAAiB,GAAG/B,MAAM,CAAC,KAAK,CAAC;MACvC,MAAMqG,oBAAoB,GAAGrG,MAAM,CAAC,KAAK,CAAC;MAC1C,MAAMyC,SAAS,GAAGzC,MAAM,CAAC,KAAK,CAAC;MAC/B,MAAMsG,+BAA+B,GAAGtG,MAAM,CAAC,CAAC;MAChDQ,yBAAyB,CAAC,MAAM;QAC9BiC,SAAS,CAACN,OAAO,GAAG,IAAI;QACxB,OAAO,MAAM;UACXM,SAAS,CAACN,OAAO,GAAG,KAAK;QAC3B,CAAC;MACH,CAAC,EAAE,EAAE,CAAC;MACN,MAAMoE,wBAAwB,GAAGxG,OAAO,CAAC,MAAM;QAC7C,MAAMyG,QAAQ,GAAGA,CAAA,KAAM;UACrB;UACA;UACA;UACA;UACA;UACA;UACA,IAAIvE,yBAAyB,CAACE,OAAO,IAAIH,YAAY,KAAKH,gBAAgB,CAACM,OAAO,EAAE;YAClF,OAAOF,yBAAyB,CAACE,OAAO;UAC1C,CAAC,CAAC;UACF;UACA;UACA;;UAGA,OAAOK,kBAAkB,CAACF,KAAK,CAACS,QAAQ,CAAC,CAAC,EAAEf,YAAY,CAAC;QAC3D,CAAC;QAED,OAAOwE,QAAQ;MACjB,CAAC,EAAE,CAAClE,KAAK,EAAEN,YAAY,CAAC,CAAC,CAAC,CAAC;MAC3B;MACA;;MAEA,MAAMyE,iBAAiB,GAAG1G,OAAO,CAAC,MAAM;QACtC,MAAM2G,SAAS,GAAGC,aAAa,IAAI;UACjC,IAAI,CAACpE,YAAY,EAAE;YACjB,OAAO,MAAM,CAAC,CAAC;UACjB;UAEA,OAAOH,gBAAgB,CAACC,wBAAwB,EAAEC,KAAK,EAAEC,YAAY;UAAE;UACvEC,kBAAkB,EAAEX,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEU,SAAS,EAAER,yBAAyB,EAAEC,gBAAgB,EAAEyE,aAAa,CAAC;QACjJ,CAAC;QAED,OAAOD,SAAS;MAClB,CAAC,EAAE,CAACnE,YAAY,CAAC,CAAC;MAClBf,iCAAiC,CAACI,mBAAmB,EAAE,CAACC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,yBAAyB,EAAEC,gBAAgB,CAAC,CAAC;MACxK,IAAI0E,gBAAgB;MAEpB,IAAI;QACFA,gBAAgB,GAAG/F,oBAAoB;QAAE;QACzC4F,iBAAiB;QAAE;QACnB;QACAF,wBAAwB,EAAEL,cAAc,GAAG,MAAM1D,kBAAkB,CAAC0D,cAAc,CAAC,CAAC,EAAElE,YAAY,CAAC,GAAGuE,wBAAwB,CAAC;MACjI,CAAC,CAAC,OAAOjF,GAAG,EAAE;QACZ,IAAIgF,+BAA+B,CAACnE,OAAO,EAAE;UAC3C;UACAb,GAAG,CAACuF,OAAO,IAAI,4DAA4DP,+BAA+B,CAACnE,OAAO,CAAC2E,KAAK,MAAM;QAChI;QAEA,MAAMxF,GAAG;MACX;MAEAd,yBAAyB,CAAC,MAAM;QAC9B8F,+BAA+B,CAACnE,OAAO,GAAGuC,SAAS;QACnDzC,yBAAyB,CAACE,OAAO,GAAGuC,SAAS;QAC7C5C,cAAc,CAACK,OAAO,GAAGyE,gBAAgB;MAC3C,CAAC,CAAC,CAAC,CAAC;MACJ;;MAEA,MAAMG,wBAAwB,GAAGhH,OAAO,CAAC,MAAM;QAC7C,QACE;UACA;UACAF,KAAK,CAACgG,aAAa,CAACZ,gBAAgB,EAAExF,QAAQ,CAAC,CAAC,CAAC,EAAEmH,gBAAgB,EAAE;YACnEI,GAAG,EAAEtB;UACP,CAAC,CAAC;QAAC;MAEP,CAAC,EAAE,CAACA,sBAAsB,EAAET,gBAAgB,EAAE2B,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAClE;;MAEA,MAAMK,aAAa,GAAGlH,OAAO,CAAC,MAAM;QAClC,IAAIsC,wBAAwB,EAAE;UAC5B;UACA;UACA;UACA,OAAO,aAAaxC,KAAK,CAACgG,aAAa,CAACF,YAAY,CAACuB,QAAQ,EAAE;YAC7DC,KAAK,EAAEf;UACT,CAAC,EAAEW,wBAAwB,CAAC;QAC9B;QAEA,OAAOA,wBAAwB;MACjC,CAAC,EAAE,CAACpB,YAAY,EAAEoB,wBAAwB,EAAEX,sBAAsB,CAAC,CAAC;MACpE,OAAOa,aAAa;IACtB;IAEA,MAAMG,QAAQ,GAAGvH,KAAK,CAACwH,IAAI,CAAC9B,eAAe,CAAC;;IAE5C;IACA,MAAM+B,OAAO,GAAGF,QAAQ;IACxBE,OAAO,CAACrC,gBAAgB,GAAGA,gBAAgB;IAC3CqC,OAAO,CAAClC,WAAW,GAAGG,eAAe,CAACH,WAAW,GAAGA,WAAW;IAE/D,IAAIf,UAAU,EAAE;MACd,MAAMkD,UAAU,GAAG1H,KAAK,CAACwE,UAAU,CAAC,SAASmD,iBAAiBA,CAAChC,KAAK,EAAEwB,GAAG,EAAE;QACzE;QACA,OAAO,aAAanH,KAAK,CAACgG,aAAa,CAACyB,OAAO,EAAE7H,QAAQ,CAAC,CAAC,CAAC,EAAE+F,KAAK,EAAE;UACnEE,sBAAsB,EAAEsB;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEF,MAAMS,SAAS,GAAGF,UAAU;MAC5BE,SAAS,CAACrC,WAAW,GAAGA,WAAW;MACnCqC,SAAS,CAACxC,gBAAgB,GAAGA,gBAAgB;MAC7C,OAAOrF,YAAY,CAAC6H,SAAS,EAAExC,gBAAgB,CAAC;IAClD;IAEA,OAAOrF,YAAY,CAAC0H,OAAO,EAAErC,gBAAgB,CAAC;EAChD,CAAC;EAED,OAAOD,eAAe;AACxB;AAEA,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}