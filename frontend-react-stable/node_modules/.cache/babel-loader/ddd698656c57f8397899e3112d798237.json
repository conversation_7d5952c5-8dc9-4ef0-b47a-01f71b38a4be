{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nvar EMPTY_LIST = [];\nfunction toErrorEntity(error, errorStatus, prefix) {\n  var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  return {\n    key: typeof error === 'string' ? error : \"\".concat(prefix, \"-\").concat(index),\n    error: error,\n    errorStatus: errorStatus\n  };\n}\nexport default function ErrorList(_ref) {\n  var help = _ref.help,\n    helpStatus = _ref.helpStatus,\n    _ref$errors = _ref.errors,\n    errors = _ref$errors === void 0 ? EMPTY_LIST : _ref$errors,\n    _ref$warnings = _ref.warnings,\n    warnings = _ref$warnings === void 0 ? EMPTY_LIST : _ref$warnings,\n    rootClassName = _ref.className,\n    fieldId = _ref.fieldId,\n    onVisibleChanged = _ref.onVisibleChanged;\n  var _React$useContext = React.useContext(FormItemPrefixContext),\n    prefixCls = _React$useContext.prefixCls;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var baseClassName = \"\".concat(prefixCls, \"-item-explain\");\n  var rootPrefixCls = getPrefixCls();\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  var debounceErrors = useDebounce(errors);\n  var debounceWarnings = useDebounce(warnings);\n  var fullKeyList = React.useMemo(function () {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, helpStatus, 'help')];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map(function (error, index) {\n      return toErrorEntity(error, 'error', 'error', index);\n    })), _toConsumableArray(debounceWarnings.map(function (warning, index) {\n      return toErrorEntity(warning, 'warning', 'warning', index);\n    })));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  var helpProps = {};\n  if (fieldId) {\n    helpProps.id = \"\".concat(fieldId, \"_help\");\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: \"\".concat(rootPrefixCls, \"-show-help\"),\n    visible: !!fullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, function (holderProps) {\n    var holderClassName = holderProps.className,\n      holderStyle = holderProps.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, rootClassName),\n      style: holderStyle,\n      role: \"alert\"\n    }), /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n      keys: fullKeyList\n    }, collapseMotion, {\n      motionName: \"\".concat(rootPrefixCls, \"-show-help-item\"),\n      component: false\n    }), function (itemProps) {\n      var key = itemProps.key,\n        error = itemProps.error,\n        errorStatus = itemProps.errorStatus,\n        itemClassName = itemProps.className,\n        itemStyle = itemProps.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, _defineProperty({}, \"\".concat(baseClassName, \"-\").concat(errorStatus), errorStatus)),\n        style: itemStyle\n      }, error);\n    }));\n  });\n}", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_toConsumableArray", "classNames", "CSSMotion", "CSSMotionList", "React", "ConfigContext", "collapseMotion", "FormItemPrefixContext", "useDebounce", "EMPTY_LIST", "toErrorEntity", "error", "errorStatus", "prefix", "index", "arguments", "length", "undefined", "key", "concat", "ErrorList", "_ref", "help", "helpStatus", "_ref$errors", "errors", "_ref$warnings", "warnings", "rootClassName", "className", "fieldId", "onVisibleChanged", "_React$useContext", "useContext", "prefixCls", "_React$useContext2", "getPrefixCls", "baseClassName", "rootPrefixCls", "debounceErrors", "debounce<PERSON><PERSON><PERSON>s", "fullKeyList", "useMemo", "map", "warning", "helpProps", "id", "createElement", "motionDeadline", "motionName", "visible", "holderProps", "holderClassName", "holder<PERSON>tyle", "style", "role", "keys", "component", "itemProps", "itemClassName", "itemStyle"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/ErrorList.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nvar EMPTY_LIST = [];\nfunction toErrorEntity(error, errorStatus, prefix) {\n  var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  return {\n    key: typeof error === 'string' ? error : \"\".concat(prefix, \"-\").concat(index),\n    error: error,\n    errorStatus: errorStatus\n  };\n}\nexport default function ErrorList(_ref) {\n  var help = _ref.help,\n    helpStatus = _ref.helpStatus,\n    _ref$errors = _ref.errors,\n    errors = _ref$errors === void 0 ? EMPTY_LIST : _ref$errors,\n    _ref$warnings = _ref.warnings,\n    warnings = _ref$warnings === void 0 ? EMPTY_LIST : _ref$warnings,\n    rootClassName = _ref.className,\n    fieldId = _ref.fieldId,\n    onVisibleChanged = _ref.onVisibleChanged;\n  var _React$useContext = React.useContext(FormItemPrefixContext),\n    prefixCls = _React$useContext.prefixCls;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var baseClassName = \"\".concat(prefixCls, \"-item-explain\");\n  var rootPrefixCls = getPrefixCls();\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  var debounceErrors = useDebounce(errors);\n  var debounceWarnings = useDebounce(warnings);\n  var fullKeyList = React.useMemo(function () {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, helpStatus, 'help')];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map(function (error, index) {\n      return toErrorEntity(error, 'error', 'error', index);\n    })), _toConsumableArray(debounceWarnings.map(function (warning, index) {\n      return toErrorEntity(warning, 'warning', 'warning', index);\n    })));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  var helpProps = {};\n  if (fieldId) {\n    helpProps.id = \"\".concat(fieldId, \"_help\");\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: \"\".concat(rootPrefixCls, \"-show-help\"),\n    visible: !!fullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, function (holderProps) {\n    var holderClassName = holderProps.className,\n      holderStyle = holderProps.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, rootClassName),\n      style: holderStyle,\n      role: \"alert\"\n    }), /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n      keys: fullKeyList\n    }, collapseMotion, {\n      motionName: \"\".concat(rootPrefixCls, \"-show-help-item\"),\n      component: false\n    }), function (itemProps) {\n      var key = itemProps.key,\n        error = itemProps.error,\n        errorStatus = itemProps.errorStatus,\n        itemClassName = itemProps.className,\n        itemStyle = itemProps.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, _defineProperty({}, \"\".concat(baseClassName, \"-\").concat(errorStatus), errorStatus)),\n        style: itemStyle\n      }, error);\n    }));\n  });\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,IAAIC,aAAa,QAAQ,WAAW;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,SAASC,qBAAqB,QAAQ,WAAW;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,IAAIC,UAAU,GAAG,EAAE;AACnB,SAASC,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACjD,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF,OAAO;IACLG,GAAG,EAAE,OAAOP,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE,CAACQ,MAAM,CAACN,MAAM,EAAE,GAAG,CAAC,CAACM,MAAM,CAACL,KAAK,CAAC;IAC7EH,KAAK,EAAEA,KAAK;IACZC,WAAW,EAAEA;EACf,CAAC;AACH;AACA,eAAe,SAASQ,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,WAAW,GAAGH,IAAI,CAACI,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGf,UAAU,GAAGe,WAAW;IAC1DE,aAAa,GAAGL,IAAI,CAACM,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGjB,UAAU,GAAGiB,aAAa;IAChEE,aAAa,GAAGP,IAAI,CAACQ,SAAS;IAC9BC,OAAO,GAAGT,IAAI,CAACS,OAAO;IACtBC,gBAAgB,GAAGV,IAAI,CAACU,gBAAgB;EAC1C,IAAIC,iBAAiB,GAAG5B,KAAK,CAAC6B,UAAU,CAAC1B,qBAAqB,CAAC;IAC7D2B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,IAAIC,kBAAkB,GAAG/B,KAAK,CAAC6B,UAAU,CAAC5B,aAAa,CAAC;IACtD+B,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAChD,IAAIC,aAAa,GAAG,EAAE,CAAClB,MAAM,CAACe,SAAS,EAAE,eAAe,CAAC;EACzD,IAAII,aAAa,GAAGF,YAAY,CAAC,CAAC;EAClC;EACA;EACA,IAAIG,cAAc,GAAG/B,WAAW,CAACiB,MAAM,CAAC;EACxC,IAAIe,gBAAgB,GAAGhC,WAAW,CAACmB,QAAQ,CAAC;EAC5C,IAAIc,WAAW,GAAGrC,KAAK,CAACsC,OAAO,CAAC,YAAY;IAC1C,IAAIpB,IAAI,KAAKL,SAAS,IAAIK,IAAI,KAAK,IAAI,EAAE;MACvC,OAAO,CAACZ,aAAa,CAACY,IAAI,EAAEC,UAAU,EAAE,MAAM,CAAC,CAAC;IAClD;IACA,OAAO,EAAE,CAACJ,MAAM,CAACnB,kBAAkB,CAACuC,cAAc,CAACI,GAAG,CAAC,UAAUhC,KAAK,EAAEG,KAAK,EAAE;MAC7E,OAAOJ,aAAa,CAACC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAEG,KAAK,CAAC;IACtD,CAAC,CAAC,CAAC,EAAEd,kBAAkB,CAACwC,gBAAgB,CAACG,GAAG,CAAC,UAAUC,OAAO,EAAE9B,KAAK,EAAE;MACrE,OAAOJ,aAAa,CAACkC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE9B,KAAK,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAACQ,IAAI,EAAEC,UAAU,EAAEgB,cAAc,EAAEC,gBAAgB,CAAC,CAAC;EACxD,IAAIK,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIf,OAAO,EAAE;IACXe,SAAS,CAACC,EAAE,GAAG,EAAE,CAAC3B,MAAM,CAACW,OAAO,EAAE,OAAO,CAAC;EAC5C;EACA,OAAO,aAAa1B,KAAK,CAAC2C,aAAa,CAAC7C,SAAS,EAAE;IACjD8C,cAAc,EAAE1C,cAAc,CAAC0C,cAAc;IAC7CC,UAAU,EAAE,EAAE,CAAC9B,MAAM,CAACmB,aAAa,EAAE,YAAY,CAAC;IAClDY,OAAO,EAAE,CAAC,CAACT,WAAW,CAACzB,MAAM;IAC7Be,gBAAgB,EAAEA;EACpB,CAAC,EAAE,UAAUoB,WAAW,EAAE;IACxB,IAAIC,eAAe,GAAGD,WAAW,CAACtB,SAAS;MACzCwB,WAAW,GAAGF,WAAW,CAACG,KAAK;IACjC,OAAO,aAAalD,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAE8C,SAAS,EAAE;MACrEhB,SAAS,EAAE5B,UAAU,CAACoC,aAAa,EAAEe,eAAe,EAAExB,aAAa,CAAC;MACpE0B,KAAK,EAAED,WAAW;MAClBE,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAanD,KAAK,CAAC2C,aAAa,CAAC5C,aAAa,EAAEJ,QAAQ,CAAC;MAC3DyD,IAAI,EAAEf;IACR,CAAC,EAAEnC,cAAc,EAAE;MACjB2C,UAAU,EAAE,EAAE,CAAC9B,MAAM,CAACmB,aAAa,EAAE,iBAAiB,CAAC;MACvDmB,SAAS,EAAE;IACb,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;MACvB,IAAIxC,GAAG,GAAGwC,SAAS,CAACxC,GAAG;QACrBP,KAAK,GAAG+C,SAAS,CAAC/C,KAAK;QACvBC,WAAW,GAAG8C,SAAS,CAAC9C,WAAW;QACnC+C,aAAa,GAAGD,SAAS,CAAC7B,SAAS;QACnC+B,SAAS,GAAGF,SAAS,CAACJ,KAAK;MAC7B,OAAO,aAAalD,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAE;QAC7C7B,GAAG,EAAEA,GAAG;QACRW,SAAS,EAAE5B,UAAU,CAAC0D,aAAa,EAAE7D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACkB,aAAa,EAAE,GAAG,CAAC,CAAClB,MAAM,CAACP,WAAW,CAAC,EAAEA,WAAW,CAAC,CAAC;QACzH0C,KAAK,EAAEM;MACT,CAAC,EAAEjD,KAAK,CAAC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}