{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nvar MobilePopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    visible = props.visible,\n    zIndex = props.zIndex,\n    children = props.children,\n    _props$mobile = props.mobile;\n  _props$mobile = _props$mobile === void 0 ? {} : _props$mobile;\n  var popupClassName = _props$mobile.popupClassName,\n    popupStyle = _props$mobile.popupStyle,\n    _props$mobile$popupMo = _props$mobile.popupMotion,\n    popupMotion = _props$mobile$popupMo === void 0 ? {} : _props$mobile$popupMo,\n    popupRender = _props$mobile.popupRender,\n    onClick = props.onClick;\n  var elementRef = React.useRef(); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {},\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread({\n    zIndex: zIndex\n  }, popupStyle);\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  } // Mobile support additional render\n\n  if (popupRender) {\n    childNode = popupRender(childNode);\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    removeOnLeave: true\n  }, popupMotion), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, popupClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      onClick: onClick,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode);\n  });\n});\nMobilePopupInner.displayName = 'MobilePopupInner';\nexport default MobilePopupInner;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "React", "CSSMotion", "classNames", "MobilePopupInner", "forwardRef", "props", "ref", "prefixCls", "visible", "zIndex", "children", "_props$mobile", "mobile", "popupClassName", "popupStyle", "_props$mobile$popupMo", "popupMotion", "popupRender", "onClick", "elementRef", "useRef", "useImperativeHandle", "forceAlign", "getElement", "current", "mergedStyle", "childNode", "Children", "count", "createElement", "className", "concat", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "style", "mergedClassName", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/Popup/MobilePopupInner.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nvar MobilePopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n      visible = props.visible,\n      zIndex = props.zIndex,\n      children = props.children,\n      _props$mobile = props.mobile;\n  _props$mobile = _props$mobile === void 0 ? {} : _props$mobile;\n  var popupClassName = _props$mobile.popupClassName,\n      popupStyle = _props$mobile.popupStyle,\n      _props$mobile$popupMo = _props$mobile.popupMotion,\n      popupMotion = _props$mobile$popupMo === void 0 ? {} : _props$mobile$popupMo,\n      popupRender = _props$mobile.popupRender,\n      onClick = props.onClick;\n  var elementRef = React.useRef(); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {},\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread({\n    zIndex: zIndex\n  }, popupStyle);\n\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  } // Mobile support additional render\n\n\n  if (popupRender) {\n    childNode = popupRender(childNode);\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    removeOnLeave: true\n  }, popupMotion), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, popupClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      onClick: onClick,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode);\n  });\n});\nMobilePopupInner.displayName = 'MobilePopupInner';\nexport default MobilePopupInner;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,gBAAgB,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACzE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,aAAa,GAAGN,KAAK,CAACO,MAAM;EAChCD,aAAa,GAAGA,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;EAC7D,IAAIE,cAAc,GAAGF,aAAa,CAACE,cAAc;IAC7CC,UAAU,GAAGH,aAAa,CAACG,UAAU;IACrCC,qBAAqB,GAAGJ,aAAa,CAACK,WAAW;IACjDA,WAAW,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;IAC3EE,WAAW,GAAGN,aAAa,CAACM,WAAW;IACvCC,OAAO,GAAGb,KAAK,CAACa,OAAO;EAC3B,IAAIC,UAAU,GAAGnB,KAAK,CAACoB,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEjCpB,KAAK,CAACqB,mBAAmB,CAACf,GAAG,EAAE,YAAY;IACzC,OAAO;MACLgB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAAC,CAAC;MACpCC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOJ,UAAU,CAACK,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,WAAW,GAAG1B,aAAa,CAAC;IAC9BU,MAAM,EAAEA;EACV,CAAC,EAAEK,UAAU,CAAC;EAEd,IAAIY,SAAS,GAAGhB,QAAQ,CAAC,CAAC;;EAE1B,IAAIV,KAAK,CAAC2B,QAAQ,CAACC,KAAK,CAAClB,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtCgB,SAAS,GAAG,aAAa1B,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;MAClDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACxB,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAEG,QAAQ,CAAC;EACd,CAAC,CAAC;;EAGF,IAAIO,WAAW,EAAE;IACfS,SAAS,GAAGT,WAAW,CAACS,SAAS,CAAC;EACpC;EAEA,OAAO,aAAa1B,KAAK,CAAC6B,aAAa,CAAC5B,SAAS,EAAEH,QAAQ,CAAC;IAC1DU,OAAO,EAAEA,OAAO;IAChBF,GAAG,EAAEa,UAAU;IACfa,aAAa,EAAE;EACjB,CAAC,EAAEhB,WAAW,CAAC,EAAE,UAAUiB,IAAI,EAAEC,SAAS,EAAE;IAC1C,IAAIC,eAAe,GAAGF,IAAI,CAACH,SAAS;MAChCM,WAAW,GAAGH,IAAI,CAACI,KAAK;IAC5B,IAAIC,eAAe,GAAGpC,UAAU,CAACK,SAAS,EAAEM,cAAc,EAAEsB,eAAe,CAAC;IAC5E,OAAO,aAAanC,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;MAC7CvB,GAAG,EAAE4B,SAAS;MACdJ,SAAS,EAAEQ,eAAe;MAC1BpB,OAAO,EAAEA,OAAO;MAChBmB,KAAK,EAAEtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqC,WAAW,CAAC,EAAEX,WAAW;IAClE,CAAC,EAAEC,SAAS,CAAC;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFvB,gBAAgB,CAACoC,WAAW,GAAG,kBAAkB;AACjD,eAAepC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}