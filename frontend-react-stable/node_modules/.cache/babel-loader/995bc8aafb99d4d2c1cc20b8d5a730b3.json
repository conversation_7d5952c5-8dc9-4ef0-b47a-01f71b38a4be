{"ast": null, "code": "export function getMotion(_ref) {\n  var prefixCls = _ref.prefixCls,\n    motion = _ref.motion,\n    animation = _ref.animation,\n    transitionName = _ref.transitionName;\n  if (motion) {\n    return motion;\n  }\n  if (animation) {\n    return {\n      motionName: \"\".concat(prefixCls, \"-\").concat(animation)\n    };\n  }\n  if (transitionName) {\n    return {\n      motionName: transitionName\n    };\n  }\n  return null;\n}", "map": {"version": 3, "names": ["getMotion", "_ref", "prefixCls", "motion", "animation", "transitionName", "motionName", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/utils/legacyUtil.js"], "sourcesContent": ["export function getMotion(_ref) {\n  var prefixCls = _ref.prefixCls,\n      motion = _ref.motion,\n      animation = _ref.animation,\n      transitionName = _ref.transitionName;\n\n  if (motion) {\n    return motion;\n  }\n\n  if (animation) {\n    return {\n      motionName: \"\".concat(prefixCls, \"-\").concat(animation)\n    };\n  }\n\n  if (transitionName) {\n    return {\n      motionName: transitionName\n    };\n  }\n\n  return null;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,cAAc,GAAGJ,IAAI,CAACI,cAAc;EAExC,IAAIF,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EAEA,IAAIC,SAAS,EAAE;IACb,OAAO;MACLE,UAAU,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,CAACK,MAAM,CAACH,SAAS;IACxD,CAAC;EACH;EAEA,IAAIC,cAAc,EAAE;IAClB,OAAO;MACLC,UAAU,EAAED;IACd,CAAC;EACH;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}