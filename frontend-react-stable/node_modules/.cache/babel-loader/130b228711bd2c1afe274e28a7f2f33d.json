{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = band;\nexports.point = point;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _init = require(\"./init.js\");\nvar _ordinal = _interopRequireDefault(require(\"./ordinal.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction band() {\n  var scale = (0, _ordinal.default)().unknown(undefined),\n    domain = scale.domain,\n    ordinalRange = scale.range,\n    r0 = 0,\n    r1 = 1,\n    step,\n    bandwidth,\n    round = false,\n    paddingInner = 0,\n    paddingOuter = 0,\n    align = 0.5;\n  delete scale.unknown;\n  function rescale() {\n    var n = domain().length,\n      reverse = r1 < r0,\n      start = reverse ? r1 : r0,\n      stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = (0, _index.range)(n).map(function (i) {\n      return start + step * i;\n    });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n  scale.range = function (_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n  scale.rangeRound = function (_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n  scale.bandwidth = function () {\n    return bandwidth;\n  };\n  scale.step = function () {\n    return step;\n  };\n  scale.round = function (_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n  scale.padding = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n  scale.paddingInner = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n  scale.paddingOuter = function (_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n  scale.align = function (_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n  scale.copy = function () {\n    return band(domain(), [r0, r1]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);\n  };\n  return _init.initRange.apply(rescale(), arguments);\n}\nfunction pointish(scale) {\n  var copy = scale.copy;\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n  scale.copy = function () {\n    return pointish(copy());\n  };\n  return scale;\n}\nfunction point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "band", "point", "_index", "require", "_init", "_ordinal", "_interopRequireDefault", "obj", "__esModule", "scale", "unknown", "undefined", "domain", "ordinalRange", "range", "r0", "r1", "step", "bandwidth", "round", "paddingInner", "paddingOuter", "align", "rescale", "n", "length", "reverse", "start", "stop", "Math", "max", "floor", "values", "map", "i", "_", "arguments", "rangeRound", "padding", "min", "copy", "initRange", "apply", "pointish"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/band.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = band;\nexports.point = point;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _init = require(\"./init.js\");\n\nvar _ordinal = _interopRequireDefault(require(\"./ordinal.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction band() {\n  var scale = (0, _ordinal.default)().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = (0, _index.range)(n).map(function (i) {\n      return start + step * i;\n    });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function (_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function () {\n    return bandwidth;\n  };\n\n  scale.step = function () {\n    return step;\n  };\n\n  scale.round = function (_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function (_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function (_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function () {\n    return band(domain(), [r0, r1]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);\n  };\n\n  return _init.initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function () {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nfunction point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AACtBH,OAAO,CAACI,KAAK,GAAGA,KAAK;AAErB,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIE,QAAQ,GAAGC,sBAAsB,CAACH,OAAO,CAAC,cAAc,CAAC,CAAC;AAE9D,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAER,OAAO,EAAEQ;EAAI,CAAC;AAAE;AAE9F,SAASP,IAAIA,CAAA,EAAG;EACd,IAAIS,KAAK,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAACN,OAAO,EAAE,CAAC,CAACW,OAAO,CAACC,SAAS,CAAC;IAClDC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,YAAY,GAAGJ,KAAK,CAACK,KAAK;IAC1BC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,IAAI;IACJC,SAAS;IACTC,KAAK,GAAG,KAAK;IACbC,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAG,CAAC;IAChBC,KAAK,GAAG,GAAG;EACf,OAAOb,KAAK,CAACC,OAAO;EAEpB,SAASa,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAGZ,MAAM,CAAC,CAAC,CAACa,MAAM;MACnBC,OAAO,GAAGV,EAAE,GAAGD,EAAE;MACjBY,KAAK,GAAGD,OAAO,GAAGV,EAAE,GAAGD,EAAE;MACzBa,IAAI,GAAGF,OAAO,GAAGX,EAAE,GAAGC,EAAE;IAC5BC,IAAI,GAAG,CAACW,IAAI,GAAGD,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,CAAC,GAAGJ,YAAY,GAAGC,YAAY,GAAG,CAAC,CAAC;IACxE,IAAIF,KAAK,EAAEF,IAAI,GAAGY,IAAI,CAACE,KAAK,CAACd,IAAI,CAAC;IAClCU,KAAK,IAAI,CAACC,IAAI,GAAGD,KAAK,GAAGV,IAAI,IAAIO,CAAC,GAAGJ,YAAY,CAAC,IAAIE,KAAK;IAC3DJ,SAAS,GAAGD,IAAI,IAAI,CAAC,GAAGG,YAAY,CAAC;IACrC,IAAID,KAAK,EAAEQ,KAAK,GAAGE,IAAI,CAACV,KAAK,CAACQ,KAAK,CAAC,EAAET,SAAS,GAAGW,IAAI,CAACV,KAAK,CAACD,SAAS,CAAC;IACvE,IAAIc,MAAM,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAACY,KAAK,EAAEU,CAAC,CAAC,CAACS,GAAG,CAAC,UAAUC,CAAC,EAAE;MACjD,OAAOP,KAAK,GAAGV,IAAI,GAAGiB,CAAC;IACzB,CAAC,CAAC;IACF,OAAOrB,YAAY,CAACa,OAAO,GAAGM,MAAM,CAACN,OAAO,CAAC,CAAC,GAAGM,MAAM,CAAC;EAC1D;EAEAvB,KAAK,CAACG,MAAM,GAAG,UAAUuB,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACX,MAAM,IAAIb,MAAM,CAACuB,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIX,MAAM,CAAC,CAAC;EAC7D,CAAC;EAEDH,KAAK,CAACK,KAAK,GAAG,UAAUqB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACX,MAAM,IAAI,CAACV,EAAE,EAAEC,EAAE,CAAC,GAAGmB,CAAC,EAAEpB,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEO,OAAO,CAAC,CAAC,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACpF,CAAC;EAEDP,KAAK,CAAC4B,UAAU,GAAG,UAAUF,CAAC,EAAE;IAC9B,OAAO,CAACpB,EAAE,EAAEC,EAAE,CAAC,GAAGmB,CAAC,EAAEpB,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEG,KAAK,GAAG,IAAI,EAAEI,OAAO,CAAC,CAAC;EAClE,CAAC;EAEDd,KAAK,CAACS,SAAS,GAAG,YAAY;IAC5B,OAAOA,SAAS;EAClB,CAAC;EAEDT,KAAK,CAACQ,IAAI,GAAG,YAAY;IACvB,OAAOA,IAAI;EACb,CAAC;EAEDR,KAAK,CAACU,KAAK,GAAG,UAAUgB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACX,MAAM,IAAIN,KAAK,GAAG,CAAC,CAACgB,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIJ,KAAK;EAC5D,CAAC;EAEDV,KAAK,CAAC6B,OAAO,GAAG,UAAUH,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACX,MAAM,IAAIL,YAAY,GAAGS,IAAI,CAACU,GAAG,CAAC,CAAC,EAAElB,YAAY,GAAG,CAACc,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIH,YAAY;EACrG,CAAC;EAEDX,KAAK,CAACW,YAAY,GAAG,UAAUe,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACX,MAAM,IAAIL,YAAY,GAAGS,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIH,YAAY;EACrF,CAAC;EAEDX,KAAK,CAACY,YAAY,GAAG,UAAUc,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACX,MAAM,IAAIJ,YAAY,GAAG,CAACc,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIF,YAAY;EACzE,CAAC;EAEDZ,KAAK,CAACa,KAAK,GAAG,UAAUa,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACX,MAAM,IAAIH,KAAK,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAID,KAAK;EACpF,CAAC;EAEDb,KAAK,CAAC+B,IAAI,GAAG,YAAY;IACvB,OAAOxC,IAAI,CAACY,MAAM,CAAC,CAAC,EAAE,CAACG,EAAE,EAAEC,EAAE,CAAC,CAAC,CAACG,KAAK,CAACA,KAAK,CAAC,CAACC,YAAY,CAACA,YAAY,CAAC,CAACC,YAAY,CAACA,YAAY,CAAC,CAACC,KAAK,CAACA,KAAK,CAAC;EACjH,CAAC;EAED,OAAOlB,KAAK,CAACqC,SAAS,CAACC,KAAK,CAACnB,OAAO,CAAC,CAAC,EAAEa,SAAS,CAAC;AACpD;AAEA,SAASO,QAAQA,CAAClC,KAAK,EAAE;EACvB,IAAI+B,IAAI,GAAG/B,KAAK,CAAC+B,IAAI;EACrB/B,KAAK,CAAC6B,OAAO,GAAG7B,KAAK,CAACY,YAAY;EAClC,OAAOZ,KAAK,CAACW,YAAY;EACzB,OAAOX,KAAK,CAACY,YAAY;EAEzBZ,KAAK,CAAC+B,IAAI,GAAG,YAAY;IACvB,OAAOG,QAAQ,CAACH,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,OAAO/B,KAAK;AACd;AAEA,SAASR,KAAKA,CAAA,EAAG;EACf,OAAO0C,QAAQ,CAAC3C,IAAI,CAAC0C,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC,CAAChB,YAAY,CAAC,CAAC,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "script"}