{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar Typography = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n    _a$component = _a.component,\n    Component = _a$component === void 0 ? 'article' : _a$component,\n    className = _a.className,\n    setContentRef = _a.setContentRef,\n    children = _a.children,\n    typographyDirection = _a.direction,\n    restProps = __rest(_a, [\"prefixCls\", \"component\", \"className\", \"setContentRef\", \"children\", \"direction\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    contextDirection = _React$useContext.direction;\n  var direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  var mergedRef = ref;\n  if (setContentRef) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography', '`setContentRef` is deprecated. Please use `ref` instead.') : void 0;\n    mergedRef = composeRef(ref, setContentRef);\n  }\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var componentClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return (/*#__PURE__*/\n    // @ts-expect-error: Expression produces a union type that is too complex to represent.\n    React.createElement(Component, _extends({\n      className: componentClassName,\n      ref: mergedRef\n    }, restProps), children)\n  );\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\n// es default export should use const instead of let\nexport default Typography;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "composeRef", "React", "ConfigContext", "warning", "Typography", "forwardRef", "_a", "ref", "customizePrefixCls", "prefixCls", "_a$component", "component", "Component", "className", "setContentRef", "children", "typographyDirection", "direction", "restProps", "_React$useContext", "useContext", "getPrefixCls", "contextDirection", "mergedRef", "process", "env", "NODE_ENV", "componentClassName", "concat", "createElement", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Typography.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar Typography = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n    _a$component = _a.component,\n    Component = _a$component === void 0 ? 'article' : _a$component,\n    className = _a.className,\n    setContentRef = _a.setContentRef,\n    children = _a.children,\n    typographyDirection = _a.direction,\n    restProps = __rest(_a, [\"prefixCls\", \"component\", \"className\", \"setContentRef\", \"children\", \"direction\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    contextDirection = _React$useContext.direction;\n  var direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  var mergedRef = ref;\n  if (setContentRef) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography', '`setContentRef` is deprecated. Please use `ref` instead.') : void 0;\n    mergedRef = composeRef(ref, setContentRef);\n  }\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var componentClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return (\n    /*#__PURE__*/\n    // @ts-expect-error: Expression produces a union type that is too complex to represent.\n    React.createElement(Component, _extends({\n      className: componentClassName,\n      ref: mergedRef\n    }, restProps), children)\n  );\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\n// es default export should use const instead of let\nexport default Typography;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,UAAU,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAChE,IAAIC,kBAAkB,GAAGF,EAAE,CAACG,SAAS;IACnCC,YAAY,GAAGJ,EAAE,CAACK,SAAS;IAC3BC,SAAS,GAAGF,YAAY,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,YAAY;IAC9DG,SAAS,GAAGP,EAAE,CAACO,SAAS;IACxBC,aAAa,GAAGR,EAAE,CAACQ,aAAa;IAChCC,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IACtBC,mBAAmB,GAAGV,EAAE,CAACW,SAAS;IAClCC,SAAS,GAAGjC,MAAM,CAACqB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;EAC3G,IAAIa,iBAAiB,GAAGlB,KAAK,CAACmB,UAAU,CAAClB,aAAa,CAAC;IACrDmB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,gBAAgB,GAAGH,iBAAiB,CAACF,SAAS;EAChD,IAAIA,SAAS,GAAGD,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGM,gBAAgB;EACvH,IAAIC,SAAS,GAAGhB,GAAG;EACnB,IAAIO,aAAa,EAAE;IACjBU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,0DAA0D,CAAC,GAAG,KAAK,CAAC;IACzIoB,SAAS,GAAGvB,UAAU,CAACO,GAAG,EAAEO,aAAa,CAAC;EAC5C;EACA,IAAIL,SAAS,GAAGY,YAAY,CAAC,YAAY,EAAEb,kBAAkB,CAAC;EAC9D,IAAImB,kBAAkB,GAAG5B,UAAU,CAACU,SAAS,EAAEzB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4C,MAAM,CAACnB,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,KAAK,KAAK,CAAC,EAAEJ,SAAS,CAAC;EACjI,QACE;IACA;IACAZ,KAAK,CAAC4B,aAAa,CAACjB,SAAS,EAAE7B,QAAQ,CAAC;MACtC8B,SAAS,EAAEc,kBAAkB;MAC7BpB,GAAG,EAAEgB;IACP,CAAC,EAAEL,SAAS,CAAC,EAAEH,QAAQ;EAAC;AAE5B,CAAC,CAAC;AACF,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCtB,UAAU,CAAC0B,WAAW,GAAG,YAAY;AACvC;AACA;AACA,eAAe1B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}