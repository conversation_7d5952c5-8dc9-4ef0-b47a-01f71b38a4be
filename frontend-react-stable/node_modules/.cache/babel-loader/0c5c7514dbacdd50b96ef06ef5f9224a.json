{"ast": null, "code": "import warning from '../_util/warning';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent(_ref) {\n  var success = _ref.success,\n    successPercent = _ref.successPercent;\n  var percent = successPercent;\n  /** @deprecated Use `percent` instead */\n  if (success && 'progress' in success) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Progress', '`success.progress` is deprecated. Please use `success.percent` instead.') : void 0;\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}", "map": {"version": 3, "names": ["warning", "validProgress", "progress", "getSuccessPercent", "_ref", "success", "successPercent", "percent", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/progress/utils.js"], "sourcesContent": ["import warning from '../_util/warning';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent(_ref) {\n  var success = _ref.success,\n    successPercent = _ref.successPercent;\n  var percent = successPercent;\n  /** @deprecated Use `percent` instead */\n  if (success && 'progress' in success) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Progress', '`success.progress` is deprecated. Please use `success.percent` instead.') : void 0;\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AACtC,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAE;EACtC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAC7B,OAAO,CAAC;EACV;EACA,IAAIA,QAAQ,GAAG,GAAG,EAAE;IAClB,OAAO,GAAG;EACZ;EACA,OAAOA,QAAQ;AACjB;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACxBC,cAAc,GAAGF,IAAI,CAACE,cAAc;EACtC,IAAIC,OAAO,GAAGD,cAAc;EAC5B;EACA,IAAID,OAAO,IAAI,UAAU,IAAIA,OAAO,EAAE;IACpCG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,yEAAyE,CAAC,GAAG,KAAK,CAAC;IACtJO,OAAO,GAAGF,OAAO,CAACH,QAAQ;EAC5B;EACA,IAAIG,OAAO,IAAI,SAAS,IAAIA,OAAO,EAAE;IACnCE,OAAO,GAAGF,OAAO,CAACE,OAAO;EAC3B;EACA,OAAOA,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}