{"ast": null, "code": "// This icon file is generated automatically.\nvar VerticalAlignBottomOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M859.9 780H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM505.7 669a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V176c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8z\"\n      }\n    }]\n  },\n  \"name\": \"vertical-align-bottom\",\n  \"theme\": \"outlined\"\n};\nexport default VerticalAlignBottomOutlined;", "map": {"version": 3, "names": ["VerticalAlignBottomOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/VerticalAlignBottomOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar VerticalAlignBottomOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M859.9 780H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM505.7 669a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V176c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8z\" } }] }, \"name\": \"vertical-align-bottom\", \"theme\": \"outlined\" };\nexport default VerticalAlignBottomOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,2BAA2B,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAsQ;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,uBAAuB;EAAE,OAAO,EAAE;AAAW,CAAC;AAC7e,eAAeA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}