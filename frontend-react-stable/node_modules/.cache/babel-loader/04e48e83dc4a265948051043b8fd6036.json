{"ast": null, "code": "/* global globalThis */\n/*\n  This file is copied from `core-js`.\n  https://github.com/zloirock/core-js/blob/master/packages/core-js/internals/global.js\n\n  MIT License\n  Author: <PERSON> (@zloirock)\n*/\n\nconst check = function (it) {\n  return it && it.Math == Math && it;\n};\nmodule.exports = check(typeof globalThis == 'object' && globalThis) || check(typeof window == 'object' && window) || check(typeof self == 'object' && self) || check(typeof global == 'object' && global) || Function('return this')();", "map": {"version": 3, "names": ["check", "it", "Math", "module", "exports", "globalThis", "window", "self", "global", "Function"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@pmmmwh/react-refresh-webpack-plugin/client/utils/safeThis.js"], "sourcesContent": ["/* global globalThis */\n/*\n  This file is copied from `core-js`.\n  https://github.com/zloirock/core-js/blob/master/packages/core-js/internals/global.js\n\n  MIT License\n  Author: <PERSON> (@zloirock)\n*/\n\nconst check = function (it) {\n  return it && it.Math == Math && it;\n};\n\nmodule.exports =\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  Function('return this')();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC1B,OAAOA,EAAE,IAAIA,EAAE,CAACC,IAAI,IAAIA,IAAI,IAAID,EAAE;AACpC,CAAC;AAEDE,MAAM,CAACC,OAAO,GACZJ,KAAK,CAAC,OAAOK,UAAU,IAAI,QAAQ,IAAIA,UAAU,CAAC,IAClDL,KAAK,CAAC,OAAOM,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC,IAC1CN,KAAK,CAAC,OAAOO,IAAI,IAAI,QAAQ,IAAIA,IAAI,CAAC,IACtCP,KAAK,CAAC,OAAOQ,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC,IAC1CC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}