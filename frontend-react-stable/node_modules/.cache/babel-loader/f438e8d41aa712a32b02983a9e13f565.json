{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx\";\nimport React from 'react';\nimport { Typography, Card, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst DataQueryPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6570\\u636E\\u67E5\\u8BE2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u67E5\\u8BE2\\u6D41\\u91CF\\u5206\\u6790\\u6A21\\u5757\\u751F\\u6210\\u7684CSV\\u6587\\u4EF6\\u548C\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\u9884\\u6D4B\\u7684\\u7279\\u5F81\\u503C\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D\",\n        description: \"\\u6570\\u636E\\u67E5\\u8BE2\\u9875\\u9762\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85...\",\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = DataQueryPage;\nexport default DataQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DataQueryPage\");", "map": {"version": 3, "names": ["React", "Typography", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Title", "Text", "DataQueryPage", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "style", "marginTop", "message", "description", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Card, Alert } from 'antd';\n\nconst { Title, Text } = Typography;\n\nconst DataQueryPage: React.FC = () => {\n  return (\n    <div>\n      <Title level={2}>数据查询</Title>\n      <Text type=\"secondary\">\n        查询流量分析模块生成的CSV文件和流量检测模型预测的特征值。\n      </Text>\n      \n      <Card style={{ marginTop: 24 }}>\n        <Alert\n          message=\"功能开发中\"\n          description=\"数据查询页面正在开发中，敬请期待...\"\n          type=\"info\"\n          showIcon\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default DataQueryPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGN,UAAU;AAElC,MAAMO,aAAuB,GAAGA,CAAA,KAAM;EACpC,oBACEH,OAAA;IAAAI,QAAA,gBACEJ,OAAA,CAACC,KAAK;MAACI,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC7BT,OAAA,CAACE,IAAI;MAACQ,IAAI,EAAC,WAAW;MAAAN,QAAA,EAAC;IAEvB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPT,OAAA,CAACH,IAAI;MAACc,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAR,QAAA,eAC7BJ,OAAA,CAACF,KAAK;QACJe,OAAO,EAAC,gCAAO;QACfC,WAAW,EAAC,qGAAqB;QACjCJ,IAAI,EAAC,MAAM;QACXK,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACO,EAAA,GAlBIb,aAAuB;AAoB7B,eAAeA,aAAa;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}