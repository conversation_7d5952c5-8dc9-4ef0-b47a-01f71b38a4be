{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = mean;\nfunction mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "mean", "values", "valueof", "count", "sum", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/mean.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = mean;\n\nfunction mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n\n  if (count) return sum / count;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,SAASA,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7B,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,CAAC;EAEX,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,IAAIP,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAIH,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9C,EAAEK,KAAK,EAAEC,GAAG,IAAIN,KAAK;MACvB;IACF;EACF,CAAC,MAAM;IACL,IAAIQ,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIR,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEQ,KAAK,EAAEL,MAAM,CAAC,KAAK,IAAI,IAAI,CAACH,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClF,EAAEK,KAAK,EAAEC,GAAG,IAAIN,KAAK;MACvB;IACF;EACF;EAEA,IAAIK,KAAK,EAAE,OAAOC,GAAG,GAAGD,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}