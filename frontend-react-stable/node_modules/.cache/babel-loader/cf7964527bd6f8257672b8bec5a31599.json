{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"containerWidth\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    containerWidth = props.containerWidth,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var builtInPlacements = React.useMemo(function () {\n    return getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // ======================= Ref =======================\n  var popupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        return popupRef.current;\n      }\n    };\n  });\n  var popupStyle = _objectSpread({\n    minWidth: containerWidth\n  }, dropdownStyle);\n  if (typeof dropdownMatchSelectWidth === 'number') {\n    popupStyle.width = dropdownMatchSelectWidth;\n  } else if (dropdownMatchSelectWidth) {\n    popupStyle.width = containerWidth;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: builtInPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      ref: popupRef,\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nRefSelectTrigger.displayName = 'SelectTrigger';\nexport default RefSelectTrigger;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_objectWithoutProperties", "_excluded", "React", "<PERSON><PERSON>", "classNames", "getBuiltInPlacements", "dropdownMatchSelectWidth", "adjustX", "bottomLeft", "points", "offset", "overflow", "adjustY", "bottomRight", "topLeft", "topRight", "SelectTrigger", "props", "ref", "prefixCls", "disabled", "visible", "children", "popupElement", "containerWidth", "animation", "transitionName", "dropdownStyle", "dropdownClassName", "_props$direction", "direction", "placement", "dropdownRender", "dropdownAlign", "getPopupContainer", "empty", "getTriggerDOMNode", "onPopupVisibleChange", "onPopupMouseEnter", "restProps", "dropdownPrefixCls", "concat", "popupNode", "builtInPlacements", "useMemo", "mergedTransitionName", "popupRef", "useRef", "useImperativeHandle", "getPopupElement", "current", "popupStyle", "min<PERSON><PERSON><PERSON>", "width", "createElement", "showAction", "hideAction", "popupPlacement", "builtinPlacements", "popupTransitionName", "popup", "onMouseEnter", "popupAlign", "popupVisible", "popupClassName", "RefSelectTrigger", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/SelectTrigger.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"containerWidth\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      }\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    containerWidth = props.containerWidth,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var builtInPlacements = React.useMemo(function () {\n    return getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // ======================= Ref =======================\n  var popupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        return popupRef.current;\n      }\n    };\n  });\n  var popupStyle = _objectSpread({\n    minWidth: containerWidth\n  }, dropdownStyle);\n  if (typeof dropdownMatchSelectWidth === 'number') {\n    popupStyle.width = dropdownMatchSelectWidth;\n  } else if (dropdownMatchSelectWidth) {\n    popupStyle.width = containerWidth;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: builtInPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      ref: popupRef,\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nRefSelectTrigger.displayName = 'SelectTrigger';\nexport default RefSelectTrigger;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,OAAO,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;AAChW,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,wBAAwB,EAAE;EACjF;EACA,IAAIC,OAAO,GAAGD,wBAAwB,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EACvD,OAAO;IACLE,UAAU,EAAE;MACVC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX;IACF,CAAC;IACDC,WAAW,EAAE;MACXJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX;IACF,CAAC;IACDE,OAAO,EAAE;MACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX;IACF,CAAC;IACDG,QAAQ,EAAE;MACRN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAII,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,cAAc,GAAGT,KAAK,CAACS,cAAc;IACrCC,aAAa,GAAGV,KAAK,CAACU,aAAa;IACnCC,iBAAiB,GAAGX,KAAK,CAACW,iBAAiB;IAC3CC,gBAAgB,GAAGZ,KAAK,CAACa,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BzB,wBAAwB,GAAGW,KAAK,CAACX,wBAAwB;IACzD0B,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,aAAa,GAAGhB,KAAK,CAACgB,aAAa;IACnCC,iBAAiB,GAAGjB,KAAK,CAACiB,iBAAiB;IAC3CC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,oBAAoB,GAAGpB,KAAK,CAACoB,oBAAoB;IACjDC,iBAAiB,GAAGrB,KAAK,CAACqB,iBAAiB;IAC3CC,SAAS,GAAGvC,wBAAwB,CAACiB,KAAK,EAAEhB,SAAS,CAAC;EACxD,IAAIuC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,WAAW,CAAC;EACzD,IAAIuB,SAAS,GAAGnB,YAAY;EAC5B,IAAIS,cAAc,EAAE;IAClBU,SAAS,GAAGV,cAAc,CAACT,YAAY,CAAC;EAC1C;EACA,IAAIoB,iBAAiB,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,YAAY;IAChD,OAAOvC,oBAAoB,CAACC,wBAAwB,CAAC;EACvD,CAAC,EAAE,CAACA,wBAAwB,CAAC,CAAC;;EAE9B;EACA,IAAIuC,oBAAoB,GAAGpB,SAAS,GAAG,EAAE,CAACgB,MAAM,CAACD,iBAAiB,EAAE,GAAG,CAAC,CAACC,MAAM,CAAChB,SAAS,CAAC,GAAGC,cAAc;;EAE3G;EACA,IAAIoB,QAAQ,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACjC7C,KAAK,CAAC8C,mBAAmB,CAAC9B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL+B,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,OAAOH,QAAQ,CAACI,OAAO;MACzB;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGpD,aAAa,CAAC;IAC7BqD,QAAQ,EAAE5B;EACZ,CAAC,EAAEG,aAAa,CAAC;EACjB,IAAI,OAAOrB,wBAAwB,KAAK,QAAQ,EAAE;IAChD6C,UAAU,CAACE,KAAK,GAAG/C,wBAAwB;EAC7C,CAAC,MAAM,IAAIA,wBAAwB,EAAE;IACnC6C,UAAU,CAACE,KAAK,GAAG7B,cAAc;EACnC;EACA,OAAO,aAAatB,KAAK,CAACoD,aAAa,CAACnD,OAAO,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAE0C,SAAS,EAAE;IACvEgB,UAAU,EAAElB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDmB,UAAU,EAAEnB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDoB,cAAc,EAAE1B,SAAS,KAAKD,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,YAAY,CAAC;IACjF4B,iBAAiB,EAAEf,iBAAiB;IACpCxB,SAAS,EAAEqB,iBAAiB;IAC5BmB,mBAAmB,EAAEd,oBAAoB;IACzCe,KAAK,EAAE,aAAa1D,KAAK,CAACoD,aAAa,CAAC,KAAK,EAAE;MAC7CpC,GAAG,EAAE4B,QAAQ;MACbe,YAAY,EAAEvB;IAChB,CAAC,EAAEI,SAAS,CAAC;IACboB,UAAU,EAAE7B,aAAa;IACzB8B,YAAY,EAAE1C,OAAO;IACrBa,iBAAiB,EAAEA,iBAAiB;IACpC8B,cAAc,EAAE5D,UAAU,CAACwB,iBAAiB,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2C,MAAM,CAACD,iBAAiB,EAAE,QAAQ,CAAC,EAAEL,KAAK,CAAC,CAAC;IACjHgB,UAAU,EAAEA,UAAU;IACtBf,iBAAiB,EAAEA,iBAAiB;IACpCC,oBAAoB,EAAEA;EACxB,CAAC,CAAC,EAAEf,QAAQ,CAAC;AACf,CAAC;AACD,IAAI2C,gBAAgB,GAAG,aAAa/D,KAAK,CAACgE,UAAU,CAAClD,aAAa,CAAC;AACnEiD,gBAAgB,CAACE,WAAW,GAAG,eAAe;AAC9C,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}