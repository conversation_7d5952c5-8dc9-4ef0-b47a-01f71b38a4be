{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, BarChartOutlined, ExperimentOutlined, AimOutlined, DatabaseOutlined, FileTextOutlined, SearchOutlined, UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst MainLayout = ({\n  children\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n  const menuItems = [{\n    key: '/data-cleaning',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    label: '流量分析'\n  }, {\n    key: '/model-training',\n    icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this),\n    label: '模型训练'\n  }, {\n    key: '/model-prediction',\n    icon: /*#__PURE__*/_jsxDEV(AimOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this),\n    label: '模型预测'\n  }, {\n    key: '/model-registry',\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this),\n    label: '模型仓库'\n  }, {\n    key: '/clean-template',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    label: '清洗模板'\n  }, {\n    key: '/data-query',\n    icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this),\n    label: '数据查询'\n  }, {\n    key: '/user-management',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    label: '用户管理'\n  }];\n  const handleMenuClick = key => {\n    navigate(key);\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    label: '个人信息'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 13\n    }, this),\n    label: '设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: sidebarCollapsed,\n      width: 200,\n      style: {\n        background: '#fff',\n        boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n        position: 'fixed',\n        left: 0,\n        top: 0,\n        bottom: 0,\n        zIndex: 1000,\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '64px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: ({\n          key\n        }) => handleMenuClick(key),\n        style: {\n          border: 'none',\n          flex: 1\n        },\n        className: sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: sidebarCollapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 63\n          }, this),\n          onClick: () => dispatch(toggleSidebar()),\n          className: \"sidebar-toggle-btn\",\n          style: {\n            fontSize: '16px',\n            width: '100%',\n            height: 40,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          title: sidebarCollapsed ? '展开菜单' : '收起菜单'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: `0 24px 0 ${(sidebarCollapsed ? 80 : 200) + 16}px`,\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px',\n          transition: 'padding-left 0.2s'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          style: {\n            margin: 0,\n            color: '#1890ff',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(24, 144, 255, 0.1)'\n          },\n          children: \"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user === null || user === void 0 ? void 0 : user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`,\n          // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden',\n          // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"mmzqcQVSMbOYssWaIw2AtJ2TNCY=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useEffect", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "Dropdown", "Space", "Typography", "MenuFoldOutlined", "MenuUnfoldOutlined", "BarChartOutlined", "ExperimentOutlined", "AimOutlined", "DatabaseOutlined", "FileTextOutlined", "SearchOutlined", "UserOutlined", "LogoutOutlined", "SettingOutlined", "useNavigate", "useLocation", "useSelector", "useDispatch", "logout", "restoreAuth", "toggleSidebar", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "MainLayout", "children", "_s", "navigate", "location", "dispatch", "user", "state", "auth", "sidebarCollapsed", "ui", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "style", "minHeight", "trigger", "collapsible", "collapsed", "width", "background", "boxShadow", "position", "left", "top", "bottom", "zIndex", "overflow", "display", "flexDirection", "height", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "border", "flex", "className", "padding", "borderTop", "textAlign", "fontSize", "alignItems", "justifyContent", "title", "right", "transition", "level", "margin", "color", "fontWeight", "letterSpacing", "textShadow", "menu", "placement", "arrow", "cursor", "username", "borderRadius", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  ExperimentOutlined,\n  AimOutlined,\n  DatabaseOutlined,\n  FileTextOutlined,\n  SearchOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const { user } = useSelector((state: RootState) => state.auth);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n\n\n\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n\n\n  const menuItems = [\n    {\n      key: '/data-cleaning',\n      icon: <BarChartOutlined />,\n      label: '流量分析',\n    },\n    {\n      key: '/model-training',\n      icon: <ExperimentOutlined />,\n      label: '模型训练',\n    },\n    {\n      key: '/model-prediction',\n      icon: <AimOutlined />,\n      label: '模型预测',\n    },\n    {\n      key: '/model-registry',\n      icon: <DatabaseOutlined />,\n      label: '模型仓库',\n    },\n    {\n      key: '/clean-template',\n      icon: <FileTextOutlined />,\n      label: '清洗模板',\n    },\n    {\n      key: '/data-query',\n      icon: <SearchOutlined />,\n      label: '数据查询',\n    },\n    {\n      key: '/user-management',\n      icon: <UserOutlined />,\n      label: '用户管理',\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={200}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          left: 0,\n          top: 0,\n          bottom: 0,\n          zIndex: 1000,\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n        }}\n      >\n        {/* 顶部留空区域，为Header让出空间 */}\n        <div style={{\n          height: '64px',\n        }}></div>\n\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          style={{\n            border: 'none',\n            flex: 1,\n          }}\n          className={sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'}\n        />\n\n        {/* 回缩按钮移到侧边栏底部 */}\n        <div style={{\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center',\n        }}>\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => dispatch(toggleSidebar())}\n            className=\"sidebar-toggle-btn\"\n            style={{\n              fontSize: '16px',\n              width: '100%',\n              height: 40,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n            title={sidebarCollapsed ? '展开菜单' : '收起菜单'}\n          />\n        </div>\n\n        {/* 强制应用选中项样式 */}\n        <style>{`\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `}</style>\n\n      </Sider>\n\n      <Layout>\n        <Header style={{\n          padding: `0 24px 0 ${(sidebarCollapsed ? 80 : 200) + 16}px`,\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px',\n          transition: 'padding-left 0.2s',\n        }}>\n          <Title level={3} style={{\n            margin: 0,\n            color: '#1890ff',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(24, 144, 255, 0.1)',\n          }}>\n            AI智能清洗策略系统\n          </Title>\n\n          <Space>\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>{user?.username}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`, // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden', // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAChF,OAAO,kBAAkB;AACzB,OAAO,eAAe;AACtB,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,WAAW,EACXC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,MAAM,EAAEC,WAAW,QAAQ,8BAA8B;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG7B,MAAM;AACzC,MAAM;EAAE8B;AAAM,CAAC,GAAGxB,UAAU;AAM5B,MAAMyB,UAAqC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEgB;EAAK,CAAC,GAAGjB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC9D,MAAM;IAAEC;EAAiB,CAAC,GAAGpB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EAIxE1C,SAAS,CAAC,MAAM;IACd;IACAqC,QAAQ,CAACb,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACa,QAAQ,CAAC,CAAC;EAId,MAAMM,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,gBAAgB;IACrBC,IAAI,eAAElB,OAAA,CAACjB,gBAAgB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAAChB,kBAAkB;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAElB,OAAA,CAACf,WAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACd,gBAAgB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACb,gBAAgB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAElB,OAAA,CAACZ,cAAc;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAElB,OAAA,CAACX,YAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIP,GAAW,IAAK;IACvCT,QAAQ,CAACS,GAAG,CAAC;EACf,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBf,QAAQ,CAACd,MAAM,CAAC,CAAC,CAAC;IAClBY,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMkB,aAAa,GAAG,CACpB;IACET,GAAG,EAAE,SAAS;IACdC,IAAI,eAAElB,OAAA,CAACX,YAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAElB,OAAA,CAACT,eAAe;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,IAAI,EAAE;EACR,CAAC,EACD;IACEV,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAElB,OAAA,CAACV,cAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbK,OAAO,EAAEH;EACX,CAAC,CACF;EAED,oBACEzB,OAAA,CAAC1B,MAAM;IAACuD,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAxB,QAAA,gBACpCN,OAAA,CAACE,KAAK;MACJ6B,OAAO,EAAE,IAAK;MACdC,WAAW;MACXC,SAAS,EAAEnB,gBAAiB;MAC5BoB,KAAK,EAAE,GAAI;MACXL,KAAK,EAAE;QACLM,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;MACjB,CAAE;MAAAtC,QAAA,gBAGFN,OAAA;QAAK6B,KAAK,EAAE;UACVgB,MAAM,EAAE;QACV;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAETtB,OAAA,CAACzB,IAAI;QACHuE,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACtC,QAAQ,CAACuC,QAAQ,CAAE;QAClCC,KAAK,EAAEjC,SAAU;QACjBY,OAAO,EAAEA,CAAC;UAAEX;QAAI,CAAC,KAAKO,eAAe,CAACP,GAAG,CAAE;QAC3CY,KAAK,EAAE;UACLqB,MAAM,EAAE,MAAM;UACdC,IAAI,EAAE;QACR,CAAE;QACFC,SAAS,EAAEtC,gBAAgB,GAAG,gBAAgB,GAAG;MAAgB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAGFtB,OAAA;QAAK6B,KAAK,EAAE;UACVwB,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,mBAAmB;UAC9BC,SAAS,EAAE;QACb,CAAE;QAAAjD,QAAA,eACAN,OAAA,CAACxB,MAAM;UACLmD,IAAI,EAAC,MAAM;UACXT,IAAI,EAAEJ,gBAAgB,gBAAGd,OAAA,CAAClB,kBAAkB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACnB,gBAAgB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvEM,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAACZ,aAAa,CAAC,CAAC,CAAE;UACzCsD,SAAS,EAAC,oBAAoB;UAC9BvB,KAAK,EAAE;YACL2B,QAAQ,EAAE,MAAM;YAChBtB,KAAK,EAAE,MAAM;YACbW,MAAM,EAAE,EAAE;YACVF,OAAO,EAAE,MAAM;YACfc,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UACFC,KAAK,EAAE7C,gBAAgB,GAAG,MAAM,GAAG;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtB,OAAA;QAAAM,QAAA,EAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CAAC,eAERtB,OAAA,CAAC1B,MAAM;MAAAgC,QAAA,gBACLN,OAAA,CAACC,MAAM;QAAC4B,KAAK,EAAE;UACbwB,OAAO,EAAE,YAAY,CAACvC,gBAAgB,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,IAAI;UAC3DqB,UAAU,EAAE,MAAM;UAClBQ,OAAO,EAAE,MAAM;UACfc,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BtB,SAAS,EAAE,2BAA2B;UACtCC,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,CAAC;UACNqB,KAAK,EAAE,CAAC;UACRtB,IAAI,EAAE,CAAC;UACPG,MAAM,EAAE,IAAI;UACZI,MAAM,EAAE,MAAM;UACdgB,UAAU,EAAE;QACd,CAAE;QAAAvD,QAAA,gBACAN,OAAA,CAACI,KAAK;UAAC0D,KAAK,EAAE,CAAE;UAACjC,KAAK,EAAE;YACtBkC,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBR,QAAQ,EAAE,MAAM;YAChBS,UAAU,EAAE,GAAG;YACfC,aAAa,EAAE,OAAO;YACtBC,UAAU,EAAE;UACd,CAAE;UAAA7D,QAAA,EAAC;QAEH;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERtB,OAAA,CAACrB,KAAK;UAAA2B,QAAA,eACJN,OAAA,CAACtB,QAAQ;YACP0F,IAAI,EAAE;cAAEnB,KAAK,EAAEvB;YAAc,CAAE;YAC/B2C,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAAhE,QAAA,eAELN,OAAA,CAACrB,KAAK;cAACkD,KAAK,EAAE;gBAAE0C,MAAM,EAAE;cAAU,CAAE;cAAAjE,QAAA,gBAClCN,OAAA,CAACvB,MAAM;gBAACyC,IAAI,eAAElB,OAAA,CAACX,YAAY;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCtB,OAAA;gBAAAM,QAAA,EAAOK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D;cAAQ;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETtB,OAAA,CAACG,OAAO;QAAC0B,KAAK,EAAE;UACdkC,MAAM,EAAE,kBAAkB,CAACjD,gBAAgB,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,IAAI;UAAE;UAClEuC,OAAO,EAAE,EAAE;UACXlB,UAAU,EAAE,MAAM;UAClBsC,YAAY,EAAE,KAAK;UACnB3C,SAAS,EAAE,qBAAqB;UAChCY,QAAQ,EAAE,QAAQ;UAAE;UACpBmB,UAAU,EAAE;QACd,CAAE;QAAAvD,QAAA,EACCA;MAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACf,EAAA,CAjOIF,UAAqC;EAAA,QACxBb,WAAW,EACXC,WAAW,EACXE,WAAW,EAEXD,WAAW,EACCA,WAAW;AAAA;AAAAgF,EAAA,GANpCrE,UAAqC;AAmO3C,eAAeA,UAAU;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}