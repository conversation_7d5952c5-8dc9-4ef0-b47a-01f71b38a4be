{"ast": null, "code": "import * as React from 'react';\nimport SliderContext from '../context';\nimport Dot from './Dot';\nexport default function Steps(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    dots = props.dots,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    step = _React$useContext.step;\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set(); // Add marks\n\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    }); // Fill dots\n\n    if (dots && step !== null) {\n      var current = min;\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n}", "map": {"version": 3, "names": ["React", "SliderContext", "Dot", "Steps", "props", "prefixCls", "marks", "dots", "style", "activeStyle", "_React$useContext", "useContext", "min", "max", "step", "stepDots", "useMemo", "dotSet", "Set", "for<PERSON>ach", "mark", "add", "value", "current", "Array", "from", "createElement", "className", "concat", "map", "dotValue", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/Steps/index.js"], "sourcesContent": ["import * as React from 'react';\nimport SliderContext from '../context';\nimport Dot from './Dot';\nexport default function Steps(props) {\n  var prefixCls = props.prefixCls,\n      marks = props.marks,\n      dots = props.dots,\n      style = props.style,\n      activeStyle = props.activeStyle;\n\n  var _React$useContext = React.useContext(SliderContext),\n      min = _React$useContext.min,\n      max = _React$useContext.max,\n      step = _React$useContext.step;\n\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set(); // Add marks\n\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    }); // Fill dots\n\n    if (dots && step !== null) {\n      var current = min;\n\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,OAAOC,GAAG,MAAM,OAAO;AACvB,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,WAAW,GAAGL,KAAK,CAACK,WAAW;EAEnC,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,aAAa,CAAC;IACnDW,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;EAEjC,IAAIC,QAAQ,GAAGf,KAAK,CAACgB,OAAO,CAAC,YAAY;IACvC,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAExBZ,KAAK,CAACa,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC5BH,MAAM,CAACI,GAAG,CAACD,IAAI,CAACE,KAAK,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIf,IAAI,IAAIO,IAAI,KAAK,IAAI,EAAE;MACzB,IAAIS,OAAO,GAAGX,GAAG;MAEjB,OAAOW,OAAO,IAAIV,GAAG,EAAE;QACrBI,MAAM,CAACI,GAAG,CAACE,OAAO,CAAC;QACnBA,OAAO,IAAIT,IAAI;MACjB;IACF;IAEA,OAAOU,KAAK,CAACC,IAAI,CAACR,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACL,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEP,IAAI,EAAED,KAAK,CAAC,CAAC;EACjC,OAAO,aAAaN,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,OAAO;EACzC,CAAC,EAAEU,QAAQ,CAACc,GAAG,CAAC,UAAUC,QAAQ,EAAE;IAClC,OAAO,aAAa9B,KAAK,CAAC0B,aAAa,CAACxB,GAAG,EAAE;MAC3CG,SAAS,EAAEA,SAAS;MACpB0B,GAAG,EAAED,QAAQ;MACbR,KAAK,EAAEQ,QAAQ;MACftB,KAAK,EAAEA,KAAK;MACZC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module"}