{"ast": null, "code": "import * as React from 'react';\nvar isValidElement = React.isValidElement;\nexport { isValidElement };\nexport function isFragment(child) {\n  return child && isValidElement(child) && child.type === React.Fragment;\n}\nexport function replaceElement(element, replacement, props) {\n  if (!isValidElement(element)) {\n    return replacement;\n  }\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n}\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}", "map": {"version": 3, "names": ["React", "isValidElement", "isFragment", "child", "type", "Fragment", "replaceElement", "element", "replacement", "props", "cloneElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/reactNode.js"], "sourcesContent": ["import * as React from 'react';\nvar isValidElement = React.isValidElement;\nexport { isValidElement };\nexport function isFragment(child) {\n  return child && isValidElement(child) && child.type === React.Fragment;\n}\nexport function replaceElement(element, replacement, props) {\n  if (!isValidElement(element)) {\n    return replacement;\n  }\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n}\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;AACzC,SAASA,cAAc;AACvB,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOA,KAAK,IAAIF,cAAc,CAACE,KAAK,CAAC,IAAIA,KAAK,CAACC,IAAI,KAAKJ,KAAK,CAACK,QAAQ;AACxE;AACA,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAC1D,IAAI,CAACR,cAAc,CAACM,OAAO,CAAC,EAAE;IAC5B,OAAOC,WAAW;EACpB;EACA,OAAO,aAAaR,KAAK,CAACU,YAAY,CAACH,OAAO,EAAE,OAAOE,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACF,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC;AACnH;AACA,OAAO,SAASC,YAAYA,CAACH,OAAO,EAAEE,KAAK,EAAE;EAC3C,OAAOH,cAAc,CAACC,OAAO,EAAEA,OAAO,EAAEE,KAAK,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}