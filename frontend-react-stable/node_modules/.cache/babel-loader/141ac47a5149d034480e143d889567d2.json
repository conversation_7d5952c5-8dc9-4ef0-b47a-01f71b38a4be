{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _appearance = _interopRequireDefault(require(\"./appearance.js\"));\nvar _ascending = require(\"./ascending.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series) {\n  var n = series.length,\n    i,\n    j,\n    sums = series.map(_ascending.sum),\n    order = (0, _appearance.default)(series),\n    top = 0,\n    bottom = 0,\n    tops = [],\n    bottoms = [];\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n  return bottoms.reverse().concat(tops);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_appearance", "_interopRequireDefault", "require", "_ascending", "obj", "__esModule", "series", "n", "length", "i", "j", "sums", "map", "sum", "order", "top", "bottom", "tops", "bottoms", "push", "reverse", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/order/insideOut.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _appearance = _interopRequireDefault(require(\"./appearance.js\"));\n\nvar _ascending = require(\"./ascending.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(_ascending.sum),\n      order = (0, _appearance.default)(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,WAAW,GAAGC,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEpE,IAAIC,UAAU,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAE1C,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,QAAQA,CAACO,MAAM,EAAE;EACxB,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;IACjBC,CAAC;IACDC,CAAC;IACDC,IAAI,GAAGL,MAAM,CAACM,GAAG,CAACT,UAAU,CAACU,GAAG,CAAC;IACjCC,KAAK,GAAG,CAAC,CAAC,EAAEd,WAAW,CAACF,OAAO,EAAEQ,MAAM,CAAC;IACxCS,GAAG,GAAG,CAAC;IACPC,MAAM,GAAG,CAAC;IACVC,IAAI,GAAG,EAAE;IACTC,OAAO,GAAG,EAAE;EAEhB,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IACtBC,CAAC,GAAGI,KAAK,CAACL,CAAC,CAAC;IAEZ,IAAIM,GAAG,GAAGC,MAAM,EAAE;MAChBD,GAAG,IAAIJ,IAAI,CAACD,CAAC,CAAC;MACdO,IAAI,CAACE,IAAI,CAACT,CAAC,CAAC;IACd,CAAC,MAAM;MACLM,MAAM,IAAIL,IAAI,CAACD,CAAC,CAAC;MACjBQ,OAAO,CAACC,IAAI,CAACT,CAAC,CAAC;IACjB;EACF;EAEA,OAAOQ,OAAO,CAACE,OAAO,CAAC,CAAC,CAACC,MAAM,CAACJ,IAAI,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}