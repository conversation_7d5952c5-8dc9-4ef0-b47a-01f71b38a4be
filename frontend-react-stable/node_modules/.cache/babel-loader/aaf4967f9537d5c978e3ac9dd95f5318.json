{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  var type = _typeof(node);\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  var totalLen = 0;\n  nodeList.forEach(function (node) {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  var currLen = 0;\n  var currentNodeList = [];\n  for (var i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    var node = nodeList[i];\n    var canCut = cuttable(node);\n    var nodeLen = canCut ? String(node).length : 1;\n    var nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      var restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nvar NONE = 0;\nvar PREPARE = 1;\nvar WALKING = 2;\nvar DONE_WITH_ELLIPSIS = 3;\nvar DONE_WITHOUT_ELLIPSIS = 4;\nvar Ellipsis = function Ellipsis(_ref) {\n  var enabledMeasure = _ref.enabledMeasure,\n    children = _ref.children,\n    text = _ref.text,\n    width = _ref.width,\n    fontSize = _ref.fontSize,\n    rows = _ref.rows,\n    onEllipsis = _ref.onEllipsis;\n  var _React$useState = React.useState([0, 0, 0]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    _React$useState2$ = _slicedToArray(_React$useState2[0], 3),\n    startLen = _React$useState2$[0],\n    midLen = _React$useState2$[1],\n    endLen = _React$useState2$[2],\n    setCutLength = _React$useState2[1];\n  var _React$useState3 = React.useState(NONE),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    walkingState = _React$useState4[0],\n    setWalkingState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    singleRowHeight = _React$useState6[0],\n    setSingleRowHeight = _React$useState6[1];\n  var singleRowRef = React.useRef(null);\n  var midRowRef = React.useRef(null);\n  var nodeList = React.useMemo(function () {\n    return toArray(text);\n  }, [text]);\n  var totalLen = React.useMemo(function () {\n    return getNodesLen(nodeList);\n  }, [nodeList]);\n  var mergedChildren = React.useMemo(function () {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(function () {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(function () {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(function () {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        var midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        var maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          var _midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          var _maxHeight = rows * singleRowHeight;\n          var nextStartLen = startLen;\n          var nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (_midHeight <= _maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          var nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  var measureStyle = {\n    width: width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  var renderMeasure = function renderMeasure(content, ref, style) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      ref: ref,\n      style: _extends({\n        position: 'fixed',\n        display: 'block',\n        left: 0,\n        top: 0,\n        zIndex: -9999,\n        visibility: 'hidden',\n        pointerEvents: 'none',\n        fontSize: Math.floor(fontSize / 2) * 2\n      }, style)\n    }, content);\n  };\n  var renderMeasureSlice = function renderMeasureSlice(len, ref) {\n    var sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_typeof", "toArray", "useIsomorphicLayoutEffect", "React", "cuttable", "node", "type", "getNodesLen", "nodeList", "totalLen", "for<PERSON>ach", "String", "length", "sliceNodes", "len", "currLen", "currentNodeList", "i", "canCut", "nodeLen", "nextLen", "restLen", "push", "slice", "NONE", "PREPARE", "WALKING", "DONE_WITH_ELLIPSIS", "DONE_WITHOUT_ELLIPSIS", "El<PERSON><PERSON>", "_ref", "enabledMeasure", "children", "text", "width", "fontSize", "rows", "onEllipsis", "_React$useState", "useState", "_React$useState2", "_React$useState2$", "startLen", "midLen", "endLen", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$useState3", "_React$useState4", "walkingState", "setWalkingState", "_React$useState5", "_React$useState6", "singleRowHeight", "setSingleRowHeight", "singleRowRef", "useRef", "midRowRef", "useMemo", "mergedChildren", "Math", "ceil", "_a", "current", "offsetHeight", "_b", "midHeight", "maxHeight", "_midHeight", "_maxHeight", "nextStartLen", "nextEndLen", "nextMidLen", "measureStyle", "whiteSpace", "margin", "padding", "renderMeasure", "content", "ref", "style", "createElement", "position", "display", "left", "top", "zIndex", "visibility", "pointerEvents", "floor", "renderMeasureSlice", "sliceNodeList", "Fragment", "wordBreak", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Base/Ellipsis.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  var type = _typeof(node);\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  var totalLen = 0;\n  nodeList.forEach(function (node) {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  var currLen = 0;\n  var currentNodeList = [];\n  for (var i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    var node = nodeList[i];\n    var canCut = cuttable(node);\n    var nodeLen = canCut ? String(node).length : 1;\n    var nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      var restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nvar NONE = 0;\nvar PREPARE = 1;\nvar WALKING = 2;\nvar DONE_WITH_ELLIPSIS = 3;\nvar DONE_WITHOUT_ELLIPSIS = 4;\nvar Ellipsis = function Ellipsis(_ref) {\n  var enabledMeasure = _ref.enabledMeasure,\n    children = _ref.children,\n    text = _ref.text,\n    width = _ref.width,\n    fontSize = _ref.fontSize,\n    rows = _ref.rows,\n    onEllipsis = _ref.onEllipsis;\n  var _React$useState = React.useState([0, 0, 0]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    _React$useState2$ = _slicedToArray(_React$useState2[0], 3),\n    startLen = _React$useState2$[0],\n    midLen = _React$useState2$[1],\n    endLen = _React$useState2$[2],\n    setCutLength = _React$useState2[1];\n  var _React$useState3 = React.useState(NONE),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    walkingState = _React$useState4[0],\n    setWalkingState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    singleRowHeight = _React$useState6[0],\n    setSingleRowHeight = _React$useState6[1];\n  var singleRowRef = React.useRef(null);\n  var midRowRef = React.useRef(null);\n  var nodeList = React.useMemo(function () {\n    return toArray(text);\n  }, [text]);\n  var totalLen = React.useMemo(function () {\n    return getNodesLen(nodeList);\n  }, [nodeList]);\n  var mergedChildren = React.useMemo(function () {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(function () {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(function () {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(function () {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        var midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        var maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          var _midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          var _maxHeight = rows * singleRowHeight;\n          var nextStartLen = startLen;\n          var nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (_midHeight <= _maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          var nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  var measureStyle = {\n    width: width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  var renderMeasure = function renderMeasure(content, ref, style) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      ref: ref,\n      style: _extends({\n        position: 'fixed',\n        display: 'block',\n        left: 0,\n        top: 0,\n        zIndex: -9999,\n        visibility: 'hidden',\n        pointerEvents: 'none',\n        fontSize: Math.floor(fontSize / 2) * 2\n      }, style)\n    }, content);\n  };\n  var renderMeasureSlice = function renderMeasureSlice(len, ref) {\n    var sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAGN,OAAO,CAACK,IAAI,CAAC;EACxB,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;AAC/C;AACA,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,IAAIC,QAAQ,GAAG,CAAC;EAChBD,QAAQ,CAACE,OAAO,CAAC,UAAUL,IAAI,EAAE;IAC/B,IAAID,QAAQ,CAACC,IAAI,CAAC,EAAE;MAClBI,QAAQ,IAAIE,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM;IACjC,CAAC,MAAM;MACLH,QAAQ,IAAI,CAAC;IACf;EACF,CAAC,CAAC;EACF,OAAOA,QAAQ;AACjB;AACA,SAASI,UAAUA,CAACL,QAAQ,EAAEM,GAAG,EAAE;EACjC,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,eAAe,GAAG,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,QAAQ,CAACI,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC3C;IACA,IAAIF,OAAO,KAAKD,GAAG,EAAE;MACnB,OAAOE,eAAe;IACxB;IACA,IAAIX,IAAI,GAAGG,QAAQ,CAACS,CAAC,CAAC;IACtB,IAAIC,MAAM,GAAGd,QAAQ,CAACC,IAAI,CAAC;IAC3B,IAAIc,OAAO,GAAGD,MAAM,GAAGP,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM,GAAG,CAAC;IAC9C,IAAIQ,OAAO,GAAGL,OAAO,GAAGI,OAAO;IAC/B;IACA;IACA,IAAIC,OAAO,GAAGN,GAAG,EAAE;MACjB,IAAIO,OAAO,GAAGP,GAAG,GAAGC,OAAO;MAC3BC,eAAe,CAACM,IAAI,CAACX,MAAM,CAACN,IAAI,CAAC,CAACkB,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC,CAAC;MACpD,OAAOL,eAAe;IACxB;IACAA,eAAe,CAACM,IAAI,CAACjB,IAAI,CAAC;IAC1BU,OAAO,GAAGK,OAAO;EACnB;EACA,OAAOZ,QAAQ;AACjB;AACA,IAAIgB,IAAI,GAAG,CAAC;AACZ,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,kBAAkB,GAAG,CAAC;AAC1B,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;IACtCC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,IAAI,GAAGN,IAAI,CAACM,IAAI;IAChBC,UAAU,GAAGP,IAAI,CAACO,UAAU;EAC9B,IAAIC,eAAe,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7CC,gBAAgB,GAAGzC,cAAc,CAACuC,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAG1C,cAAc,CAACyC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1DE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAC/BE,MAAM,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IAC7BG,MAAM,GAAGH,iBAAiB,CAAC,CAAC,CAAC;IAC7BI,YAAY,GAAGL,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIM,gBAAgB,GAAG3C,KAAK,CAACoC,QAAQ,CAACf,IAAI,CAAC;IACzCuB,gBAAgB,GAAGhD,cAAc,CAAC+C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,gBAAgB,GAAG/C,KAAK,CAACoC,QAAQ,CAAC,CAAC,CAAC;IACtCY,gBAAgB,GAAGpD,cAAc,CAACmD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,YAAY,GAAGnD,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIC,SAAS,GAAGrD,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EAClC,IAAI/C,QAAQ,GAAGL,KAAK,CAACsD,OAAO,CAAC,YAAY;IACvC,OAAOxD,OAAO,CAACgC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,IAAIxB,QAAQ,GAAGN,KAAK,CAACsD,OAAO,CAAC,YAAY;IACvC,OAAOlD,WAAW,CAACC,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,IAAIkD,cAAc,GAAGvD,KAAK,CAACsD,OAAO,CAAC,YAAY;IAC7C,IAAI,CAAC1B,cAAc,IAAIiB,YAAY,KAAKrB,kBAAkB,EAAE;MAC1D,OAAOK,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC;IAClC;IACA,OAAOwB,QAAQ,CAACnB,UAAU,CAACL,QAAQ,EAAEmC,MAAM,CAAC,EAAEA,MAAM,GAAGlC,QAAQ,CAAC;EAClE,CAAC,EAAE,CAACsB,cAAc,EAAEiB,YAAY,EAAEhB,QAAQ,EAAExB,QAAQ,EAAEmC,MAAM,EAAElC,QAAQ,CAAC,CAAC;EACxE;EACAP,yBAAyB,CAAC,YAAY;IACpC,IAAI6B,cAAc,IAAIG,KAAK,IAAIC,QAAQ,IAAI1B,QAAQ,EAAE;MACnDwC,eAAe,CAACxB,OAAO,CAAC;MACxBoB,YAAY,CAAC,CAAC,CAAC,EAAEc,IAAI,CAACC,IAAI,CAACnD,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC;IACtD;EACF,CAAC,EAAE,CAACsB,cAAc,EAAEG,KAAK,EAAEC,QAAQ,EAAEF,IAAI,EAAExB,QAAQ,EAAE2B,IAAI,CAAC,CAAC;EAC3DlC,yBAAyB,CAAC,YAAY;IACpC,IAAI2D,EAAE;IACN,IAAIb,YAAY,KAAKvB,OAAO,EAAE;MAC5B4B,kBAAkB,CAAC,CAAC,CAACQ,EAAE,GAAGP,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC,CAAC;IAC7G;EACF,CAAC,EAAE,CAACf,YAAY,CAAC,CAAC;EAClB9C,yBAAyB,CAAC,YAAY;IACpC,IAAI2D,EAAE,EAAEG,EAAE;IACV,IAAIZ,eAAe,EAAE;MACnB,IAAIJ,YAAY,KAAKvB,OAAO,EAAE;QAC5B;QACA,IAAIwC,SAAS,GAAG,CAAC,CAACJ,EAAE,GAAGL,SAAS,CAACM,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC;QACpG,IAAIG,SAAS,GAAG9B,IAAI,GAAGgB,eAAe;QACtC,IAAIa,SAAS,IAAIC,SAAS,EAAE;UAC1BjB,eAAe,CAACrB,qBAAqB,CAAC;UACtCS,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,MAAM;UACLY,eAAe,CAACvB,OAAO,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIsB,YAAY,KAAKtB,OAAO,EAAE;QACnC,IAAIgB,QAAQ,KAAKE,MAAM,EAAE;UACvB,IAAIuB,UAAU,GAAG,CAAC,CAACH,EAAE,GAAGR,SAAS,CAACM,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,YAAY,KAAK,CAAC;UACrG,IAAIK,UAAU,GAAGhC,IAAI,GAAGgB,eAAe;UACvC,IAAIiB,YAAY,GAAG3B,QAAQ;UAC3B,IAAI4B,UAAU,GAAG1B,MAAM;UACvB;UACA,IAAIF,QAAQ,KAAKE,MAAM,GAAG,CAAC,EAAE;YAC3B0B,UAAU,GAAG5B,QAAQ;UACvB,CAAC,MAAM,IAAIyB,UAAU,IAAIC,UAAU,EAAE;YACnCC,YAAY,GAAG1B,MAAM;UACvB,CAAC,MAAM;YACL2B,UAAU,GAAG3B,MAAM;UACrB;UACA,IAAI4B,UAAU,GAAGZ,IAAI,CAACC,IAAI,CAAC,CAACS,YAAY,GAAGC,UAAU,IAAI,CAAC,CAAC;UAC3DzB,YAAY,CAAC,CAACwB,YAAY,EAAEE,UAAU,EAAED,UAAU,CAAC,CAAC;QACtD,CAAC,MAAM;UACLrB,eAAe,CAACtB,kBAAkB,CAAC;UACnCU,UAAU,CAAC,IAAI,CAAC;QAClB;MACF;IACF;EACF,CAAC,EAAE,CAACW,YAAY,EAAEN,QAAQ,EAAEE,MAAM,EAAER,IAAI,EAAEgB,eAAe,CAAC,CAAC;EAC3D;EACA,IAAIoB,YAAY,GAAG;IACjBtC,KAAK,EAAEA,KAAK;IACZuC,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAC9D,OAAO,aAAa5E,KAAK,CAAC6E,aAAa,CAAC,MAAM,EAAE;MAC9C,aAAa,EAAE,IAAI;MACnBF,GAAG,EAAEA,GAAG;MACRC,KAAK,EAAEjF,QAAQ,CAAC;QACdmF,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,OAAO;QAChBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC,IAAI;QACbC,UAAU,EAAE,QAAQ;QACpBC,aAAa,EAAE,MAAM;QACrBpD,QAAQ,EAAEwB,IAAI,CAAC6B,KAAK,CAACrD,QAAQ,GAAG,CAAC,CAAC,GAAG;MACvC,CAAC,EAAE4C,KAAK;IACV,CAAC,EAAEF,OAAO,CAAC;EACb,CAAC;EACD,IAAIY,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3E,GAAG,EAAEgE,GAAG,EAAE;IAC7D,IAAIY,aAAa,GAAG7E,UAAU,CAACL,QAAQ,EAAEM,GAAG,CAAC;IAC7C,OAAO8D,aAAa,CAAC5C,QAAQ,CAAC0D,aAAa,EAAE,IAAI,CAAC,EAAEZ,GAAG,EAAEN,YAAY,CAAC;EACxE,CAAC;EACD,OAAO,aAAarE,KAAK,CAAC6E,aAAa,CAAC7E,KAAK,CAACwF,QAAQ,EAAE,IAAI,EAAEjC,cAAc,EAAE3B,cAAc,IAAIiB,YAAY,KAAKrB,kBAAkB,IAAIqB,YAAY,KAAKpB,qBAAqB,IAAI,aAAazB,KAAK,CAAC6E,aAAa,CAAC7E,KAAK,CAACwF,QAAQ,EAAE,IAAI,EAAEf,aAAa,CAAC,IAAI,EAAEtB,YAAY,EAAE;IACxQsC,SAAS,EAAE,UAAU;IACrBnB,UAAU,EAAE;EACd,CAAC,CAAC,EAAEzB,YAAY,KAAKvB,OAAO,GAAGmD,aAAa,CAAC5C,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC,EAAEgD,SAAS,EAAEgB,YAAY,CAAC,GAAGiB,kBAAkB,CAAC9C,MAAM,EAAEa,SAAS,CAAC,CAAC,CAAC;AAC5I,CAAC;AACD,IAAIqC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClE,QAAQ,CAACmE,WAAW,GAAG,UAAU;AACnC;AACA,eAAenE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}