{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = sum;\nfunction sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "sum", "values", "valueof", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/sum.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = sum;\n\nfunction sum(values, valueof) {\n  let sum = 0;\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n\n  return sum;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,SAASA,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC5B,IAAIF,GAAG,GAAG,CAAC;EAEX,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,IAAIL,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAIH,KAAK,GAAG,CAACA,KAAK,EAAE;QAClBE,GAAG,IAAIF,KAAK;MACd;IACF;EACF,CAAC,MAAM;IACL,IAAIM,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIN,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAIH,KAAK,GAAG,CAACI,OAAO,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,EAAE;QAC5CD,GAAG,IAAIF,KAAK;MACd;IACF;EACF;EAEA,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}