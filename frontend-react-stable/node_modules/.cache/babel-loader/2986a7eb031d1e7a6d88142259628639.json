{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"onPressEnter\", \"defaultValue\", \"value\", \"autoSize\", \"onResize\", \"className\", \"style\", \"disabled\", \"onChange\", \"onInternalAutoSize\"];\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport calculateAutoSizeStyle from './calculateNodeHeight';\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-textarea' : _props$prefixCls,\n    onPressEnter = props.onPressEnter,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    autoSize = props.autoSize,\n    onResize = props.onResize,\n    className = props.className,\n    style = props.style,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onInternalAutoSize = props.onInternalAutoSize,\n    restProps = _objectWithoutProperties(props, _excluded);\n  // =============================== Value ================================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value,\n      postState: function postState(val) {\n        return val !== null && val !== void 0 ? val : '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n  var onInternalChange = function onInternalChange(event) {\n    setMergedValue(event.target.value);\n    onChange === null || onChange === void 0 ? void 0 : onChange(event);\n  };\n  // ================================ Ref =================================\n  var textareaRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      textArea: textareaRef.current\n    };\n  });\n  // ============================== AutoSize ==============================\n  var _React$useMemo = React.useMemo(function () {\n      if (autoSize && _typeof(autoSize) === 'object') {\n        return [autoSize.minRows, autoSize.maxRows];\n      }\n      return [];\n    }, [autoSize]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    minRows = _React$useMemo2[0],\n    maxRows = _React$useMemo2[1];\n  var needAutoSize = !!autoSize;\n  // =============================== Scroll ===============================\n  // https://github.com/ant-design/ant-design/issues/21870\n  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n    try {\n      // FF has bug with jump of scroll to top. We force back here.\n      if (document.activeElement === textareaRef.current) {\n        var _textareaRef$current = textareaRef.current,\n          selectionStart = _textareaRef$current.selectionStart,\n          selectionEnd = _textareaRef$current.selectionEnd,\n          scrollTop = _textareaRef$current.scrollTop;\n        // Fix Safari bug which not rollback when break line\n        // This makes Chinese IME can't input. Do not fix this\n        // const { value: tmpValue } = textareaRef.current;\n        // textareaRef.current.value = '';\n        // textareaRef.current.value = tmpValue;\n        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n        textareaRef.current.scrollTop = scrollTop;\n      }\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  };\n  // =============================== Resize ===============================\n  var _React$useState = React.useState(RESIZE_STABLE),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    resizeState = _React$useState2[0],\n    setResizeState = _React$useState2[1];\n  var _React$useState3 = React.useState(),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    autoSizeStyle = _React$useState4[0],\n    setAutoSizeStyle = _React$useState4[1];\n  var startResize = function startResize() {\n    setResizeState(RESIZE_START);\n    if (process.env.NODE_ENV === 'test') {\n      onInternalAutoSize === null || onInternalAutoSize === void 0 ? void 0 : onInternalAutoSize();\n    }\n  };\n  // Change to trigger resize measure\n  useLayoutEffect(function () {\n    if (needAutoSize) {\n      startResize();\n    }\n  }, [value, minRows, maxRows, needAutoSize]);\n  useLayoutEffect(function () {\n    if (resizeState === RESIZE_START) {\n      setResizeState(RESIZE_MEASURING);\n    } else if (resizeState === RESIZE_MEASURING) {\n      var textareaStyles = calculateAutoSizeStyle(textareaRef.current, false, minRows, maxRows);\n      // Safari has bug that text will keep break line on text cut when it's prev is break line.\n      // ZombieJ: This not often happen. So we just skip it.\n      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n      // const { value: tmpValue } = textareaRef.current;\n      // textareaRef.current.value = '';\n      // textareaRef.current.value = tmpValue;\n      // if (document.activeElement === textareaRef.current) {\n      //   textareaRef.current.scrollTop = scrollTop;\n      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n      // }\n      setResizeState(RESIZE_STABLE);\n      setAutoSizeStyle(textareaStyles);\n    } else {\n      fixFirefoxAutoScroll();\n    }\n  }, [resizeState]);\n  // We lock resize trigger by raf to avoid Safari warning\n  var resizeRafRef = React.useRef();\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(resizeRafRef.current);\n  };\n  var onInternalResize = function onInternalResize(size) {\n    if (resizeState === RESIZE_STABLE) {\n      onResize === null || onResize === void 0 ? void 0 : onResize(size);\n      if (autoSize) {\n        cleanRaf();\n        resizeRafRef.current = raf(function () {\n          startResize();\n        });\n      }\n    }\n  };\n  React.useEffect(function () {\n    return cleanRaf;\n  }, []);\n  // =============================== Render ===============================\n  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n  var mergedStyle = _objectSpread(_objectSpread({}, style), mergedAutoSizeStyle);\n  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n    mergedStyle.overflowY = 'hidden';\n    mergedStyle.overflowX = 'hidden';\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onInternalResize,\n    disabled: !(autoSize || onResize)\n  }, /*#__PURE__*/React.createElement(\"textarea\", _extends({}, restProps, {\n    ref: textareaRef,\n    style: mergedStyle,\n    className: classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n    disabled: disabled,\n    value: mergedValue,\n    onChange: onInternalChange\n  })));\n});\nexport default ResizableTextArea;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_typeof", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "ResizeObserver", "useLayoutEffect", "raf", "useMergedState", "classNames", "calculateAutoSizeStyle", "RESIZE_START", "RESIZE_MEASURING", "RESIZE_STABLE", "ResizableTextArea", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "onPressEnter", "defaultValue", "value", "autoSize", "onResize", "className", "style", "disabled", "onChange", "onInternalAutoSize", "restProps", "_useMergedState", "postState", "val", "_useMergedState2", "mergedValue", "setMergedValue", "onInternalChange", "event", "target", "textareaRef", "useRef", "useImperativeHandle", "textArea", "current", "_React$useMemo", "useMemo", "minRows", "maxRows", "_React$useMemo2", "needAutoSize", "fixFirefoxAutoScroll", "document", "activeElement", "_textareaRef$current", "selectionStart", "selectionEnd", "scrollTop", "setSelectionRange", "e", "_React$useState", "useState", "_React$useState2", "resizeState", "setResizeState", "_React$useState3", "_React$useState4", "autoSizeStyle", "setAutoSizeStyle", "startResize", "process", "env", "NODE_ENV", "textareaStyles", "resizeRafRef", "cleanRaf", "cancel", "onInternalResize", "size", "useEffect", "mergedAutoSizeStyle", "mergedStyle", "overflowY", "overflowX", "createElement", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-textarea/es/ResizableTextArea.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"onPressEnter\", \"defaultValue\", \"value\", \"autoSize\", \"onResize\", \"className\", \"style\", \"disabled\", \"onChange\", \"onInternalAutoSize\"];\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport calculateAutoSizeStyle from './calculateNodeHeight';\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-textarea' : _props$prefixCls,\n    onPressEnter = props.onPressEnter,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    autoSize = props.autoSize,\n    onResize = props.onResize,\n    className = props.className,\n    style = props.style,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onInternalAutoSize = props.onInternalAutoSize,\n    restProps = _objectWithoutProperties(props, _excluded);\n  // =============================== Value ================================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value,\n      postState: function postState(val) {\n        return val !== null && val !== void 0 ? val : '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n  var onInternalChange = function onInternalChange(event) {\n    setMergedValue(event.target.value);\n    onChange === null || onChange === void 0 ? void 0 : onChange(event);\n  };\n  // ================================ Ref =================================\n  var textareaRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      textArea: textareaRef.current\n    };\n  });\n  // ============================== AutoSize ==============================\n  var _React$useMemo = React.useMemo(function () {\n      if (autoSize && _typeof(autoSize) === 'object') {\n        return [autoSize.minRows, autoSize.maxRows];\n      }\n      return [];\n    }, [autoSize]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    minRows = _React$useMemo2[0],\n    maxRows = _React$useMemo2[1];\n  var needAutoSize = !!autoSize;\n  // =============================== Scroll ===============================\n  // https://github.com/ant-design/ant-design/issues/21870\n  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n    try {\n      // FF has bug with jump of scroll to top. We force back here.\n      if (document.activeElement === textareaRef.current) {\n        var _textareaRef$current = textareaRef.current,\n          selectionStart = _textareaRef$current.selectionStart,\n          selectionEnd = _textareaRef$current.selectionEnd,\n          scrollTop = _textareaRef$current.scrollTop;\n        // Fix Safari bug which not rollback when break line\n        // This makes Chinese IME can't input. Do not fix this\n        // const { value: tmpValue } = textareaRef.current;\n        // textareaRef.current.value = '';\n        // textareaRef.current.value = tmpValue;\n        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n        textareaRef.current.scrollTop = scrollTop;\n      }\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  };\n  // =============================== Resize ===============================\n  var _React$useState = React.useState(RESIZE_STABLE),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    resizeState = _React$useState2[0],\n    setResizeState = _React$useState2[1];\n  var _React$useState3 = React.useState(),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    autoSizeStyle = _React$useState4[0],\n    setAutoSizeStyle = _React$useState4[1];\n  var startResize = function startResize() {\n    setResizeState(RESIZE_START);\n    if (process.env.NODE_ENV === 'test') {\n      onInternalAutoSize === null || onInternalAutoSize === void 0 ? void 0 : onInternalAutoSize();\n    }\n  };\n  // Change to trigger resize measure\n  useLayoutEffect(function () {\n    if (needAutoSize) {\n      startResize();\n    }\n  }, [value, minRows, maxRows, needAutoSize]);\n  useLayoutEffect(function () {\n    if (resizeState === RESIZE_START) {\n      setResizeState(RESIZE_MEASURING);\n    } else if (resizeState === RESIZE_MEASURING) {\n      var textareaStyles = calculateAutoSizeStyle(textareaRef.current, false, minRows, maxRows);\n      // Safari has bug that text will keep break line on text cut when it's prev is break line.\n      // ZombieJ: This not often happen. So we just skip it.\n      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n      // const { value: tmpValue } = textareaRef.current;\n      // textareaRef.current.value = '';\n      // textareaRef.current.value = tmpValue;\n      // if (document.activeElement === textareaRef.current) {\n      //   textareaRef.current.scrollTop = scrollTop;\n      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n      // }\n      setResizeState(RESIZE_STABLE);\n      setAutoSizeStyle(textareaStyles);\n    } else {\n      fixFirefoxAutoScroll();\n    }\n  }, [resizeState]);\n  // We lock resize trigger by raf to avoid Safari warning\n  var resizeRafRef = React.useRef();\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(resizeRafRef.current);\n  };\n  var onInternalResize = function onInternalResize(size) {\n    if (resizeState === RESIZE_STABLE) {\n      onResize === null || onResize === void 0 ? void 0 : onResize(size);\n      if (autoSize) {\n        cleanRaf();\n        resizeRafRef.current = raf(function () {\n          startResize();\n        });\n      }\n    }\n  };\n  React.useEffect(function () {\n    return cleanRaf;\n  }, []);\n  // =============================== Render ===============================\n  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n  var mergedStyle = _objectSpread(_objectSpread({}, style), mergedAutoSizeStyle);\n  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n    mergedStyle.overflowY = 'hidden';\n    mergedStyle.overflowX = 'hidden';\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onInternalResize,\n    disabled: !(autoSize || onResize)\n  }, /*#__PURE__*/React.createElement(\"textarea\", _extends({}, restProps, {\n    ref: textareaRef,\n    style: mergedStyle,\n    className: classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n    disabled: disabled,\n    value: mergedValue,\n    onChange: onInternalChange\n  })));\n});\nexport default ResizableTextArea;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB,CAAC;AAClK,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,sBAAsB,MAAM,uBAAuB;AAC1D,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,aAAa,GAAG,CAAC;AACrB,IAAIC,iBAAiB,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC1E,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,kBAAkB,GAAGb,KAAK,CAACa,kBAAkB;IAC7CC,SAAS,GAAG5B,wBAAwB,CAACc,KAAK,EAAEb,SAAS,CAAC;EACxD;EACA,IAAI4B,eAAe,GAAGvB,cAAc,CAACa,YAAY,EAAE;MAC/CC,KAAK,EAAEA,KAAK;MACZU,SAAS,EAAE,SAASA,SAASA,CAACC,GAAG,EAAE;QACjC,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE;MAClD;IACF,CAAC,CAAC;IACFC,gBAAgB,GAAGjC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDI,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE;IACtDF,cAAc,CAACE,KAAK,CAACC,MAAM,CAACjB,KAAK,CAAC;IAClCM,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,KAAK,CAAC;EACrE,CAAC;EACD;EACA,IAAIE,WAAW,GAAGpC,KAAK,CAACqC,MAAM,CAAC,CAAC;EAChCrC,KAAK,CAACsC,mBAAmB,CAACzB,GAAG,EAAE,YAAY;IACzC,OAAO;MACL0B,QAAQ,EAAEH,WAAW,CAACI;IACxB,CAAC;EACH,CAAC,CAAC;EACF;EACA,IAAIC,cAAc,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,YAAY;MAC3C,IAAIvB,QAAQ,IAAIvB,OAAO,CAACuB,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAC9C,OAAO,CAACA,QAAQ,CAACwB,OAAO,EAAExB,QAAQ,CAACyB,OAAO,CAAC;MAC7C;MACA,OAAO,EAAE;IACX,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;IACd0B,eAAe,GAAGhD,cAAc,CAAC4C,cAAc,EAAE,CAAC,CAAC;IACnDE,OAAO,GAAGE,eAAe,CAAC,CAAC,CAAC;IAC5BD,OAAO,GAAGC,eAAe,CAAC,CAAC,CAAC;EAC9B,IAAIC,YAAY,GAAG,CAAC,CAAC3B,QAAQ;EAC7B;EACA;EACA,IAAI4B,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,IAAI;MACF;MACA,IAAIC,QAAQ,CAACC,aAAa,KAAKb,WAAW,CAACI,OAAO,EAAE;QAClD,IAAIU,oBAAoB,GAAGd,WAAW,CAACI,OAAO;UAC5CW,cAAc,GAAGD,oBAAoB,CAACC,cAAc;UACpDC,YAAY,GAAGF,oBAAoB,CAACE,YAAY;UAChDC,SAAS,GAAGH,oBAAoB,CAACG,SAAS;QAC5C;QACA;QACA;QACA;QACA;QACAjB,WAAW,CAACI,OAAO,CAACc,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;QACnEhB,WAAW,CAACI,OAAO,CAACa,SAAS,GAAGA,SAAS;MAC3C;IACF,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV;MACA;MACA;IAAA;EAEJ,CAAC;EACD;EACA,IAAIC,eAAe,GAAGxD,KAAK,CAACyD,QAAQ,CAAChD,aAAa,CAAC;IACjDiD,gBAAgB,GAAG7D,cAAc,CAAC2D,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG7D,KAAK,CAACyD,QAAQ,CAAC,CAAC;IACrCK,gBAAgB,GAAGjE,cAAc,CAACgE,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCL,cAAc,CAACrD,YAAY,CAAC;IAC5B,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC3C,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAC,CAAC;IAC9F;EACF,CAAC;EACD;EACAvB,eAAe,CAAC,YAAY;IAC1B,IAAI4C,YAAY,EAAE;MAChBmB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC/C,KAAK,EAAEyB,OAAO,EAAEC,OAAO,EAAEE,YAAY,CAAC,CAAC;EAC3C5C,eAAe,CAAC,YAAY;IAC1B,IAAIyD,WAAW,KAAKpD,YAAY,EAAE;MAChCqD,cAAc,CAACpD,gBAAgB,CAAC;IAClC,CAAC,MAAM,IAAImD,WAAW,KAAKnD,gBAAgB,EAAE;MAC3C,IAAI6D,cAAc,GAAG/D,sBAAsB,CAAC8B,WAAW,CAACI,OAAO,EAAE,KAAK,EAAEG,OAAO,EAAEC,OAAO,CAAC;MACzF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAgB,cAAc,CAACnD,aAAa,CAAC;MAC7BuD,gBAAgB,CAACK,cAAc,CAAC;IAClC,CAAC,MAAM;MACLtB,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACY,WAAW,CAAC,CAAC;EACjB;EACA,IAAIW,YAAY,GAAGtE,KAAK,CAACqC,MAAM,CAAC,CAAC;EACjC,IAAIkC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjCpE,GAAG,CAACqE,MAAM,CAACF,YAAY,CAAC9B,OAAO,CAAC;EAClC,CAAC;EACD,IAAIiC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrD,IAAIf,WAAW,KAAKlD,aAAa,EAAE;MACjCW,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACsD,IAAI,CAAC;MAClE,IAAIvD,QAAQ,EAAE;QACZoD,QAAQ,CAAC,CAAC;QACVD,YAAY,CAAC9B,OAAO,GAAGrC,GAAG,CAAC,YAAY;UACrC8D,WAAW,CAAC,CAAC;QACf,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACDjE,KAAK,CAAC2E,SAAS,CAAC,YAAY;IAC1B,OAAOJ,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC;EACN;EACA,IAAIK,mBAAmB,GAAG9B,YAAY,GAAGiB,aAAa,GAAG,IAAI;EAC7D,IAAIc,WAAW,GAAGlF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAAC,EAAEsD,mBAAmB,CAAC;EAC9E,IAAIjB,WAAW,KAAKpD,YAAY,IAAIoD,WAAW,KAAKnD,gBAAgB,EAAE;IACpEqE,WAAW,CAACC,SAAS,GAAG,QAAQ;IAChCD,WAAW,CAACE,SAAS,GAAG,QAAQ;EAClC;EACA,OAAO,aAAa/E,KAAK,CAACgF,aAAa,CAAC/E,cAAc,EAAE;IACtDmB,QAAQ,EAAEqD,gBAAgB;IAC1BlD,QAAQ,EAAE,EAAEJ,QAAQ,IAAIC,QAAQ;EAClC,CAAC,EAAE,aAAapB,KAAK,CAACgF,aAAa,CAAC,UAAU,EAAEvF,QAAQ,CAAC,CAAC,CAAC,EAAEiC,SAAS,EAAE;IACtEb,GAAG,EAAEuB,WAAW;IAChBd,KAAK,EAAEuD,WAAW;IAClBxD,SAAS,EAAEhB,UAAU,CAACU,SAAS,EAAEM,SAAS,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuF,MAAM,CAAClE,SAAS,EAAE,WAAW,CAAC,EAAEQ,QAAQ,CAAC,CAAC;IAC7GA,QAAQ,EAAEA,QAAQ;IAClBL,KAAK,EAAEa,WAAW;IAClBP,QAAQ,EAAES;EACZ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,eAAevB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}