{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useForm as useRcForm } from 'rc-field-form';\nimport * as React from 'react';\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport { getFieldId, toArray } from '../util';\nfunction toNamePathStr(name) {\n  var namePath = toArray(name);\n  return namePath.join('_');\n}\nexport default function useForm(form) {\n  var _useRcForm = useRcForm(),\n    _useRcForm2 = _slicedToArray(_useRcForm, 1),\n    rcForm = _useRcForm2[0];\n  var itemsRef = React.useRef({});\n  var wrapForm = React.useMemo(function () {\n    return form !== null && form !== void 0 ? form : _extends(_extends({}, rcForm), {\n      __INTERNAL__: {\n        itemRef: function itemRef(name) {\n          return function (node) {\n            var namePathStr = toNamePathStr(name);\n            if (node) {\n              itemsRef.current[namePathStr] = node;\n            } else {\n              delete itemsRef.current[namePathStr];\n            }\n          };\n        }\n      },\n      scrollToField: function scrollToField(name) {\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var namePath = toArray(name);\n        var fieldId = getFieldId(namePath, wrapForm.__INTERNAL__.name);\n        var node = fieldId ? document.getElementById(fieldId) : null;\n        if (node) {\n          scrollIntoView(node, _extends({\n            scrollMode: 'if-needed',\n            block: 'nearest'\n          }, options));\n        }\n      },\n      getFieldInstance: function getFieldInstance(name) {\n        var namePathStr = toNamePathStr(name);\n        return itemsRef.current[namePathStr];\n      }\n    });\n  }, [form, rcForm]);\n  return [wrapForm];\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "useForm", "useRcForm", "React", "scrollIntoView", "getFieldId", "toArray", "toNamePathStr", "name", "namePath", "join", "form", "_useRcForm", "_useRcForm2", "rcForm", "itemsRef", "useRef", "wrapForm", "useMemo", "__INTERNAL__", "itemRef", "node", "namePathStr", "current", "scrollToField", "options", "arguments", "length", "undefined", "fieldId", "document", "getElementById", "scrollMode", "block", "getFieldInstance"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/hooks/useForm.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useForm as useRcForm } from 'rc-field-form';\nimport * as React from 'react';\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport { getFieldId, toArray } from '../util';\nfunction toNamePathStr(name) {\n  var namePath = toArray(name);\n  return namePath.join('_');\n}\nexport default function useForm(form) {\n  var _useRcForm = useRcForm(),\n    _useRcForm2 = _slicedToArray(_useRcForm, 1),\n    rcForm = _useRcForm2[0];\n  var itemsRef = React.useRef({});\n  var wrapForm = React.useMemo(function () {\n    return form !== null && form !== void 0 ? form : _extends(_extends({}, rcForm), {\n      __INTERNAL__: {\n        itemRef: function itemRef(name) {\n          return function (node) {\n            var namePathStr = toNamePathStr(name);\n            if (node) {\n              itemsRef.current[namePathStr] = node;\n            } else {\n              delete itemsRef.current[namePathStr];\n            }\n          };\n        }\n      },\n      scrollToField: function scrollToField(name) {\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var namePath = toArray(name);\n        var fieldId = getFieldId(namePath, wrapForm.__INTERNAL__.name);\n        var node = fieldId ? document.getElementById(fieldId) : null;\n        if (node) {\n          scrollIntoView(node, _extends({\n            scrollMode: 'if-needed',\n            block: 'nearest'\n          }, options));\n        }\n      },\n      getFieldInstance: function getFieldInstance(name) {\n        var namePathStr = toNamePathStr(name);\n        return itemsRef.current[namePathStr];\n      }\n    });\n  }, [form, rcForm]);\n  return [wrapForm];\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,OAAO,IAAIC,SAAS,QAAQ,eAAe;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,UAAU,EAAEC,OAAO,QAAQ,SAAS;AAC7C,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,QAAQ,GAAGH,OAAO,CAACE,IAAI,CAAC;EAC5B,OAAOC,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3B;AACA,eAAe,SAAST,OAAOA,CAACU,IAAI,EAAE;EACpC,IAAIC,UAAU,GAAGV,SAAS,CAAC,CAAC;IAC1BW,WAAW,GAAGb,cAAc,CAACY,UAAU,EAAE,CAAC,CAAC;IAC3CE,MAAM,GAAGD,WAAW,CAAC,CAAC,CAAC;EACzB,IAAIE,QAAQ,GAAGZ,KAAK,CAACa,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIC,QAAQ,GAAGd,KAAK,CAACe,OAAO,CAAC,YAAY;IACvC,OAAOP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGZ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEe,MAAM,CAAC,EAAE;MAC9EK,YAAY,EAAE;QACZC,OAAO,EAAE,SAASA,OAAOA,CAACZ,IAAI,EAAE;UAC9B,OAAO,UAAUa,IAAI,EAAE;YACrB,IAAIC,WAAW,GAAGf,aAAa,CAACC,IAAI,CAAC;YACrC,IAAIa,IAAI,EAAE;cACRN,QAAQ,CAACQ,OAAO,CAACD,WAAW,CAAC,GAAGD,IAAI;YACtC,CAAC,MAAM;cACL,OAAON,QAAQ,CAACQ,OAAO,CAACD,WAAW,CAAC;YACtC;UACF,CAAC;QACH;MACF,CAAC;MACDE,aAAa,EAAE,SAASA,aAAaA,CAAChB,IAAI,EAAE;QAC1C,IAAIiB,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACpF,IAAIjB,QAAQ,GAAGH,OAAO,CAACE,IAAI,CAAC;QAC5B,IAAIqB,OAAO,GAAGxB,UAAU,CAACI,QAAQ,EAAEQ,QAAQ,CAACE,YAAY,CAACX,IAAI,CAAC;QAC9D,IAAIa,IAAI,GAAGQ,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACF,OAAO,CAAC,GAAG,IAAI;QAC5D,IAAIR,IAAI,EAAE;UACRjB,cAAc,CAACiB,IAAI,EAAEtB,QAAQ,CAAC;YAC5BiC,UAAU,EAAE,WAAW;YACvBC,KAAK,EAAE;UACT,CAAC,EAAER,OAAO,CAAC,CAAC;QACd;MACF,CAAC;MACDS,gBAAgB,EAAE,SAASA,gBAAgBA,CAAC1B,IAAI,EAAE;QAChD,IAAIc,WAAW,GAAGf,aAAa,CAACC,IAAI,CAAC;QACrC,OAAOO,QAAQ,CAACQ,OAAO,CAACD,WAAW,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,IAAI,EAAEG,MAAM,CAAC,CAAC;EAClB,OAAO,CAACG,QAAQ,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}