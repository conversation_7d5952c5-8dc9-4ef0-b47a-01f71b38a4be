{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    var _classNames;\n    list.push(/*#__PURE__*/React.createElement(\"span\", {\n      key: i,\n      className: classNames(baseClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(baseClassName, \"-start\"), isStart[i]), _defineProperty(_classNames, \"\".concat(baseClassName, \"-end\"), isEnd[i]), _classNames))\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\nexport default /*#__PURE__*/React.memo(Indent);", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "Indent", "_ref", "prefixCls", "level", "isStart", "isEnd", "baseClassName", "concat", "list", "i", "_classNames", "push", "createElement", "key", "className", "memo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/Indent.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    var _classNames;\n    list.push( /*#__PURE__*/React.createElement(\"span\", {\n      key: i,\n      className: classNames(baseClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(baseClassName, \"-start\"), isStart[i]), _defineProperty(_classNames, \"\".concat(baseClassName, \"-end\"), isEnd[i]), _classNames))\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\nexport default /*#__PURE__*/React.memo(Indent);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,cAAc,CAAC;EACxD,IAAIM,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,IAAI,CAAC,EAAE;IACjC,IAAIC,WAAW;IACfF,IAAI,CAACG,IAAI,CAAE,aAAab,KAAK,CAACc,aAAa,CAAC,MAAM,EAAE;MAClDC,GAAG,EAAEJ,CAAC;MACNK,SAAS,EAAEf,UAAU,CAACO,aAAa,GAAGI,WAAW,GAAG,CAAC,CAAC,EAAEb,eAAe,CAACa,WAAW,EAAE,EAAE,CAACH,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC,EAAEF,OAAO,CAACK,CAAC,CAAC,CAAC,EAAEZ,eAAe,CAACa,WAAW,EAAE,EAAE,CAACH,MAAM,CAACD,aAAa,EAAE,MAAM,CAAC,EAAED,KAAK,CAACI,CAAC,CAAC,CAAC,EAAEC,WAAW,CAAC;IAC9N,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAaZ,KAAK,CAACc,aAAa,CAAC,MAAM,EAAE;IAC9C,aAAa,EAAE,MAAM;IACrBE,SAAS,EAAE,EAAE,CAACP,MAAM,CAACL,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEM,IAAI,CAAC;AACV,CAAC;AACD,eAAe,aAAaV,KAAK,CAACiB,IAAI,CAACf,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}