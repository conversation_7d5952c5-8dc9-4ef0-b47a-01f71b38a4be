{"ast": null, "code": "import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nif (eventCenter.setMaxListeners) {\n  eventCenter.setMaxListeners(10);\n}\nexport { eventCenter };\nexport var SYNC_EVENT = 'recharts.syncMouseEvents';", "map": {"version": 3, "names": ["EventEmitter", "eventCenter", "setMaxListeners", "SYNC_EVENT"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/util/Events.js"], "sourcesContent": ["import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nif (eventCenter.setMaxListeners) {\n  eventCenter.setMaxListeners(10);\n}\nexport { eventCenter };\nexport var SYNC_EVENT = 'recharts.syncMouseEvents';"], "mappings": "AAAA,OAAOA,YAAY,MAAM,eAAe;AACxC,IAAIC,WAAW,GAAG,IAAID,YAAY,CAAC,CAAC;AACpC,IAAIC,WAAW,CAACC,eAAe,EAAE;EAC/BD,WAAW,CAACC,eAAe,CAAC,EAAE,CAAC;AACjC;AACA,SAASD,WAAW;AACpB,OAAO,IAAIE,UAAU,GAAG,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}