{"ast": null, "code": "// form item name black list.  in form ,you can use form.id get the form item element.\n// use object hasOwnProperty will get better performance if black list is longer.\nvar formItemNameBlackList = ['parentNode'];\n// default form item id prefix.\nvar defaultItemNamePrefixCls = 'form_item';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\nexport function getFieldId(namePath, formName) {\n  if (!namePath.length) return undefined;\n  var mergedId = namePath.join('_');\n  if (formName) {\n    return \"\".concat(formName, \"_\").concat(mergedId);\n  }\n  var isIllegalName = formItemNameBlackList.includes(mergedId);\n  return isIllegalName ? \"\".concat(defaultItemNamePrefixCls, \"_\").concat(mergedId) : mergedId;\n}", "map": {"version": 3, "names": ["formItemNameBlackList", "defaultItemNamePrefixCls", "toArray", "candidate", "undefined", "Array", "isArray", "getFieldId", "namePath", "formName", "length", "mergedId", "join", "concat", "isIllegalName", "includes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/util.js"], "sourcesContent": ["// form item name black list.  in form ,you can use form.id get the form item element.\n// use object hasOwnProperty will get better performance if black list is longer.\nvar formItemNameBlackList = ['parentNode'];\n// default form item id prefix.\nvar defaultItemNamePrefixCls = 'form_item';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\nexport function getFieldId(namePath, formName) {\n  if (!namePath.length) return undefined;\n  var mergedId = namePath.join('_');\n  if (formName) {\n    return \"\".concat(formName, \"_\").concat(mergedId);\n  }\n  var isIllegalName = formItemNameBlackList.includes(mergedId);\n  return isIllegalName ? \"\".concat(defaultItemNamePrefixCls, \"_\").concat(mergedId) : mergedId;\n}"], "mappings": "AAAA;AACA;AACA,IAAIA,qBAAqB,GAAG,CAAC,YAAY,CAAC;AAC1C;AACA,IAAIC,wBAAwB,GAAG,WAAW;AAC1C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjC,IAAIA,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,KAAK,EAAE,OAAO,EAAE;EAC7D,OAAOE,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC3D;AACA,OAAO,SAASI,UAAUA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC7C,IAAI,CAACD,QAAQ,CAACE,MAAM,EAAE,OAAON,SAAS;EACtC,IAAIO,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAAC,GAAG,CAAC;EACjC,IAAIH,QAAQ,EAAE;IACZ,OAAO,EAAE,CAACI,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACF,QAAQ,CAAC;EAClD;EACA,IAAIG,aAAa,GAAGd,qBAAqB,CAACe,QAAQ,CAACJ,QAAQ,CAAC;EAC5D,OAAOG,aAAa,GAAG,EAAE,CAACD,MAAM,CAACZ,wBAAwB,EAAE,GAAG,CAAC,CAACY,MAAM,CAACF,QAAQ,CAAC,GAAGA,QAAQ;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module"}