{"ast": null, "code": "// `victory-vendor/d3-scale` (CommonJS)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// Our CommonJS package relies on transpiled vendor files in `lib-vendor/d3-scale`\nmodule.exports = require(\"../lib-vendor/d3-scale/src/index.js\");", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib/d3-scale.js"], "sourcesContent": ["\n// `victory-vendor/d3-scale` (CommonJS)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// Our CommonJS package relies on transpiled vendor files in `lib-vendor/d3-scale`\nmodule.exports = require(\"../lib-vendor/d3-scale/src/index.js\");\n"], "mappings": "AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,qCAAqC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}