{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _array = _interopRequireDefault(require(\"./array.js\"));\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nvar _none = _interopRequireDefault(require(\"./offset/none.js\"));\nvar _none2 = _interopRequireDefault(require(\"./order/none.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction stackValue(d, key) {\n  return d[key];\n}\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\nfunction _default() {\n  var keys = (0, _constant.default)([]),\n    order = _none2.default,\n    offset = _none.default,\n    value = stackValue;\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n      i,\n      n = sz.length,\n      j = -1,\n      oz;\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n    for (i = 0, oz = (0, _array.default)(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n    offset(sz, oz);\n    return sz;\n  }\n  stack.keys = function (_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : (0, _constant.default)(Array.from(_)), stack) : keys;\n  };\n  stack.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), stack) : value;\n  };\n  stack.order = function (_) {\n    return arguments.length ? (order = _ == null ? _none2.default : typeof _ === \"function\" ? _ : (0, _constant.default)(Array.from(_)), stack) : order;\n  };\n  stack.offset = function (_) {\n    return arguments.length ? (offset = _ == null ? _none.default : _, stack) : offset;\n  };\n  return stack;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_array", "_interopRequireDefault", "require", "_constant", "_none", "_none2", "obj", "__esModule", "stackValue", "d", "key", "stackSeries", "series", "keys", "order", "offset", "stack", "data", "sz", "Array", "from", "apply", "arguments", "i", "n", "length", "j", "oz", "index", "_"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/stack.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _array = _interopRequireDefault(require(\"./array.js\"));\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nvar _none = _interopRequireDefault(require(\"./offset/none.js\"));\n\nvar _none2 = _interopRequireDefault(require(\"./order/none.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\n\nfunction _default() {\n  var keys = (0, _constant.default)([]),\n      order = _none2.default,\n      offset = _none.default,\n      value = stackValue;\n\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n        i,\n        n = sz.length,\n        j = -1,\n        oz;\n\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n\n    for (i = 0, oz = (0, _array.default)(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function (_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : (0, _constant.default)(Array.from(_)), stack) : keys;\n  };\n\n  stack.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), stack) : value;\n  };\n\n  stack.order = function (_) {\n    return arguments.length ? (order = _ == null ? _none2.default : typeof _ === \"function\" ? _ : (0, _constant.default)(Array.from(_)), stack) : order;\n  };\n\n  stack.offset = function (_) {\n    return arguments.length ? (offset = _ == null ? _none.default : _, stack) : offset;\n  };\n\n  return stack;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,IAAIC,SAAS,GAAGF,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIE,KAAK,GAAGH,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAE/D,IAAIG,MAAM,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAE/D,SAASD,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAER,OAAO,EAAEQ;EAAI,CAAC;AAAE;AAE9F,SAASE,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC1B,OAAOD,CAAC,CAACC,GAAG,CAAC;AACf;AAEA,SAASC,WAAWA,CAACD,GAAG,EAAE;EACxB,MAAME,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACF,GAAG,GAAGA,GAAG;EAChB,OAAOE,MAAM;AACf;AAEA,SAASb,QAAQA,CAAA,EAAG;EAClB,IAAIc,IAAI,GAAG,CAAC,CAAC,EAAEV,SAAS,CAACL,OAAO,EAAE,EAAE,CAAC;IACjCgB,KAAK,GAAGT,MAAM,CAACP,OAAO;IACtBiB,MAAM,GAAGX,KAAK,CAACN,OAAO;IACtBD,KAAK,GAAGW,UAAU;EAEtB,SAASQ,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIC,EAAE,GAAGC,KAAK,CAACC,IAAI,CAACP,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAAEX,WAAW,CAAC;MACzDY,CAAC;MACDC,CAAC,GAAGN,EAAE,CAACO,MAAM;MACbC,CAAC,GAAG,CAAC,CAAC;MACNC,EAAE;IAEN,KAAK,MAAMlB,CAAC,IAAIQ,IAAI,EAAE;MACpB,KAAKM,CAAC,GAAG,CAAC,EAAE,EAAEG,CAAC,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC3B,CAACL,EAAE,CAACK,CAAC,CAAC,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC7B,KAAK,CAACY,CAAC,EAAES,EAAE,CAACK,CAAC,CAAC,CAACb,GAAG,EAAEgB,CAAC,EAAET,IAAI,CAAC,CAAC,EAAEA,IAAI,GAAGR,CAAC;MAC1D;IACF;IAEA,KAAKc,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAG,CAAC,CAAC,EAAE3B,MAAM,CAACF,OAAO,EAAEgB,KAAK,CAACI,EAAE,CAAC,CAAC,EAAEK,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC3DL,EAAE,CAACS,EAAE,CAACJ,CAAC,CAAC,CAAC,CAACK,KAAK,GAAGL,CAAC;IACrB;IAEAR,MAAM,CAACG,EAAE,EAAES,EAAE,CAAC;IACd,OAAOT,EAAE;EACX;EAEAF,KAAK,CAACH,IAAI,GAAG,UAAUgB,CAAC,EAAE;IACxB,OAAOP,SAAS,CAACG,MAAM,IAAIZ,IAAI,GAAG,OAAOgB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAE1B,SAAS,CAACL,OAAO,EAAEqB,KAAK,CAACC,IAAI,CAACS,CAAC,CAAC,CAAC,EAAEb,KAAK,IAAIH,IAAI;EACtH,CAAC;EAEDG,KAAK,CAACnB,KAAK,GAAG,UAAUgC,CAAC,EAAE;IACzB,OAAOP,SAAS,CAACG,MAAM,IAAI5B,KAAK,GAAG,OAAOgC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAE1B,SAAS,CAACL,OAAO,EAAE,CAAC+B,CAAC,CAAC,EAAEb,KAAK,IAAInB,KAAK;EAC7G,CAAC;EAEDmB,KAAK,CAACF,KAAK,GAAG,UAAUe,CAAC,EAAE;IACzB,OAAOP,SAAS,CAACG,MAAM,IAAIX,KAAK,GAAGe,CAAC,IAAI,IAAI,GAAGxB,MAAM,CAACP,OAAO,GAAG,OAAO+B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAE1B,SAAS,CAACL,OAAO,EAAEqB,KAAK,CAACC,IAAI,CAACS,CAAC,CAAC,CAAC,EAAEb,KAAK,IAAIF,KAAK;EACrJ,CAAC;EAEDE,KAAK,CAACD,MAAM,GAAG,UAAUc,CAAC,EAAE;IAC1B,OAAOP,SAAS,CAACG,MAAM,IAAIV,MAAM,GAAGc,CAAC,IAAI,IAAI,GAAGzB,KAAK,CAACN,OAAO,GAAG+B,CAAC,EAAEb,KAAK,IAAID,MAAM;EACpF,CAAC;EAED,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}