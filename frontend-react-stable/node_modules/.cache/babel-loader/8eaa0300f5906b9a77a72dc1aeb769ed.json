{"ast": null, "code": "import Picker from './Picker';\nimport PickerPanel from './PickerPanel';\nimport RangePicker from './RangePicker';\nexport { PickerPanel, RangePicker };\nexport default Picker;", "map": {"version": 3, "names": ["Picker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RangePicker"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/index.js"], "sourcesContent": ["import Picker from './Picker';\nimport PickerPanel from './PickerPanel';\nimport RangePicker from './RangePicker';\nexport { PickerPanel, RangePicker };\nexport default Picker;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASD,WAAW,EAAEC,WAAW;AACjC,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}