{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { warning } from \"rc-util/es/warning\";\nimport SingleObserver from \"./SingleObserver\";\nimport { Collection } from \"./Collection\";\nvar INTERNAL_PREFIX_KEY = 'rc-observer-key';\nimport { _rs } from \"./utils/observerUtil\";\nexport { /** @private Test only for mock trigger resize event */\n_rs };\nfunction ResizeObserver(props, ref) {\n  var children = props.children;\n  var childNodes = typeof children === 'function' ? [children] : toArray(children);\n  if (process.env.NODE_ENV !== 'production') {\n    if (childNodes.length > 1) {\n      warning(false, 'Find more than one child node with `children` in ResizeObserver. Please use ResizeObserver.Collection instead.');\n    } else if (childNodes.length === 0) {\n      warning(false, '`children` of ResizeObserver is empty. Nothing is in observe.');\n    }\n  }\n  return childNodes.map(function (child, index) {\n    var key = (child === null || child === void 0 ? void 0 : child.key) || \"\".concat(INTERNAL_PREFIX_KEY, \"-\").concat(index);\n    return /*#__PURE__*/React.createElement(SingleObserver, _extends({}, props, {\n      key: key,\n      ref: index === 0 ? ref : undefined\n    }), child);\n  });\n}\nvar RefResizeObserver = /*#__PURE__*/React.forwardRef(ResizeObserver);\nif (process.env.NODE_ENV !== 'production') {\n  RefResizeObserver.displayName = 'ResizeObserver';\n}\nRefResizeObserver.Collection = Collection;\nexport default RefResizeObserver;", "map": {"version": 3, "names": ["_extends", "React", "toArray", "warning", "SingleObserver", "Collection", "INTERNAL_PREFIX_KEY", "_rs", "ResizeObserver", "props", "ref", "children", "childNodes", "process", "env", "NODE_ENV", "length", "map", "child", "index", "key", "concat", "createElement", "undefined", "RefResizeObserver", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-resize-observer/es/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { warning } from \"rc-util/es/warning\";\nimport SingleObserver from \"./SingleObserver\";\nimport { Collection } from \"./Collection\";\nvar INTERNAL_PREFIX_KEY = 'rc-observer-key';\nimport { _rs } from \"./utils/observerUtil\";\nexport { /** @private Test only for mock trigger resize event */\n_rs };\nfunction ResizeObserver(props, ref) {\n  var children = props.children;\n  var childNodes = typeof children === 'function' ? [children] : toArray(children);\n  if (process.env.NODE_ENV !== 'production') {\n    if (childNodes.length > 1) {\n      warning(false, 'Find more than one child node with `children` in ResizeObserver. Please use ResizeObserver.Collection instead.');\n    } else if (childNodes.length === 0) {\n      warning(false, '`children` of ResizeObserver is empty. Nothing is in observe.');\n    }\n  }\n  return childNodes.map(function (child, index) {\n    var key = (child === null || child === void 0 ? void 0 : child.key) || \"\".concat(INTERNAL_PREFIX_KEY, \"-\").concat(index);\n    return /*#__PURE__*/React.createElement(SingleObserver, _extends({}, props, {\n      key: key,\n      ref: index === 0 ? ref : undefined\n    }), child);\n  });\n}\nvar RefResizeObserver = /*#__PURE__*/React.forwardRef(ResizeObserver);\nif (process.env.NODE_ENV !== 'production') {\n  RefResizeObserver.displayName = 'ResizeObserver';\n}\nRefResizeObserver.Collection = Collection;\nexport default RefResizeObserver;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,IAAIC,mBAAmB,GAAG,iBAAiB;AAC3C,SAASC,GAAG,QAAQ,sBAAsB;AAC1C,SAAS;AACTA,GAAG;AACH,SAASC,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;EAC7B,IAAIC,UAAU,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAG,CAACA,QAAQ,CAAC,GAAGT,OAAO,CAACS,QAAQ,CAAC;EAChF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACzBb,OAAO,CAAC,KAAK,EAAE,gHAAgH,CAAC;IAClI,CAAC,MAAM,IAAIS,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;MAClCb,OAAO,CAAC,KAAK,EAAE,+DAA+D,CAAC;IACjF;EACF;EACA,OAAOS,UAAU,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC5C,IAAIC,GAAG,GAAG,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,KAAK,EAAE,CAACC,MAAM,CAACf,mBAAmB,EAAE,GAAG,CAAC,CAACe,MAAM,CAACF,KAAK,CAAC;IACxH,OAAO,aAAalB,KAAK,CAACqB,aAAa,CAAClB,cAAc,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;MAC1EW,GAAG,EAAEA,GAAG;MACRV,GAAG,EAAES,KAAK,KAAK,CAAC,GAAGT,GAAG,GAAGa;IAC3B,CAAC,CAAC,EAAEL,KAAK,CAAC;EACZ,CAAC,CAAC;AACJ;AACA,IAAIM,iBAAiB,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAACjB,cAAc,CAAC;AACrE,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCS,iBAAiB,CAACE,WAAW,GAAG,gBAAgB;AAClD;AACAF,iBAAiB,CAACnB,UAAU,GAAGA,UAAU;AACzC,eAAemB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}