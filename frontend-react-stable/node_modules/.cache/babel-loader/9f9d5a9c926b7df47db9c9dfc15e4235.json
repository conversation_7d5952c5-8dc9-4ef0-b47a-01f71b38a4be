{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"rootClassName\", \"style\", \"className\", \"tabIndex\", \"items\", \"children\", \"direction\", \"id\", \"mode\", \"inlineCollapsed\", \"disabled\", \"disabledOverflow\", \"subMenuOpenDelay\", \"subMenuCloseDelay\", \"forceSubMenuRender\", \"defaultOpenKeys\", \"openKeys\", \"activeKey\", \"defaultActiveFirst\", \"selectable\", \"multiple\", \"defaultSelectedKeys\", \"selectedKeys\", \"onSelect\", \"onDeselect\", \"inlineIndent\", \"motion\", \"defaultMotions\", \"triggerSubMenuAction\", \"builtinPlacements\", \"itemIcon\", \"expandIcon\", \"overflowedIndicator\", \"overflowedIndicatorPopupClassName\", \"getPopupContainer\", \"onClick\", \"onOpenChange\", \"onKeyDown\", \"openAnimation\", \"openTransitionName\", \"_internalRenderMenuItem\", \"_internalRenderSubMenuItem\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport Overflow from 'rc-overflow';\nimport MenuItem from './MenuItem';\nimport { parseItems } from './utils/nodeUtil';\nimport MenuContextProvider from './context/MenuContext';\nimport useMemoCallback from './hooks/useMemoCallback';\nimport { warnItemProp } from './utils/warnUtil';\nimport SubMenu from './SubMenu';\nimport useAccessibility from './hooks/useAccessibility';\nimport useUUID from './hooks/useUUID';\nimport { PathRegisterContext, PathUserContext } from './context/PathContext';\nimport useKeyRecords, { OVERFLOW_KEY } from './hooks/useKeyRecords';\nimport { getMenuId, IdContext } from './context/IdContext';\nimport PrivateContext from './context/PrivateContext';\nimport { useImperativeHandle } from 'react';\n/**\n * Menu modify after refactor:\n * ## Add\n * - disabled\n *\n * ## Remove\n * - openTransitionName\n * - openAnimation\n * - onDestroy\n * - siderCollapsed: Seems antd do not use this prop (Need test in antd)\n * - collapsedWidth: Seems this logic should be handle by antd Layout.Sider\n */\n// optimize for render\n\nvar EMPTY_LIST = [];\nvar Menu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _childList$, _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-menu' : _props$prefixCls,\n    rootClassName = props.rootClassName,\n    style = props.style,\n    className = props.className,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    items = props.items,\n    children = props.children,\n    direction = props.direction,\n    id = props.id,\n    _props$mode = props.mode,\n    mode = _props$mode === void 0 ? 'vertical' : _props$mode,\n    inlineCollapsed = props.inlineCollapsed,\n    disabled = props.disabled,\n    disabledOverflow = props.disabledOverflow,\n    _props$subMenuOpenDel = props.subMenuOpenDelay,\n    subMenuOpenDelay = _props$subMenuOpenDel === void 0 ? 0.1 : _props$subMenuOpenDel,\n    _props$subMenuCloseDe = props.subMenuCloseDelay,\n    subMenuCloseDelay = _props$subMenuCloseDe === void 0 ? 0.1 : _props$subMenuCloseDe,\n    forceSubMenuRender = props.forceSubMenuRender,\n    defaultOpenKeys = props.defaultOpenKeys,\n    openKeys = props.openKeys,\n    activeKey = props.activeKey,\n    defaultActiveFirst = props.defaultActiveFirst,\n    _props$selectable = props.selectable,\n    selectable = _props$selectable === void 0 ? true : _props$selectable,\n    _props$multiple = props.multiple,\n    multiple = _props$multiple === void 0 ? false : _props$multiple,\n    defaultSelectedKeys = props.defaultSelectedKeys,\n    selectedKeys = props.selectedKeys,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$inlineIndent = props.inlineIndent,\n    inlineIndent = _props$inlineIndent === void 0 ? 24 : _props$inlineIndent,\n    motion = props.motion,\n    defaultMotions = props.defaultMotions,\n    _props$triggerSubMenu = props.triggerSubMenuAction,\n    triggerSubMenuAction = _props$triggerSubMenu === void 0 ? 'hover' : _props$triggerSubMenu,\n    builtinPlacements = props.builtinPlacements,\n    itemIcon = props.itemIcon,\n    expandIcon = props.expandIcon,\n    _props$overflowedIndi = props.overflowedIndicator,\n    overflowedIndicator = _props$overflowedIndi === void 0 ? '...' : _props$overflowedIndi,\n    overflowedIndicatorPopupClassName = props.overflowedIndicatorPopupClassName,\n    getPopupContainer = props.getPopupContainer,\n    onClick = props.onClick,\n    onOpenChange = props.onOpenChange,\n    onKeyDown = props.onKeyDown,\n    openAnimation = props.openAnimation,\n    openTransitionName = props.openTransitionName,\n    _internalRenderMenuItem = props._internalRenderMenuItem,\n    _internalRenderSubMenuItem = props._internalRenderSubMenuItem,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var childList = React.useMemo(function () {\n    return parseItems(children, items, EMPTY_LIST);\n  }, [children, items]);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var containerRef = React.useRef();\n  var uuid = useUUID(id);\n  var isRtl = direction === 'rtl'; // ========================= Warn =========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!openAnimation && !openTransitionName, '`openAnimation` and `openTransitionName` is removed. Please use `motion` or `defaultMotion` instead.');\n  } // ========================= Mode =========================\n\n  var _React$useMemo = React.useMemo(function () {\n      if ((mode === 'inline' || mode === 'vertical') && inlineCollapsed) {\n        return ['vertical', inlineCollapsed];\n      }\n      return [mode, false];\n    }, [mode, inlineCollapsed]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    mergedMode = _React$useMemo2[0],\n    mergedInlineCollapsed = _React$useMemo2[1]; // ====================== Responsive ======================\n\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    lastVisibleIndex = _React$useState4[0],\n    setLastVisibleIndex = _React$useState4[1];\n  var allVisible = lastVisibleIndex >= childList.length - 1 || mergedMode !== 'horizontal' || disabledOverflow; // ========================= Open =========================\n\n  var _useMergedState = useMergedState(defaultOpenKeys, {\n      value: openKeys,\n      postState: function postState(keys) {\n        return keys || EMPTY_LIST;\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedOpenKeys = _useMergedState2[0],\n    setMergedOpenKeys = _useMergedState2[1];\n  var triggerOpenKeys = function triggerOpenKeys(keys) {\n    setMergedOpenKeys(keys);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(keys);\n  }; // >>>>> Cache & Reset open keys when inlineCollapsed changed\n\n  var _React$useState5 = React.useState(mergedOpenKeys),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inlineCacheOpenKeys = _React$useState6[0],\n    setInlineCacheOpenKeys = _React$useState6[1];\n  var isInlineMode = mergedMode === 'inline';\n  var mountRef = React.useRef(false); // Cache\n\n  React.useEffect(function () {\n    if (isInlineMode) {\n      setInlineCacheOpenKeys(mergedOpenKeys);\n    }\n  }, [mergedOpenKeys]); // Restore\n\n  React.useEffect(function () {\n    if (!mountRef.current) {\n      return;\n    }\n    if (isInlineMode) {\n      setMergedOpenKeys(inlineCacheOpenKeys);\n    } else {\n      // Trigger open event in case its in control\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }, [isInlineMode]);\n  React.useEffect(function () {\n    mountRef.current = true;\n    return function () {\n      mountRef.current = false;\n    };\n  }, []); // ========================= Path =========================\n\n  var _useKeyRecords = useKeyRecords(),\n    registerPath = _useKeyRecords.registerPath,\n    unregisterPath = _useKeyRecords.unregisterPath,\n    refreshOverflowKeys = _useKeyRecords.refreshOverflowKeys,\n    isSubPathKey = _useKeyRecords.isSubPathKey,\n    getKeyPath = _useKeyRecords.getKeyPath,\n    getKeys = _useKeyRecords.getKeys,\n    getSubPathKeys = _useKeyRecords.getSubPathKeys;\n  var registerPathContext = React.useMemo(function () {\n    return {\n      registerPath: registerPath,\n      unregisterPath: unregisterPath\n    };\n  }, [registerPath, unregisterPath]);\n  var pathUserContext = React.useMemo(function () {\n    return {\n      isSubPathKey: isSubPathKey\n    };\n  }, [isSubPathKey]);\n  React.useEffect(function () {\n    refreshOverflowKeys(allVisible ? EMPTY_LIST : childList.slice(lastVisibleIndex + 1).map(function (child) {\n      return child.key;\n    }));\n  }, [lastVisibleIndex, allVisible]); // ======================== Active ========================\n\n  var _useMergedState3 = useMergedState(activeKey || defaultActiveFirst && ((_childList$ = childList[0]) === null || _childList$ === void 0 ? void 0 : _childList$.key), {\n      value: activeKey\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedActiveKey = _useMergedState4[0],\n    setMergedActiveKey = _useMergedState4[1];\n  var onActive = useMemoCallback(function (key) {\n    setMergedActiveKey(key);\n  });\n  var onInactive = useMemoCallback(function () {\n    setMergedActiveKey(undefined);\n  });\n  useImperativeHandle(ref, function () {\n    return {\n      list: containerRef.current,\n      focus: function focus(options) {\n        var _childList$find;\n        var shouldFocusKey = mergedActiveKey !== null && mergedActiveKey !== void 0 ? mergedActiveKey : (_childList$find = childList.find(function (node) {\n          return !node.props.disabled;\n        })) === null || _childList$find === void 0 ? void 0 : _childList$find.key;\n        if (shouldFocusKey) {\n          var _containerRef$current, _containerRef$current2, _containerRef$current3;\n          (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : (_containerRef$current2 = _containerRef$current.querySelector(\"li[data-menu-id='\".concat(getMenuId(uuid, shouldFocusKey), \"']\"))) === null || _containerRef$current2 === void 0 ? void 0 : (_containerRef$current3 = _containerRef$current2.focus) === null || _containerRef$current3 === void 0 ? void 0 : _containerRef$current3.call(_containerRef$current2, options);\n        }\n      }\n    };\n  }); // ======================== Select ========================\n  // >>>>> Select keys\n\n  var _useMergedState5 = useMergedState(defaultSelectedKeys || [], {\n      value: selectedKeys,\n      // Legacy convert key to array\n      postState: function postState(keys) {\n        if (Array.isArray(keys)) {\n          return keys;\n        }\n        if (keys === null || keys === undefined) {\n          return EMPTY_LIST;\n        }\n        return [keys];\n      }\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedSelectKeys = _useMergedState6[0],\n    setMergedSelectKeys = _useMergedState6[1]; // >>>>> Trigger select\n\n  var triggerSelection = function triggerSelection(info) {\n    if (selectable) {\n      // Insert or Remove\n      var targetKey = info.key;\n      var exist = mergedSelectKeys.includes(targetKey);\n      var newSelectKeys;\n      if (multiple) {\n        if (exist) {\n          newSelectKeys = mergedSelectKeys.filter(function (key) {\n            return key !== targetKey;\n          });\n        } else {\n          newSelectKeys = [].concat(_toConsumableArray(mergedSelectKeys), [targetKey]);\n        }\n      } else {\n        newSelectKeys = [targetKey];\n      }\n      setMergedSelectKeys(newSelectKeys); // Trigger event\n\n      var selectInfo = _objectSpread(_objectSpread({}, info), {}, {\n        selectedKeys: newSelectKeys\n      });\n      if (exist) {\n        onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectInfo);\n      } else {\n        onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectInfo);\n      }\n    } // Whatever selectable, always close it\n\n    if (!multiple && mergedOpenKeys.length && mergedMode !== 'inline') {\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }; // ========================= Open =========================\n\n  /**\n   * Click for item. SubMenu do not have selection status\n   */\n\n  var onInternalClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(warnItemProp(info));\n    triggerSelection(info);\n  });\n  var onInternalOpenChange = useMemoCallback(function (key, open) {\n    var newOpenKeys = mergedOpenKeys.filter(function (k) {\n      return k !== key;\n    });\n    if (open) {\n      newOpenKeys.push(key);\n    } else if (mergedMode !== 'inline') {\n      // We need find all related popup to close\n      var subPathKeys = getSubPathKeys(key);\n      newOpenKeys = newOpenKeys.filter(function (k) {\n        return !subPathKeys.has(k);\n      });\n    }\n    if (!shallowEqual(mergedOpenKeys, newOpenKeys)) {\n      triggerOpenKeys(newOpenKeys);\n    }\n  });\n  var getInternalPopupContainer = useMemoCallback(getPopupContainer); // ==================== Accessibility =====================\n\n  var triggerAccessibilityOpen = function triggerAccessibilityOpen(key, open) {\n    var nextOpen = open !== null && open !== void 0 ? open : !mergedOpenKeys.includes(key);\n    onInternalOpenChange(key, nextOpen);\n  };\n  var onInternalKeyDown = useAccessibility(mergedMode, mergedActiveKey, isRtl, uuid, containerRef, getKeys, getKeyPath, setMergedActiveKey, triggerAccessibilityOpen, onKeyDown); // ======================== Effect ========================\n\n  React.useEffect(function () {\n    setMounted(true);\n  }, []); // ======================= Context ========================\n\n  var privateContext = React.useMemo(function () {\n    return {\n      _internalRenderMenuItem: _internalRenderMenuItem,\n      _internalRenderSubMenuItem: _internalRenderSubMenuItem\n    };\n  }, [_internalRenderMenuItem, _internalRenderSubMenuItem]); // ======================== Render ========================\n  // >>>>> Children\n\n  var wrappedChildList = mergedMode !== 'horizontal' || disabledOverflow ? childList :\n  // Need wrap for overflow dropdown that do not response for open\n  childList.map(function (child, index) {\n    return (/*#__PURE__*/\n      // Always wrap provider to avoid sub node re-mount\n      React.createElement(MenuContextProvider, {\n        key: child.key,\n        overflowDisabled: index > lastVisibleIndex\n      }, child)\n    );\n  }); // >>>>> Container\n\n  var container = /*#__PURE__*/React.createElement(Overflow, _extends({\n    id: id,\n    ref: containerRef,\n    prefixCls: \"\".concat(prefixCls, \"-overflow\"),\n    component: \"ul\",\n    itemComponent: MenuItem,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-root\"), \"\".concat(prefixCls, \"-\").concat(mergedMode), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-inline-collapsed\"), mergedInlineCollapsed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), isRtl), _classNames), rootClassName),\n    dir: direction,\n    style: style,\n    role: \"menu\",\n    tabIndex: tabIndex,\n    data: wrappedChildList,\n    renderRawItem: function renderRawItem(node) {\n      return node;\n    },\n    renderRawRest: function renderRawRest(omitItems) {\n      // We use origin list since wrapped list use context to prevent open\n      var len = omitItems.length;\n      var originOmitItems = len ? childList.slice(-len) : null;\n      return /*#__PURE__*/React.createElement(SubMenu, {\n        eventKey: OVERFLOW_KEY,\n        title: overflowedIndicator,\n        disabled: allVisible,\n        internalPopupClose: len === 0,\n        popupClassName: overflowedIndicatorPopupClassName\n      }, originOmitItems);\n    },\n    maxCount: mergedMode !== 'horizontal' || disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n    ssr: \"full\",\n    \"data-menu-list\": true,\n    onVisibleChange: function onVisibleChange(newLastIndex) {\n      setLastVisibleIndex(newLastIndex);\n    },\n    onKeyDown: onInternalKeyDown\n  }, restProps)); // >>>>> Render\n\n  return /*#__PURE__*/React.createElement(PrivateContext.Provider, {\n    value: privateContext\n  }, /*#__PURE__*/React.createElement(IdContext.Provider, {\n    value: uuid\n  }, /*#__PURE__*/React.createElement(MenuContextProvider, {\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    mode: mergedMode,\n    openKeys: mergedOpenKeys,\n    rtl: isRtl // Disabled\n    ,\n\n    disabled: disabled // Motion\n    ,\n\n    motion: mounted ? motion : null,\n    defaultMotions: mounted ? defaultMotions : null // Active\n    ,\n\n    activeKey: mergedActiveKey,\n    onActive: onActive,\n    onInactive: onInactive // Selection\n    ,\n\n    selectedKeys: mergedSelectKeys // Level\n    ,\n\n    inlineIndent: inlineIndent // Popup\n    ,\n\n    subMenuOpenDelay: subMenuOpenDelay,\n    subMenuCloseDelay: subMenuCloseDelay,\n    forceSubMenuRender: forceSubMenuRender,\n    builtinPlacements: builtinPlacements,\n    triggerSubMenuAction: triggerSubMenuAction,\n    getPopupContainer: getInternalPopupContainer // Icon\n    ,\n\n    itemIcon: itemIcon,\n    expandIcon: expandIcon // Events\n    ,\n\n    onItemClick: onInternalClick,\n    onOpenChange: onInternalOpenChange\n  }, /*#__PURE__*/React.createElement(PathUserContext.Provider, {\n    value: pathUserContext\n  }, container), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none'\n    },\n    \"aria-hidden\": true\n  }, /*#__PURE__*/React.createElement(PathRegisterContext.Provider, {\n    value: registerPathContext\n  }, childList)))));\n});\nexport default Menu;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_toConsumableArray", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "classNames", "shallowEqual", "useMergedState", "warning", "Overflow", "MenuItem", "parseItems", "MenuContextProvider", "useMemoCallback", "warnItemProp", "SubMenu", "useAccessibility", "useUUID", "PathRegisterContext", "PathUserContext", "useKeyRecords", "OVERFLOW_KEY", "getMenuId", "IdContext", "PrivateContext", "useImperativeHandle", "EMPTY_LIST", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_childList$", "_classNames", "_props$prefixCls", "prefixCls", "rootClassName", "style", "className", "_props$tabIndex", "tabIndex", "items", "children", "direction", "id", "_props$mode", "mode", "inlineCollapsed", "disabled", "disabledOverflow", "_props$subMenuOpenDel", "subMenuOpenDelay", "_props$subMenuCloseDe", "subMenuCloseDelay", "forceSubMenuRender", "defaultOpenKeys", "openKeys", "active<PERSON><PERSON>", "defaultActiveFirst", "_props$selectable", "selectable", "_props$multiple", "multiple", "defaultSelectedKeys", "<PERSON><PERSON><PERSON><PERSON>", "onSelect", "onDeselect", "_props$inlineIndent", "inlineIndent", "motion", "defaultMotions", "_props$triggerSubMenu", "triggerSubMenuAction", "builtinPlacements", "itemIcon", "expandIcon", "_props$overflowedIndi", "overflowedIndicator", "overflowedIndicatorPopupClassName", "getPopupContainer", "onClick", "onOpenChange", "onKeyDown", "openAnimation", "openTransitionName", "_internalRenderMenuItem", "_internalRenderSubMenuItem", "restProps", "childList", "useMemo", "_React$useState", "useState", "_React$useState2", "mounted", "setMounted", "containerRef", "useRef", "uuid", "isRtl", "process", "env", "NODE_ENV", "_React$useMemo", "_React$useMemo2", "mergedMode", "mergedInlineCollapsed", "_React$useState3", "_React$useState4", "lastVisibleIndex", "setLastVisibleIndex", "allVisible", "length", "_useMergedState", "value", "postState", "keys", "_useMergedState2", "mergedOpenKeys", "setMergedOpenKeys", "triggerOpenKeys", "_React$useState5", "_React$useState6", "inlineCacheOpenKeys", "setInlineCacheOpenKeys", "isInlineMode", "mountRef", "useEffect", "current", "_useKeyRecords", "registerPath", "unregisterPath", "refreshOverflowKeys", "isSubPath<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "getSub<PERSON><PERSON><PERSON><PERSON><PERSON>", "registerPathContext", "pathUserContext", "slice", "map", "child", "key", "_useMergedState3", "_useMergedState4", "mergedActiveKey", "setMergedActiveKey", "onActive", "onInactive", "undefined", "list", "focus", "options", "_childList$find", "shouldFocus<PERSON>ey", "find", "node", "_containerRef$current", "_containerRef$current2", "_containerRef$current3", "querySelector", "concat", "call", "_useMergedState5", "Array", "isArray", "_useMergedState6", "mergedSelectKeys", "setMergedSelectKeys", "triggerSelection", "info", "<PERSON><PERSON><PERSON>", "exist", "includes", "newSelectKeys", "filter", "selectInfo", "onInternalClick", "onInternalOpenChange", "open", "newOpenKeys", "k", "push", "subPath<PERSON><PERSON>s", "has", "getInternalPopupContainer", "triggerAccessibilityOpen", "nextOpen", "onInternalKeyDown", "privateContext", "wrappedChildList", "index", "createElement", "overflowDisabled", "container", "component", "itemComponent", "dir", "role", "data", "renderRawItem", "renderRawRest", "omitItems", "len", "originOmitItems", "eventKey", "title", "internalPopupClose", "popupClassName", "maxCount", "INVALIDATE", "RESPONSIVE", "ssr", "onVisibleChange", "newLastIndex", "Provider", "rtl", "onItemClick", "display"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/Menu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"rootClassName\", \"style\", \"className\", \"tabIndex\", \"items\", \"children\", \"direction\", \"id\", \"mode\", \"inlineCollapsed\", \"disabled\", \"disabledOverflow\", \"subMenuOpenDelay\", \"subMenuCloseDelay\", \"forceSubMenuRender\", \"defaultOpenKeys\", \"openKeys\", \"activeKey\", \"defaultActiveFirst\", \"selectable\", \"multiple\", \"defaultSelectedKeys\", \"selectedKeys\", \"onSelect\", \"onDeselect\", \"inlineIndent\", \"motion\", \"defaultMotions\", \"triggerSubMenuAction\", \"builtinPlacements\", \"itemIcon\", \"expandIcon\", \"overflowedIndicator\", \"overflowedIndicatorPopupClassName\", \"getPopupContainer\", \"onClick\", \"onOpenChange\", \"onKeyDown\", \"openAnimation\", \"openTransitionName\", \"_internalRenderMenuItem\", \"_internalRenderSubMenuItem\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport Overflow from 'rc-overflow';\nimport MenuItem from './MenuItem';\nimport { parseItems } from './utils/nodeUtil';\nimport MenuContextProvider from './context/MenuContext';\nimport useMemoCallback from './hooks/useMemoCallback';\nimport { warnItemProp } from './utils/warnUtil';\nimport SubMenu from './SubMenu';\nimport useAccessibility from './hooks/useAccessibility';\nimport useUUID from './hooks/useUUID';\nimport { PathRegisterContext, PathUserContext } from './context/PathContext';\nimport useKeyRecords, { OVERFLOW_KEY } from './hooks/useKeyRecords';\nimport { getMenuId, IdContext } from './context/IdContext';\nimport PrivateContext from './context/PrivateContext';\nimport { useImperativeHandle } from 'react';\n/**\n * Menu modify after refactor:\n * ## Add\n * - disabled\n *\n * ## Remove\n * - openTransitionName\n * - openAnimation\n * - onDestroy\n * - siderCollapsed: Seems antd do not use this prop (Need test in antd)\n * - collapsedWidth: Seems this logic should be handle by antd Layout.Sider\n */\n// optimize for render\n\nvar EMPTY_LIST = [];\nvar Menu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _childList$, _classNames;\n\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-menu' : _props$prefixCls,\n      rootClassName = props.rootClassName,\n      style = props.style,\n      className = props.className,\n      _props$tabIndex = props.tabIndex,\n      tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n      items = props.items,\n      children = props.children,\n      direction = props.direction,\n      id = props.id,\n      _props$mode = props.mode,\n      mode = _props$mode === void 0 ? 'vertical' : _props$mode,\n      inlineCollapsed = props.inlineCollapsed,\n      disabled = props.disabled,\n      disabledOverflow = props.disabledOverflow,\n      _props$subMenuOpenDel = props.subMenuOpenDelay,\n      subMenuOpenDelay = _props$subMenuOpenDel === void 0 ? 0.1 : _props$subMenuOpenDel,\n      _props$subMenuCloseDe = props.subMenuCloseDelay,\n      subMenuCloseDelay = _props$subMenuCloseDe === void 0 ? 0.1 : _props$subMenuCloseDe,\n      forceSubMenuRender = props.forceSubMenuRender,\n      defaultOpenKeys = props.defaultOpenKeys,\n      openKeys = props.openKeys,\n      activeKey = props.activeKey,\n      defaultActiveFirst = props.defaultActiveFirst,\n      _props$selectable = props.selectable,\n      selectable = _props$selectable === void 0 ? true : _props$selectable,\n      _props$multiple = props.multiple,\n      multiple = _props$multiple === void 0 ? false : _props$multiple,\n      defaultSelectedKeys = props.defaultSelectedKeys,\n      selectedKeys = props.selectedKeys,\n      onSelect = props.onSelect,\n      onDeselect = props.onDeselect,\n      _props$inlineIndent = props.inlineIndent,\n      inlineIndent = _props$inlineIndent === void 0 ? 24 : _props$inlineIndent,\n      motion = props.motion,\n      defaultMotions = props.defaultMotions,\n      _props$triggerSubMenu = props.triggerSubMenuAction,\n      triggerSubMenuAction = _props$triggerSubMenu === void 0 ? 'hover' : _props$triggerSubMenu,\n      builtinPlacements = props.builtinPlacements,\n      itemIcon = props.itemIcon,\n      expandIcon = props.expandIcon,\n      _props$overflowedIndi = props.overflowedIndicator,\n      overflowedIndicator = _props$overflowedIndi === void 0 ? '...' : _props$overflowedIndi,\n      overflowedIndicatorPopupClassName = props.overflowedIndicatorPopupClassName,\n      getPopupContainer = props.getPopupContainer,\n      onClick = props.onClick,\n      onOpenChange = props.onOpenChange,\n      onKeyDown = props.onKeyDown,\n      openAnimation = props.openAnimation,\n      openTransitionName = props.openTransitionName,\n      _internalRenderMenuItem = props._internalRenderMenuItem,\n      _internalRenderSubMenuItem = props._internalRenderSubMenuItem,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var childList = React.useMemo(function () {\n    return parseItems(children, items, EMPTY_LIST);\n  }, [children, items]);\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mounted = _React$useState2[0],\n      setMounted = _React$useState2[1];\n\n  var containerRef = React.useRef();\n  var uuid = useUUID(id);\n  var isRtl = direction === 'rtl'; // ========================= Warn =========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!openAnimation && !openTransitionName, '`openAnimation` and `openTransitionName` is removed. Please use `motion` or `defaultMotion` instead.');\n  } // ========================= Mode =========================\n\n\n  var _React$useMemo = React.useMemo(function () {\n    if ((mode === 'inline' || mode === 'vertical') && inlineCollapsed) {\n      return ['vertical', inlineCollapsed];\n    }\n\n    return [mode, false];\n  }, [mode, inlineCollapsed]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      mergedMode = _React$useMemo2[0],\n      mergedInlineCollapsed = _React$useMemo2[1]; // ====================== Responsive ======================\n\n\n  var _React$useState3 = React.useState(0),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      lastVisibleIndex = _React$useState4[0],\n      setLastVisibleIndex = _React$useState4[1];\n\n  var allVisible = lastVisibleIndex >= childList.length - 1 || mergedMode !== 'horizontal' || disabledOverflow; // ========================= Open =========================\n\n  var _useMergedState = useMergedState(defaultOpenKeys, {\n    value: openKeys,\n    postState: function postState(keys) {\n      return keys || EMPTY_LIST;\n    }\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedOpenKeys = _useMergedState2[0],\n      setMergedOpenKeys = _useMergedState2[1];\n\n  var triggerOpenKeys = function triggerOpenKeys(keys) {\n    setMergedOpenKeys(keys);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(keys);\n  }; // >>>>> Cache & Reset open keys when inlineCollapsed changed\n\n\n  var _React$useState5 = React.useState(mergedOpenKeys),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      inlineCacheOpenKeys = _React$useState6[0],\n      setInlineCacheOpenKeys = _React$useState6[1];\n\n  var isInlineMode = mergedMode === 'inline';\n  var mountRef = React.useRef(false); // Cache\n\n  React.useEffect(function () {\n    if (isInlineMode) {\n      setInlineCacheOpenKeys(mergedOpenKeys);\n    }\n  }, [mergedOpenKeys]); // Restore\n\n  React.useEffect(function () {\n    if (!mountRef.current) {\n      return;\n    }\n\n    if (isInlineMode) {\n      setMergedOpenKeys(inlineCacheOpenKeys);\n    } else {\n      // Trigger open event in case its in control\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }, [isInlineMode]);\n  React.useEffect(function () {\n    mountRef.current = true;\n    return function () {\n      mountRef.current = false;\n    };\n  }, []); // ========================= Path =========================\n\n  var _useKeyRecords = useKeyRecords(),\n      registerPath = _useKeyRecords.registerPath,\n      unregisterPath = _useKeyRecords.unregisterPath,\n      refreshOverflowKeys = _useKeyRecords.refreshOverflowKeys,\n      isSubPathKey = _useKeyRecords.isSubPathKey,\n      getKeyPath = _useKeyRecords.getKeyPath,\n      getKeys = _useKeyRecords.getKeys,\n      getSubPathKeys = _useKeyRecords.getSubPathKeys;\n\n  var registerPathContext = React.useMemo(function () {\n    return {\n      registerPath: registerPath,\n      unregisterPath: unregisterPath\n    };\n  }, [registerPath, unregisterPath]);\n  var pathUserContext = React.useMemo(function () {\n    return {\n      isSubPathKey: isSubPathKey\n    };\n  }, [isSubPathKey]);\n  React.useEffect(function () {\n    refreshOverflowKeys(allVisible ? EMPTY_LIST : childList.slice(lastVisibleIndex + 1).map(function (child) {\n      return child.key;\n    }));\n  }, [lastVisibleIndex, allVisible]); // ======================== Active ========================\n\n  var _useMergedState3 = useMergedState(activeKey || defaultActiveFirst && ((_childList$ = childList[0]) === null || _childList$ === void 0 ? void 0 : _childList$.key), {\n    value: activeKey\n  }),\n      _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n      mergedActiveKey = _useMergedState4[0],\n      setMergedActiveKey = _useMergedState4[1];\n\n  var onActive = useMemoCallback(function (key) {\n    setMergedActiveKey(key);\n  });\n  var onInactive = useMemoCallback(function () {\n    setMergedActiveKey(undefined);\n  });\n  useImperativeHandle(ref, function () {\n    return {\n      list: containerRef.current,\n      focus: function focus(options) {\n        var _childList$find;\n\n        var shouldFocusKey = mergedActiveKey !== null && mergedActiveKey !== void 0 ? mergedActiveKey : (_childList$find = childList.find(function (node) {\n          return !node.props.disabled;\n        })) === null || _childList$find === void 0 ? void 0 : _childList$find.key;\n\n        if (shouldFocusKey) {\n          var _containerRef$current, _containerRef$current2, _containerRef$current3;\n\n          (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : (_containerRef$current2 = _containerRef$current.querySelector(\"li[data-menu-id='\".concat(getMenuId(uuid, shouldFocusKey), \"']\"))) === null || _containerRef$current2 === void 0 ? void 0 : (_containerRef$current3 = _containerRef$current2.focus) === null || _containerRef$current3 === void 0 ? void 0 : _containerRef$current3.call(_containerRef$current2, options);\n        }\n      }\n    };\n  }); // ======================== Select ========================\n  // >>>>> Select keys\n\n  var _useMergedState5 = useMergedState(defaultSelectedKeys || [], {\n    value: selectedKeys,\n    // Legacy convert key to array\n    postState: function postState(keys) {\n      if (Array.isArray(keys)) {\n        return keys;\n      }\n\n      if (keys === null || keys === undefined) {\n        return EMPTY_LIST;\n      }\n\n      return [keys];\n    }\n  }),\n      _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n      mergedSelectKeys = _useMergedState6[0],\n      setMergedSelectKeys = _useMergedState6[1]; // >>>>> Trigger select\n\n\n  var triggerSelection = function triggerSelection(info) {\n    if (selectable) {\n      // Insert or Remove\n      var targetKey = info.key;\n      var exist = mergedSelectKeys.includes(targetKey);\n      var newSelectKeys;\n\n      if (multiple) {\n        if (exist) {\n          newSelectKeys = mergedSelectKeys.filter(function (key) {\n            return key !== targetKey;\n          });\n        } else {\n          newSelectKeys = [].concat(_toConsumableArray(mergedSelectKeys), [targetKey]);\n        }\n      } else {\n        newSelectKeys = [targetKey];\n      }\n\n      setMergedSelectKeys(newSelectKeys); // Trigger event\n\n      var selectInfo = _objectSpread(_objectSpread({}, info), {}, {\n        selectedKeys: newSelectKeys\n      });\n\n      if (exist) {\n        onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectInfo);\n      } else {\n        onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectInfo);\n      }\n    } // Whatever selectable, always close it\n\n\n    if (!multiple && mergedOpenKeys.length && mergedMode !== 'inline') {\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }; // ========================= Open =========================\n\n  /**\n   * Click for item. SubMenu do not have selection status\n   */\n\n\n  var onInternalClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(warnItemProp(info));\n    triggerSelection(info);\n  });\n  var onInternalOpenChange = useMemoCallback(function (key, open) {\n    var newOpenKeys = mergedOpenKeys.filter(function (k) {\n      return k !== key;\n    });\n\n    if (open) {\n      newOpenKeys.push(key);\n    } else if (mergedMode !== 'inline') {\n      // We need find all related popup to close\n      var subPathKeys = getSubPathKeys(key);\n      newOpenKeys = newOpenKeys.filter(function (k) {\n        return !subPathKeys.has(k);\n      });\n    }\n\n    if (!shallowEqual(mergedOpenKeys, newOpenKeys)) {\n      triggerOpenKeys(newOpenKeys);\n    }\n  });\n  var getInternalPopupContainer = useMemoCallback(getPopupContainer); // ==================== Accessibility =====================\n\n  var triggerAccessibilityOpen = function triggerAccessibilityOpen(key, open) {\n    var nextOpen = open !== null && open !== void 0 ? open : !mergedOpenKeys.includes(key);\n    onInternalOpenChange(key, nextOpen);\n  };\n\n  var onInternalKeyDown = useAccessibility(mergedMode, mergedActiveKey, isRtl, uuid, containerRef, getKeys, getKeyPath, setMergedActiveKey, triggerAccessibilityOpen, onKeyDown); // ======================== Effect ========================\n\n  React.useEffect(function () {\n    setMounted(true);\n  }, []); // ======================= Context ========================\n\n  var privateContext = React.useMemo(function () {\n    return {\n      _internalRenderMenuItem: _internalRenderMenuItem,\n      _internalRenderSubMenuItem: _internalRenderSubMenuItem\n    };\n  }, [_internalRenderMenuItem, _internalRenderSubMenuItem]); // ======================== Render ========================\n  // >>>>> Children\n\n  var wrappedChildList = mergedMode !== 'horizontal' || disabledOverflow ? childList : // Need wrap for overflow dropdown that do not response for open\n  childList.map(function (child, index) {\n    return (\n      /*#__PURE__*/\n      // Always wrap provider to avoid sub node re-mount\n      React.createElement(MenuContextProvider, {\n        key: child.key,\n        overflowDisabled: index > lastVisibleIndex\n      }, child)\n    );\n  }); // >>>>> Container\n\n  var container = /*#__PURE__*/React.createElement(Overflow, _extends({\n    id: id,\n    ref: containerRef,\n    prefixCls: \"\".concat(prefixCls, \"-overflow\"),\n    component: \"ul\",\n    itemComponent: MenuItem,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-root\"), \"\".concat(prefixCls, \"-\").concat(mergedMode), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-inline-collapsed\"), mergedInlineCollapsed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), isRtl), _classNames), rootClassName),\n    dir: direction,\n    style: style,\n    role: \"menu\",\n    tabIndex: tabIndex,\n    data: wrappedChildList,\n    renderRawItem: function renderRawItem(node) {\n      return node;\n    },\n    renderRawRest: function renderRawRest(omitItems) {\n      // We use origin list since wrapped list use context to prevent open\n      var len = omitItems.length;\n      var originOmitItems = len ? childList.slice(-len) : null;\n      return /*#__PURE__*/React.createElement(SubMenu, {\n        eventKey: OVERFLOW_KEY,\n        title: overflowedIndicator,\n        disabled: allVisible,\n        internalPopupClose: len === 0,\n        popupClassName: overflowedIndicatorPopupClassName\n      }, originOmitItems);\n    },\n    maxCount: mergedMode !== 'horizontal' || disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n    ssr: \"full\",\n    \"data-menu-list\": true,\n    onVisibleChange: function onVisibleChange(newLastIndex) {\n      setLastVisibleIndex(newLastIndex);\n    },\n    onKeyDown: onInternalKeyDown\n  }, restProps)); // >>>>> Render\n\n  return /*#__PURE__*/React.createElement(PrivateContext.Provider, {\n    value: privateContext\n  }, /*#__PURE__*/React.createElement(IdContext.Provider, {\n    value: uuid\n  }, /*#__PURE__*/React.createElement(MenuContextProvider, {\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    mode: mergedMode,\n    openKeys: mergedOpenKeys,\n    rtl: isRtl // Disabled\n    ,\n    disabled: disabled // Motion\n    ,\n    motion: mounted ? motion : null,\n    defaultMotions: mounted ? defaultMotions : null // Active\n    ,\n    activeKey: mergedActiveKey,\n    onActive: onActive,\n    onInactive: onInactive // Selection\n    ,\n    selectedKeys: mergedSelectKeys // Level\n    ,\n    inlineIndent: inlineIndent // Popup\n    ,\n    subMenuOpenDelay: subMenuOpenDelay,\n    subMenuCloseDelay: subMenuCloseDelay,\n    forceSubMenuRender: forceSubMenuRender,\n    builtinPlacements: builtinPlacements,\n    triggerSubMenuAction: triggerSubMenuAction,\n    getPopupContainer: getInternalPopupContainer // Icon\n    ,\n    itemIcon: itemIcon,\n    expandIcon: expandIcon // Events\n    ,\n    onItemClick: onInternalClick,\n    onOpenChange: onInternalOpenChange\n  }, /*#__PURE__*/React.createElement(PathUserContext.Provider, {\n    value: pathUserContext\n  }, container), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none'\n    },\n    \"aria-hidden\": true\n  }, /*#__PURE__*/React.createElement(PathRegisterContext.Provider, {\n    value: registerPathContext\n  }, childList)))));\n});\nexport default Menu;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,oBAAoB,EAAE,YAAY,EAAE,UAAU,EAAE,qBAAqB,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,UAAU,EAAE,YAAY,EAAE,qBAAqB,EAAE,mCAAmC,EAAE,mBAAmB,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,4BAA4B,CAAC;AAC3tB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,uBAAuB;AAC5E,OAAOC,aAAa,IAAIC,YAAY,QAAQ,uBAAuB;AACnE,SAASC,SAAS,EAAEC,SAAS,QAAQ,qBAAqB;AAC1D,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,mBAAmB,QAAQ,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,IAAI,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW,EAAEC,WAAW;EAE5B,IAAIC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,gBAAgB;IACtEE,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,eAAe,GAAGT,KAAK,CAACU,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,EAAE,GAAGd,KAAK,CAACc,EAAE;IACbC,WAAW,GAAGf,KAAK,CAACgB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,WAAW;IACxDE,eAAe,GAAGjB,KAAK,CAACiB,eAAe;IACvCC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,qBAAqB,GAAGpB,KAAK,CAACqB,gBAAgB;IAC9CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IACjFE,qBAAqB,GAAGtB,KAAK,CAACuB,iBAAiB;IAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IAClFE,kBAAkB,GAAGxB,KAAK,CAACwB,kBAAkB;IAC7CC,eAAe,GAAGzB,KAAK,CAACyB,eAAe;IACvCC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IACzBC,SAAS,GAAG3B,KAAK,CAAC2B,SAAS;IAC3BC,kBAAkB,GAAG5B,KAAK,CAAC4B,kBAAkB;IAC7CC,iBAAiB,GAAG7B,KAAK,CAAC8B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,eAAe,GAAG/B,KAAK,CAACgC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,mBAAmB,GAAGjC,KAAK,CAACiC,mBAAmB;IAC/CC,YAAY,GAAGlC,KAAK,CAACkC,YAAY;IACjCC,QAAQ,GAAGnC,KAAK,CAACmC,QAAQ;IACzBC,UAAU,GAAGpC,KAAK,CAACoC,UAAU;IAC7BC,mBAAmB,GAAGrC,KAAK,CAACsC,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,mBAAmB;IACxEE,MAAM,GAAGvC,KAAK,CAACuC,MAAM;IACrBC,cAAc,GAAGxC,KAAK,CAACwC,cAAc;IACrCC,qBAAqB,GAAGzC,KAAK,CAAC0C,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;IACzFE,iBAAiB,GAAG3C,KAAK,CAAC2C,iBAAiB;IAC3CC,QAAQ,GAAG5C,KAAK,CAAC4C,QAAQ;IACzBC,UAAU,GAAG7C,KAAK,CAAC6C,UAAU;IAC7BC,qBAAqB,GAAG9C,KAAK,CAAC+C,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACtFE,iCAAiC,GAAGhD,KAAK,CAACgD,iCAAiC;IAC3EC,iBAAiB,GAAGjD,KAAK,CAACiD,iBAAiB;IAC3CC,OAAO,GAAGlD,KAAK,CAACkD,OAAO;IACvBC,YAAY,GAAGnD,KAAK,CAACmD,YAAY;IACjCC,SAAS,GAAGpD,KAAK,CAACoD,SAAS;IAC3BC,aAAa,GAAGrD,KAAK,CAACqD,aAAa;IACnCC,kBAAkB,GAAGtD,KAAK,CAACsD,kBAAkB;IAC7CC,uBAAuB,GAAGvD,KAAK,CAACuD,uBAAuB;IACvDC,0BAA0B,GAAGxD,KAAK,CAACwD,0BAA0B;IAC7DC,SAAS,GAAGpF,wBAAwB,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;EAE1D,IAAIoF,SAAS,GAAGnF,KAAK,CAACoF,OAAO,CAAC,YAAY;IACxC,OAAO7E,UAAU,CAAC8B,QAAQ,EAAED,KAAK,EAAEd,UAAU,CAAC;EAChD,CAAC,EAAE,CAACe,QAAQ,EAAED,KAAK,CAAC,CAAC;EAErB,IAAIiD,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAG1F,cAAc,CAACwF,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIG,YAAY,GAAG1F,KAAK,CAAC2F,MAAM,CAAC,CAAC;EACjC,IAAIC,IAAI,GAAG/E,OAAO,CAAC0B,EAAE,CAAC;EACtB,IAAIsD,KAAK,GAAGvD,SAAS,KAAK,KAAK,CAAC,CAAC;;EAEjC,IAAIwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5F,OAAO,CAAC,CAAC0E,aAAa,IAAI,CAACC,kBAAkB,EAAE,sGAAsG,CAAC;EACxJ,CAAC,CAAC;;EAGF,IAAIkB,cAAc,GAAGjG,KAAK,CAACoF,OAAO,CAAC,YAAY;MAC7C,IAAI,CAAC3C,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,KAAKC,eAAe,EAAE;QACjE,OAAO,CAAC,UAAU,EAAEA,eAAe,CAAC;MACtC;MAEA,OAAO,CAACD,IAAI,EAAE,KAAK,CAAC;IACtB,CAAC,EAAE,CAACA,IAAI,EAAEC,eAAe,CAAC,CAAC;IACvBwD,eAAe,GAAGrG,cAAc,CAACoG,cAAc,EAAE,CAAC,CAAC;IACnDE,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,qBAAqB,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGhD,IAAIG,gBAAgB,GAAGrG,KAAK,CAACsF,QAAQ,CAAC,CAAC,CAAC;IACpCgB,gBAAgB,GAAGzG,cAAc,CAACwG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE7C,IAAIG,UAAU,GAAGF,gBAAgB,IAAIpB,SAAS,CAACuB,MAAM,GAAG,CAAC,IAAIP,UAAU,KAAK,YAAY,IAAIvD,gBAAgB,CAAC,CAAC;;EAE9G,IAAI+D,eAAe,GAAGxG,cAAc,CAAC+C,eAAe,EAAE;MACpD0D,KAAK,EAAEzD,QAAQ;MACf0D,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,OAAOA,IAAI,IAAIxF,UAAU;MAC3B;IACF,CAAC,CAAC;IACEyF,gBAAgB,GAAGlH,cAAc,CAAC8G,eAAe,EAAE,CAAC,CAAC;IACrDK,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACJ,IAAI,EAAE;IACnDG,iBAAiB,CAACH,IAAI,CAAC;IACvBlC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACkC,IAAI,CAAC;EAChF,CAAC,CAAC,CAAC;;EAGH,IAAIK,gBAAgB,GAAGnH,KAAK,CAACsF,QAAQ,CAAC0B,cAAc,CAAC;IACjDI,gBAAgB,GAAGvH,cAAc,CAACsH,gBAAgB,EAAE,CAAC,CAAC;IACtDE,mBAAmB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACzCE,sBAAsB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEhD,IAAIG,YAAY,GAAGpB,UAAU,KAAK,QAAQ;EAC1C,IAAIqB,QAAQ,GAAGxH,KAAK,CAAC2F,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEpC3F,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1B,IAAIF,YAAY,EAAE;MAChBD,sBAAsB,CAACN,cAAc,CAAC;IACxC;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEtBhH,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACD,QAAQ,CAACE,OAAO,EAAE;MACrB;IACF;IAEA,IAAIH,YAAY,EAAE;MAChBN,iBAAiB,CAACI,mBAAmB,CAAC;IACxC,CAAC,MAAM;MACL;MACAH,eAAe,CAAC5F,UAAU,CAAC;IAC7B;EACF,CAAC,EAAE,CAACiG,YAAY,CAAC,CAAC;EAClBvH,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1BD,QAAQ,CAACE,OAAO,GAAG,IAAI;IACvB,OAAO,YAAY;MACjBF,QAAQ,CAACE,OAAO,GAAG,KAAK;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIC,cAAc,GAAG3G,aAAa,CAAC,CAAC;IAChC4G,YAAY,GAAGD,cAAc,CAACC,YAAY;IAC1CC,cAAc,GAAGF,cAAc,CAACE,cAAc;IAC9CC,mBAAmB,GAAGH,cAAc,CAACG,mBAAmB;IACxDC,YAAY,GAAGJ,cAAc,CAACI,YAAY;IAC1CC,UAAU,GAAGL,cAAc,CAACK,UAAU;IACtCC,OAAO,GAAGN,cAAc,CAACM,OAAO;IAChCC,cAAc,GAAGP,cAAc,CAACO,cAAc;EAElD,IAAIC,mBAAmB,GAAGnI,KAAK,CAACoF,OAAO,CAAC,YAAY;IAClD,OAAO;MACLwC,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,YAAY,EAAEC,cAAc,CAAC,CAAC;EAClC,IAAIO,eAAe,GAAGpI,KAAK,CAACoF,OAAO,CAAC,YAAY;IAC9C,OAAO;MACL2C,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB/H,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1BK,mBAAmB,CAACrB,UAAU,GAAGnF,UAAU,GAAG6D,SAAS,CAACkD,KAAK,CAAC9B,gBAAgB,GAAG,CAAC,CAAC,CAAC+B,GAAG,CAAC,UAAUC,KAAK,EAAE;MACvG,OAAOA,KAAK,CAACC,GAAG;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACjC,gBAAgB,EAAEE,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEpC,IAAIgC,gBAAgB,GAAGtI,cAAc,CAACiD,SAAS,IAAIC,kBAAkB,KAAK,CAAC1B,WAAW,GAAGwD,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIxD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6G,GAAG,CAAC,EAAE;MACrK5B,KAAK,EAAExD;IACT,CAAC,CAAC;IACEsF,gBAAgB,GAAG7I,cAAc,CAAC4I,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE5C,IAAIG,QAAQ,GAAGpI,eAAe,CAAC,UAAU+H,GAAG,EAAE;IAC5CI,kBAAkB,CAACJ,GAAG,CAAC;EACzB,CAAC,CAAC;EACF,IAAIM,UAAU,GAAGrI,eAAe,CAAC,YAAY;IAC3CmI,kBAAkB,CAACG,SAAS,CAAC;EAC/B,CAAC,CAAC;EACF1H,mBAAmB,CAACK,GAAG,EAAE,YAAY;IACnC,OAAO;MACLsH,IAAI,EAAEtD,YAAY,CAACgC,OAAO;MAC1BuB,KAAK,EAAE,SAASA,KAAKA,CAACC,OAAO,EAAE;QAC7B,IAAIC,eAAe;QAEnB,IAAIC,cAAc,GAAGT,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAACQ,eAAe,GAAGhE,SAAS,CAACkE,IAAI,CAAC,UAAUC,IAAI,EAAE;UAChJ,OAAO,CAACA,IAAI,CAAC7H,KAAK,CAACkB,QAAQ;QAC7B,CAAC,CAAC,MAAM,IAAI,IAAIwG,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACX,GAAG;QAEzE,IAAIY,cAAc,EAAE;UAClB,IAAIG,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;UAEzE,CAACF,qBAAqB,GAAG7D,YAAY,CAACgC,OAAO,MAAM,IAAI,IAAI6B,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACG,aAAa,CAAC,mBAAmB,CAACC,MAAM,CAACzI,SAAS,CAAC0E,IAAI,EAAEwD,cAAc,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,IAAII,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,sBAAsB,CAACP,KAAK,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACG,IAAI,CAACJ,sBAAsB,EAAEN,OAAO,CAAC;QACjd;MACF;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEA,IAAIW,gBAAgB,GAAG1J,cAAc,CAACuD,mBAAmB,IAAI,EAAE,EAAE;MAC/DkD,KAAK,EAAEjD,YAAY;MACnB;MACAkD,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,IAAIgD,KAAK,CAACC,OAAO,CAACjD,IAAI,CAAC,EAAE;UACvB,OAAOA,IAAI;QACb;QAEA,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKiC,SAAS,EAAE;UACvC,OAAOzH,UAAU;QACnB;QAEA,OAAO,CAACwF,IAAI,CAAC;MACf;IACF,CAAC,CAAC;IACEkD,gBAAgB,GAAGnK,cAAc,CAACgK,gBAAgB,EAAE,CAAC,CAAC;IACtDI,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/C,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrD,IAAI7G,UAAU,EAAE;MACd;MACA,IAAI8G,SAAS,GAAGD,IAAI,CAAC5B,GAAG;MACxB,IAAI8B,KAAK,GAAGL,gBAAgB,CAACM,QAAQ,CAACF,SAAS,CAAC;MAChD,IAAIG,aAAa;MAEjB,IAAI/G,QAAQ,EAAE;QACZ,IAAI6G,KAAK,EAAE;UACTE,aAAa,GAAGP,gBAAgB,CAACQ,MAAM,CAAC,UAAUjC,GAAG,EAAE;YACrD,OAAOA,GAAG,KAAK6B,SAAS;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACLG,aAAa,GAAG,EAAE,CAACb,MAAM,CAAC/J,kBAAkB,CAACqK,gBAAgB,CAAC,EAAE,CAACI,SAAS,CAAC,CAAC;QAC9E;MACF,CAAC,MAAM;QACLG,aAAa,GAAG,CAACH,SAAS,CAAC;MAC7B;MAEAH,mBAAmB,CAACM,aAAa,CAAC,CAAC,CAAC;;MAEpC,IAAIE,UAAU,GAAG/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyK,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1DzG,YAAY,EAAE6G;MAChB,CAAC,CAAC;MAEF,IAAIF,KAAK,EAAE;QACTzG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC6G,UAAU,CAAC;MAChF,CAAC,MAAM;QACL9G,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC8G,UAAU,CAAC;MAC1E;IACF,CAAC,CAAC;;IAGF,IAAI,CAACjH,QAAQ,IAAIuD,cAAc,CAACN,MAAM,IAAIP,UAAU,KAAK,QAAQ,EAAE;MACjEe,eAAe,CAAC5F,UAAU,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;;EAEH;AACF;AACA;;EAGE,IAAIqJ,eAAe,GAAGlK,eAAe,CAAC,UAAU2J,IAAI,EAAE;IACpDzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACjE,YAAY,CAAC0J,IAAI,CAAC,CAAC;IAC7ED,gBAAgB,CAACC,IAAI,CAAC;EACxB,CAAC,CAAC;EACF,IAAIQ,oBAAoB,GAAGnK,eAAe,CAAC,UAAU+H,GAAG,EAAEqC,IAAI,EAAE;IAC9D,IAAIC,WAAW,GAAG9D,cAAc,CAACyD,MAAM,CAAC,UAAUM,CAAC,EAAE;MACnD,OAAOA,CAAC,KAAKvC,GAAG;IAClB,CAAC,CAAC;IAEF,IAAIqC,IAAI,EAAE;MACRC,WAAW,CAACE,IAAI,CAACxC,GAAG,CAAC;IACvB,CAAC,MAAM,IAAIrC,UAAU,KAAK,QAAQ,EAAE;MAClC;MACA,IAAI8E,WAAW,GAAG/C,cAAc,CAACM,GAAG,CAAC;MACrCsC,WAAW,GAAGA,WAAW,CAACL,MAAM,CAAC,UAAUM,CAAC,EAAE;QAC5C,OAAO,CAACE,WAAW,CAACC,GAAG,CAACH,CAAC,CAAC;MAC5B,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC7K,YAAY,CAAC8G,cAAc,EAAE8D,WAAW,CAAC,EAAE;MAC9C5D,eAAe,CAAC4D,WAAW,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,IAAIK,yBAAyB,GAAG1K,eAAe,CAACiE,iBAAiB,CAAC,CAAC,CAAC;;EAEpE,IAAI0G,wBAAwB,GAAG,SAASA,wBAAwBA,CAAC5C,GAAG,EAAEqC,IAAI,EAAE;IAC1E,IAAIQ,QAAQ,GAAGR,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC7D,cAAc,CAACuD,QAAQ,CAAC/B,GAAG,CAAC;IACtFoC,oBAAoB,CAACpC,GAAG,EAAE6C,QAAQ,CAAC;EACrC,CAAC;EAED,IAAIC,iBAAiB,GAAG1K,gBAAgB,CAACuF,UAAU,EAAEwC,eAAe,EAAE9C,KAAK,EAAED,IAAI,EAAEF,YAAY,EAAEuC,OAAO,EAAED,UAAU,EAAEY,kBAAkB,EAAEwC,wBAAwB,EAAEvG,SAAS,CAAC,CAAC,CAAC;;EAEhL7E,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1BhC,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAI8F,cAAc,GAAGvL,KAAK,CAACoF,OAAO,CAAC,YAAY;IAC7C,OAAO;MACLJ,uBAAuB,EAAEA,uBAAuB;MAChDC,0BAA0B,EAAEA;IAC9B,CAAC;EACH,CAAC,EAAE,CAACD,uBAAuB,EAAEC,0BAA0B,CAAC,CAAC,CAAC,CAAC;EAC3D;;EAEA,IAAIuG,gBAAgB,GAAGrF,UAAU,KAAK,YAAY,IAAIvD,gBAAgB,GAAGuC,SAAS;EAAG;EACrFA,SAAS,CAACmD,GAAG,CAAC,UAAUC,KAAK,EAAEkD,KAAK,EAAE;IACpC,QACE;MACA;MACAzL,KAAK,CAAC0L,aAAa,CAAClL,mBAAmB,EAAE;QACvCgI,GAAG,EAAED,KAAK,CAACC,GAAG;QACdmD,gBAAgB,EAAEF,KAAK,GAAGlF;MAC5B,CAAC,EAAEgC,KAAK;IAAC;EAEb,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIqD,SAAS,GAAG,aAAa5L,KAAK,CAAC0L,aAAa,CAACrL,QAAQ,EAAEZ,QAAQ,CAAC;IAClE8C,EAAE,EAAEA,EAAE;IACNb,GAAG,EAAEgE,YAAY;IACjB5D,SAAS,EAAE,EAAE,CAAC6H,MAAM,CAAC7H,SAAS,EAAE,WAAW,CAAC;IAC5C+J,SAAS,EAAE,IAAI;IACfC,aAAa,EAAExL,QAAQ;IACvB2B,SAAS,EAAEhC,UAAU,CAAC6B,SAAS,EAAE,EAAE,CAAC6H,MAAM,CAAC7H,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC6H,MAAM,CAAC7H,SAAS,EAAE,GAAG,CAAC,CAAC6H,MAAM,CAACxD,UAAU,CAAC,EAAElE,SAAS,GAAGL,WAAW,GAAG,CAAC,CAAC,EAAElC,eAAe,CAACkC,WAAW,EAAE,EAAE,CAAC+H,MAAM,CAAC7H,SAAS,EAAE,mBAAmB,CAAC,EAAEsE,qBAAqB,CAAC,EAAE1G,eAAe,CAACkC,WAAW,EAAE,EAAE,CAAC+H,MAAM,CAAC7H,SAAS,EAAE,MAAM,CAAC,EAAE+D,KAAK,CAAC,EAAEjE,WAAW,GAAGG,aAAa,CAAC;IAC3UgK,GAAG,EAAEzJ,SAAS;IACdN,KAAK,EAAEA,KAAK;IACZgK,IAAI,EAAE,MAAM;IACZ7J,QAAQ,EAAEA,QAAQ;IAClB8J,IAAI,EAAET,gBAAgB;IACtBU,aAAa,EAAE,SAASA,aAAaA,CAAC5C,IAAI,EAAE;MAC1C,OAAOA,IAAI;IACb,CAAC;IACD6C,aAAa,EAAE,SAASA,aAAaA,CAACC,SAAS,EAAE;MAC/C;MACA,IAAIC,GAAG,GAAGD,SAAS,CAAC1F,MAAM;MAC1B,IAAI4F,eAAe,GAAGD,GAAG,GAAGlH,SAAS,CAACkD,KAAK,CAAC,CAACgE,GAAG,CAAC,GAAG,IAAI;MACxD,OAAO,aAAarM,KAAK,CAAC0L,aAAa,CAAC/K,OAAO,EAAE;QAC/C4L,QAAQ,EAAEtL,YAAY;QACtBuL,KAAK,EAAEhI,mBAAmB;QAC1B7B,QAAQ,EAAE8D,UAAU;QACpBgG,kBAAkB,EAAEJ,GAAG,KAAK,CAAC;QAC7BK,cAAc,EAAEjI;MAClB,CAAC,EAAE6H,eAAe,CAAC;IACrB,CAAC;IACDK,QAAQ,EAAExG,UAAU,KAAK,YAAY,IAAIvD,gBAAgB,GAAGvC,QAAQ,CAACuM,UAAU,GAAGvM,QAAQ,CAACwM,UAAU;IACrGC,GAAG,EAAE,MAAM;IACX,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,SAASA,eAAeA,CAACC,YAAY,EAAE;MACtDxG,mBAAmB,CAACwG,YAAY,CAAC;IACnC,CAAC;IACDnI,SAAS,EAAEyG;EACb,CAAC,EAAEpG,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhB,OAAO,aAAalF,KAAK,CAAC0L,aAAa,CAACtK,cAAc,CAAC6L,QAAQ,EAAE;IAC/DrG,KAAK,EAAE2E;EACT,CAAC,EAAE,aAAavL,KAAK,CAAC0L,aAAa,CAACvK,SAAS,CAAC8L,QAAQ,EAAE;IACtDrG,KAAK,EAAEhB;EACT,CAAC,EAAE,aAAa5F,KAAK,CAAC0L,aAAa,CAAClL,mBAAmB,EAAE;IACvDsB,SAAS,EAAEA,SAAS;IACpBC,aAAa,EAAEA,aAAa;IAC5BU,IAAI,EAAE0D,UAAU;IAChBhD,QAAQ,EAAE6D,cAAc;IACxBkG,GAAG,EAAErH,KAAK,CAAC;IAAA;;IAEXlD,QAAQ,EAAEA,QAAQ,CAAC;IAAA;;IAEnBqB,MAAM,EAAEwB,OAAO,GAAGxB,MAAM,GAAG,IAAI;IAC/BC,cAAc,EAAEuB,OAAO,GAAGvB,cAAc,GAAG,IAAI,CAAC;IAAA;;IAEhDb,SAAS,EAAEuF,eAAe;IAC1BE,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAEA,UAAU,CAAC;IAAA;;IAEvBnF,YAAY,EAAEsG,gBAAgB,CAAC;IAAA;;IAE/BlG,YAAY,EAAEA,YAAY,CAAC;IAAA;;IAE3BjB,gBAAgB,EAAEA,gBAAgB;IAClCE,iBAAiB,EAAEA,iBAAiB;IACpCC,kBAAkB,EAAEA,kBAAkB;IACtCmB,iBAAiB,EAAEA,iBAAiB;IACpCD,oBAAoB,EAAEA,oBAAoB;IAC1CO,iBAAiB,EAAEyG,yBAAyB,CAAC;IAAA;;IAE7C9G,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAEA,UAAU,CAAC;IAAA;;IAEvB6I,WAAW,EAAExC,eAAe;IAC5B/F,YAAY,EAAEgG;EAChB,CAAC,EAAE,aAAa5K,KAAK,CAAC0L,aAAa,CAAC3K,eAAe,CAACkM,QAAQ,EAAE;IAC5DrG,KAAK,EAAEwB;EACT,CAAC,EAAEwD,SAAS,CAAC,EAAE,aAAa5L,KAAK,CAAC0L,aAAa,CAAC,KAAK,EAAE;IACrD1J,KAAK,EAAE;MACLoL,OAAO,EAAE;IACX,CAAC;IACD,aAAa,EAAE;EACjB,CAAC,EAAE,aAAapN,KAAK,CAAC0L,aAAa,CAAC5K,mBAAmB,CAACmM,QAAQ,EAAE;IAChErG,KAAK,EAAEuB;EACT,CAAC,EAAEhD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC;AACF,eAAe5D,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}