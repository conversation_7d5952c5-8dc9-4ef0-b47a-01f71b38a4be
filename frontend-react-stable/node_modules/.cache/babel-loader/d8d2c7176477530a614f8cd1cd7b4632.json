{"ast": null, "code": "import * as React from 'react';\nfunction AddButton(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    editable = _ref.editable,\n    locale = _ref.locale,\n    style = _ref.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n}\nexport default /*#__PURE__*/React.forwardRef(AddButton);", "map": {"version": 3, "names": ["React", "AddButton", "_ref", "ref", "prefixCls", "editable", "locale", "style", "showAdd", "createElement", "type", "className", "concat", "addAriaLabel", "onClick", "event", "onEdit", "addIcon", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabNavList/AddButton.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction AddButton(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n      editable = _ref.editable,\n      locale = _ref.locale,\n      style = _ref.style;\n\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n}\n\nexport default /*#__PURE__*/React.forwardRef(AddButton);"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC5B,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,KAAK,GAAGL,IAAI,CAACK,KAAK;EAEtB,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACG,OAAO,KAAK,KAAK,EAAE;IAC3C,OAAO,IAAI;EACb;EAEA,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,QAAQ,EAAE;IAChDN,GAAG,EAAEA,GAAG;IACRO,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC;IAC3CG,KAAK,EAAEA,KAAK;IACZ,YAAY,EAAE,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,YAAY,KAAK,SAAS;IAChGC,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/BV,QAAQ,CAACW,MAAM,CAAC,KAAK,EAAE;QACrBD,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAEV,QAAQ,CAACY,OAAO,IAAI,GAAG,CAAC;AAC7B;AAEA,eAAe,aAAajB,KAAK,CAACkB,UAAU,CAACjB,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}