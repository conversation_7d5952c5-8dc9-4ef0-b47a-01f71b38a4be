{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = transpose;\nvar _min = _interopRequireDefault(require(\"./min.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = (0, _min.default)(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\nfunction length(d) {\n  return d.length;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "transpose", "_min", "_interopRequireDefault", "require", "obj", "__esModule", "matrix", "n", "length", "i", "m", "Array", "j", "row", "d"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/transpose.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = transpose;\n\nvar _min = _interopRequireDefault(require(\"./min.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n\n  for (var i = -1, m = (0, _min.default)(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,IAAIC,IAAI,GAAGC,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEtD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,SAASA,CAACM,MAAM,EAAE;EACzB,IAAI,EAAEC,CAAC,GAAGD,MAAM,CAACE,MAAM,CAAC,EAAE,OAAO,EAAE;EAEnC,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAET,IAAI,CAACF,OAAO,EAAEO,MAAM,EAAEE,MAAM,CAAC,EAAER,SAAS,GAAG,IAAIW,KAAK,CAACD,CAAC,CAAC,EAAE,EAAED,CAAC,GAAGC,CAAC,GAAG;IAC1F,KAAK,IAAIE,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,EAAEM,GAAG,GAAGb,SAAS,CAACS,CAAC,CAAC,GAAG,IAAIE,KAAK,CAACJ,CAAC,CAAC,EAAE,EAAEK,CAAC,GAAGL,CAAC,GAAG;MAC/DM,GAAG,CAACD,CAAC,CAAC,GAAGN,MAAM,CAACM,CAAC,CAAC,CAACH,CAAC,CAAC;IACvB;EACF;EAEA,OAAOT,SAAS;AAClB;AAEA,SAASQ,MAAMA,CAACM,CAAC,EAAE;EACjB,OAAOA,CAAC,CAACN,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}