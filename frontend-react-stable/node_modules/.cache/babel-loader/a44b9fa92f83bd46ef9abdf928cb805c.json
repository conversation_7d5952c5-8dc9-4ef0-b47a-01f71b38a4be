{"ast": null, "code": "import DialogWrap from './DialogWrap';\nimport Panel from './Dialog/Content/Panel';\nexport { Panel };\nexport default DialogWrap;", "map": {"version": 3, "names": ["DialogWrap", "Panel"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dialog/es/index.js"], "sourcesContent": ["import DialogWrap from './DialogWrap';\nimport Panel from './Dialog/Content/Panel';\nexport { Panel };\nexport default DialogWrap;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASA,KAAK;AACd,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}