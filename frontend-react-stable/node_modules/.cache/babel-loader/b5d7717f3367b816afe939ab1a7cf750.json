{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = bin;\nvar _array = require(\"./array.js\");\nvar _bisect = _interopRequireDefault(require(\"./bisect.js\"));\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nvar _extent = _interopRequireDefault(require(\"./extent.js\"));\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\nvar _ticks = _interopRequireWildcard(require(\"./ticks.js\"));\nvar _sturges = _interopRequireDefault(require(\"./threshold/sturges.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction bin() {\n  var value = _identity.default,\n    domain = _extent.default,\n    threshold = _sturges.default;\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n    var i,\n      n = data.length,\n      x,\n      step,\n      values = new Array(n);\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n    var xz = domain(values),\n      x0 = xz[0],\n      x1 = xz[1],\n      tz = threshold(values, x0, x1); // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n\n    if (!Array.isArray(tz)) {\n      const max = x1,\n        tn = +tz;\n      if (domain === _extent.default) [x0, x1] = (0, _nice.default)(x0, x1, tn);\n      tz = (0, _ticks.default)(x0, x1, tn); // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n\n      if (tz[0] <= x0) step = (0, _ticks.tickIncrement)(x0, x1, tn); // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === _extent.default) {\n          const step = (0, _ticks.tickIncrement)(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    } // Remove any thresholds outside the domain.\n\n    var m = tz.length;\n    while (tz[0] <= x0) tz.shift(), --m;\n    while (tz[m - 1] > x1) tz.pop(), --m;\n    var bins = new Array(m + 1),\n      bin; // Initialize bins.\n\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    } // Assign data to bins by value, ignoring any outside the domain.\n\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[(0, _bisect.default)(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n    return bins;\n  }\n  histogram.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(_), histogram) : value;\n  };\n  histogram.domain = function (_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0, _constant.default)([_[0], _[1]]), histogram) : domain;\n  };\n  histogram.thresholds = function (_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0, _constant.default)(_array.slice.call(_)) : (0, _constant.default)(_), histogram) : threshold;\n  };\n  return histogram;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "bin", "_array", "require", "_bisect", "_interopRequireDefault", "_constant", "_extent", "_identity", "_nice", "_ticks", "_interopRequireWildcard", "_sturges", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "domain", "threshold", "histogram", "data", "Array", "isArray", "from", "i", "n", "length", "x", "step", "values", "xz", "x0", "x1", "tz", "max", "tn", "tickIncrement", "isFinite", "Math", "floor", "ceil", "pop", "m", "shift", "bins", "min", "push", "j", "_", "arguments", "thresholds", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/bin.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = bin;\n\nvar _array = require(\"./array.js\");\n\nvar _bisect = _interopRequireDefault(require(\"./bisect.js\"));\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nvar _extent = _interopRequireDefault(require(\"./extent.js\"));\n\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\n\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\n\nvar _ticks = _interopRequireWildcard(require(\"./ticks.js\"));\n\nvar _sturges = _interopRequireDefault(require(\"./threshold/sturges.js\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction bin() {\n  var value = _identity.default,\n      domain = _extent.default,\n      threshold = _sturges.default;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1); // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n\n    if (!Array.isArray(tz)) {\n      const max = x1,\n            tn = +tz;\n      if (domain === _extent.default) [x0, x1] = (0, _nice.default)(x0, x1, tn);\n      tz = (0, _ticks.default)(x0, x1, tn); // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n\n      if (tz[0] <= x0) step = (0, _ticks.tickIncrement)(x0, x1, tn); // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === _extent.default) {\n          const step = (0, _ticks.tickIncrement)(x0, x1, tn);\n\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    } // Remove any thresholds outside the domain.\n\n\n    var m = tz.length;\n\n    while (tz[0] <= x0) tz.shift(), --m;\n\n    while (tz[m - 1] > x1) tz.pop(), --m;\n\n    var bins = new Array(m + 1),\n        bin; // Initialize bins.\n\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    } // Assign data to bins by value, ignoring any outside the domain.\n\n\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[(0, _bisect.default)(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(_), histogram) : value;\n  };\n\n  histogram.domain = function (_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0, _constant.default)([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function (_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0, _constant.default)(_array.slice.call(_)) : (0, _constant.default)(_), histogram) : threshold;\n  };\n\n  return histogram;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,IAAIC,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AAElC,IAAIC,OAAO,GAAGC,sBAAsB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIG,SAAS,GAAGD,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAII,OAAO,GAAGF,sBAAsB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIK,SAAS,GAAGH,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIM,KAAK,GAAGJ,sBAAsB,CAACF,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,IAAIO,MAAM,GAAGC,uBAAuB,CAACR,OAAO,CAAC,YAAY,CAAC,CAAC;AAE3D,IAAIS,QAAQ,GAAGP,sBAAsB,CAACF,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAExE,SAASU,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASH,uBAAuBA,CAACO,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAElB,OAAO,EAAEkB;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAG5B,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC6B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAI9B,MAAM,CAAC+B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAG5B,MAAM,CAAC6B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEnC,MAAM,CAACC,cAAc,CAAC0B,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACvB,OAAO,GAAGkB,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASlB,sBAAsBA,CAACa,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAElB,OAAO,EAAEkB;EAAI,CAAC;AAAE;AAE9F,SAASjB,GAAGA,CAAA,EAAG;EACb,IAAIF,KAAK,GAAGS,SAAS,CAACR,OAAO;IACzBgC,MAAM,GAAGzB,OAAO,CAACP,OAAO;IACxBiC,SAAS,GAAGrB,QAAQ,CAACZ,OAAO;EAEhC,SAASkC,SAASA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAEA,IAAI,GAAGC,KAAK,CAACE,IAAI,CAACH,IAAI,CAAC;IACjD,IAAII,CAAC;MACDC,CAAC,GAAGL,IAAI,CAACM,MAAM;MACfC,CAAC;MACDC,IAAI;MACJC,MAAM,GAAG,IAAIR,KAAK,CAACI,CAAC,CAAC;IAEzB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBK,MAAM,CAACL,CAAC,CAAC,GAAGxC,KAAK,CAACoC,IAAI,CAACI,CAAC,CAAC,EAAEA,CAAC,EAAEJ,IAAI,CAAC;IACrC;IAEA,IAAIU,EAAE,GAAGb,MAAM,CAACY,MAAM,CAAC;MACnBE,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;MACVE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;MACVG,EAAE,GAAGf,SAAS,CAACW,MAAM,EAAEE,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;IACpC;;IAEA,IAAI,CAACX,KAAK,CAACC,OAAO,CAACW,EAAE,CAAC,EAAE;MACtB,MAAMC,GAAG,GAAGF,EAAE;QACRG,EAAE,GAAG,CAACF,EAAE;MACd,IAAIhB,MAAM,KAAKzB,OAAO,CAACP,OAAO,EAAE,CAAC8C,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEtC,KAAK,CAACT,OAAO,EAAE8C,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;MACzEF,EAAE,GAAG,CAAC,CAAC,EAAEtC,MAAM,CAACV,OAAO,EAAE8C,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MACtC;MACA;;MAEA,IAAIF,EAAE,CAAC,CAAC,CAAC,IAAIF,EAAE,EAAEH,IAAI,GAAG,CAAC,CAAC,EAAEjC,MAAM,CAACyC,aAAa,EAAEL,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MAC/D;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIF,EAAE,CAACA,EAAE,CAACP,MAAM,GAAG,CAAC,CAAC,IAAIM,EAAE,EAAE;QAC3B,IAAIE,GAAG,IAAIF,EAAE,IAAIf,MAAM,KAAKzB,OAAO,CAACP,OAAO,EAAE;UAC3C,MAAM2C,IAAI,GAAG,CAAC,CAAC,EAAEjC,MAAM,CAACyC,aAAa,EAAEL,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;UAElD,IAAIE,QAAQ,CAACT,IAAI,CAAC,EAAE;YAClB,IAAIA,IAAI,GAAG,CAAC,EAAE;cACZI,EAAE,GAAG,CAACM,IAAI,CAACC,KAAK,CAACP,EAAE,GAAGJ,IAAI,CAAC,GAAG,CAAC,IAAIA,IAAI;YACzC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;cACnBI,EAAE,GAAG,CAACM,IAAI,CAACE,IAAI,CAACR,EAAE,GAAG,CAACJ,IAAI,CAAC,GAAG,CAAC,IAAI,CAACA,IAAI;YAC1C;UACF;QACF,CAAC,MAAM;UACLK,EAAE,CAACQ,GAAG,CAAC,CAAC;QACV;MACF;IACF,CAAC,CAAC;;IAGF,IAAIC,CAAC,GAAGT,EAAE,CAACP,MAAM;IAEjB,OAAOO,EAAE,CAAC,CAAC,CAAC,IAAIF,EAAE,EAAEE,EAAE,CAACU,KAAK,CAAC,CAAC,EAAE,EAAED,CAAC;IAEnC,OAAOT,EAAE,CAACS,CAAC,GAAG,CAAC,CAAC,GAAGV,EAAE,EAAEC,EAAE,CAACQ,GAAG,CAAC,CAAC,EAAE,EAAEC,CAAC;IAEpC,IAAIE,IAAI,GAAG,IAAIvB,KAAK,CAACqB,CAAC,GAAG,CAAC,CAAC;MACvBxD,GAAG,CAAC,CAAC;;IAET,KAAKsC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIkB,CAAC,EAAE,EAAElB,CAAC,EAAE;MACvBtC,GAAG,GAAG0D,IAAI,CAACpB,CAAC,CAAC,GAAG,EAAE;MAClBtC,GAAG,CAAC6C,EAAE,GAAGP,CAAC,GAAG,CAAC,GAAGS,EAAE,CAACT,CAAC,GAAG,CAAC,CAAC,GAAGO,EAAE;MAC/B7C,GAAG,CAAC8C,EAAE,GAAGR,CAAC,GAAGkB,CAAC,GAAGT,EAAE,CAACT,CAAC,CAAC,GAAGQ,EAAE;IAC7B,CAAC,CAAC;;IAGF,IAAIK,QAAQ,CAACT,IAAI,CAAC,EAAE;MAClB,IAAIA,IAAI,GAAG,CAAC,EAAE;QACZ,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;YACjDY,IAAI,CAACN,IAAI,CAACO,GAAG,CAACH,CAAC,EAAEJ,IAAI,CAACC,KAAK,CAAC,CAACZ,CAAC,GAAGI,EAAE,IAAIH,IAAI,CAAC,CAAC,CAAC,CAACkB,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC;UAC9D;QACF;MACF,CAAC,MAAM,IAAII,IAAI,GAAG,CAAC,EAAE;QACnB,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;YACjD,MAAMe,CAAC,GAAGT,IAAI,CAACC,KAAK,CAAC,CAACR,EAAE,GAAGJ,CAAC,IAAIC,IAAI,CAAC;YACrCgB,IAAI,CAACN,IAAI,CAACO,GAAG,CAACH,CAAC,EAAEK,CAAC,IAAId,EAAE,CAACc,CAAC,CAAC,IAAIpB,CAAC,CAAC,CAAC,CAAC,CAACmB,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD;QACF;MACF;IACF,CAAC,MAAM;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;UACjDY,IAAI,CAAC,CAAC,CAAC,EAAEvD,OAAO,CAACJ,OAAO,EAAEgD,EAAE,EAAEN,CAAC,EAAE,CAAC,EAAEe,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC;QACvD;MACF;IACF;IAEA,OAAOoB,IAAI;EACb;EAEAzB,SAAS,CAACnC,KAAK,GAAG,UAAUgE,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACvB,MAAM,IAAI1C,KAAK,GAAG,OAAOgE,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEzD,SAAS,CAACN,OAAO,EAAE+D,CAAC,CAAC,EAAE7B,SAAS,IAAInC,KAAK;EAChH,CAAC;EAEDmC,SAAS,CAACF,MAAM,GAAG,UAAU+B,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACvB,MAAM,IAAIT,MAAM,GAAG,OAAO+B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEzD,SAAS,CAACN,OAAO,EAAE,CAAC+D,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE7B,SAAS,IAAIF,MAAM;EAC7H,CAAC;EAEDE,SAAS,CAAC+B,UAAU,GAAG,UAAUF,CAAC,EAAE;IAClC,OAAOC,SAAS,CAACvB,MAAM,IAAIR,SAAS,GAAG,OAAO8B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG3B,KAAK,CAACC,OAAO,CAAC0B,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEzD,SAAS,CAACN,OAAO,EAAEE,MAAM,CAACgE,KAAK,CAACrC,IAAI,CAACkC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEzD,SAAS,CAACN,OAAO,EAAE+D,CAAC,CAAC,EAAE7B,SAAS,IAAID,SAAS;EAC1L,CAAC;EAED,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}