{"ast": null, "code": "import * as React from 'react';\nvar DisabledContext = /*#__PURE__*/React.createContext(false);\nexport var DisabledContextProvider = function DisabledContextProvider(_ref) {\n  var children = _ref.children,\n    disabled = _ref.disabled;\n  var originDisabled = React.useContext(DisabledContext);\n  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {\n    value: disabled || originDisabled\n  }, children);\n};\nexport default DisabledContext;", "map": {"version": 3, "names": ["React", "DisabledContext", "createContext", "DisabledContextProvider", "_ref", "children", "disabled", "originDisabled", "useContext", "createElement", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/config-provider/DisabledContext.js"], "sourcesContent": ["import * as React from 'react';\nvar DisabledContext = /*#__PURE__*/React.createContext(false);\nexport var DisabledContextProvider = function DisabledContextProvider(_ref) {\n  var children = _ref.children,\n    disabled = _ref.disabled;\n  var originDisabled = React.useContext(DisabledContext);\n  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {\n    value: disabled || originDisabled\n  }, children);\n};\nexport default DisabledContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAC7D,OAAO,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EAC1E,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,IAAIC,cAAc,GAAGP,KAAK,CAACQ,UAAU,CAACP,eAAe,CAAC;EACtD,OAAO,aAAaD,KAAK,CAACS,aAAa,CAACR,eAAe,CAACS,QAAQ,EAAE;IAChEC,KAAK,EAAEL,QAAQ,IAAIC;EACrB,CAAC,EAAEF,QAAQ,CAAC;AACd,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}