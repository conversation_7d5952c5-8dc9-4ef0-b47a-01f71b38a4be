{"ast": null, "code": "/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar Option = function Option() {\n  return null;\n};\nOption.isSelectOption = true;\nexport default Option;", "map": {"version": 3, "names": ["Option", "isSelectOption"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/Option.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar Option = function Option() {\n  return null;\n};\nOption.isSelectOption = true;\nexport default Option;"], "mappings": "AAAA;;AAEA;AACA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAO,IAAI;AACb,CAAC;AACDA,MAAM,CAACC,cAAc,GAAG,IAAI;AAC5B,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}