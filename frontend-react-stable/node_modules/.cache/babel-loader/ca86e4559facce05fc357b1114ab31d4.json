{"ast": null, "code": "export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nexport default function getScroll(target, top) {\n  var _a, _b;\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n  var method = top ? 'scrollTop' : 'scrollLeft';\n  var result = 0;\n  if (isWindow(target)) {\n    result = target[top ? 'pageYOffset' : 'pageXOffset'];\n  } else if (target instanceof Document) {\n    result = target.documentElement[method];\n  } else if (target instanceof HTMLElement) {\n    result = target[method];\n  } else if (target) {\n    // According to the type inference, the `target` is `never` type.\n    // Since we configured the loose mode type checking, and supports mocking the target with such shape below::\n    //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,\n    //    the program may falls into this branch.\n    // Check the corresponding tests for details. Don't sure what is the real scenario this happens.\n    result = target[method];\n  }\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_b = ((_a = target.ownerDocument) !== null && _a !== void 0 ? _a : target).documentElement) === null || _b === void 0 ? void 0 : _b[method];\n  }\n  return result;\n}", "map": {"version": 3, "names": ["isWindow", "obj", "undefined", "window", "getScroll", "target", "top", "_a", "_b", "method", "result", "Document", "documentElement", "HTMLElement", "ownerDocument"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/getScroll.js"], "sourcesContent": ["export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nexport default function getScroll(target, top) {\n  var _a, _b;\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n  var method = top ? 'scrollTop' : 'scrollLeft';\n  var result = 0;\n  if (isWindow(target)) {\n    result = target[top ? 'pageYOffset' : 'pageXOffset'];\n  } else if (target instanceof Document) {\n    result = target.documentElement[method];\n  } else if (target instanceof HTMLElement) {\n    result = target[method];\n  } else if (target) {\n    // According to the type inference, the `target` is `never` type.\n    // Since we configured the loose mode type checking, and supports mocking the target with such shape below::\n    //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,\n    //    the program may falls into this branch.\n    // Check the corresponding tests for details. Don't sure what is the real scenario this happens.\n    result = target[method];\n  }\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_b = ((_a = target.ownerDocument) !== null && _a !== void 0 ? _a : target).documentElement) === null || _b === void 0 ? void 0 : _b[method];\n  }\n  return result;\n}"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAKA,GAAG,CAACE,MAAM;AAChE;AACA,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC7C,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAI,OAAOL,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,CAAC;EACV;EACA,IAAIM,MAAM,GAAGH,GAAG,GAAG,WAAW,GAAG,YAAY;EAC7C,IAAII,MAAM,GAAG,CAAC;EACd,IAAIV,QAAQ,CAACK,MAAM,CAAC,EAAE;IACpBK,MAAM,GAAGL,MAAM,CAACC,GAAG,GAAG,aAAa,GAAG,aAAa,CAAC;EACtD,CAAC,MAAM,IAAID,MAAM,YAAYM,QAAQ,EAAE;IACrCD,MAAM,GAAGL,MAAM,CAACO,eAAe,CAACH,MAAM,CAAC;EACzC,CAAC,MAAM,IAAIJ,MAAM,YAAYQ,WAAW,EAAE;IACxCH,MAAM,GAAGL,MAAM,CAACI,MAAM,CAAC;EACzB,CAAC,MAAM,IAAIJ,MAAM,EAAE;IACjB;IACA;IACA;IACA;IACA;IACAK,MAAM,GAAGL,MAAM,CAACI,MAAM,CAAC;EACzB;EACA,IAAIJ,MAAM,IAAI,CAACL,QAAQ,CAACK,MAAM,CAAC,IAAI,OAAOK,MAAM,KAAK,QAAQ,EAAE;IAC7DA,MAAM,GAAG,CAACF,EAAE,GAAG,CAAC,CAACD,EAAE,GAAGF,MAAM,CAACS,aAAa,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,MAAM,EAAEO,eAAe,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,MAAM,CAAC;EACvJ;EACA,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}