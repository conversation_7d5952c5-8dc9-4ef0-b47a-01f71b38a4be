{"ast": null, "code": "/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n    length = array == null ? 0 : array.length,\n    resIndex = 0,\n    result = [];\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\nmodule.exports = arrayFilter;", "map": {"version": 3, "names": ["arrayFilter", "array", "predicate", "index", "length", "resIndex", "result", "value", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_arrayFilter.js"], "sourcesContent": ["/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = arrayFilter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACrC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;IACzCC,QAAQ,GAAG,CAAC;IACZC,MAAM,GAAG,EAAE;EAEf,OAAO,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGN,KAAK,CAACE,KAAK,CAAC;IACxB,IAAID,SAAS,CAACK,KAAK,EAAEJ,KAAK,EAAEF,KAAK,CAAC,EAAE;MAClCK,MAAM,CAACD,QAAQ,EAAE,CAAC,GAAGE,KAAK;IAC5B;EACF;EACA,OAAOD,MAAM;AACf;AAEAE,MAAM,CAACC,OAAO,GAAGT,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}