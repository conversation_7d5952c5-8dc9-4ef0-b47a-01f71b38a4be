{"ast": null, "code": "(function (global, undefined) {\n  \"use strict\";\n\n  if (global.setImmediate) {\n    return;\n  }\n  var nextHandle = 1; // Spec says greater than zero\n  var tasksByHandle = {};\n  var currentlyRunningATask = false;\n  var doc = global.document;\n  var registerImmediate;\n  function setImmediate(callback) {\n    // Callback can either be a function or a string\n    if (typeof callback !== \"function\") {\n      callback = new Function(\"\" + callback);\n    }\n    // Copy function arguments\n    var args = new Array(arguments.length - 1);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i + 1];\n    }\n    // Store and register the task\n    var task = {\n      callback: callback,\n      args: args\n    };\n    tasksByHandle[nextHandle] = task;\n    registerImmediate(nextHandle);\n    return nextHandle++;\n  }\n  function clearImmediate(handle) {\n    delete tasksByHandle[handle];\n  }\n  function run(task) {\n    var callback = task.callback;\n    var args = task.args;\n    switch (args.length) {\n      case 0:\n        callback();\n        break;\n      case 1:\n        callback(args[0]);\n        break;\n      case 2:\n        callback(args[0], args[1]);\n        break;\n      case 3:\n        callback(args[0], args[1], args[2]);\n        break;\n      default:\n        callback.apply(undefined, args);\n        break;\n    }\n  }\n  function runIfPresent(handle) {\n    // From the spec: \"Wait until any invocations of this algorithm started before this one have completed.\"\n    // So if we're currently running a task, we'll need to delay this invocation.\n    if (currentlyRunningATask) {\n      // Delay by doing a setTimeout. setImmediate was tried instead, but in Firefox 7 it generated a\n      // \"too much recursion\" error.\n      setTimeout(runIfPresent, 0, handle);\n    } else {\n      var task = tasksByHandle[handle];\n      if (task) {\n        currentlyRunningATask = true;\n        try {\n          run(task);\n        } finally {\n          clearImmediate(handle);\n          currentlyRunningATask = false;\n        }\n      }\n    }\n  }\n  function installNextTickImplementation() {\n    registerImmediate = function (handle) {\n      process.nextTick(function () {\n        runIfPresent(handle);\n      });\n    };\n  }\n  function canUsePostMessage() {\n    // The test against `importScripts` prevents this implementation from being installed inside a web worker,\n    // where `global.postMessage` means something completely different and can't be used for this purpose.\n    if (global.postMessage && !global.importScripts) {\n      var postMessageIsAsynchronous = true;\n      var oldOnMessage = global.onmessage;\n      global.onmessage = function () {\n        postMessageIsAsynchronous = false;\n      };\n      global.postMessage(\"\", \"*\");\n      global.onmessage = oldOnMessage;\n      return postMessageIsAsynchronous;\n    }\n  }\n  function installPostMessageImplementation() {\n    // Installs an event handler on `global` for the `message` event: see\n    // * https://developer.mozilla.org/en/DOM/window.postMessage\n    // * http://www.whatwg.org/specs/web-apps/current-work/multipage/comms.html#crossDocumentMessages\n\n    var messagePrefix = \"setImmediate$\" + Math.random() + \"$\";\n    var onGlobalMessage = function (event) {\n      if (event.source === global && typeof event.data === \"string\" && event.data.indexOf(messagePrefix) === 0) {\n        runIfPresent(+event.data.slice(messagePrefix.length));\n      }\n    };\n    if (global.addEventListener) {\n      global.addEventListener(\"message\", onGlobalMessage, false);\n    } else {\n      global.attachEvent(\"onmessage\", onGlobalMessage);\n    }\n    registerImmediate = function (handle) {\n      global.postMessage(messagePrefix + handle, \"*\");\n    };\n  }\n  function installMessageChannelImplementation() {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function (event) {\n      var handle = event.data;\n      runIfPresent(handle);\n    };\n    registerImmediate = function (handle) {\n      channel.port2.postMessage(handle);\n    };\n  }\n  function installReadyStateChangeImplementation() {\n    var html = doc.documentElement;\n    registerImmediate = function (handle) {\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var script = doc.createElement(\"script\");\n      script.onreadystatechange = function () {\n        runIfPresent(handle);\n        script.onreadystatechange = null;\n        html.removeChild(script);\n        script = null;\n      };\n      html.appendChild(script);\n    };\n  }\n  function installSetTimeoutImplementation() {\n    registerImmediate = function (handle) {\n      setTimeout(runIfPresent, 0, handle);\n    };\n  }\n\n  // If supported, we should attach to the prototype of global, since that is where setTimeout et al. live.\n  var attachTo = Object.getPrototypeOf && Object.getPrototypeOf(global);\n  attachTo = attachTo && attachTo.setTimeout ? attachTo : global;\n\n  // Don't get fooled by e.g. browserify environments.\n  if ({}.toString.call(global.process) === \"[object process]\") {\n    // For Node.js before 0.9\n    installNextTickImplementation();\n  } else if (canUsePostMessage()) {\n    // For non-IE10 modern browsers\n    installPostMessageImplementation();\n  } else if (global.MessageChannel) {\n    // For web workers, where supported\n    installMessageChannelImplementation();\n  } else if (doc && \"onreadystatechange\" in doc.createElement(\"script\")) {\n    // For IE 6–8\n    installReadyStateChangeImplementation();\n  } else {\n    // For older browsers\n    installSetTimeoutImplementation();\n  }\n  attachTo.setImmediate = setImmediate;\n  attachTo.clearImmediate = clearImmediate;\n})(typeof self === \"undefined\" ? typeof global === \"undefined\" ? this : global : self);", "map": {"version": 3, "names": ["global", "undefined", "setImmediate", "nextH<PERSON>le", "tasksByHandle", "currentlyRunningATask", "doc", "document", "registerImmediate", "callback", "Function", "args", "Array", "arguments", "length", "i", "task", "clearImmediate", "handle", "run", "apply", "runIfPresent", "setTimeout", "installNextTickImplementation", "process", "nextTick", "canUsePostMessage", "postMessage", "importScripts", "postMessageIsAsynchronous", "oldOnMessage", "onmessage", "installPostMessageImplementation", "messagePrefix", "Math", "random", "onGlobalMessage", "event", "source", "data", "indexOf", "slice", "addEventListener", "attachEvent", "installMessageChannelImplementation", "channel", "MessageChannel", "port1", "port2", "installReadyStateChangeImplementation", "html", "documentElement", "script", "createElement", "onreadystatechange", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "installSetTimeoutImplementation", "attachTo", "Object", "getPrototypeOf", "toString", "call", "self"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/setimmediate/setImmediate.js"], "sourcesContent": ["(function (global, undefined) {\n    \"use strict\";\n\n    if (global.setImmediate) {\n        return;\n    }\n\n    var nextHandle = 1; // Spec says greater than zero\n    var tasksByHandle = {};\n    var currentlyRunningATask = false;\n    var doc = global.document;\n    var registerImmediate;\n\n    function setImmediate(callback) {\n      // Callback can either be a function or a string\n      if (typeof callback !== \"function\") {\n        callback = new Function(\"\" + callback);\n      }\n      // Copy function arguments\n      var args = new Array(arguments.length - 1);\n      for (var i = 0; i < args.length; i++) {\n          args[i] = arguments[i + 1];\n      }\n      // Store and register the task\n      var task = { callback: callback, args: args };\n      tasksByHandle[nextHandle] = task;\n      registerImmediate(nextHandle);\n      return nextHandle++;\n    }\n\n    function clearImmediate(handle) {\n        delete tasksByHandle[handle];\n    }\n\n    function run(task) {\n        var callback = task.callback;\n        var args = task.args;\n        switch (args.length) {\n        case 0:\n            callback();\n            break;\n        case 1:\n            callback(args[0]);\n            break;\n        case 2:\n            callback(args[0], args[1]);\n            break;\n        case 3:\n            callback(args[0], args[1], args[2]);\n            break;\n        default:\n            callback.apply(undefined, args);\n            break;\n        }\n    }\n\n    function runIfPresent(handle) {\n        // From the spec: \"Wait until any invocations of this algorithm started before this one have completed.\"\n        // So if we're currently running a task, we'll need to delay this invocation.\n        if (currentlyRunningATask) {\n            // Delay by doing a setTimeout. setImmediate was tried instead, but in Firefox 7 it generated a\n            // \"too much recursion\" error.\n            setTimeout(runIfPresent, 0, handle);\n        } else {\n            var task = tasksByHandle[handle];\n            if (task) {\n                currentlyRunningATask = true;\n                try {\n                    run(task);\n                } finally {\n                    clearImmediate(handle);\n                    currentlyRunningATask = false;\n                }\n            }\n        }\n    }\n\n    function installNextTickImplementation() {\n        registerImmediate = function(handle) {\n            process.nextTick(function () { runIfPresent(handle); });\n        };\n    }\n\n    function canUsePostMessage() {\n        // The test against `importScripts` prevents this implementation from being installed inside a web worker,\n        // where `global.postMessage` means something completely different and can't be used for this purpose.\n        if (global.postMessage && !global.importScripts) {\n            var postMessageIsAsynchronous = true;\n            var oldOnMessage = global.onmessage;\n            global.onmessage = function() {\n                postMessageIsAsynchronous = false;\n            };\n            global.postMessage(\"\", \"*\");\n            global.onmessage = oldOnMessage;\n            return postMessageIsAsynchronous;\n        }\n    }\n\n    function installPostMessageImplementation() {\n        // Installs an event handler on `global` for the `message` event: see\n        // * https://developer.mozilla.org/en/DOM/window.postMessage\n        // * http://www.whatwg.org/specs/web-apps/current-work/multipage/comms.html#crossDocumentMessages\n\n        var messagePrefix = \"setImmediate$\" + Math.random() + \"$\";\n        var onGlobalMessage = function(event) {\n            if (event.source === global &&\n                typeof event.data === \"string\" &&\n                event.data.indexOf(messagePrefix) === 0) {\n                runIfPresent(+event.data.slice(messagePrefix.length));\n            }\n        };\n\n        if (global.addEventListener) {\n            global.addEventListener(\"message\", onGlobalMessage, false);\n        } else {\n            global.attachEvent(\"onmessage\", onGlobalMessage);\n        }\n\n        registerImmediate = function(handle) {\n            global.postMessage(messagePrefix + handle, \"*\");\n        };\n    }\n\n    function installMessageChannelImplementation() {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function(event) {\n            var handle = event.data;\n            runIfPresent(handle);\n        };\n\n        registerImmediate = function(handle) {\n            channel.port2.postMessage(handle);\n        };\n    }\n\n    function installReadyStateChangeImplementation() {\n        var html = doc.documentElement;\n        registerImmediate = function(handle) {\n            // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n            // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n            var script = doc.createElement(\"script\");\n            script.onreadystatechange = function () {\n                runIfPresent(handle);\n                script.onreadystatechange = null;\n                html.removeChild(script);\n                script = null;\n            };\n            html.appendChild(script);\n        };\n    }\n\n    function installSetTimeoutImplementation() {\n        registerImmediate = function(handle) {\n            setTimeout(runIfPresent, 0, handle);\n        };\n    }\n\n    // If supported, we should attach to the prototype of global, since that is where setTimeout et al. live.\n    var attachTo = Object.getPrototypeOf && Object.getPrototypeOf(global);\n    attachTo = attachTo && attachTo.setTimeout ? attachTo : global;\n\n    // Don't get fooled by e.g. browserify environments.\n    if ({}.toString.call(global.process) === \"[object process]\") {\n        // For Node.js before 0.9\n        installNextTickImplementation();\n\n    } else if (canUsePostMessage()) {\n        // For non-IE10 modern browsers\n        installPostMessageImplementation();\n\n    } else if (global.MessageChannel) {\n        // For web workers, where supported\n        installMessageChannelImplementation();\n\n    } else if (doc && \"onreadystatechange\" in doc.createElement(\"script\")) {\n        // For IE 6–8\n        installReadyStateChangeImplementation();\n\n    } else {\n        // For older browsers\n        installSetTimeoutImplementation();\n    }\n\n    attachTo.setImmediate = setImmediate;\n    attachTo.clearImmediate = clearImmediate;\n}(typeof self === \"undefined\" ? typeof global === \"undefined\" ? this : global : self));\n"], "mappings": "AAAC,WAAUA,MAAM,EAAEC,SAAS,EAAE;EAC1B,YAAY;;EAEZ,IAAID,MAAM,CAACE,YAAY,EAAE;IACrB;EACJ;EAEA,IAAIC,UAAU,GAAG,CAAC,CAAC,CAAC;EACpB,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,qBAAqB,GAAG,KAAK;EACjC,IAAIC,GAAG,GAAGN,MAAM,CAACO,QAAQ;EACzB,IAAIC,iBAAiB;EAErB,SAASN,YAAYA,CAACO,QAAQ,EAAE;IAC9B;IACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClCA,QAAQ,GAAG,IAAIC,QAAQ,CAAC,EAAE,GAAGD,QAAQ,CAAC;IACxC;IACA;IACA,IAAIE,IAAI,GAAG,IAAIC,KAAK,CAACC,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;IAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;MAClCJ,IAAI,CAACI,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC;IAC9B;IACA;IACA,IAAIC,IAAI,GAAG;MAAEP,QAAQ,EAAEA,QAAQ;MAAEE,IAAI,EAAEA;IAAK,CAAC;IAC7CP,aAAa,CAACD,UAAU,CAAC,GAAGa,IAAI;IAChCR,iBAAiB,CAACL,UAAU,CAAC;IAC7B,OAAOA,UAAU,EAAE;EACrB;EAEA,SAASc,cAAcA,CAACC,MAAM,EAAE;IAC5B,OAAOd,aAAa,CAACc,MAAM,CAAC;EAChC;EAEA,SAASC,GAAGA,CAACH,IAAI,EAAE;IACf,IAAIP,QAAQ,GAAGO,IAAI,CAACP,QAAQ;IAC5B,IAAIE,IAAI,GAAGK,IAAI,CAACL,IAAI;IACpB,QAAQA,IAAI,CAACG,MAAM;MACnB,KAAK,CAAC;QACFL,QAAQ,CAAC,CAAC;QACV;MACJ,KAAK,CAAC;QACFA,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QACjB;MACJ,KAAK,CAAC;QACFF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B;MACJ,KAAK,CAAC;QACFF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC;MACJ;QACIF,QAAQ,CAACW,KAAK,CAACnB,SAAS,EAAEU,IAAI,CAAC;QAC/B;IACJ;EACJ;EAEA,SAASU,YAAYA,CAACH,MAAM,EAAE;IAC1B;IACA;IACA,IAAIb,qBAAqB,EAAE;MACvB;MACA;MACAiB,UAAU,CAACD,YAAY,EAAE,CAAC,EAAEH,MAAM,CAAC;IACvC,CAAC,MAAM;MACH,IAAIF,IAAI,GAAGZ,aAAa,CAACc,MAAM,CAAC;MAChC,IAAIF,IAAI,EAAE;QACNX,qBAAqB,GAAG,IAAI;QAC5B,IAAI;UACAc,GAAG,CAACH,IAAI,CAAC;QACb,CAAC,SAAS;UACNC,cAAc,CAACC,MAAM,CAAC;UACtBb,qBAAqB,GAAG,KAAK;QACjC;MACJ;IACJ;EACJ;EAEA,SAASkB,6BAA6BA,CAAA,EAAG;IACrCf,iBAAiB,GAAG,SAAAA,CAASU,MAAM,EAAE;MACjCM,OAAO,CAACC,QAAQ,CAAC,YAAY;QAAEJ,YAAY,CAACH,MAAM,CAAC;MAAE,CAAC,CAAC;IAC3D,CAAC;EACL;EAEA,SAASQ,iBAAiBA,CAAA,EAAG;IACzB;IACA;IACA,IAAI1B,MAAM,CAAC2B,WAAW,IAAI,CAAC3B,MAAM,CAAC4B,aAAa,EAAE;MAC7C,IAAIC,yBAAyB,GAAG,IAAI;MACpC,IAAIC,YAAY,GAAG9B,MAAM,CAAC+B,SAAS;MACnC/B,MAAM,CAAC+B,SAAS,GAAG,YAAW;QAC1BF,yBAAyB,GAAG,KAAK;MACrC,CAAC;MACD7B,MAAM,CAAC2B,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC;MAC3B3B,MAAM,CAAC+B,SAAS,GAAGD,YAAY;MAC/B,OAAOD,yBAAyB;IACpC;EACJ;EAEA,SAASG,gCAAgCA,CAAA,EAAG;IACxC;IACA;IACA;;IAEA,IAAIC,aAAa,GAAG,eAAe,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IACzD,IAAIC,eAAe,GAAG,SAAAA,CAASC,KAAK,EAAE;MAClC,IAAIA,KAAK,CAACC,MAAM,KAAKtC,MAAM,IACvB,OAAOqC,KAAK,CAACE,IAAI,KAAK,QAAQ,IAC9BF,KAAK,CAACE,IAAI,CAACC,OAAO,CAACP,aAAa,CAAC,KAAK,CAAC,EAAE;QACzCZ,YAAY,CAAC,CAACgB,KAAK,CAACE,IAAI,CAACE,KAAK,CAACR,aAAa,CAACnB,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC;IAED,IAAId,MAAM,CAAC0C,gBAAgB,EAAE;MACzB1C,MAAM,CAAC0C,gBAAgB,CAAC,SAAS,EAAEN,eAAe,EAAE,KAAK,CAAC;IAC9D,CAAC,MAAM;MACHpC,MAAM,CAAC2C,WAAW,CAAC,WAAW,EAAEP,eAAe,CAAC;IACpD;IAEA5B,iBAAiB,GAAG,SAAAA,CAASU,MAAM,EAAE;MACjClB,MAAM,CAAC2B,WAAW,CAACM,aAAa,GAAGf,MAAM,EAAE,GAAG,CAAC;IACnD,CAAC;EACL;EAEA,SAAS0B,mCAAmCA,CAAA,EAAG;IAC3C,IAAIC,OAAO,GAAG,IAAIC,cAAc,CAAC,CAAC;IAClCD,OAAO,CAACE,KAAK,CAAChB,SAAS,GAAG,UAASM,KAAK,EAAE;MACtC,IAAInB,MAAM,GAAGmB,KAAK,CAACE,IAAI;MACvBlB,YAAY,CAACH,MAAM,CAAC;IACxB,CAAC;IAEDV,iBAAiB,GAAG,SAAAA,CAASU,MAAM,EAAE;MACjC2B,OAAO,CAACG,KAAK,CAACrB,WAAW,CAACT,MAAM,CAAC;IACrC,CAAC;EACL;EAEA,SAAS+B,qCAAqCA,CAAA,EAAG;IAC7C,IAAIC,IAAI,GAAG5C,GAAG,CAAC6C,eAAe;IAC9B3C,iBAAiB,GAAG,SAAAA,CAASU,MAAM,EAAE;MACjC;MACA;MACA,IAAIkC,MAAM,GAAG9C,GAAG,CAAC+C,aAAa,CAAC,QAAQ,CAAC;MACxCD,MAAM,CAACE,kBAAkB,GAAG,YAAY;QACpCjC,YAAY,CAACH,MAAM,CAAC;QACpBkC,MAAM,CAACE,kBAAkB,GAAG,IAAI;QAChCJ,IAAI,CAACK,WAAW,CAACH,MAAM,CAAC;QACxBA,MAAM,GAAG,IAAI;MACjB,CAAC;MACDF,IAAI,CAACM,WAAW,CAACJ,MAAM,CAAC;IAC5B,CAAC;EACL;EAEA,SAASK,+BAA+BA,CAAA,EAAG;IACvCjD,iBAAiB,GAAG,SAAAA,CAASU,MAAM,EAAE;MACjCI,UAAU,CAACD,YAAY,EAAE,CAAC,EAAEH,MAAM,CAAC;IACvC,CAAC;EACL;;EAEA;EACA,IAAIwC,QAAQ,GAAGC,MAAM,CAACC,cAAc,IAAID,MAAM,CAACC,cAAc,CAAC5D,MAAM,CAAC;EACrE0D,QAAQ,GAAGA,QAAQ,IAAIA,QAAQ,CAACpC,UAAU,GAAGoC,QAAQ,GAAG1D,MAAM;;EAE9D;EACA,IAAI,CAAC,CAAC,CAAC6D,QAAQ,CAACC,IAAI,CAAC9D,MAAM,CAACwB,OAAO,CAAC,KAAK,kBAAkB,EAAE;IACzD;IACAD,6BAA6B,CAAC,CAAC;EAEnC,CAAC,MAAM,IAAIG,iBAAiB,CAAC,CAAC,EAAE;IAC5B;IACAM,gCAAgC,CAAC,CAAC;EAEtC,CAAC,MAAM,IAAIhC,MAAM,CAAC8C,cAAc,EAAE;IAC9B;IACAF,mCAAmC,CAAC,CAAC;EAEzC,CAAC,MAAM,IAAItC,GAAG,IAAI,oBAAoB,IAAIA,GAAG,CAAC+C,aAAa,CAAC,QAAQ,CAAC,EAAE;IACnE;IACAJ,qCAAqC,CAAC,CAAC;EAE3C,CAAC,MAAM;IACH;IACAQ,+BAA+B,CAAC,CAAC;EACrC;EAEAC,QAAQ,CAACxD,YAAY,GAAGA,YAAY;EACpCwD,QAAQ,CAACzC,cAAc,GAAGA,cAAc;AAC5C,CAAC,EAAC,OAAO8C,IAAI,KAAK,WAAW,GAAG,OAAO/D,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,GAAG+D,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}