{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nexport var LayoutContext = /*#__PURE__*/React.createContext({\n  siderHook: {\n    addSider: function addSider() {\n      return null;\n    },\n    removeSider: function removeSider() {\n      return null;\n    }\n  }\n});\nfunction generator(_ref) {\n  var suffixCls = _ref.suffixCls,\n    tagName = _ref.tagName,\n    displayName = _ref.displayName;\n  return function (BasicComponent) {\n    var Adapter = /*#__PURE__*/React.forwardRef(function (props, ref) {\n      var _React$useContext = React.useContext(ConfigContext),\n        getPrefixCls = _React$useContext.getPrefixCls;\n      var customizePrefixCls = props.prefixCls;\n      var prefixCls = getPrefixCls(suffixCls, customizePrefixCls);\n      return /*#__PURE__*/React.createElement(BasicComponent, _extends({\n        ref: ref,\n        prefixCls: prefixCls,\n        tagName: tagName\n      }, props));\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      Adapter.displayName = displayName;\n    }\n    return Adapter;\n  };\n}\nvar Basic = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    tagName = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"tagName\"]);\n  var classString = classNames(prefixCls, className);\n  return /*#__PURE__*/React.createElement(tagName, _extends(_extends({\n    className: classString\n  }, others), {\n    ref: ref\n  }), children);\n});\nvar BasicLayout = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    direction = _React$useContext2.direction;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    siders = _React$useState2[0],\n    setSiders = _React$useState2[1];\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    hasSider = props.hasSider,\n    Tag = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"hasSider\", \"tagName\"]);\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-sider\"), typeof hasSider === 'boolean' ? hasSider : siders.length > 0), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var contextValue = React.useMemo(function () {\n    return {\n      siderHook: {\n        addSider: function addSider(id) {\n          setSiders(function (prev) {\n            return [].concat(_toConsumableArray(prev), [id]);\n          });\n        },\n        removeSider: function removeSider(id) {\n          setSiders(function (prev) {\n            return prev.filter(function (currentId) {\n              return currentId !== id;\n            });\n          });\n        }\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: ref,\n    className: classString\n  }, others), children));\n});\nvar Layout = generator({\n  suffixCls: 'layout',\n  tagName: 'section',\n  displayName: 'Layout'\n})(BasicLayout);\nvar Header = generator({\n  suffixCls: 'layout-header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nvar Footer = generator({\n  suffixCls: 'layout-footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nvar Content = generator({\n  suffixCls: 'layout-content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Header, Footer, Content };\nexport default Layout;", "map": {"version": 3, "names": ["_toConsumableArray", "_defineProperty", "_slicedToArray", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "ConfigContext", "LayoutContext", "createContext", "<PERSON>r<PERSON><PERSON>", "addSider", "removeSider", "generator", "_ref", "suffixCls", "tagName", "displayName", "BasicComponent", "Adapter", "forwardRef", "props", "ref", "_React$useContext", "useContext", "getPrefixCls", "customizePrefixCls", "prefixCls", "createElement", "process", "env", "NODE_ENV", "Basic", "className", "children", "others", "classString", "BasicLayout", "_classNames", "_React$useContext2", "direction", "_React$useState", "useState", "_React$useState2", "siders", "setSiders", "hasSider", "Tag", "concat", "contextValue", "useMemo", "id", "prev", "filter", "currentId", "Provider", "value", "Layout", "Header", "Footer", "Content"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/layout/layout.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nexport var LayoutContext = /*#__PURE__*/React.createContext({\n  siderHook: {\n    addSider: function addSider() {\n      return null;\n    },\n    removeSider: function removeSider() {\n      return null;\n    }\n  }\n});\nfunction generator(_ref) {\n  var suffixCls = _ref.suffixCls,\n    tagName = _ref.tagName,\n    displayName = _ref.displayName;\n  return function (BasicComponent) {\n    var Adapter = /*#__PURE__*/React.forwardRef(function (props, ref) {\n      var _React$useContext = React.useContext(ConfigContext),\n        getPrefixCls = _React$useContext.getPrefixCls;\n      var customizePrefixCls = props.prefixCls;\n      var prefixCls = getPrefixCls(suffixCls, customizePrefixCls);\n      return /*#__PURE__*/React.createElement(BasicComponent, _extends({\n        ref: ref,\n        prefixCls: prefixCls,\n        tagName: tagName\n      }, props));\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      Adapter.displayName = displayName;\n    }\n    return Adapter;\n  };\n}\nvar Basic = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    tagName = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"tagName\"]);\n  var classString = classNames(prefixCls, className);\n  return /*#__PURE__*/React.createElement(tagName, _extends(_extends({\n    className: classString\n  }, others), {\n    ref: ref\n  }), children);\n});\nvar BasicLayout = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    direction = _React$useContext2.direction;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    siders = _React$useState2[0],\n    setSiders = _React$useState2[1];\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    hasSider = props.hasSider,\n    Tag = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"hasSider\", \"tagName\"]);\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-sider\"), typeof hasSider === 'boolean' ? hasSider : siders.length > 0), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var contextValue = React.useMemo(function () {\n    return {\n      siderHook: {\n        addSider: function addSider(id) {\n          setSiders(function (prev) {\n            return [].concat(_toConsumableArray(prev), [id]);\n          });\n        },\n        removeSider: function removeSider(id) {\n          setSiders(function (prev) {\n            return prev.filter(function (currentId) {\n              return currentId !== id;\n            });\n          });\n        }\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: ref,\n    className: classString\n  }, others), children));\n});\nvar Layout = generator({\n  suffixCls: 'layout',\n  tagName: 'section',\n  displayName: 'Layout'\n})(BasicLayout);\nvar Header = generator({\n  suffixCls: 'layout-header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nvar Footer = generator({\n  suffixCls: 'layout-footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nvar Content = generator({\n  suffixCls: 'layout-content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Header, Footer, Content };\nexport default Layout;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,IAAIC,aAAa,GAAG,aAAaF,KAAK,CAACG,aAAa,CAAC;EAC1DC,SAAS,EAAE;IACTC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;MAC5B,OAAO,IAAI;IACb,CAAC;IACDC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClC,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC;AACF,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,WAAW,GAAGH,IAAI,CAACG,WAAW;EAChC,OAAO,UAAUC,cAAc,EAAE;IAC/B,IAAIC,OAAO,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAChE,IAAIC,iBAAiB,GAAGjB,KAAK,CAACkB,UAAU,CAACjB,aAAa,CAAC;QACrDkB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;MAC/C,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;MACxC,IAAIA,SAAS,GAAGF,YAAY,CAACV,SAAS,EAAEW,kBAAkB,CAAC;MAC3D,OAAO,aAAapB,KAAK,CAACsB,aAAa,CAACV,cAAc,EAAE5B,QAAQ,CAAC;QAC/DgC,GAAG,EAAEA,GAAG;QACRK,SAAS,EAAEA,SAAS;QACpBX,OAAO,EAAEA;MACX,CAAC,EAAEK,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCZ,OAAO,CAACF,WAAW,GAAGA,WAAW;IACnC;IACA,OAAOE,OAAO;EAChB,CAAC;AACH;AACA,IAAIa,KAAK,GAAG,aAAa1B,KAAK,CAACc,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIK,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC7BM,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBlB,OAAO,GAAGK,KAAK,CAACL,OAAO;IACvBmB,MAAM,GAAG5C,MAAM,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAC3E,IAAIe,WAAW,GAAG/B,UAAU,CAACsB,SAAS,EAAEM,SAAS,CAAC;EAClD,OAAO,aAAa3B,KAAK,CAACsB,aAAa,CAACZ,OAAO,EAAE1B,QAAQ,CAACA,QAAQ,CAAC;IACjE2C,SAAS,EAAEG;EACb,CAAC,EAAED,MAAM,CAAC,EAAE;IACVb,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEY,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,IAAIG,WAAW,GAAG,aAAa/B,KAAK,CAACc,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIgB,WAAW;EACf,IAAIC,kBAAkB,GAAGjC,KAAK,CAACkB,UAAU,CAACjB,aAAa,CAAC;IACtDiC,SAAS,GAAGD,kBAAkB,CAACC,SAAS;EAC1C,IAAIC,eAAe,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGtD,cAAc,CAACoD,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIhB,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC7BM,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBY,QAAQ,GAAGzB,KAAK,CAACyB,QAAQ;IACzBC,GAAG,GAAG1B,KAAK,CAACL,OAAO;IACnBmB,MAAM,GAAG5C,MAAM,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EACvF,IAAIe,WAAW,GAAG/B,UAAU,CAACsB,SAAS,GAAGW,WAAW,GAAG,CAAC,CAAC,EAAElD,eAAe,CAACkD,WAAW,EAAE,EAAE,CAACU,MAAM,CAACrB,SAAS,EAAE,YAAY,CAAC,EAAE,OAAOmB,QAAQ,KAAK,SAAS,GAAGA,QAAQ,GAAGF,MAAM,CAACzC,MAAM,GAAG,CAAC,CAAC,EAAEf,eAAe,CAACkD,WAAW,EAAE,EAAE,CAACU,MAAM,CAACrB,SAAS,EAAE,MAAM,CAAC,EAAEa,SAAS,KAAK,KAAK,CAAC,EAAEF,WAAW,GAAGL,SAAS,CAAC;EACpS,IAAIgB,YAAY,GAAG3C,KAAK,CAAC4C,OAAO,CAAC,YAAY;IAC3C,OAAO;MACLxC,SAAS,EAAE;QACTC,QAAQ,EAAE,SAASA,QAAQA,CAACwC,EAAE,EAAE;UAC9BN,SAAS,CAAC,UAAUO,IAAI,EAAE;YACxB,OAAO,EAAE,CAACJ,MAAM,CAAC7D,kBAAkB,CAACiE,IAAI,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC;UAClD,CAAC,CAAC;QACJ,CAAC;QACDvC,WAAW,EAAE,SAASA,WAAWA,CAACuC,EAAE,EAAE;UACpCN,SAAS,CAAC,UAAUO,IAAI,EAAE;YACxB,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUC,SAAS,EAAE;cACtC,OAAOA,SAAS,KAAKH,EAAE;YACzB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa7C,KAAK,CAACsB,aAAa,CAACpB,aAAa,CAAC+C,QAAQ,EAAE;IAC9DC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAa3C,KAAK,CAACsB,aAAa,CAACmB,GAAG,EAAEzD,QAAQ,CAAC;IAChDgC,GAAG,EAAEA,GAAG;IACRW,SAAS,EAAEG;EACb,CAAC,EAAED,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,IAAIuB,MAAM,GAAG5C,SAAS,CAAC;EACrBE,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACoB,WAAW,CAAC;AACf,IAAIqB,MAAM,GAAG7C,SAAS,CAAC;EACrBE,SAAS,EAAE,eAAe;EAC1BC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACe,KAAK,CAAC;AACT,IAAI2B,MAAM,GAAG9C,SAAS,CAAC;EACrBE,SAAS,EAAE,eAAe;EAC1BC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACe,KAAK,CAAC;AACT,IAAI4B,OAAO,GAAG/C,SAAS,CAAC;EACtBE,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE;AACf,CAAC,CAAC,CAACe,KAAK,CAAC;AACT,SAAS0B,MAAM,EAAEC,MAAM,EAAEC,OAAO;AAChC,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}