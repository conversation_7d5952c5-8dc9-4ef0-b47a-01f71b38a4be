{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _array = _interopRequireDefault(require(\"./array.js\"));\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nvar _descending = _interopRequireDefault(require(\"./descending.js\"));\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\nvar _math = require(\"./math.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default() {\n  var value = _identity.default,\n    sortValues = _descending.default,\n    sort = null,\n    startAngle = (0, _constant.default)(0),\n    endAngle = (0, _constant.default)(_math.tau),\n    padAngle = (0, _constant.default)(0);\n  function pie(data) {\n    var i,\n      n = (data = (0, _array.default)(data)).length,\n      j,\n      k,\n      sum = 0,\n      index = new Array(n),\n      arcs = new Array(n),\n      a0 = +startAngle.apply(this, arguments),\n      da = Math.min(_math.tau, Math.max(-_math.tau, endAngle.apply(this, arguments) - a0)),\n      a1,\n      p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n      pa = p * (da < 0 ? -1 : 1),\n      v;\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    } // Optionally sort the arcs by previously-computed values or by data.\n\n    if (sortValues != null) index.sort(function (i, j) {\n      return sortValues(arcs[i], arcs[j]);\n    });else if (sort != null) index.sort(function (i, j) {\n      return sort(data[i], data[j]);\n    }); // Compute the arcs! They are stored in the original data's order.\n\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n    return arcs;\n  }\n  pie.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : value;\n  };\n  pie.sortValues = function (_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n  pie.sort = function (_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n  pie.startAngle = function (_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : startAngle;\n  };\n  pie.endAngle = function (_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : endAngle;\n  };\n  pie.padAngle = function (_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : padAngle;\n  };\n  return pie;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_array", "_interopRequireDefault", "require", "_constant", "_descending", "_identity", "_math", "obj", "__esModule", "sortValues", "sort", "startAngle", "endAngle", "tau", "padAngle", "pie", "data", "i", "n", "length", "j", "k", "sum", "index", "Array", "arcs", "a0", "apply", "arguments", "da", "Math", "min", "max", "a1", "p", "abs", "pa", "v", "_"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/pie.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _array = _interopRequireDefault(require(\"./array.js\"));\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nvar _descending = _interopRequireDefault(require(\"./descending.js\"));\n\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\n\nvar _math = require(\"./math.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default() {\n  var value = _identity.default,\n      sortValues = _descending.default,\n      sort = null,\n      startAngle = (0, _constant.default)(0),\n      endAngle = (0, _constant.default)(_math.tau),\n      padAngle = (0, _constant.default)(0);\n\n  function pie(data) {\n    var i,\n        n = (data = (0, _array.default)(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(_math.tau, Math.max(-_math.tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    } // Optionally sort the arcs by previously-computed values or by data.\n\n\n    if (sortValues != null) index.sort(function (i, j) {\n      return sortValues(arcs[i], arcs[j]);\n    });else if (sort != null) index.sort(function (i, j) {\n      return sort(data[i], data[j]);\n    }); // Compute the arcs! They are stored in the original data's order.\n\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : value;\n  };\n\n  pie.sortValues = function (_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function (_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function (_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function (_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function (_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), pie) : padAngle;\n  };\n\n  return pie;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,IAAIC,SAAS,GAAGF,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIE,WAAW,GAAGH,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEpE,IAAIG,SAAS,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAII,KAAK,GAAGJ,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASD,sBAAsBA,CAACM,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAET,OAAO,EAAES;EAAI,CAAC;AAAE;AAE9F,SAASR,QAAQA,CAAA,EAAG;EAClB,IAAIF,KAAK,GAAGQ,SAAS,CAACP,OAAO;IACzBW,UAAU,GAAGL,WAAW,CAACN,OAAO;IAChCY,IAAI,GAAG,IAAI;IACXC,UAAU,GAAG,CAAC,CAAC,EAAER,SAAS,CAACL,OAAO,EAAE,CAAC,CAAC;IACtCc,QAAQ,GAAG,CAAC,CAAC,EAAET,SAAS,CAACL,OAAO,EAAEQ,KAAK,CAACO,GAAG,CAAC;IAC5CC,QAAQ,GAAG,CAAC,CAAC,EAAEX,SAAS,CAACL,OAAO,EAAE,CAAC,CAAC;EAExC,SAASiB,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAIC,CAAC;MACDC,CAAC,GAAG,CAACF,IAAI,GAAG,CAAC,CAAC,EAAEhB,MAAM,CAACF,OAAO,EAAEkB,IAAI,CAAC,EAAEG,MAAM;MAC7CC,CAAC;MACDC,CAAC;MACDC,GAAG,GAAG,CAAC;MACPC,KAAK,GAAG,IAAIC,KAAK,CAACN,CAAC,CAAC;MACpBO,IAAI,GAAG,IAAID,KAAK,CAACN,CAAC,CAAC;MACnBQ,EAAE,GAAG,CAACf,UAAU,CAACgB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACvCC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACzB,KAAK,CAACO,GAAG,EAAEiB,IAAI,CAACE,GAAG,CAAC,CAAC1B,KAAK,CAACO,GAAG,EAAED,QAAQ,CAACe,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGF,EAAE,CAAC,CAAC;MACpFO,EAAE;MACFC,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACN,EAAE,CAAC,GAAGX,CAAC,EAAEJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;MAC/DQ,EAAE,GAAGF,CAAC,IAAIL,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1BQ,CAAC;IAEL,KAAKpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtB,IAAI,CAACoB,CAAC,GAAGZ,IAAI,CAACF,KAAK,CAACN,CAAC,CAAC,GAAGA,CAAC,CAAC,GAAG,CAACpB,KAAK,CAACmB,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3DM,GAAG,IAAIe,CAAC;MACV;IACF,CAAC,CAAC;;IAGF,IAAI5B,UAAU,IAAI,IAAI,EAAEc,KAAK,CAACb,IAAI,CAAC,UAAUO,CAAC,EAAEG,CAAC,EAAE;MACjD,OAAOX,UAAU,CAACgB,IAAI,CAACR,CAAC,CAAC,EAAEQ,IAAI,CAACL,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,KAAK,IAAIV,IAAI,IAAI,IAAI,EAAEa,KAAK,CAACb,IAAI,CAAC,UAAUO,CAAC,EAAEG,CAAC,EAAE;MACnD,OAAOV,IAAI,CAACM,IAAI,CAACC,CAAC,CAAC,EAAED,IAAI,CAACI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC;;IAEJ,KAAKH,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGC,GAAG,GAAG,CAACO,EAAE,GAAGX,CAAC,GAAGkB,EAAE,IAAId,GAAG,GAAG,CAAC,EAAEL,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAES,EAAE,GAAGO,EAAE,EAAE;MAClEb,CAAC,GAAGG,KAAK,CAACN,CAAC,CAAC,EAAEoB,CAAC,GAAGZ,IAAI,CAACL,CAAC,CAAC,EAAEa,EAAE,GAAGP,EAAE,IAAIW,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGhB,CAAC,GAAG,CAAC,CAAC,GAAGe,EAAE,EAAEX,IAAI,CAACL,CAAC,CAAC,GAAG;QACvEJ,IAAI,EAAEA,IAAI,CAACI,CAAC,CAAC;QACbG,KAAK,EAAEN,CAAC;QACRpB,KAAK,EAAEwC,CAAC;QACR1B,UAAU,EAAEe,EAAE;QACdd,QAAQ,EAAEqB,EAAE;QACZnB,QAAQ,EAAEoB;MACZ,CAAC;IACH;IAEA,OAAOT,IAAI;EACb;EAEAV,GAAG,CAAClB,KAAK,GAAG,UAAUyC,CAAC,EAAE;IACvB,OAAOV,SAAS,CAACT,MAAM,IAAItB,KAAK,GAAG,OAAOyC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEnC,SAAS,CAACL,OAAO,EAAE,CAACwC,CAAC,CAAC,EAAEvB,GAAG,IAAIlB,KAAK;EAC3G,CAAC;EAEDkB,GAAG,CAACN,UAAU,GAAG,UAAU6B,CAAC,EAAE;IAC5B,OAAOV,SAAS,CAACT,MAAM,IAAIV,UAAU,GAAG6B,CAAC,EAAE5B,IAAI,GAAG,IAAI,EAAEK,GAAG,IAAIN,UAAU;EAC3E,CAAC;EAEDM,GAAG,CAACL,IAAI,GAAG,UAAU4B,CAAC,EAAE;IACtB,OAAOV,SAAS,CAACT,MAAM,IAAIT,IAAI,GAAG4B,CAAC,EAAE7B,UAAU,GAAG,IAAI,EAAEM,GAAG,IAAIL,IAAI;EACrE,CAAC;EAEDK,GAAG,CAACJ,UAAU,GAAG,UAAU2B,CAAC,EAAE;IAC5B,OAAOV,SAAS,CAACT,MAAM,IAAIR,UAAU,GAAG,OAAO2B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEnC,SAAS,CAACL,OAAO,EAAE,CAACwC,CAAC,CAAC,EAAEvB,GAAG,IAAIJ,UAAU;EACrH,CAAC;EAEDI,GAAG,CAACH,QAAQ,GAAG,UAAU0B,CAAC,EAAE;IAC1B,OAAOV,SAAS,CAACT,MAAM,IAAIP,QAAQ,GAAG,OAAO0B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEnC,SAAS,CAACL,OAAO,EAAE,CAACwC,CAAC,CAAC,EAAEvB,GAAG,IAAIH,QAAQ;EACjH,CAAC;EAEDG,GAAG,CAACD,QAAQ,GAAG,UAAUwB,CAAC,EAAE;IAC1B,OAAOV,SAAS,CAACT,MAAM,IAAIL,QAAQ,GAAG,OAAOwB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAEnC,SAAS,CAACL,OAAO,EAAE,CAACwC,CAAC,CAAC,EAAEvB,GAAG,IAAID,QAAQ;EACjH,CAAC;EAED,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}