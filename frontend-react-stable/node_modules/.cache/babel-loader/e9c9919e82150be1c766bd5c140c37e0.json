{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport var Context = /*#__PURE__*/React.createContext({});\nexport default function MotionProvider(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: props\n  }, children);\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_excluded", "React", "Context", "createContext", "MotionProvider", "_ref", "children", "props", "createElement", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/context.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport var Context = /*#__PURE__*/React.createContext({});\nexport default function MotionProvider(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: props\n  }, children);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACzD,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,KAAK,GAAGR,wBAAwB,CAACM,IAAI,EAAEL,SAAS,CAAC;EACnD,OAAO,aAAaC,KAAK,CAACO,aAAa,CAACN,OAAO,CAACO,QAAQ,EAAE;IACxDC,KAAK,EAAEH;EACT,CAAC,EAAED,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}