{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nvar id = 0;\nvar ids = {};\n// Support call raf with delay specified frame\nexport default function wrapperRaf(callback) {\n  var delayFrames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  var myId = id++;\n  var restFrames = delayFrames;\n  function internalCallback() {\n    restFrames -= 1;\n    if (restFrames <= 0) {\n      callback();\n      delete ids[myId];\n    } else {\n      ids[myId] = raf(internalCallback);\n    }\n  }\n  ids[myId] = raf(internalCallback);\n  return myId;\n}\nwrapperRaf.cancel = function cancel(pid) {\n  if (pid === undefined) return;\n  raf.cancel(ids[pid]);\n  delete ids[pid];\n};\nwrapperRaf.ids = ids; // export this for test usage", "map": {"version": 3, "names": ["raf", "id", "ids", "wrapperRaf", "callback", "delayFrames", "arguments", "length", "undefined", "myId", "restFrames", "internalCallback", "cancel", "pid"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/raf.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nvar id = 0;\nvar ids = {};\n// Support call raf with delay specified frame\nexport default function wrapperRaf(callback) {\n  var delayFrames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  var myId = id++;\n  var restFrames = delayFrames;\n  function internalCallback() {\n    restFrames -= 1;\n    if (restFrames <= 0) {\n      callback();\n      delete ids[myId];\n    } else {\n      ids[myId] = raf(internalCallback);\n    }\n  }\n  ids[myId] = raf(internalCallback);\n  return myId;\n}\nwrapperRaf.cancel = function cancel(pid) {\n  if (pid === undefined) return;\n  raf.cancel(ids[pid]);\n  delete ids[pid];\n};\nwrapperRaf.ids = ids; // export this for test usage"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,GAAG,GAAG,CAAC,CAAC;AACZ;AACA,eAAe,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC3C,IAAIC,WAAW,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACvF,IAAIG,IAAI,GAAGR,EAAE,EAAE;EACf,IAAIS,UAAU,GAAGL,WAAW;EAC5B,SAASM,gBAAgBA,CAAA,EAAG;IAC1BD,UAAU,IAAI,CAAC;IACf,IAAIA,UAAU,IAAI,CAAC,EAAE;MACnBN,QAAQ,CAAC,CAAC;MACV,OAAOF,GAAG,CAACO,IAAI,CAAC;IAClB,CAAC,MAAM;MACLP,GAAG,CAACO,IAAI,CAAC,GAAGT,GAAG,CAACW,gBAAgB,CAAC;IACnC;EACF;EACAT,GAAG,CAACO,IAAI,CAAC,GAAGT,GAAG,CAACW,gBAAgB,CAAC;EACjC,OAAOF,IAAI;AACb;AACAN,UAAU,CAACS,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;EACvC,IAAIA,GAAG,KAAKL,SAAS,EAAE;EACvBR,GAAG,CAACY,MAAM,CAACV,GAAG,CAACW,GAAG,CAAC,CAAC;EACpB,OAAOX,GAAG,CAACW,GAAG,CAAC;AACjB,CAAC;AACDV,UAAU,CAACD,GAAG,GAAGA,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}