{"ast": null, "code": "'use strict';\n\nvar $TypeError = require('es-errors/type');\nvar inspect = require('object-inspect');\nvar getSideChannelList = require('side-channel-list');\nvar getSideChannelMap = require('side-channel-map');\nvar getSideChannelWeakMap = require('side-channel-weakmap');\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n  /** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n  /** @type {Channel | undefined} */var $channelData;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      return !!$channelData && $channelData['delete'](key);\n    },\n    get: function (key) {\n      return $channelData && $channelData.get(key);\n    },\n    has: function (key) {\n      return !!$channelData && $channelData.has(key);\n    },\n    set: function (key, value) {\n      if (!$channelData) {\n        $channelData = makeChannel();\n      }\n      $channelData.set(key, value);\n    }\n  };\n  // @ts-expect-error TODO: figure out why this is erroring\n  return channel;\n};", "map": {"version": 3, "names": ["$TypeError", "require", "inspect", "getSideChannelList", "getSideChannelMap", "getSideChannelWeakMap", "makeChannel", "module", "exports", "getSideChannel", "$channelData", "channel", "assert", "key", "has", "delete", "get", "set", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/side-channel/index.js"], "sourcesContent": ["'use strict';\n\nvar $TypeError = require('es-errors/type');\nvar inspect = require('object-inspect');\nvar getSideChannelList = require('side-channel-list');\nvar getSideChannelMap = require('side-channel-map');\nvar getSideChannelWeakMap = require('side-channel-weakmap');\n\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n\t/** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n\t/** @type {Channel | undefined} */ var $channelData;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\treturn !!$channelData && $channelData['delete'](key);\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn $channelData && $channelData.get(key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn !!$channelData && $channelData.has(key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$channelData) {\n\t\t\t\t$channelData = makeChannel();\n\t\t\t}\n\n\t\t\t$channelData.set(key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAIC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACvC,IAAIE,kBAAkB,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AACrD,IAAIG,iBAAiB,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACnD,IAAII,qBAAqB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAE3D,IAAIK,WAAW,GAAGD,qBAAqB,IAAID,iBAAiB,IAAID,kBAAkB;;AAElF;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAAA,EAAG;EAC1C;;EAEA,kCAAmC,IAAIC,YAAY;;EAEnD;EACA,IAAIC,OAAO,GAAG;IACbC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAE;MACtB,IAAI,CAACF,OAAO,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIb,UAAU,CAAC,gCAAgC,GAAGE,OAAO,CAACW,GAAG,CAAC,CAAC;MACtE;IACD,CAAC;IACD,QAAQ,EAAE,SAAAE,CAAUF,GAAG,EAAE;MACxB,OAAO,CAAC,CAACH,YAAY,IAAIA,YAAY,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;IACrD,CAAC;IACDG,GAAG,EAAE,SAAAA,CAAUH,GAAG,EAAE;MACnB,OAAOH,YAAY,IAAIA,YAAY,CAACM,GAAG,CAACH,GAAG,CAAC;IAC7C,CAAC;IACDC,GAAG,EAAE,SAAAA,CAAUD,GAAG,EAAE;MACnB,OAAO,CAAC,CAACH,YAAY,IAAIA,YAAY,CAACI,GAAG,CAACD,GAAG,CAAC;IAC/C,CAAC;IACDI,GAAG,EAAE,SAAAA,CAAUJ,GAAG,EAAEK,KAAK,EAAE;MAC1B,IAAI,CAACR,YAAY,EAAE;QAClBA,YAAY,GAAGJ,WAAW,CAAC,CAAC;MAC7B;MAEAI,YAAY,CAACO,GAAG,CAACJ,GAAG,EAAEK,KAAK,CAAC;IAC7B;EACD,CAAC;EACD;EACA,OAAOP,OAAO;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}