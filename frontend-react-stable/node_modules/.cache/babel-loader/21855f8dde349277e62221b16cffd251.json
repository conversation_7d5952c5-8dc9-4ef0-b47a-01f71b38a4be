{"ast": null, "code": "// https://stackoverflow.com/questions/46176165/ways-to-get-string-literal-type-of-array-values-without-enum-overhead\nexport var tuple = function tuple() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return args;\n};\nexport var tupleNum = function tupleNum() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  return args;\n};", "map": {"version": 3, "names": ["tuple", "_len", "arguments", "length", "args", "Array", "_key", "tupleNum", "_len2", "_key2"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/type.js"], "sourcesContent": ["// https://stackoverflow.com/questions/46176165/ways-to-get-string-literal-type-of-array-values-without-enum-overhead\nexport var tuple = function tuple() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return args;\n};\nexport var tupleNum = function tupleNum() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  return args;\n};"], "mappings": "AAAA;AACA,OAAO,IAAIA,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAClC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAOF,IAAI;AACb,CAAC;AACD,OAAO,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACxC,KAAK,IAAIC,KAAK,GAAGN,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACG,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FL,IAAI,CAACK,KAAK,CAAC,GAAGP,SAAS,CAACO,KAAK,CAAC;EAChC;EACA,OAAOL,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}