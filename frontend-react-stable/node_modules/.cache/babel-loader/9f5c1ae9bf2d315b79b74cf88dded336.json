{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = exports.EXITING = exports.ENTERED = exports.ENTERING = exports.EXITED = exports.UNMOUNTED = void 0;\nvar PropTypes = _interopRequireWildcard(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _reactLifecyclesCompat = require(\"react-lifecycles-compat\");\nvar _PropTypes = require(\"./utils/PropTypes\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n          var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};\n          if (desc.get || desc.set) {\n            Object.defineProperty(newObj, key, desc);\n          } else {\n            newObj[key] = obj[key];\n          }\n        }\n      }\n    }\n    newObj.default = obj;\n    return newObj;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nvar UNMOUNTED = 'unmounted';\nexports.UNMOUNTED = UNMOUNTED;\nvar EXITED = 'exited';\nexports.EXITED = EXITED;\nvar ENTERING = 'entering';\nexports.ENTERING = ENTERING;\nvar ENTERED = 'entered';\nexports.ENTERED = ENTERED;\nvar EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 0 },\n *   entered:  { opacity: 1 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nexports.EXITING = EXITING;\nvar Transition = /*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n  function Transition(props, context) {\n    var _this;\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context.transitionGroup; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n  var _proto = Transition.prototype;\n  _proto.getChildContext = function getChildContext() {\n    return {\n      transitionGroup: null // allows for nested Transitions\n    };\n  };\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n    return null;\n  }; // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n    this.updateStatus(false, nextStatus);\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n      var node = _reactDom.default.findDOMNode(this);\n      if (nextStatus === ENTERING) {\n        this.performEnter(node, mounting);\n      } else {\n        this.performExit(node);\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n  _proto.performEnter = function performEnter(node, mounting) {\n    var _this2 = this;\n    var enter = this.props.enter;\n    var appearing = this.context.transitionGroup ? this.context.transitionGroup.isMounting : mounting;\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(node);\n      });\n      return;\n    }\n    this.props.onEnter(node, appearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(node, appearing);\n      _this2.onTransitionEnd(node, enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(node, appearing);\n        });\n      });\n    });\n  };\n  _proto.performExit = function performExit(node) {\n    var _this3 = this;\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts(); // no exit animation skip right to EXITED\n\n    if (!exit) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(node);\n      });\n      return;\n    }\n    this.props.onExit(node);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(node);\n      _this3.onTransitionEnd(node, timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(node);\n        });\n      });\n    });\n  };\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n    var active = true;\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n    return this.nextCallback;\n  };\n  _proto.onTransitionEnd = function onTransitionEnd(node, timeout, handler) {\n    this.setNextCallback(handler);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n    if (this.props.addEndListener) {\n      this.props.addEndListener(node, this.nextCallback);\n    }\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n  _proto.render = function render() {\n    var status = this.state.status;\n    if (status === UNMOUNTED) {\n      return null;\n    }\n    var _this$props = this.props,\n      children = _this$props.children,\n      childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\"]); // filter props for Transtition\n\n    delete childProps.in;\n    delete childProps.mountOnEnter;\n    delete childProps.unmountOnExit;\n    delete childProps.appear;\n    delete childProps.enter;\n    delete childProps.exit;\n    delete childProps.timeout;\n    delete childProps.addEndListener;\n    delete childProps.onEnter;\n    delete childProps.onEntering;\n    delete childProps.onEntered;\n    delete childProps.onExit;\n    delete childProps.onExiting;\n    delete childProps.onExited;\n    if (typeof children === 'function') {\n      return children(status, childProps);\n    }\n    var child = _react.default.Children.only(children);\n    return _react.default.cloneElement(child, childProps);\n  };\n  return Transition;\n}(_react.default.Component);\nTransition.contextTypes = {\n  transitionGroup: PropTypes.object\n};\nTransition.childContextTypes = {\n  transitionGroup: function transitionGroup() {}\n};\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`, `'unmounted'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n  /**\n   * Normally a component is not transitioned if it is shown when the `<Transition>` component mounts.\n   * If you want to transition on the first mount set `appear` to `true`, and the\n   * component will transition in as soon as the `<Transition>` mounts.\n   *\n   * > Note: there are no specific \"appear\" states. `appear` only adds an additional `enter` transition.\n   */\n  appear: PropTypes.bool,\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = _PropTypes.timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return pt.apply(void 0, [props].concat(args));\n  },\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. **Note:** Timeouts are still used as a fallback if provided.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func // Name the function so it is clearer in the documentation\n} : {};\nfunction noop() {}\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = 0;\nTransition.EXITED = 1;\nTransition.ENTERING = 2;\nTransition.ENTERED = 3;\nTransition.EXITING = 4;\nvar _default = (0, _reactLifecyclesCompat.polyfill)(Transition);\nexports.default = _default;", "map": {"version": 3, "names": ["exports", "__esModule", "default", "EXITING", "ENTERED", "ENTERING", "EXITED", "UNMOUNTED", "PropTypes", "_interopRequireWildcard", "require", "_react", "_interopRequireDefault", "_reactDom", "_reactLifecyclesCompat", "_PropTypes", "obj", "newObj", "key", "Object", "prototype", "hasOwnProperty", "call", "desc", "defineProperty", "getOwnPropertyDescriptor", "get", "set", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "keys", "i", "length", "indexOf", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "__proto__", "Transition", "_React$Component", "props", "context", "_this", "parentGroup", "transitionGroup", "appear", "isMounting", "enter", "initialStatus", "appearStatus", "in", "unmountOnExit", "mountOnEnter", "state", "status", "nextCallback", "_proto", "getChildContext", "getDerivedStateFromProps", "_ref", "prevState", "nextIn", "componentDidMount", "updateStatus", "componentDidUpdate", "prevProps", "nextStatus", "componentWillUnmount", "cancelNextCallback", "getTimeouts", "timeout", "exit", "undefined", "mounting", "node", "findDOMNode", "performEnter", "performExit", "setState", "_this2", "appearing", "timeouts", "enterTimeout", "safeSetState", "onEntered", "onEnter", "onEntering", "onTransitionEnd", "_this3", "onExited", "onExit", "onExiting", "cancel", "nextState", "callback", "setNextCallback", "_this4", "active", "event", "handler", "doesNotHaveTimeoutOrListener", "addEndListener", "setTimeout", "render", "_this$props", "children", "childProps", "child", "Children", "only", "cloneElement", "Component", "contextTypes", "object", "childContextTypes", "propTypes", "process", "env", "NODE_ENV", "oneOfType", "func", "isRequired", "element", "bool", "pt", "timeoutsShape", "_len", "arguments", "args", "Array", "_key", "apply", "concat", "noop", "defaultProps", "_default", "polyfill"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-transition-group/Transition.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = exports.EXITING = exports.ENTERED = exports.ENTERING = exports.EXITED = exports.UNMOUNTED = void 0;\n\nvar PropTypes = _interopRequireWildcard(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\n\nvar _reactLifecyclesCompat = require(\"react-lifecycles-compat\");\n\nvar _PropTypes = require(\"./utils/PropTypes\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {}; if (desc.get || desc.set) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } } newObj.default = obj; return newObj; } }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; subClass.__proto__ = superClass; }\n\nvar UNMOUNTED = 'unmounted';\nexports.UNMOUNTED = UNMOUNTED;\nvar EXITED = 'exited';\nexports.EXITED = EXITED;\nvar ENTERING = 'entering';\nexports.ENTERING = ENTERING;\nvar ENTERED = 'entered';\nexports.ENTERED = ENTERED;\nvar EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 0 },\n *   entered:  { opacity: 1 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nexports.EXITING = EXITING;\n\nvar Transition =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n\n  function Transition(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context.transitionGroup; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n\n  var _proto = Transition.prototype;\n\n  _proto.getChildContext = function getChildContext() {\n    return {\n      transitionGroup: null // allows for nested Transitions\n\n    };\n  };\n\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n\n    return null;\n  }; // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n\n    this.updateStatus(false, nextStatus);\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n\n      var node = _reactDom.default.findDOMNode(this);\n\n      if (nextStatus === ENTERING) {\n        this.performEnter(node, mounting);\n      } else {\n        this.performExit(node);\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n\n  _proto.performEnter = function performEnter(node, mounting) {\n    var _this2 = this;\n\n    var enter = this.props.enter;\n    var appearing = this.context.transitionGroup ? this.context.transitionGroup.isMounting : mounting;\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(node);\n      });\n      return;\n    }\n\n    this.props.onEnter(node, appearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(node, appearing);\n\n      _this2.onTransitionEnd(node, enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(node, appearing);\n        });\n      });\n    });\n  };\n\n  _proto.performExit = function performExit(node) {\n    var _this3 = this;\n\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts(); // no exit animation skip right to EXITED\n\n    if (!exit) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(node);\n      });\n      return;\n    }\n\n    this.props.onExit(node);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(node);\n\n      _this3.onTransitionEnd(node, timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(node);\n        });\n      });\n    });\n  };\n\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n\n    var active = true;\n\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n\n    return this.nextCallback;\n  };\n\n  _proto.onTransitionEnd = function onTransitionEnd(node, timeout, handler) {\n    this.setNextCallback(handler);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n\n    if (this.props.addEndListener) {\n      this.props.addEndListener(node, this.nextCallback);\n    }\n\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n\n  _proto.render = function render() {\n    var status = this.state.status;\n\n    if (status === UNMOUNTED) {\n      return null;\n    }\n\n    var _this$props = this.props,\n        children = _this$props.children,\n        childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\"]); // filter props for Transtition\n\n\n    delete childProps.in;\n    delete childProps.mountOnEnter;\n    delete childProps.unmountOnExit;\n    delete childProps.appear;\n    delete childProps.enter;\n    delete childProps.exit;\n    delete childProps.timeout;\n    delete childProps.addEndListener;\n    delete childProps.onEnter;\n    delete childProps.onEntering;\n    delete childProps.onEntered;\n    delete childProps.onExit;\n    delete childProps.onExiting;\n    delete childProps.onExited;\n\n    if (typeof children === 'function') {\n      return children(status, childProps);\n    }\n\n    var child = _react.default.Children.only(children);\n\n    return _react.default.cloneElement(child, childProps);\n  };\n\n  return Transition;\n}(_react.default.Component);\n\nTransition.contextTypes = {\n  transitionGroup: PropTypes.object\n};\nTransition.childContextTypes = {\n  transitionGroup: function transitionGroup() {}\n};\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`, `'unmounted'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n\n  /**\n   * Normally a component is not transitioned if it is shown when the `<Transition>` component mounts.\n   * If you want to transition on the first mount set `appear` to `true`, and the\n   * component will transition in as soon as the `<Transition>` mounts.\n   *\n   * > Note: there are no specific \"appear\" states. `appear` only adds an additional `enter` transition.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = _PropTypes.timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return pt.apply(void 0, [props].concat(args));\n  },\n\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. **Note:** Timeouts are still used as a fallback if provided.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func // Name the function so it is clearer in the documentation\n\n} : {};\n\nfunction noop() {}\n\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = 0;\nTransition.EXITED = 1;\nTransition.ENTERING = 2;\nTransition.ENTERED = 3;\nTransition.EXITING = 4;\n\nvar _default = (0, _reactLifecyclesCompat.polyfill)(Transition);\n\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACK,QAAQ,GAAGL,OAAO,CAACM,MAAM,GAAGN,OAAO,CAACO,SAAS,GAAG,KAAK,CAAC;AAEpH,IAAIC,SAAS,GAAGC,uBAAuB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIC,MAAM,GAAGC,sBAAsB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIG,SAAS,GAAGD,sBAAsB,CAACF,OAAO,CAAC,WAAW,CAAC,CAAC;AAE5D,IAAII,sBAAsB,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAE/D,IAAIK,UAAU,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAE7C,SAASE,sBAAsBA,CAACI,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACf,UAAU,GAAGe,GAAG,GAAG;IAAEd,OAAO,EAAEc;EAAI,CAAC;AAAE;AAE9F,SAASP,uBAAuBA,CAACO,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAIA,GAAG,CAACf,UAAU,EAAE;IAAE,OAAOe,GAAG;EAAE,CAAC,MAAM;IAAE,IAAIC,MAAM,GAAG,CAAC,CAAC;IAAE,IAAID,GAAG,IAAI,IAAI,EAAE;MAAE,KAAK,IAAIE,GAAG,IAAIF,GAAG,EAAE;QAAE,IAAIG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,GAAG,EAAEE,GAAG,CAAC,EAAE;UAAE,IAAIK,IAAI,GAAGJ,MAAM,CAACK,cAAc,IAAIL,MAAM,CAACM,wBAAwB,GAAGN,MAAM,CAACM,wBAAwB,CAACT,GAAG,EAAEE,GAAG,CAAC,GAAG,CAAC,CAAC;UAAE,IAAIK,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,GAAG,EAAE;YAAER,MAAM,CAACK,cAAc,CAACP,MAAM,EAAEC,GAAG,EAAEK,IAAI,CAAC;UAAE,CAAC,MAAM;YAAEN,MAAM,CAACC,GAAG,CAAC,GAAGF,GAAG,CAACE,GAAG,CAAC;UAAE;QAAE;MAAE;IAAE;IAAED,MAAM,CAACf,OAAO,GAAGc,GAAG;IAAE,OAAOC,MAAM;EAAE;AAAE;AAEvd,SAASW,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGb,MAAM,CAACc,IAAI,CAACJ,MAAM,CAAC;EAAE,IAAIX,GAAG,EAAEgB,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAAEhB,GAAG,GAAGc,UAAU,CAACE,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACM,OAAO,CAAClB,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUa,MAAM,CAACb,GAAG,CAAC,GAAGW,MAAM,CAACX,GAAG,CAAC;EAAE;EAAE,OAAOa,MAAM;AAAE;AAElT,SAASM,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAED,QAAQ,CAAClB,SAAS,GAAGD,MAAM,CAACqB,MAAM,CAACD,UAAU,CAACnB,SAAS,CAAC;EAAEkB,QAAQ,CAAClB,SAAS,CAACqB,WAAW,GAAGH,QAAQ;EAAEA,QAAQ,CAACI,SAAS,GAAGH,UAAU;AAAE;AAEtL,IAAIhC,SAAS,GAAG,WAAW;AAC3BP,OAAO,CAACO,SAAS,GAAGA,SAAS;AAC7B,IAAID,MAAM,GAAG,QAAQ;AACrBN,OAAO,CAACM,MAAM,GAAGA,MAAM;AACvB,IAAID,QAAQ,GAAG,UAAU;AACzBL,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3B,IAAID,OAAO,GAAG,SAAS;AACvBJ,OAAO,CAACI,OAAO,GAAGA,OAAO;AACzB,IAAID,OAAO,GAAG,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAH,OAAO,CAACG,OAAO,GAAGA,OAAO;AAEzB,IAAIwC,UAAU,GACd;AACA,UAAUC,gBAAgB,EAAE;EAC1BP,cAAc,CAACM,UAAU,EAAEC,gBAAgB,CAAC;EAE5C,SAASD,UAAUA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAIC,KAAK;IAETA,KAAK,GAAGH,gBAAgB,CAACtB,IAAI,CAAC,IAAI,EAAEuB,KAAK,EAAEC,OAAO,CAAC,IAAI,IAAI;IAC3D,IAAIE,WAAW,GAAGF,OAAO,CAACG,eAAe,CAAC,CAAC;;IAE3C,IAAIC,MAAM,GAAGF,WAAW,IAAI,CAACA,WAAW,CAACG,UAAU,GAAGN,KAAK,CAACO,KAAK,GAAGP,KAAK,CAACK,MAAM;IAChF,IAAIG,aAAa;IACjBN,KAAK,CAACO,YAAY,GAAG,IAAI;IAEzB,IAAIT,KAAK,CAACU,EAAE,EAAE;MACZ,IAAIL,MAAM,EAAE;QACVG,aAAa,GAAG/C,MAAM;QACtByC,KAAK,CAACO,YAAY,GAAGjD,QAAQ;MAC/B,CAAC,MAAM;QACLgD,aAAa,GAAGjD,OAAO;MACzB;IACF,CAAC,MAAM;MACL,IAAIyC,KAAK,CAACW,aAAa,IAAIX,KAAK,CAACY,YAAY,EAAE;QAC7CJ,aAAa,GAAG9C,SAAS;MAC3B,CAAC,MAAM;QACL8C,aAAa,GAAG/C,MAAM;MACxB;IACF;IAEAyC,KAAK,CAACW,KAAK,GAAG;MACZC,MAAM,EAAEN;IACV,CAAC;IACDN,KAAK,CAACa,YAAY,GAAG,IAAI;IACzB,OAAOb,KAAK;EACd;EAEA,IAAIc,MAAM,GAAGlB,UAAU,CAACvB,SAAS;EAEjCyC,MAAM,CAACC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAClD,OAAO;MACLb,eAAe,EAAE,IAAI,CAAC;IAExB,CAAC;EACH,CAAC;EAEDN,UAAU,CAACoB,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACvF,IAAIC,MAAM,GAAGF,IAAI,CAACT,EAAE;IAEpB,IAAIW,MAAM,IAAID,SAAS,CAACN,MAAM,KAAKpD,SAAS,EAAE;MAC5C,OAAO;QACLoD,MAAM,EAAErD;MACV,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,CAAC;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGAuD,MAAM,CAACM,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,IAAI,CAACC,YAAY,CAAC,IAAI,EAAE,IAAI,CAACd,YAAY,CAAC;EAC5C,CAAC;EAEDO,MAAM,CAACQ,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAE;IACjE,IAAIC,UAAU,GAAG,IAAI;IAErB,IAAID,SAAS,KAAK,IAAI,CAACzB,KAAK,EAAE;MAC5B,IAAIc,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM;MAE9B,IAAI,IAAI,CAACd,KAAK,CAACU,EAAE,EAAE;QACjB,IAAII,MAAM,KAAKtD,QAAQ,IAAIsD,MAAM,KAAKvD,OAAO,EAAE;UAC7CmE,UAAU,GAAGlE,QAAQ;QACvB;MACF,CAAC,MAAM;QACL,IAAIsD,MAAM,KAAKtD,QAAQ,IAAIsD,MAAM,KAAKvD,OAAO,EAAE;UAC7CmE,UAAU,GAAGpE,OAAO;QACtB;MACF;IACF;IAEA,IAAI,CAACiE,YAAY,CAAC,KAAK,EAAEG,UAAU,CAAC;EACtC,CAAC;EAEDV,MAAM,CAACW,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5D,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B,CAAC;EAEDZ,MAAM,CAACa,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAIC,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAAC8B,OAAO;IAChC,IAAIC,IAAI,EAAExB,KAAK,EAAEF,MAAM;IACvB0B,IAAI,GAAGxB,KAAK,GAAGF,MAAM,GAAGyB,OAAO;IAE/B,IAAIA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAClDC,IAAI,GAAGD,OAAO,CAACC,IAAI;MACnBxB,KAAK,GAAGuB,OAAO,CAACvB,KAAK,CAAC,CAAC;;MAEvBF,MAAM,GAAGyB,OAAO,CAACzB,MAAM,KAAK2B,SAAS,GAAGF,OAAO,CAACzB,MAAM,GAAGE,KAAK;IAChE;IAEA,OAAO;MACLwB,IAAI,EAAEA,IAAI;MACVxB,KAAK,EAAEA,KAAK;MACZF,MAAM,EAAEA;IACV,CAAC;EACH,CAAC;EAEDW,MAAM,CAACO,YAAY,GAAG,SAASA,YAAYA,CAACU,QAAQ,EAAEP,UAAU,EAAE;IAChE,IAAIO,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAG,KAAK;IAClB;IAEA,IAAIP,UAAU,KAAK,IAAI,EAAE;MACvB;MACA,IAAI,CAACE,kBAAkB,CAAC,CAAC;MAEzB,IAAIM,IAAI,GAAGlE,SAAS,CAACX,OAAO,CAAC8E,WAAW,CAAC,IAAI,CAAC;MAE9C,IAAIT,UAAU,KAAKlE,QAAQ,EAAE;QAC3B,IAAI,CAAC4E,YAAY,CAACF,IAAI,EAAED,QAAQ,CAAC;MACnC,CAAC,MAAM;QACL,IAAI,CAACI,WAAW,CAACH,IAAI,CAAC;MACxB;IACF,CAAC,MAAM,IAAI,IAAI,CAAClC,KAAK,CAACW,aAAa,IAAI,IAAI,CAACE,KAAK,CAACC,MAAM,KAAKrD,MAAM,EAAE;MACnE,IAAI,CAAC6E,QAAQ,CAAC;QACZxB,MAAM,EAAEpD;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAEDsD,MAAM,CAACoB,YAAY,GAAG,SAASA,YAAYA,CAACF,IAAI,EAAED,QAAQ,EAAE;IAC1D,IAAIM,MAAM,GAAG,IAAI;IAEjB,IAAIhC,KAAK,GAAG,IAAI,CAACP,KAAK,CAACO,KAAK;IAC5B,IAAIiC,SAAS,GAAG,IAAI,CAACvC,OAAO,CAACG,eAAe,GAAG,IAAI,CAACH,OAAO,CAACG,eAAe,CAACE,UAAU,GAAG2B,QAAQ;IACjG,IAAIQ,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAAC,CAAC;IACjC,IAAIa,YAAY,GAAGF,SAAS,GAAGC,QAAQ,CAACpC,MAAM,GAAGoC,QAAQ,CAAClC,KAAK,CAAC,CAAC;IACjE;;IAEA,IAAI,CAAC0B,QAAQ,IAAI,CAAC1B,KAAK,EAAE;MACvB,IAAI,CAACoC,YAAY,CAAC;QAChB7B,MAAM,EAAEvD;MACV,CAAC,EAAE,YAAY;QACbgF,MAAM,CAACvC,KAAK,CAAC4C,SAAS,CAACV,IAAI,CAAC;MAC9B,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAClC,KAAK,CAAC6C,OAAO,CAACX,IAAI,EAAEM,SAAS,CAAC;IACnC,IAAI,CAACG,YAAY,CAAC;MAChB7B,MAAM,EAAEtD;IACV,CAAC,EAAE,YAAY;MACb+E,MAAM,CAACvC,KAAK,CAAC8C,UAAU,CAACZ,IAAI,EAAEM,SAAS,CAAC;MAExCD,MAAM,CAACQ,eAAe,CAACb,IAAI,EAAEQ,YAAY,EAAE,YAAY;QACrDH,MAAM,CAACI,YAAY,CAAC;UAClB7B,MAAM,EAAEvD;QACV,CAAC,EAAE,YAAY;UACbgF,MAAM,CAACvC,KAAK,CAAC4C,SAAS,CAACV,IAAI,EAAEM,SAAS,CAAC;QACzC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDxB,MAAM,CAACqB,WAAW,GAAG,SAASA,WAAWA,CAACH,IAAI,EAAE;IAC9C,IAAIc,MAAM,GAAG,IAAI;IAEjB,IAAIjB,IAAI,GAAG,IAAI,CAAC/B,KAAK,CAAC+B,IAAI;IAC1B,IAAIU,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC,CAAC;;IAEnC,IAAI,CAACE,IAAI,EAAE;MACT,IAAI,CAACY,YAAY,CAAC;QAChB7B,MAAM,EAAErD;MACV,CAAC,EAAE,YAAY;QACbuF,MAAM,CAAChD,KAAK,CAACiD,QAAQ,CAACf,IAAI,CAAC;MAC7B,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAClC,KAAK,CAACkD,MAAM,CAAChB,IAAI,CAAC;IACvB,IAAI,CAACS,YAAY,CAAC;MAChB7B,MAAM,EAAExD;IACV,CAAC,EAAE,YAAY;MACb0F,MAAM,CAAChD,KAAK,CAACmD,SAAS,CAACjB,IAAI,CAAC;MAE5Bc,MAAM,CAACD,eAAe,CAACb,IAAI,EAAEO,QAAQ,CAACV,IAAI,EAAE,YAAY;QACtDiB,MAAM,CAACL,YAAY,CAAC;UAClB7B,MAAM,EAAErD;QACV,CAAC,EAAE,YAAY;UACbuF,MAAM,CAAChD,KAAK,CAACiD,QAAQ,CAACf,IAAI,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDlB,MAAM,CAACY,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACxD,IAAI,IAAI,CAACb,YAAY,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,YAAY,CAACqC,MAAM,CAAC,CAAC;MAC1B,IAAI,CAACrC,YAAY,GAAG,IAAI;IAC1B;EACF,CAAC;EAEDC,MAAM,CAAC2B,YAAY,GAAG,SAASA,YAAYA,CAACU,SAAS,EAAEC,QAAQ,EAAE;IAC/D;IACA;IACA;IACAA,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACD,QAAQ,CAAC;IACzC,IAAI,CAAChB,QAAQ,CAACe,SAAS,EAAEC,QAAQ,CAAC;EACpC,CAAC;EAEDtC,MAAM,CAACuC,eAAe,GAAG,SAASA,eAAeA,CAACD,QAAQ,EAAE;IAC1D,IAAIE,MAAM,GAAG,IAAI;IAEjB,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAAC1C,YAAY,GAAG,UAAU2C,KAAK,EAAE;MACnC,IAAID,MAAM,EAAE;QACVA,MAAM,GAAG,KAAK;QACdD,MAAM,CAACzC,YAAY,GAAG,IAAI;QAC1BuC,QAAQ,CAACI,KAAK,CAAC;MACjB;IACF,CAAC;IAED,IAAI,CAAC3C,YAAY,CAACqC,MAAM,GAAG,YAAY;MACrCK,MAAM,GAAG,KAAK;IAChB,CAAC;IAED,OAAO,IAAI,CAAC1C,YAAY;EAC1B,CAAC;EAEDC,MAAM,CAAC+B,eAAe,GAAG,SAASA,eAAeA,CAACb,IAAI,EAAEJ,OAAO,EAAE6B,OAAO,EAAE;IACxE,IAAI,CAACJ,eAAe,CAACI,OAAO,CAAC;IAC7B,IAAIC,4BAA4B,GAAG9B,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC9B,KAAK,CAAC6D,cAAc;IAEhF,IAAI,CAAC3B,IAAI,IAAI0B,4BAA4B,EAAE;MACzCE,UAAU,CAAC,IAAI,CAAC/C,YAAY,EAAE,CAAC,CAAC;MAChC;IACF;IAEA,IAAI,IAAI,CAACf,KAAK,CAAC6D,cAAc,EAAE;MAC7B,IAAI,CAAC7D,KAAK,CAAC6D,cAAc,CAAC3B,IAAI,EAAE,IAAI,CAACnB,YAAY,CAAC;IACpD;IAEA,IAAIe,OAAO,IAAI,IAAI,EAAE;MACnBgC,UAAU,CAAC,IAAI,CAAC/C,YAAY,EAAEe,OAAO,CAAC;IACxC;EACF,CAAC;EAEDd,MAAM,CAAC+C,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIjD,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM;IAE9B,IAAIA,MAAM,KAAKpD,SAAS,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,IAAIsG,WAAW,GAAG,IAAI,CAAChE,KAAK;MACxBiE,QAAQ,GAAGD,WAAW,CAACC,QAAQ;MAC/BC,UAAU,GAAGnF,6BAA6B,CAACiF,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;IAG3E,OAAOE,UAAU,CAACxD,EAAE;IACpB,OAAOwD,UAAU,CAACtD,YAAY;IAC9B,OAAOsD,UAAU,CAACvD,aAAa;IAC/B,OAAOuD,UAAU,CAAC7D,MAAM;IACxB,OAAO6D,UAAU,CAAC3D,KAAK;IACvB,OAAO2D,UAAU,CAACnC,IAAI;IACtB,OAAOmC,UAAU,CAACpC,OAAO;IACzB,OAAOoC,UAAU,CAACL,cAAc;IAChC,OAAOK,UAAU,CAACrB,OAAO;IACzB,OAAOqB,UAAU,CAACpB,UAAU;IAC5B,OAAOoB,UAAU,CAACtB,SAAS;IAC3B,OAAOsB,UAAU,CAAChB,MAAM;IACxB,OAAOgB,UAAU,CAACf,SAAS;IAC3B,OAAOe,UAAU,CAACjB,QAAQ;IAE1B,IAAI,OAAOgB,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACnD,MAAM,EAAEoD,UAAU,CAAC;IACrC;IAEA,IAAIC,KAAK,GAAGrG,MAAM,CAACT,OAAO,CAAC+G,QAAQ,CAACC,IAAI,CAACJ,QAAQ,CAAC;IAElD,OAAOnG,MAAM,CAACT,OAAO,CAACiH,YAAY,CAACH,KAAK,EAAED,UAAU,CAAC;EACvD,CAAC;EAED,OAAOpE,UAAU;AACnB,CAAC,CAAChC,MAAM,CAACT,OAAO,CAACkH,SAAS,CAAC;AAE3BzE,UAAU,CAAC0E,YAAY,GAAG;EACxBpE,eAAe,EAAEzC,SAAS,CAAC8G;AAC7B,CAAC;AACD3E,UAAU,CAAC4E,iBAAiB,GAAG;EAC7BtE,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG,CAAC;AAC/C,CAAC;AACDN,UAAU,CAAC6E,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC7D;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEb,QAAQ,EAAEtG,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAACqH,IAAI,CAACC,UAAU,EAAEtH,SAAS,CAACuH,OAAO,CAACD,UAAU,CAAC,CAAC,CAACA,UAAU;EAEnG;AACF;AACA;EACEvE,EAAE,EAAE/C,SAAS,CAACwH,IAAI;EAElB;AACF;AACA;AACA;AACA;AACA;EACEvE,YAAY,EAAEjD,SAAS,CAACwH,IAAI;EAE5B;AACF;AACA;AACA;EACExE,aAAa,EAAEhD,SAAS,CAACwH,IAAI;EAE7B;AACF;AACA;AACA;AACA;AACA;AACA;EACE9E,MAAM,EAAE1C,SAAS,CAACwH,IAAI;EAEtB;AACF;AACA;EACE5E,KAAK,EAAE5C,SAAS,CAACwH,IAAI;EAErB;AACF;AACA;EACEpD,IAAI,EAAEpE,SAAS,CAACwH,IAAI;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErD,OAAO,EAAE,SAASA,OAAOA,CAAC9B,KAAK,EAAE;IAC/B,IAAIoF,EAAE,GAAGlH,UAAU,CAACmH,aAAa;IACjC,IAAI,CAACrF,KAAK,CAAC6D,cAAc,EAAEuB,EAAE,GAAGA,EAAE,CAACH,UAAU;IAE7C,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACjG,MAAM,EAAEkG,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAClC;IAEA,OAAON,EAAE,CAACO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC3F,KAAK,CAAC,CAAC4F,MAAM,CAACJ,IAAI,CAAC,CAAC;EAC/C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3B,cAAc,EAAElG,SAAS,CAACqH,IAAI;EAE9B;AACF;AACA;AACA;AACA;AACA;EACEnC,OAAO,EAAElF,SAAS,CAACqH,IAAI;EAEvB;AACF;AACA;AACA;AACA;AACA;EACElC,UAAU,EAAEnF,SAAS,CAACqH,IAAI;EAE1B;AACF;AACA;AACA;AACA;AACA;EACEpC,SAAS,EAAEjF,SAAS,CAACqH,IAAI;EAEzB;AACF;AACA;AACA;AACA;EACE9B,MAAM,EAAEvF,SAAS,CAACqH,IAAI;EAEtB;AACF;AACA;AACA;AACA;EACE7B,SAAS,EAAExF,SAAS,CAACqH,IAAI;EAEzB;AACF;AACA;AACA;AACA;EACE/B,QAAQ,EAAEtF,SAAS,CAACqH,IAAI,CAAC;AAE3B,CAAC,GAAG,CAAC,CAAC;AAEN,SAASa,IAAIA,CAAA,EAAG,CAAC;AAEjB/F,UAAU,CAACgG,YAAY,GAAG;EACxBpF,EAAE,EAAE,KAAK;EACTE,YAAY,EAAE,KAAK;EACnBD,aAAa,EAAE,KAAK;EACpBN,MAAM,EAAE,KAAK;EACbE,KAAK,EAAE,IAAI;EACXwB,IAAI,EAAE,IAAI;EACVc,OAAO,EAAEgD,IAAI;EACb/C,UAAU,EAAE+C,IAAI;EAChBjD,SAAS,EAAEiD,IAAI;EACf3C,MAAM,EAAE2C,IAAI;EACZ1C,SAAS,EAAE0C,IAAI;EACf5C,QAAQ,EAAE4C;AACZ,CAAC;AACD/F,UAAU,CAACpC,SAAS,GAAG,CAAC;AACxBoC,UAAU,CAACrC,MAAM,GAAG,CAAC;AACrBqC,UAAU,CAACtC,QAAQ,GAAG,CAAC;AACvBsC,UAAU,CAACvC,OAAO,GAAG,CAAC;AACtBuC,UAAU,CAACxC,OAAO,GAAG,CAAC;AAEtB,IAAIyI,QAAQ,GAAG,CAAC,CAAC,EAAE9H,sBAAsB,CAAC+H,QAAQ,EAAElG,UAAU,CAAC;AAE/D3C,OAAO,CAACE,OAAO,GAAG0I,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}