{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { tuple } from '../../_util/type';\nimport PickerButton from '../PickerButton';\nimport PickerTag from '../PickerTag';\nimport generateRangePicker from './generateRangePicker';\nimport generateSinglePicker from './generateSinglePicker';\nexport var Components = {\n  button: PickerButton,\n  rangeItem: PickerTag\n};\nfunction toArray(list) {\n  if (!list) {\n    return [];\n  }\n  return Array.isArray(list) ? list : [list];\n}\nexport function getTimeProps(props) {\n  var format = props.format,\n    picker = props.picker,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    use12Hours = props.use12Hours;\n  var firstFormat = toArray(format)[0];\n  var showTimeObj = _extends({}, props);\n  if (firstFormat && typeof firstFormat === 'string') {\n    if (!firstFormat.includes('s') && showSecond === undefined) {\n      showTimeObj.showSecond = false;\n    }\n    if (!firstFormat.includes('m') && showMinute === undefined) {\n      showTimeObj.showMinute = false;\n    }\n    if (!firstFormat.includes('H') && !firstFormat.includes('h') && showHour === undefined) {\n      showTimeObj.showHour = false;\n    }\n    if ((firstFormat.includes('a') || firstFormat.includes('A')) && use12Hours === undefined) {\n      showTimeObj.use12Hours = true;\n    }\n  }\n  if (picker === 'time') {\n    return showTimeObj;\n  }\n  if (typeof firstFormat === 'function') {\n    // format of showTime should use default when format is custom format function\n    delete showTimeObj.format;\n  }\n  return {\n    showTime: showTimeObj\n  };\n}\nvar DataPickerPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\nfunction generatePicker(generateConfig) {\n  // =========================== Picker ===========================\n  var _generateSinglePicker = generateSinglePicker(generateConfig),\n    DatePicker = _generateSinglePicker.DatePicker,\n    WeekPicker = _generateSinglePicker.WeekPicker,\n    MonthPicker = _generateSinglePicker.MonthPicker,\n    YearPicker = _generateSinglePicker.YearPicker,\n    TimePicker = _generateSinglePicker.TimePicker,\n    QuarterPicker = _generateSinglePicker.QuarterPicker;\n  // ======================== Range Picker ========================\n  var RangePicker = generateRangePicker(generateConfig);\n  var MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  return MergedDatePicker;\n}\nexport default generatePicker;", "map": {"version": 3, "names": ["_extends", "tuple", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PickerTag", "generateRangePicker", "generateSinglePicker", "Components", "button", "rangeItem", "toArray", "list", "Array", "isArray", "getTimeProps", "props", "format", "picker", "showHour", "showMinute", "showSecond", "use12Hours", "firstFormat", "showTimeObj", "includes", "undefined", "showTime", "DataPickerPlacements", "generatePicker", "generateConfig", "_generateSinglePicker", "DatePicker", "WeekPicker", "MonthPicker", "YearPicker", "TimePicker", "QuarterPicker", "RangePicker", "MergedDatePicker"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/date-picker/generatePicker/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { tuple } from '../../_util/type';\nimport PickerButton from '../PickerButton';\nimport PickerTag from '../PickerTag';\nimport generateRangePicker from './generateRangePicker';\nimport generateSinglePicker from './generateSinglePicker';\nexport var Components = {\n  button: PickerButton,\n  rangeItem: PickerTag\n};\nfunction toArray(list) {\n  if (!list) {\n    return [];\n  }\n  return Array.isArray(list) ? list : [list];\n}\nexport function getTimeProps(props) {\n  var format = props.format,\n    picker = props.picker,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    use12Hours = props.use12Hours;\n  var firstFormat = toArray(format)[0];\n  var showTimeObj = _extends({}, props);\n  if (firstFormat && typeof firstFormat === 'string') {\n    if (!firstFormat.includes('s') && showSecond === undefined) {\n      showTimeObj.showSecond = false;\n    }\n    if (!firstFormat.includes('m') && showMinute === undefined) {\n      showTimeObj.showMinute = false;\n    }\n    if (!firstFormat.includes('H') && !firstFormat.includes('h') && showHour === undefined) {\n      showTimeObj.showHour = false;\n    }\n    if ((firstFormat.includes('a') || firstFormat.includes('A')) && use12Hours === undefined) {\n      showTimeObj.use12Hours = true;\n    }\n  }\n  if (picker === 'time') {\n    return showTimeObj;\n  }\n  if (typeof firstFormat === 'function') {\n    // format of showTime should use default when format is custom format function\n    delete showTimeObj.format;\n  }\n  return {\n    showTime: showTimeObj\n  };\n}\nvar DataPickerPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\nfunction generatePicker(generateConfig) {\n  // =========================== Picker ===========================\n  var _generateSinglePicker = generateSinglePicker(generateConfig),\n    DatePicker = _generateSinglePicker.DatePicker,\n    WeekPicker = _generateSinglePicker.WeekPicker,\n    MonthPicker = _generateSinglePicker.MonthPicker,\n    YearPicker = _generateSinglePicker.YearPicker,\n    TimePicker = _generateSinglePicker.TimePicker,\n    QuarterPicker = _generateSinglePicker.QuarterPicker;\n  // ======================== Range Picker ========================\n  var RangePicker = generateRangePicker(generateConfig);\n  var MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  return MergedDatePicker;\n}\nexport default generatePicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAO,IAAIC,UAAU,GAAG;EACtBC,MAAM,EAAEL,YAAY;EACpBM,SAAS,EAAEL;AACb,CAAC;AACD,SAASM,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC5C;AACA,OAAO,SAASG,YAAYA,CAACC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,UAAU,GAAGN,KAAK,CAACM,UAAU;EAC/B,IAAIC,WAAW,GAAGZ,OAAO,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,IAAIO,WAAW,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,CAAC;EACrC,IAAIO,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAClD,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIJ,UAAU,KAAKK,SAAS,EAAE;MAC1DF,WAAW,CAACH,UAAU,GAAG,KAAK;IAChC;IACA,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIL,UAAU,KAAKM,SAAS,EAAE;MAC1DF,WAAW,CAACJ,UAAU,GAAG,KAAK;IAChC;IACA,IAAI,CAACG,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIN,QAAQ,KAAKO,SAAS,EAAE;MACtFF,WAAW,CAACL,QAAQ,GAAG,KAAK;IAC9B;IACA,IAAI,CAACI,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,KAAKH,UAAU,KAAKI,SAAS,EAAE;MACxFF,WAAW,CAACF,UAAU,GAAG,IAAI;IAC/B;EACF;EACA,IAAIJ,MAAM,KAAK,MAAM,EAAE;IACrB,OAAOM,WAAW;EACpB;EACA,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;IACrC;IACA,OAAOC,WAAW,CAACP,MAAM;EAC3B;EACA,OAAO;IACLU,QAAQ,EAAEH;EACZ,CAAC;AACH;AACA,IAAII,oBAAoB,GAAGzB,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;AACpF,SAAS0B,cAAcA,CAACC,cAAc,EAAE;EACtC;EACA,IAAIC,qBAAqB,GAAGxB,oBAAoB,CAACuB,cAAc,CAAC;IAC9DE,UAAU,GAAGD,qBAAqB,CAACC,UAAU;IAC7CC,UAAU,GAAGF,qBAAqB,CAACE,UAAU;IAC7CC,WAAW,GAAGH,qBAAqB,CAACG,WAAW;IAC/CC,UAAU,GAAGJ,qBAAqB,CAACI,UAAU;IAC7CC,UAAU,GAAGL,qBAAqB,CAACK,UAAU;IAC7CC,aAAa,GAAGN,qBAAqB,CAACM,aAAa;EACrD;EACA,IAAIC,WAAW,GAAGhC,mBAAmB,CAACwB,cAAc,CAAC;EACrD,IAAIS,gBAAgB,GAAGP,UAAU;EACjCO,gBAAgB,CAACN,UAAU,GAAGA,UAAU;EACxCM,gBAAgB,CAACL,WAAW,GAAGA,WAAW;EAC1CK,gBAAgB,CAACJ,UAAU,GAAGA,UAAU;EACxCI,gBAAgB,CAACD,WAAW,GAAGA,WAAW;EAC1CC,gBAAgB,CAACH,UAAU,GAAGA,UAAU;EACxCG,gBAAgB,CAACF,aAAa,GAAGA,aAAa;EAC9C,OAAOE,gBAAgB;AACzB;AACA,eAAeV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}