{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ascendingDefined = ascendingDefined;\nexports.compareDefined = compareDefined;\nexports.default = sort;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _permute = _interopRequireDefault(require(\"./permute.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if (f && f.length !== 2 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return (0, _permute.default)(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\nfunction compareDefined(compare = _ascending.default) {\n  if (compare === _ascending.default) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\nfunction ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ascendingDefined", "compareDefined", "default", "sort", "_ascending", "_interopRequireDefault", "require", "_permute", "obj", "__esModule", "values", "F", "Symbol", "iterator", "TypeError", "Array", "from", "f", "length", "index", "Uint32Array", "d", "i", "map", "j", "c", "compare", "a", "b", "x"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/sort.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ascendingDefined = ascendingDefined;\nexports.compareDefined = compareDefined;\nexports.default = sort;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _permute = _interopRequireDefault(require(\"./permute.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n\n  if (f && f.length !== 2 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n\n    return (0, _permute.default)(values, index);\n  }\n\n  return values.sort(compareDefined(f));\n}\n\nfunction compareDefined(compare = _ascending.default) {\n  if (compare === _ascending.default) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nfunction ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvCH,OAAO,CAACI,OAAO,GAAGC,IAAI;AAEtB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,QAAQ,GAAGF,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,IAAIA,CAACO,MAAM,EAAE,GAAGC,CAAC,EAAE;EAC1B,IAAI,OAAOD,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChGJ,MAAM,GAAGK,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC;EAC3B,IAAI,CAACO,CAAC,CAAC,GAAGN,CAAC;EAEX,IAAIM,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,IAAIP,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;IACvC,MAAMC,KAAK,GAAGC,WAAW,CAACJ,IAAI,CAACN,MAAM,EAAE,CAACW,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAEnD,IAAIX,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;MAChBP,CAAC,GAAGA,CAAC,CAACY,GAAG,CAACN,CAAC,IAAIP,MAAM,CAACa,GAAG,CAACN,CAAC,CAAC,CAAC;MAC7BE,KAAK,CAAChB,IAAI,CAAC,CAACmB,CAAC,EAAEE,CAAC,KAAK;QACnB,KAAK,MAAMP,CAAC,IAAIN,CAAC,EAAE;UACjB,MAAMc,CAAC,GAAGzB,gBAAgB,CAACiB,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAACO,CAAC,CAAC,CAAC;UACtC,IAAIC,CAAC,EAAE,OAAOA,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLR,CAAC,GAAGP,MAAM,CAACa,GAAG,CAACN,CAAC,CAAC;MACjBE,KAAK,CAAChB,IAAI,CAAC,CAACmB,CAAC,EAAEE,CAAC,KAAKxB,gBAAgB,CAACiB,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAACO,CAAC,CAAC,CAAC,CAAC;IACpD;IAEA,OAAO,CAAC,CAAC,EAAEjB,QAAQ,CAACL,OAAO,EAAEQ,MAAM,EAAES,KAAK,CAAC;EAC7C;EAEA,OAAOT,MAAM,CAACP,IAAI,CAACF,cAAc,CAACgB,CAAC,CAAC,CAAC;AACvC;AAEA,SAAShB,cAAcA,CAACyB,OAAO,GAAGtB,UAAU,CAACF,OAAO,EAAE;EACpD,IAAIwB,OAAO,KAAKtB,UAAU,CAACF,OAAO,EAAE,OAAOF,gBAAgB;EAC3D,IAAI,OAAO0B,OAAO,KAAK,UAAU,EAAE,MAAM,IAAIZ,SAAS,CAAC,2BAA2B,CAAC;EACnF,OAAO,CAACa,CAAC,EAAEC,CAAC,KAAK;IACf,MAAMC,CAAC,GAAGH,OAAO,CAACC,CAAC,EAAEC,CAAC,CAAC;IACvB,IAAIC,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE,OAAOA,CAAC;IAC1B,OAAO,CAACH,OAAO,CAACE,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,KAAKF,OAAO,CAACC,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,CAAC;EACtD,CAAC;AACH;AAEA,SAAS3B,gBAAgBA,CAAC2B,CAAC,EAAEC,CAAC,EAAE;EAC9B,OAAO,CAACD,CAAC,IAAI,IAAI,IAAI,EAAEA,CAAC,IAAIA,CAAC,CAAC,KAAKC,CAAC,IAAI,IAAI,IAAI,EAAEA,CAAC,IAAIA,CAAC,CAAC,CAAC,KAAKD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "script"}