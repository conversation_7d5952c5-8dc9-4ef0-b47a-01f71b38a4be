{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.initInterpolator = initInterpolator;\nexports.initRange = initRange;\nfunction initRange(domain, range) {\n  switch (arguments.length) {\n    case 0:\n      break;\n    case 1:\n      this.range(domain);\n      break;\n    default:\n      this.range(range).domain(domain);\n      break;\n  }\n  return this;\n}\nfunction initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0:\n      break;\n    case 1:\n      {\n        if (typeof domain === \"function\") this.interpolator(domain);else this.range(domain);\n        break;\n      }\n    default:\n      {\n        this.domain(domain);\n        if (typeof interpolator === \"function\") this.interpolator(interpolator);else this.range(interpolator);\n        break;\n      }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "initInterpolator", "initRange", "domain", "range", "arguments", "length", "interpolator"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/init.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.initInterpolator = initInterpolator;\nexports.initRange = initRange;\n\nfunction initRange(domain, range) {\n  switch (arguments.length) {\n    case 0:\n      break;\n\n    case 1:\n      this.range(domain);\n      break;\n\n    default:\n      this.range(range).domain(domain);\n      break;\n  }\n\n  return this;\n}\n\nfunction initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0:\n      break;\n\n    case 1:\n      {\n        if (typeof domain === \"function\") this.interpolator(domain);else this.range(domain);\n        break;\n      }\n\n    default:\n      {\n        this.domain(domain);\n        if (typeof interpolator === \"function\") this.interpolator(interpolator);else this.range(interpolator);\n        break;\n      }\n  }\n\n  return this;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACG,SAAS,GAAGA,SAAS;AAE7B,SAASA,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAChC,QAAQC,SAAS,CAACC,MAAM;IACtB,KAAK,CAAC;MACJ;IAEF,KAAK,CAAC;MACJ,IAAI,CAACF,KAAK,CAACD,MAAM,CAAC;MAClB;IAEF;MACE,IAAI,CAACC,KAAK,CAACA,KAAK,CAAC,CAACD,MAAM,CAACA,MAAM,CAAC;MAChC;EACJ;EAEA,OAAO,IAAI;AACb;AAEA,SAASF,gBAAgBA,CAACE,MAAM,EAAEI,YAAY,EAAE;EAC9C,QAAQF,SAAS,CAACC,MAAM;IACtB,KAAK,CAAC;MACJ;IAEF,KAAK,CAAC;MACJ;QACE,IAAI,OAAOH,MAAM,KAAK,UAAU,EAAE,IAAI,CAACI,YAAY,CAACJ,MAAM,CAAC,CAAC,KAAK,IAAI,CAACC,KAAK,CAACD,MAAM,CAAC;QACnF;MACF;IAEF;MACE;QACE,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;QACnB,IAAI,OAAOI,YAAY,KAAK,UAAU,EAAE,IAAI,CAACA,YAAY,CAACA,YAAY,CAAC,CAAC,KAAK,IAAI,CAACH,KAAK,CAACG,YAAY,CAAC;QACrG;MACF;EACJ;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script"}