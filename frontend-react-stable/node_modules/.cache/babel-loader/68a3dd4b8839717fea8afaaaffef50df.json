{"ast": null, "code": "import addEventListener from \"rc-util/es/Dom/addEventListener\";\nexport function getTargetRect(target) {\n  return target !== window ? target.getBoundingClientRect() : {\n    top: 0,\n    bottom: window.innerHeight\n  };\n}\nexport function getFixedTop(placeholderReact, targetRect, offsetTop) {\n  if (offsetTop !== undefined && targetRect.top > placeholderReact.top - offsetTop) {\n    return offsetTop + targetRect.top;\n  }\n  return undefined;\n}\nexport function getFixedBottom(placeholderReact, targetRect, offsetBottom) {\n  if (offsetBottom !== undefined && targetRect.bottom < placeholderReact.bottom + offsetBottom) {\n    var targetBottomOffset = window.innerHeight - targetRect.bottom;\n    return offsetBottom + targetBottomOffset;\n  }\n  return undefined;\n}\n// ======================== Observer ========================\nvar TRIGGER_EVENTS = ['resize', 'scroll', 'touchstart', 'touchmove', 'touchend', 'pageshow', 'load'];\nvar observerEntities = [];\nexport function getObserverEntities() {\n  // Only used in test env. Can be removed if refactor.\n  return observerEntities;\n}\nexport function addObserveTarget(target, affix) {\n  if (!target) {\n    return;\n  }\n  var entity = observerEntities.find(function (item) {\n    return item.target === target;\n  });\n  if (entity) {\n    entity.affixList.push(affix);\n  } else {\n    entity = {\n      target: target,\n      affixList: [affix],\n      eventHandlers: {}\n    };\n    observerEntities.push(entity);\n    // Add listener\n    TRIGGER_EVENTS.forEach(function (eventName) {\n      entity.eventHandlers[eventName] = addEventListener(target, eventName, function () {\n        entity.affixList.forEach(function (targetAffix) {\n          targetAffix.lazyUpdatePosition();\n        });\n      });\n    });\n  }\n}\nexport function removeObserveTarget(affix) {\n  var observerEntity = observerEntities.find(function (oriObserverEntity) {\n    var hasAffix = oriObserverEntity.affixList.some(function (item) {\n      return item === affix;\n    });\n    if (hasAffix) {\n      oriObserverEntity.affixList = oriObserverEntity.affixList.filter(function (item) {\n        return item !== affix;\n      });\n    }\n    return hasAffix;\n  });\n  if (observerEntity && observerEntity.affixList.length === 0) {\n    observerEntities = observerEntities.filter(function (item) {\n      return item !== observerEntity;\n    });\n    // Remove listener\n    TRIGGER_EVENTS.forEach(function (eventName) {\n      var handler = observerEntity.eventHandlers[eventName];\n      if (handler && handler.remove) {\n        handler.remove();\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["addEventListener", "getTargetRect", "target", "window", "getBoundingClientRect", "top", "bottom", "innerHeight", "getFixedTop", "placeholder<PERSON><PERSON><PERSON>", "targetRect", "offsetTop", "undefined", "getFixedBottom", "offsetBottom", "targetBottomOffset", "TRIGGER_EVENTS", "observerEntities", "getObserverEntities", "addObserveTarget", "affix", "entity", "find", "item", "affixList", "push", "eventHandlers", "for<PERSON>ach", "eventName", "targetAffix", "lazyUpdatePosition", "removeObserveTarget", "observerEntity", "oriObserverEntity", "hasAffix", "some", "filter", "length", "handler", "remove"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/affix/utils.js"], "sourcesContent": ["import addEventListener from \"rc-util/es/Dom/addEventListener\";\nexport function getTargetRect(target) {\n  return target !== window ? target.getBoundingClientRect() : {\n    top: 0,\n    bottom: window.innerHeight\n  };\n}\nexport function getFixedTop(placeholderReact, targetRect, offsetTop) {\n  if (offsetTop !== undefined && targetRect.top > placeholderReact.top - offsetTop) {\n    return offsetTop + targetRect.top;\n  }\n  return undefined;\n}\nexport function getFixedBottom(placeholderReact, targetRect, offsetBottom) {\n  if (offsetBottom !== undefined && targetRect.bottom < placeholderReact.bottom + offsetBottom) {\n    var targetBottomOffset = window.innerHeight - targetRect.bottom;\n    return offsetBottom + targetBottomOffset;\n  }\n  return undefined;\n}\n// ======================== Observer ========================\nvar TRIGGER_EVENTS = ['resize', 'scroll', 'touchstart', 'touchmove', 'touchend', 'pageshow', 'load'];\nvar observerEntities = [];\nexport function getObserverEntities() {\n  // Only used in test env. Can be removed if refactor.\n  return observerEntities;\n}\nexport function addObserveTarget(target, affix) {\n  if (!target) {\n    return;\n  }\n  var entity = observerEntities.find(function (item) {\n    return item.target === target;\n  });\n  if (entity) {\n    entity.affixList.push(affix);\n  } else {\n    entity = {\n      target: target,\n      affixList: [affix],\n      eventHandlers: {}\n    };\n    observerEntities.push(entity);\n    // Add listener\n    TRIGGER_EVENTS.forEach(function (eventName) {\n      entity.eventHandlers[eventName] = addEventListener(target, eventName, function () {\n        entity.affixList.forEach(function (targetAffix) {\n          targetAffix.lazyUpdatePosition();\n        });\n      });\n    });\n  }\n}\nexport function removeObserveTarget(affix) {\n  var observerEntity = observerEntities.find(function (oriObserverEntity) {\n    var hasAffix = oriObserverEntity.affixList.some(function (item) {\n      return item === affix;\n    });\n    if (hasAffix) {\n      oriObserverEntity.affixList = oriObserverEntity.affixList.filter(function (item) {\n        return item !== affix;\n      });\n    }\n    return hasAffix;\n  });\n  if (observerEntity && observerEntity.affixList.length === 0) {\n    observerEntities = observerEntities.filter(function (item) {\n      return item !== observerEntity;\n    });\n    // Remove listener\n    TRIGGER_EVENTS.forEach(function (eventName) {\n      var handler = observerEntity.eventHandlers[eventName];\n      if (handler && handler.remove) {\n        handler.remove();\n      }\n    });\n  }\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOA,MAAM,KAAKC,MAAM,GAAGD,MAAM,CAACE,qBAAqB,CAAC,CAAC,GAAG;IAC1DC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAEH,MAAM,CAACI;EACjB,CAAC;AACH;AACA,OAAO,SAASC,WAAWA,CAACC,gBAAgB,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACnE,IAAIA,SAAS,KAAKC,SAAS,IAAIF,UAAU,CAACL,GAAG,GAAGI,gBAAgB,CAACJ,GAAG,GAAGM,SAAS,EAAE;IAChF,OAAOA,SAAS,GAAGD,UAAU,CAACL,GAAG;EACnC;EACA,OAAOO,SAAS;AAClB;AACA,OAAO,SAASC,cAAcA,CAACJ,gBAAgB,EAAEC,UAAU,EAAEI,YAAY,EAAE;EACzE,IAAIA,YAAY,KAAKF,SAAS,IAAIF,UAAU,CAACJ,MAAM,GAAGG,gBAAgB,CAACH,MAAM,GAAGQ,YAAY,EAAE;IAC5F,IAAIC,kBAAkB,GAAGZ,MAAM,CAACI,WAAW,GAAGG,UAAU,CAACJ,MAAM;IAC/D,OAAOQ,YAAY,GAAGC,kBAAkB;EAC1C;EACA,OAAOH,SAAS;AAClB;AACA;AACA,IAAII,cAAc,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;AACpG,IAAIC,gBAAgB,GAAG,EAAE;AACzB,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC;EACA,OAAOD,gBAAgB;AACzB;AACA,OAAO,SAASE,gBAAgBA,CAACjB,MAAM,EAAEkB,KAAK,EAAE;EAC9C,IAAI,CAAClB,MAAM,EAAE;IACX;EACF;EACA,IAAImB,MAAM,GAAGJ,gBAAgB,CAACK,IAAI,CAAC,UAAUC,IAAI,EAAE;IACjD,OAAOA,IAAI,CAACrB,MAAM,KAAKA,MAAM;EAC/B,CAAC,CAAC;EACF,IAAImB,MAAM,EAAE;IACVA,MAAM,CAACG,SAAS,CAACC,IAAI,CAACL,KAAK,CAAC;EAC9B,CAAC,MAAM;IACLC,MAAM,GAAG;MACPnB,MAAM,EAAEA,MAAM;MACdsB,SAAS,EAAE,CAACJ,KAAK,CAAC;MAClBM,aAAa,EAAE,CAAC;IAClB,CAAC;IACDT,gBAAgB,CAACQ,IAAI,CAACJ,MAAM,CAAC;IAC7B;IACAL,cAAc,CAACW,OAAO,CAAC,UAAUC,SAAS,EAAE;MAC1CP,MAAM,CAACK,aAAa,CAACE,SAAS,CAAC,GAAG5B,gBAAgB,CAACE,MAAM,EAAE0B,SAAS,EAAE,YAAY;QAChFP,MAAM,CAACG,SAAS,CAACG,OAAO,CAAC,UAAUE,WAAW,EAAE;UAC9CA,WAAW,CAACC,kBAAkB,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AACA,OAAO,SAASC,mBAAmBA,CAACX,KAAK,EAAE;EACzC,IAAIY,cAAc,GAAGf,gBAAgB,CAACK,IAAI,CAAC,UAAUW,iBAAiB,EAAE;IACtE,IAAIC,QAAQ,GAAGD,iBAAiB,CAACT,SAAS,CAACW,IAAI,CAAC,UAAUZ,IAAI,EAAE;MAC9D,OAAOA,IAAI,KAAKH,KAAK;IACvB,CAAC,CAAC;IACF,IAAIc,QAAQ,EAAE;MACZD,iBAAiB,CAACT,SAAS,GAAGS,iBAAiB,CAACT,SAAS,CAACY,MAAM,CAAC,UAAUb,IAAI,EAAE;QAC/E,OAAOA,IAAI,KAAKH,KAAK;MACvB,CAAC,CAAC;IACJ;IACA,OAAOc,QAAQ;EACjB,CAAC,CAAC;EACF,IAAIF,cAAc,IAAIA,cAAc,CAACR,SAAS,CAACa,MAAM,KAAK,CAAC,EAAE;IAC3DpB,gBAAgB,GAAGA,gBAAgB,CAACmB,MAAM,CAAC,UAAUb,IAAI,EAAE;MACzD,OAAOA,IAAI,KAAKS,cAAc;IAChC,CAAC,CAAC;IACF;IACAhB,cAAc,CAACW,OAAO,CAAC,UAAUC,SAAS,EAAE;MAC1C,IAAIU,OAAO,GAAGN,cAAc,CAACN,aAAa,CAACE,SAAS,CAAC;MACrD,IAAIU,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;QAC7BD,OAAO,CAACC,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}