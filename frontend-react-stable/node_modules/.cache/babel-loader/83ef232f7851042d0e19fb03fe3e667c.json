{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport ShopFilledSvg from \"@ant-design/icons-svg/es/asn/ShopFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar ShopFilled = function ShopFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: ShopFilledSvg\n  }));\n};\nShopFilled.displayName = 'ShopFilled';\nexport default /*#__PURE__*/React.forwardRef(ShopFilled);", "map": {"version": 3, "names": ["_objectSpread", "React", "ShopFilledSvg", "AntdIcon", "ShopFilled", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/ShopFilled.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport ShopFilledSvg from \"@ant-design/icons-svg/es/asn/ShopFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar ShopFilled = function ShopFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: ShopFilledSvg\n  }));\n};\nShopFilled.displayName = 'ShopFilled';\nexport default /*#__PURE__*/React.forwardRef(ShopFilled);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,UAAU,CAACK,WAAW,GAAG,YAAY;AACrC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}