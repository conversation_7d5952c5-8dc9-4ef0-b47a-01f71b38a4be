{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction isPointsEq(a1, a2, isAlignPoint) {\n  if (isAlignPoint) {\n    return a1[0] === a2[0];\n  }\n  return a1[0] === a2[0] && a1[1] === a2[1];\n}\nexport function getAlignFromPlacement(builtinPlacements, placementStr, align) {\n  var baseAlign = builtinPlacements[placementStr] || {};\n  return _objectSpread(_objectSpread({}, baseAlign), align);\n}\nexport function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n  var points = align.points;\n  var placements = Object.keys(builtinPlacements);\n  for (var i = 0; i < placements.length; i += 1) {\n    var placement = placements[i];\n    if (isPointsEq(builtinPlacements[placement].points, points, isAlignPoint)) {\n      return \"\".concat(prefixCls, \"-placement-\").concat(placement);\n    }\n  }\n  return '';\n}", "map": {"version": 3, "names": ["_objectSpread", "isPointsEq", "a1", "a2", "isAlignPoint", "getAlignFromPlacement", "builtinPlacements", "placementStr", "align", "baseAlign", "getAlignPopupClassName", "prefixCls", "points", "placements", "Object", "keys", "i", "length", "placement", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/utils/alignUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n\nfunction isPointsEq(a1, a2, isAlignPoint) {\n  if (isAlignPoint) {\n    return a1[0] === a2[0];\n  }\n\n  return a1[0] === a2[0] && a1[1] === a2[1];\n}\n\nexport function getAlignFromPlacement(builtinPlacements, placementStr, align) {\n  var baseAlign = builtinPlacements[placementStr] || {};\n  return _objectSpread(_objectSpread({}, baseAlign), align);\n}\nexport function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n  var points = align.points;\n  var placements = Object.keys(builtinPlacements);\n\n  for (var i = 0; i < placements.length; i += 1) {\n    var placement = placements[i];\n\n    if (isPointsEq(builtinPlacements[placement].points, points, isAlignPoint)) {\n      return \"\".concat(prefixCls, \"-placement-\").concat(placement);\n    }\n  }\n\n  return '';\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AAEpE,SAASC,UAAUA,CAACC,EAAE,EAAEC,EAAE,EAAEC,YAAY,EAAE;EACxC,IAAIA,YAAY,EAAE;IAChB,OAAOF,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC;EACxB;EAEA,OAAOD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IAAID,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC;AAC3C;AAEA,OAAO,SAASE,qBAAqBA,CAACC,iBAAiB,EAAEC,YAAY,EAAEC,KAAK,EAAE;EAC5E,IAAIC,SAAS,GAAGH,iBAAiB,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;EACrD,OAAOP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAES,SAAS,CAAC,EAAED,KAAK,CAAC;AAC3D;AACA,OAAO,SAASE,sBAAsBA,CAACJ,iBAAiB,EAAEK,SAAS,EAAEH,KAAK,EAAEJ,YAAY,EAAE;EACxF,IAAIQ,MAAM,GAAGJ,KAAK,CAACI,MAAM;EACzB,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACT,iBAAiB,CAAC;EAE/C,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAIE,SAAS,GAAGL,UAAU,CAACG,CAAC,CAAC;IAE7B,IAAIf,UAAU,CAACK,iBAAiB,CAACY,SAAS,CAAC,CAACN,MAAM,EAAEA,MAAM,EAAER,YAAY,CAAC,EAAE;MACzE,OAAO,EAAE,CAACe,MAAM,CAACR,SAAS,EAAE,aAAa,CAAC,CAACQ,MAAM,CAACD,SAAS,CAAC;IAC9D;EACF;EAEA,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module"}