{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport padStart from 'lodash/padStart';\n// Countdown\nvar timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365], ['M', 1000 * 60 * 60 * 24 * 30], ['D', 1000 * 60 * 60 * 24], ['H', 1000 * 60 * 60], ['m', 1000 * 60], ['s', 1000], ['S', 1] // million seconds\n];\nexport function formatTimeStr(duration, format) {\n  var leftDuration = duration;\n  var escapeRegex = /\\[[^\\]]*]/g;\n  var keepList = (format.match(escapeRegex) || []).map(function (str) {\n    return str.slice(1, -1);\n  });\n  var templateText = format.replace(escapeRegex, '[]');\n  var replacedText = timeUnits.reduce(function (current, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      name = _ref2[0],\n      unit = _ref2[1];\n    if (current.includes(name)) {\n      var value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(\"\".concat(name, \"+\"), 'g'), function (match) {\n        var len = match.length;\n        return padStart(value.toString(), len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  var index = 0;\n  return replacedText.replace(escapeRegex, function () {\n    var match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCountdown(value, config) {\n  var _config$format = config.format,\n    format = _config$format === void 0 ? '' : _config$format;\n  var target = new Date(value).getTime();\n  var current = Date.now();\n  var diff = Math.max(target - current, 0);\n  return formatTimeStr(diff, format);\n}", "map": {"version": 3, "names": ["_slicedToArray", "padStart", "timeUnits", "formatTimeStr", "duration", "format", "leftDuration", "escapeRegex", "keepList", "match", "map", "str", "slice", "templateText", "replace", "replacedText", "reduce", "current", "_ref", "_ref2", "name", "unit", "includes", "value", "Math", "floor", "RegExp", "concat", "len", "length", "toString", "index", "formatCountdown", "config", "_config$format", "target", "Date", "getTime", "now", "diff", "max"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/statistic/utils.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport padStart from 'lodash/padStart';\n// Countdown\nvar timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365], ['M', 1000 * 60 * 60 * 24 * 30], ['D', 1000 * 60 * 60 * 24], ['H', 1000 * 60 * 60], ['m', 1000 * 60], ['s', 1000], ['S', 1] // million seconds\n];\n\nexport function formatTimeStr(duration, format) {\n  var leftDuration = duration;\n  var escapeRegex = /\\[[^\\]]*]/g;\n  var keepList = (format.match(escapeRegex) || []).map(function (str) {\n    return str.slice(1, -1);\n  });\n  var templateText = format.replace(escapeRegex, '[]');\n  var replacedText = timeUnits.reduce(function (current, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      name = _ref2[0],\n      unit = _ref2[1];\n    if (current.includes(name)) {\n      var value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(\"\".concat(name, \"+\"), 'g'), function (match) {\n        var len = match.length;\n        return padStart(value.toString(), len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  var index = 0;\n  return replacedText.replace(escapeRegex, function () {\n    var match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCountdown(value, config) {\n  var _config$format = config.format,\n    format = _config$format === void 0 ? '' : _config$format;\n  var target = new Date(value).getTime();\n  var current = Date.now();\n  var diff = Math.max(target - current, 0);\n  return formatTimeStr(diff, format);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC;AACA,IAAIC,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,CAC9K;AAED,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9C,IAAIC,YAAY,GAAGF,QAAQ;EAC3B,IAAIG,WAAW,GAAG,YAAY;EAC9B,IAAIC,QAAQ,GAAG,CAACH,MAAM,CAACI,KAAK,CAACF,WAAW,CAAC,IAAI,EAAE,EAAEG,GAAG,CAAC,UAAUC,GAAG,EAAE;IAClE,OAAOA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC;EACF,IAAIC,YAAY,GAAGR,MAAM,CAACS,OAAO,CAACP,WAAW,EAAE,IAAI,CAAC;EACpD,IAAIQ,YAAY,GAAGb,SAAS,CAACc,MAAM,CAAC,UAAUC,OAAO,EAAEC,IAAI,EAAE;IAC3D,IAAIC,KAAK,GAAGnB,cAAc,CAACkB,IAAI,EAAE,CAAC,CAAC;MACjCE,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;MACfE,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC;IACjB,IAAIF,OAAO,CAACK,QAAQ,CAACF,IAAI,CAAC,EAAE;MAC1B,IAAIG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACnB,YAAY,GAAGe,IAAI,CAAC;MAC3Cf,YAAY,IAAIiB,KAAK,GAAGF,IAAI;MAC5B,OAAOJ,OAAO,CAACH,OAAO,CAAC,IAAIY,MAAM,CAAC,EAAE,CAACC,MAAM,CAACP,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,UAAUX,KAAK,EAAE;QAC7E,IAAImB,GAAG,GAAGnB,KAAK,CAACoB,MAAM;QACtB,OAAO5B,QAAQ,CAACsB,KAAK,CAACO,QAAQ,CAAC,CAAC,EAAEF,GAAG,EAAE,GAAG,CAAC;MAC7C,CAAC,CAAC;IACJ;IACA,OAAOX,OAAO;EAChB,CAAC,EAAEJ,YAAY,CAAC;EAChB,IAAIkB,KAAK,GAAG,CAAC;EACb,OAAOhB,YAAY,CAACD,OAAO,CAACP,WAAW,EAAE,YAAY;IACnD,IAAIE,KAAK,GAAGD,QAAQ,CAACuB,KAAK,CAAC;IAC3BA,KAAK,IAAI,CAAC;IACV,OAAOtB,KAAK;EACd,CAAC,CAAC;AACJ;AACA,OAAO,SAASuB,eAAeA,CAACT,KAAK,EAAEU,MAAM,EAAE;EAC7C,IAAIC,cAAc,GAAGD,MAAM,CAAC5B,MAAM;IAChCA,MAAM,GAAG6B,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;EAC1D,IAAIC,MAAM,GAAG,IAAIC,IAAI,CAACb,KAAK,CAAC,CAACc,OAAO,CAAC,CAAC;EACtC,IAAIpB,OAAO,GAAGmB,IAAI,CAACE,GAAG,CAAC,CAAC;EACxB,IAAIC,IAAI,GAAGf,IAAI,CAACgB,GAAG,CAACL,MAAM,GAAGlB,OAAO,EAAE,CAAC,CAAC;EACxC,OAAOd,aAAa,CAACoC,IAAI,EAAElC,MAAM,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}