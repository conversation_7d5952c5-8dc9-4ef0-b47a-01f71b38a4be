{"ast": null, "code": "// This icon file is generated automatically.\nvar CaretRightOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"0 0 1024 1024\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z\"\n      }\n    }]\n  },\n  \"name\": \"caret-right\",\n  \"theme\": \"outlined\"\n};\nexport default CaretRightOutlined;", "map": {"version": 3, "names": ["CaretRightOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/CaretRightOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CaretRightOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"0 0 1024 1024\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z\" } }] }, \"name\": \"caret-right\", \"theme\": \"outlined\" };\nexport default CaretRightOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAuH;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,aAAa;EAAE,OAAO,EAAE;AAAW,CAAC;AAC3U,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}