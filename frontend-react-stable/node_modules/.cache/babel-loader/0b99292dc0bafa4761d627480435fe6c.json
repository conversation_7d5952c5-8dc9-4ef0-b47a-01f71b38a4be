{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport classNames from 'classnames';\nimport RcUpload from 'rc-upload';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport warning from '../_util/warning';\nimport UploadList from './UploadList';\nimport { file2Obj, getFileItem, removeFileItem, updateFileList } from './utils';\nexport var LIST_IGNORE = \"__LIST_IGNORE_\".concat(Date.now(), \"__\");\nvar InternalUpload = function InternalUpload(props, ref) {\n  var _classNames2;\n  var fileList = props.fileList,\n    defaultFileList = props.defaultFileList,\n    onRemove = props.onRemove,\n    _props$showUploadList = props.showUploadList,\n    showUploadList = _props$showUploadList === void 0 ? true : _props$showUploadList,\n    _props$listType = props.listType,\n    listType = _props$listType === void 0 ? 'text' : _props$listType,\n    onPreview = props.onPreview,\n    onDownload = props.onDownload,\n    onChange = props.onChange,\n    onDrop = props.onDrop,\n    previewFile = props.previewFile,\n    customDisabled = props.disabled,\n    propLocale = props.locale,\n    iconRender = props.iconRender,\n    isImageUrl = props.isImageUrl,\n    progress = props.progress,\n    customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'select' : _props$type,\n    children = props.children,\n    style = props.style,\n    itemRender = props.itemRender,\n    maxCount = props.maxCount,\n    _props$data = props.data,\n    data = _props$data === void 0 ? {} : _props$data,\n    _props$multiple = props.multiple,\n    multiple = _props$multiple === void 0 ? false : _props$multiple,\n    _props$action = props.action,\n    action = _props$action === void 0 ? '' : _props$action,\n    _props$accept = props.accept,\n    accept = _props$accept === void 0 ? '' : _props$accept,\n    _props$supportServerR = props.supportServerRender,\n    supportServerRender = _props$supportServerR === void 0 ? true : _props$supportServerR;\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  var _useMergedState = useMergedState(defaultFileList || [], {\n      value: fileList,\n      postState: function postState(list) {\n        return list !== null && list !== void 0 ? list : [];\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedFileList = _useMergedState2[0],\n    setMergedFileList = _useMergedState2[1];\n  var _React$useState = React.useState('drop'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    dragState = _React$useState2[0],\n    setDragState = _React$useState2[1];\n  var upload = React.useRef();\n  process.env.NODE_ENV !== \"production\" ? warning('fileList' in props || !('value' in props), 'Upload', '`value` is not a valid prop, do you mean `fileList`?') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!('transformFile' in props), 'Upload', '`transformFile` is deprecated. Please use `beforeUpload` directly.') : void 0;\n  // Control mode will auto fill file uid if not provided\n  React.useMemo(function () {\n    var timestamp = Date.now();\n    (fileList || []).forEach(function (file, index) {\n      if (!file.uid && !Object.isFrozen(file)) {\n        file.uid = \"__AUTO__\".concat(timestamp, \"_\").concat(index, \"__\");\n      }\n    });\n  }, [fileList]);\n  var onInternalChange = function onInternalChange(file, changedFileList, event) {\n    var cloneList = _toConsumableArray(changedFileList);\n    // Cut to match count\n    if (maxCount === 1) {\n      cloneList = cloneList.slice(-1);\n    } else if (maxCount) {\n      cloneList = cloneList.slice(0, maxCount);\n    }\n    // Prevent React18 auto batch since input[upload] trigger process at same time\n    // which makes fileList closure problem\n    flushSync(function () {\n      setMergedFileList(cloneList);\n    });\n    var changeInfo = {\n      file: file,\n      fileList: cloneList\n    };\n    if (event) {\n      changeInfo.event = event;\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);\n  };\n  var mergedBeforeUpload = function mergedBeforeUpload(file, fileListArgs) {\n    return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      var beforeUpload, transformFile, parsedFile, result;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              beforeUpload = props.beforeUpload, transformFile = props.transformFile;\n              parsedFile = file;\n              if (!beforeUpload) {\n                _context.next = 13;\n                break;\n              }\n              _context.next = 5;\n              return beforeUpload(file, fileListArgs);\n            case 5:\n              result = _context.sent;\n              if (!(result === false)) {\n                _context.next = 8;\n                break;\n              }\n              return _context.abrupt(\"return\", false);\n            case 8:\n              // Hack for LIST_IGNORE, we add additional info to remove from the list\n              delete file[LIST_IGNORE];\n              if (!(result === LIST_IGNORE)) {\n                _context.next = 12;\n                break;\n              }\n              Object.defineProperty(file, LIST_IGNORE, {\n                value: true,\n                configurable: true\n              });\n              return _context.abrupt(\"return\", false);\n            case 12:\n              if (_typeof(result) === 'object' && result) {\n                parsedFile = result;\n              }\n            case 13:\n              if (!transformFile) {\n                _context.next = 17;\n                break;\n              }\n              _context.next = 16;\n              return transformFile(parsedFile);\n            case 16:\n              parsedFile = _context.sent;\n            case 17:\n              return _context.abrupt(\"return\", parsedFile);\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee);\n    }));\n  };\n  var onBatchStart = function onBatchStart(batchFileInfoList) {\n    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list\n    var filteredFileInfoList = batchFileInfoList.filter(function (info) {\n      return !info.file[LIST_IGNORE];\n    });\n    // Nothing to do since no file need upload\n    if (!filteredFileInfoList.length) {\n      return;\n    }\n    var objectFileList = filteredFileInfoList.map(function (info) {\n      return file2Obj(info.file);\n    });\n    // Concat new files with prev files\n    var newFileList = _toConsumableArray(mergedFileList);\n    objectFileList.forEach(function (fileObj) {\n      // Replace file if exist\n      newFileList = updateFileList(fileObj, newFileList);\n    });\n    objectFileList.forEach(function (fileObj, index) {\n      // Repeat trigger `onChange` event for compatible\n      var triggerFileObj = fileObj;\n      if (!filteredFileInfoList[index].parsedFile) {\n        // `beforeUpload` return false\n        var originFileObj = fileObj.originFileObj;\n        var clone;\n        try {\n          clone = new File([originFileObj], originFileObj.name, {\n            type: originFileObj.type\n          });\n        } catch (e) {\n          clone = new Blob([originFileObj], {\n            type: originFileObj.type\n          });\n          clone.name = originFileObj.name;\n          clone.lastModifiedDate = new Date();\n          clone.lastModified = new Date().getTime();\n        }\n        clone.uid = fileObj.uid;\n        triggerFileObj = clone;\n      } else {\n        // Inject `uploading` status\n        fileObj.status = 'uploading';\n      }\n      onInternalChange(triggerFileObj, newFileList);\n    });\n  };\n  var onSuccess = function onSuccess(response, file, xhr) {\n    try {\n      if (typeof response === 'string') {\n        response = JSON.parse(response);\n      }\n    } catch (e) {\n      /* do nothing */\n    }\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'done';\n    targetItem.percent = 100;\n    targetItem.response = response;\n    targetItem.xhr = xhr;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var onProgress = function onProgress(e, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'uploading';\n    targetItem.percent = e.percent;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList, e);\n  };\n  var onError = function onError(error, response, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.error = error;\n    targetItem.response = response;\n    targetItem.status = 'error';\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var handleRemove = function handleRemove(file) {\n    var currentFile;\n    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(function (ret) {\n      var _a;\n      // Prevent removing file\n      if (ret === false) {\n        return;\n      }\n      var removedFileList = removeFileItem(file, mergedFileList);\n      if (removedFileList) {\n        currentFile = _extends(_extends({}, file), {\n          status: 'removed'\n        });\n        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(function (item) {\n          var matchKey = currentFile.uid !== undefined ? 'uid' : 'name';\n          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {\n            item.status = 'removed';\n          }\n        });\n        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);\n        onInternalChange(currentFile, removedFileList);\n      }\n    });\n  };\n  var onFileDrop = function onFileDrop(e) {\n    setDragState(e.type);\n    if (e.type === 'drop') {\n      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);\n    }\n  };\n  // Test needs\n  React.useImperativeHandle(ref, function () {\n    return {\n      onBatchStart: onBatchStart,\n      onSuccess: onSuccess,\n      onProgress: onProgress,\n      onError: onError,\n      fileList: mergedFileList,\n      upload: upload.current\n    };\n  });\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('upload', customizePrefixCls);\n  var rcUploadProps = _extends(_extends({\n    onBatchStart: onBatchStart,\n    onError: onError,\n    onProgress: onProgress,\n    onSuccess: onSuccess\n  }, props), {\n    data: data,\n    multiple: multiple,\n    action: action,\n    accept: accept,\n    supportServerRender: supportServerRender,\n    prefixCls: prefixCls,\n    disabled: mergedDisabled,\n    beforeUpload: mergedBeforeUpload,\n    onChange: undefined\n  });\n  delete rcUploadProps.className;\n  delete rcUploadProps.style;\n  // Remove id to avoid open by label when trigger is hidden\n  // !children: https://github.com/ant-design/ant-design/issues/14298\n  // disabled: https://github.com/ant-design/ant-design/issues/16478\n  //           https://github.com/ant-design/ant-design/issues/24197\n  if (!children || mergedDisabled) {\n    delete rcUploadProps.id;\n  }\n  var renderUploadList = function renderUploadList(button, buttonVisible) {\n    return showUploadList ? /*#__PURE__*/React.createElement(LocaleReceiver, {\n      componentName: \"Upload\",\n      defaultLocale: defaultLocale.Upload\n    }, function (contextLocale) {\n      var _ref = typeof showUploadList === 'boolean' ? {} : showUploadList,\n        showRemoveIcon = _ref.showRemoveIcon,\n        showPreviewIcon = _ref.showPreviewIcon,\n        showDownloadIcon = _ref.showDownloadIcon,\n        removeIcon = _ref.removeIcon,\n        previewIcon = _ref.previewIcon,\n        downloadIcon = _ref.downloadIcon;\n      return /*#__PURE__*/React.createElement(UploadList, {\n        prefixCls: prefixCls,\n        listType: listType,\n        items: mergedFileList,\n        previewFile: previewFile,\n        onPreview: onPreview,\n        onDownload: onDownload,\n        onRemove: handleRemove,\n        showRemoveIcon: !mergedDisabled && showRemoveIcon,\n        showPreviewIcon: showPreviewIcon,\n        showDownloadIcon: showDownloadIcon,\n        removeIcon: removeIcon,\n        previewIcon: previewIcon,\n        downloadIcon: downloadIcon,\n        iconRender: iconRender,\n        locale: _extends(_extends({}, contextLocale), propLocale),\n        isImageUrl: isImageUrl,\n        progress: progress,\n        appendAction: button,\n        appendActionVisible: buttonVisible,\n        itemRender: itemRender\n      });\n    }) : button;\n  };\n  if (type === 'drag') {\n    var _classNames;\n    var dragCls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-uploading\"), mergedFileList.some(function (file) {\n      return file.status === 'uploading';\n    })), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-hover\"), dragState === 'dragover'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(\"div\", {\n      className: dragCls,\n      onDrop: onFileDrop,\n      onDragOver: onFileDrop,\n      onDragLeave: onFileDrop,\n      style: style\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload,\n      className: \"\".concat(prefixCls, \"-btn\")\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-drag-container\")\n    }, children))), renderUploadList());\n  }\n  var uploadButtonCls = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select-\").concat(listType), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2));\n  var renderUploadButton = function renderUploadButton(uploadButtonStyle) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: uploadButtonCls,\n      style: uploadButtonStyle\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload\n    })));\n  };\n  var uploadButton = renderUploadButton(children ? undefined : {\n    display: 'none'\n  });\n  if (listType === 'picture-card') {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(prefixCls, \"-picture-card-wrapper\"), className)\n    }, renderUploadList(uploadButton, !!children));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className\n  }, uploadButton, renderUploadList());\n};\nvar Upload = /*#__PURE__*/React.forwardRef(InternalUpload);\nif (process.env.NODE_ENV !== 'production') {\n  Upload.displayName = 'Upload';\n}\nexport default Upload;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_regeneratorRuntime", "_typeof", "_toConsumableArray", "_slicedToArray", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "classNames", "RcUpload", "useMergedState", "React", "flushSync", "ConfigContext", "DisabledContext", "LocaleReceiver", "defaultLocale", "warning", "UploadList", "file2Obj", "getFileItem", "removeFileItem", "updateFileList", "LIST_IGNORE", "concat", "Date", "now", "InternalUpload", "props", "ref", "_classNames2", "fileList", "defaultFileList", "onRemove", "_props$showUploadList", "showUploadList", "_props$listType", "listType", "onPreview", "onDownload", "onChange", "onDrop", "previewFile", "customDisabled", "disabled", "propLocale", "locale", "iconRender", "isImageUrl", "progress", "customizePrefixCls", "prefixCls", "className", "_props$type", "type", "children", "style", "itemRender", "maxCount", "_props$data", "data", "_props$multiple", "multiple", "_props$action", "action", "_props$accept", "accept", "_props$supportServerR", "supportServerRender", "useContext", "mergedDisabled", "_useMergedState", "postState", "list", "_useMergedState2", "mergedFileList", "setMergedFileList", "_React$useState", "useState", "_React$useState2", "dragState", "setDragState", "upload", "useRef", "process", "env", "NODE_ENV", "useMemo", "timestamp", "for<PERSON>ach", "file", "index", "uid", "Object", "isFrozen", "onInternalChange", "changedFileList", "event", "cloneList", "slice", "changeInfo", "mergedBeforeUpload", "fileListArgs", "mark", "_callee", "beforeUpload", "transformFile", "parsedFile", "wrap", "_callee$", "_context", "prev", "sent", "abrupt", "defineProperty", "configurable", "stop", "onBatchStart", "batchFileInfoList", "filteredFileInfoList", "filter", "info", "length", "objectFileList", "map", "newFileList", "fileObj", "triggerFileObj", "originFileObj", "clone", "File", "name", "Blob", "lastModifiedDate", "lastModified", "getTime", "status", "onSuccess", "response", "xhr", "JSON", "parse", "targetItem", "percent", "nextFileList", "onProgress", "onError", "error", "handleRemove", "currentFile", "ret", "_a", "removedFileList", "item", "matchKey", "undefined", "current", "abort", "onFileDrop", "useImperativeHandle", "_React$useContext", "getPrefixCls", "direction", "rcUploadProps", "id", "renderUploadList", "button", "buttonVisible", "createElement", "componentName", "Upload", "contextLocale", "_ref", "showRemoveIcon", "showPreviewIcon", "showDownloadIcon", "removeIcon", "previewIcon", "downloadIcon", "items", "appendAction", "appendActionVisible", "_classNames", "dragCls", "some", "onDragOver", "onDragLeave", "uploadButtonCls", "renderUploadButton", "uploadButtonStyle", "uploadButton", "display", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/upload/Upload.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport classNames from 'classnames';\nimport RcUpload from 'rc-upload';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport warning from '../_util/warning';\nimport UploadList from './UploadList';\nimport { file2Obj, getFileItem, removeFileItem, updateFileList } from './utils';\nexport var LIST_IGNORE = \"__LIST_IGNORE_\".concat(Date.now(), \"__\");\nvar InternalUpload = function InternalUpload(props, ref) {\n  var _classNames2;\n  var fileList = props.fileList,\n    defaultFileList = props.defaultFileList,\n    onRemove = props.onRemove,\n    _props$showUploadList = props.showUploadList,\n    showUploadList = _props$showUploadList === void 0 ? true : _props$showUploadList,\n    _props$listType = props.listType,\n    listType = _props$listType === void 0 ? 'text' : _props$listType,\n    onPreview = props.onPreview,\n    onDownload = props.onDownload,\n    onChange = props.onChange,\n    onDrop = props.onDrop,\n    previewFile = props.previewFile,\n    customDisabled = props.disabled,\n    propLocale = props.locale,\n    iconRender = props.iconRender,\n    isImageUrl = props.isImageUrl,\n    progress = props.progress,\n    customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'select' : _props$type,\n    children = props.children,\n    style = props.style,\n    itemRender = props.itemRender,\n    maxCount = props.maxCount,\n    _props$data = props.data,\n    data = _props$data === void 0 ? {} : _props$data,\n    _props$multiple = props.multiple,\n    multiple = _props$multiple === void 0 ? false : _props$multiple,\n    _props$action = props.action,\n    action = _props$action === void 0 ? '' : _props$action,\n    _props$accept = props.accept,\n    accept = _props$accept === void 0 ? '' : _props$accept,\n    _props$supportServerR = props.supportServerRender,\n    supportServerRender = _props$supportServerR === void 0 ? true : _props$supportServerR;\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  var _useMergedState = useMergedState(defaultFileList || [], {\n      value: fileList,\n      postState: function postState(list) {\n        return list !== null && list !== void 0 ? list : [];\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedFileList = _useMergedState2[0],\n    setMergedFileList = _useMergedState2[1];\n  var _React$useState = React.useState('drop'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    dragState = _React$useState2[0],\n    setDragState = _React$useState2[1];\n  var upload = React.useRef();\n  process.env.NODE_ENV !== \"production\" ? warning('fileList' in props || !('value' in props), 'Upload', '`value` is not a valid prop, do you mean `fileList`?') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!('transformFile' in props), 'Upload', '`transformFile` is deprecated. Please use `beforeUpload` directly.') : void 0;\n  // Control mode will auto fill file uid if not provided\n  React.useMemo(function () {\n    var timestamp = Date.now();\n    (fileList || []).forEach(function (file, index) {\n      if (!file.uid && !Object.isFrozen(file)) {\n        file.uid = \"__AUTO__\".concat(timestamp, \"_\").concat(index, \"__\");\n      }\n    });\n  }, [fileList]);\n  var onInternalChange = function onInternalChange(file, changedFileList, event) {\n    var cloneList = _toConsumableArray(changedFileList);\n    // Cut to match count\n    if (maxCount === 1) {\n      cloneList = cloneList.slice(-1);\n    } else if (maxCount) {\n      cloneList = cloneList.slice(0, maxCount);\n    }\n    // Prevent React18 auto batch since input[upload] trigger process at same time\n    // which makes fileList closure problem\n    flushSync(function () {\n      setMergedFileList(cloneList);\n    });\n    var changeInfo = {\n      file: file,\n      fileList: cloneList\n    };\n    if (event) {\n      changeInfo.event = event;\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);\n  };\n  var mergedBeforeUpload = function mergedBeforeUpload(file, fileListArgs) {\n    return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      var beforeUpload, transformFile, parsedFile, result;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              beforeUpload = props.beforeUpload, transformFile = props.transformFile;\n              parsedFile = file;\n              if (!beforeUpload) {\n                _context.next = 13;\n                break;\n              }\n              _context.next = 5;\n              return beforeUpload(file, fileListArgs);\n            case 5:\n              result = _context.sent;\n              if (!(result === false)) {\n                _context.next = 8;\n                break;\n              }\n              return _context.abrupt(\"return\", false);\n            case 8:\n              // Hack for LIST_IGNORE, we add additional info to remove from the list\n              delete file[LIST_IGNORE];\n              if (!(result === LIST_IGNORE)) {\n                _context.next = 12;\n                break;\n              }\n              Object.defineProperty(file, LIST_IGNORE, {\n                value: true,\n                configurable: true\n              });\n              return _context.abrupt(\"return\", false);\n            case 12:\n              if (_typeof(result) === 'object' && result) {\n                parsedFile = result;\n              }\n            case 13:\n              if (!transformFile) {\n                _context.next = 17;\n                break;\n              }\n              _context.next = 16;\n              return transformFile(parsedFile);\n            case 16:\n              parsedFile = _context.sent;\n            case 17:\n              return _context.abrupt(\"return\", parsedFile);\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee);\n    }));\n  };\n  var onBatchStart = function onBatchStart(batchFileInfoList) {\n    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list\n    var filteredFileInfoList = batchFileInfoList.filter(function (info) {\n      return !info.file[LIST_IGNORE];\n    });\n    // Nothing to do since no file need upload\n    if (!filteredFileInfoList.length) {\n      return;\n    }\n    var objectFileList = filteredFileInfoList.map(function (info) {\n      return file2Obj(info.file);\n    });\n    // Concat new files with prev files\n    var newFileList = _toConsumableArray(mergedFileList);\n    objectFileList.forEach(function (fileObj) {\n      // Replace file if exist\n      newFileList = updateFileList(fileObj, newFileList);\n    });\n    objectFileList.forEach(function (fileObj, index) {\n      // Repeat trigger `onChange` event for compatible\n      var triggerFileObj = fileObj;\n      if (!filteredFileInfoList[index].parsedFile) {\n        // `beforeUpload` return false\n        var originFileObj = fileObj.originFileObj;\n        var clone;\n        try {\n          clone = new File([originFileObj], originFileObj.name, {\n            type: originFileObj.type\n          });\n        } catch (e) {\n          clone = new Blob([originFileObj], {\n            type: originFileObj.type\n          });\n          clone.name = originFileObj.name;\n          clone.lastModifiedDate = new Date();\n          clone.lastModified = new Date().getTime();\n        }\n        clone.uid = fileObj.uid;\n        triggerFileObj = clone;\n      } else {\n        // Inject `uploading` status\n        fileObj.status = 'uploading';\n      }\n      onInternalChange(triggerFileObj, newFileList);\n    });\n  };\n  var onSuccess = function onSuccess(response, file, xhr) {\n    try {\n      if (typeof response === 'string') {\n        response = JSON.parse(response);\n      }\n    } catch (e) {\n      /* do nothing */\n    }\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'done';\n    targetItem.percent = 100;\n    targetItem.response = response;\n    targetItem.xhr = xhr;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var onProgress = function onProgress(e, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'uploading';\n    targetItem.percent = e.percent;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList, e);\n  };\n  var onError = function onError(error, response, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.error = error;\n    targetItem.response = response;\n    targetItem.status = 'error';\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var handleRemove = function handleRemove(file) {\n    var currentFile;\n    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(function (ret) {\n      var _a;\n      // Prevent removing file\n      if (ret === false) {\n        return;\n      }\n      var removedFileList = removeFileItem(file, mergedFileList);\n      if (removedFileList) {\n        currentFile = _extends(_extends({}, file), {\n          status: 'removed'\n        });\n        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(function (item) {\n          var matchKey = currentFile.uid !== undefined ? 'uid' : 'name';\n          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {\n            item.status = 'removed';\n          }\n        });\n        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);\n        onInternalChange(currentFile, removedFileList);\n      }\n    });\n  };\n  var onFileDrop = function onFileDrop(e) {\n    setDragState(e.type);\n    if (e.type === 'drop') {\n      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);\n    }\n  };\n  // Test needs\n  React.useImperativeHandle(ref, function () {\n    return {\n      onBatchStart: onBatchStart,\n      onSuccess: onSuccess,\n      onProgress: onProgress,\n      onError: onError,\n      fileList: mergedFileList,\n      upload: upload.current\n    };\n  });\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('upload', customizePrefixCls);\n  var rcUploadProps = _extends(_extends({\n    onBatchStart: onBatchStart,\n    onError: onError,\n    onProgress: onProgress,\n    onSuccess: onSuccess\n  }, props), {\n    data: data,\n    multiple: multiple,\n    action: action,\n    accept: accept,\n    supportServerRender: supportServerRender,\n    prefixCls: prefixCls,\n    disabled: mergedDisabled,\n    beforeUpload: mergedBeforeUpload,\n    onChange: undefined\n  });\n  delete rcUploadProps.className;\n  delete rcUploadProps.style;\n  // Remove id to avoid open by label when trigger is hidden\n  // !children: https://github.com/ant-design/ant-design/issues/14298\n  // disabled: https://github.com/ant-design/ant-design/issues/16478\n  //           https://github.com/ant-design/ant-design/issues/24197\n  if (!children || mergedDisabled) {\n    delete rcUploadProps.id;\n  }\n  var renderUploadList = function renderUploadList(button, buttonVisible) {\n    return showUploadList ? /*#__PURE__*/React.createElement(LocaleReceiver, {\n      componentName: \"Upload\",\n      defaultLocale: defaultLocale.Upload\n    }, function (contextLocale) {\n      var _ref = typeof showUploadList === 'boolean' ? {} : showUploadList,\n        showRemoveIcon = _ref.showRemoveIcon,\n        showPreviewIcon = _ref.showPreviewIcon,\n        showDownloadIcon = _ref.showDownloadIcon,\n        removeIcon = _ref.removeIcon,\n        previewIcon = _ref.previewIcon,\n        downloadIcon = _ref.downloadIcon;\n      return /*#__PURE__*/React.createElement(UploadList, {\n        prefixCls: prefixCls,\n        listType: listType,\n        items: mergedFileList,\n        previewFile: previewFile,\n        onPreview: onPreview,\n        onDownload: onDownload,\n        onRemove: handleRemove,\n        showRemoveIcon: !mergedDisabled && showRemoveIcon,\n        showPreviewIcon: showPreviewIcon,\n        showDownloadIcon: showDownloadIcon,\n        removeIcon: removeIcon,\n        previewIcon: previewIcon,\n        downloadIcon: downloadIcon,\n        iconRender: iconRender,\n        locale: _extends(_extends({}, contextLocale), propLocale),\n        isImageUrl: isImageUrl,\n        progress: progress,\n        appendAction: button,\n        appendActionVisible: buttonVisible,\n        itemRender: itemRender\n      });\n    }) : button;\n  };\n  if (type === 'drag') {\n    var _classNames;\n    var dragCls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-uploading\"), mergedFileList.some(function (file) {\n      return file.status === 'uploading';\n    })), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-hover\"), dragState === 'dragover'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(\"div\", {\n      className: dragCls,\n      onDrop: onFileDrop,\n      onDragOver: onFileDrop,\n      onDragLeave: onFileDrop,\n      style: style\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload,\n      className: \"\".concat(prefixCls, \"-btn\")\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-drag-container\")\n    }, children))), renderUploadList());\n  }\n  var uploadButtonCls = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select-\").concat(listType), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2));\n  var renderUploadButton = function renderUploadButton(uploadButtonStyle) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: uploadButtonCls,\n      style: uploadButtonStyle\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload\n    })));\n  };\n  var uploadButton = renderUploadButton(children ? undefined : {\n    display: 'none'\n  });\n  if (listType === 'picture-card') {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(prefixCls, \"-picture-card-wrapper\"), className)\n    }, renderUploadList(uploadButton, !!children));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className\n  }, uploadButton, renderUploadList());\n};\nvar Upload = /*#__PURE__*/React.forwardRef(InternalUpload);\nif (process.env.NODE_ENV !== 'production') {\n  Upload.displayName = 'Upload';\n}\nexport default Upload;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,SAAS,GAAG,IAAI,IAAI,IAAI,CAACA,SAAS,IAAI,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IACpB,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAC3DA,OAAO,CAACD,KAAK,CAAC;IAChB,CAAC,CAAC;EACJ;EACA,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACzD,SAASC,SAASA,CAACJ,KAAK,EAAE;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IACA,SAASC,QAAQA,CAACR,KAAK,EAAE;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IACA,SAASF,IAAIA,CAACI,MAAM,EAAE;MACpBA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IACrF;IACAH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ,CAAC;AACD,OAAOO,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,QAAQ,SAAS;AAC/E,OAAO,IAAIC,WAAW,GAAG,gBAAgB,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAClE,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,IAAIC,YAAY;EAChB,IAAIC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IAC3BC,eAAe,GAAGJ,KAAK,CAACI,eAAe;IACvCC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,qBAAqB,GAAGN,KAAK,CAACO,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAChFE,eAAe,GAAGR,KAAK,CAACS,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,eAAe;IAChEE,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,MAAM,GAAGb,KAAK,CAACa,MAAM;IACrBC,WAAW,GAAGd,KAAK,CAACc,WAAW;IAC/BC,cAAc,GAAGf,KAAK,CAACgB,QAAQ;IAC/BC,UAAU,GAAGjB,KAAK,CAACkB,MAAM;IACzBC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,UAAU,GAAGpB,KAAK,CAACoB,UAAU;IAC7BC,QAAQ,GAAGrB,KAAK,CAACqB,QAAQ;IACzBC,kBAAkB,GAAGtB,KAAK,CAACuB,SAAS;IACpCC,SAAS,GAAGxB,KAAK,CAACwB,SAAS;IAC3BC,WAAW,GAAGzB,KAAK,CAAC0B,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,WAAW;IACtDE,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;IACzBC,KAAK,GAAG5B,KAAK,CAAC4B,KAAK;IACnBC,UAAU,GAAG7B,KAAK,CAAC6B,UAAU;IAC7BC,QAAQ,GAAG9B,KAAK,CAAC8B,QAAQ;IACzBC,WAAW,GAAG/B,KAAK,CAACgC,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,WAAW;IAChDE,eAAe,GAAGjC,KAAK,CAACkC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,aAAa,GAAGnC,KAAK,CAACoC,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;IACtDE,aAAa,GAAGrC,KAAK,CAACsC,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;IACtDE,qBAAqB,GAAGvC,KAAK,CAACwC,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;EACvF;EACA,IAAIvB,QAAQ,GAAGjC,KAAK,CAAC0D,UAAU,CAACvD,eAAe,CAAC;EAChD,IAAIwD,cAAc,GAAG3B,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC,QAAQ;EACrG,IAAI2B,eAAe,GAAG7D,cAAc,CAACsB,eAAe,IAAI,EAAE,EAAE;MACxDrC,KAAK,EAAEoC,QAAQ;MACfyC,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE;MACrD;IACF,CAAC,CAAC;IACFC,gBAAgB,GAAGtF,cAAc,CAACmF,eAAe,EAAE,CAAC,CAAC;IACrDI,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,eAAe,GAAGlE,KAAK,CAACmE,QAAQ,CAAC,MAAM,CAAC;IAC1CC,gBAAgB,GAAG3F,cAAc,CAACyF,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,MAAM,GAAGvE,KAAK,CAACwE,MAAM,CAAC,CAAC;EAC3BC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrE,OAAO,CAAC,UAAU,IAAIW,KAAK,IAAI,EAAE,OAAO,IAAIA,KAAK,CAAC,EAAE,QAAQ,EAAE,sDAAsD,CAAC,GAAG,KAAK,CAAC;EACtKwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrE,OAAO,CAAC,EAAE,eAAe,IAAIW,KAAK,CAAC,EAAE,QAAQ,EAAE,oEAAoE,CAAC,GAAG,KAAK,CAAC;EACrK;EACAjB,KAAK,CAAC4E,OAAO,CAAC,YAAY;IACxB,IAAIC,SAAS,GAAG/D,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,CAACK,QAAQ,IAAI,EAAE,EAAE0D,OAAO,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;MAC9C,IAAI,CAACD,IAAI,CAACE,GAAG,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACJ,IAAI,CAAC,EAAE;QACvCA,IAAI,CAACE,GAAG,GAAG,UAAU,CAACpE,MAAM,CAACgE,SAAS,EAAE,GAAG,CAAC,CAAChE,MAAM,CAACmE,KAAK,EAAE,IAAI,CAAC;MAClE;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5D,QAAQ,CAAC,CAAC;EACd,IAAIgE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACL,IAAI,EAAEM,eAAe,EAAEC,KAAK,EAAE;IAC7E,IAAIC,SAAS,GAAG/G,kBAAkB,CAAC6G,eAAe,CAAC;IACnD;IACA,IAAItC,QAAQ,KAAK,CAAC,EAAE;MAClBwC,SAAS,GAAGA,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIzC,QAAQ,EAAE;MACnBwC,SAAS,GAAGA,SAAS,CAACC,KAAK,CAAC,CAAC,EAAEzC,QAAQ,CAAC;IAC1C;IACA;IACA;IACA9C,SAAS,CAAC,YAAY;MACpBgE,iBAAiB,CAACsB,SAAS,CAAC;IAC9B,CAAC,CAAC;IACF,IAAIE,UAAU,GAAG;MACfV,IAAI,EAAEA,IAAI;MACV3D,QAAQ,EAAEmE;IACZ,CAAC;IACD,IAAID,KAAK,EAAE;MACTG,UAAU,CAACH,KAAK,GAAGA,KAAK;IAC1B;IACAzD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC4D,UAAU,CAAC;EAC1E,CAAC;EACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACX,IAAI,EAAEY,YAAY,EAAE;IACvE,OAAOjH,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAaJ,mBAAmB,CAAC,CAAC,CAACsH,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;MAClG,IAAIC,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAEvG,MAAM;MACnD,OAAOnB,mBAAmB,CAAC,CAAC,CAAC2H,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;QAC5D,OAAO,CAAC,EAAE;UACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAAC7G,IAAI;YACnC,KAAK,CAAC;cACJwG,YAAY,GAAG7E,KAAK,CAAC6E,YAAY,EAAEC,aAAa,GAAG9E,KAAK,CAAC8E,aAAa;cACtEC,UAAU,GAAGjB,IAAI;cACjB,IAAI,CAACe,YAAY,EAAE;gBACjBK,QAAQ,CAAC7G,IAAI,GAAG,EAAE;gBAClB;cACF;cACA6G,QAAQ,CAAC7G,IAAI,GAAG,CAAC;cACjB,OAAOwG,YAAY,CAACf,IAAI,EAAEY,YAAY,CAAC;YACzC,KAAK,CAAC;cACJlG,MAAM,GAAG0G,QAAQ,CAACE,IAAI;cACtB,IAAI,EAAE5G,MAAM,KAAK,KAAK,CAAC,EAAE;gBACvB0G,QAAQ,CAAC7G,IAAI,GAAG,CAAC;gBACjB;cACF;cACA,OAAO6G,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzC,KAAK,CAAC;cACJ;cACA,OAAOvB,IAAI,CAACnE,WAAW,CAAC;cACxB,IAAI,EAAEnB,MAAM,KAAKmB,WAAW,CAAC,EAAE;gBAC7BuF,QAAQ,CAAC7G,IAAI,GAAG,EAAE;gBAClB;cACF;cACA4F,MAAM,CAACqB,cAAc,CAACxB,IAAI,EAAEnE,WAAW,EAAE;gBACvC5B,KAAK,EAAE,IAAI;gBACXwH,YAAY,EAAE;cAChB,CAAC,CAAC;cACF,OAAOL,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzC,KAAK,EAAE;cACL,IAAI/H,OAAO,CAACkB,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,EAAE;gBAC1CuG,UAAU,GAAGvG,MAAM;cACrB;YACF,KAAK,EAAE;cACL,IAAI,CAACsG,aAAa,EAAE;gBAClBI,QAAQ,CAAC7G,IAAI,GAAG,EAAE;gBAClB;cACF;cACA6G,QAAQ,CAAC7G,IAAI,GAAG,EAAE;cAClB,OAAOyG,aAAa,CAACC,UAAU,CAAC;YAClC,KAAK,EAAE;cACLA,UAAU,GAAGG,QAAQ,CAACE,IAAI;YAC5B,KAAK,EAAE;cACL,OAAOF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEN,UAAU,CAAC;YAC9C,KAAK,EAAE;YACP,KAAK,KAAK;cACR,OAAOG,QAAQ,CAACM,IAAI,CAAC,CAAC;UAC1B;QACF;MACF,CAAC,EAAEZ,OAAO,CAAC;IACb,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAIa,YAAY,GAAG,SAASA,YAAYA,CAACC,iBAAiB,EAAE;IAC1D;IACA,IAAIC,oBAAoB,GAAGD,iBAAiB,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAE;MAClE,OAAO,CAACA,IAAI,CAAC/B,IAAI,CAACnE,WAAW,CAAC;IAChC,CAAC,CAAC;IACF;IACA,IAAI,CAACgG,oBAAoB,CAACG,MAAM,EAAE;MAChC;IACF;IACA,IAAIC,cAAc,GAAGJ,oBAAoB,CAACK,GAAG,CAAC,UAAUH,IAAI,EAAE;MAC5D,OAAOtG,QAAQ,CAACsG,IAAI,CAAC/B,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF;IACA,IAAImC,WAAW,GAAG1I,kBAAkB,CAACwF,cAAc,CAAC;IACpDgD,cAAc,CAAClC,OAAO,CAAC,UAAUqC,OAAO,EAAE;MACxC;MACAD,WAAW,GAAGvG,cAAc,CAACwG,OAAO,EAAED,WAAW,CAAC;IACpD,CAAC,CAAC;IACFF,cAAc,CAAClC,OAAO,CAAC,UAAUqC,OAAO,EAAEnC,KAAK,EAAE;MAC/C;MACA,IAAIoC,cAAc,GAAGD,OAAO;MAC5B,IAAI,CAACP,oBAAoB,CAAC5B,KAAK,CAAC,CAACgB,UAAU,EAAE;QAC3C;QACA,IAAIqB,aAAa,GAAGF,OAAO,CAACE,aAAa;QACzC,IAAIC,KAAK;QACT,IAAI;UACFA,KAAK,GAAG,IAAIC,IAAI,CAAC,CAACF,aAAa,CAAC,EAAEA,aAAa,CAACG,IAAI,EAAE;YACpD7E,IAAI,EAAE0E,aAAa,CAAC1E;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOpD,CAAC,EAAE;UACV+H,KAAK,GAAG,IAAIG,IAAI,CAAC,CAACJ,aAAa,CAAC,EAAE;YAChC1E,IAAI,EAAE0E,aAAa,CAAC1E;UACtB,CAAC,CAAC;UACF2E,KAAK,CAACE,IAAI,GAAGH,aAAa,CAACG,IAAI;UAC/BF,KAAK,CAACI,gBAAgB,GAAG,IAAI5G,IAAI,CAAC,CAAC;UACnCwG,KAAK,CAACK,YAAY,GAAG,IAAI7G,IAAI,CAAC,CAAC,CAAC8G,OAAO,CAAC,CAAC;QAC3C;QACAN,KAAK,CAACrC,GAAG,GAAGkC,OAAO,CAAClC,GAAG;QACvBmC,cAAc,GAAGE,KAAK;MACxB,CAAC,MAAM;QACL;QACAH,OAAO,CAACU,MAAM,GAAG,WAAW;MAC9B;MACAzC,gBAAgB,CAACgC,cAAc,EAAEF,WAAW,CAAC;IAC/C,CAAC,CAAC;EACJ,CAAC;EACD,IAAIY,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAEhD,IAAI,EAAEiD,GAAG,EAAE;IACtD,IAAI;MACF,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOxI,CAAC,EAAE;MACV;IAAA;IAEF;IACA,IAAI,CAACkB,WAAW,CAACsE,IAAI,EAAEf,cAAc,CAAC,EAAE;MACtC;IACF;IACA,IAAImE,UAAU,GAAG3H,QAAQ,CAACuE,IAAI,CAAC;IAC/BoD,UAAU,CAACN,MAAM,GAAG,MAAM;IAC1BM,UAAU,CAACC,OAAO,GAAG,GAAG;IACxBD,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;IAC9BI,UAAU,CAACH,GAAG,GAAGA,GAAG;IACpB,IAAIK,YAAY,GAAG1H,cAAc,CAACwH,UAAU,EAAEnE,cAAc,CAAC;IAC7DoB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,CAAC;EAC5C,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAC/I,CAAC,EAAEwF,IAAI,EAAE;IAC5C;IACA,IAAI,CAACtE,WAAW,CAACsE,IAAI,EAAEf,cAAc,CAAC,EAAE;MACtC;IACF;IACA,IAAImE,UAAU,GAAG3H,QAAQ,CAACuE,IAAI,CAAC;IAC/BoD,UAAU,CAACN,MAAM,GAAG,WAAW;IAC/BM,UAAU,CAACC,OAAO,GAAG7I,CAAC,CAAC6I,OAAO;IAC9B,IAAIC,YAAY,GAAG1H,cAAc,CAACwH,UAAU,EAAEnE,cAAc,CAAC;IAC7DoB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,EAAE9I,CAAC,CAAC;EAC/C,CAAC;EACD,IAAIgJ,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAET,QAAQ,EAAEhD,IAAI,EAAE;IACpD;IACA,IAAI,CAACtE,WAAW,CAACsE,IAAI,EAAEf,cAAc,CAAC,EAAE;MACtC;IACF;IACA,IAAImE,UAAU,GAAG3H,QAAQ,CAACuE,IAAI,CAAC;IAC/BoD,UAAU,CAACK,KAAK,GAAGA,KAAK;IACxBL,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;IAC9BI,UAAU,CAACN,MAAM,GAAG,OAAO;IAC3B,IAAIQ,YAAY,GAAG1H,cAAc,CAACwH,UAAU,EAAEnE,cAAc,CAAC;IAC7DoB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,CAAC;EAC5C,CAAC;EACD,IAAII,YAAY,GAAG,SAASA,YAAYA,CAAC1D,IAAI,EAAE;IAC7C,IAAI2D,WAAW;IACfxJ,OAAO,CAACD,OAAO,CAAC,OAAOqC,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACyD,IAAI,CAAC,GAAGzD,QAAQ,CAAC,CAAC3B,IAAI,CAAC,UAAUgJ,GAAG,EAAE;MAC9F,IAAIC,EAAE;MACN;MACA,IAAID,GAAG,KAAK,KAAK,EAAE;QACjB;MACF;MACA,IAAIE,eAAe,GAAGnI,cAAc,CAACqE,IAAI,EAAEf,cAAc,CAAC;MAC1D,IAAI6E,eAAe,EAAE;QACnBH,WAAW,GAAGrK,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0G,IAAI,CAAC,EAAE;UACzC8C,MAAM,EAAE;QACV,CAAC,CAAC;QACF7D,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACc,OAAO,CAAC,UAAUgE,IAAI,EAAE;UACrG,IAAIC,QAAQ,GAAGL,WAAW,CAACzD,GAAG,KAAK+D,SAAS,GAAG,KAAK,GAAG,MAAM;UAC7D,IAAIF,IAAI,CAACC,QAAQ,CAAC,KAAKL,WAAW,CAACK,QAAQ,CAAC,IAAI,CAAC7D,MAAM,CAACC,QAAQ,CAAC2D,IAAI,CAAC,EAAE;YACtEA,IAAI,CAACjB,MAAM,GAAG,SAAS;UACzB;QACF,CAAC,CAAC;QACF,CAACe,EAAE,GAAGrE,MAAM,CAAC0E,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAACR,WAAW,CAAC;QAChFtD,gBAAgB,CAACsD,WAAW,EAAEG,eAAe,CAAC;MAChD;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIM,UAAU,GAAG,SAASA,UAAUA,CAAC5J,CAAC,EAAE;IACtC+E,YAAY,CAAC/E,CAAC,CAACoD,IAAI,CAAC;IACpB,IAAIpD,CAAC,CAACoD,IAAI,KAAK,MAAM,EAAE;MACrBb,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvC,CAAC,CAAC;IAC3D;EACF,CAAC;EACD;EACAS,KAAK,CAACoJ,mBAAmB,CAAClI,GAAG,EAAE,YAAY;IACzC,OAAO;MACLwF,YAAY,EAAEA,YAAY;MAC1BoB,SAAS,EAAEA,SAAS;MACpBQ,UAAU,EAAEA,UAAU;MACtBC,OAAO,EAAEA,OAAO;MAChBnH,QAAQ,EAAE4C,cAAc;MACxBO,MAAM,EAAEA,MAAM,CAAC0E;IACjB,CAAC;EACH,CAAC,CAAC;EACF,IAAII,iBAAiB,GAAGrJ,KAAK,CAAC0D,UAAU,CAACxD,aAAa,CAAC;IACrDoJ,YAAY,GAAGD,iBAAiB,CAACC,YAAY;IAC7CC,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,IAAI/G,SAAS,GAAG8G,YAAY,CAAC,QAAQ,EAAE/G,kBAAkB,CAAC;EAC1D,IAAIiH,aAAa,GAAGnL,QAAQ,CAACA,QAAQ,CAAC;IACpCqI,YAAY,EAAEA,YAAY;IAC1B6B,OAAO,EAAEA,OAAO;IAChBD,UAAU,EAAEA,UAAU;IACtBR,SAAS,EAAEA;EACb,CAAC,EAAE7G,KAAK,CAAC,EAAE;IACTgC,IAAI,EAAEA,IAAI;IACVE,QAAQ,EAAEA,QAAQ;IAClBE,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA,MAAM;IACdE,mBAAmB,EAAEA,mBAAmB;IACxCjB,SAAS,EAAEA,SAAS;IACpBP,QAAQ,EAAE0B,cAAc;IACxBmC,YAAY,EAAEJ,kBAAkB;IAChC7D,QAAQ,EAAEmH;EACZ,CAAC,CAAC;EACF,OAAOQ,aAAa,CAAC/G,SAAS;EAC9B,OAAO+G,aAAa,CAAC3G,KAAK;EAC1B;EACA;EACA;EACA;EACA,IAAI,CAACD,QAAQ,IAAIe,cAAc,EAAE;IAC/B,OAAO6F,aAAa,CAACC,EAAE;EACzB;EACA,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,MAAM,EAAEC,aAAa,EAAE;IACtE,OAAOpI,cAAc,GAAG,aAAaxB,KAAK,CAAC6J,aAAa,CAACzJ,cAAc,EAAE;MACvE0J,aAAa,EAAE,QAAQ;MACvBzJ,aAAa,EAAEA,aAAa,CAAC0J;IAC/B,CAAC,EAAE,UAAUC,aAAa,EAAE;MAC1B,IAAIC,IAAI,GAAG,OAAOzI,cAAc,KAAK,SAAS,GAAG,CAAC,CAAC,GAAGA,cAAc;QAClE0I,cAAc,GAAGD,IAAI,CAACC,cAAc;QACpCC,eAAe,GAAGF,IAAI,CAACE,eAAe;QACtCC,gBAAgB,GAAGH,IAAI,CAACG,gBAAgB;QACxCC,UAAU,GAAGJ,IAAI,CAACI,UAAU;QAC5BC,WAAW,GAAGL,IAAI,CAACK,WAAW;QAC9BC,YAAY,GAAGN,IAAI,CAACM,YAAY;MAClC,OAAO,aAAavK,KAAK,CAAC6J,aAAa,CAACtJ,UAAU,EAAE;QAClDiC,SAAS,EAAEA,SAAS;QACpBd,QAAQ,EAAEA,QAAQ;QAClB8I,KAAK,EAAExG,cAAc;QACrBjC,WAAW,EAAEA,WAAW;QACxBJ,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBN,QAAQ,EAAEmH,YAAY;QACtByB,cAAc,EAAE,CAACvG,cAAc,IAAIuG,cAAc;QACjDC,eAAe,EAAEA,eAAe;QAChCC,gBAAgB,EAAEA,gBAAgB;QAClCC,UAAU,EAAEA,UAAU;QACtBC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA,YAAY;QAC1BnI,UAAU,EAAEA,UAAU;QACtBD,MAAM,EAAE9D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2L,aAAa,CAAC,EAAE9H,UAAU,CAAC;QACzDG,UAAU,EAAEA,UAAU;QACtBC,QAAQ,EAAEA,QAAQ;QAClBmI,YAAY,EAAEd,MAAM;QACpBe,mBAAmB,EAAEd,aAAa;QAClC9G,UAAU,EAAEA;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,GAAG6G,MAAM;EACb,CAAC;EACD,IAAIhH,IAAI,KAAK,MAAM,EAAE;IACnB,IAAIgI,WAAW;IACf,IAAIC,OAAO,GAAG/K,UAAU,CAAC2C,SAAS,GAAGmI,WAAW,GAAG,CAAC,CAAC,EAAEvM,eAAe,CAACuM,WAAW,EAAE,EAAE,CAAC9J,MAAM,CAAC2B,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAEpE,eAAe,CAACuM,WAAW,EAAE,EAAE,CAAC9J,MAAM,CAAC2B,SAAS,EAAE,iBAAiB,CAAC,EAAEwB,cAAc,CAAC6G,IAAI,CAAC,UAAU9F,IAAI,EAAE;MAClO,OAAOA,IAAI,CAAC8C,MAAM,KAAK,WAAW;IACpC,CAAC,CAAC,CAAC,EAAEzJ,eAAe,CAACuM,WAAW,EAAE,EAAE,CAAC9J,MAAM,CAAC2B,SAAS,EAAE,aAAa,CAAC,EAAE6B,SAAS,KAAK,UAAU,CAAC,EAAEjG,eAAe,CAACuM,WAAW,EAAE,EAAE,CAAC9J,MAAM,CAAC2B,SAAS,EAAE,WAAW,CAAC,EAAEmB,cAAc,CAAC,EAAEvF,eAAe,CAACuM,WAAW,EAAE,EAAE,CAAC9J,MAAM,CAAC2B,SAAS,EAAE,MAAM,CAAC,EAAE+G,SAAS,KAAK,KAAK,CAAC,EAAEoB,WAAW,GAAGlI,SAAS,CAAC;IAC5R,OAAO,aAAazC,KAAK,CAAC6J,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa7J,KAAK,CAAC6J,aAAa,CAAC,KAAK,EAAE;MAC5FpH,SAAS,EAAEmI,OAAO;MAClB9I,MAAM,EAAEqH,UAAU;MAClB2B,UAAU,EAAE3B,UAAU;MACtB4B,WAAW,EAAE5B,UAAU;MACvBtG,KAAK,EAAEA;IACT,CAAC,EAAE,aAAa7C,KAAK,CAAC6J,aAAa,CAAC/J,QAAQ,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEmL,aAAa,EAAE;MACxEtI,GAAG,EAAEqD,MAAM;MACX9B,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAAC2B,SAAS,EAAE,MAAM;IACxC,CAAC,CAAC,EAAE,aAAaxC,KAAK,CAAC6J,aAAa,CAAC,KAAK,EAAE;MAC1CpH,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAAC2B,SAAS,EAAE,iBAAiB;IACnD,CAAC,EAAEI,QAAQ,CAAC,CAAC,CAAC,EAAE8G,gBAAgB,CAAC,CAAC,CAAC;EACrC;EACA,IAAIsB,eAAe,GAAGnL,UAAU,CAAC2C,SAAS,GAAGrB,YAAY,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACN,MAAM,CAAC2B,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,EAAEpE,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACN,MAAM,CAAC2B,SAAS,EAAE,UAAU,CAAC,CAAC3B,MAAM,CAACa,QAAQ,CAAC,EAAE,IAAI,CAAC,EAAEtD,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACN,MAAM,CAAC2B,SAAS,EAAE,WAAW,CAAC,EAAEmB,cAAc,CAAC,EAAEvF,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACN,MAAM,CAAC2B,SAAS,EAAE,MAAM,CAAC,EAAE+G,SAAS,KAAK,KAAK,CAAC,EAAEpI,YAAY,CAAC,CAAC;EAChZ,IAAI8J,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,iBAAiB,EAAE;IACtE,OAAO,aAAalL,KAAK,CAAC6J,aAAa,CAAC,KAAK,EAAE;MAC7CpH,SAAS,EAAEuI,eAAe;MAC1BnI,KAAK,EAAEqI;IACT,CAAC,EAAE,aAAalL,KAAK,CAAC6J,aAAa,CAAC/J,QAAQ,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEmL,aAAa,EAAE;MACxEtI,GAAG,EAAEqD;IACP,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;EACD,IAAI4G,YAAY,GAAGF,kBAAkB,CAACrI,QAAQ,GAAGoG,SAAS,GAAG;IAC3DoC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,IAAI1J,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO,aAAa1B,KAAK,CAAC6J,aAAa,CAAC,MAAM,EAAE;MAC9CpH,SAAS,EAAE5C,UAAU,CAAC,EAAE,CAACgB,MAAM,CAAC2B,SAAS,EAAE,uBAAuB,CAAC,EAAEC,SAAS;IAChF,CAAC,EAAEiH,gBAAgB,CAACyB,YAAY,EAAE,CAAC,CAACvI,QAAQ,CAAC,CAAC;EAChD;EACA,OAAO,aAAa5C,KAAK,CAAC6J,aAAa,CAAC,MAAM,EAAE;IAC9CpH,SAAS,EAAEA;EACb,CAAC,EAAE0I,YAAY,EAAEzB,gBAAgB,CAAC,CAAC,CAAC;AACtC,CAAC;AACD,IAAIK,MAAM,GAAG,aAAa/J,KAAK,CAACqL,UAAU,CAACrK,cAAc,CAAC;AAC1D,IAAIyD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCoF,MAAM,CAACuB,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAevB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}