{"ast": null, "code": "/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;", "map": {"version": 3, "names": ["Column", "_"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/Column.js"], "sourcesContent": ["/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,IAAI;AACb;AACA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}