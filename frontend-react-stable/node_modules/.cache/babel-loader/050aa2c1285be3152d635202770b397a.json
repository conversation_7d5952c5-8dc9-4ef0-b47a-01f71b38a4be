{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nexport default function useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = _objectSpread({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  } // Enable tabPane animation if provide motion\n\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n    mergedAnimated.tabPane = false;\n  }\n  return mergedAnimated;\n}", "map": {"version": 3, "names": ["_typeof", "_objectSpread", "warning", "useAnimateConfig", "animated", "arguments", "length", "undefined", "inkBar", "tabPane", "mergedAnimated", "tabPaneMotion", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/hooks/useAnimateConfig.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nexport default function useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = _objectSpread({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  } // Enable tabPane animation if provide motion\n\n\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n\n    mergedAnimated.tabPane = false;\n  }\n\n  return mergedAnimated;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IACjFG,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,cAAc;EAElB,IAAIN,QAAQ,KAAK,KAAK,EAAE;IACtBM,cAAc,GAAG;MACfF,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIL,QAAQ,KAAK,IAAI,EAAE;IAC5BM,cAAc,GAAG;MACfF,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM;IACLC,cAAc,GAAGT,aAAa,CAAC;MAC7BO,MAAM,EAAE;IACV,CAAC,EAAER,OAAO,CAACI,QAAQ,CAAC,KAAK,QAAQ,GAAGA,QAAQ,GAAG,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;;EAGF,IAAIM,cAAc,CAACC,aAAa,IAAID,cAAc,CAACD,OAAO,KAAKF,SAAS,EAAE;IACxEG,cAAc,CAACD,OAAO,GAAG,IAAI;EAC/B;EAEA,IAAI,CAACC,cAAc,CAACC,aAAa,IAAID,cAAc,CAACD,OAAO,EAAE;IAC3D,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCZ,OAAO,CAAC,KAAK,EAAE,gGAAgG,CAAC;IAClH;IAEAQ,cAAc,CAACD,OAAO,GAAG,KAAK;EAChC;EAEA,OAAOC,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}