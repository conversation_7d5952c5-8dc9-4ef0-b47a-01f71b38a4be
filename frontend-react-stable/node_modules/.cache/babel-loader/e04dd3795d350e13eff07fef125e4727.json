{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcMinutes = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nvar _duration = require(\"./duration.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar utcMinute = (0, _interval.default)(function (date) {\n  date.setUTCSeconds(0, 0);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationMinute);\n}, function (start, end) {\n  return (end - start) / _duration.durationMinute;\n}, function (date) {\n  return date.getUTCMinutes();\n});\nvar _default = utcMinute;\nexports.default = _default;\nvar utcMinutes = utcMinute.range;\nexports.utcMinutes = utcMinutes;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcMinutes", "default", "_interval", "_interopRequireDefault", "require", "_duration", "obj", "__esModule", "utcMinute", "date", "setUTCSeconds", "step", "setTime", "durationMinute", "start", "end", "getUTCMinutes", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/utcMinute.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcMinutes = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nvar _duration = require(\"./duration.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar utcMinute = (0, _interval.default)(function (date) {\n  date.setUTCSeconds(0, 0);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationMinute);\n}, function (start, end) {\n  return (end - start) / _duration.durationMinute;\n}, function (date) {\n  return date.getUTCMinutes();\n});\nvar _default = utcMinute;\nexports.default = _default;\nvar utcMinutes = utcMinute.range;\nexports.utcMinutes = utcMinutes;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE7C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,SAAS,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACD,OAAO,EAAE,UAAUQ,IAAI,EAAE;EACrDA,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC,EAAE,UAAUD,IAAI,EAAEE,IAAI,EAAE;EACvBF,IAAI,CAACG,OAAO,CAAC,CAACH,IAAI,GAAGE,IAAI,GAAGN,SAAS,CAACQ,cAAc,CAAC;AACvD,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIT,SAAS,CAACQ,cAAc;AACjD,CAAC,EAAE,UAAUJ,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACO,aAAa,CAAC,CAAC;AAC7B,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGT,SAAS;AACxBV,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,UAAU,GAAGQ,SAAS,CAACU,KAAK;AAChCpB,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}