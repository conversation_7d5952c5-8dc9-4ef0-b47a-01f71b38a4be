{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nvar Cell = function Cell(_ref) {\n  var itemPrefixCls = _ref.itemPrefixCls,\n    component = _ref.component,\n    span = _ref.span,\n    className = _ref.className,\n    style = _ref.style,\n    labelStyle = _ref.labelStyle,\n    contentStyle = _ref.contentStyle,\n    bordered = _ref.bordered,\n    label = _ref.label,\n    content = _ref.content,\n    colon = _ref.colon;\n  var Component = component;\n  if (bordered) {\n    var _classNames;\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(itemPrefixCls, \"-item-label\"), notEmpty(label)), _defineProperty(_classNames, \"\".concat(itemPrefixCls, \"-item-content\"), notEmpty(content)), _classNames), className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: labelStyle\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: contentStyle\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item\"), className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(itemPrefixCls, \"-item-container\")\n  }, (label || label === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item-label\"), _defineProperty({}, \"\".concat(itemPrefixCls, \"-item-no-colon\"), !colon)),\n    style: labelStyle\n  }, label), (content || content === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item-content\")),\n    style: contentStyle\n  }, content)));\n};\nexport default Cell;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "notEmpty", "val", "undefined", "Cell", "_ref", "itemPrefixCls", "component", "span", "className", "style", "labelStyle", "contentStyle", "bordered", "label", "content", "colon", "Component", "_classNames", "createElement", "concat", "colSpan"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/descriptions/Cell.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nvar Cell = function Cell(_ref) {\n  var itemPrefixCls = _ref.itemPrefixCls,\n    component = _ref.component,\n    span = _ref.span,\n    className = _ref.className,\n    style = _ref.style,\n    labelStyle = _ref.labelStyle,\n    contentStyle = _ref.contentStyle,\n    bordered = _ref.bordered,\n    label = _ref.label,\n    content = _ref.content,\n    colon = _ref.colon;\n  var Component = component;\n  if (bordered) {\n    var _classNames;\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(itemPrefixCls, \"-item-label\"), notEmpty(label)), _defineProperty(_classNames, \"\".concat(itemPrefixCls, \"-item-content\"), notEmpty(content)), _classNames), className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: labelStyle\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: contentStyle\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item\"), className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(itemPrefixCls, \"-item-container\")\n  }, (label || label === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item-label\"), _defineProperty({}, \"\".concat(itemPrefixCls, \"-item-no-colon\"), !colon)),\n    style: labelStyle\n  }, label), (content || content === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(itemPrefixCls, \"-item-content\")),\n    style: contentStyle\n  }, content)));\n};\nexport default Cell;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI;AAC1C;AACA,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAE;EAC7B,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;IACpCC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC1BC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC5BC,YAAY,GAAGP,IAAI,CAACO,YAAY;IAChCC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,KAAK,GAAGT,IAAI,CAACS,KAAK;IAClBC,OAAO,GAAGV,IAAI,CAACU,OAAO;IACtBC,KAAK,GAAGX,IAAI,CAACW,KAAK;EACpB,IAAIC,SAAS,GAAGV,SAAS;EACzB,IAAIM,QAAQ,EAAE;IACZ,IAAIK,WAAW;IACf,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAACF,SAAS,EAAE;MACjDR,SAAS,EAAEV,UAAU,EAAEmB,WAAW,GAAG,CAAC,CAAC,EAAEpB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACE,MAAM,CAACd,aAAa,EAAE,aAAa,CAAC,EAAEL,QAAQ,CAACa,KAAK,CAAC,CAAC,EAAEhB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACE,MAAM,CAACd,aAAa,EAAE,eAAe,CAAC,EAAEL,QAAQ,CAACc,OAAO,CAAC,CAAC,EAAEG,WAAW,GAAGT,SAAS,CAAC;MACrPC,KAAK,EAAEA,KAAK;MACZW,OAAO,EAAEb;IACX,CAAC,EAAEP,QAAQ,CAACa,KAAK,CAAC,IAAI,aAAad,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC7DT,KAAK,EAAEC;IACT,CAAC,EAAEG,KAAK,CAAC,EAAEb,QAAQ,CAACc,OAAO,CAAC,IAAI,aAAaf,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MACvET,KAAK,EAAEE;IACT,CAAC,EAAEG,OAAO,CAAC,CAAC;EACd;EACA,OAAO,aAAaf,KAAK,CAACmB,aAAa,CAACF,SAAS,EAAE;IACjDR,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACd,aAAa,EAAE,OAAO,CAAC,EAAEG,SAAS,CAAC;IACnEC,KAAK,EAAEA,KAAK;IACZW,OAAO,EAAEb;EACX,CAAC,EAAE,aAAaR,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCV,SAAS,EAAE,EAAE,CAACW,MAAM,CAACd,aAAa,EAAE,iBAAiB;EACvD,CAAC,EAAE,CAACQ,KAAK,IAAIA,KAAK,KAAK,CAAC,KAAK,aAAad,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IACpEV,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACd,aAAa,EAAE,aAAa,CAAC,EAAER,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsB,MAAM,CAACd,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAACU,KAAK,CAAC,CAAC;IACvIN,KAAK,EAAEC;EACT,CAAC,EAAEG,KAAK,CAAC,EAAE,CAACC,OAAO,IAAIA,OAAO,KAAK,CAAC,KAAK,aAAaf,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IAChFV,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACd,aAAa,EAAE,eAAe,CAAC,CAAC;IAChEI,KAAK,EAAEE;EACT,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC;AACf,CAAC;AACD,eAAeX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}