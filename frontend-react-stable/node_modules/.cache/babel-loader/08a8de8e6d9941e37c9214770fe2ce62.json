{"ast": null, "code": "import { PresetColorTypes } from '../_util/colors';\n// eslint-disable-next-line import/prefer-default-export\nexport function isPresetColor(color) {\n  return PresetColorTypes.includes(color);\n}", "map": {"version": 3, "names": ["PresetColorTypes", "isPresetColor", "color", "includes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/badge/utils.js"], "sourcesContent": ["import { PresetColorTypes } from '../_util/colors';\n// eslint-disable-next-line import/prefer-default-export\nexport function isPresetColor(color) {\n  return PresetColorTypes.includes(color);\n}"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAOF,gBAAgB,CAACG,QAAQ,CAACD,KAAK,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}