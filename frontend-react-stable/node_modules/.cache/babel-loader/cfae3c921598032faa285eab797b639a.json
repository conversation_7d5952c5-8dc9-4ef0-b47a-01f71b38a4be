{"ast": null, "code": "import _isFunction from \"lodash/isFunction\";\nvar _excluded = [\"component\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n/**\n * @fileOverview Customized\n */\nimport React, { isValidElement, cloneElement, createElement } from 'react';\nimport { Layer } from '../container/Layer';\nimport { warn } from '../util/LogUtils';\n/**\n * custom svg elements by rechart instance props and state.\n * @returns {Object}   svg elements\n */\nexport function Customized(_ref) {\n  var component = _ref.component,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var child;\n  if (/*#__PURE__*/isValidElement(component)) {\n    child = /*#__PURE__*/cloneElement(component, props);\n  } else if (_isFunction(component)) {\n    child = /*#__PURE__*/createElement(component, props);\n  } else {\n    warn(false, \"Customized's props `component` must be React.element or Function, but got %s.\", _typeof(component));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-customized-wrapper\"\n  }, child);\n}\nCustomized.displayName = 'Customized';", "map": {"version": 3, "names": ["_isFunction", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "React", "isValidElement", "cloneElement", "createElement", "Layer", "warn", "Customized", "_ref", "component", "props", "child", "className", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/Customized.js"], "sourcesContent": ["import _isFunction from \"lodash/isFunction\";\nvar _excluded = [\"component\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n/**\n * @fileOverview Customized\n */\nimport React, { isValidElement, cloneElement, createElement } from 'react';\nimport { Layer } from '../container/Layer';\nimport { warn } from '../util/LogUtils';\n/**\n * custom svg elements by rechart instance props and state.\n * @returns {Object}   svg elements\n */\nexport function Customized(_ref) {\n  var component = _ref.component,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var child;\n  if ( /*#__PURE__*/isValidElement(component)) {\n    child = /*#__PURE__*/cloneElement(component, props);\n  } else if (_isFunction(component)) {\n    child = /*#__PURE__*/createElement(component, props);\n  } else {\n    warn(false, \"Customized's props `component` must be React.element or Function, but got %s.\", _typeof(component));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-customized-wrapper\"\n  }, child);\n}\nCustomized.displayName = 'Customized';"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,IAAIC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC7B,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT;AACA;AACA;AACA,OAAOa,KAAK,IAAIC,cAAc,EAAEC,YAAY,EAAEC,aAAa,QAAQ,OAAO;AAC1E,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,kBAAkB;AACvC;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGzB,wBAAwB,CAACuB,IAAI,EAAE9B,SAAS,CAAC;EACnD,IAAIiC,KAAK;EACT,IAAK,aAAaT,cAAc,CAACO,SAAS,CAAC,EAAE;IAC3CE,KAAK,GAAG,aAAaR,YAAY,CAACM,SAAS,EAAEC,KAAK,CAAC;EACrD,CAAC,MAAM,IAAIjC,WAAW,CAACgC,SAAS,CAAC,EAAE;IACjCE,KAAK,GAAG,aAAaP,aAAa,CAACK,SAAS,EAAEC,KAAK,CAAC;EACtD,CAAC,MAAM;IACLJ,IAAI,CAAC,KAAK,EAAE,+EAA+E,EAAE3B,OAAO,CAAC8B,SAAS,CAAC,CAAC;EAClH;EACA,OAAO,aAAaR,KAAK,CAACG,aAAa,CAACC,KAAK,EAAE;IAC7CO,SAAS,EAAE;EACb,CAAC,EAAED,KAAK,CAAC;AACX;AACAJ,UAAU,CAACM,WAAW,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}