{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default (function (stretch) {\n  var _React$useState = React.useState({\n      width: 0,\n      height: 0\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    targetSize = _React$useState2[0],\n    setTargetSize = _React$useState2[1];\n  function measureStretch(element) {\n    var tgtWidth = element.offsetWidth,\n      tgtHeight = element.offsetHeight;\n    var _element$getBoundingC = element.getBoundingClientRect(),\n      width = _element$getBoundingC.width,\n      height = _element$getBoundingC.height; // Rect is more accurate than offset, use if near\n\n    if (Math.abs(tgtWidth - width) < 1 && Math.abs(tgtHeight - height) < 1) {\n      tgtWidth = width;\n      tgtHeight = height;\n    }\n    setTargetSize({\n      width: tgtWidth,\n      height: tgtHeight\n    });\n  } // Merge stretch style\n\n  var style = React.useMemo(function () {\n    var sizeStyle = {};\n    if (stretch) {\n      var width = targetSize.width,\n        height = targetSize.height; // Stretch with target\n\n      if (stretch.indexOf('height') !== -1 && height) {\n        sizeStyle.height = height;\n      } else if (stretch.indexOf('minHeight') !== -1 && height) {\n        sizeStyle.minHeight = height;\n      }\n      if (stretch.indexOf('width') !== -1 && width) {\n        sizeStyle.width = width;\n      } else if (stretch.indexOf('minWidth') !== -1 && width) {\n        sizeStyle.minWidth = width;\n      }\n    }\n    return sizeStyle;\n  }, [stretch, targetSize]);\n  return [style, measureStretch];\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "stretch", "_React$useState", "useState", "width", "height", "_React$useState2", "targetSize", "setTargetSize", "measureStretch", "element", "tgtWidth", "offsetWidth", "tgtHeight", "offsetHeight", "_element$getBoundingC", "getBoundingClientRect", "Math", "abs", "style", "useMemo", "sizeStyle", "indexOf", "minHeight", "min<PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/Popup/useStretchStyle.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default (function (stretch) {\n  var _React$useState = React.useState({\n    width: 0,\n    height: 0\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      targetSize = _React$useState2[0],\n      setTargetSize = _React$useState2[1];\n\n  function measureStretch(element) {\n    var tgtWidth = element.offsetWidth,\n        tgtHeight = element.offsetHeight;\n\n    var _element$getBoundingC = element.getBoundingClientRect(),\n        width = _element$getBoundingC.width,\n        height = _element$getBoundingC.height; // Rect is more accurate than offset, use if near\n\n\n    if (Math.abs(tgtWidth - width) < 1 && Math.abs(tgtHeight - height) < 1) {\n      tgtWidth = width;\n      tgtHeight = height;\n    }\n\n    setTargetSize({\n      width: tgtWidth,\n      height: tgtHeight\n    });\n  } // Merge stretch style\n\n\n  var style = React.useMemo(function () {\n    var sizeStyle = {};\n\n    if (stretch) {\n      var width = targetSize.width,\n          height = targetSize.height; // Stretch with target\n\n      if (stretch.indexOf('height') !== -1 && height) {\n        sizeStyle.height = height;\n      } else if (stretch.indexOf('minHeight') !== -1 && height) {\n        sizeStyle.minHeight = height;\n      }\n\n      if (stretch.indexOf('width') !== -1 && width) {\n        sizeStyle.width = width;\n      } else if (stretch.indexOf('minWidth') !== -1 && width) {\n        sizeStyle.minWidth = width;\n      }\n    }\n\n    return sizeStyle;\n  }, [stretch, targetSize]);\n  return [style, measureStretch];\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,gBAAgB,UAAUC,OAAO,EAAE;EACjC,IAAIC,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAAC;MACnCC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IACEC,gBAAgB,GAAGP,cAAc,CAACG,eAAe,EAAE,CAAC,CAAC;IACrDK,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,SAASG,cAAcA,CAACC,OAAO,EAAE;IAC/B,IAAIC,QAAQ,GAAGD,OAAO,CAACE,WAAW;MAC9BC,SAAS,GAAGH,OAAO,CAACI,YAAY;IAEpC,IAAIC,qBAAqB,GAAGL,OAAO,CAACM,qBAAqB,CAAC,CAAC;MACvDZ,KAAK,GAAGW,qBAAqB,CAACX,KAAK;MACnCC,MAAM,GAAGU,qBAAqB,CAACV,MAAM,CAAC,CAAC;;IAG3C,IAAIY,IAAI,CAACC,GAAG,CAACP,QAAQ,GAAGP,KAAK,CAAC,GAAG,CAAC,IAAIa,IAAI,CAACC,GAAG,CAACL,SAAS,GAAGR,MAAM,CAAC,GAAG,CAAC,EAAE;MACtEM,QAAQ,GAAGP,KAAK;MAChBS,SAAS,GAAGR,MAAM;IACpB;IAEAG,aAAa,CAAC;MACZJ,KAAK,EAAEO,QAAQ;MACfN,MAAM,EAAEQ;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIM,KAAK,GAAGnB,KAAK,CAACoB,OAAO,CAAC,YAAY;IACpC,IAAIC,SAAS,GAAG,CAAC,CAAC;IAElB,IAAIpB,OAAO,EAAE;MACX,IAAIG,KAAK,GAAGG,UAAU,CAACH,KAAK;QACxBC,MAAM,GAAGE,UAAU,CAACF,MAAM,CAAC,CAAC;;MAEhC,IAAIJ,OAAO,CAACqB,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAIjB,MAAM,EAAE;QAC9CgB,SAAS,CAAChB,MAAM,GAAGA,MAAM;MAC3B,CAAC,MAAM,IAAIJ,OAAO,CAACqB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAIjB,MAAM,EAAE;QACxDgB,SAAS,CAACE,SAAS,GAAGlB,MAAM;MAC9B;MAEA,IAAIJ,OAAO,CAACqB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIlB,KAAK,EAAE;QAC5CiB,SAAS,CAACjB,KAAK,GAAGA,KAAK;MACzB,CAAC,MAAM,IAAIH,OAAO,CAACqB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIlB,KAAK,EAAE;QACtDiB,SAAS,CAACG,QAAQ,GAAGpB,KAAK;MAC5B;IACF;IAEA,OAAOiB,SAAS;EAClB,CAAC,EAAE,CAACpB,OAAO,EAAEM,UAAU,CAAC,CAAC;EACzB,OAAO,CAACY,KAAK,EAAEV,cAAc,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}