{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = merge;\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\nfunction merge(arrays) {\n  return Array.from(flatten(arrays));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "merge", "flatten", "arrays", "array", "Array", "from"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/merge.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = merge;\n\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nfunction merge(arrays) {\n  return Array.from(flatten(arrays));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,UAAUC,OAAOA,CAACC,MAAM,EAAE;EACxB,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;IAC1B,OAAOC,KAAK;EACd;AACF;AAEA,SAASH,KAAKA,CAACE,MAAM,EAAE;EACrB,OAAOE,KAAK,CAACC,IAAI,CAACJ,OAAO,CAACC,MAAM,CAAC,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}