{"ast": null, "code": "// This icon file is generated automatically.\nvar NodeExpandOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M952 612c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H298a95.92 95.92 0 00-89-60c-53 0-96 43-96 96s43 96 96 96c40.3 0 74.8-24.8 89-60h150.3v152c0 55.2 44.8 100 100 100H952c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H548.3c-15.5 0-28-12.5-28-28V612H952zM456 344h264v98.2c0 8.1 9.5 12.8 15.8 7.7l172.5-136.2c5-3.9 5-11.4 0-15.3L735.8 162.1c-6.4-5.1-15.8-.5-15.8 7.7V268H456c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8z\"\n      }\n    }]\n  },\n  \"name\": \"node-expand\",\n  \"theme\": \"outlined\"\n};\nexport default NodeExpandOutlined;", "map": {"version": 3, "names": ["NodeExpandOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/NodeExpandOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar NodeExpandOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M952 612c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H298a95.92 95.92 0 00-89-60c-53 0-96 43-96 96s43 96 96 96c40.3 0 74.8-24.8 89-60h150.3v152c0 55.2 44.8 100 100 100H952c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H548.3c-15.5 0-28-12.5-28-28V612H952zM456 344h264v98.2c0 8.1 9.5 12.8 15.8 7.7l172.5-136.2c5-3.9 5-11.4 0-15.3L735.8 162.1c-6.4-5.1-15.8-.5-15.8 7.7V268H456c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8z\" } }] }, \"name\": \"node-expand\", \"theme\": \"outlined\" };\nexport default NodeExpandOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA0Y;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,aAAa;EAAE,OAAO,EAAE;AAAW,CAAC;AAC7qB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}