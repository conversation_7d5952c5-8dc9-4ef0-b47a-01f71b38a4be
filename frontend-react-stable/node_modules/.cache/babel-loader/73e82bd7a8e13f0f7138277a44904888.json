{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar CollapsePanel = function CollapsePanel(props) {\n  process.env.NODE_ENV !== \"production\" ? warning(!('disabled' in props), 'Collapse.Panel', '`disabled` is deprecated. Please use `collapsible=\"disabled\"` instead.') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var collapsePanelClassName = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-no-arrow\"), !showArrow), className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n};\nexport default CollapsePanel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "RcCollapse", "React", "ConfigContext", "warning", "CollapsePanel", "props", "process", "env", "NODE_ENV", "_React$useContext", "useContext", "getPrefixCls", "customizePrefixCls", "prefixCls", "_props$className", "className", "_props$showArrow", "showArrow", "collapsePanelClassName", "concat", "createElement", "Panel"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/collapse/CollapsePanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar CollapsePanel = function CollapsePanel(props) {\n  process.env.NODE_ENV !== \"production\" ? warning(!('disabled' in props), 'Collapse.Panel', '`disabled` is deprecated. Please use `collapsible=\"disabled\"` instead.') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var collapsePanelClassName = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-no-arrow\"), !showArrow), className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n};\nexport default CollapsePanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,OAAO,CAAC,EAAE,UAAU,IAAIE,KAAK,CAAC,EAAE,gBAAgB,EAAE,wEAAwE,CAAC,GAAG,KAAK,CAAC;EAC5K,IAAII,iBAAiB,GAAGR,KAAK,CAACS,UAAU,CAACR,aAAa,CAAC;IACrDS,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIC,kBAAkB,GAAGP,KAAK,CAACQ,SAAS;IACtCC,gBAAgB,GAAGT,KAAK,CAACU,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,gBAAgB,GAAGX,KAAK,CAACY,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;EACnE,IAAIH,SAAS,GAAGF,YAAY,CAAC,UAAU,EAAEC,kBAAkB,CAAC;EAC5D,IAAIM,sBAAsB,GAAGnB,UAAU,CAACD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACN,SAAS,EAAE,WAAW,CAAC,EAAE,CAACI,SAAS,CAAC,EAAEF,SAAS,CAAC;EACtH,OAAO,aAAad,KAAK,CAACmB,aAAa,CAACpB,UAAU,CAACqB,KAAK,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IAC5EQ,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEG;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}