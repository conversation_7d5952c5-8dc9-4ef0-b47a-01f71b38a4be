{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\")[\"default\"];\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = _zh_CN[\"default\"];\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_zh_CN", "_default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/lib/calendar/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\")[\"default\"];\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = _zh_CN[\"default\"];\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAAC,SAAS,CAAC;AAC/FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAIE,MAAM,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAC9E,IAAIM,QAAQ,GAAGD,MAAM,CAAC,SAAS,CAAC;AAChCF,OAAO,CAAC,SAAS,CAAC,GAAGG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}