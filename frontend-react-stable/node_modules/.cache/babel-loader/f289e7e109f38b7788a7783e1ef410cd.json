{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from \"../TreeNode\";\nexport function convertChildrenToData(nodes) {\n  return toArray(nodes).map(function (node) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref = node,\n      key = _ref.key,\n      _ref$props = _ref.props,\n      children = _ref$props.children,\n      value = _ref$props.value,\n      restProps = _objectWithoutProperties(_ref$props, _excluded);\n    var data = _objectSpread({\n      key: key,\n      value: value\n    }, restProps);\n    var childData = convertChildrenToData(children);\n    if (childData.length) {\n      data.children = childData;\n    }\n    return data;\n  }).filter(function (data) {\n    return data;\n  });\n}\nexport function fillLegacyProps(dataNode) {\n  if (!dataNode) {\n    return dataNode;\n  }\n  var cloneNode = _objectSpread({}, dataNode);\n  if (!('props' in cloneNode)) {\n    Object.defineProperty(cloneNode, 'props', {\n      get: function get() {\n        warning(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');\n        return cloneNode;\n      }\n    });\n  }\n  return cloneNode;\n}\nexport function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {\n  var triggerNode = null;\n  var nodeList = null;\n  function generateMap() {\n    function dig(list) {\n      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';\n      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      return list.map(function (option, index) {\n        var pos = \"\".concat(level, \"-\").concat(index);\n        var value = option[fieldNames.value];\n        var included = checkedValues.includes(value);\n        var children = dig(option[fieldNames.children] || [], pos, included);\n        var node = /*#__PURE__*/React.createElement(TreeNode, option, children.map(function (child) {\n          return child.node;\n        })); // Link with trigger node\n\n        if (triggerValue === value) {\n          triggerNode = node;\n        }\n        if (included) {\n          var checkedNode = {\n            pos: pos,\n            node: node,\n            children: children\n          };\n          if (!parentIncluded) {\n            nodeList.push(checkedNode);\n          }\n          return checkedNode;\n        }\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n    if (!nodeList) {\n      nodeList = [];\n      dig(treeData); // Sort to keep the checked node length\n\n      nodeList.sort(function (_ref2, _ref3) {\n        var val1 = _ref2.node.props.value;\n        var val2 = _ref3.node.props.value;\n        var index1 = checkedValues.indexOf(val1);\n        var index2 = checkedValues.indexOf(val2);\n        return index1 - index2;\n      });\n    }\n  }\n  Object.defineProperty(extra, 'triggerNode', {\n    get: function get() {\n      warning(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      return triggerNode;\n    }\n  });\n  Object.defineProperty(extra, 'allCheckedNodes', {\n    get: function get() {\n      warning(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      if (showPosition) {\n        return nodeList;\n      }\n      return nodeList.map(function (_ref4) {\n        var node = _ref4.node;\n        return node;\n      });\n    }\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "toArray", "warning", "TreeNode", "convertChildrenToData", "nodes", "map", "node", "isValidElement", "type", "_ref", "key", "_ref$props", "props", "children", "value", "restProps", "data", "childData", "length", "filter", "fillLegacyProps", "dataNode", "cloneNode", "Object", "defineProperty", "get", "fillAdditionalInfo", "extra", "triggerValue", "checkedValues", "treeData", "showPosition", "fieldNames", "triggerNode", "nodeList", "generateMap", "dig", "list", "level", "arguments", "undefined", "parentIncluded", "option", "index", "pos", "concat", "included", "includes", "createElement", "child", "checkedNode", "push", "sort", "_ref2", "_ref3", "val1", "val2", "index1", "indexOf", "index2", "_ref4"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from \"../TreeNode\";\nexport function convertChildrenToData(nodes) {\n  return toArray(nodes).map(function (node) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n\n    var _ref = node,\n        key = _ref.key,\n        _ref$props = _ref.props,\n        children = _ref$props.children,\n        value = _ref$props.value,\n        restProps = _objectWithoutProperties(_ref$props, _excluded);\n\n    var data = _objectSpread({\n      key: key,\n      value: value\n    }, restProps);\n\n    var childData = convertChildrenToData(children);\n\n    if (childData.length) {\n      data.children = childData;\n    }\n\n    return data;\n  }).filter(function (data) {\n    return data;\n  });\n}\nexport function fillLegacyProps(dataNode) {\n  if (!dataNode) {\n    return dataNode;\n  }\n\n  var cloneNode = _objectSpread({}, dataNode);\n\n  if (!('props' in cloneNode)) {\n    Object.defineProperty(cloneNode, 'props', {\n      get: function get() {\n        warning(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');\n        return cloneNode;\n      }\n    });\n  }\n\n  return cloneNode;\n}\nexport function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {\n  var triggerNode = null;\n  var nodeList = null;\n\n  function generateMap() {\n    function dig(list) {\n      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';\n      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      return list.map(function (option, index) {\n        var pos = \"\".concat(level, \"-\").concat(index);\n        var value = option[fieldNames.value];\n        var included = checkedValues.includes(value);\n        var children = dig(option[fieldNames.children] || [], pos, included);\n        var node = /*#__PURE__*/React.createElement(TreeNode, option, children.map(function (child) {\n          return child.node;\n        })); // Link with trigger node\n\n        if (triggerValue === value) {\n          triggerNode = node;\n        }\n\n        if (included) {\n          var checkedNode = {\n            pos: pos,\n            node: node,\n            children: children\n          };\n\n          if (!parentIncluded) {\n            nodeList.push(checkedNode);\n          }\n\n          return checkedNode;\n        }\n\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n\n    if (!nodeList) {\n      nodeList = [];\n      dig(treeData); // Sort to keep the checked node length\n\n      nodeList.sort(function (_ref2, _ref3) {\n        var val1 = _ref2.node.props.value;\n        var val2 = _ref3.node.props.value;\n        var index1 = checkedValues.indexOf(val1);\n        var index2 = checkedValues.indexOf(val2);\n        return index1 - index2;\n      });\n    }\n  }\n\n  Object.defineProperty(extra, 'triggerNode', {\n    get: function get() {\n      warning(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      return triggerNode;\n    }\n  });\n  Object.defineProperty(extra, 'allCheckedNodes', {\n    get: function get() {\n      warning(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n\n      if (showPosition) {\n        return nodeList;\n      }\n\n      return nodeList.map(function (_ref4) {\n        var node = _ref4.node;\n        return node;\n      });\n    }\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,OAAOJ,OAAO,CAACI,KAAK,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;IACxC,IAAI,EAAE,aAAaP,KAAK,CAACQ,cAAc,CAACD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACE,IAAI,EAAE;MAC3D,OAAO,IAAI;IACb;IAEA,IAAIC,IAAI,GAAGH,IAAI;MACXI,GAAG,GAAGD,IAAI,CAACC,GAAG;MACdC,UAAU,GAAGF,IAAI,CAACG,KAAK;MACvBC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;MAC9BC,KAAK,GAAGH,UAAU,CAACG,KAAK;MACxBC,SAAS,GAAGlB,wBAAwB,CAACc,UAAU,EAAEb,SAAS,CAAC;IAE/D,IAAIkB,IAAI,GAAGpB,aAAa,CAAC;MACvBc,GAAG,EAAEA,GAAG;MACRI,KAAK,EAAEA;IACT,CAAC,EAAEC,SAAS,CAAC;IAEb,IAAIE,SAAS,GAAGd,qBAAqB,CAACU,QAAQ,CAAC;IAE/C,IAAII,SAAS,CAACC,MAAM,EAAE;MACpBF,IAAI,CAACH,QAAQ,GAAGI,SAAS;IAC3B;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUH,IAAI,EAAE;IACxB,OAAOA,IAAI;EACb,CAAC,CAAC;AACJ;AACA,OAAO,SAASI,eAAeA,CAACC,QAAQ,EAAE;EACxC,IAAI,CAACA,QAAQ,EAAE;IACb,OAAOA,QAAQ;EACjB;EAEA,IAAIC,SAAS,GAAG1B,aAAa,CAAC,CAAC,CAAC,EAAEyB,QAAQ,CAAC;EAE3C,IAAI,EAAE,OAAO,IAAIC,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBxB,OAAO,CAAC,KAAK,EAAE,sHAAsH,CAAC;QACtI,OAAOqB,SAAS;MAClB;IACF,CAAC,CAAC;EACJ;EAEA,OAAOA,SAAS;AAClB;AACA,OAAO,SAASI,kBAAkBA,CAACC,KAAK,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAE;EACzG,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,QAAQ,GAAG,IAAI;EAEnB,SAASC,WAAWA,CAAA,EAAG;IACrB,SAASC,GAAGA,CAACC,IAAI,EAAE;MACjB,IAAIC,KAAK,GAAGC,SAAS,CAACrB,MAAM,GAAG,CAAC,IAAIqB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;MACnF,IAAIE,cAAc,GAAGF,SAAS,CAACrB,MAAM,GAAG,CAAC,IAAIqB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC9F,OAAOF,IAAI,CAAChC,GAAG,CAAC,UAAUqC,MAAM,EAAEC,KAAK,EAAE;QACvC,IAAIC,GAAG,GAAG,EAAE,CAACC,MAAM,CAACP,KAAK,EAAE,GAAG,CAAC,CAACO,MAAM,CAACF,KAAK,CAAC;QAC7C,IAAI7B,KAAK,GAAG4B,MAAM,CAACV,UAAU,CAAClB,KAAK,CAAC;QACpC,IAAIgC,QAAQ,GAAGjB,aAAa,CAACkB,QAAQ,CAACjC,KAAK,CAAC;QAC5C,IAAID,QAAQ,GAAGuB,GAAG,CAACM,MAAM,CAACV,UAAU,CAACnB,QAAQ,CAAC,IAAI,EAAE,EAAE+B,GAAG,EAAEE,QAAQ,CAAC;QACpE,IAAIxC,IAAI,GAAG,aAAaP,KAAK,CAACiD,aAAa,CAAC9C,QAAQ,EAAEwC,MAAM,EAAE7B,QAAQ,CAACR,GAAG,CAAC,UAAU4C,KAAK,EAAE;UAC1F,OAAOA,KAAK,CAAC3C,IAAI;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,IAAIsB,YAAY,KAAKd,KAAK,EAAE;UAC1BmB,WAAW,GAAG3B,IAAI;QACpB;QAEA,IAAIwC,QAAQ,EAAE;UACZ,IAAII,WAAW,GAAG;YAChBN,GAAG,EAAEA,GAAG;YACRtC,IAAI,EAAEA,IAAI;YACVO,QAAQ,EAAEA;UACZ,CAAC;UAED,IAAI,CAAC4B,cAAc,EAAE;YACnBP,QAAQ,CAACiB,IAAI,CAACD,WAAW,CAAC;UAC5B;UAEA,OAAOA,WAAW;QACpB;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,CAAC/B,MAAM,CAAC,UAAUb,IAAI,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC4B,QAAQ,EAAE;MACbA,QAAQ,GAAG,EAAE;MACbE,GAAG,CAACN,QAAQ,CAAC,CAAC,CAAC;;MAEfI,QAAQ,CAACkB,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;QACpC,IAAIC,IAAI,GAAGF,KAAK,CAAC/C,IAAI,CAACM,KAAK,CAACE,KAAK;QACjC,IAAI0C,IAAI,GAAGF,KAAK,CAAChD,IAAI,CAACM,KAAK,CAACE,KAAK;QACjC,IAAI2C,MAAM,GAAG5B,aAAa,CAAC6B,OAAO,CAACH,IAAI,CAAC;QACxC,IAAII,MAAM,GAAG9B,aAAa,CAAC6B,OAAO,CAACF,IAAI,CAAC;QACxC,OAAOC,MAAM,GAAGE,MAAM;MACxB,CAAC,CAAC;IACJ;EACF;EAEApC,MAAM,CAACC,cAAc,CAACG,KAAK,EAAE,aAAa,EAAE;IAC1CF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClBxB,OAAO,CAAC,KAAK,EAAE,yEAAyE,CAAC;MACzFkC,WAAW,CAAC,CAAC;MACb,OAAOF,WAAW;IACpB;EACF,CAAC,CAAC;EACFV,MAAM,CAACC,cAAc,CAACG,KAAK,EAAE,iBAAiB,EAAE;IAC9CF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClBxB,OAAO,CAAC,KAAK,EAAE,6EAA6E,CAAC;MAC7FkC,WAAW,CAAC,CAAC;MAEb,IAAIJ,YAAY,EAAE;QAChB,OAAOG,QAAQ;MACjB;MAEA,OAAOA,QAAQ,CAAC7B,GAAG,CAAC,UAAUuD,KAAK,EAAE;QACnC,IAAItD,IAAI,GAAGsD,KAAK,CAACtD,IAAI;QACrB,OAAOA,IAAI;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}