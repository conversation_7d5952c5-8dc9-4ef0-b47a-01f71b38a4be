{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = zip;\nvar _transpose = _interopRequireDefault(require(\"./transpose.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction zip() {\n  return (0, _transpose.default)(arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "zip", "_transpose", "_interopRequireDefault", "require", "obj", "__esModule", "arguments"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/zip.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = zip;\n\nvar _transpose = _interopRequireDefault(require(\"./transpose.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction zip() {\n  return (0, _transpose.default)(arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,GAAGA,CAAA,EAAG;EACb,OAAO,CAAC,CAAC,EAAEC,UAAU,CAACF,OAAO,EAAEO,SAAS,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script"}