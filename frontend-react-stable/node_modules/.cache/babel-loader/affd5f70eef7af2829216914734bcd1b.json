{"ast": null, "code": "// `victory-vendor/d3-scale` (CommonJS)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// This file only exists for tooling that doesn't work yet with package.json:exports\n// by proxying through the CommonJS version.\nmodule.exports = require(\"./lib/d3-scale\");", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/d3-scale.js"], "sourcesContent": ["\n// `victory-vendor/d3-scale` (CommonJS)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// This file only exists for tooling that doesn't work yet with package.json:exports\n// by proxying through the CommonJS version.\nmodule.exports = require(\"./lib/d3-scale\");\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}