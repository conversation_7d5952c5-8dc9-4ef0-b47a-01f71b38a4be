{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Default Legend Content\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport { Surface } from '../container/Surface';\nimport { Symbols } from '../shape/Symbols';\nimport { adaptEventsOfChild } from '../util/types';\nvar SIZE = 32;\nexport var DefaultLegendContent = /*#__PURE__*/function (_PureComponent) {\n  _inherits(DefaultLegendContent, _PureComponent);\n  var _super = _createSuper(DefaultLegendContent);\n  function DefaultLegendContent() {\n    _classCallCheck(this, DefaultLegendContent);\n    return _super.apply(this, arguments);\n  }\n  _createClass(DefaultLegendContent, [{\n    key: \"renderIcon\",\n    value:\n    /**\n     * Render the path of icon\n     * @param {Object} data Data of each legend item\n     * @return {String} Path element\n     */\n    function renderIcon(data) {\n      var inactiveColor = this.props.inactiveColor;\n      var halfSize = SIZE / 2;\n      var sixthSize = SIZE / 6;\n      var thirdSize = SIZE / 3;\n      var color = data.inactive ? inactiveColor : data.color;\n      if (data.type === 'plainline') {\n        return /*#__PURE__*/React.createElement(\"line\", {\n          strokeWidth: 4,\n          fill: \"none\",\n          stroke: color,\n          strokeDasharray: data.payload.strokeDasharray,\n          x1: 0,\n          y1: halfSize,\n          x2: SIZE,\n          y2: halfSize,\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if (data.type === 'line') {\n        return /*#__PURE__*/React.createElement(\"path\", {\n          strokeWidth: 4,\n          fill: \"none\",\n          stroke: color,\n          d: \"M0,\".concat(halfSize, \"h\").concat(thirdSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            H\").concat(SIZE, \"M\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(thirdSize, \",\").concat(halfSize),\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if (data.type === 'rect') {\n        return /*#__PURE__*/React.createElement(\"path\", {\n          stroke: \"none\",\n          fill: color,\n          d: \"M0,\".concat(SIZE / 8, \"h\").concat(SIZE, \"v\").concat(SIZE * 3 / 4, \"h\").concat(-SIZE, \"z\"),\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if (/*#__PURE__*/React.isValidElement(data.legendIcon)) {\n        var iconProps = _objectSpread({}, data);\n        delete iconProps.legendIcon;\n        return /*#__PURE__*/React.cloneElement(data.legendIcon, iconProps);\n      }\n      return /*#__PURE__*/React.createElement(Symbols, {\n        fill: color,\n        cx: halfSize,\n        cy: halfSize,\n        size: SIZE,\n        sizeType: \"diameter\",\n        type: data.type\n      });\n    }\n\n    /**\n     * Draw items of legend\n     * @return {ReactElement} Items\n     */\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this = this;\n      var _this$props = this.props,\n        payload = _this$props.payload,\n        iconSize = _this$props.iconSize,\n        layout = _this$props.layout,\n        formatter = _this$props.formatter,\n        inactiveColor = _this$props.inactiveColor;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: SIZE,\n        height: SIZE\n      };\n      var itemStyle = {\n        display: layout === 'horizontal' ? 'inline-block' : 'block',\n        marginRight: 10\n      };\n      var svgStyle = {\n        display: 'inline-block',\n        verticalAlign: 'middle',\n        marginRight: 4\n      };\n      return payload.map(function (entry, i) {\n        var _classNames;\n        var finalFormatter = entry.formatter || formatter;\n        var className = classNames((_classNames = {\n          'recharts-legend-item': true\n        }, _defineProperty(_classNames, \"legend-item-\".concat(i), true), _defineProperty(_classNames, \"inactive\", entry.inactive), _classNames));\n        if (entry.type === 'none') {\n          return null;\n        }\n        var color = entry.inactive ? inactiveColor : entry.color;\n        return /*#__PURE__*/React.createElement(\"li\", _extends({\n          className: className,\n          style: itemStyle,\n          key: \"legend-item-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }, adaptEventsOfChild(_this.props, entry, i)), /*#__PURE__*/React.createElement(Surface, {\n          width: iconSize,\n          height: iconSize,\n          viewBox: viewBox,\n          style: svgStyle\n        }, _this.renderIcon(entry)), /*#__PURE__*/React.createElement(\"span\", {\n          className: \"recharts-legend-item-text\",\n          style: {\n            color: color\n          }\n        }, finalFormatter ? finalFormatter(entry.value, entry, i) : entry.value));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        payload = _this$props2.payload,\n        layout = _this$props2.layout,\n        align = _this$props2.align;\n      if (!payload || !payload.length) {\n        return null;\n      }\n      var finalStyle = {\n        padding: 0,\n        margin: 0,\n        textAlign: layout === 'horizontal' ? align : 'left'\n      };\n      return /*#__PURE__*/React.createElement(\"ul\", {\n        className: \"recharts-default-legend\",\n        style: finalStyle\n      }, this.renderItems());\n    }\n  }]);\n  return DefaultLegendContent;\n}(PureComponent);\n_defineProperty(DefaultLegendContent, \"displayName\", 'Legend');\n_defineProperty(DefaultLegendContent, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'middle',\n  inactiveColor: '#ccc'\n});", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "classNames", "Surface", "Symbols", "adaptEventsOfChild", "SIZE", "DefaultLegendContent", "_PureComponent", "_super", "renderIcon", "data", "inactiveColor", "halfSize", "sixthSize", "thirdSize", "color", "inactive", "type", "createElement", "strokeWidth", "fill", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payload", "x1", "y1", "x2", "y2", "className", "d", "concat", "isValidElement", "legendIcon", "iconProps", "cloneElement", "cx", "cy", "size", "sizeType", "renderItems", "_this", "_this$props", "iconSize", "layout", "formatter", "viewBox", "x", "y", "width", "height", "itemStyle", "display", "marginRight", "svgStyle", "verticalAlign", "map", "entry", "_classNames", "<PERSON><PERSON><PERSON><PERSON>er", "style", "render", "_this$props2", "align", "finalStyle", "padding", "margin", "textAlign"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/DefaultLegendContent.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Default Legend Content\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport { Surface } from '../container/Surface';\nimport { Symbols } from '../shape/Symbols';\nimport { adaptEventsOfChild } from '../util/types';\nvar SIZE = 32;\nexport var DefaultLegendContent = /*#__PURE__*/function (_PureComponent) {\n  _inherits(DefaultLegendContent, _PureComponent);\n  var _super = _createSuper(DefaultLegendContent);\n  function DefaultLegendContent() {\n    _classCallCheck(this, DefaultLegendContent);\n    return _super.apply(this, arguments);\n  }\n  _createClass(DefaultLegendContent, [{\n    key: \"renderIcon\",\n    value:\n    /**\n     * Render the path of icon\n     * @param {Object} data Data of each legend item\n     * @return {String} Path element\n     */\n    function renderIcon(data) {\n      var inactiveColor = this.props.inactiveColor;\n      var halfSize = SIZE / 2;\n      var sixthSize = SIZE / 6;\n      var thirdSize = SIZE / 3;\n      var color = data.inactive ? inactiveColor : data.color;\n      if (data.type === 'plainline') {\n        return /*#__PURE__*/React.createElement(\"line\", {\n          strokeWidth: 4,\n          fill: \"none\",\n          stroke: color,\n          strokeDasharray: data.payload.strokeDasharray,\n          x1: 0,\n          y1: halfSize,\n          x2: SIZE,\n          y2: halfSize,\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if (data.type === 'line') {\n        return /*#__PURE__*/React.createElement(\"path\", {\n          strokeWidth: 4,\n          fill: \"none\",\n          stroke: color,\n          d: \"M0,\".concat(halfSize, \"h\").concat(thirdSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            H\").concat(SIZE, \"M\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(thirdSize, \",\").concat(halfSize),\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if (data.type === 'rect') {\n        return /*#__PURE__*/React.createElement(\"path\", {\n          stroke: \"none\",\n          fill: color,\n          d: \"M0,\".concat(SIZE / 8, \"h\").concat(SIZE, \"v\").concat(SIZE * 3 / 4, \"h\").concat(-SIZE, \"z\"),\n          className: \"recharts-legend-icon\"\n        });\n      }\n      if ( /*#__PURE__*/React.isValidElement(data.legendIcon)) {\n        var iconProps = _objectSpread({}, data);\n        delete iconProps.legendIcon;\n        return /*#__PURE__*/React.cloneElement(data.legendIcon, iconProps);\n      }\n      return /*#__PURE__*/React.createElement(Symbols, {\n        fill: color,\n        cx: halfSize,\n        cy: halfSize,\n        size: SIZE,\n        sizeType: \"diameter\",\n        type: data.type\n      });\n    }\n\n    /**\n     * Draw items of legend\n     * @return {ReactElement} Items\n     */\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this = this;\n      var _this$props = this.props,\n        payload = _this$props.payload,\n        iconSize = _this$props.iconSize,\n        layout = _this$props.layout,\n        formatter = _this$props.formatter,\n        inactiveColor = _this$props.inactiveColor;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: SIZE,\n        height: SIZE\n      };\n      var itemStyle = {\n        display: layout === 'horizontal' ? 'inline-block' : 'block',\n        marginRight: 10\n      };\n      var svgStyle = {\n        display: 'inline-block',\n        verticalAlign: 'middle',\n        marginRight: 4\n      };\n      return payload.map(function (entry, i) {\n        var _classNames;\n        var finalFormatter = entry.formatter || formatter;\n        var className = classNames((_classNames = {\n          'recharts-legend-item': true\n        }, _defineProperty(_classNames, \"legend-item-\".concat(i), true), _defineProperty(_classNames, \"inactive\", entry.inactive), _classNames));\n        if (entry.type === 'none') {\n          return null;\n        }\n        var color = entry.inactive ? inactiveColor : entry.color;\n        return /*#__PURE__*/React.createElement(\"li\", _extends({\n          className: className,\n          style: itemStyle,\n          key: \"legend-item-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }, adaptEventsOfChild(_this.props, entry, i)), /*#__PURE__*/React.createElement(Surface, {\n          width: iconSize,\n          height: iconSize,\n          viewBox: viewBox,\n          style: svgStyle\n        }, _this.renderIcon(entry)), /*#__PURE__*/React.createElement(\"span\", {\n          className: \"recharts-legend-item-text\",\n          style: {\n            color: color\n          }\n        }, finalFormatter ? finalFormatter(entry.value, entry, i) : entry.value));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        payload = _this$props2.payload,\n        layout = _this$props2.layout,\n        align = _this$props2.align;\n      if (!payload || !payload.length) {\n        return null;\n      }\n      var finalStyle = {\n        padding: 0,\n        margin: 0,\n        textAlign: layout === 'horizontal' ? align : 'left'\n      };\n      return /*#__PURE__*/React.createElement(\"ul\", {\n        className: \"recharts-default-legend\",\n        style: finalStyle\n      }, this.renderItems());\n    }\n  }]);\n  return DefaultLegendContent;\n}(PureComponent);\n_defineProperty(DefaultLegendContent, \"displayName\", 'Legend');\n_defineProperty(DefaultLegendContent, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'middle',\n  inactiveColor: '#ccc'\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAACpB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGQ,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAEiB,eAAe,CAACtB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACxB,MAAM,EAAEH,MAAM,CAAC0B,yBAAyB,CAACnB,MAAM,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAER,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACoB,wBAAwB,CAACb,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC9B,MAAM,EAAE+B,KAAK,EAAE;EAAE,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,KAAK,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI+B,UAAU,GAAGD,KAAK,CAAC9B,CAAC,CAAC;IAAE+B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAErC,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEmC,cAAc,CAACH,UAAU,CAAC3B,GAAG,CAAC,EAAE2B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACjC,SAAS,EAAE0C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEzC,MAAM,CAAC4B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAAC7C,SAAS,GAAGE,MAAM,CAAC6C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC9C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEiD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEpC,MAAM,CAAC4B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAG/C,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAAChD,IAAI,CAAC,CAAC,GAAG,SAAS6C,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC7D,WAAW;MAAE8D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEpD,SAAS,EAAEuD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC9C,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAO0D,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEtD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIsB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACvE,SAAS,CAACwE,OAAO,CAAC5D,IAAI,CAACmD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG1D,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACwE,cAAc,CAACtE,IAAI,CAAC,CAAC,GAAG,SAASwD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAInD,MAAM,CAACwE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC/B,GAAG,EAAEc,GAAG,EAAEsC,KAAK,EAAE;EAAEtC,GAAG,GAAG8B,cAAc,CAAC9B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC4B,cAAc,CAAClC,GAAG,EAAEc,GAAG,EAAE;MAAEsC,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE3C,GAAG,CAACc,GAAG,CAAC,GAAGsC,KAAK;EAAE;EAAE,OAAOpD,GAAG;AAAE;AAC3O,SAAS4C,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAIjE,GAAG,GAAGkE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGmE,MAAM,CAACnE,GAAG,CAAC;AAAE;AAC5H,SAASkE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpF,OAAO,CAACmF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjF,MAAM,CAACoF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACpE,IAAI,CAACkE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpF,OAAO,CAACwF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,IAAIC,IAAI,GAAG,EAAE;AACb,OAAO,IAAIC,oBAAoB,GAAG,aAAa,UAAUC,cAAc,EAAE;EACvEjD,SAAS,CAACgD,oBAAoB,EAAEC,cAAc,CAAC;EAC/C,IAAIC,MAAM,GAAGxC,YAAY,CAACsC,oBAAoB,CAAC;EAC/C,SAASA,oBAAoBA,CAAA,EAAG;IAC9B7D,eAAe,CAAC,IAAI,EAAE6D,oBAAoB,CAAC;IAC3C,OAAOE,MAAM,CAACjF,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;EACtC;EACAkC,YAAY,CAACmD,oBAAoB,EAAE,CAAC;IAClClF,GAAG,EAAE,YAAY;IACjBsC,KAAK;IACL;AACJ;AACA;AACA;AACA;IACI,SAAS+C,UAAUA,CAACC,IAAI,EAAE;MACxB,IAAIC,aAAa,GAAG,IAAI,CAAC7D,KAAK,CAAC6D,aAAa;MAC5C,IAAIC,QAAQ,GAAGP,IAAI,GAAG,CAAC;MACvB,IAAIQ,SAAS,GAAGR,IAAI,GAAG,CAAC;MACxB,IAAIS,SAAS,GAAGT,IAAI,GAAG,CAAC;MACxB,IAAIU,KAAK,GAAGL,IAAI,CAACM,QAAQ,GAAGL,aAAa,GAAGD,IAAI,CAACK,KAAK;MACtD,IAAIL,IAAI,CAACO,IAAI,KAAK,WAAW,EAAE;QAC7B,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;UAC9CC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAEN,KAAK;UACbO,eAAe,EAAEZ,IAAI,CAACa,OAAO,CAACD,eAAe;UAC7CE,EAAE,EAAE,CAAC;UACLC,EAAE,EAAEb,QAAQ;UACZc,EAAE,EAAErB,IAAI;UACRsB,EAAE,EAAEf,QAAQ;UACZgB,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA,IAAIlB,IAAI,CAACO,IAAI,KAAK,MAAM,EAAE;QACxB,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;UAC9CC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAEN,KAAK;UACbc,CAAC,EAAE,KAAK,CAACC,MAAM,CAAClB,QAAQ,EAAE,GAAG,CAAC,CAACkB,MAAM,CAAChB,SAAS,EAAE,iBAAiB,CAAC,CAACgB,MAAM,CAACjB,SAAS,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACjB,SAAS,EAAE,SAAS,CAAC,CAACiB,MAAM,CAAC,CAAC,GAAGhB,SAAS,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAClB,QAAQ,EAAE,iBAAiB,CAAC,CAACkB,MAAM,CAACzB,IAAI,EAAE,GAAG,CAAC,CAACyB,MAAM,CAAC,CAAC,GAAGhB,SAAS,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAClB,QAAQ,EAAE,iBAAiB,CAAC,CAACkB,MAAM,CAACjB,SAAS,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACjB,SAAS,EAAE,SAAS,CAAC,CAACiB,MAAM,CAAChB,SAAS,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAClB,QAAQ,CAAC;UACnWgB,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA,IAAIlB,IAAI,CAACO,IAAI,KAAK,MAAM,EAAE;QACxB,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;UAC9CG,MAAM,EAAE,MAAM;UACdD,IAAI,EAAEL,KAAK;UACXc,CAAC,EAAE,KAAK,CAACC,MAAM,CAACzB,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAACyB,MAAM,CAACzB,IAAI,EAAE,GAAG,CAAC,CAACyB,MAAM,CAACzB,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAACyB,MAAM,CAAC,CAACzB,IAAI,EAAE,GAAG,CAAC;UAC7FuB,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA,IAAK,aAAa7B,KAAK,CAACgC,cAAc,CAACrB,IAAI,CAACsB,UAAU,CAAC,EAAE;QACvD,IAAIC,SAAS,GAAG9F,aAAa,CAAC,CAAC,CAAC,EAAEuE,IAAI,CAAC;QACvC,OAAOuB,SAAS,CAACD,UAAU;QAC3B,OAAO,aAAajC,KAAK,CAACmC,YAAY,CAACxB,IAAI,CAACsB,UAAU,EAAEC,SAAS,CAAC;MACpE;MACA,OAAO,aAAalC,KAAK,CAACmB,aAAa,CAACf,OAAO,EAAE;QAC/CiB,IAAI,EAAEL,KAAK;QACXoB,EAAE,EAAEvB,QAAQ;QACZwB,EAAE,EAAExB,QAAQ;QACZyB,IAAI,EAAEhC,IAAI;QACViC,QAAQ,EAAE,UAAU;QACpBrB,IAAI,EAAEP,IAAI,CAACO;MACb,CAAC,CAAC;IACJ;;IAEA;AACJ;AACA;AACA;EACE,CAAC,EAAE;IACD7F,GAAG,EAAE,aAAa;IAClBsC,KAAK,EAAE,SAAS6E,WAAWA,CAAA,EAAG;MAC5B,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAIC,WAAW,GAAG,IAAI,CAAC3F,KAAK;QAC1ByE,OAAO,GAAGkB,WAAW,CAAClB,OAAO;QAC7BmB,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,MAAM,GAAGF,WAAW,CAACE,MAAM;QAC3BC,SAAS,GAAGH,WAAW,CAACG,SAAS;QACjCjC,aAAa,GAAG8B,WAAW,CAAC9B,aAAa;MAC3C,IAAIkC,OAAO,GAAG;QACZC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE3C,IAAI;QACX4C,MAAM,EAAE5C;MACV,CAAC;MACD,IAAI6C,SAAS,GAAG;QACdC,OAAO,EAAER,MAAM,KAAK,YAAY,GAAG,cAAc,GAAG,OAAO;QAC3DS,WAAW,EAAE;MACf,CAAC;MACD,IAAIC,QAAQ,GAAG;QACbF,OAAO,EAAE,cAAc;QACvBG,aAAa,EAAE,QAAQ;QACvBF,WAAW,EAAE;MACf,CAAC;MACD,OAAO7B,OAAO,CAACgC,GAAG,CAAC,UAAUC,KAAK,EAAExI,CAAC,EAAE;QACrC,IAAIyI,WAAW;QACf,IAAIC,cAAc,GAAGF,KAAK,CAACZ,SAAS,IAAIA,SAAS;QACjD,IAAIhB,SAAS,GAAG3B,UAAU,EAAEwD,WAAW,GAAG;UACxC,sBAAsB,EAAE;QAC1B,CAAC,EAAEpH,eAAe,CAACoH,WAAW,EAAE,cAAc,CAAC3B,MAAM,CAAC9G,CAAC,CAAC,EAAE,IAAI,CAAC,EAAEqB,eAAe,CAACoH,WAAW,EAAE,UAAU,EAAED,KAAK,CAACxC,QAAQ,CAAC,EAAEyC,WAAW,CAAC,CAAC;QACxI,IAAID,KAAK,CAACvC,IAAI,KAAK,MAAM,EAAE;UACzB,OAAO,IAAI;QACb;QACA,IAAIF,KAAK,GAAGyC,KAAK,CAACxC,QAAQ,GAAGL,aAAa,GAAG6C,KAAK,CAACzC,KAAK;QACxD,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEvG,QAAQ,CAAC;UACrDiH,SAAS,EAAEA,SAAS;UACpB+B,KAAK,EAAET,SAAS;UAChB9H,GAAG,EAAE,cAAc,CAAC0G,MAAM,CAAC9G,CAAC,CAAC,CAAC;QAChC,CAAC,EAAEoF,kBAAkB,CAACoC,KAAK,CAAC1F,KAAK,EAAE0G,KAAK,EAAExI,CAAC,CAAC,CAAC,EAAE,aAAa+E,KAAK,CAACmB,aAAa,CAAChB,OAAO,EAAE;UACvF8C,KAAK,EAAEN,QAAQ;UACfO,MAAM,EAAEP,QAAQ;UAChBG,OAAO,EAAEA,OAAO;UAChBc,KAAK,EAAEN;QACT,CAAC,EAAEb,KAAK,CAAC/B,UAAU,CAAC+C,KAAK,CAAC,CAAC,EAAE,aAAazD,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;UACpEU,SAAS,EAAE,2BAA2B;UACtC+B,KAAK,EAAE;YACL5C,KAAK,EAAEA;UACT;QACF,CAAC,EAAE2C,cAAc,GAAGA,cAAc,CAACF,KAAK,CAAC9F,KAAK,EAAE8F,KAAK,EAAExI,CAAC,CAAC,GAAGwI,KAAK,CAAC9F,KAAK,CAAC,CAAC;MAC3E,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,QAAQ;IACbsC,KAAK,EAAE,SAASkG,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC/G,KAAK;QAC3ByE,OAAO,GAAGsC,YAAY,CAACtC,OAAO;QAC9BoB,MAAM,GAAGkB,YAAY,CAAClB,MAAM;QAC5BmB,KAAK,GAAGD,YAAY,CAACC,KAAK;MAC5B,IAAI,CAACvC,OAAO,IAAI,CAACA,OAAO,CAACrG,MAAM,EAAE;QAC/B,OAAO,IAAI;MACb;MACA,IAAI6I,UAAU,GAAG;QACfC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAEvB,MAAM,KAAK,YAAY,GAAGmB,KAAK,GAAG;MAC/C,CAAC;MACD,OAAO,aAAa/D,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;QAC5CU,SAAS,EAAE,yBAAyB;QACpC+B,KAAK,EAAEI;MACT,CAAC,EAAE,IAAI,CAACxB,WAAW,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,CAAC,CAAC;EACH,OAAOjC,oBAAoB;AAC7B,CAAC,CAACN,aAAa,CAAC;AAChB3D,eAAe,CAACiE,oBAAoB,EAAE,aAAa,EAAE,QAAQ,CAAC;AAC9DjE,eAAe,CAACiE,oBAAoB,EAAE,cAAc,EAAE;EACpDoC,QAAQ,EAAE,EAAE;EACZC,MAAM,EAAE,YAAY;EACpBmB,KAAK,EAAE,QAAQ;EACfR,aAAa,EAAE,QAAQ;EACvB3C,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}