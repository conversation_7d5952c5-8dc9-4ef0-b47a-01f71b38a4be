{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown from './FilterDropdown';\nfunction collectFilterStates(columns, init, pos) {\n  var filterStates = [];\n  (columns || []).forEach(function (column, index) {\n    var _a;\n    var columnPos = getColumnPos(index, pos);\n    if (column.filters || 'filterDropdown' in column || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        var filteredValues = column.filteredValue;\n        if (!('filterDropdown' in column)) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n}\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, triggerFilter, getPopupContainer, locale, pos) {\n  return columns.map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var _column$filterMultipl = column.filterMultiple,\n      filterMultiple = _column$filterMultipl === void 0 ? true : _column$filterMultipl,\n      filterMode = column.filterMode,\n      filterSearch = column.filterSearch;\n    var newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var filterState = filterStates.find(function (_ref) {\n        var key = _ref.key;\n        return columnKey === key;\n      });\n      newColumn = _extends(_extends({}, newColumn), {\n        title: function title(renderProps) {\n          return /*#__PURE__*/React.createElement(FilterDropdown, {\n            tablePrefixCls: prefixCls,\n            prefixCls: \"\".concat(prefixCls, \"-filter\"),\n            dropdownPrefixCls: dropdownPrefixCls,\n            column: newColumn,\n            columnKey: columnKey,\n            filterState: filterState,\n            filterMultiple: filterMultiple,\n            filterMode: filterMode,\n            filterSearch: filterSearch,\n            triggerFilter: triggerFilter,\n            locale: locale,\n            getPopupContainer: getPopupContainer\n          }, renderColumnTitle(column.title, renderProps));\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, triggerFilter, getPopupContainer, locale, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nexport function flattenKeys(filters) {\n  var keys = [];\n  (filters || []).forEach(function (_ref2) {\n    var value = _ref2.value,\n      children = _ref2.children;\n    keys.push(value);\n    if (children) {\n      keys = [].concat(_toConsumableArray(keys), _toConsumableArray(flattenKeys(children)));\n    }\n  });\n  return keys;\n}\nfunction generateFilterInfo(filterStates) {\n  var currentFilters = {};\n  filterStates.forEach(function (_ref3) {\n    var key = _ref3.key,\n      filteredKeys = _ref3.filteredKeys,\n      column = _ref3.column;\n    var filters = column.filters,\n      filterDropdown = column.filterDropdown;\n    if (filterDropdown) {\n      currentFilters[key] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      var keys = flattenKeys(filters);\n      currentFilters[key] = keys.filter(function (originKey) {\n        return filteredKeys.includes(String(originKey));\n      });\n    } else {\n      currentFilters[key] = null;\n    }\n  });\n  return currentFilters;\n}\nexport function getFilterData(data, filterStates) {\n  return filterStates.reduce(function (currentData, filterState) {\n    var _filterState$column = filterState.column,\n      onFilter = _filterState$column.onFilter,\n      filters = _filterState$column.filters,\n      filteredKeys = filterState.filteredKeys;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData.filter(function (record) {\n        return filteredKeys.some(function (key) {\n          var keys = flattenKeys(filters);\n          var keyIndex = keys.findIndex(function (k) {\n            return String(k) === String(key);\n          });\n          var realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n          return onFilter(realKey, record);\n        });\n      });\n    }\n    return currentData;\n  }, data);\n}\nfunction useFilter(_ref4) {\n  var prefixCls = _ref4.prefixCls,\n    dropdownPrefixCls = _ref4.dropdownPrefixCls,\n    mergedColumns = _ref4.mergedColumns,\n    onFilterChange = _ref4.onFilterChange,\n    getPopupContainer = _ref4.getPopupContainer,\n    tableLocale = _ref4.locale;\n  var _React$useState = React.useState(function () {\n      return collectFilterStates(mergedColumns, true);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    filterStates = _React$useState2[0],\n    setFilterStates = _React$useState2[1];\n  var mergedFilterStates = React.useMemo(function () {\n    var collectedStates = collectFilterStates(mergedColumns, false);\n    var filteredKeysIsAllNotControlled = true;\n    var filteredKeysIsAllControlled = true;\n    collectedStates.forEach(function (_ref5) {\n      var filteredKeys = _ref5.filteredKeys;\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      return filterStates;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'Table', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  var filters = React.useMemo(function () {\n    return generateFilterInfo(mergedFilterStates);\n  }, [mergedFilterStates]);\n  var triggerFilter = function triggerFilter(filterState) {\n    var newFilterStates = mergedFilterStates.filter(function (_ref6) {\n      var key = _ref6.key;\n      return key !== filterState.key;\n    });\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, triggerFilter, getPopupContainer, tableLocale);\n  };\n  return [transformColumns, mergedFilterStates, filters];\n}\nexport default useFilter;", "map": {"version": 3, "names": ["_slicedToArray", "_extends", "_toConsumableArray", "React", "warning", "getColumnKey", "getColumnPos", "renderColumnTitle", "FilterDropdown", "collectFilterStates", "columns", "init", "pos", "filterStates", "for<PERSON>ach", "column", "index", "_a", "columnPos", "filters", "filteredValues", "filteredValue", "map", "String", "push", "key", "filtered<PERSON>eys", "forceFiltered", "filtered", "defaultFilteredValue", "undefined", "concat", "children", "injectFilter", "prefixCls", "dropdownPrefixCls", "triggerFilter", "getPopupContainer", "locale", "_column$filterMultipl", "filterMultiple", "filterMode", "filterSearch", "newColumn", "filterDropdown", "column<PERSON>ey", "filterState", "find", "_ref", "title", "renderProps", "createElement", "tablePrefixCls", "flatten<PERSON>eys", "keys", "_ref2", "value", "generateFilterInfo", "currentFilters", "_ref3", "Array", "isArray", "filter", "<PERSON><PERSON><PERSON>", "includes", "getFilterData", "data", "reduce", "currentData", "_filterState$column", "onFilter", "length", "record", "some", "keyIndex", "findIndex", "k", "realKey", "useFilter", "_ref4", "mergedColumns", "onFilterChange", "tableLocale", "_React$useState", "useState", "_React$useState2", "setFilterStates", "mergedFilterStates", "useMemo", "collectedStates", "filteredKeysIsAllNotControlled", "filteredKeysIsAllControlled", "_ref5", "process", "env", "NODE_ENV", "newFilterStates", "_ref6", "transformColumns", "innerColumns"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/hooks/useFilter/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown from './FilterDropdown';\nfunction collectFilterStates(columns, init, pos) {\n  var filterStates = [];\n  (columns || []).forEach(function (column, index) {\n    var _a;\n    var columnPos = getColumnPos(index, pos);\n    if (column.filters || 'filterDropdown' in column || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        var filteredValues = column.filteredValue;\n        if (!('filterDropdown' in column)) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n}\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, triggerFilter, getPopupContainer, locale, pos) {\n  return columns.map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var _column$filterMultipl = column.filterMultiple,\n      filterMultiple = _column$filterMultipl === void 0 ? true : _column$filterMultipl,\n      filterMode = column.filterMode,\n      filterSearch = column.filterSearch;\n    var newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var filterState = filterStates.find(function (_ref) {\n        var key = _ref.key;\n        return columnKey === key;\n      });\n      newColumn = _extends(_extends({}, newColumn), {\n        title: function title(renderProps) {\n          return /*#__PURE__*/React.createElement(FilterDropdown, {\n            tablePrefixCls: prefixCls,\n            prefixCls: \"\".concat(prefixCls, \"-filter\"),\n            dropdownPrefixCls: dropdownPrefixCls,\n            column: newColumn,\n            columnKey: columnKey,\n            filterState: filterState,\n            filterMultiple: filterMultiple,\n            filterMode: filterMode,\n            filterSearch: filterSearch,\n            triggerFilter: triggerFilter,\n            locale: locale,\n            getPopupContainer: getPopupContainer\n          }, renderColumnTitle(column.title, renderProps));\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, triggerFilter, getPopupContainer, locale, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nexport function flattenKeys(filters) {\n  var keys = [];\n  (filters || []).forEach(function (_ref2) {\n    var value = _ref2.value,\n      children = _ref2.children;\n    keys.push(value);\n    if (children) {\n      keys = [].concat(_toConsumableArray(keys), _toConsumableArray(flattenKeys(children)));\n    }\n  });\n  return keys;\n}\nfunction generateFilterInfo(filterStates) {\n  var currentFilters = {};\n  filterStates.forEach(function (_ref3) {\n    var key = _ref3.key,\n      filteredKeys = _ref3.filteredKeys,\n      column = _ref3.column;\n    var filters = column.filters,\n      filterDropdown = column.filterDropdown;\n    if (filterDropdown) {\n      currentFilters[key] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      var keys = flattenKeys(filters);\n      currentFilters[key] = keys.filter(function (originKey) {\n        return filteredKeys.includes(String(originKey));\n      });\n    } else {\n      currentFilters[key] = null;\n    }\n  });\n  return currentFilters;\n}\nexport function getFilterData(data, filterStates) {\n  return filterStates.reduce(function (currentData, filterState) {\n    var _filterState$column = filterState.column,\n      onFilter = _filterState$column.onFilter,\n      filters = _filterState$column.filters,\n      filteredKeys = filterState.filteredKeys;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData.filter(function (record) {\n        return filteredKeys.some(function (key) {\n          var keys = flattenKeys(filters);\n          var keyIndex = keys.findIndex(function (k) {\n            return String(k) === String(key);\n          });\n          var realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n          return onFilter(realKey, record);\n        });\n      });\n    }\n    return currentData;\n  }, data);\n}\nfunction useFilter(_ref4) {\n  var prefixCls = _ref4.prefixCls,\n    dropdownPrefixCls = _ref4.dropdownPrefixCls,\n    mergedColumns = _ref4.mergedColumns,\n    onFilterChange = _ref4.onFilterChange,\n    getPopupContainer = _ref4.getPopupContainer,\n    tableLocale = _ref4.locale;\n  var _React$useState = React.useState(function () {\n      return collectFilterStates(mergedColumns, true);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    filterStates = _React$useState2[0],\n    setFilterStates = _React$useState2[1];\n  var mergedFilterStates = React.useMemo(function () {\n    var collectedStates = collectFilterStates(mergedColumns, false);\n    var filteredKeysIsAllNotControlled = true;\n    var filteredKeysIsAllControlled = true;\n    collectedStates.forEach(function (_ref5) {\n      var filteredKeys = _ref5.filteredKeys;\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      return filterStates;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'Table', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  var filters = React.useMemo(function () {\n    return generateFilterInfo(mergedFilterStates);\n  }, [mergedFilterStates]);\n  var triggerFilter = function triggerFilter(filterState) {\n    var newFilterStates = mergedFilterStates.filter(function (_ref6) {\n      var key = _ref6.key;\n      return key !== filterState.key;\n    });\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, triggerFilter, getPopupContainer, tableLocale);\n  };\n  return [transformColumns, mergedFilterStates, filters];\n}\nexport default useFilter;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,YAAY;AAC1E,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC/C,IAAIC,YAAY,GAAG,EAAE;EACrB,CAACH,OAAO,IAAI,EAAE,EAAEI,OAAO,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC/C,IAAIC,EAAE;IACN,IAAIC,SAAS,GAAGZ,YAAY,CAACU,KAAK,EAAEJ,GAAG,CAAC;IACxC,IAAIG,MAAM,CAACI,OAAO,IAAI,gBAAgB,IAAIJ,MAAM,IAAI,UAAU,IAAIA,MAAM,EAAE;MACxE,IAAI,eAAe,IAAIA,MAAM,EAAE;QAC7B;QACA,IAAIK,cAAc,GAAGL,MAAM,CAACM,aAAa;QACzC,IAAI,EAAE,gBAAgB,IAAIN,MAAM,CAAC,EAAE;UACjCK,cAAc,GAAG,CAACH,EAAE,GAAGG,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,GAAG,CAACC,MAAM,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGG,cAAc;QACpK;QACAP,YAAY,CAACW,IAAI,CAAC;UAChBT,MAAM,EAAEA,MAAM;UACdU,GAAG,EAAEpB,YAAY,CAACU,MAAM,EAAEG,SAAS,CAAC;UACpCQ,YAAY,EAAEN,cAAc;UAC5BO,aAAa,EAAEZ,MAAM,CAACa;QACxB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAf,YAAY,CAACW,IAAI,CAAC;UAChBT,MAAM,EAAEA,MAAM;UACdU,GAAG,EAAEpB,YAAY,CAACU,MAAM,EAAEG,SAAS,CAAC;UACpCQ,YAAY,EAAEf,IAAI,IAAII,MAAM,CAACc,oBAAoB,GAAGd,MAAM,CAACc,oBAAoB,GAAGC,SAAS;UAC3FH,aAAa,EAAEZ,MAAM,CAACa;QACxB,CAAC,CAAC;MACJ;IACF;IACA,IAAI,UAAU,IAAIb,MAAM,EAAE;MACxBF,YAAY,GAAG,EAAE,CAACkB,MAAM,CAAC7B,kBAAkB,CAACW,YAAY,CAAC,EAAEX,kBAAkB,CAACO,mBAAmB,CAACM,MAAM,CAACiB,QAAQ,EAAErB,IAAI,EAAEO,SAAS,CAAC,CAAC,CAAC;IACvI;EACF,CAAC,CAAC;EACF,OAAOL,YAAY;AACrB;AACA,SAASoB,YAAYA,CAACC,SAAS,EAAEC,iBAAiB,EAAEzB,OAAO,EAAEG,YAAY,EAAEuB,aAAa,EAAEC,iBAAiB,EAAEC,MAAM,EAAE1B,GAAG,EAAE;EACxH,OAAOF,OAAO,CAACY,GAAG,CAAC,UAAUP,MAAM,EAAEC,KAAK,EAAE;IAC1C,IAAIE,SAAS,GAAGZ,YAAY,CAACU,KAAK,EAAEJ,GAAG,CAAC;IACxC,IAAI2B,qBAAqB,GAAGxB,MAAM,CAACyB,cAAc;MAC/CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;MAChFE,UAAU,GAAG1B,MAAM,CAAC0B,UAAU;MAC9BC,YAAY,GAAG3B,MAAM,CAAC2B,YAAY;IACpC,IAAIC,SAAS,GAAG5B,MAAM;IACtB,IAAI4B,SAAS,CAACxB,OAAO,IAAIwB,SAAS,CAACC,cAAc,EAAE;MACjD,IAAIC,SAAS,GAAGxC,YAAY,CAACsC,SAAS,EAAEzB,SAAS,CAAC;MAClD,IAAI4B,WAAW,GAAGjC,YAAY,CAACkC,IAAI,CAAC,UAAUC,IAAI,EAAE;QAClD,IAAIvB,GAAG,GAAGuB,IAAI,CAACvB,GAAG;QAClB,OAAOoB,SAAS,KAAKpB,GAAG;MAC1B,CAAC,CAAC;MACFkB,SAAS,GAAG1C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,SAAS,CAAC,EAAE;QAC5CM,KAAK,EAAE,SAASA,KAAKA,CAACC,WAAW,EAAE;UACjC,OAAO,aAAa/C,KAAK,CAACgD,aAAa,CAAC3C,cAAc,EAAE;YACtD4C,cAAc,EAAElB,SAAS;YACzBA,SAAS,EAAE,EAAE,CAACH,MAAM,CAACG,SAAS,EAAE,SAAS,CAAC;YAC1CC,iBAAiB,EAAEA,iBAAiB;YACpCpB,MAAM,EAAE4B,SAAS;YACjBE,SAAS,EAAEA,SAAS;YACpBC,WAAW,EAAEA,WAAW;YACxBN,cAAc,EAAEA,cAAc;YAC9BC,UAAU,EAAEA,UAAU;YACtBC,YAAY,EAAEA,YAAY;YAC1BN,aAAa,EAAEA,aAAa;YAC5BE,MAAM,EAAEA,MAAM;YACdD,iBAAiB,EAAEA;UACrB,CAAC,EAAE9B,iBAAiB,CAACQ,MAAM,CAACkC,KAAK,EAAEC,WAAW,CAAC,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;IACA,IAAI,UAAU,IAAIP,SAAS,EAAE;MAC3BA,SAAS,GAAG1C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,SAAS,CAAC,EAAE;QAC5CX,QAAQ,EAAEC,YAAY,CAACC,SAAS,EAAEC,iBAAiB,EAAEQ,SAAS,CAACX,QAAQ,EAAEnB,YAAY,EAAEuB,aAAa,EAAEC,iBAAiB,EAAEC,MAAM,EAAEpB,SAAS;MAC5I,CAAC,CAAC;IACJ;IACA,OAAOyB,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,OAAO,SAASU,WAAWA,CAAClC,OAAO,EAAE;EACnC,IAAImC,IAAI,GAAG,EAAE;EACb,CAACnC,OAAO,IAAI,EAAE,EAAEL,OAAO,CAAC,UAAUyC,KAAK,EAAE;IACvC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACrBxB,QAAQ,GAAGuB,KAAK,CAACvB,QAAQ;IAC3BsB,IAAI,CAAC9B,IAAI,CAACgC,KAAK,CAAC;IAChB,IAAIxB,QAAQ,EAAE;MACZsB,IAAI,GAAG,EAAE,CAACvB,MAAM,CAAC7B,kBAAkB,CAACoD,IAAI,CAAC,EAAEpD,kBAAkB,CAACmD,WAAW,CAACrB,QAAQ,CAAC,CAAC,CAAC;IACvF;EACF,CAAC,CAAC;EACF,OAAOsB,IAAI;AACb;AACA,SAASG,kBAAkBA,CAAC5C,YAAY,EAAE;EACxC,IAAI6C,cAAc,GAAG,CAAC,CAAC;EACvB7C,YAAY,CAACC,OAAO,CAAC,UAAU6C,KAAK,EAAE;IACpC,IAAIlC,GAAG,GAAGkC,KAAK,CAAClC,GAAG;MACjBC,YAAY,GAAGiC,KAAK,CAACjC,YAAY;MACjCX,MAAM,GAAG4C,KAAK,CAAC5C,MAAM;IACvB,IAAII,OAAO,GAAGJ,MAAM,CAACI,OAAO;MAC1ByB,cAAc,GAAG7B,MAAM,CAAC6B,cAAc;IACxC,IAAIA,cAAc,EAAE;MAClBc,cAAc,CAACjC,GAAG,CAAC,GAAGC,YAAY,IAAI,IAAI;IAC5C,CAAC,MAAM,IAAIkC,KAAK,CAACC,OAAO,CAACnC,YAAY,CAAC,EAAE;MACtC,IAAI4B,IAAI,GAAGD,WAAW,CAAClC,OAAO,CAAC;MAC/BuC,cAAc,CAACjC,GAAG,CAAC,GAAG6B,IAAI,CAACQ,MAAM,CAAC,UAAUC,SAAS,EAAE;QACrD,OAAOrC,YAAY,CAACsC,QAAQ,CAACzC,MAAM,CAACwC,SAAS,CAAC,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,cAAc,CAACjC,GAAG,CAAC,GAAG,IAAI;IAC5B;EACF,CAAC,CAAC;EACF,OAAOiC,cAAc;AACvB;AACA,OAAO,SAASO,aAAaA,CAACC,IAAI,EAAErD,YAAY,EAAE;EAChD,OAAOA,YAAY,CAACsD,MAAM,CAAC,UAAUC,WAAW,EAAEtB,WAAW,EAAE;IAC7D,IAAIuB,mBAAmB,GAAGvB,WAAW,CAAC/B,MAAM;MAC1CuD,QAAQ,GAAGD,mBAAmB,CAACC,QAAQ;MACvCnD,OAAO,GAAGkD,mBAAmB,CAAClD,OAAO;MACrCO,YAAY,GAAGoB,WAAW,CAACpB,YAAY;IACzC,IAAI4C,QAAQ,IAAI5C,YAAY,IAAIA,YAAY,CAAC6C,MAAM,EAAE;MACnD,OAAOH,WAAW,CAACN,MAAM,CAAC,UAAUU,MAAM,EAAE;QAC1C,OAAO9C,YAAY,CAAC+C,IAAI,CAAC,UAAUhD,GAAG,EAAE;UACtC,IAAI6B,IAAI,GAAGD,WAAW,CAAClC,OAAO,CAAC;UAC/B,IAAIuD,QAAQ,GAAGpB,IAAI,CAACqB,SAAS,CAAC,UAAUC,CAAC,EAAE;YACzC,OAAOrD,MAAM,CAACqD,CAAC,CAAC,KAAKrD,MAAM,CAACE,GAAG,CAAC;UAClC,CAAC,CAAC;UACF,IAAIoD,OAAO,GAAGH,QAAQ,KAAK,CAAC,CAAC,GAAGpB,IAAI,CAACoB,QAAQ,CAAC,GAAGjD,GAAG;UACpD,OAAO6C,QAAQ,CAACO,OAAO,EAAEL,MAAM,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,OAAOJ,WAAW;EACpB,CAAC,EAAEF,IAAI,CAAC;AACV;AACA,SAASY,SAASA,CAACC,KAAK,EAAE;EACxB,IAAI7C,SAAS,GAAG6C,KAAK,CAAC7C,SAAS;IAC7BC,iBAAiB,GAAG4C,KAAK,CAAC5C,iBAAiB;IAC3C6C,aAAa,GAAGD,KAAK,CAACC,aAAa;IACnCC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrC5C,iBAAiB,GAAG0C,KAAK,CAAC1C,iBAAiB;IAC3C6C,WAAW,GAAGH,KAAK,CAACzC,MAAM;EAC5B,IAAI6C,eAAe,GAAGhF,KAAK,CAACiF,QAAQ,CAAC,YAAY;MAC7C,OAAO3E,mBAAmB,CAACuE,aAAa,EAAE,IAAI,CAAC;IACjD,CAAC,CAAC;IACFK,gBAAgB,GAAGrF,cAAc,CAACmF,eAAe,EAAE,CAAC,CAAC;IACrDtE,YAAY,GAAGwE,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIE,kBAAkB,GAAGpF,KAAK,CAACqF,OAAO,CAAC,YAAY;IACjD,IAAIC,eAAe,GAAGhF,mBAAmB,CAACuE,aAAa,EAAE,KAAK,CAAC;IAC/D,IAAIU,8BAA8B,GAAG,IAAI;IACzC,IAAIC,2BAA2B,GAAG,IAAI;IACtCF,eAAe,CAAC3E,OAAO,CAAC,UAAU8E,KAAK,EAAE;MACvC,IAAIlE,YAAY,GAAGkE,KAAK,CAAClE,YAAY;MACrC,IAAIA,YAAY,KAAKI,SAAS,EAAE;QAC9B4D,8BAA8B,GAAG,KAAK;MACxC,CAAC,MAAM;QACLC,2BAA2B,GAAG,KAAK;MACrC;IACF,CAAC,CAAC;IACF;IACA,IAAID,8BAA8B,EAAE;MAClC,OAAO7E,YAAY;IACrB;IACAgF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3F,OAAO,CAACuF,2BAA2B,EAAE,OAAO,EAAE,4EAA4E,CAAC,GAAG,KAAK,CAAC;IAC5K,OAAOF,eAAe;EACxB,CAAC,EAAE,CAACT,aAAa,EAAEnE,YAAY,CAAC,CAAC;EACjC,IAAIM,OAAO,GAAGhB,KAAK,CAACqF,OAAO,CAAC,YAAY;IACtC,OAAO/B,kBAAkB,CAAC8B,kBAAkB,CAAC;EAC/C,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACxB,IAAInD,aAAa,GAAG,SAASA,aAAaA,CAACU,WAAW,EAAE;IACtD,IAAIkD,eAAe,GAAGT,kBAAkB,CAACzB,MAAM,CAAC,UAAUmC,KAAK,EAAE;MAC/D,IAAIxE,GAAG,GAAGwE,KAAK,CAACxE,GAAG;MACnB,OAAOA,GAAG,KAAKqB,WAAW,CAACrB,GAAG;IAChC,CAAC,CAAC;IACFuE,eAAe,CAACxE,IAAI,CAACsB,WAAW,CAAC;IACjCwC,eAAe,CAACU,eAAe,CAAC;IAChCf,cAAc,CAACxB,kBAAkB,CAACuC,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE,CAAC;EACD,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,OAAOlE,YAAY,CAACC,SAAS,EAAEC,iBAAiB,EAAEgE,YAAY,EAAEZ,kBAAkB,EAAEnD,aAAa,EAAEC,iBAAiB,EAAE6C,WAAW,CAAC;EACpI,CAAC;EACD,OAAO,CAACgB,gBAAgB,EAAEX,kBAAkB,EAAEpE,OAAO,CAAC;AACxD;AACA,eAAe2D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}