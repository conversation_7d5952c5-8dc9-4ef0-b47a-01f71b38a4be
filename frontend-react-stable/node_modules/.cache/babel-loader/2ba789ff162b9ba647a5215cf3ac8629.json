{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport raf from \"rc-util/es/raf\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Portal from \"rc-util/es/Portal\";\nimport classNames from 'classnames';\nimport { getAlignFromPlacement, getAlignPopupClassName } from \"./utils/alignUtil\";\nimport Popup from \"./Popup\";\nimport TriggerContext from \"./context\";\nfunction noop() {}\nfunction returnEmptyString() {\n  return '';\n}\nfunction returnDocument(element) {\n  if (element) {\n    return element.ownerDocument;\n  }\n  return window.document;\n}\nvar ALL_HANDLERS = ['onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur', 'onContextMenu'];\n\n/**\n * Internal usage. Do not use in your code since this will be removed.\n */\nexport function generateTrigger(PortalComponent) {\n  var Trigger = /*#__PURE__*/function (_React$Component) {\n    _inherits(Trigger, _React$Component);\n    var _super = _createSuper(Trigger);\n\n    // ensure `getContainer` will be called only once\n    function Trigger(props) {\n      var _this;\n      _classCallCheck(this, Trigger);\n      _this = _super.call(this, props);\n      _defineProperty(_assertThisInitialized(_this), \"popupRef\", /*#__PURE__*/React.createRef());\n      _defineProperty(_assertThisInitialized(_this), \"triggerRef\", /*#__PURE__*/React.createRef());\n      _defineProperty(_assertThisInitialized(_this), \"portalContainer\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"attachId\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"clickOutsideHandler\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"touchOutsideHandler\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"contextMenuOutsideHandler1\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"contextMenuOutsideHandler2\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"mouseDownTimeout\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"focusTime\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"preClickTime\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"preTouchTime\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"delayTimer\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"hasPopupMouseDown\", void 0);\n      _defineProperty(_assertThisInitialized(_this), \"onMouseEnter\", function (e) {\n        var mouseEnterDelay = _this.props.mouseEnterDelay;\n        _this.fireEvents('onMouseEnter', e);\n        _this.delaySetPopupVisible(true, mouseEnterDelay, mouseEnterDelay ? null : e);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onMouseMove\", function (e) {\n        _this.fireEvents('onMouseMove', e);\n        _this.setPoint(e);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onMouseLeave\", function (e) {\n        _this.fireEvents('onMouseLeave', e);\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseEnter\", function () {\n        _this.clearDelayTimer();\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseLeave\", function (e) {\n        var _this$popupRef$curren;\n\n        // https://github.com/react-component/trigger/pull/13\n        // react bug?\n        if (e.relatedTarget && !e.relatedTarget.setTimeout && contains((_this$popupRef$curren = _this.popupRef.current) === null || _this$popupRef$curren === void 0 ? void 0 : _this$popupRef$curren.getElement(), e.relatedTarget)) {\n          return;\n        }\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onFocus\", function (e) {\n        _this.fireEvents('onFocus', e); // incase focusin and focusout\n\n        _this.clearDelayTimer();\n        if (_this.isFocusToShow()) {\n          _this.focusTime = Date.now();\n          _this.delaySetPopupVisible(true, _this.props.focusDelay);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onMouseDown\", function (e) {\n        _this.fireEvents('onMouseDown', e);\n        _this.preClickTime = Date.now();\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onTouchStart\", function (e) {\n        _this.fireEvents('onTouchStart', e);\n        _this.preTouchTime = Date.now();\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onBlur\", function (e) {\n        _this.fireEvents('onBlur', e);\n        _this.clearDelayTimer();\n        if (_this.isBlurToHide()) {\n          _this.delaySetPopupVisible(false, _this.props.blurDelay);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onContextMenu\", function (e) {\n        e.preventDefault();\n        _this.fireEvents('onContextMenu', e);\n        _this.setPopupVisible(true, e);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onContextMenuClose\", function () {\n        if (_this.isContextMenuToShow()) {\n          _this.close();\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onClick\", function (event) {\n        _this.fireEvents('onClick', event); // focus will trigger click\n\n        if (_this.focusTime) {\n          var preTime;\n          if (_this.preClickTime && _this.preTouchTime) {\n            preTime = Math.min(_this.preClickTime, _this.preTouchTime);\n          } else if (_this.preClickTime) {\n            preTime = _this.preClickTime;\n          } else if (_this.preTouchTime) {\n            preTime = _this.preTouchTime;\n          }\n          if (Math.abs(preTime - _this.focusTime) < 20) {\n            return;\n          }\n          _this.focusTime = 0;\n        }\n        _this.preClickTime = 0;\n        _this.preTouchTime = 0; // Only prevent default when all the action is click.\n        // https://github.com/ant-design/ant-design/issues/17043\n        // https://github.com/ant-design/ant-design/issues/17291\n\n        if (_this.isClickToShow() && (_this.isClickToHide() || _this.isBlurToHide()) && event && event.preventDefault) {\n          event.preventDefault();\n        }\n        var nextVisible = !_this.state.popupVisible;\n        if (_this.isClickToHide() && !nextVisible || nextVisible && _this.isClickToShow()) {\n          _this.setPopupVisible(!_this.state.popupVisible, event);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseDown\", function () {\n        _this.hasPopupMouseDown = true;\n        clearTimeout(_this.mouseDownTimeout);\n        _this.mouseDownTimeout = window.setTimeout(function () {\n          _this.hasPopupMouseDown = false;\n        }, 0);\n        if (_this.context) {\n          var _this$context;\n          (_this$context = _this.context).onPopupMouseDown.apply(_this$context, arguments);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"onDocumentClick\", function (event) {\n        if (_this.props.mask && !_this.props.maskClosable) {\n          return;\n        }\n        var target = event.target;\n        var root = _this.getRootDomNode();\n        var popupNode = _this.getPopupDomNode();\n        if (\n        // mousedown on the target should also close popup when action is contextMenu.\n        // https://github.com/ant-design/ant-design/issues/29853\n        (!contains(root, target) || _this.isContextMenuOnly()) && !contains(popupNode, target) && !_this.hasPopupMouseDown) {\n          _this.close();\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"getRootDomNode\", function () {\n        var getTriggerDOMNode = _this.props.getTriggerDOMNode;\n        if (getTriggerDOMNode) {\n          return getTriggerDOMNode(_this.triggerRef.current);\n        }\n        try {\n          var domNode = findDOMNode(_this.triggerRef.current);\n          if (domNode) {\n            return domNode;\n          }\n        } catch (err) {// Do nothing\n        }\n        return ReactDOM.findDOMNode(_assertThisInitialized(_this));\n      });\n      _defineProperty(_assertThisInitialized(_this), \"getPopupClassNameFromAlign\", function (align) {\n        var className = [];\n        var _this$props = _this.props,\n          popupPlacement = _this$props.popupPlacement,\n          builtinPlacements = _this$props.builtinPlacements,\n          prefixCls = _this$props.prefixCls,\n          alignPoint = _this$props.alignPoint,\n          getPopupClassNameFromAlign = _this$props.getPopupClassNameFromAlign;\n        if (popupPlacement && builtinPlacements) {\n          className.push(getAlignPopupClassName(builtinPlacements, prefixCls, align, alignPoint));\n        }\n        if (getPopupClassNameFromAlign) {\n          className.push(getPopupClassNameFromAlign(align));\n        }\n        return className.join(' ');\n      });\n      _defineProperty(_assertThisInitialized(_this), \"getComponent\", function () {\n        var _this$props2 = _this.props,\n          prefixCls = _this$props2.prefixCls,\n          destroyPopupOnHide = _this$props2.destroyPopupOnHide,\n          popupClassName = _this$props2.popupClassName,\n          onPopupAlign = _this$props2.onPopupAlign,\n          popupMotion = _this$props2.popupMotion,\n          popupAnimation = _this$props2.popupAnimation,\n          popupTransitionName = _this$props2.popupTransitionName,\n          popupStyle = _this$props2.popupStyle,\n          mask = _this$props2.mask,\n          maskAnimation = _this$props2.maskAnimation,\n          maskTransitionName = _this$props2.maskTransitionName,\n          maskMotion = _this$props2.maskMotion,\n          zIndex = _this$props2.zIndex,\n          popup = _this$props2.popup,\n          stretch = _this$props2.stretch,\n          alignPoint = _this$props2.alignPoint,\n          mobile = _this$props2.mobile,\n          forceRender = _this$props2.forceRender,\n          onPopupClick = _this$props2.onPopupClick;\n        var _this$state = _this.state,\n          popupVisible = _this$state.popupVisible,\n          point = _this$state.point;\n        var align = _this.getPopupAlign();\n        var mouseProps = {};\n        if (_this.isMouseEnterToShow()) {\n          mouseProps.onMouseEnter = _this.onPopupMouseEnter;\n        }\n        if (_this.isMouseLeaveToHide()) {\n          mouseProps.onMouseLeave = _this.onPopupMouseLeave;\n        }\n        mouseProps.onMouseDown = _this.onPopupMouseDown;\n        mouseProps.onTouchStart = _this.onPopupMouseDown;\n        return /*#__PURE__*/React.createElement(Popup, _extends({\n          prefixCls: prefixCls,\n          destroyPopupOnHide: destroyPopupOnHide,\n          visible: popupVisible,\n          point: alignPoint && point,\n          className: popupClassName,\n          align: align,\n          onAlign: onPopupAlign,\n          animation: popupAnimation,\n          getClassNameFromAlign: _this.getPopupClassNameFromAlign\n        }, mouseProps, {\n          stretch: stretch,\n          getRootDomNode: _this.getRootDomNode,\n          style: popupStyle,\n          mask: mask,\n          zIndex: zIndex,\n          transitionName: popupTransitionName,\n          maskAnimation: maskAnimation,\n          maskTransitionName: maskTransitionName,\n          maskMotion: maskMotion,\n          ref: _this.popupRef,\n          motion: popupMotion,\n          mobile: mobile,\n          forceRender: forceRender,\n          onClick: onPopupClick\n        }), typeof popup === 'function' ? popup() : popup);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"attachParent\", function (popupContainer) {\n        raf.cancel(_this.attachId);\n        var _this$props3 = _this.props,\n          getPopupContainer = _this$props3.getPopupContainer,\n          getDocument = _this$props3.getDocument;\n        var domNode = _this.getRootDomNode();\n        var mountNode;\n        if (!getPopupContainer) {\n          mountNode = getDocument(_this.getRootDomNode()).body;\n        } else if (domNode || getPopupContainer.length === 0) {\n          // Compatible for legacy getPopupContainer with domNode argument.\n          // If no need `domNode` argument, will call directly.\n          // https://codesandbox.io/s/eloquent-mclean-ss93m?file=/src/App.js\n          mountNode = getPopupContainer(domNode);\n        }\n        if (mountNode) {\n          mountNode.appendChild(popupContainer);\n        } else {\n          // Retry after frame render in case parent not ready\n          _this.attachId = raf(function () {\n            _this.attachParent(popupContainer);\n          });\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"getContainer\", function () {\n        if (!_this.portalContainer) {\n          // In React.StrictMode component will call render multiple time in first mount.\n          // When you want to refactor with FC, useRef will also init multiple time and\n          // point to different useRef instance which will create multiple element\n          // (This multiple render will not trigger effect so you can not clean up this\n          // in effect). But this is safe with class component since it always point to same class instance.\n          var getDocument = _this.props.getDocument;\n          var popupContainer = getDocument(_this.getRootDomNode()).createElement('div'); // Make sure default popup container will never cause scrollbar appearing\n          // https://github.com/react-component/trigger/issues/41\n\n          popupContainer.style.position = 'absolute';\n          popupContainer.style.top = '0';\n          popupContainer.style.left = '0';\n          popupContainer.style.width = '100%';\n          _this.portalContainer = popupContainer;\n        }\n        _this.attachParent(_this.portalContainer);\n        return _this.portalContainer;\n      });\n      _defineProperty(_assertThisInitialized(_this), \"setPoint\", function (point) {\n        var alignPoint = _this.props.alignPoint;\n        if (!alignPoint || !point) return;\n        _this.setState({\n          point: {\n            pageX: point.pageX,\n            pageY: point.pageY\n          }\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handlePortalUpdate\", function () {\n        if (_this.state.prevPopupVisible !== _this.state.popupVisible) {\n          _this.props.afterPopupVisibleChange(_this.state.popupVisible);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"triggerContextValue\", {\n        onPopupMouseDown: _this.onPopupMouseDown\n      });\n      var _popupVisible;\n      if ('popupVisible' in props) {\n        _popupVisible = !!props.popupVisible;\n      } else {\n        _popupVisible = !!props.defaultPopupVisible;\n      }\n      _this.state = {\n        prevPopupVisible: _popupVisible,\n        popupVisible: _popupVisible\n      };\n      ALL_HANDLERS.forEach(function (h) {\n        _this[\"fire\".concat(h)] = function (e) {\n          _this.fireEvents(h, e);\n        };\n      });\n      return _this;\n    }\n    _createClass(Trigger, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.componentDidUpdate();\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate() {\n        var props = this.props;\n        var state = this.state; // We must listen to `mousedown` or `touchstart`, edge case:\n        // https://github.com/ant-design/ant-design/issues/5804\n        // https://github.com/react-component/calendar/issues/250\n        // https://github.com/react-component/trigger/issues/50\n\n        if (state.popupVisible) {\n          var currentDocument;\n          if (!this.clickOutsideHandler && (this.isClickToHide() || this.isContextMenuToShow())) {\n            currentDocument = props.getDocument(this.getRootDomNode());\n            this.clickOutsideHandler = addEventListener(currentDocument, 'mousedown', this.onDocumentClick);\n          } // always hide on mobile\n\n          if (!this.touchOutsideHandler) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.touchOutsideHandler = addEventListener(currentDocument, 'touchstart', this.onDocumentClick);\n          } // close popup when trigger type contains 'onContextMenu' and document is scrolling.\n\n          if (!this.contextMenuOutsideHandler1 && this.isContextMenuToShow()) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.contextMenuOutsideHandler1 = addEventListener(currentDocument, 'scroll', this.onContextMenuClose);\n          } // close popup when trigger type contains 'onContextMenu' and window is blur.\n\n          if (!this.contextMenuOutsideHandler2 && this.isContextMenuToShow()) {\n            this.contextMenuOutsideHandler2 = addEventListener(window, 'blur', this.onContextMenuClose);\n          }\n          return;\n        }\n        this.clearOutsideHandler();\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDelayTimer();\n        this.clearOutsideHandler();\n        clearTimeout(this.mouseDownTimeout);\n        raf.cancel(this.attachId);\n      }\n    }, {\n      key: \"getPopupDomNode\",\n      value: function getPopupDomNode() {\n        var _this$popupRef$curren2;\n\n        // for test\n        return ((_this$popupRef$curren2 = this.popupRef.current) === null || _this$popupRef$curren2 === void 0 ? void 0 : _this$popupRef$curren2.getElement()) || null;\n      }\n    }, {\n      key: \"getPopupAlign\",\n      value: function getPopupAlign() {\n        var props = this.props;\n        var popupPlacement = props.popupPlacement,\n          popupAlign = props.popupAlign,\n          builtinPlacements = props.builtinPlacements;\n        if (popupPlacement && builtinPlacements) {\n          return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n        }\n        return popupAlign;\n      }\n    }, {\n      key: \"setPopupVisible\",\n      value:\n      /**\n       * @param popupVisible    Show or not the popup element\n       * @param event           SyntheticEvent, used for `pointAlign`\n       */\n      function setPopupVisible(popupVisible, event) {\n        var alignPoint = this.props.alignPoint;\n        var prevPopupVisible = this.state.popupVisible;\n        this.clearDelayTimer();\n        if (prevPopupVisible !== popupVisible) {\n          if (!('popupVisible' in this.props)) {\n            this.setState({\n              popupVisible: popupVisible,\n              prevPopupVisible: prevPopupVisible\n            });\n          }\n          this.props.onPopupVisibleChange(popupVisible);\n        } // Always record the point position since mouseEnterDelay will delay the show\n\n        if (alignPoint && event && popupVisible) {\n          this.setPoint(event);\n        }\n      }\n    }, {\n      key: \"delaySetPopupVisible\",\n      value: function delaySetPopupVisible(visible, delayS, event) {\n        var _this2 = this;\n        var delay = delayS * 1000;\n        this.clearDelayTimer();\n        if (delay) {\n          var point = event ? {\n            pageX: event.pageX,\n            pageY: event.pageY\n          } : null;\n          this.delayTimer = window.setTimeout(function () {\n            _this2.setPopupVisible(visible, point);\n            _this2.clearDelayTimer();\n          }, delay);\n        } else {\n          this.setPopupVisible(visible, event);\n        }\n      }\n    }, {\n      key: \"clearDelayTimer\",\n      value: function clearDelayTimer() {\n        if (this.delayTimer) {\n          clearTimeout(this.delayTimer);\n          this.delayTimer = null;\n        }\n      }\n    }, {\n      key: \"clearOutsideHandler\",\n      value: function clearOutsideHandler() {\n        if (this.clickOutsideHandler) {\n          this.clickOutsideHandler.remove();\n          this.clickOutsideHandler = null;\n        }\n        if (this.contextMenuOutsideHandler1) {\n          this.contextMenuOutsideHandler1.remove();\n          this.contextMenuOutsideHandler1 = null;\n        }\n        if (this.contextMenuOutsideHandler2) {\n          this.contextMenuOutsideHandler2.remove();\n          this.contextMenuOutsideHandler2 = null;\n        }\n        if (this.touchOutsideHandler) {\n          this.touchOutsideHandler.remove();\n          this.touchOutsideHandler = null;\n        }\n      }\n    }, {\n      key: \"createTwoChains\",\n      value: function createTwoChains(event) {\n        var childPros = this.props.children.props;\n        var props = this.props;\n        if (childPros[event] && props[event]) {\n          return this[\"fire\".concat(event)];\n        }\n        return childPros[event] || props[event];\n      }\n    }, {\n      key: \"isClickToShow\",\n      value: function isClickToShow() {\n        var _this$props4 = this.props,\n          action = _this$props4.action,\n          showAction = _this$props4.showAction;\n        return action.indexOf('click') !== -1 || showAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isContextMenuOnly\",\n      value: function isContextMenuOnly() {\n        var action = this.props.action;\n        return action === 'contextMenu' || action.length === 1 && action[0] === 'contextMenu';\n      }\n    }, {\n      key: \"isContextMenuToShow\",\n      value: function isContextMenuToShow() {\n        var _this$props5 = this.props,\n          action = _this$props5.action,\n          showAction = _this$props5.showAction;\n        return action.indexOf('contextMenu') !== -1 || showAction.indexOf('contextMenu') !== -1;\n      }\n    }, {\n      key: \"isClickToHide\",\n      value: function isClickToHide() {\n        var _this$props6 = this.props,\n          action = _this$props6.action,\n          hideAction = _this$props6.hideAction;\n        return action.indexOf('click') !== -1 || hideAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isMouseEnterToShow\",\n      value: function isMouseEnterToShow() {\n        var _this$props7 = this.props,\n          action = _this$props7.action,\n          showAction = _this$props7.showAction;\n        return action.indexOf('hover') !== -1 || showAction.indexOf('mouseEnter') !== -1;\n      }\n    }, {\n      key: \"isMouseLeaveToHide\",\n      value: function isMouseLeaveToHide() {\n        var _this$props8 = this.props,\n          action = _this$props8.action,\n          hideAction = _this$props8.hideAction;\n        return action.indexOf('hover') !== -1 || hideAction.indexOf('mouseLeave') !== -1;\n      }\n    }, {\n      key: \"isFocusToShow\",\n      value: function isFocusToShow() {\n        var _this$props9 = this.props,\n          action = _this$props9.action,\n          showAction = _this$props9.showAction;\n        return action.indexOf('focus') !== -1 || showAction.indexOf('focus') !== -1;\n      }\n    }, {\n      key: \"isBlurToHide\",\n      value: function isBlurToHide() {\n        var _this$props10 = this.props,\n          action = _this$props10.action,\n          hideAction = _this$props10.hideAction;\n        return action.indexOf('focus') !== -1 || hideAction.indexOf('blur') !== -1;\n      }\n    }, {\n      key: \"forcePopupAlign\",\n      value: function forcePopupAlign() {\n        if (this.state.popupVisible) {\n          var _this$popupRef$curren3;\n          (_this$popupRef$curren3 = this.popupRef.current) === null || _this$popupRef$curren3 === void 0 ? void 0 : _this$popupRef$curren3.forceAlign();\n        }\n      }\n    }, {\n      key: \"fireEvents\",\n      value: function fireEvents(type, e) {\n        var childCallback = this.props.children.props[type];\n        if (childCallback) {\n          childCallback(e);\n        }\n        var callback = this.props[type];\n        if (callback) {\n          callback(e);\n        }\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        this.setPopupVisible(false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var popupVisible = this.state.popupVisible;\n        var _this$props11 = this.props,\n          children = _this$props11.children,\n          forceRender = _this$props11.forceRender,\n          alignPoint = _this$props11.alignPoint,\n          className = _this$props11.className,\n          autoDestroy = _this$props11.autoDestroy;\n        var child = React.Children.only(children);\n        var newChildProps = {\n          key: 'trigger'\n        }; // ============================== Visible Handlers ==============================\n        // >>> ContextMenu\n\n        if (this.isContextMenuToShow()) {\n          newChildProps.onContextMenu = this.onContextMenu;\n        } else {\n          newChildProps.onContextMenu = this.createTwoChains('onContextMenu');\n        } // >>> Click\n\n        if (this.isClickToHide() || this.isClickToShow()) {\n          newChildProps.onClick = this.onClick;\n          newChildProps.onMouseDown = this.onMouseDown;\n          newChildProps.onTouchStart = this.onTouchStart;\n        } else {\n          newChildProps.onClick = this.createTwoChains('onClick');\n          newChildProps.onMouseDown = this.createTwoChains('onMouseDown');\n          newChildProps.onTouchStart = this.createTwoChains('onTouchStart');\n        } // >>> Hover(enter)\n\n        if (this.isMouseEnterToShow()) {\n          newChildProps.onMouseEnter = this.onMouseEnter; // Point align\n\n          if (alignPoint) {\n            newChildProps.onMouseMove = this.onMouseMove;\n          }\n        } else {\n          newChildProps.onMouseEnter = this.createTwoChains('onMouseEnter');\n        } // >>> Hover(leave)\n\n        if (this.isMouseLeaveToHide()) {\n          newChildProps.onMouseLeave = this.onMouseLeave;\n        } else {\n          newChildProps.onMouseLeave = this.createTwoChains('onMouseLeave');\n        } // >>> Focus\n\n        if (this.isFocusToShow() || this.isBlurToHide()) {\n          newChildProps.onFocus = this.onFocus;\n          newChildProps.onBlur = this.onBlur;\n        } else {\n          newChildProps.onFocus = this.createTwoChains('onFocus');\n          newChildProps.onBlur = this.createTwoChains('onBlur');\n        } // =================================== Render ===================================\n\n        var childrenClassName = classNames(child && child.props && child.props.className, className);\n        if (childrenClassName) {\n          newChildProps.className = childrenClassName;\n        }\n        var cloneProps = _objectSpread({}, newChildProps);\n        if (supportRef(child)) {\n          cloneProps.ref = composeRef(this.triggerRef, child.ref);\n        }\n        var trigger = /*#__PURE__*/React.cloneElement(child, cloneProps);\n        var portal; // prevent unmounting after it's rendered\n\n        if (popupVisible || this.popupRef.current || forceRender) {\n          portal = /*#__PURE__*/React.createElement(PortalComponent, {\n            key: \"portal\",\n            getContainer: this.getContainer,\n            didUpdate: this.handlePortalUpdate\n          }, this.getComponent());\n        }\n        if (!popupVisible && autoDestroy) {\n          portal = null;\n        }\n        return /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n          value: this.triggerContextValue\n        }, trigger, portal);\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref, prevState) {\n        var popupVisible = _ref.popupVisible;\n        var newState = {};\n        if (popupVisible !== undefined && prevState.popupVisible !== popupVisible) {\n          newState.popupVisible = popupVisible;\n          newState.prevPopupVisible = prevState.popupVisible;\n        }\n        return newState;\n      }\n    }]);\n    return Trigger;\n  }(React.Component);\n  _defineProperty(Trigger, \"contextType\", TriggerContext);\n  _defineProperty(Trigger, \"defaultProps\", {\n    prefixCls: 'rc-trigger-popup',\n    getPopupClassNameFromAlign: returnEmptyString,\n    getDocument: returnDocument,\n    onPopupVisibleChange: noop,\n    afterPopupVisibleChange: noop,\n    onPopupAlign: noop,\n    popupClassName: '',\n    mouseEnterDelay: 0,\n    mouseLeaveDelay: 0.1,\n    focusDelay: 0,\n    blurDelay: 0.15,\n    popupStyle: {},\n    destroyPopupOnHide: false,\n    popupAlign: {},\n    defaultPopupVisible: false,\n    mask: false,\n    maskClosable: true,\n    action: [],\n    showAction: [],\n    hideAction: [],\n    autoDestroy: false\n  });\n  return Trigger;\n}\nexport default generateTrigger(Portal);", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "React", "ReactDOM", "raf", "contains", "findDOMNode", "composeRef", "supportRef", "addEventListener", "Portal", "classNames", "getAlignFromPlacement", "getAlignPopupClassName", "Popup", "TriggerContext", "noop", "returnEmptyString", "returnDocument", "element", "ownerDocument", "window", "document", "ALL_HANDLERS", "generateTrigger", "PortalComponent", "<PERSON><PERSON>", "_React$Component", "_super", "props", "_this", "call", "createRef", "e", "mouseEnterDelay", "fireEvents", "delaySetPopupVisible", "setPoint", "mouseLeaveDelay", "clearDelayTimer", "_this$popupRef$curren", "relatedTarget", "setTimeout", "popupRef", "current", "getElement", "isFocusToShow", "focusTime", "Date", "now", "focusDelay", "preClickTime", "preTouchTime", "isBlurToHide", "blurDelay", "preventDefault", "setPopupVisible", "isContextMenuToShow", "close", "event", "preTime", "Math", "min", "abs", "isClickToShow", "isClickToHide", "nextVisible", "state", "popupVisible", "hasPopupMouseDown", "clearTimeout", "mouseDownTimeout", "context", "_this$context", "onPopupMouseDown", "apply", "arguments", "mask", "maskClosable", "target", "root", "getRootDomNode", "popupNode", "getPopupDomNode", "isContextMenuOnly", "getTriggerDOMNode", "triggerRef", "domNode", "err", "align", "className", "_this$props", "popupPlacement", "builtinPlacements", "prefixCls", "alignPoint", "getPopupClassNameFromAlign", "push", "join", "_this$props2", "destroyPopupOnHide", "popupClassName", "onPopupAlign", "popupMotion", "popupAnimation", "popupTransitionName", "popupStyle", "maskAnimation", "maskTransitionName", "maskMotion", "zIndex", "popup", "stretch", "mobile", "forceRender", "onPopupClick", "_this$state", "point", "getPopupAlign", "mouseProps", "isMouseEnterToShow", "onMouseEnter", "onPopupMouseEnter", "isMouseLeaveToHide", "onMouseLeave", "onPopupMouseLeave", "onMouseDown", "onTouchStart", "createElement", "visible", "onAlign", "animation", "getClassNameFromAlign", "style", "transitionName", "ref", "motion", "onClick", "popup<PERSON><PERSON><PERSON>", "cancel", "attachId", "_this$props3", "getPopupContainer", "getDocument", "mountNode", "body", "length", "append<PERSON><PERSON><PERSON>", "attachParent", "portalContainer", "position", "top", "left", "width", "setState", "pageX", "pageY", "prevPopupVisible", "afterPopupVisibleChange", "_popupVisible", "defaultPopupVisible", "for<PERSON>ach", "h", "concat", "key", "value", "componentDidMount", "componentDidUpdate", "currentDocument", "clickOutsideHandler", "onDocumentClick", "touchOutsideHandler", "contextMenuOutsideHandler1", "onContextMenuClose", "contextMenuOutsideHandler2", "clearOutsideHandler", "componentWillUnmount", "_this$popupRef$curren2", "popupAlign", "onPopupVisibleChange", "delayS", "_this2", "delay", "delayTimer", "remove", "createTwoChains", "child<PERSON><PERSON>", "children", "_this$props4", "action", "showAction", "indexOf", "_this$props5", "_this$props6", "hideAction", "_this$props7", "_this$props8", "_this$props9", "_this$props10", "forcePopupAlign", "_this$popupRef$curren3", "forceAlign", "type", "child<PERSON><PERSON><PERSON>", "callback", "render", "_this$props11", "autoDestroy", "child", "Children", "only", "newChildProps", "onContextMenu", "onMouseMove", "onFocus", "onBlur", "childrenClassName", "cloneProps", "trigger", "cloneElement", "portal", "getContainer", "didUpdate", "handlePortalUpdate", "getComponent", "Provider", "triggerContextValue", "getDerivedStateFromProps", "_ref", "prevState", "newState", "undefined", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-trigger/es/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport raf from \"rc-util/es/raf\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Portal from \"rc-util/es/Portal\";\nimport classNames from 'classnames';\nimport { getAlignFromPlacement, getAlignPopupClassName } from \"./utils/alignUtil\";\nimport Popup from \"./Popup\";\nimport TriggerContext from \"./context\";\n\nfunction noop() {}\n\nfunction returnEmptyString() {\n  return '';\n}\n\nfunction returnDocument(element) {\n  if (element) {\n    return element.ownerDocument;\n  }\n\n  return window.document;\n}\n\nvar ALL_HANDLERS = ['onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur', 'onContextMenu'];\n\n/**\n * Internal usage. Do not use in your code since this will be removed.\n */\nexport function generateTrigger(PortalComponent) {\n  var Trigger = /*#__PURE__*/function (_React$Component) {\n    _inherits(Trigger, _React$Component);\n\n    var _super = _createSuper(Trigger);\n\n    // ensure `getContainer` will be called only once\n    function Trigger(props) {\n      var _this;\n\n      _classCallCheck(this, Trigger);\n\n      _this = _super.call(this, props);\n\n      _defineProperty(_assertThisInitialized(_this), \"popupRef\", /*#__PURE__*/React.createRef());\n\n      _defineProperty(_assertThisInitialized(_this), \"triggerRef\", /*#__PURE__*/React.createRef());\n\n      _defineProperty(_assertThisInitialized(_this), \"portalContainer\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"attachId\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"clickOutsideHandler\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"touchOutsideHandler\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"contextMenuOutsideHandler1\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"contextMenuOutsideHandler2\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"mouseDownTimeout\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"focusTime\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"preClickTime\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"preTouchTime\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"delayTimer\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"hasPopupMouseDown\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"onMouseEnter\", function (e) {\n        var mouseEnterDelay = _this.props.mouseEnterDelay;\n\n        _this.fireEvents('onMouseEnter', e);\n\n        _this.delaySetPopupVisible(true, mouseEnterDelay, mouseEnterDelay ? null : e);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onMouseMove\", function (e) {\n        _this.fireEvents('onMouseMove', e);\n\n        _this.setPoint(e);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onMouseLeave\", function (e) {\n        _this.fireEvents('onMouseLeave', e);\n\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseEnter\", function () {\n        _this.clearDelayTimer();\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseLeave\", function (e) {\n        var _this$popupRef$curren;\n\n        // https://github.com/react-component/trigger/pull/13\n        // react bug?\n        if (e.relatedTarget && !e.relatedTarget.setTimeout && contains((_this$popupRef$curren = _this.popupRef.current) === null || _this$popupRef$curren === void 0 ? void 0 : _this$popupRef$curren.getElement(), e.relatedTarget)) {\n          return;\n        }\n\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onFocus\", function (e) {\n        _this.fireEvents('onFocus', e); // incase focusin and focusout\n\n\n        _this.clearDelayTimer();\n\n        if (_this.isFocusToShow()) {\n          _this.focusTime = Date.now();\n\n          _this.delaySetPopupVisible(true, _this.props.focusDelay);\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onMouseDown\", function (e) {\n        _this.fireEvents('onMouseDown', e);\n\n        _this.preClickTime = Date.now();\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onTouchStart\", function (e) {\n        _this.fireEvents('onTouchStart', e);\n\n        _this.preTouchTime = Date.now();\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onBlur\", function (e) {\n        _this.fireEvents('onBlur', e);\n\n        _this.clearDelayTimer();\n\n        if (_this.isBlurToHide()) {\n          _this.delaySetPopupVisible(false, _this.props.blurDelay);\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onContextMenu\", function (e) {\n        e.preventDefault();\n\n        _this.fireEvents('onContextMenu', e);\n\n        _this.setPopupVisible(true, e);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onContextMenuClose\", function () {\n        if (_this.isContextMenuToShow()) {\n          _this.close();\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onClick\", function (event) {\n        _this.fireEvents('onClick', event); // focus will trigger click\n\n\n        if (_this.focusTime) {\n          var preTime;\n\n          if (_this.preClickTime && _this.preTouchTime) {\n            preTime = Math.min(_this.preClickTime, _this.preTouchTime);\n          } else if (_this.preClickTime) {\n            preTime = _this.preClickTime;\n          } else if (_this.preTouchTime) {\n            preTime = _this.preTouchTime;\n          }\n\n          if (Math.abs(preTime - _this.focusTime) < 20) {\n            return;\n          }\n\n          _this.focusTime = 0;\n        }\n\n        _this.preClickTime = 0;\n        _this.preTouchTime = 0; // Only prevent default when all the action is click.\n        // https://github.com/ant-design/ant-design/issues/17043\n        // https://github.com/ant-design/ant-design/issues/17291\n\n        if (_this.isClickToShow() && (_this.isClickToHide() || _this.isBlurToHide()) && event && event.preventDefault) {\n          event.preventDefault();\n        }\n\n        var nextVisible = !_this.state.popupVisible;\n\n        if (_this.isClickToHide() && !nextVisible || nextVisible && _this.isClickToShow()) {\n          _this.setPopupVisible(!_this.state.popupVisible, event);\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onPopupMouseDown\", function () {\n        _this.hasPopupMouseDown = true;\n        clearTimeout(_this.mouseDownTimeout);\n        _this.mouseDownTimeout = window.setTimeout(function () {\n          _this.hasPopupMouseDown = false;\n        }, 0);\n\n        if (_this.context) {\n          var _this$context;\n\n          (_this$context = _this.context).onPopupMouseDown.apply(_this$context, arguments);\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"onDocumentClick\", function (event) {\n        if (_this.props.mask && !_this.props.maskClosable) {\n          return;\n        }\n\n        var target = event.target;\n\n        var root = _this.getRootDomNode();\n\n        var popupNode = _this.getPopupDomNode();\n\n        if ( // mousedown on the target should also close popup when action is contextMenu.\n        // https://github.com/ant-design/ant-design/issues/29853\n        (!contains(root, target) || _this.isContextMenuOnly()) && !contains(popupNode, target) && !_this.hasPopupMouseDown) {\n          _this.close();\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"getRootDomNode\", function () {\n        var getTriggerDOMNode = _this.props.getTriggerDOMNode;\n\n        if (getTriggerDOMNode) {\n          return getTriggerDOMNode(_this.triggerRef.current);\n        }\n\n        try {\n          var domNode = findDOMNode(_this.triggerRef.current);\n\n          if (domNode) {\n            return domNode;\n          }\n        } catch (err) {// Do nothing\n        }\n\n        return ReactDOM.findDOMNode(_assertThisInitialized(_this));\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"getPopupClassNameFromAlign\", function (align) {\n        var className = [];\n        var _this$props = _this.props,\n            popupPlacement = _this$props.popupPlacement,\n            builtinPlacements = _this$props.builtinPlacements,\n            prefixCls = _this$props.prefixCls,\n            alignPoint = _this$props.alignPoint,\n            getPopupClassNameFromAlign = _this$props.getPopupClassNameFromAlign;\n\n        if (popupPlacement && builtinPlacements) {\n          className.push(getAlignPopupClassName(builtinPlacements, prefixCls, align, alignPoint));\n        }\n\n        if (getPopupClassNameFromAlign) {\n          className.push(getPopupClassNameFromAlign(align));\n        }\n\n        return className.join(' ');\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"getComponent\", function () {\n        var _this$props2 = _this.props,\n            prefixCls = _this$props2.prefixCls,\n            destroyPopupOnHide = _this$props2.destroyPopupOnHide,\n            popupClassName = _this$props2.popupClassName,\n            onPopupAlign = _this$props2.onPopupAlign,\n            popupMotion = _this$props2.popupMotion,\n            popupAnimation = _this$props2.popupAnimation,\n            popupTransitionName = _this$props2.popupTransitionName,\n            popupStyle = _this$props2.popupStyle,\n            mask = _this$props2.mask,\n            maskAnimation = _this$props2.maskAnimation,\n            maskTransitionName = _this$props2.maskTransitionName,\n            maskMotion = _this$props2.maskMotion,\n            zIndex = _this$props2.zIndex,\n            popup = _this$props2.popup,\n            stretch = _this$props2.stretch,\n            alignPoint = _this$props2.alignPoint,\n            mobile = _this$props2.mobile,\n            forceRender = _this$props2.forceRender,\n            onPopupClick = _this$props2.onPopupClick;\n        var _this$state = _this.state,\n            popupVisible = _this$state.popupVisible,\n            point = _this$state.point;\n\n        var align = _this.getPopupAlign();\n\n        var mouseProps = {};\n\n        if (_this.isMouseEnterToShow()) {\n          mouseProps.onMouseEnter = _this.onPopupMouseEnter;\n        }\n\n        if (_this.isMouseLeaveToHide()) {\n          mouseProps.onMouseLeave = _this.onPopupMouseLeave;\n        }\n\n        mouseProps.onMouseDown = _this.onPopupMouseDown;\n        mouseProps.onTouchStart = _this.onPopupMouseDown;\n        return /*#__PURE__*/React.createElement(Popup, _extends({\n          prefixCls: prefixCls,\n          destroyPopupOnHide: destroyPopupOnHide,\n          visible: popupVisible,\n          point: alignPoint && point,\n          className: popupClassName,\n          align: align,\n          onAlign: onPopupAlign,\n          animation: popupAnimation,\n          getClassNameFromAlign: _this.getPopupClassNameFromAlign\n        }, mouseProps, {\n          stretch: stretch,\n          getRootDomNode: _this.getRootDomNode,\n          style: popupStyle,\n          mask: mask,\n          zIndex: zIndex,\n          transitionName: popupTransitionName,\n          maskAnimation: maskAnimation,\n          maskTransitionName: maskTransitionName,\n          maskMotion: maskMotion,\n          ref: _this.popupRef,\n          motion: popupMotion,\n          mobile: mobile,\n          forceRender: forceRender,\n          onClick: onPopupClick\n        }), typeof popup === 'function' ? popup() : popup);\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"attachParent\", function (popupContainer) {\n        raf.cancel(_this.attachId);\n        var _this$props3 = _this.props,\n            getPopupContainer = _this$props3.getPopupContainer,\n            getDocument = _this$props3.getDocument;\n\n        var domNode = _this.getRootDomNode();\n\n        var mountNode;\n\n        if (!getPopupContainer) {\n          mountNode = getDocument(_this.getRootDomNode()).body;\n        } else if (domNode || getPopupContainer.length === 0) {\n          // Compatible for legacy getPopupContainer with domNode argument.\n          // If no need `domNode` argument, will call directly.\n          // https://codesandbox.io/s/eloquent-mclean-ss93m?file=/src/App.js\n          mountNode = getPopupContainer(domNode);\n        }\n\n        if (mountNode) {\n          mountNode.appendChild(popupContainer);\n        } else {\n          // Retry after frame render in case parent not ready\n          _this.attachId = raf(function () {\n            _this.attachParent(popupContainer);\n          });\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"getContainer\", function () {\n        if (!_this.portalContainer) {\n          // In React.StrictMode component will call render multiple time in first mount.\n          // When you want to refactor with FC, useRef will also init multiple time and\n          // point to different useRef instance which will create multiple element\n          // (This multiple render will not trigger effect so you can not clean up this\n          // in effect). But this is safe with class component since it always point to same class instance.\n          var getDocument = _this.props.getDocument;\n          var popupContainer = getDocument(_this.getRootDomNode()).createElement('div'); // Make sure default popup container will never cause scrollbar appearing\n          // https://github.com/react-component/trigger/issues/41\n\n          popupContainer.style.position = 'absolute';\n          popupContainer.style.top = '0';\n          popupContainer.style.left = '0';\n          popupContainer.style.width = '100%';\n          _this.portalContainer = popupContainer;\n        }\n\n        _this.attachParent(_this.portalContainer);\n\n        return _this.portalContainer;\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"setPoint\", function (point) {\n        var alignPoint = _this.props.alignPoint;\n        if (!alignPoint || !point) return;\n\n        _this.setState({\n          point: {\n            pageX: point.pageX,\n            pageY: point.pageY\n          }\n        });\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"handlePortalUpdate\", function () {\n        if (_this.state.prevPopupVisible !== _this.state.popupVisible) {\n          _this.props.afterPopupVisibleChange(_this.state.popupVisible);\n        }\n      });\n\n      _defineProperty(_assertThisInitialized(_this), \"triggerContextValue\", {\n        onPopupMouseDown: _this.onPopupMouseDown\n      });\n\n      var _popupVisible;\n\n      if ('popupVisible' in props) {\n        _popupVisible = !!props.popupVisible;\n      } else {\n        _popupVisible = !!props.defaultPopupVisible;\n      }\n\n      _this.state = {\n        prevPopupVisible: _popupVisible,\n        popupVisible: _popupVisible\n      };\n      ALL_HANDLERS.forEach(function (h) {\n        _this[\"fire\".concat(h)] = function (e) {\n          _this.fireEvents(h, e);\n        };\n      });\n      return _this;\n    }\n\n    _createClass(Trigger, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.componentDidUpdate();\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate() {\n        var props = this.props;\n        var state = this.state; // We must listen to `mousedown` or `touchstart`, edge case:\n        // https://github.com/ant-design/ant-design/issues/5804\n        // https://github.com/react-component/calendar/issues/250\n        // https://github.com/react-component/trigger/issues/50\n\n        if (state.popupVisible) {\n          var currentDocument;\n\n          if (!this.clickOutsideHandler && (this.isClickToHide() || this.isContextMenuToShow())) {\n            currentDocument = props.getDocument(this.getRootDomNode());\n            this.clickOutsideHandler = addEventListener(currentDocument, 'mousedown', this.onDocumentClick);\n          } // always hide on mobile\n\n\n          if (!this.touchOutsideHandler) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.touchOutsideHandler = addEventListener(currentDocument, 'touchstart', this.onDocumentClick);\n          } // close popup when trigger type contains 'onContextMenu' and document is scrolling.\n\n\n          if (!this.contextMenuOutsideHandler1 && this.isContextMenuToShow()) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.contextMenuOutsideHandler1 = addEventListener(currentDocument, 'scroll', this.onContextMenuClose);\n          } // close popup when trigger type contains 'onContextMenu' and window is blur.\n\n\n          if (!this.contextMenuOutsideHandler2 && this.isContextMenuToShow()) {\n            this.contextMenuOutsideHandler2 = addEventListener(window, 'blur', this.onContextMenuClose);\n          }\n\n          return;\n        }\n\n        this.clearOutsideHandler();\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDelayTimer();\n        this.clearOutsideHandler();\n        clearTimeout(this.mouseDownTimeout);\n        raf.cancel(this.attachId);\n      }\n    }, {\n      key: \"getPopupDomNode\",\n      value: function getPopupDomNode() {\n        var _this$popupRef$curren2;\n\n        // for test\n        return ((_this$popupRef$curren2 = this.popupRef.current) === null || _this$popupRef$curren2 === void 0 ? void 0 : _this$popupRef$curren2.getElement()) || null;\n      }\n    }, {\n      key: \"getPopupAlign\",\n      value: function getPopupAlign() {\n        var props = this.props;\n        var popupPlacement = props.popupPlacement,\n            popupAlign = props.popupAlign,\n            builtinPlacements = props.builtinPlacements;\n\n        if (popupPlacement && builtinPlacements) {\n          return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n        }\n\n        return popupAlign;\n      }\n    }, {\n      key: \"setPopupVisible\",\n      value:\n      /**\n       * @param popupVisible    Show or not the popup element\n       * @param event           SyntheticEvent, used for `pointAlign`\n       */\n      function setPopupVisible(popupVisible, event) {\n        var alignPoint = this.props.alignPoint;\n        var prevPopupVisible = this.state.popupVisible;\n        this.clearDelayTimer();\n\n        if (prevPopupVisible !== popupVisible) {\n          if (!('popupVisible' in this.props)) {\n            this.setState({\n              popupVisible: popupVisible,\n              prevPopupVisible: prevPopupVisible\n            });\n          }\n\n          this.props.onPopupVisibleChange(popupVisible);\n        } // Always record the point position since mouseEnterDelay will delay the show\n\n\n        if (alignPoint && event && popupVisible) {\n          this.setPoint(event);\n        }\n      }\n    }, {\n      key: \"delaySetPopupVisible\",\n      value: function delaySetPopupVisible(visible, delayS, event) {\n        var _this2 = this;\n\n        var delay = delayS * 1000;\n        this.clearDelayTimer();\n\n        if (delay) {\n          var point = event ? {\n            pageX: event.pageX,\n            pageY: event.pageY\n          } : null;\n          this.delayTimer = window.setTimeout(function () {\n            _this2.setPopupVisible(visible, point);\n\n            _this2.clearDelayTimer();\n          }, delay);\n        } else {\n          this.setPopupVisible(visible, event);\n        }\n      }\n    }, {\n      key: \"clearDelayTimer\",\n      value: function clearDelayTimer() {\n        if (this.delayTimer) {\n          clearTimeout(this.delayTimer);\n          this.delayTimer = null;\n        }\n      }\n    }, {\n      key: \"clearOutsideHandler\",\n      value: function clearOutsideHandler() {\n        if (this.clickOutsideHandler) {\n          this.clickOutsideHandler.remove();\n          this.clickOutsideHandler = null;\n        }\n\n        if (this.contextMenuOutsideHandler1) {\n          this.contextMenuOutsideHandler1.remove();\n          this.contextMenuOutsideHandler1 = null;\n        }\n\n        if (this.contextMenuOutsideHandler2) {\n          this.contextMenuOutsideHandler2.remove();\n          this.contextMenuOutsideHandler2 = null;\n        }\n\n        if (this.touchOutsideHandler) {\n          this.touchOutsideHandler.remove();\n          this.touchOutsideHandler = null;\n        }\n      }\n    }, {\n      key: \"createTwoChains\",\n      value: function createTwoChains(event) {\n        var childPros = this.props.children.props;\n        var props = this.props;\n\n        if (childPros[event] && props[event]) {\n          return this[\"fire\".concat(event)];\n        }\n\n        return childPros[event] || props[event];\n      }\n    }, {\n      key: \"isClickToShow\",\n      value: function isClickToShow() {\n        var _this$props4 = this.props,\n            action = _this$props4.action,\n            showAction = _this$props4.showAction;\n        return action.indexOf('click') !== -1 || showAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isContextMenuOnly\",\n      value: function isContextMenuOnly() {\n        var action = this.props.action;\n        return action === 'contextMenu' || action.length === 1 && action[0] === 'contextMenu';\n      }\n    }, {\n      key: \"isContextMenuToShow\",\n      value: function isContextMenuToShow() {\n        var _this$props5 = this.props,\n            action = _this$props5.action,\n            showAction = _this$props5.showAction;\n        return action.indexOf('contextMenu') !== -1 || showAction.indexOf('contextMenu') !== -1;\n      }\n    }, {\n      key: \"isClickToHide\",\n      value: function isClickToHide() {\n        var _this$props6 = this.props,\n            action = _this$props6.action,\n            hideAction = _this$props6.hideAction;\n        return action.indexOf('click') !== -1 || hideAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isMouseEnterToShow\",\n      value: function isMouseEnterToShow() {\n        var _this$props7 = this.props,\n            action = _this$props7.action,\n            showAction = _this$props7.showAction;\n        return action.indexOf('hover') !== -1 || showAction.indexOf('mouseEnter') !== -1;\n      }\n    }, {\n      key: \"isMouseLeaveToHide\",\n      value: function isMouseLeaveToHide() {\n        var _this$props8 = this.props,\n            action = _this$props8.action,\n            hideAction = _this$props8.hideAction;\n        return action.indexOf('hover') !== -1 || hideAction.indexOf('mouseLeave') !== -1;\n      }\n    }, {\n      key: \"isFocusToShow\",\n      value: function isFocusToShow() {\n        var _this$props9 = this.props,\n            action = _this$props9.action,\n            showAction = _this$props9.showAction;\n        return action.indexOf('focus') !== -1 || showAction.indexOf('focus') !== -1;\n      }\n    }, {\n      key: \"isBlurToHide\",\n      value: function isBlurToHide() {\n        var _this$props10 = this.props,\n            action = _this$props10.action,\n            hideAction = _this$props10.hideAction;\n        return action.indexOf('focus') !== -1 || hideAction.indexOf('blur') !== -1;\n      }\n    }, {\n      key: \"forcePopupAlign\",\n      value: function forcePopupAlign() {\n        if (this.state.popupVisible) {\n          var _this$popupRef$curren3;\n\n          (_this$popupRef$curren3 = this.popupRef.current) === null || _this$popupRef$curren3 === void 0 ? void 0 : _this$popupRef$curren3.forceAlign();\n        }\n      }\n    }, {\n      key: \"fireEvents\",\n      value: function fireEvents(type, e) {\n        var childCallback = this.props.children.props[type];\n\n        if (childCallback) {\n          childCallback(e);\n        }\n\n        var callback = this.props[type];\n\n        if (callback) {\n          callback(e);\n        }\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        this.setPopupVisible(false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var popupVisible = this.state.popupVisible;\n        var _this$props11 = this.props,\n            children = _this$props11.children,\n            forceRender = _this$props11.forceRender,\n            alignPoint = _this$props11.alignPoint,\n            className = _this$props11.className,\n            autoDestroy = _this$props11.autoDestroy;\n        var child = React.Children.only(children);\n        var newChildProps = {\n          key: 'trigger'\n        }; // ============================== Visible Handlers ==============================\n        // >>> ContextMenu\n\n        if (this.isContextMenuToShow()) {\n          newChildProps.onContextMenu = this.onContextMenu;\n        } else {\n          newChildProps.onContextMenu = this.createTwoChains('onContextMenu');\n        } // >>> Click\n\n\n        if (this.isClickToHide() || this.isClickToShow()) {\n          newChildProps.onClick = this.onClick;\n          newChildProps.onMouseDown = this.onMouseDown;\n          newChildProps.onTouchStart = this.onTouchStart;\n        } else {\n          newChildProps.onClick = this.createTwoChains('onClick');\n          newChildProps.onMouseDown = this.createTwoChains('onMouseDown');\n          newChildProps.onTouchStart = this.createTwoChains('onTouchStart');\n        } // >>> Hover(enter)\n\n\n        if (this.isMouseEnterToShow()) {\n          newChildProps.onMouseEnter = this.onMouseEnter; // Point align\n\n          if (alignPoint) {\n            newChildProps.onMouseMove = this.onMouseMove;\n          }\n        } else {\n          newChildProps.onMouseEnter = this.createTwoChains('onMouseEnter');\n        } // >>> Hover(leave)\n\n\n        if (this.isMouseLeaveToHide()) {\n          newChildProps.onMouseLeave = this.onMouseLeave;\n        } else {\n          newChildProps.onMouseLeave = this.createTwoChains('onMouseLeave');\n        } // >>> Focus\n\n\n        if (this.isFocusToShow() || this.isBlurToHide()) {\n          newChildProps.onFocus = this.onFocus;\n          newChildProps.onBlur = this.onBlur;\n        } else {\n          newChildProps.onFocus = this.createTwoChains('onFocus');\n          newChildProps.onBlur = this.createTwoChains('onBlur');\n        } // =================================== Render ===================================\n\n\n        var childrenClassName = classNames(child && child.props && child.props.className, className);\n\n        if (childrenClassName) {\n          newChildProps.className = childrenClassName;\n        }\n\n        var cloneProps = _objectSpread({}, newChildProps);\n\n        if (supportRef(child)) {\n          cloneProps.ref = composeRef(this.triggerRef, child.ref);\n        }\n\n        var trigger = /*#__PURE__*/React.cloneElement(child, cloneProps);\n        var portal; // prevent unmounting after it's rendered\n\n        if (popupVisible || this.popupRef.current || forceRender) {\n          portal = /*#__PURE__*/React.createElement(PortalComponent, {\n            key: \"portal\",\n            getContainer: this.getContainer,\n            didUpdate: this.handlePortalUpdate\n          }, this.getComponent());\n        }\n\n        if (!popupVisible && autoDestroy) {\n          portal = null;\n        }\n\n        return /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n          value: this.triggerContextValue\n        }, trigger, portal);\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref, prevState) {\n        var popupVisible = _ref.popupVisible;\n        var newState = {};\n\n        if (popupVisible !== undefined && prevState.popupVisible !== popupVisible) {\n          newState.popupVisible = popupVisible;\n          newState.prevPopupVisible = prevState.popupVisible;\n        }\n\n        return newState;\n      }\n    }]);\n\n    return Trigger;\n  }(React.Component);\n\n  _defineProperty(Trigger, \"contextType\", TriggerContext);\n\n  _defineProperty(Trigger, \"defaultProps\", {\n    prefixCls: 'rc-trigger-popup',\n    getPopupClassNameFromAlign: returnEmptyString,\n    getDocument: returnDocument,\n    onPopupVisibleChange: noop,\n    afterPopupVisibleChange: noop,\n    onPopupAlign: noop,\n    popupClassName: '',\n    mouseEnterDelay: 0,\n    mouseLeaveDelay: 0.1,\n    focusDelay: 0,\n    blurDelay: 0.15,\n    popupStyle: {},\n    destroyPopupOnHide: false,\n    popupAlign: {},\n    defaultPopupVisible: false,\n    mask: false,\n    maskClosable: true,\n    action: [],\n    showAction: [],\n    hideAction: [],\n    autoDestroy: false\n  });\n\n  return Trigger;\n}\nexport default generateTrigger(Portal);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,qBAAqB,EAAEC,sBAAsB,QAAQ,mBAAmB;AACjF,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,WAAW;AAEtC,SAASC,IAAIA,CAAA,EAAG,CAAC;AAEjB,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,OAAO,EAAE;AACX;AAEA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIA,OAAO,EAAE;IACX,OAAOA,OAAO,CAACC,aAAa;EAC9B;EAEA,OAAOC,MAAM,CAACC,QAAQ;AACxB;AAEA,IAAIC,YAAY,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC;;AAEnI;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,eAAe,EAAE;EAC/C,IAAIC,OAAO,GAAG,aAAa,UAAUC,gBAAgB,EAAE;IACrD5B,SAAS,CAAC2B,OAAO,EAAEC,gBAAgB,CAAC;IAEpC,IAAIC,MAAM,GAAG5B,YAAY,CAAC0B,OAAO,CAAC;;IAElC;IACA,SAASA,OAAOA,CAACG,KAAK,EAAE;MACtB,IAAIC,KAAK;MAETlC,eAAe,CAAC,IAAI,EAAE8B,OAAO,CAAC;MAE9BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;MAEhC5B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,UAAU,EAAE,aAAa5B,KAAK,CAAC8B,SAAS,CAAC,CAAC,CAAC;MAE1F/B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,YAAY,EAAE,aAAa5B,KAAK,CAAC8B,SAAS,CAAC,CAAC,CAAC;MAE5F/B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;MAEzE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;MAElE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAC;MAE7E7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAC;MAE7E7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,4BAA4B,EAAE,KAAK,CAAC,CAAC;MAEpF7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,4BAA4B,EAAE,KAAK,CAAC,CAAC;MAEpF7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;MAE1E7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;MAEnE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;MAEtE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;MAEtE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;MAEpE7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;MAE3E7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUG,CAAC,EAAE;QAC1E,IAAIC,eAAe,GAAGJ,KAAK,CAACD,KAAK,CAACK,eAAe;QAEjDJ,KAAK,CAACK,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnCH,KAAK,CAACM,oBAAoB,CAAC,IAAI,EAAEF,eAAe,EAAEA,eAAe,GAAG,IAAI,GAAGD,CAAC,CAAC;MAC/E,CAAC,CAAC;MAEFhC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUG,CAAC,EAAE;QACzEH,KAAK,CAACK,UAAU,CAAC,aAAa,EAAEF,CAAC,CAAC;QAElCH,KAAK,CAACO,QAAQ,CAACJ,CAAC,CAAC;MACnB,CAAC,CAAC;MAEFhC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUG,CAAC,EAAE;QAC1EH,KAAK,CAACK,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnCH,KAAK,CAACM,oBAAoB,CAAC,KAAK,EAAEN,KAAK,CAACD,KAAK,CAACS,eAAe,CAAC;MAChE,CAAC,CAAC;MAEFrC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,mBAAmB,EAAE,YAAY;QAC9EA,KAAK,CAACS,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC;MAEFtC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,mBAAmB,EAAE,UAAUG,CAAC,EAAE;QAC/E,IAAIO,qBAAqB;;QAEzB;QACA;QACA,IAAIP,CAAC,CAACQ,aAAa,IAAI,CAACR,CAAC,CAACQ,aAAa,CAACC,UAAU,IAAIrC,QAAQ,CAAC,CAACmC,qBAAqB,GAAGV,KAAK,CAACa,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAIJ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,UAAU,CAAC,CAAC,EAAEZ,CAAC,CAACQ,aAAa,CAAC,EAAE;UAC5N;QACF;QAEAX,KAAK,CAACM,oBAAoB,CAAC,KAAK,EAAEN,KAAK,CAACD,KAAK,CAACS,eAAe,CAAC;MAChE,CAAC,CAAC;MAEFrC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,SAAS,EAAE,UAAUG,CAAC,EAAE;QACrEH,KAAK,CAACK,UAAU,CAAC,SAAS,EAAEF,CAAC,CAAC,CAAC,CAAC;;QAGhCH,KAAK,CAACS,eAAe,CAAC,CAAC;QAEvB,IAAIT,KAAK,CAACgB,aAAa,CAAC,CAAC,EAAE;UACzBhB,KAAK,CAACiB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAE5BnB,KAAK,CAACM,oBAAoB,CAAC,IAAI,EAAEN,KAAK,CAACD,KAAK,CAACqB,UAAU,CAAC;QAC1D;MACF,CAAC,CAAC;MAEFjD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUG,CAAC,EAAE;QACzEH,KAAK,CAACK,UAAU,CAAC,aAAa,EAAEF,CAAC,CAAC;QAElCH,KAAK,CAACqB,YAAY,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;MACjC,CAAC,CAAC;MAEFhD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUG,CAAC,EAAE;QAC1EH,KAAK,CAACK,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnCH,KAAK,CAACsB,YAAY,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;MACjC,CAAC,CAAC;MAEFhD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,QAAQ,EAAE,UAAUG,CAAC,EAAE;QACpEH,KAAK,CAACK,UAAU,CAAC,QAAQ,EAAEF,CAAC,CAAC;QAE7BH,KAAK,CAACS,eAAe,CAAC,CAAC;QAEvB,IAAIT,KAAK,CAACuB,YAAY,CAAC,CAAC,EAAE;UACxBvB,KAAK,CAACM,oBAAoB,CAAC,KAAK,EAAEN,KAAK,CAACD,KAAK,CAACyB,SAAS,CAAC;QAC1D;MACF,CAAC,CAAC;MAEFrD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,eAAe,EAAE,UAAUG,CAAC,EAAE;QAC3EA,CAAC,CAACsB,cAAc,CAAC,CAAC;QAElBzB,KAAK,CAACK,UAAU,CAAC,eAAe,EAAEF,CAAC,CAAC;QAEpCH,KAAK,CAAC0B,eAAe,CAAC,IAAI,EAAEvB,CAAC,CAAC;MAChC,CAAC,CAAC;MAEFhC,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;QAC/E,IAAIA,KAAK,CAAC2B,mBAAmB,CAAC,CAAC,EAAE;UAC/B3B,KAAK,CAAC4B,KAAK,CAAC,CAAC;QACf;MACF,CAAC,CAAC;MAEFzD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,SAAS,EAAE,UAAU6B,KAAK,EAAE;QACzE7B,KAAK,CAACK,UAAU,CAAC,SAAS,EAAEwB,KAAK,CAAC,CAAC,CAAC;;QAGpC,IAAI7B,KAAK,CAACiB,SAAS,EAAE;UACnB,IAAIa,OAAO;UAEX,IAAI9B,KAAK,CAACqB,YAAY,IAAIrB,KAAK,CAACsB,YAAY,EAAE;YAC5CQ,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAChC,KAAK,CAACqB,YAAY,EAAErB,KAAK,CAACsB,YAAY,CAAC;UAC5D,CAAC,MAAM,IAAItB,KAAK,CAACqB,YAAY,EAAE;YAC7BS,OAAO,GAAG9B,KAAK,CAACqB,YAAY;UAC9B,CAAC,MAAM,IAAIrB,KAAK,CAACsB,YAAY,EAAE;YAC7BQ,OAAO,GAAG9B,KAAK,CAACsB,YAAY;UAC9B;UAEA,IAAIS,IAAI,CAACE,GAAG,CAACH,OAAO,GAAG9B,KAAK,CAACiB,SAAS,CAAC,GAAG,EAAE,EAAE;YAC5C;UACF;UAEAjB,KAAK,CAACiB,SAAS,GAAG,CAAC;QACrB;QAEAjB,KAAK,CAACqB,YAAY,GAAG,CAAC;QACtBrB,KAAK,CAACsB,YAAY,GAAG,CAAC,CAAC,CAAC;QACxB;QACA;;QAEA,IAAItB,KAAK,CAACkC,aAAa,CAAC,CAAC,KAAKlC,KAAK,CAACmC,aAAa,CAAC,CAAC,IAAInC,KAAK,CAACuB,YAAY,CAAC,CAAC,CAAC,IAAIM,KAAK,IAAIA,KAAK,CAACJ,cAAc,EAAE;UAC7GI,KAAK,CAACJ,cAAc,CAAC,CAAC;QACxB;QAEA,IAAIW,WAAW,GAAG,CAACpC,KAAK,CAACqC,KAAK,CAACC,YAAY;QAE3C,IAAItC,KAAK,CAACmC,aAAa,CAAC,CAAC,IAAI,CAACC,WAAW,IAAIA,WAAW,IAAIpC,KAAK,CAACkC,aAAa,CAAC,CAAC,EAAE;UACjFlC,KAAK,CAAC0B,eAAe,CAAC,CAAC1B,KAAK,CAACqC,KAAK,CAACC,YAAY,EAAET,KAAK,CAAC;QACzD;MACF,CAAC,CAAC;MAEF1D,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,kBAAkB,EAAE,YAAY;QAC7EA,KAAK,CAACuC,iBAAiB,GAAG,IAAI;QAC9BC,YAAY,CAACxC,KAAK,CAACyC,gBAAgB,CAAC;QACpCzC,KAAK,CAACyC,gBAAgB,GAAGlD,MAAM,CAACqB,UAAU,CAAC,YAAY;UACrDZ,KAAK,CAACuC,iBAAiB,GAAG,KAAK;QACjC,CAAC,EAAE,CAAC,CAAC;QAEL,IAAIvC,KAAK,CAAC0C,OAAO,EAAE;UACjB,IAAIC,aAAa;UAEjB,CAACA,aAAa,GAAG3C,KAAK,CAAC0C,OAAO,EAAEE,gBAAgB,CAACC,KAAK,CAACF,aAAa,EAAEG,SAAS,CAAC;QAClF;MACF,CAAC,CAAC;MAEF3E,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAU6B,KAAK,EAAE;QACjF,IAAI7B,KAAK,CAACD,KAAK,CAACgD,IAAI,IAAI,CAAC/C,KAAK,CAACD,KAAK,CAACiD,YAAY,EAAE;UACjD;QACF;QAEA,IAAIC,MAAM,GAAGpB,KAAK,CAACoB,MAAM;QAEzB,IAAIC,IAAI,GAAGlD,KAAK,CAACmD,cAAc,CAAC,CAAC;QAEjC,IAAIC,SAAS,GAAGpD,KAAK,CAACqD,eAAe,CAAC,CAAC;QAEvC;QAAK;QACL;QACA,CAAC,CAAC9E,QAAQ,CAAC2E,IAAI,EAAED,MAAM,CAAC,IAAIjD,KAAK,CAACsD,iBAAiB,CAAC,CAAC,KAAK,CAAC/E,QAAQ,CAAC6E,SAAS,EAAEH,MAAM,CAAC,IAAI,CAACjD,KAAK,CAACuC,iBAAiB,EAAE;UAClHvC,KAAK,CAAC4B,KAAK,CAAC,CAAC;QACf;MACF,CAAC,CAAC;MAEFzD,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,gBAAgB,EAAE,YAAY;QAC3E,IAAIuD,iBAAiB,GAAGvD,KAAK,CAACD,KAAK,CAACwD,iBAAiB;QAErD,IAAIA,iBAAiB,EAAE;UACrB,OAAOA,iBAAiB,CAACvD,KAAK,CAACwD,UAAU,CAAC1C,OAAO,CAAC;QACpD;QAEA,IAAI;UACF,IAAI2C,OAAO,GAAGjF,WAAW,CAACwB,KAAK,CAACwD,UAAU,CAAC1C,OAAO,CAAC;UAEnD,IAAI2C,OAAO,EAAE;YACX,OAAOA,OAAO;UAChB;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE,CAAC;QAAA;QAGf,OAAOrF,QAAQ,CAACG,WAAW,CAACR,sBAAsB,CAACgC,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;MAEF7B,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,4BAA4B,EAAE,UAAU2D,KAAK,EAAE;QAC5F,IAAIC,SAAS,GAAG,EAAE;QAClB,IAAIC,WAAW,GAAG7D,KAAK,CAACD,KAAK;UACzB+D,cAAc,GAAGD,WAAW,CAACC,cAAc;UAC3CC,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB;UACjDC,SAAS,GAAGH,WAAW,CAACG,SAAS;UACjCC,UAAU,GAAGJ,WAAW,CAACI,UAAU;UACnCC,0BAA0B,GAAGL,WAAW,CAACK,0BAA0B;QAEvE,IAAIJ,cAAc,IAAIC,iBAAiB,EAAE;UACvCH,SAAS,CAACO,IAAI,CAACpF,sBAAsB,CAACgF,iBAAiB,EAAEC,SAAS,EAAEL,KAAK,EAAEM,UAAU,CAAC,CAAC;QACzF;QAEA,IAAIC,0BAA0B,EAAE;UAC9BN,SAAS,CAACO,IAAI,CAACD,0BAA0B,CAACP,KAAK,CAAC,CAAC;QACnD;QAEA,OAAOC,SAAS,CAACQ,IAAI,CAAC,GAAG,CAAC;MAC5B,CAAC,CAAC;MAEFjG,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;QACzE,IAAIqE,YAAY,GAAGrE,KAAK,CAACD,KAAK;UAC1BiE,SAAS,GAAGK,YAAY,CAACL,SAAS;UAClCM,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;UACpDC,cAAc,GAAGF,YAAY,CAACE,cAAc;UAC5CC,YAAY,GAAGH,YAAY,CAACG,YAAY;UACxCC,WAAW,GAAGJ,YAAY,CAACI,WAAW;UACtCC,cAAc,GAAGL,YAAY,CAACK,cAAc;UAC5CC,mBAAmB,GAAGN,YAAY,CAACM,mBAAmB;UACtDC,UAAU,GAAGP,YAAY,CAACO,UAAU;UACpC7B,IAAI,GAAGsB,YAAY,CAACtB,IAAI;UACxB8B,aAAa,GAAGR,YAAY,CAACQ,aAAa;UAC1CC,kBAAkB,GAAGT,YAAY,CAACS,kBAAkB;UACpDC,UAAU,GAAGV,YAAY,CAACU,UAAU;UACpCC,MAAM,GAAGX,YAAY,CAACW,MAAM;UAC5BC,KAAK,GAAGZ,YAAY,CAACY,KAAK;UAC1BC,OAAO,GAAGb,YAAY,CAACa,OAAO;UAC9BjB,UAAU,GAAGI,YAAY,CAACJ,UAAU;UACpCkB,MAAM,GAAGd,YAAY,CAACc,MAAM;UAC5BC,WAAW,GAAGf,YAAY,CAACe,WAAW;UACtCC,YAAY,GAAGhB,YAAY,CAACgB,YAAY;QAC5C,IAAIC,WAAW,GAAGtF,KAAK,CAACqC,KAAK;UACzBC,YAAY,GAAGgD,WAAW,CAAChD,YAAY;UACvCiD,KAAK,GAAGD,WAAW,CAACC,KAAK;QAE7B,IAAI5B,KAAK,GAAG3D,KAAK,CAACwF,aAAa,CAAC,CAAC;QAEjC,IAAIC,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAIzF,KAAK,CAAC0F,kBAAkB,CAAC,CAAC,EAAE;UAC9BD,UAAU,CAACE,YAAY,GAAG3F,KAAK,CAAC4F,iBAAiB;QACnD;QAEA,IAAI5F,KAAK,CAAC6F,kBAAkB,CAAC,CAAC,EAAE;UAC9BJ,UAAU,CAACK,YAAY,GAAG9F,KAAK,CAAC+F,iBAAiB;QACnD;QAEAN,UAAU,CAACO,WAAW,GAAGhG,KAAK,CAAC4C,gBAAgB;QAC/C6C,UAAU,CAACQ,YAAY,GAAGjG,KAAK,CAAC4C,gBAAgB;QAChD,OAAO,aAAaxE,KAAK,CAAC8H,aAAa,CAAClH,KAAK,EAAEnB,QAAQ,CAAC;UACtDmG,SAAS,EAAEA,SAAS;UACpBM,kBAAkB,EAAEA,kBAAkB;UACtC6B,OAAO,EAAE7D,YAAY;UACrBiD,KAAK,EAAEtB,UAAU,IAAIsB,KAAK;UAC1B3B,SAAS,EAAEW,cAAc;UACzBZ,KAAK,EAAEA,KAAK;UACZyC,OAAO,EAAE5B,YAAY;UACrB6B,SAAS,EAAE3B,cAAc;UACzB4B,qBAAqB,EAAEtG,KAAK,CAACkE;QAC/B,CAAC,EAAEuB,UAAU,EAAE;UACbP,OAAO,EAAEA,OAAO;UAChB/B,cAAc,EAAEnD,KAAK,CAACmD,cAAc;UACpCoD,KAAK,EAAE3B,UAAU;UACjB7B,IAAI,EAAEA,IAAI;UACViC,MAAM,EAAEA,MAAM;UACdwB,cAAc,EAAE7B,mBAAmB;UACnCE,aAAa,EAAEA,aAAa;UAC5BC,kBAAkB,EAAEA,kBAAkB;UACtCC,UAAU,EAAEA,UAAU;UACtB0B,GAAG,EAAEzG,KAAK,CAACa,QAAQ;UACnB6F,MAAM,EAAEjC,WAAW;UACnBU,MAAM,EAAEA,MAAM;UACdC,WAAW,EAAEA,WAAW;UACxBuB,OAAO,EAAEtB;QACX,CAAC,CAAC,EAAE,OAAOJ,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK,CAAC;MACpD,CAAC,CAAC;MAEF9G,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAU4G,cAAc,EAAE;QACvFtI,GAAG,CAACuI,MAAM,CAAC7G,KAAK,CAAC8G,QAAQ,CAAC;QAC1B,IAAIC,YAAY,GAAG/G,KAAK,CAACD,KAAK;UAC1BiH,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;UAClDC,WAAW,GAAGF,YAAY,CAACE,WAAW;QAE1C,IAAIxD,OAAO,GAAGzD,KAAK,CAACmD,cAAc,CAAC,CAAC;QAEpC,IAAI+D,SAAS;QAEb,IAAI,CAACF,iBAAiB,EAAE;UACtBE,SAAS,GAAGD,WAAW,CAACjH,KAAK,CAACmD,cAAc,CAAC,CAAC,CAAC,CAACgE,IAAI;QACtD,CAAC,MAAM,IAAI1D,OAAO,IAAIuD,iBAAiB,CAACI,MAAM,KAAK,CAAC,EAAE;UACpD;UACA;UACA;UACAF,SAAS,GAAGF,iBAAiB,CAACvD,OAAO,CAAC;QACxC;QAEA,IAAIyD,SAAS,EAAE;UACbA,SAAS,CAACG,WAAW,CAACT,cAAc,CAAC;QACvC,CAAC,MAAM;UACL;UACA5G,KAAK,CAAC8G,QAAQ,GAAGxI,GAAG,CAAC,YAAY;YAC/B0B,KAAK,CAACsH,YAAY,CAACV,cAAc,CAAC;UACpC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEFzI,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;QACzE,IAAI,CAACA,KAAK,CAACuH,eAAe,EAAE;UAC1B;UACA;UACA;UACA;UACA;UACA,IAAIN,WAAW,GAAGjH,KAAK,CAACD,KAAK,CAACkH,WAAW;UACzC,IAAIL,cAAc,GAAGK,WAAW,CAACjH,KAAK,CAACmD,cAAc,CAAC,CAAC,CAAC,CAAC+C,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;UAC/E;;UAEAU,cAAc,CAACL,KAAK,CAACiB,QAAQ,GAAG,UAAU;UAC1CZ,cAAc,CAACL,KAAK,CAACkB,GAAG,GAAG,GAAG;UAC9Bb,cAAc,CAACL,KAAK,CAACmB,IAAI,GAAG,GAAG;UAC/Bd,cAAc,CAACL,KAAK,CAACoB,KAAK,GAAG,MAAM;UACnC3H,KAAK,CAACuH,eAAe,GAAGX,cAAc;QACxC;QAEA5G,KAAK,CAACsH,YAAY,CAACtH,KAAK,CAACuH,eAAe,CAAC;QAEzC,OAAOvH,KAAK,CAACuH,eAAe;MAC9B,CAAC,CAAC;MAEFpJ,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,UAAU,EAAE,UAAUuF,KAAK,EAAE;QAC1E,IAAItB,UAAU,GAAGjE,KAAK,CAACD,KAAK,CAACkE,UAAU;QACvC,IAAI,CAACA,UAAU,IAAI,CAACsB,KAAK,EAAE;QAE3BvF,KAAK,CAAC4H,QAAQ,CAAC;UACbrC,KAAK,EAAE;YACLsC,KAAK,EAAEtC,KAAK,CAACsC,KAAK;YAClBC,KAAK,EAAEvC,KAAK,CAACuC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF3J,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;QAC/E,IAAIA,KAAK,CAACqC,KAAK,CAAC0F,gBAAgB,KAAK/H,KAAK,CAACqC,KAAK,CAACC,YAAY,EAAE;UAC7DtC,KAAK,CAACD,KAAK,CAACiI,uBAAuB,CAAChI,KAAK,CAACqC,KAAK,CAACC,YAAY,CAAC;QAC/D;MACF,CAAC,CAAC;MAEFnE,eAAe,CAACH,sBAAsB,CAACgC,KAAK,CAAC,EAAE,qBAAqB,EAAE;QACpE4C,gBAAgB,EAAE5C,KAAK,CAAC4C;MAC1B,CAAC,CAAC;MAEF,IAAIqF,aAAa;MAEjB,IAAI,cAAc,IAAIlI,KAAK,EAAE;QAC3BkI,aAAa,GAAG,CAAC,CAAClI,KAAK,CAACuC,YAAY;MACtC,CAAC,MAAM;QACL2F,aAAa,GAAG,CAAC,CAAClI,KAAK,CAACmI,mBAAmB;MAC7C;MAEAlI,KAAK,CAACqC,KAAK,GAAG;QACZ0F,gBAAgB,EAAEE,aAAa;QAC/B3F,YAAY,EAAE2F;MAChB,CAAC;MACDxI,YAAY,CAAC0I,OAAO,CAAC,UAAUC,CAAC,EAAE;QAChCpI,KAAK,CAAC,MAAM,CAACqI,MAAM,CAACD,CAAC,CAAC,CAAC,GAAG,UAAUjI,CAAC,EAAE;UACrCH,KAAK,CAACK,UAAU,CAAC+H,CAAC,EAAEjI,CAAC,CAAC;QACxB,CAAC;MACH,CAAC,CAAC;MACF,OAAOH,KAAK;IACd;IAEAjC,YAAY,CAAC6B,OAAO,EAAE,CAAC;MACrB0I,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;QAClC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC3B;IACF,CAAC,EAAE;MACDH,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAAA,EAAG;QACnC,IAAI1I,KAAK,GAAG,IAAI,CAACA,KAAK;QACtB,IAAIsC,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;QACxB;QACA;QACA;;QAEA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB,IAAIoG,eAAe;UAEnB,IAAI,CAAC,IAAI,CAACC,mBAAmB,KAAK,IAAI,CAACxG,aAAa,CAAC,CAAC,IAAI,IAAI,CAACR,mBAAmB,CAAC,CAAC,CAAC,EAAE;YACrF+G,eAAe,GAAG3I,KAAK,CAACkH,WAAW,CAAC,IAAI,CAAC9D,cAAc,CAAC,CAAC,CAAC;YAC1D,IAAI,CAACwF,mBAAmB,GAAGhK,gBAAgB,CAAC+J,eAAe,EAAE,WAAW,EAAE,IAAI,CAACE,eAAe,CAAC;UACjG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAACC,mBAAmB,EAAE;YAC7BH,eAAe,GAAGA,eAAe,IAAI3I,KAAK,CAACkH,WAAW,CAAC,IAAI,CAAC9D,cAAc,CAAC,CAAC,CAAC;YAC7E,IAAI,CAAC0F,mBAAmB,GAAGlK,gBAAgB,CAAC+J,eAAe,EAAE,YAAY,EAAE,IAAI,CAACE,eAAe,CAAC;UAClG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAACE,0BAA0B,IAAI,IAAI,CAACnH,mBAAmB,CAAC,CAAC,EAAE;YAClE+G,eAAe,GAAGA,eAAe,IAAI3I,KAAK,CAACkH,WAAW,CAAC,IAAI,CAAC9D,cAAc,CAAC,CAAC,CAAC;YAC7E,IAAI,CAAC2F,0BAA0B,GAAGnK,gBAAgB,CAAC+J,eAAe,EAAE,QAAQ,EAAE,IAAI,CAACK,kBAAkB,CAAC;UACxG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAACC,0BAA0B,IAAI,IAAI,CAACrH,mBAAmB,CAAC,CAAC,EAAE;YAClE,IAAI,CAACqH,0BAA0B,GAAGrK,gBAAgB,CAACY,MAAM,EAAE,MAAM,EAAE,IAAI,CAACwJ,kBAAkB,CAAC;UAC7F;UAEA;QACF;QAEA,IAAI,CAACE,mBAAmB,CAAC,CAAC;MAC5B;IACF,CAAC,EAAE;MACDX,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE,SAASW,oBAAoBA,CAAA,EAAG;QACrC,IAAI,CAACzI,eAAe,CAAC,CAAC;QACtB,IAAI,CAACwI,mBAAmB,CAAC,CAAC;QAC1BzG,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;QACnCnE,GAAG,CAACuI,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC;MAC3B;IACF,CAAC,EAAE;MACDwB,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAASlF,eAAeA,CAAA,EAAG;QAChC,IAAI8F,sBAAsB;;QAE1B;QACA,OAAO,CAAC,CAACA,sBAAsB,GAAG,IAAI,CAACtI,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAIqI,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACpI,UAAU,CAAC,CAAC,KAAK,IAAI;MAChK;IACF,CAAC,EAAE;MACDuH,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAAS/C,aAAaA,CAAA,EAAG;QAC9B,IAAIzF,KAAK,GAAG,IAAI,CAACA,KAAK;QACtB,IAAI+D,cAAc,GAAG/D,KAAK,CAAC+D,cAAc;UACrCsF,UAAU,GAAGrJ,KAAK,CAACqJ,UAAU;UAC7BrF,iBAAiB,GAAGhE,KAAK,CAACgE,iBAAiB;QAE/C,IAAID,cAAc,IAAIC,iBAAiB,EAAE;UACvC,OAAOjF,qBAAqB,CAACiF,iBAAiB,EAAED,cAAc,EAAEsF,UAAU,CAAC;QAC7E;QAEA,OAAOA,UAAU;MACnB;IACF,CAAC,EAAE;MACDd,GAAG,EAAE,iBAAiB;MACtBC,KAAK;MACL;AACN;AACA;AACA;MACM,SAAS7G,eAAeA,CAACY,YAAY,EAAET,KAAK,EAAE;QAC5C,IAAIoC,UAAU,GAAG,IAAI,CAAClE,KAAK,CAACkE,UAAU;QACtC,IAAI8D,gBAAgB,GAAG,IAAI,CAAC1F,KAAK,CAACC,YAAY;QAC9C,IAAI,CAAC7B,eAAe,CAAC,CAAC;QAEtB,IAAIsH,gBAAgB,KAAKzF,YAAY,EAAE;UACrC,IAAI,EAAE,cAAc,IAAI,IAAI,CAACvC,KAAK,CAAC,EAAE;YACnC,IAAI,CAAC6H,QAAQ,CAAC;cACZtF,YAAY,EAAEA,YAAY;cAC1ByF,gBAAgB,EAAEA;YACpB,CAAC,CAAC;UACJ;UAEA,IAAI,CAAChI,KAAK,CAACsJ,oBAAoB,CAAC/G,YAAY,CAAC;QAC/C,CAAC,CAAC;;QAGF,IAAI2B,UAAU,IAAIpC,KAAK,IAAIS,YAAY,EAAE;UACvC,IAAI,CAAC/B,QAAQ,CAACsB,KAAK,CAAC;QACtB;MACF;IACF,CAAC,EAAE;MACDyG,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE,SAASjI,oBAAoBA,CAAC6F,OAAO,EAAEmD,MAAM,EAAEzH,KAAK,EAAE;QAC3D,IAAI0H,MAAM,GAAG,IAAI;QAEjB,IAAIC,KAAK,GAAGF,MAAM,GAAG,IAAI;QACzB,IAAI,CAAC7I,eAAe,CAAC,CAAC;QAEtB,IAAI+I,KAAK,EAAE;UACT,IAAIjE,KAAK,GAAG1D,KAAK,GAAG;YAClBgG,KAAK,EAAEhG,KAAK,CAACgG,KAAK;YAClBC,KAAK,EAAEjG,KAAK,CAACiG;UACf,CAAC,GAAG,IAAI;UACR,IAAI,CAAC2B,UAAU,GAAGlK,MAAM,CAACqB,UAAU,CAAC,YAAY;YAC9C2I,MAAM,CAAC7H,eAAe,CAACyE,OAAO,EAAEZ,KAAK,CAAC;YAEtCgE,MAAM,CAAC9I,eAAe,CAAC,CAAC;UAC1B,CAAC,EAAE+I,KAAK,CAAC;QACX,CAAC,MAAM;UACL,IAAI,CAAC9H,eAAe,CAACyE,OAAO,EAAEtE,KAAK,CAAC;QACtC;MACF;IACF,CAAC,EAAE;MACDyG,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAAS9H,eAAeA,CAAA,EAAG;QAChC,IAAI,IAAI,CAACgJ,UAAU,EAAE;UACnBjH,YAAY,CAAC,IAAI,CAACiH,UAAU,CAAC;UAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;QACxB;MACF;IACF,CAAC,EAAE;MACDnB,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE,SAASU,mBAAmBA,CAAA,EAAG;QACpC,IAAI,IAAI,CAACN,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACe,MAAM,CAAC,CAAC;UACjC,IAAI,CAACf,mBAAmB,GAAG,IAAI;QACjC;QAEA,IAAI,IAAI,CAACG,0BAA0B,EAAE;UACnC,IAAI,CAACA,0BAA0B,CAACY,MAAM,CAAC,CAAC;UACxC,IAAI,CAACZ,0BAA0B,GAAG,IAAI;QACxC;QAEA,IAAI,IAAI,CAACE,0BAA0B,EAAE;UACnC,IAAI,CAACA,0BAA0B,CAACU,MAAM,CAAC,CAAC;UACxC,IAAI,CAACV,0BAA0B,GAAG,IAAI;QACxC;QAEA,IAAI,IAAI,CAACH,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACa,MAAM,CAAC,CAAC;UACjC,IAAI,CAACb,mBAAmB,GAAG,IAAI;QACjC;MACF;IACF,CAAC,EAAE;MACDP,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAASoB,eAAeA,CAAC9H,KAAK,EAAE;QACrC,IAAI+H,SAAS,GAAG,IAAI,CAAC7J,KAAK,CAAC8J,QAAQ,CAAC9J,KAAK;QACzC,IAAIA,KAAK,GAAG,IAAI,CAACA,KAAK;QAEtB,IAAI6J,SAAS,CAAC/H,KAAK,CAAC,IAAI9B,KAAK,CAAC8B,KAAK,CAAC,EAAE;UACpC,OAAO,IAAI,CAAC,MAAM,CAACwG,MAAM,CAACxG,KAAK,CAAC,CAAC;QACnC;QAEA,OAAO+H,SAAS,CAAC/H,KAAK,CAAC,IAAI9B,KAAK,CAAC8B,KAAK,CAAC;MACzC;IACF,CAAC,EAAE;MACDyG,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAASrG,aAAaA,CAAA,EAAG;QAC9B,IAAI4H,YAAY,GAAG,IAAI,CAAC/J,KAAK;UACzBgK,MAAM,GAAGD,YAAY,CAACC,MAAM;UAC5BC,UAAU,GAAGF,YAAY,CAACE,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,SAASjF,iBAAiBA,CAAA,EAAG;QAClC,IAAIyG,MAAM,GAAG,IAAI,CAAChK,KAAK,CAACgK,MAAM;QAC9B,OAAOA,MAAM,KAAK,aAAa,IAAIA,MAAM,CAAC3C,MAAM,KAAK,CAAC,IAAI2C,MAAM,CAAC,CAAC,CAAC,KAAK,aAAa;MACvF;IACF,CAAC,EAAE;MACDzB,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE,SAAS5G,mBAAmBA,CAAA,EAAG;QACpC,IAAIuI,YAAY,GAAG,IAAI,CAACnK,KAAK;UACzBgK,MAAM,GAAGG,YAAY,CAACH,MAAM;UAC5BC,UAAU,GAAGE,YAAY,CAACF,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;MACzF;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAASpG,aAAaA,CAAA,EAAG;QAC9B,IAAIgI,YAAY,GAAG,IAAI,CAACpK,KAAK;UACzBgK,MAAM,GAAGI,YAAY,CAACJ,MAAM;UAC5BK,UAAU,GAAGD,YAAY,CAACC,UAAU;QACxC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAAS7C,kBAAkBA,CAAA,EAAG;QACnC,IAAI2E,YAAY,GAAG,IAAI,CAACtK,KAAK;UACzBgK,MAAM,GAAGM,YAAY,CAACN,MAAM;UAC5BC,UAAU,GAAGK,YAAY,CAACL,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;MAClF;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAAS1C,kBAAkBA,CAAA,EAAG;QACnC,IAAIyE,YAAY,GAAG,IAAI,CAACvK,KAAK;UACzBgK,MAAM,GAAGO,YAAY,CAACP,MAAM;UAC5BK,UAAU,GAAGE,YAAY,CAACF,UAAU;QACxC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;MAClF;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAASvH,aAAaA,CAAA,EAAG;QAC9B,IAAIuJ,YAAY,GAAG,IAAI,CAACxK,KAAK;UACzBgK,MAAM,GAAGQ,YAAY,CAACR,MAAM;UAC5BC,UAAU,GAAGO,YAAY,CAACP,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,SAAShH,YAAYA,CAAA,EAAG;QAC7B,IAAIiJ,aAAa,GAAG,IAAI,CAACzK,KAAK;UAC1BgK,MAAM,GAAGS,aAAa,CAACT,MAAM;UAC7BK,UAAU,GAAGI,aAAa,CAACJ,UAAU;QACzC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;MAC5E;IACF,CAAC,EAAE;MACD3B,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAASkC,eAAeA,CAAA,EAAG;QAChC,IAAI,IAAI,CAACpI,KAAK,CAACC,YAAY,EAAE;UAC3B,IAAIoI,sBAAsB;UAE1B,CAACA,sBAAsB,GAAG,IAAI,CAAC7J,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAI4J,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACC,UAAU,CAAC,CAAC;QAC/I;MACF;IACF,CAAC,EAAE;MACDrC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,SAASlI,UAAUA,CAACuK,IAAI,EAAEzK,CAAC,EAAE;QAClC,IAAI0K,aAAa,GAAG,IAAI,CAAC9K,KAAK,CAAC8J,QAAQ,CAAC9J,KAAK,CAAC6K,IAAI,CAAC;QAEnD,IAAIC,aAAa,EAAE;UACjBA,aAAa,CAAC1K,CAAC,CAAC;QAClB;QAEA,IAAI2K,QAAQ,GAAG,IAAI,CAAC/K,KAAK,CAAC6K,IAAI,CAAC;QAE/B,IAAIE,QAAQ,EAAE;UACZA,QAAQ,CAAC3K,CAAC,CAAC;QACb;MACF;IACF,CAAC,EAAE;MACDmI,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE,SAAS3G,KAAKA,CAAA,EAAG;QACtB,IAAI,CAACF,eAAe,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC,EAAE;MACD4G,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,SAASwC,MAAMA,CAAA,EAAG;QACvB,IAAIzI,YAAY,GAAG,IAAI,CAACD,KAAK,CAACC,YAAY;QAC1C,IAAI0I,aAAa,GAAG,IAAI,CAACjL,KAAK;UAC1B8J,QAAQ,GAAGmB,aAAa,CAACnB,QAAQ;UACjCzE,WAAW,GAAG4F,aAAa,CAAC5F,WAAW;UACvCnB,UAAU,GAAG+G,aAAa,CAAC/G,UAAU;UACrCL,SAAS,GAAGoH,aAAa,CAACpH,SAAS;UACnCqH,WAAW,GAAGD,aAAa,CAACC,WAAW;QAC3C,IAAIC,KAAK,GAAG9M,KAAK,CAAC+M,QAAQ,CAACC,IAAI,CAACvB,QAAQ,CAAC;QACzC,IAAIwB,aAAa,GAAG;UAClB/C,GAAG,EAAE;QACP,CAAC,CAAC,CAAC;QACH;;QAEA,IAAI,IAAI,CAAC3G,mBAAmB,CAAC,CAAC,EAAE;UAC9B0J,aAAa,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa;QAClD,CAAC,MAAM;UACLD,aAAa,CAACC,aAAa,GAAG,IAAI,CAAC3B,eAAe,CAAC,eAAe,CAAC;QACrE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACxH,aAAa,CAAC,CAAC,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;UAChDmJ,aAAa,CAAC1E,OAAO,GAAG,IAAI,CAACA,OAAO;UACpC0E,aAAa,CAACrF,WAAW,GAAG,IAAI,CAACA,WAAW;UAC5CqF,aAAa,CAACpF,YAAY,GAAG,IAAI,CAACA,YAAY;QAChD,CAAC,MAAM;UACLoF,aAAa,CAAC1E,OAAO,GAAG,IAAI,CAACgD,eAAe,CAAC,SAAS,CAAC;UACvD0B,aAAa,CAACrF,WAAW,GAAG,IAAI,CAAC2D,eAAe,CAAC,aAAa,CAAC;UAC/D0B,aAAa,CAACpF,YAAY,GAAG,IAAI,CAAC0D,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACjE,kBAAkB,CAAC,CAAC,EAAE;UAC7B2F,aAAa,CAAC1F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;;UAEhD,IAAI1B,UAAU,EAAE;YACdoH,aAAa,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW;UAC9C;QACF,CAAC,MAAM;UACLF,aAAa,CAAC1F,YAAY,GAAG,IAAI,CAACgE,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAAC9D,kBAAkB,CAAC,CAAC,EAAE;UAC7BwF,aAAa,CAACvF,YAAY,GAAG,IAAI,CAACA,YAAY;QAChD,CAAC,MAAM;UACLuF,aAAa,CAACvF,YAAY,GAAG,IAAI,CAAC6D,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAAC3I,aAAa,CAAC,CAAC,IAAI,IAAI,CAACO,YAAY,CAAC,CAAC,EAAE;UAC/C8J,aAAa,CAACG,OAAO,GAAG,IAAI,CAACA,OAAO;UACpCH,aAAa,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM;QACpC,CAAC,MAAM;UACLJ,aAAa,CAACG,OAAO,GAAG,IAAI,CAAC7B,eAAe,CAAC,SAAS,CAAC;UACvD0B,aAAa,CAACI,MAAM,GAAG,IAAI,CAAC9B,eAAe,CAAC,QAAQ,CAAC;QACvD,CAAC,CAAC;;QAGF,IAAI+B,iBAAiB,GAAG7M,UAAU,CAACqM,KAAK,IAAIA,KAAK,CAACnL,KAAK,IAAImL,KAAK,CAACnL,KAAK,CAAC6D,SAAS,EAAEA,SAAS,CAAC;QAE5F,IAAI8H,iBAAiB,EAAE;UACrBL,aAAa,CAACzH,SAAS,GAAG8H,iBAAiB;QAC7C;QAEA,IAAIC,UAAU,GAAG/N,aAAa,CAAC,CAAC,CAAC,EAAEyN,aAAa,CAAC;QAEjD,IAAI3M,UAAU,CAACwM,KAAK,CAAC,EAAE;UACrBS,UAAU,CAAClF,GAAG,GAAGhI,UAAU,CAAC,IAAI,CAAC+E,UAAU,EAAE0H,KAAK,CAACzE,GAAG,CAAC;QACzD;QAEA,IAAImF,OAAO,GAAG,aAAaxN,KAAK,CAACyN,YAAY,CAACX,KAAK,EAAES,UAAU,CAAC;QAChE,IAAIG,MAAM,CAAC,CAAC;;QAEZ,IAAIxJ,YAAY,IAAI,IAAI,CAACzB,QAAQ,CAACC,OAAO,IAAIsE,WAAW,EAAE;UACxD0G,MAAM,GAAG,aAAa1N,KAAK,CAAC8H,aAAa,CAACvG,eAAe,EAAE;YACzD2I,GAAG,EAAE,QAAQ;YACbyD,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/BC,SAAS,EAAE,IAAI,CAACC;UAClB,CAAC,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;QACzB;QAEA,IAAI,CAAC5J,YAAY,IAAI2I,WAAW,EAAE;UAChCa,MAAM,GAAG,IAAI;QACf;QAEA,OAAO,aAAa1N,KAAK,CAAC8H,aAAa,CAACjH,cAAc,CAACkN,QAAQ,EAAE;UAC/D5D,KAAK,EAAE,IAAI,CAAC6D;QACd,CAAC,EAAER,OAAO,EAAEE,MAAM,CAAC;MACrB;IACF,CAAC,CAAC,EAAE,CAAC;MACHxD,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE,SAAS8D,wBAAwBA,CAACC,IAAI,EAAEC,SAAS,EAAE;QACxD,IAAIjK,YAAY,GAAGgK,IAAI,CAAChK,YAAY;QACpC,IAAIkK,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAIlK,YAAY,KAAKmK,SAAS,IAAIF,SAAS,CAACjK,YAAY,KAAKA,YAAY,EAAE;UACzEkK,QAAQ,CAAClK,YAAY,GAAGA,YAAY;UACpCkK,QAAQ,CAACzE,gBAAgB,GAAGwE,SAAS,CAACjK,YAAY;QACpD;QAEA,OAAOkK,QAAQ;MACjB;IACF,CAAC,CAAC,CAAC;IAEH,OAAO5M,OAAO;EAChB,CAAC,CAACxB,KAAK,CAACsO,SAAS,CAAC;EAElBvO,eAAe,CAACyB,OAAO,EAAE,aAAa,EAAEX,cAAc,CAAC;EAEvDd,eAAe,CAACyB,OAAO,EAAE,cAAc,EAAE;IACvCoE,SAAS,EAAE,kBAAkB;IAC7BE,0BAA0B,EAAE/E,iBAAiB;IAC7C8H,WAAW,EAAE7H,cAAc;IAC3BiK,oBAAoB,EAAEnK,IAAI;IAC1B8I,uBAAuB,EAAE9I,IAAI;IAC7BsF,YAAY,EAAEtF,IAAI;IAClBqF,cAAc,EAAE,EAAE;IAClBnE,eAAe,EAAE,CAAC;IAClBI,eAAe,EAAE,GAAG;IACpBY,UAAU,EAAE,CAAC;IACbI,SAAS,EAAE,IAAI;IACfoD,UAAU,EAAE,CAAC,CAAC;IACdN,kBAAkB,EAAE,KAAK;IACzB8E,UAAU,EAAE,CAAC,CAAC;IACdlB,mBAAmB,EAAE,KAAK;IAC1BnF,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,IAAI;IAClB+G,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdI,UAAU,EAAE,EAAE;IACda,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,OAAOrL,OAAO;AAChB;AACA,eAAeF,eAAe,CAACd,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}