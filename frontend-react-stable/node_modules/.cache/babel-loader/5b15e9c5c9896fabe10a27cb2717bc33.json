{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.isNumberArray = isNumberArray;\nfunction _default(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n    c = b.slice(),\n    i;\n  return function (t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\nfunction isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "isNumberArray", "a", "b", "n", "Math", "min", "length", "c", "slice", "i", "t", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/numberArray.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.isNumberArray = isNumberArray;\n\nfunction _default(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function (t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n\n    return c;\n  };\n}\n\nfunction isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,aAAa,GAAGA,aAAa;AAErC,SAASD,QAAQA,CAACE,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAI,CAACA,CAAC,EAAEA,CAAC,GAAG,EAAE;EACd,IAAIC,CAAC,GAAGF,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,MAAM,EAAEL,CAAC,CAACK,MAAM,CAAC,GAAG,CAAC;IACxCC,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC;IACbC,CAAC;EACL,OAAO,UAAUC,CAAC,EAAE;IAClB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,CAAC,EAAE,EAAEM,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,GAAGR,CAAC,CAACO,CAAC,CAAC,GAAGC,CAAC;IAExD,OAAOH,CAAC;EACV,CAAC;AACH;AAEA,SAASP,aAAaA,CAACW,CAAC,EAAE;EACxB,OAAOC,WAAW,CAACC,MAAM,CAACF,CAAC,CAAC,IAAI,EAAEA,CAAC,YAAYG,QAAQ,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "script"}