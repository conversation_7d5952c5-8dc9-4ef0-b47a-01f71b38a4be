{"ast": null, "code": "// This icon file is generated automatically.\nvar DashOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M112 476h160v72H112zm320 0h160v72H432zm320 0h160v72H752z\"\n      }\n    }]\n  },\n  \"name\": \"dash\",\n  \"theme\": \"outlined\"\n};\nexport default DashOutlined;", "map": {"version": 3, "names": ["DashOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/DashOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar DashOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M112 476h160v72H112zm320 0h160v72H432zm320 0h160v72H752z\" } }] }, \"name\": \"dash\", \"theme\": \"outlined\" };\nexport default DashOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA2D;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,MAAM;EAAE,OAAO,EAAE;AAAW,CAAC;AAClQ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}