{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, InputNumber, Slider, Checkbox, Progress, Alert } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { modelTrainingAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst ModelTrainingPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('/home/<USER>');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(50);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState(null);\n  const [selectedResultKey, setSelectedResultKey] = useState('');\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      fetchCsvFiles();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = prots => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot]) {\n        newDatatypes[prot] = [datatypeOptions[prot][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n    const hasValidDatatypes = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n    setTraining(true);\n    setProgress(0);\n    setResult(null);\n    try {\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        message.info('本地文件训练功能开发中...');\n        return;\n      }\n      setProgress(100);\n      setResult(response.data);\n      message.success('模型训练完成！');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '模型训练失败');\n    } finally {\n      setTraining(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              value: csvDir,\n              onChange: e => setCsvDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n              style: {\n                marginTop: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedFile,\n                onChange: setSelectedFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            value: selectedProts,\n            onChange: handleProtocolChange,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n              value: prot,\n              children: prot\n            }, prot, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), selectedProts.map(prot => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: selectedDatatypes[prot] || [],\n            onChange: datatypes => handleDatatypeChange(prot, datatypes),\n            style: {\n              marginTop: 8\n            },\n            children: (datatypeOptions[prot] || []).map(datatype => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: datatype,\n              children: datatype\n            }, datatype, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, prot, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u57FA\\u7840\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5B66\\u4E60\\u7387\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: learningRate,\n                  onChange: value => setLearningRate(value || 0.001),\n                  min: 0.0001,\n                  max: 1,\n                  step: 0.0001,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: batchSize,\n                  onChange: value => setBatchSize(value || 32),\n                  min: 1,\n                  max: 512,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: epochs,\n                  onChange: value => setEpochs(value || 50),\n                  min: 1,\n                  max: 1000,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u53C2\\u6570 (GRU/LSTM)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: sequenceLength,\n                  onChange: value => setSequenceLength(value || 10),\n                  min: 1,\n                  max: 100,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: hiddenSize,\n                  onChange: value => setHiddenSize(value || 50),\n                  min: 10,\n                  max: 512,\n                  step: 10,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5C42\\u6570\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: numLayers,\n                  onChange: value => setNumLayers(value || 2),\n                  min: 1,\n                  max: 10,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"Dropout \\u6982\\u7387\\uFF1A\", dropout]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                  value: dropout,\n                  onChange: setDropout,\n                  min: 0,\n                  max: 0.9,\n                  step: 0.05,\n                  style: {\n                    marginTop: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: outputFolder,\n            onChange: e => setOutputFolder(e.target.value),\n            placeholder: \"\\u4F8B\\u5982: /data/output\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartTraining,\n        loading: training,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: training ? '正在训练...' : '开始训练预测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), training && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u8BAD\\u7EC3\\u7ED3\\u679C\\uFF1A\", result.message]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 17\n          }, this), result.model_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\\uFF1A\", result.model_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 39\n          }, this), result.training_time && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u8BAD\\u7EC3\\u65F6\\u95F4\\uFF1A\", result.training_time, \"\\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 15\n        }, this),\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginTop: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelTrainingPage, \"6fFFR4kmqECw7edlkC7CK8+CNMw=\");\n_c = ModelTrainingPage;\nexport default ModelTrainingPage;\nvar _c;\n$RefreshReg$(_c, \"ModelTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "InboxOutlined", "PlayCircleOutlined", "modelTrainingAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "ModelTrainingPage", "_s", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "data", "files", "error", "_error$response", "_error$response$data", "detail", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "length", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "Object", "keys", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "handleStartTraining", "hasValidDatatypes", "some", "setResult", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "toString", "progressInterval", "setInterval", "clearInterval", "trainModel", "success", "_error$response2", "_error$response2$data", "isFormValid", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "title", "className", "direction", "size", "style", "width", "strong", "Group", "value", "e", "target", "marginTop", "placeholder", "spinning", "loading", "map", "file", "mode", "datatype", "min", "max", "step", "marginLeft", "icon", "onClick", "disabled", "percent", "status", "result", "description", "model_path", "training_time", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { modelTrainingAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst ModelTrainingPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('/home/<USER>');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(50);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      fetchCsvFiles();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        message.info('本地文件训练功能开发中...');\n        return;\n      }\n\n      setProgress(100);\n      setResult(response.data);\n      message.success('模型训练完成！');\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '模型训练失败');\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={2}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input\n                  value={csvDir}\n                  onChange={(e) => setCsvDir(e.target.value)}\n                  placeholder=\"例如: /home/<USER>\"\n                  style={{ marginTop: 8 }}\n                />\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card title=\"训练参数配置\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>基础参数</Text>\n            <div style={{ marginTop: 16 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>学习率：</Text>\n                  <InputNumber\n                    value={learningRate}\n                    onChange={(value) => setLearningRate(value || 0.001)}\n                    min={0.0001}\n                    max={1}\n                    step={0.0001}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>批量大小：</Text>\n                  <InputNumber\n                    value={batchSize}\n                    onChange={(value) => setBatchSize(value || 32)}\n                    min={1}\n                    max={512}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>训练轮数：</Text>\n                  <InputNumber\n                    value={epochs}\n                    onChange={(value) => setEpochs(value || 50)}\n                    min={1}\n                    max={1000}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n              </Space>\n            </div>\n          </div>\n\n          <div>\n            <Text strong>模型参数 (GRU/LSTM)</Text>\n            <div style={{ marginTop: 16 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>序列长度：</Text>\n                  <InputNumber\n                    value={sequenceLength}\n                    onChange={(value) => setSequenceLength(value || 10)}\n                    min={1}\n                    max={100}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>隐藏层大小：</Text>\n                  <InputNumber\n                    value={hiddenSize}\n                    onChange={(value) => setHiddenSize(value || 50)}\n                    min={10}\n                    max={512}\n                    step={10}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>层数：</Text>\n                  <InputNumber\n                    value={numLayers}\n                    onChange={(value) => setNumLayers(value || 2)}\n                    min={1}\n                    max={10}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div style={{ width: '100%' }}>\n                  <Text>Dropout 概率：{dropout}</Text>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    style={{ marginTop: 8 }}\n                  />\n                </div>\n              </Space>\n            </div>\n          </div>\n\n          <div>\n            <Text strong>模型保存路径：</Text>\n            <Input\n              value={outputFolder}\n              onChange={(e) => setOutputFolder(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果 */}\n        {result && (\n          <Alert\n            message=\"训练完成\"\n            description={\n              <div>\n                <p>训练结果：{result.message}</p>\n                {result.model_path && <p>模型保存路径：{result.model_path}</p>}\n                {result.training_time && <p>训练时间：{result.training_time}秒</p>}\n              </div>\n            }\n            type=\"success\"\n            showIcon\n            style={{ marginTop: 16 }}\n          />\n        )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,QACA,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,gBAAgB,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAClC,MAAM;EAAEiB;AAAQ,CAAC,GAAGrB,MAAM;AAC1B,MAAM;EAAEsB;AAAO,CAAC,GAAGpB,MAAM;AAEzB,MAAMqB,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,cAAc,CAAC;EACpD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAA4B;IACpF6C,GAAG,EAAE,CAAC,aAAa;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkD,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,cAAc,CAAC;;EAEhE;EACA,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAACoE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrE,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAMsE,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtB1B,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjE2B,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACzC,MAAM,EAAE;IAEbO,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAMvD,gBAAgB,CAACwD,YAAY,CAAC3C,MAAM,CAAC;MAC5DG,iBAAiB,CAACuC,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBtE,OAAO,CAACoE,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzD9C,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACd,IAAI4B,UAAU,KAAK,OAAO,IAAII,MAAM,EAAE;MACpCyC,aAAa,CAAC,CAAC;IACjB;IACA;EACF,CAAC,EAAE,CAAC7C,UAAU,EAAEI,MAAM,CAAC,CAAC;;EAExB;EACA,MAAMkD,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5B3D,eAAe,CAACyD,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL1D,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAM4D,oBAAoB,GAAIC,KAAe,IAAK;IAChDnD,gBAAgB,CAACmD,KAAK,CAAC;IACvB;IACA,MAAMC,YAAY,GAAG;MAAE,GAAGnD;IAAkB,CAAC;IAC7CkD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpB,IAAI,CAACF,YAAY,CAACE,IAAI,CAAC,IAAIzB,eAAe,CAACyB,IAAI,CAAiC,EAAE;QAChFF,YAAY,CAACE,IAAI,CAAC,GAAG,CAACzB,eAAe,CAACyB,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC;MACjF;IACF,CAAC,CAAC;IACF;IACAC,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACH,IAAI,CAAC,EAAE;QACzB,OAAOF,YAAY,CAACE,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;IACFpD,oBAAoB,CAACkD,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtE1D,oBAAoB,CAAC2D,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IAAI3E,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;MAC5CpB,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIlD,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,YAAY,CAAC,EAAE;MACxD1B,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAItC,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BhF,OAAO,CAACoE,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,MAAM0B,iBAAiB,GAAGhE,aAAa,CAACiE,IAAI,CAACV,IAAI,IAC/CrD,iBAAiB,CAACqD,IAAI,CAAC,IAAIrD,iBAAiB,CAACqD,IAAI,CAAC,CAACL,MAAM,GAAG,CAC9D,CAAC;IAED,IAAI,CAACc,iBAAiB,EAAE;MACtB9F,OAAO,CAACoE,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF;IAEAhB,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,CAAC,CAAC;IACd0C,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,IAAIhC,QAAQ;MAEZ,IAAI9C,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAM+E,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/E,YAAY,CAACgF,aAAa,CAAC;QACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACxE,aAAa,CAAC,CAAC;QAChEmE,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACtE,iBAAiB,CAAC,CAAC;QACxEiE,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEhE,YAAY,CAACoE,QAAQ,CAAC,CAAC,CAAC;QACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE9D,SAAS,CAACkE,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE5D,MAAM,CAACgE,QAAQ,CAAC,CAAC,CAAC;QAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE1D,cAAc,CAAC8D,QAAQ,CAAC,CAAC,CAAC;QAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExD,UAAU,CAAC4D,QAAQ,CAAC,CAAC,CAAC;QACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEtD,SAAS,CAAC0D,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEpD,OAAO,CAACwD,QAAQ,CAAC,CAAC,CAAC;QAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElD,YAAY,CAAC;;QAE9C;QACA,MAAMuD,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCnD,WAAW,CAAEsC,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdc,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOZ,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAER5B,QAAQ,GAAG,MAAMvD,gBAAgB,CAACkG,UAAU,CAACV,QAAQ,CAAC;QACtDS,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACAxG,OAAO,CAAC8E,IAAI,CAAC,gBAAgB,CAAC;QAC9B;MACF;MAEAxB,WAAW,CAAC,GAAG,CAAC;MAChB0C,SAAS,CAAChC,QAAQ,CAACE,IAAI,CAAC;MACxBlE,OAAO,CAAC4G,OAAO,CAAC,SAAS,CAAC;IAE5B,CAAC,CAAC,OAAOxC,KAAU,EAAE;MAAA,IAAAyC,gBAAA,EAAAC,qBAAA;MACnB9G,OAAO,CAACoE,KAAK,CAAC,EAAAyC,gBAAA,GAAAzC,KAAK,CAACJ,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBvC,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACRnB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM2D,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI7F,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOE,YAAY,IAAIU,aAAa,CAACkD,MAAM,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAO1D,MAAM,IAAII,YAAY,IAAII,aAAa,CAACkD,MAAM,GAAG,CAAC;IAC3D;EACF,CAAC;EAED,oBACErE,OAAA;IAAAqG,QAAA,gBACErG,OAAA,CAACC,KAAK;MAACqG,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClC1G,OAAA,CAACE,IAAI;MAACyG,IAAI,EAAC,WAAW;MAAAN,QAAA,EAAC;IAEvB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP1G,OAAA,CAACZ,OAAO;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGX1G,OAAA,CAACpB,IAAI;MAACgI,KAAK,EAAC,oBAAK;MAACC,SAAS,EAAC,eAAe;MAAAR,QAAA,eACzCrG,OAAA,CAACb,KAAK;QAAC2H,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAChErG,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1B1G,OAAA,CAACnB,KAAK,CAACsI,KAAK;YACVC,KAAK,EAAE7G,UAAW;YAClB2D,QAAQ,EAAGmD,CAAC,IAAK7G,aAAa,CAAC6G,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;YAC/CJ,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAExBrG,OAAA,CAACnB,KAAK;cAACuI,KAAK,EAAC,QAAQ;cAAAf,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrC1G,OAAA,CAACnB,KAAK;cAACuI,KAAK,EAAC,OAAO;cAAAf,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLnG,UAAU,KAAK,QAAQ,iBACtBP,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB1G,OAAA,CAACG,OAAO;YAAA,GAAK0D,WAAW;YAAEmD,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAChDrG,OAAA;cAAG6G,SAAS,EAAC,sBAAsB;cAAAR,QAAA,eACjCrG,OAAA,CAACJ,aAAa;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJ1G,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAR,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnD1G,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAR,QAAA,EAAC;YAE/B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGAnG,UAAU,KAAK,OAAO,iBACrBP,OAAA,CAACb,KAAK;UAAC2H,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACnDrG,OAAA;YAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;cAACgH,MAAM;cAAAb,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5B1G,OAAA,CAACjB,KAAK;cACJqI,KAAK,EAAEzG,MAAO;cACduD,QAAQ,EAAGmD,CAAC,IAAKzG,SAAS,CAACyG,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;cAC3CI,WAAW,EAAC,4BAAkB;cAC9BR,KAAK,EAAE;gBAAEO,SAAS,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1G,OAAA;YAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;cAACgH,MAAM;cAAAb,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB1G,OAAA,CAACV,IAAI;cAACmI,QAAQ,EAAExG,YAAa;cAAAoF,QAAA,eAC3BrG,OAAA,CAAChB,MAAM;gBACLoI,KAAK,EAAErG,YAAa;gBACpBmD,QAAQ,EAAElD,eAAgB;gBAC1BwG,WAAW,EAAC,mCAAU;gBACtBR,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEM,SAAS,EAAE;gBAAE,CAAE;gBACvCG,OAAO,EAAEzG,YAAa;gBAAAoF,QAAA,EAErBxF,cAAc,CAAC8G,GAAG,CAAEC,IAAI,iBACvB5H,OAAA,CAACI,MAAM;kBAAYgH,KAAK,EAAEQ,IAAK;kBAAAvB,QAAA,EAC5BuB;gBAAI,GADMA,IAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGP1G,OAAA,CAACpB,IAAI;MAACgI,KAAK,EAAC,wDAAW;MAACC,SAAS,EAAC,eAAe;MAAAR,QAAA,eAC/CrG,OAAA,CAACb,KAAK;QAAC2H,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAChErG,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB1G,OAAA,CAAChB,MAAM;YACL6I,IAAI,EAAC,UAAU;YACfT,KAAK,EAAEjG,aAAc;YACrB+C,QAAQ,EAAEI,oBAAqB;YAC/BkD,WAAW,EAAC,gCAAO;YACnBR,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEM,SAAS,EAAE;YAAE,CAAE;YAAAlB,QAAA,EAEtCrD,eAAe,CAAC2E,GAAG,CAAEjD,IAAI,iBACxB1E,OAAA,CAACI,MAAM;cAAYgH,KAAK,EAAE1C,IAAK;cAAA2B,QAAA,EAC5B3B;YAAI,GADMA,IAAI;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELvF,aAAa,CAACwG,GAAG,CAAEjD,IAAI,iBACtB1E,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,GAAE3B,IAAI,EAAC,iCAAM;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChC1G,OAAA,CAACP,QAAQ,CAAC0H,KAAK;YACbC,KAAK,EAAE/F,iBAAiB,CAACqD,IAAI,CAAC,IAAI,EAAG;YACrCR,QAAQ,EAAGc,SAAS,IAAKF,oBAAoB,CAACJ,IAAI,EAAEM,SAAqB,CAAE;YAC3EgC,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAE,CAAE;YAAAlB,QAAA,EAEvB,CAACpD,eAAe,CAACyB,IAAI,CAAiC,IAAI,EAAE,EAAEiD,GAAG,CAAEG,QAAQ,iBAC1E9H,OAAA,CAACP,QAAQ;cAAgB2H,KAAK,EAAEU,QAAS;cAAAzB,QAAA,EACtCyB;YAAQ,GADIA,QAAQ;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA,GAZThC,IAAI;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGP1G,OAAA,CAACpB,IAAI;MAACgI,KAAK,EAAC,sCAAQ;MAACC,SAAS,EAAC,eAAe;MAAAR,QAAA,eAC5CrG,OAAA,CAACb,KAAK;QAAC2H,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAChErG,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxB1G,OAAA;YAAKgH,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAG,CAAE;YAAAlB,QAAA,eAC5BrG,OAAA,CAACb,KAAK;cAAC2H,SAAS,EAAC,UAAU;cAACE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAZ,QAAA,gBACnDrG,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAE5F,YAAa;kBACpB0C,QAAQ,EAAGkD,KAAK,IAAK3F,eAAe,CAAC2F,KAAK,IAAI,KAAK,CAAE;kBACrDW,GAAG,EAAE,MAAO;kBACZC,GAAG,EAAE,CAAE;kBACPC,IAAI,EAAE,MAAO;kBACbjB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1G,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAE1F,SAAU;kBACjBwC,QAAQ,EAAGkD,KAAK,IAAKzF,YAAY,CAACyF,KAAK,IAAI,EAAE,CAAE;kBAC/CW,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACThB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1G,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAExF,MAAO;kBACdsC,QAAQ,EAAGkD,KAAK,IAAKvF,SAAS,CAACuF,KAAK,IAAI,EAAE,CAAE;kBAC5CW,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,IAAK;kBACVhB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1G,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnC1G,OAAA;YAAKgH,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAG,CAAE;YAAAlB,QAAA,eAC5BrG,OAAA,CAACb,KAAK;cAAC2H,SAAS,EAAC,UAAU;cAACE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAZ,QAAA,gBACnDrG,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAEtF,cAAe;kBACtBoC,QAAQ,EAAGkD,KAAK,IAAKrF,iBAAiB,CAACqF,KAAK,IAAI,EAAE,CAAE;kBACpDW,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACThB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1G,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAEpF,UAAW;kBAClBkC,QAAQ,EAAGkD,KAAK,IAAKnF,aAAa,CAACmF,KAAK,IAAI,EAAE,CAAE;kBAChDW,GAAG,EAAE,EAAG;kBACRC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,EAAG;kBACTjB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1G,OAAA;gBAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChB1G,OAAA,CAACT,WAAW;kBACV6H,KAAK,EAAElF,SAAU;kBACjBgC,QAAQ,EAAGkD,KAAK,IAAKjF,YAAY,CAACiF,KAAK,IAAI,CAAC,CAAE;kBAC9CW,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,EAAG;kBACRhB,KAAK,EAAE;oBAAEkB,UAAU,EAAE;kBAAE;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1G,OAAA;gBAAKgH,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,gBAC5BrG,OAAA,CAACE,IAAI;kBAAAmG,QAAA,GAAC,4BAAW,EAACjE,OAAO;gBAAA;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjC1G,OAAA,CAACR,MAAM;kBACL4H,KAAK,EAAEhF,OAAQ;kBACf8B,QAAQ,EAAE7B,UAAW;kBACrB0F,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,IAAK;kBACXjB,KAAK,EAAE;oBAAEO,SAAS,EAAE;kBAAE;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1G,OAAA;UAAAqG,QAAA,gBACErG,OAAA,CAACE,IAAI;YAACgH,MAAM;YAAAb,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3B1G,OAAA,CAACjB,KAAK;YACJqI,KAAK,EAAE9E,YAAa;YACpB4B,QAAQ,EAAGmD,CAAC,IAAK9E,eAAe,CAAC8E,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;YACjDI,WAAW,EAAC,4BAAkB;YAC9BR,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGP1G,OAAA,CAACpB,IAAI;MAACiI,SAAS,EAAC,eAAe;MAAAR,QAAA,gBAC7BrG,OAAA,CAACf,MAAM;QACL0H,IAAI,EAAC,SAAS;QACdI,IAAI,EAAC,OAAO;QACZoB,IAAI,eAAEnI,OAAA,CAACH,kBAAkB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B0B,OAAO,EAAElD,mBAAoB;QAC7BwC,OAAO,EAAElF,QAAS;QAClB6F,QAAQ,EAAE,CAACjC,WAAW,CAAC,CAAE;QACzBS,SAAS,EAAC,eAAe;QAAAR,QAAA,EAExB7D,QAAQ,GAAG,SAAS,GAAG;MAAQ;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAGRlE,QAAQ,iBACPxC,OAAA;QAAK6G,SAAS,EAAC,kBAAkB;QAAAR,QAAA,gBAC/BrG,OAAA,CAACE,IAAI;UAAAmG,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClB1G,OAAA,CAACN,QAAQ;UAAC4I,OAAO,EAAE5F,QAAS;UAAC6F,MAAM,EAAC;QAAQ;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAGA8B,MAAM,iBACLxI,OAAA,CAACL,KAAK;QACJN,OAAO,EAAC,0BAAM;QACdoJ,WAAW,eACTzI,OAAA;UAAAqG,QAAA,gBACErG,OAAA;YAAAqG,QAAA,GAAG,gCAAK,EAACmC,MAAM,CAACnJ,OAAO;UAAA;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC3B8B,MAAM,CAACE,UAAU,iBAAI1I,OAAA;YAAAqG,QAAA,GAAG,4CAAO,EAACmC,MAAM,CAACE,UAAU;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtD8B,MAAM,CAACG,aAAa,iBAAI3I,OAAA;YAAAqG,QAAA,GAAG,gCAAK,EAACmC,MAAM,CAACG,aAAa,EAAC,QAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CACN;QACDC,IAAI,EAAC,SAAS;QACdiC,QAAQ;QACR5B,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAG;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpG,EAAA,CAjcID,iBAA2B;AAAAwI,EAAA,GAA3BxI,iBAA2B;AAmcjC,eAAeA,iBAAiB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}