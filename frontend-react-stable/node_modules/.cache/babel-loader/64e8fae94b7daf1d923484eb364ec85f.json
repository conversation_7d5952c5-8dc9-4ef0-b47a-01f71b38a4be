{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\nimport React from \"react\";\n// zombieJ: To compatible with `renderTabBar` usage.\nimport TabNavList from '.';\nimport { useContext } from 'react';\nimport TabContext from '../TabContext';\nimport TabPane from '../TabPanelList/TabPane'; // We have to create a TabNavList components.\n\nexport default function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _useContext = useContext(TabContext),\n    tabs = _useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "TabNavList", "useContext", "TabContext", "TabPane", "TabNavListWrapper", "_ref", "renderTabBar", "restProps", "_useContext", "tabs", "tabNavBarProps", "panes", "map", "_ref2", "label", "key", "restTabProps", "createElement", "tab", "tabKey"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabNavList/Wrapper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n    _excluded2 = [\"label\", \"key\"];\nimport React from \"react\";\n// zombieJ: To compatible with `renderTabBar` usage.\nimport TabNavList from '.';\nimport { useContext } from 'react';\nimport TabContext from '../TabContext';\nimport TabPane from '../TabPanelList/TabPane'; // We have to create a TabNavList components.\n\nexport default function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var _useContext = useContext(TabContext),\n      tabs = _useContext.tabs;\n\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n            key = _ref2.key,\n            restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,cAAc,CAAC;EAC5BC,UAAU,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB;AACA,OAAOC,UAAU,MAAM,GAAG;AAC1B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,yBAAyB,CAAC,CAAC;;AAE/C,eAAe,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCC,SAAS,GAAGX,wBAAwB,CAACS,IAAI,EAAER,SAAS,CAAC;EAEzD,IAAIW,WAAW,GAAGP,UAAU,CAACC,UAAU,CAAC;IACpCO,IAAI,GAAGD,WAAW,CAACC,IAAI;EAE3B,IAAIH,YAAY,EAAE;IAChB,IAAII,cAAc,GAAGf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEY,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACnE;MACAI,KAAK,EAAEF,IAAI,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC/B,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;UACnBC,GAAG,GAAGF,KAAK,CAACE,GAAG;UACfC,YAAY,GAAGpB,wBAAwB,CAACiB,KAAK,EAAEf,UAAU,CAAC;QAE9D,OAAO,aAAaC,KAAK,CAACkB,aAAa,CAACd,OAAO,EAAET,QAAQ,CAAC;UACxDwB,GAAG,EAAEJ,KAAK;UACVC,GAAG,EAAEA,GAAG;UACRI,MAAM,EAAEJ;QACV,CAAC,EAAEC,YAAY,CAAC,CAAC;MACnB,CAAC;IACH,CAAC,CAAC;IAEF,OAAOV,YAAY,CAACI,cAAc,EAAEV,UAAU,CAAC;EACjD;EAEA,OAAO,aAAaD,KAAK,CAACkB,aAAa,CAACjB,UAAU,EAAEO,SAAS,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}