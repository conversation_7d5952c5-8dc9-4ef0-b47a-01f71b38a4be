{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Comment = function Comment(_a) {\n  var actions = _a.actions,\n    author = _a.author,\n    avatar = _a.avatar,\n    children = _a.children,\n    className = _a.className,\n    content = _a.content,\n    customizePrefixCls = _a.prefixCls,\n    datetime = _a.datetime,\n    otherProps = __rest(_a, [\"actions\", \"author\", \"avatar\", \"children\", \"className\", \"content\", \"prefixCls\", \"datetime\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var renderNested = function renderNested(prefixCls, nestedChildren) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-nested\"))\n    }, nestedChildren);\n  };\n  var prefixCls = getPrefixCls('comment', customizePrefixCls);\n  var avatarDom = avatar ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-avatar\")\n  }, typeof avatar === 'string' ? /*#__PURE__*/React.createElement(\"img\", {\n    src: avatar,\n    alt: \"comment-avatar\"\n  }) : avatar) : null;\n  var actionDom = actions && actions.length ? /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-actions\")\n  }, actions.map(function (action, index) {\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: \"action-\".concat(index)\n    }, action) // eslint-disable-line react/no-array-index-key\n    ;\n  })) : null;\n  var authorContent = (author || datetime) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-author\")\n  }, author && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-author-name\")\n  }, author), datetime && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-author-time\")\n  }, datetime));\n  var contentDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, authorContent, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-detail\")\n  }, content), actionDom);\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, otherProps, {\n    className: cls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, avatarDom, contentDom), children ? renderNested(prefixCls, children) : null);\n};\nexport default Comment;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "ConfigContext", "Comment", "_a", "actions", "author", "avatar", "children", "className", "content", "customizePrefixCls", "prefixCls", "datetime", "otherProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "renderNested", "nested<PERSON><PERSON><PERSON><PERSON>", "createElement", "concat", "avatarDom", "src", "alt", "actionDom", "map", "action", "index", "key", "author<PERSON><PERSON><PERSON>", "contentDom", "cls"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/comment/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Comment = function Comment(_a) {\n  var actions = _a.actions,\n    author = _a.author,\n    avatar = _a.avatar,\n    children = _a.children,\n    className = _a.className,\n    content = _a.content,\n    customizePrefixCls = _a.prefixCls,\n    datetime = _a.datetime,\n    otherProps = __rest(_a, [\"actions\", \"author\", \"avatar\", \"children\", \"className\", \"content\", \"prefixCls\", \"datetime\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var renderNested = function renderNested(prefixCls, nestedChildren) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-nested\"))\n    }, nestedChildren);\n  };\n  var prefixCls = getPrefixCls('comment', customizePrefixCls);\n  var avatarDom = avatar ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-avatar\")\n  }, typeof avatar === 'string' ? /*#__PURE__*/React.createElement(\"img\", {\n    src: avatar,\n    alt: \"comment-avatar\"\n  }) : avatar) : null;\n  var actionDom = actions && actions.length ? /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-actions\")\n  }, actions.map(function (action, index) {\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: \"action-\".concat(index)\n    }, action) // eslint-disable-line react/no-array-index-key\n    ;\n  })) : null;\n  var authorContent = (author || datetime) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-author\")\n  }, author && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-author-name\")\n  }, author), datetime && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-author-time\")\n  }, datetime));\n  var contentDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, authorContent, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-detail\")\n  }, content), actionDom);\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, otherProps, {\n    className: cls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, avatarDom, contentDom), children ? renderNested(prefixCls, children) : null);\n};\nexport default Comment;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,EAAE,EAAE;EACjC,IAAIC,OAAO,GAAGD,EAAE,CAACC,OAAO;IACtBC,MAAM,GAAGF,EAAE,CAACE,MAAM;IAClBC,MAAM,GAAGH,EAAE,CAACG,MAAM;IAClBC,QAAQ,GAAGJ,EAAE,CAACI,QAAQ;IACtBC,SAAS,GAAGL,EAAE,CAACK,SAAS;IACxBC,OAAO,GAAGN,EAAE,CAACM,OAAO;IACpBC,kBAAkB,GAAGP,EAAE,CAACQ,SAAS;IACjCC,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IACtBC,UAAU,GAAG5B,MAAM,CAACkB,EAAE,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EACvH,IAAIW,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACd,aAAa,CAAC;IACrDe,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACP,SAAS,EAAEQ,cAAc,EAAE;IAClE,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;MAC7CZ,SAAS,EAAET,UAAU,CAAC,EAAE,CAACsB,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IACvD,CAAC,EAAEQ,cAAc,CAAC;EACpB,CAAC;EACD,IAAIR,SAAS,GAAGK,YAAY,CAAC,SAAS,EAAEN,kBAAkB,CAAC;EAC3D,IAAIY,SAAS,GAAGhB,MAAM,GAAG,aAAaN,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC/DZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,OAAOL,MAAM,KAAK,QAAQ,GAAG,aAAaN,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACtEG,GAAG,EAAEjB,MAAM;IACXkB,GAAG,EAAE;EACP,CAAC,CAAC,GAAGlB,MAAM,CAAC,GAAG,IAAI;EACnB,IAAImB,SAAS,GAAGrB,OAAO,IAAIA,OAAO,CAACP,MAAM,GAAG,aAAaG,KAAK,CAACoB,aAAa,CAAC,IAAI,EAAE;IACjFZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEP,OAAO,CAACsB,GAAG,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACtC,OAAO,aAAa5B,KAAK,CAACoB,aAAa,CAAC,IAAI,EAAE;MAC5CS,GAAG,EAAE,SAAS,CAACR,MAAM,CAACO,KAAK;IAC7B,CAAC,EAAED,MAAM,CAAC,CAAC;IAAA;EAEb,CAAC,CAAC,CAAC,GAAG,IAAI;EACV,IAAIG,aAAa,GAAG,CAACzB,MAAM,IAAIO,QAAQ,KAAK,aAAaZ,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAClFZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,iBAAiB;EACnD,CAAC,EAAEN,MAAM,IAAI,aAAaL,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;IACpDZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,sBAAsB;EACxD,CAAC,EAAEN,MAAM,CAAC,EAAEO,QAAQ,IAAI,aAAaZ,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;IAC/DZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,sBAAsB;EACxD,CAAC,EAAEC,QAAQ,CAAC,CAAC;EACb,IAAImB,UAAU,GAAG,aAAa/B,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACvDZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEmB,aAAa,EAAE,aAAa9B,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACxDZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,iBAAiB;EACnD,CAAC,EAAEF,OAAO,CAAC,EAAEgB,SAAS,CAAC;EACvB,IAAIO,GAAG,GAAGjC,UAAU,CAACY,SAAS,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqC,MAAM,CAACV,SAAS,EAAE,MAAM,CAAC,EAAEM,SAAS,KAAK,KAAK,CAAC,EAAET,SAAS,CAAC;EAClH,OAAO,aAAaR,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAE8B,UAAU,EAAE;IACtEL,SAAS,EAAEwB;EACb,CAAC,CAAC,EAAE,aAAahC,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC1CZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACV,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEW,SAAS,EAAES,UAAU,CAAC,EAAExB,QAAQ,GAAGW,YAAY,CAACP,SAAS,EAAEJ,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjF,CAAC;AACD,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}