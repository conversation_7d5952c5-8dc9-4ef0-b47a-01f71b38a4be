{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport { scrollTo, waitElementReady } from '../../utils/uiUtil';\nimport PanelContext from '../../PanelContext';\nfunction TimeUnitColumn(props) {\n  var prefixCls = props.prefixCls,\n    units = props.units,\n    onSelect = props.onSelect,\n    value = props.value,\n    active = props.active,\n    hideDisabledOptions = props.hideDisabledOptions;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var _React$useContext = React.useContext(PanelContext),\n    open = _React$useContext.open;\n  var ulRef = useRef(null);\n  var liRefs = useRef(new Map());\n  var scrollRef = useRef();\n  // `useLayoutEffect` here to avoid blink by duration is 0\n  useLayoutEffect(function () {\n    var li = liRefs.current.get(value);\n    if (li && open !== false) {\n      scrollTo(ulRef.current, li.offsetTop, 120);\n    }\n  }, [value]);\n  useLayoutEffect(function () {\n    if (open) {\n      var li = liRefs.current.get(value);\n      if (li) {\n        scrollRef.current = waitElementReady(li, function () {\n          scrollTo(ulRef.current, li.offsetTop, 0);\n        });\n      }\n    }\n    return function () {\n      var _scrollRef$current;\n      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);\n    };\n  }, [open]);\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-column\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-active\"), active)),\n    ref: ulRef,\n    style: {\n      position: 'relative'\n    }\n  }, units.map(function (unit) {\n    var _classNames2;\n    if (hideDisabledOptions && unit.disabled) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unit.value,\n      ref: function ref(element) {\n        liRefs.current.set(unit.value, element);\n      },\n      className: classNames(cellPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-disabled\"), unit.disabled), _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-selected\"), value === unit.value), _classNames2)),\n      onClick: function onClick() {\n        if (unit.disabled) {\n          return;\n        }\n        onSelect(unit.value);\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, unit.label));\n  }));\n}\nexport default TimeUnitColumn;", "map": {"version": 3, "names": ["_defineProperty", "React", "useRef", "useLayoutEffect", "classNames", "scrollTo", "waitE<PERSON><PERSON><PERSON><PERSON>", "PanelContext", "TimeUnitColumn", "props", "prefixCls", "units", "onSelect", "value", "active", "hideDisabledOptions", "cellPrefixCls", "concat", "_React$useContext", "useContext", "open", "ulRef", "liRefs", "Map", "scrollRef", "li", "current", "get", "offsetTop", "_scrollRef$current", "call", "createElement", "className", "ref", "style", "position", "map", "unit", "_classNames2", "disabled", "key", "element", "set", "onClick", "label"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/TimePanel/TimeUnitColumn.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport { scrollTo, waitElementReady } from '../../utils/uiUtil';\nimport PanelContext from '../../PanelContext';\nfunction TimeUnitColumn(props) {\n  var prefixCls = props.prefixCls,\n    units = props.units,\n    onSelect = props.onSelect,\n    value = props.value,\n    active = props.active,\n    hideDisabledOptions = props.hideDisabledOptions;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var _React$useContext = React.useContext(PanelContext),\n    open = _React$useContext.open;\n  var ulRef = useRef(null);\n  var liRefs = useRef(new Map());\n  var scrollRef = useRef();\n  // `useLayoutEffect` here to avoid blink by duration is 0\n  useLayoutEffect(function () {\n    var li = liRefs.current.get(value);\n    if (li && open !== false) {\n      scrollTo(ulRef.current, li.offsetTop, 120);\n    }\n  }, [value]);\n  useLayoutEffect(function () {\n    if (open) {\n      var li = liRefs.current.get(value);\n      if (li) {\n        scrollRef.current = waitElementReady(li, function () {\n          scrollTo(ulRef.current, li.offsetTop, 0);\n        });\n      }\n    }\n    return function () {\n      var _scrollRef$current;\n      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);\n    };\n  }, [open]);\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-column\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-active\"), active)),\n    ref: ulRef,\n    style: {\n      position: 'relative'\n    }\n  }, units.map(function (unit) {\n    var _classNames2;\n    if (hideDisabledOptions && unit.disabled) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unit.value,\n      ref: function ref(element) {\n        liRefs.current.set(unit.value, element);\n      },\n      className: classNames(cellPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-disabled\"), unit.disabled), _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-selected\"), value === unit.value), _classNames2)),\n      onClick: function onClick() {\n        if (unit.disabled) {\n          return;\n        }\n        onSelect(unit.value);\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, unit.label));\n  }));\n}\nexport default TimeUnitColumn;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,OAAO;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC/D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,mBAAmB,GAAGN,KAAK,CAACM,mBAAmB;EACjD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIQ,iBAAiB,GAAGjB,KAAK,CAACkB,UAAU,CAACZ,YAAY,CAAC;IACpDa,IAAI,GAAGF,iBAAiB,CAACE,IAAI;EAC/B,IAAIC,KAAK,GAAGnB,MAAM,CAAC,IAAI,CAAC;EACxB,IAAIoB,MAAM,GAAGpB,MAAM,CAAC,IAAIqB,GAAG,CAAC,CAAC,CAAC;EAC9B,IAAIC,SAAS,GAAGtB,MAAM,CAAC,CAAC;EACxB;EACAC,eAAe,CAAC,YAAY;IAC1B,IAAIsB,EAAE,GAAGH,MAAM,CAACI,OAAO,CAACC,GAAG,CAACd,KAAK,CAAC;IAClC,IAAIY,EAAE,IAAIL,IAAI,KAAK,KAAK,EAAE;MACxBf,QAAQ,CAACgB,KAAK,CAACK,OAAO,EAAED,EAAE,CAACG,SAAS,EAAE,GAAG,CAAC;IAC5C;EACF,CAAC,EAAE,CAACf,KAAK,CAAC,CAAC;EACXV,eAAe,CAAC,YAAY;IAC1B,IAAIiB,IAAI,EAAE;MACR,IAAIK,EAAE,GAAGH,MAAM,CAACI,OAAO,CAACC,GAAG,CAACd,KAAK,CAAC;MAClC,IAAIY,EAAE,EAAE;QACND,SAAS,CAACE,OAAO,GAAGpB,gBAAgB,CAACmB,EAAE,EAAE,YAAY;UACnDpB,QAAQ,CAACgB,KAAK,CAACK,OAAO,EAAED,EAAE,CAACG,SAAS,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC;MACJ;IACF;IACA,OAAO,YAAY;MACjB,IAAIC,kBAAkB;MACtB,CAACA,kBAAkB,GAAGL,SAAS,CAACE,OAAO,MAAM,IAAI,IAAIG,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,IAAI,CAACN,SAAS,CAAC;IAClI,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EACV,OAAO,aAAanB,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;IAC5CC,SAAS,EAAE5B,UAAU,CAAC,EAAE,CAACa,MAAM,CAACP,SAAS,EAAE,SAAS,CAAC,EAAEV,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACiB,MAAM,CAACP,SAAS,EAAE,gBAAgB,CAAC,EAAEI,MAAM,CAAC,CAAC;IAC3HmB,GAAG,EAAEZ,KAAK;IACVa,KAAK,EAAE;MACLC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAExB,KAAK,CAACyB,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC3B,IAAIC,YAAY;IAChB,IAAIvB,mBAAmB,IAAIsB,IAAI,CAACE,QAAQ,EAAE;MACxC,OAAO,IAAI;IACb;IACA,OAAO,aAAatC,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;MAC5CS,GAAG,EAAEH,IAAI,CAACxB,KAAK;MACfoB,GAAG,EAAE,SAASA,GAAGA,CAACQ,OAAO,EAAE;QACzBnB,MAAM,CAACI,OAAO,CAACgB,GAAG,CAACL,IAAI,CAACxB,KAAK,EAAE4B,OAAO,CAAC;MACzC,CAAC;MACDT,SAAS,EAAE5B,UAAU,CAACY,aAAa,GAAGsB,YAAY,GAAG,CAAC,CAAC,EAAEtC,eAAe,CAACsC,YAAY,EAAE,EAAE,CAACrB,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEqB,IAAI,CAACE,QAAQ,CAAC,EAAEvC,eAAe,CAACsC,YAAY,EAAE,EAAE,CAACrB,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEH,KAAK,KAAKwB,IAAI,CAACxB,KAAK,CAAC,EAAEyB,YAAY,CAAC,CAAC;MACxPK,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAIN,IAAI,CAACE,QAAQ,EAAE;UACjB;QACF;QACA3B,QAAQ,CAACyB,IAAI,CAACxB,KAAK,CAAC;MACtB;IACF,CAAC,EAAE,aAAaZ,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;MACzCC,SAAS,EAAE,EAAE,CAACf,MAAM,CAACD,aAAa,EAAE,QAAQ;IAC9C,CAAC,EAAEqB,IAAI,CAACO,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC;AACL;AACA,eAAepC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}