{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FundProjectionScreenOutlinedSvg from \"@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FundProjectionScreenOutlinedSvg\n  }));\n};\nFundProjectionScreenOutlined.displayName = 'FundProjectionScreenOutlined';\nexport default /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "FundProjectionScreenOutlinedSvg", "AntdIcon", "FundProjectionScreenOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/FundProjectionScreenOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FundProjectionScreenOutlinedSvg from \"@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FundProjectionScreenOutlinedSvg\n  }));\n};\nFundProjectionScreenOutlined.displayName = 'FundProjectionScreenOutlined';\nexport default /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,+BAA+B,MAAM,2DAA2D;AACvG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,4BAA4B,GAAG,SAASA,4BAA4BA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnF,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,4BAA4B,CAACK,WAAW,GAAG,8BAA8B;AACzE,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,4BAA4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}