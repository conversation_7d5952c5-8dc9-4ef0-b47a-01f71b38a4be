{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport CalendarLocale from \"rc-picker/es/locale/en_US\";\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nvar locale = {\n  lang: _extends({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: _extends({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;", "map": {"version": 3, "names": ["_extends", "CalendarLocale", "TimePickerLocale", "locale", "lang", "placeholder", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "rangePlaceholder", "rangeYearPlaceholder", "rangeQuarterPlaceholder", "rangeMonthPlaceholder", "rangeWeekPlaceholder", "timePickerLocale"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/date-picker/locale/en_US.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport CalendarLocale from \"rc-picker/es/locale/en_US\";\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nvar locale = {\n  lang: _extends({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: _extends({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAEJ,QAAQ,CAAC;IACbK,WAAW,EAAE,aAAa;IAC1BC,eAAe,EAAE,aAAa;IAC9BC,kBAAkB,EAAE,gBAAgB;IACpCC,gBAAgB,EAAE,cAAc;IAChCC,eAAe,EAAE,aAAa;IAC9BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAC5CC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAChDC,uBAAuB,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;IACzDC,qBAAqB,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;IACnDC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU;EACjD,CAAC,EAAEb,cAAc,CAAC;EAClBc,gBAAgB,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEE,gBAAgB;AACjD,CAAC;AACD;AACA;AACA,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}