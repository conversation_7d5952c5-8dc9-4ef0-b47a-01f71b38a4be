{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  sidebarCollapsed: false,\n  theme: 'light',\n  loading: false,\n  notifications: []\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: state => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    setSidebarCollapsed: (state, action) => {\n      state.sidebarCollapsed = action.payload;\n    },\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    addNotification: (state, action) => {\n      const notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: Date.now()\n      };\n      state.notifications.unshift(notification);\n      // 保持最多10个通知\n      if (state.notifications.length > 10) {\n        state.notifications = state.notifications.slice(0, 10);\n      }\n    },\n    removeNotification: (state, action) => {\n      state.notifications = state.notifications.filter(notification => notification.id !== action.payload);\n    },\n    clearNotifications: state => {\n      state.notifications = [];\n    }\n  }\n});\nexport const {\n  toggleSidebar,\n  setSidebarCollapsed,\n  setTheme,\n  setLoading,\n  addNotification,\n  removeNotification,\n  clearNotifications\n} = uiSlice.actions;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarCollapsed", "theme", "loading", "notifications", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarCollapsed", "action", "payload", "setTheme", "setLoading", "addNotification", "notification", "id", "Date", "now", "toString", "timestamp", "unshift", "length", "slice", "removeNotification", "filter", "clearNotifications", "actions", "reducer"], "sources": ["/home/<USER>/frontend-react-stable/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface UIState {\n  sidebarCollapsed: boolean;\n  theme: 'light' | 'dark';\n  loading: boolean;\n  notifications: Notification[];\n}\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: number;\n}\n\nconst initialState: UIState = {\n  sidebarCollapsed: false,\n  theme: 'light',\n  loading: false,\n  notifications: [],\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {\n      state.sidebarCollapsed = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: Date.now(),\n      };\n      state.notifications.unshift(notification);\n      // 保持最多10个通知\n      if (state.notifications.length > 10) {\n        state.notifications = state.notifications.slice(0, 10);\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(\n        (notification) => notification.id !== action.payload\n      );\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarCollapsed,\n  setTheme,\n  setLoading,\n  addNotification,\n  removeNotification,\n  clearNotifications,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAiB7D,MAAMC,YAAqB,GAAG;EAC5BC,gBAAgB,EAAE,KAAK;EACvBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,KAAK;EACdC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,OAAO,GAAGN,WAAW,CAAC;EAC1BO,IAAI,EAAE,IAAI;EACVN,YAAY;EACZO,QAAQ,EAAE;IACRC,aAAa,EAAGC,KAAK,IAAK;MACxBA,KAAK,CAACR,gBAAgB,GAAG,CAACQ,KAAK,CAACR,gBAAgB;IAClD,CAAC;IACDS,mBAAmB,EAAEA,CAACD,KAAK,EAAEE,MAA8B,KAAK;MAC9DF,KAAK,CAACR,gBAAgB,GAAGU,MAAM,CAACC,OAAO;IACzC,CAAC;IACDC,QAAQ,EAAEA,CAACJ,KAAK,EAAEE,MAAuC,KAAK;MAC5DF,KAAK,CAACP,KAAK,GAAGS,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDE,UAAU,EAAEA,CAACL,KAAK,EAAEE,MAA8B,KAAK;MACrDF,KAAK,CAACN,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAChC,CAAC;IACDG,eAAe,EAAEA,CAACN,KAAK,EAAEE,MAA6D,KAAK;MACzF,MAAMK,YAA0B,GAAG;QACjC,GAAGL,MAAM,CAACC,OAAO;QACjBK,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,SAAS,EAAEH,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;MACDV,KAAK,CAACL,aAAa,CAACkB,OAAO,CAACN,YAAY,CAAC;MACzC;MACA,IAAIP,KAAK,CAACL,aAAa,CAACmB,MAAM,GAAG,EAAE,EAAE;QACnCd,KAAK,CAACL,aAAa,GAAGK,KAAK,CAACL,aAAa,CAACoB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACxD;IACF,CAAC;IACDC,kBAAkB,EAAEA,CAAChB,KAAK,EAAEE,MAA6B,KAAK;MAC5DF,KAAK,CAACL,aAAa,GAAGK,KAAK,CAACL,aAAa,CAACsB,MAAM,CAC7CV,YAAY,IAAKA,YAAY,CAACC,EAAE,KAAKN,MAAM,CAACC,OAC/C,CAAC;IACH,CAAC;IACDe,kBAAkB,EAAGlB,KAAK,IAAK;MAC7BA,KAAK,CAACL,aAAa,GAAG,EAAE;IAC1B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXI,aAAa;EACbE,mBAAmB;EACnBG,QAAQ;EACRC,UAAU;EACVC,eAAe;EACfU,kBAAkB;EAClBE;AACF,CAAC,GAAGtB,OAAO,CAACuB,OAAO;AAEnB,eAAevB,OAAO,CAACwB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}