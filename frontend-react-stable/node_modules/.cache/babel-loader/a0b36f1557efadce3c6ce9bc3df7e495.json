{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"listHeight\", \"listItemHeight\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\"];\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\n\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport BaseSelect, { isMultiple } from \"./BaseSelect\";\nimport useCache from \"./hooks/useCache\";\nimport useFilterOptions from \"./hooks/useFilterOptions\";\nimport useId from \"./hooks/useId\";\nimport useOptions from \"./hooks/useOptions\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport OptGroup from \"./OptGroup\";\nimport Option from \"./Option\";\nimport OptionList from \"./OptionList\";\nimport SelectContext from \"./SelectContext\";\nimport { hasValue, toArray } from \"./utils/commonUtil\";\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from \"./utils/valueUtil\";\nimport warningProps, { warningNullOptions } from \"./utils/warningPropsUtil\";\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]);\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n\n  // =========================== Option ===========================\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options;\n\n  // ========================= Wrap Value =========================\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues);\n\n    // Convert to labelInValue type\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled;\n      var rawTitle;\n\n      // Fill label & value\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled;\n        rawTitle = option === null || option === void 0 ? void 0 : option.title;\n\n        // Warning if label not same as provided\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && ! /*#__PURE__*/React.isValidElement(optionLabel) && ! /*#__PURE__*/React.isValidElement(rawLabel) && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled,\n        title: rawTitle\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]);\n\n  // =========================== Values ===========================\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n\n  // Merged value with LabelValueType\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var values = convert2LabelValues(internalValue);\n\n    // combobox no need save value when it's no value\n    if (mode === 'combobox' && !((_values$ = values[0]) !== null && _values$ !== void 0 && _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode]);\n\n  // Fill label with cache to avoid option remove\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mode, mergedValues]);\n\n  /** Convert `displayValues` to raw value type set */\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      setSearchValue(hasValue(strValue) ? String(strValue) : '');\n    }\n  }, [mergedValues]);\n\n  // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n  var createTagOption = useRefFunc(function (val, label) {\n    var _ref;\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _ref = {}, _defineProperty(_ref, mergedFieldNames.value, val), _defineProperty(_ref, mergedFieldNames.label, mergedLabel), _ref;\n  });\n\n  // Fill tag as option if mode is `tags`\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    }\n\n    // >>> Tag mode\n    var cloneOptions = _toConsumableArray(mergedOptions);\n\n    // Check if value exist in options (include new patch item)\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    };\n\n    // Fill current value as option\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp);\n\n  // Fill options with search value if needed\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n\n    // Fill search value as option\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue]);\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return _toConsumableArray(filledSearchOptions).sort(function (a, b) {\n      return filterSort(a, b);\n    });\n  }, [filledSearchOptions, filterSort]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]);\n\n  // =========================== Change ===========================\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  };\n\n  // ======================= Accessibility ========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref2$source = _ref2.source,\n      source = _ref2$source === void 0 ? 'keyboard' : _ref2$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]);\n\n  // ========================= OptionList =========================\n  var triggerSelect = function triggerSelect(val, selected, type) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        _option = _getSelectEnt2[1];\n      onSelect(wrappedValue, _option);\n    } else if (!selected && onDeselect && type !== 'clear') {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option2 = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option2);\n    }\n  };\n\n  // Used for OptionList selection\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues;\n\n    // Single mode always trigger select only with option list\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect);\n\n    // Clean search value if single or configured\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  });\n\n  // ======================= Display Change =======================\n  // BaseSelect display values change\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    var type = info.type,\n      values = info.values;\n    if (type === 'remove' || type === 'clear') {\n      values.forEach(function (item) {\n        triggerSelect(item.value, false, type);\n      });\n    }\n  };\n\n  // =========================== Search ===========================\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null);\n\n    // [Submit] Tag mode should flush input\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim();\n      // prevent empty tags from appearing when you click the Enter button\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  };\n\n  // ========================== Context ===========================\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData\n    });\n  }, [parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, childrenAsData]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n    warningNullOptions(mergedOptions, mergedFieldNames);\n  }\n\n  // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode\n    // >>> Values\n    ,\n\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Search\n    ,\n\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n    // >>> OptionList\n    ,\n\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length\n    // >>> Accessibility\n    ,\n\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_defineProperty", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_typeof", "_excluded", "useMergedState", "warning", "React", "BaseSelect", "isMultiple", "useCache", "useFilterOptions", "useId", "useOptions", "useRefFunc", "OptGroup", "Option", "OptionList", "SelectContext", "hasValue", "toArray", "fillFieldNames", "flattenOptions", "injectPropsWithOption", "warningProps", "warningNullOptions", "OMIT_DOM_PROPS", "isRawValue", "value", "Select", "forwardRef", "props", "ref", "id", "mode", "_props$prefixCls", "prefixCls", "backfill", "fieldNames", "inputValue", "searchValue", "onSearch", "_props$autoClearSearc", "autoClearSearchValue", "onSelect", "onDeselect", "_props$dropdownMatchS", "dropdownMatchSelectWidth", "filterOption", "filterSort", "optionFilterProp", "optionLabelProp", "options", "children", "defaultActiveFirstOption", "menuItemSelectedIcon", "virtual", "_props$listHeight", "listHeight", "_props$listItemHeight", "listItemHeight", "defaultValue", "labelInValue", "onChange", "restProps", "mergedId", "multiple", "childrenAsData", "mergedFilterOption", "useMemo", "undefined", "mergedFieldNames", "JSON", "stringify", "_useMergedState", "postState", "search", "_useMergedState2", "mergedSearchValue", "setSearchValue", "parsedOptions", "valueOptions", "labelOptions", "mergedOptions", "convert2LabelValues", "useCallback", "draftV<PERSON><PERSON>", "valueList", "map", "val", "rawValue", "rawLabel", "<PERSON><PERSON><PERSON>", "rawDisabled", "rawTitle", "_val$value", "key", "label", "option", "get", "_option$key", "disabled", "title", "process", "env", "NODE_ENV", "optionLabel", "isValidElement", "_useMergedState3", "_useMergedState4", "internalValue", "setInternalValue", "rawLabeledValues", "_values$", "values", "_useCache", "_useCache2", "mergedValues", "getMixedOption", "displayValues", "length", "firstValue", "item", "_item$label", "rawValues", "Set", "useEffect", "_mergedValues$", "strValue", "String", "createTagOption", "_ref", "mergedLabel", "filledTagOptions", "cloneOptions", "existOptions", "has", "sort", "a", "b", "for<PERSON>ach", "push", "filteredOptions", "filledSearchOptions", "some", "concat", "orderedFilteredOptions", "displayOptions", "trigger<PERSON>hange", "labeledV<PERSON>ues", "newVal", "index", "_mergedValues$index", "returnV<PERSON>ues", "v", "returnOptions", "_React$useState", "useState", "_React$useState2", "activeValue", "setActiveValue", "_React$useState3", "_React$useState4", "accessibilityIndex", "setAccessibilityIndex", "mergedDefaultActiveFirstOption", "onActiveValue", "active", "_ref2", "arguments", "_ref2$source", "source", "triggerSelect", "selected", "type", "getSelectEnt", "_option$key2", "_getSelectEnt", "_getSelectEnt2", "wrappedValue", "_option", "_getSelectEnt3", "_getSelectEnt4", "_wrappedValue", "_option2", "onInternalSelect", "info", "clone<PERSON><PERSON>ues", "mergedSelect", "filter", "onDisplayValuesChange", "nextV<PERSON>ues", "onInternalSearch", "searchText", "formatted", "trim", "newRawValues", "Array", "from", "onInternalSearchSplit", "words", "patchValues", "word", "opt", "newRawValue", "selectContext", "realVirtual", "createElement", "Provider", "omitDomProps", "onSearchSplit", "emptyOptions", "activeDescendantId", "displayName", "TypedSelect"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/Select.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"listHeight\", \"listItemHeight\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\"];\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\n\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport BaseSelect, { isMultiple } from \"./BaseSelect\";\nimport useCache from \"./hooks/useCache\";\nimport useFilterOptions from \"./hooks/useFilterOptions\";\nimport useId from \"./hooks/useId\";\nimport useOptions from \"./hooks/useOptions\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport OptGroup from \"./OptGroup\";\nimport Option from \"./Option\";\nimport OptionList from \"./OptionList\";\nimport SelectContext from \"./SelectContext\";\nimport { hasValue, toArray } from \"./utils/commonUtil\";\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from \"./utils/valueUtil\";\nimport warningProps, { warningNullOptions } from \"./utils/warningPropsUtil\";\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]);\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n\n  // =========================== Option ===========================\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options;\n\n  // ========================= Wrap Value =========================\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues);\n\n    // Convert to labelInValue type\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled;\n      var rawTitle;\n\n      // Fill label & value\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled;\n        rawTitle = option === null || option === void 0 ? void 0 : option.title;\n\n        // Warning if label not same as provided\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && ! /*#__PURE__*/React.isValidElement(optionLabel) && ! /*#__PURE__*/React.isValidElement(rawLabel) && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled,\n        title: rawTitle\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]);\n\n  // =========================== Values ===========================\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n\n  // Merged value with LabelValueType\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var values = convert2LabelValues(internalValue);\n\n    // combobox no need save value when it's no value\n    if (mode === 'combobox' && !((_values$ = values[0]) !== null && _values$ !== void 0 && _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode]);\n\n  // Fill label with cache to avoid option remove\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mode, mergedValues]);\n\n  /** Convert `displayValues` to raw value type set */\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      setSearchValue(hasValue(strValue) ? String(strValue) : '');\n    }\n  }, [mergedValues]);\n\n  // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n  var createTagOption = useRefFunc(function (val, label) {\n    var _ref;\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _ref = {}, _defineProperty(_ref, mergedFieldNames.value, val), _defineProperty(_ref, mergedFieldNames.label, mergedLabel), _ref;\n  });\n\n  // Fill tag as option if mode is `tags`\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    }\n\n    // >>> Tag mode\n    var cloneOptions = _toConsumableArray(mergedOptions);\n\n    // Check if value exist in options (include new patch item)\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    };\n\n    // Fill current value as option\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp);\n\n  // Fill options with search value if needed\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n\n    // Fill search value as option\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue]);\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return _toConsumableArray(filledSearchOptions).sort(function (a, b) {\n      return filterSort(a, b);\n    });\n  }, [filledSearchOptions, filterSort]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]);\n\n  // =========================== Change ===========================\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  };\n\n  // ======================= Accessibility ========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref2$source = _ref2.source,\n      source = _ref2$source === void 0 ? 'keyboard' : _ref2$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]);\n\n  // ========================= OptionList =========================\n  var triggerSelect = function triggerSelect(val, selected, type) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        _option = _getSelectEnt2[1];\n      onSelect(wrappedValue, _option);\n    } else if (!selected && onDeselect && type !== 'clear') {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option2 = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option2);\n    }\n  };\n\n  // Used for OptionList selection\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues;\n\n    // Single mode always trigger select only with option list\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect);\n\n    // Clean search value if single or configured\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  });\n\n  // ======================= Display Change =======================\n  // BaseSelect display values change\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    var type = info.type,\n      values = info.values;\n    if (type === 'remove' || type === 'clear') {\n      values.forEach(function (item) {\n        triggerSelect(item.value, false, type);\n      });\n    }\n  };\n\n  // =========================== Search ===========================\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null);\n\n    // [Submit] Tag mode should flush input\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim();\n      // prevent empty tags from appearing when you click the Enter button\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  };\n\n  // ========================== Context ===========================\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData\n    });\n  }, [parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, childrenAsData]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n    warningNullOptions(mergedOptions, mergedFieldNames);\n  }\n\n  // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode\n    // >>> Values\n    ,\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n    // >>> OptionList\n    ,\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length\n    // >>> Accessibility\n    ,\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,0BAA0B,EAAE,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,CAAC;AAC5a;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,UAAU,QAAQ,cAAc;AACrD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,QAAQ,EAAEC,OAAO,QAAQ,oBAAoB;AACtD,SAASC,cAAc,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,mBAAmB;AACzF,OAAOC,YAAY,IAAIC,kBAAkB,QAAQ,0BAA0B;AAC3E,IAAIC,cAAc,GAAG,CAAC,YAAY,CAAC;AACnC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAACA,KAAK,IAAIzB,OAAO,CAACyB,KAAK,CAAC,KAAK,QAAQ;AAC9C;AACA,IAAIC,MAAM,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACfC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,qBAAqB,GAAGX,KAAK,CAACY,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACtFE,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,qBAAqB,GAAGf,KAAK,CAACgB,wBAAwB;IACtDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FE,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,eAAe,GAAGpB,KAAK,CAACoB,eAAe;IACvCC,OAAO,GAAGrB,KAAK,CAACqB,OAAO;IACvBC,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,wBAAwB,GAAGvB,KAAK,CAACuB,wBAAwB;IACzDC,oBAAoB,GAAGxB,KAAK,CAACwB,oBAAoB;IACjDC,OAAO,GAAGzB,KAAK,CAACyB,OAAO;IACvBC,iBAAiB,GAAG1B,KAAK,CAAC2B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,qBAAqB,GAAG5B,KAAK,CAAC6B,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9E/B,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBiC,YAAY,GAAG9B,KAAK,CAAC8B,YAAY;IACjCC,YAAY,GAAG/B,KAAK,CAAC+B,YAAY;IACjCC,QAAQ,GAAGhC,KAAK,CAACgC,QAAQ;IACzBC,SAAS,GAAG9D,wBAAwB,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACxD,IAAI6D,QAAQ,GAAGrD,KAAK,CAACqB,EAAE,CAAC;EACxB,IAAIiC,QAAQ,GAAGzD,UAAU,CAACyB,IAAI,CAAC;EAC/B,IAAIiC,cAAc,GAAG,CAAC,EAAE,CAACf,OAAO,IAAIC,QAAQ,CAAC;EAC7C,IAAIe,kBAAkB,GAAG7D,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACjD,IAAIrB,YAAY,KAAKsB,SAAS,IAAIpC,IAAI,KAAK,UAAU,EAAE;MACrD,OAAO,KAAK;IACd;IACA,OAAOc,YAAY;EACrB,CAAC,EAAE,CAACA,YAAY,EAAEd,IAAI,CAAC,CAAC;;EAExB;EACA,IAAIqC,gBAAgB,GAAGhE,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,OAAOhD,cAAc,CAACiB,UAAU,EAAE6B,cAAc,CAAC;EACnD,CAAC,EAAE;EACH;EACA;EACAK,IAAI,CAACC,SAAS,CAACnC,UAAU,CAAC,EAAE6B,cAAc;EAC1C,+CAA+C,CAAC;;EAEhD;EACA,IAAIO,eAAe,GAAGrE,cAAc,CAAC,EAAE,EAAE;MACrCuB,KAAK,EAAEY,WAAW,KAAK8B,SAAS,GAAG9B,WAAW,GAAGD,UAAU;MAC3DoC,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;QACpC,OAAOA,MAAM,IAAI,EAAE;MACrB;IACF,CAAC,CAAC;IACFC,gBAAgB,GAAG5E,cAAc,CAACyE,eAAe,EAAE,CAAC,CAAC;IACrDI,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEtC;EACA,IAAIG,aAAa,GAAGnE,UAAU,CAACuC,OAAO,EAAEC,QAAQ,EAAEkB,gBAAgB,EAAErB,gBAAgB,EAAEC,eAAe,CAAC;EACtG,IAAI8B,YAAY,GAAGD,aAAa,CAACC,YAAY;IAC3CC,YAAY,GAAGF,aAAa,CAACE,YAAY;IACzCC,aAAa,GAAGH,aAAa,CAAC5B,OAAO;;EAEvC;EACA,IAAIgC,mBAAmB,GAAG7E,KAAK,CAAC8E,WAAW,CAAC,UAAUC,WAAW,EAAE;IACjE;IACA,IAAIC,SAAS,GAAGnE,OAAO,CAACkE,WAAW,CAAC;;IAEpC;IACA,OAAOC,SAAS,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIC,QAAQ;MACZ,IAAIC,QAAQ;MACZ,IAAIC,MAAM;MACV,IAAIC,WAAW;MACf,IAAIC,QAAQ;;MAEZ;MACA,IAAInE,UAAU,CAAC8D,GAAG,CAAC,EAAE;QACnBC,QAAQ,GAAGD,GAAG;MAChB,CAAC,MAAM;QACL,IAAIM,UAAU;QACdH,MAAM,GAAGH,GAAG,CAACO,GAAG;QAChBL,QAAQ,GAAGF,GAAG,CAACQ,KAAK;QACpBP,QAAQ,GAAG,CAACK,UAAU,GAAGN,GAAG,CAAC7D,KAAK,MAAM,IAAI,IAAImE,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGH,MAAM;MAC7F;MACA,IAAIM,MAAM,GAAGjB,YAAY,CAACkB,GAAG,CAACT,QAAQ,CAAC;MACvC,IAAIQ,MAAM,EAAE;QACV,IAAIE,WAAW;QACf;QACA,IAAIT,QAAQ,KAAKrB,SAAS,EAAEqB,QAAQ,GAAGO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC/C,eAAe,IAAIoB,gBAAgB,CAAC0B,KAAK,CAAC;QACxI,IAAIL,MAAM,KAAKtB,SAAS,EAAEsB,MAAM,GAAG,CAACQ,WAAW,GAAGF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAII,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGV,QAAQ;QACzKG,WAAW,GAAGK,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,QAAQ;QAC7EP,QAAQ,GAAGI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,KAAK;;QAEvE;QACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACtD,eAAe,EAAE;UAC7D,IAAIuD,WAAW,GAAGR,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,gBAAgB,CAAC0B,KAAK,CAAC;UAChG,IAAIS,WAAW,KAAKpC,SAAS,IAAI,EAAE,aAAa/D,KAAK,CAACoG,cAAc,CAACD,WAAW,CAAC,IAAI,EAAE,aAAanG,KAAK,CAACoG,cAAc,CAAChB,QAAQ,CAAC,IAAIe,WAAW,KAAKf,QAAQ,EAAE;YAC9JrF,OAAO,CAAC,KAAK,EAAE,8DAA8D,CAAC;UAChF;QACF;MACF;MACA,OAAO;QACL2F,KAAK,EAAEN,QAAQ;QACf/D,KAAK,EAAE8D,QAAQ;QACfM,GAAG,EAAEJ,MAAM;QACXS,QAAQ,EAAER,WAAW;QACrBS,KAAK,EAAER;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvB,gBAAgB,EAAEpB,eAAe,EAAE8B,YAAY,CAAC,CAAC;;EAErD;EACA,IAAI2B,gBAAgB,GAAGvG,cAAc,CAACwD,YAAY,EAAE;MAChDjC,KAAK,EAAEA;IACT,CAAC,CAAC;IACFiF,gBAAgB,GAAG5G,cAAc,CAAC2G,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAExC;EACA,IAAIG,gBAAgB,GAAGzG,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,IAAI4C,QAAQ;IACZ,IAAIC,MAAM,GAAG9B,mBAAmB,CAAC0B,aAAa,CAAC;;IAE/C;IACA,IAAI5E,IAAI,KAAK,UAAU,IAAI,EAAE,CAAC+E,QAAQ,GAAGC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACrF,KAAK,CAAC,EAAE;MACtG,OAAO,EAAE;IACX;IACA,OAAOsF,MAAM;EACf,CAAC,EAAE,CAACJ,aAAa,EAAE1B,mBAAmB,EAAElD,IAAI,CAAC,CAAC;;EAE9C;EACA,IAAIiF,SAAS,GAAGzG,QAAQ,CAACsG,gBAAgB,EAAE/B,YAAY,CAAC;IACtDmC,UAAU,GAAGnH,cAAc,CAACkH,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,aAAa,GAAGhH,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC5C;IACA;IACA,IAAI,CAACnC,IAAI,IAAImF,YAAY,CAACG,MAAM,KAAK,CAAC,EAAE;MACtC,IAAIC,UAAU,GAAGJ,YAAY,CAAC,CAAC,CAAC;MAChC,IAAII,UAAU,CAAC7F,KAAK,KAAK,IAAI,KAAK6F,UAAU,CAACxB,KAAK,KAAK,IAAI,IAAIwB,UAAU,CAACxB,KAAK,KAAK3B,SAAS,CAAC,EAAE;QAC9F,OAAO,EAAE;MACX;IACF;IACA,OAAO+C,YAAY,CAAC7B,GAAG,CAAC,UAAUkC,IAAI,EAAE;MACtC,IAAIC,WAAW;MACf,OAAO3H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0H,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDzB,KAAK,EAAE,CAAC0B,WAAW,GAAGD,IAAI,CAACzB,KAAK,MAAM,IAAI,IAAI0B,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGD,IAAI,CAAC9F;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACM,IAAI,EAAEmF,YAAY,CAAC,CAAC;;EAExB;EACA,IAAIO,SAAS,GAAGrH,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACxC,OAAO,IAAIwD,GAAG,CAACR,YAAY,CAAC7B,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC7C,OAAOA,GAAG,CAAC7D,KAAK;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACyF,YAAY,CAAC,CAAC;EAClB9G,KAAK,CAACuH,SAAS,CAAC,YAAY;IAC1B,IAAI5F,IAAI,KAAK,UAAU,EAAE;MACvB,IAAI6F,cAAc;MAClB,IAAIC,QAAQ,GAAG,CAACD,cAAc,GAAGV,YAAY,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIU,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACnG,KAAK;MACvHmD,cAAc,CAAC5D,QAAQ,CAAC6G,QAAQ,CAAC,GAAGC,MAAM,CAACD,QAAQ,CAAC,GAAG,EAAE,CAAC;IAC5D;EACF,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;;EAElB;EACA;EACA,IAAIa,eAAe,GAAGpH,UAAU,CAAC,UAAU2E,GAAG,EAAEQ,KAAK,EAAE;IACrD,IAAIkC,IAAI;IACR,IAAIC,WAAW,GAAGnC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGR,GAAG;IAClE,OAAO0C,IAAI,GAAG,CAAC,CAAC,EAAEpI,eAAe,CAACoI,IAAI,EAAE5D,gBAAgB,CAAC3C,KAAK,EAAE6D,GAAG,CAAC,EAAE1F,eAAe,CAACoI,IAAI,EAAE5D,gBAAgB,CAAC0B,KAAK,EAAEmC,WAAW,CAAC,EAAED,IAAI;EACxI,CAAC,CAAC;;EAEF;EACA,IAAIE,gBAAgB,GAAG9H,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,IAAInC,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOiD,aAAa;IACtB;;IAEA;IACA,IAAImD,YAAY,GAAGxI,kBAAkB,CAACqF,aAAa,CAAC;;IAEpD;IACA,IAAIoD,YAAY,GAAG,SAASA,YAAYA,CAAC9C,GAAG,EAAE;MAC5C,OAAOR,YAAY,CAACuD,GAAG,CAAC/C,GAAG,CAAC;IAC9B,CAAC;;IAED;IACA3F,kBAAkB,CAACuH,YAAY,CAAC,CAACoB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpD,OAAOD,CAAC,CAAC9G,KAAK,GAAG+G,CAAC,CAAC/G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC,CAACgH,OAAO,CAAC,UAAUlB,IAAI,EAAE;MACzB,IAAIjC,GAAG,GAAGiC,IAAI,CAAC9F,KAAK;MACpB,IAAI,CAAC2G,YAAY,CAAC9C,GAAG,CAAC,EAAE;QACtB6C,YAAY,CAACO,IAAI,CAACX,eAAe,CAACzC,GAAG,EAAEiC,IAAI,CAACzB,KAAK,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IACF,OAAOqC,YAAY;EACrB,CAAC,EAAE,CAACJ,eAAe,EAAE/C,aAAa,EAAEF,YAAY,EAAEoC,YAAY,EAAEnF,IAAI,CAAC,CAAC;EACtE,IAAI4G,eAAe,GAAGnI,gBAAgB,CAAC0H,gBAAgB,EAAE9D,gBAAgB,EAAEO,iBAAiB,EAAEV,kBAAkB,EAAElB,gBAAgB,CAAC;;EAEnI;EACA,IAAI6F,mBAAmB,GAAGxI,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAClD,IAAInC,IAAI,KAAK,MAAM,IAAI,CAAC4C,iBAAiB,IAAIgE,eAAe,CAACE,IAAI,CAAC,UAAUtB,IAAI,EAAE;MAChF,OAAOA,IAAI,CAACxE,gBAAgB,IAAI,OAAO,CAAC,KAAK4B,iBAAiB;IAChE,CAAC,CAAC,EAAE;MACF,OAAOgE,eAAe;IACxB;;IAEA;IACA,OAAO,CAACZ,eAAe,CAACpD,iBAAiB,CAAC,CAAC,CAACmE,MAAM,CAACnJ,kBAAkB,CAACgJ,eAAe,CAAC,CAAC;EACzF,CAAC,EAAE,CAACZ,eAAe,EAAEhF,gBAAgB,EAAEhB,IAAI,EAAE4G,eAAe,EAAEhE,iBAAiB,CAAC,CAAC;EACjF,IAAIoE,sBAAsB,GAAG3I,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACrD,IAAI,CAACpB,UAAU,EAAE;MACf,OAAO8F,mBAAmB;IAC5B;IACA,OAAOjJ,kBAAkB,CAACiJ,mBAAmB,CAAC,CAACN,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAClE,OAAO1F,UAAU,CAACyF,CAAC,EAAEC,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACI,mBAAmB,EAAE9F,UAAU,CAAC,CAAC;EACrC,IAAIkG,cAAc,GAAG5I,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC7C,OAAO/C,cAAc,CAAC4H,sBAAsB,EAAE;MAC5C5G,UAAU,EAAEiC,gBAAgB;MAC5BJ,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC+E,sBAAsB,EAAE3E,gBAAgB,EAAEJ,cAAc,CAAC,CAAC;;EAE9D;EACA,IAAIiF,aAAa,GAAG,SAASA,aAAaA,CAAClC,MAAM,EAAE;IACjD,IAAImC,aAAa,GAAGjE,mBAAmB,CAAC8B,MAAM,CAAC;IAC/CH,gBAAgB,CAACsC,aAAa,CAAC;IAC/B,IAAItF,QAAQ;IACZ;IACAsF,aAAa,CAAC7B,MAAM,KAAKH,YAAY,CAACG,MAAM,IAAI6B,aAAa,CAACL,IAAI,CAAC,UAAUM,MAAM,EAAEC,KAAK,EAAE;MAC1F,IAAIC,mBAAmB;MACvB,OAAO,CAAC,CAACA,mBAAmB,GAAGnC,YAAY,CAACkC,KAAK,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC5H,KAAK,OAAO0H,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC1H,KAAK,CAAC;IACzM,CAAC,CAAC,CAAC,EAAE;MACH,IAAI6H,YAAY,GAAG3F,YAAY,GAAGuF,aAAa,GAAGA,aAAa,CAAC7D,GAAG,CAAC,UAAUkE,CAAC,EAAE;QAC/E,OAAOA,CAAC,CAAC9H,KAAK;MAChB,CAAC,CAAC;MACF,IAAI+H,aAAa,GAAGN,aAAa,CAAC7D,GAAG,CAAC,UAAUkE,CAAC,EAAE;QACjD,OAAOnI,qBAAqB,CAAC+F,cAAc,CAACoC,CAAC,CAAC9H,KAAK,CAAC,CAAC;MACvD,CAAC,CAAC;MACFmC,QAAQ;MACR;MACAG,QAAQ,GAAGuF,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC;MACzC;MACAvF,QAAQ,GAAGyF,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,IAAIC,eAAe,GAAGrJ,KAAK,CAACsJ,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAG7J,cAAc,CAAC2J,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG1J,KAAK,CAACsJ,QAAQ,CAAC,CAAC,CAAC;IACtCK,gBAAgB,GAAGjK,cAAc,CAACgK,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,8BAA8B,GAAG/G,wBAAwB,KAAKgB,SAAS,GAAGhB,wBAAwB,GAAGpB,IAAI,KAAK,UAAU;EAC5H,IAAIoI,aAAa,GAAG/J,KAAK,CAAC8E,WAAW,CAAC,UAAUkF,MAAM,EAAEhB,KAAK,EAAE;IAC7D,IAAIiB,KAAK,GAAGC,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKnG,SAAS,GAAGmG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAChFC,YAAY,GAAGF,KAAK,CAACG,MAAM;MAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,YAAY;IAC9DN,qBAAqB,CAACb,KAAK,CAAC;IAC5B,IAAIlH,QAAQ,IAAIH,IAAI,KAAK,UAAU,IAAIqI,MAAM,KAAK,IAAI,IAAII,MAAM,KAAK,UAAU,EAAE;MAC/EX,cAAc,CAAC/B,MAAM,CAACsC,MAAM,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAAClI,QAAQ,EAAEH,IAAI,CAAC,CAAC;;EAEpB;EACA,IAAI0I,aAAa,GAAG,SAASA,aAAaA,CAACnF,GAAG,EAAEoF,QAAQ,EAAEC,IAAI,EAAE;IAC9D,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;MACzC,IAAIC,YAAY;MAChB,IAAI9E,MAAM,GAAGoB,cAAc,CAAC7B,GAAG,CAAC;MAChC,OAAO,CAAC3B,YAAY,GAAG;QACrBmC,KAAK,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,gBAAgB,CAAC0B,KAAK,CAAC;QACrFrE,KAAK,EAAE6D,GAAG;QACVO,GAAG,EAAE,CAACgF,YAAY,GAAG9E,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAIgF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGvF;MACxI,CAAC,GAAGA,GAAG,EAAElE,qBAAqB,CAAC2E,MAAM,CAAC,CAAC;IACzC,CAAC;IACD,IAAI2E,QAAQ,IAAIjI,QAAQ,EAAE;MACxB,IAAIqI,aAAa,GAAGF,YAAY,CAAC,CAAC;QAChCG,cAAc,GAAGjL,cAAc,CAACgL,aAAa,EAAE,CAAC,CAAC;QACjDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;QAChCE,OAAO,GAAGF,cAAc,CAAC,CAAC,CAAC;MAC7BtI,QAAQ,CAACuI,YAAY,EAAEC,OAAO,CAAC;IACjC,CAAC,MAAM,IAAI,CAACP,QAAQ,IAAIhI,UAAU,IAAIiI,IAAI,KAAK,OAAO,EAAE;MACtD,IAAIO,cAAc,GAAGN,YAAY,CAAC,CAAC;QACjCO,cAAc,GAAGrL,cAAc,CAACoL,cAAc,EAAE,CAAC,CAAC;QAClDE,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;QACjCE,QAAQ,GAAGF,cAAc,CAAC,CAAC,CAAC;MAC9BzI,UAAU,CAAC0I,aAAa,EAAEC,QAAQ,CAAC;IACrC;EACF,CAAC;;EAED;EACA,IAAIC,gBAAgB,GAAG3K,UAAU,CAAC,UAAU2E,GAAG,EAAEiG,IAAI,EAAE;IACrD,IAAIC,WAAW;;IAEf;IACA,IAAIC,YAAY,GAAG1H,QAAQ,GAAGwH,IAAI,CAACb,QAAQ,GAAG,IAAI;IAClD,IAAIe,YAAY,EAAE;MAChBD,WAAW,GAAGzH,QAAQ,GAAG,EAAE,CAAC+E,MAAM,CAACnJ,kBAAkB,CAACuH,YAAY,CAAC,EAAE,CAAC5B,GAAG,CAAC,CAAC,GAAG,CAACA,GAAG,CAAC;IACrF,CAAC,MAAM;MACLkG,WAAW,GAAGtE,YAAY,CAACwE,MAAM,CAAC,UAAUnC,CAAC,EAAE;QAC7C,OAAOA,CAAC,CAAC9H,KAAK,KAAK6D,GAAG;MACxB,CAAC,CAAC;IACJ;IACA2D,aAAa,CAACuC,WAAW,CAAC;IAC1Bf,aAAa,CAACnF,GAAG,EAAEmG,YAAY,CAAC;;IAEhC;IACA,IAAI1J,IAAI,KAAK,UAAU,EAAE;MACvB;MACA8H,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,MAAM,IAAI,CAACvJ,UAAU,IAAIkC,oBAAoB,EAAE;MAC9CoC,cAAc,CAAC,EAAE,CAAC;MAClBiF,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA;EACA,IAAI8B,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,UAAU,EAAEL,IAAI,EAAE;IAC3EtC,aAAa,CAAC2C,UAAU,CAAC;IACzB,IAAIjB,IAAI,GAAGY,IAAI,CAACZ,IAAI;MAClB5D,MAAM,GAAGwE,IAAI,CAACxE,MAAM;IACtB,IAAI4D,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,OAAO,EAAE;MACzC5D,MAAM,CAAC0B,OAAO,CAAC,UAAUlB,IAAI,EAAE;QAC7BkD,aAAa,CAAClD,IAAI,CAAC9F,KAAK,EAAE,KAAK,EAAEkJ,IAAI,CAAC;MACxC,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAIkB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAEP,IAAI,EAAE;IACjE3G,cAAc,CAACkH,UAAU,CAAC;IAC1BjC,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACA,IAAI0B,IAAI,CAACf,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIuB,SAAS,GAAG,CAACD,UAAU,IAAI,EAAE,EAAEE,IAAI,CAAC,CAAC;MACzC;MACA,IAAID,SAAS,EAAE;QACb,IAAIE,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIzE,GAAG,CAAC,EAAE,CAACoB,MAAM,CAACnJ,kBAAkB,CAAC8H,SAAS,CAAC,EAAE,CAACsE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7F9C,aAAa,CAACgD,YAAY,CAAC;QAC3BxB,aAAa,CAACsB,SAAS,EAAE,IAAI,CAAC;QAC9BnH,cAAc,CAAC,EAAE,CAAC;MACpB;MACA;IACF;IACA,IAAI2G,IAAI,CAACf,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAIzI,IAAI,KAAK,UAAU,EAAE;QACvBkH,aAAa,CAAC6C,UAAU,CAAC;MAC3B;MACAxJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwJ,UAAU,CAAC;IAC1E;EACF,CAAC;EACD,IAAIM,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAE;IAChE,IAAIC,WAAW,GAAGD,KAAK;IACvB,IAAItK,IAAI,KAAK,MAAM,EAAE;MACnBuK,WAAW,GAAGD,KAAK,CAAChH,GAAG,CAAC,UAAUkH,IAAI,EAAE;QACtC,IAAIC,GAAG,GAAGzH,YAAY,CAACiB,GAAG,CAACuG,IAAI,CAAC;QAChC,OAAOC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC/K,KAAK;MAC5D,CAAC,CAAC,CAACiK,MAAM,CAAC,UAAUpG,GAAG,EAAE;QACvB,OAAOA,GAAG,KAAKnB,SAAS;MAC1B,CAAC,CAAC;IACJ;IACA,IAAI8H,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIzE,GAAG,CAAC,EAAE,CAACoB,MAAM,CAACnJ,kBAAkB,CAAC8H,SAAS,CAAC,EAAE9H,kBAAkB,CAAC2M,WAAW,CAAC,CAAC,CAAC,CAAC;IACjHrD,aAAa,CAACgD,YAAY,CAAC;IAC3BA,YAAY,CAACxD,OAAO,CAAC,UAAUgE,WAAW,EAAE;MAC1ChC,aAAa,CAACgC,WAAW,EAAE,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIC,aAAa,GAAGtM,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC5C,IAAIyI,WAAW,GAAGtJ,OAAO,KAAK,KAAK,IAAIT,wBAAwB,KAAK,KAAK;IACzE,OAAO/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgF,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzD1D,cAAc,EAAE6H,cAAc;MAC9BmB,aAAa,EAAEA,aAAa;MAC5BhH,wBAAwB,EAAE+G,8BAA8B;MACxDzH,QAAQ,EAAE6I,gBAAgB;MAC1BlI,oBAAoB,EAAEA,oBAAoB;MAC1CqE,SAAS,EAAEA,SAAS;MACpBtF,UAAU,EAAEiC,gBAAgB;MAC5Bf,OAAO,EAAEsJ,WAAW;MACpBpJ,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9BO,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,aAAa,EAAEmE,cAAc,EAAEmB,aAAa,EAAED,8BAA8B,EAAEoB,gBAAgB,EAAElI,oBAAoB,EAAEqE,SAAS,EAAErD,gBAAgB,EAAEf,OAAO,EAAET,wBAAwB,EAAEW,UAAU,EAAEE,cAAc,EAAEO,cAAc,CAAC,CAAC;;EAEtO;EACA,IAAIoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjF,YAAY,CAACO,KAAK,CAAC;IACnBN,kBAAkB,CAAC0D,aAAa,EAAEZ,gBAAgB,CAAC;EACrD;;EAEA;EACA;EACA;EACA,OAAO,aAAahE,KAAK,CAACwM,aAAa,CAAC7L,aAAa,CAAC8L,QAAQ,EAAE;IAC9DpL,KAAK,EAAEiL;EACT,CAAC,EAAE,aAAatM,KAAK,CAACwM,aAAa,CAACvM,UAAU,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEmE,SAAS,EAAE;IACtE;IACA/B,EAAE,EAAEgC,QAAQ;IACZ7B,SAAS,EAAEA,SAAS;IACpBJ,GAAG,EAAEA,GAAG;IACRiL,YAAY,EAAEvL,cAAc;IAC5BQ,IAAI,EAAEA;IACN;IAAA;;IAEAqF,aAAa,EAAEA,aAAa;IAC5BuE,qBAAqB,EAAEA;IACvB;IAAA;;IAEAtJ,WAAW,EAAEsC,iBAAiB;IAC9BrC,QAAQ,EAAEuJ,gBAAgB;IAC1BrJ,oBAAoB,EAAEA,oBAAoB;IAC1CuK,aAAa,EAAEX,qBAAqB;IACpCxJ,wBAAwB,EAAEA;IAC1B;IAAA;;IAEA9B,UAAU,EAAEA,UAAU;IACtBkM,YAAY,EAAE,CAAChE,cAAc,CAAC3B;IAC9B;IAAA;;IAEAuC,WAAW,EAAEA,WAAW;IACxBqD,kBAAkB,EAAE,EAAE,CAACnE,MAAM,CAAChF,QAAQ,EAAE,QAAQ,CAAC,CAACgF,MAAM,CAACkB,kBAAkB;EAC7E,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC5E,MAAM,CAACwL,WAAW,GAAG,QAAQ;AAC/B;AACA,IAAIC,WAAW,GAAGzL,MAAM;AACxByL,WAAW,CAACtM,MAAM,GAAGA,MAAM;AAC3BsM,WAAW,CAACvM,QAAQ,GAAGA,QAAQ;AAC/B,eAAeuM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}