{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantize;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _linear = require(\"./linear.js\");\nvar _init = require(\"./init.js\");\nfunction quantize() {\n  var x0 = 0,\n    x1 = 1,\n    n = 1,\n    domain = [0.5],\n    range = [0, 1],\n    unknown;\n  function scale(x) {\n    return x != null && x <= x ? range[(0, _index.bisect)(domain, x, 0, n)] : unknown;\n  }\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n  scale.range = function (_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : i < 1 ? [x0, domain[0]] : i >= n ? [domain[n - 1], x1] : [domain[i - 1], domain[i]];\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n  scale.thresholds = function () {\n    return domain.slice();\n  };\n  scale.copy = function () {\n    return quantize().domain([x0, x1]).range(range).unknown(unknown);\n  };\n  return _init.initRange.apply((0, _linear.linearish)(scale), arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "quantize", "_index", "require", "_linear", "_init", "x0", "x1", "n", "domain", "range", "unknown", "scale", "x", "bisect", "rescale", "i", "Array", "_", "arguments", "length", "from", "slice", "invertExtent", "y", "indexOf", "NaN", "thresholds", "copy", "initRange", "apply", "linearish"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/quantize.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantize;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _linear = require(\"./linear.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0, _index.bisect)(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n\n    return scale;\n  }\n\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : i < 1 ? [x0, domain[0]] : i >= n ? [domain[n - 1], x1] : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function () {\n    return domain.slice();\n  };\n\n  scale.copy = function () {\n    return quantize().domain([x0, x1]).range(range).unknown(unknown);\n  };\n\n  return _init.initRange.apply((0, _linear.linearish)(scale), arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,OAAO,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASF,QAAQA,CAAA,EAAG;EAClB,IAAIK,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,CAAC,GAAG,CAAC;IACLC,MAAM,GAAG,CAAC,GAAG,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC,EAAER,MAAM,CAACY,MAAM,EAAEL,MAAM,EAAEI,CAAC,EAAE,CAAC,EAAEL,CAAC,CAAC,CAAC,GAAGG,OAAO;EACnF;EAEA,SAASI,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG,CAAC,CAAC;IACVP,MAAM,GAAG,IAAIQ,KAAK,CAACT,CAAC,CAAC;IAErB,OAAO,EAAEQ,CAAC,GAAGR,CAAC,EAAEC,MAAM,CAACO,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,GAAG,CAAC,IAAIT,EAAE,GAAG,CAACS,CAAC,GAAGR,CAAC,IAAIF,EAAE,KAAKE,CAAC,GAAG,CAAC,CAAC;IAEnE,OAAOI,KAAK;EACd;EAEAA,KAAK,CAACH,MAAM,GAAG,UAAUS,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAI,CAACd,EAAE,EAAEC,EAAE,CAAC,GAAGW,CAAC,EAAEZ,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEQ,OAAO,CAAC,CAAC,IAAI,CAACT,EAAE,EAAEC,EAAE,CAAC;EACpF,CAAC;EAEDK,KAAK,CAACF,KAAK,GAAG,UAAUQ,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIZ,CAAC,GAAG,CAACE,KAAK,GAAGO,KAAK,CAACI,IAAI,CAACH,CAAC,CAAC,EAAEE,MAAM,GAAG,CAAC,EAAEL,OAAO,CAAC,CAAC,IAAIL,KAAK,CAACY,KAAK,CAAC,CAAC;EAC/F,CAAC;EAEDV,KAAK,CAACW,YAAY,GAAG,UAAUC,CAAC,EAAE;IAChC,IAAIR,CAAC,GAAGN,KAAK,CAACe,OAAO,CAACD,CAAC,CAAC;IACxB,OAAOR,CAAC,GAAG,CAAC,GAAG,CAACU,GAAG,EAAEA,GAAG,CAAC,GAAGV,CAAC,GAAG,CAAC,GAAG,CAACV,EAAE,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGO,CAAC,IAAIR,CAAC,GAAG,CAACC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,EAAED,EAAE,CAAC,GAAG,CAACE,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,CAAC,CAAC;EACjH,CAAC;EAEDJ,KAAK,CAACD,OAAO,GAAG,UAAUO,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIT,OAAO,GAAGO,CAAC,EAAEN,KAAK,IAAIA,KAAK;EACxD,CAAC;EAEDA,KAAK,CAACe,UAAU,GAAG,YAAY;IAC7B,OAAOlB,MAAM,CAACa,KAAK,CAAC,CAAC;EACvB,CAAC;EAEDV,KAAK,CAACgB,IAAI,GAAG,YAAY;IACvB,OAAO3B,QAAQ,CAAC,CAAC,CAACQ,MAAM,CAAC,CAACH,EAAE,EAAEC,EAAE,CAAC,CAAC,CAACG,KAAK,CAACA,KAAK,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EAClE,CAAC;EAED,OAAON,KAAK,CAACwB,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE1B,OAAO,CAAC2B,SAAS,EAAEnB,KAAK,CAAC,EAAEO,SAAS,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "script"}