{"ast": null, "code": "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict';\n\nvar base64 = require('base64-js');\nvar ieee754 = require('ieee754');\nvar isArray = require('isarray');\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength();\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function () {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 &&\n    // typed array instances can be augmented\n    typeof arr.subarray === 'function' &&\n    // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n    that.length = length;\n  }\n  return that;\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n    return allocUnsafe(this, arg);\n  }\n  return from(this, arg, encodingOrOffset, length);\n}\nBuffer.poolSize = 8192; // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n  return fromObject(that, value);\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n  return createBuffer(that, size);\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n  return that;\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n  return that;\n}\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n  return that;\n}\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n  return that;\n}\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n    if (that.length === 0) {\n      return that;\n    }\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n      return fromArrayLike(that, obj);\n    }\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n  return length | 0;\n}\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n  return Buffer.alloc(+length);\n}\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n    default:\n      return false;\n  }\n};\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n  var i;\n  if (length === undefined) {\n    length = 0;\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n  return buffer;\n};\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n  var len = string.length;\n  if (len === 0) return 0;\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n      case 'hex':\n        return len >>> 1;\n      case 'base64':\n        return base64ToBytes(string).length;\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\nBuffer.byteLength = byteLength;\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false;\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0;\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return '';\n  }\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n  if (end <= 0) {\n    return '';\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0;\n  start >>>= 0;\n  if (end <= start) {\n    return '';\n  }\n  if (!encoding) encoding = 'utf8';\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n      case 'ascii':\n        return asciiSlice(this, start, end);\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n      case 'base64':\n        return base64Slice(this, start, end);\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true;\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n  return this;\n};\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n  return this;\n};\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n  return this;\n};\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n  return '<Buffer ' + str + '>';\n};\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n  if (start === undefined) {\n    start = 0;\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n  if (start >= end) {\n    return 1;\n  }\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1;\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n  byteOffset = +byteOffset; // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n  throw new TypeError('val must be string, number or Buffer');\n}\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n  var i;\n  if (dir) {\n    var foundIndex = -1;\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n      if (found) return i;\n    }\n  }\n  return -1;\n}\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n    if (length > remaining) {\n      length = remaining;\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n  return i;\n}\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0;\n    // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0;\n    // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    }\n    // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n          break;\n        case 2:\n          secondByte = buf[i + 1];\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break;\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break;\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n      }\n    }\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n  return decodeCodePointsArray(res);\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = '';\n  var i = 0;\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n  return res;\n}\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n  return ret;\n}\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n  return ret;\n}\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n  return out;\n}\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n  return res;\n}\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n  if (end < start) end = start;\n  var newBuf;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n  return newBuf;\n};\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  return val;\n};\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n  var val = this[offset + --byteLength];\n  var mul = 1;\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n  return val;\n};\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2;\n};\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2;\n};\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4;\n};\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4;\n};\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2;\n};\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2;\n};\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4;\n};\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4;\n};\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n};\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start;\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0;\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds');\n\n  // Are we oob?\n  if (end > this.length) end = this.length;\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n  var len = end - start;\n  var i;\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n  return len;\n};\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n      if (code < 256) {\n        val = code;\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n  if (end <= start) {\n    return this;\n  }\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n  return this;\n};\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '');\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return '';\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n  return str;\n}\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i);\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        }\n\n        // valid lead\n        leadSurrogate = codePoint;\n        continue;\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n    leadSurrogate = null;\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n  return bytes;\n}\nfunction asciiToBytes(str) {\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n  return byteArray;\n}\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n  return byteArray;\n}\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n  return i;\n}\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "map": {"version": 3, "names": ["base64", "require", "ieee754", "isArray", "exports", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "INSPECT_MAX_BYTES", "TYPED_ARRAY_SUPPORT", "global", "undefined", "typedArraySupport", "kMaxLength", "arr", "Uint8Array", "__proto__", "prototype", "foo", "subarray", "byteLength", "e", "createBuffer", "that", "length", "RangeError", "arg", "encodingOrOffset", "Error", "allocUnsafe", "from", "poolSize", "_augment", "value", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fromArrayBuffer", "fromString", "fromObject", "Symbol", "species", "Object", "defineProperty", "configurable", "assertSize", "size", "alloc", "fill", "encoding", "checked", "i", "allocUnsafeSlow", "string", "isEncoding", "actual", "write", "slice", "fromArrayLike", "array", "byteOffset", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "copy", "buffer", "isnan", "type", "data", "toString", "b", "_isBuffer", "compare", "a", "x", "y", "Math", "min", "String", "toLowerCase", "concat", "list", "pos", "buf", "<PERSON><PERSON><PERSON><PERSON>", "loweredCase", "utf8ToBytes", "base64ToBytes", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "n", "m", "swap16", "swap32", "swap64", "arguments", "apply", "equals", "inspect", "str", "max", "match", "join", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "bidirectionalIndexOf", "val", "dir", "isNaN", "arrayIndexOf", "indexOf", "call", "lastIndexOf", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "read", "readUInt16BE", "foundIndex", "found", "j", "includes", "hexWrite", "offset", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "asciiToBytes", "latin1Write", "base64Write", "ucs2Write", "utf16leToBytes", "isFinite", "toJSON", "Array", "_arr", "fromByteArray", "res", "firstByte", "codePoint", "bytesPerSequence", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "push", "decodeCodePointsArray", "MAX_ARGUMENTS_LENGTH", "codePoints", "fromCharCode", "ret", "out", "toHex", "bytes", "newBuf", "sliceLen", "checkOffset", "ext", "readUIntLE", "noAssert", "mul", "readUIntBE", "readUInt8", "readUInt16LE", "readUInt32LE", "readUInt32BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "checkInt", "writeUIntLE", "maxBytes", "writeUIntBE", "writeUInt8", "floor", "objectWriteUInt16", "littleEndian", "writeUInt16LE", "writeUInt16BE", "objectWriteUInt32", "writeUInt32LE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "checkIEEE754", "writeFloat", "writeFloatLE", "writeFloatBE", "writeDouble", "writeDoubleLE", "writeDoubleBE", "targetStart", "set", "code", "charCodeAt", "INVALID_BASE64_RE", "base64clean", "stringtrim", "replace", "trim", "units", "Infinity", "leadSurrogate", "byteArray", "c", "hi", "lo", "toByteArray", "src", "dst"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/buffer/index.js"], "sourcesContent": ["/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar isArray = require('isarray')\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined\n  ? global.TYPED_ARRAY_SUPPORT\n  : typedArraySupport()\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength()\n\nfunction typedArraySupport () {\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42 && // typed array instances can be augmented\n        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`\n  } catch (e) {\n    return false\n  }\n}\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length)\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length)\n    }\n    that.length = length\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype\n  return arr\n}\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    })\n  }\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n}\n\nfunction allocUnsafe (that, size) {\n  assertSize(size)\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n}\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0\n  that = createBuffer(that, length)\n\n  var actual = that.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual)\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  that = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array)\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset)\n  } else {\n    array = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array)\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    that = createBuffer(that, len)\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len)\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')\n    if (this.length > max) str += ' ... '\n  }\n  return '<Buffer ' + str + '>'\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i])\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 1] = (value >>> 8)\n    this[offset] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 3] = (value >>> 24)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString())\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;AACjC,IAAIC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIE,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAEhCG,OAAO,CAACC,MAAM,GAAGA,MAAM;AACvBD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,iBAAiB,GAAG,EAAE;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,MAAM,CAACG,mBAAmB,GAAGC,MAAM,CAACD,mBAAmB,KAAKE,SAAS,GACjED,MAAM,CAACD,mBAAmB,GAC1BG,iBAAiB,CAAC,CAAC;;AAEvB;AACA;AACA;AACAP,OAAO,CAACQ,UAAU,GAAGA,UAAU,CAAC,CAAC;AAEjC,SAASD,iBAAiBA,CAAA,EAAI;EAC5B,IAAI;IACF,IAAIE,GAAG,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC3BD,GAAG,CAACE,SAAS,GAAG;MAACA,SAAS,EAAED,UAAU,CAACE,SAAS;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;QAAE,OAAO,EAAE;MAAC;IAAC,CAAC;IACjF,OAAOJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAK,EAAE;IAAI;IACvB,OAAOJ,GAAG,CAACK,QAAQ,KAAK,UAAU;IAAI;IACtCL,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,UAAU,KAAK,CAAC,EAAC;EAC1C,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASR,UAAUA,CAAA,EAAI;EACrB,OAAOP,MAAM,CAACG,mBAAmB,GAC7B,UAAU,GACV,UAAU;AAChB;AAEA,SAASa,YAAYA,CAAEC,IAAI,EAAEC,MAAM,EAAE;EACnC,IAAIX,UAAU,CAAC,CAAC,GAAGW,MAAM,EAAE;IACzB,MAAM,IAAIC,UAAU,CAAC,4BAA4B,CAAC;EACpD;EACA,IAAInB,MAAM,CAACG,mBAAmB,EAAE;IAC9B;IACAc,IAAI,GAAG,IAAIR,UAAU,CAACS,MAAM,CAAC;IAC7BD,IAAI,CAACP,SAAS,GAAGV,MAAM,CAACW,SAAS;EACnC,CAAC,MAAM;IACL;IACA,IAAIM,IAAI,KAAK,IAAI,EAAE;MACjBA,IAAI,GAAG,IAAIjB,MAAM,CAACkB,MAAM,CAAC;IAC3B;IACAD,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;EAEA,OAAOD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASjB,MAAMA,CAAEoB,GAAG,EAAEC,gBAAgB,EAAEH,MAAM,EAAE;EAC9C,IAAI,CAAClB,MAAM,CAACG,mBAAmB,IAAI,EAAE,IAAI,YAAYH,MAAM,CAAC,EAAE;IAC5D,OAAO,IAAIA,MAAM,CAACoB,GAAG,EAAEC,gBAAgB,EAAEH,MAAM,CAAC;EAClD;;EAEA;EACA,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAOC,gBAAgB,KAAK,QAAQ,EAAE;MACxC,MAAM,IAAIC,KAAK,CACb,mEACF,CAAC;IACH;IACA,OAAOC,WAAW,CAAC,IAAI,EAAEH,GAAG,CAAC;EAC/B;EACA,OAAOI,IAAI,CAAC,IAAI,EAAEJ,GAAG,EAAEC,gBAAgB,EAAEH,MAAM,CAAC;AAClD;AAEAlB,MAAM,CAACyB,QAAQ,GAAG,IAAI,EAAC;;AAEvB;AACAzB,MAAM,CAAC0B,QAAQ,GAAG,UAAUlB,GAAG,EAAE;EAC/BA,GAAG,CAACE,SAAS,GAAGV,MAAM,CAACW,SAAS;EAChC,OAAOH,GAAG;AACZ,CAAC;AAED,SAASgB,IAAIA,CAAEP,IAAI,EAAEU,KAAK,EAAEN,gBAAgB,EAAEH,MAAM,EAAE;EACpD,IAAI,OAAOS,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIC,SAAS,CAAC,uCAAuC,CAAC;EAC9D;EAEA,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAIF,KAAK,YAAYE,WAAW,EAAE;IACtE,OAAOC,eAAe,CAACb,IAAI,EAAEU,KAAK,EAAEN,gBAAgB,EAAEH,MAAM,CAAC;EAC/D;EAEA,IAAI,OAAOS,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOI,UAAU,CAACd,IAAI,EAAEU,KAAK,EAAEN,gBAAgB,CAAC;EAClD;EAEA,OAAOW,UAAU,CAACf,IAAI,EAAEU,KAAK,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3B,MAAM,CAACwB,IAAI,GAAG,UAAUG,KAAK,EAAEN,gBAAgB,EAAEH,MAAM,EAAE;EACvD,OAAOM,IAAI,CAAC,IAAI,EAAEG,KAAK,EAAEN,gBAAgB,EAAEH,MAAM,CAAC;AACpD,CAAC;AAED,IAAIlB,MAAM,CAACG,mBAAmB,EAAE;EAC9BH,MAAM,CAACW,SAAS,CAACD,SAAS,GAAGD,UAAU,CAACE,SAAS;EACjDX,MAAM,CAACU,SAAS,GAAGD,UAAU;EAC7B,IAAI,OAAOwB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,IAC/ClC,MAAM,CAACiC,MAAM,CAACC,OAAO,CAAC,KAAKlC,MAAM,EAAE;IACrC;IACAmC,MAAM,CAACC,cAAc,CAACpC,MAAM,EAAEiC,MAAM,CAACC,OAAO,EAAE;MAC5CP,KAAK,EAAE,IAAI;MACXU,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;AACF;AAEA,SAASC,UAAUA,CAAEC,IAAI,EAAE;EACzB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIX,SAAS,CAAC,kCAAkC,CAAC;EACzD,CAAC,MAAM,IAAIW,IAAI,GAAG,CAAC,EAAE;IACnB,MAAM,IAAIpB,UAAU,CAAC,sCAAsC,CAAC;EAC9D;AACF;AAEA,SAASqB,KAAKA,CAAEvB,IAAI,EAAEsB,IAAI,EAAEE,IAAI,EAAEC,QAAQ,EAAE;EAC1CJ,UAAU,CAACC,IAAI,CAAC;EAChB,IAAIA,IAAI,IAAI,CAAC,EAAE;IACb,OAAOvB,YAAY,CAACC,IAAI,EAAEsB,IAAI,CAAC;EACjC;EACA,IAAIE,IAAI,KAAKpC,SAAS,EAAE;IACtB;IACA;IACA;IACA,OAAO,OAAOqC,QAAQ,KAAK,QAAQ,GAC/B1B,YAAY,CAACC,IAAI,EAAEsB,IAAI,CAAC,CAACE,IAAI,CAACA,IAAI,EAAEC,QAAQ,CAAC,GAC7C1B,YAAY,CAACC,IAAI,EAAEsB,IAAI,CAAC,CAACE,IAAI,CAACA,IAAI,CAAC;EACzC;EACA,OAAOzB,YAAY,CAACC,IAAI,EAAEsB,IAAI,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACAvC,MAAM,CAACwC,KAAK,GAAG,UAAUD,IAAI,EAAEE,IAAI,EAAEC,QAAQ,EAAE;EAC7C,OAAOF,KAAK,CAAC,IAAI,EAAED,IAAI,EAAEE,IAAI,EAAEC,QAAQ,CAAC;AAC1C,CAAC;AAED,SAASnB,WAAWA,CAAEN,IAAI,EAAEsB,IAAI,EAAE;EAChCD,UAAU,CAACC,IAAI,CAAC;EAChBtB,IAAI,GAAGD,YAAY,CAACC,IAAI,EAAEsB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;EAC3D,IAAI,CAACvC,MAAM,CAACG,mBAAmB,EAAE;IAC/B,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,EAAE,EAAEK,CAAC,EAAE;MAC7B3B,IAAI,CAAC2B,CAAC,CAAC,GAAG,CAAC;IACb;EACF;EACA,OAAO3B,IAAI;AACb;;AAEA;AACA;AACA;AACAjB,MAAM,CAACuB,WAAW,GAAG,UAAUgB,IAAI,EAAE;EACnC,OAAOhB,WAAW,CAAC,IAAI,EAAEgB,IAAI,CAAC;AAChC,CAAC;AACD;AACA;AACA;AACAvC,MAAM,CAAC6C,eAAe,GAAG,UAAUN,IAAI,EAAE;EACvC,OAAOhB,WAAW,CAAC,IAAI,EAAEgB,IAAI,CAAC;AAChC,CAAC;AAED,SAASR,UAAUA,CAAEd,IAAI,EAAE6B,MAAM,EAAEJ,QAAQ,EAAE;EAC3C,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACnDA,QAAQ,GAAG,MAAM;EACnB;EAEA,IAAI,CAAC1C,MAAM,CAAC+C,UAAU,CAACL,QAAQ,CAAC,EAAE;IAChC,MAAM,IAAId,SAAS,CAAC,4CAA4C,CAAC;EACnE;EAEA,IAAIV,MAAM,GAAGJ,UAAU,CAACgC,MAAM,EAAEJ,QAAQ,CAAC,GAAG,CAAC;EAC7CzB,IAAI,GAAGD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC;EAEjC,IAAI8B,MAAM,GAAG/B,IAAI,CAACgC,KAAK,CAACH,MAAM,EAAEJ,QAAQ,CAAC;EAEzC,IAAIM,MAAM,KAAK9B,MAAM,EAAE;IACrB;IACA;IACA;IACAD,IAAI,GAAGA,IAAI,CAACiC,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;EAC9B;EAEA,OAAO/B,IAAI;AACb;AAEA,SAASkC,aAAaA,CAAElC,IAAI,EAAEmC,KAAK,EAAE;EACnC,IAAIlC,MAAM,GAAGkC,KAAK,CAAClC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGyB,OAAO,CAACS,KAAK,CAAClC,MAAM,CAAC,GAAG,CAAC;EAC7DD,IAAI,GAAGD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC;EACjC,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,EAAE0B,CAAC,IAAI,CAAC,EAAE;IAClC3B,IAAI,CAAC2B,CAAC,CAAC,GAAGQ,KAAK,CAACR,CAAC,CAAC,GAAG,GAAG;EAC1B;EACA,OAAO3B,IAAI;AACb;AAEA,SAASa,eAAeA,CAAEb,IAAI,EAAEmC,KAAK,EAAEC,UAAU,EAAEnC,MAAM,EAAE;EACzDkC,KAAK,CAACtC,UAAU,EAAC;;EAEjB,IAAIuC,UAAU,GAAG,CAAC,IAAID,KAAK,CAACtC,UAAU,GAAGuC,UAAU,EAAE;IACnD,MAAM,IAAIlC,UAAU,CAAC,6BAA6B,CAAC;EACrD;EAEA,IAAIiC,KAAK,CAACtC,UAAU,GAAGuC,UAAU,IAAInC,MAAM,IAAI,CAAC,CAAC,EAAE;IACjD,MAAM,IAAIC,UAAU,CAAC,6BAA6B,CAAC;EACrD;EAEA,IAAIkC,UAAU,KAAKhD,SAAS,IAAIa,MAAM,KAAKb,SAAS,EAAE;IACpD+C,KAAK,GAAG,IAAI3C,UAAU,CAAC2C,KAAK,CAAC;EAC/B,CAAC,MAAM,IAAIlC,MAAM,KAAKb,SAAS,EAAE;IAC/B+C,KAAK,GAAG,IAAI3C,UAAU,CAAC2C,KAAK,EAAEC,UAAU,CAAC;EAC3C,CAAC,MAAM;IACLD,KAAK,GAAG,IAAI3C,UAAU,CAAC2C,KAAK,EAAEC,UAAU,EAAEnC,MAAM,CAAC;EACnD;EAEA,IAAIlB,MAAM,CAACG,mBAAmB,EAAE;IAC9B;IACAc,IAAI,GAAGmC,KAAK;IACZnC,IAAI,CAACP,SAAS,GAAGV,MAAM,CAACW,SAAS;EACnC,CAAC,MAAM;IACL;IACAM,IAAI,GAAGkC,aAAa,CAAClC,IAAI,EAAEmC,KAAK,CAAC;EACnC;EACA,OAAOnC,IAAI;AACb;AAEA,SAASe,UAAUA,CAAEf,IAAI,EAAEqC,GAAG,EAAE;EAC9B,IAAItD,MAAM,CAACuD,QAAQ,CAACD,GAAG,CAAC,EAAE;IACxB,IAAIE,GAAG,GAAGb,OAAO,CAACW,GAAG,CAACpC,MAAM,CAAC,GAAG,CAAC;IACjCD,IAAI,GAAGD,YAAY,CAACC,IAAI,EAAEuC,GAAG,CAAC;IAE9B,IAAIvC,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,OAAOD,IAAI;IACb;IAEAqC,GAAG,CAACG,IAAI,CAACxC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEuC,GAAG,CAAC;IACzB,OAAOvC,IAAI;EACb;EAEA,IAAIqC,GAAG,EAAE;IACP,IAAK,OAAOzB,WAAW,KAAK,WAAW,IACnCyB,GAAG,CAACI,MAAM,YAAY7B,WAAW,IAAK,QAAQ,IAAIyB,GAAG,EAAE;MACzD,IAAI,OAAOA,GAAG,CAACpC,MAAM,KAAK,QAAQ,IAAIyC,KAAK,CAACL,GAAG,CAACpC,MAAM,CAAC,EAAE;QACvD,OAAOF,YAAY,CAACC,IAAI,EAAE,CAAC,CAAC;MAC9B;MACA,OAAOkC,aAAa,CAAClC,IAAI,EAAEqC,GAAG,CAAC;IACjC;IAEA,IAAIA,GAAG,CAACM,IAAI,KAAK,QAAQ,IAAI9D,OAAO,CAACwD,GAAG,CAACO,IAAI,CAAC,EAAE;MAC9C,OAAOV,aAAa,CAAClC,IAAI,EAAEqC,GAAG,CAACO,IAAI,CAAC;IACtC;EACF;EAEA,MAAM,IAAIjC,SAAS,CAAC,oFAAoF,CAAC;AAC3G;AAEA,SAASe,OAAOA,CAAEzB,MAAM,EAAE;EACxB;EACA;EACA,IAAIA,MAAM,IAAIX,UAAU,CAAC,CAAC,EAAE;IAC1B,MAAM,IAAIY,UAAU,CAAC,iDAAiD,GACjD,UAAU,GAAGZ,UAAU,CAAC,CAAC,CAACuD,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;EACzE;EACA,OAAO5C,MAAM,GAAG,CAAC;AACnB;AAEA,SAASjB,UAAUA,CAAEiB,MAAM,EAAE;EAC3B,IAAI,CAACA,MAAM,IAAIA,MAAM,EAAE;IAAE;IACvBA,MAAM,GAAG,CAAC;EACZ;EACA,OAAOlB,MAAM,CAACwC,KAAK,CAAC,CAACtB,MAAM,CAAC;AAC9B;AAEAlB,MAAM,CAACuD,QAAQ,GAAG,SAASA,QAAQA,CAAEQ,CAAC,EAAE;EACtC,OAAO,CAAC,EAAEA,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACC,SAAS,CAAC;AACrC,CAAC;AAEDhE,MAAM,CAACiE,OAAO,GAAG,SAASA,OAAOA,CAAEC,CAAC,EAAEH,CAAC,EAAE;EACvC,IAAI,CAAC/D,MAAM,CAACuD,QAAQ,CAACW,CAAC,CAAC,IAAI,CAAClE,MAAM,CAACuD,QAAQ,CAACQ,CAAC,CAAC,EAAE;IAC9C,MAAM,IAAInC,SAAS,CAAC,2BAA2B,CAAC;EAClD;EAEA,IAAIsC,CAAC,KAAKH,CAAC,EAAE,OAAO,CAAC;EAErB,IAAII,CAAC,GAAGD,CAAC,CAAChD,MAAM;EAChB,IAAIkD,CAAC,GAAGL,CAAC,CAAC7C,MAAM;EAEhB,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEY,GAAG,GAAGa,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAExB,CAAC,GAAGY,GAAG,EAAE,EAAEZ,CAAC,EAAE;IAClD,IAAIsB,CAAC,CAACtB,CAAC,CAAC,KAAKmB,CAAC,CAACnB,CAAC,CAAC,EAAE;MACjBuB,CAAC,GAAGD,CAAC,CAACtB,CAAC,CAAC;MACRwB,CAAC,GAAGL,CAAC,CAACnB,CAAC,CAAC;MACR;IACF;EACF;EAEA,IAAIuB,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;EACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;EACnB,OAAO,CAAC;AACV,CAAC;AAEDnE,MAAM,CAAC+C,UAAU,GAAG,SAASA,UAAUA,CAAEL,QAAQ,EAAE;EACjD,QAAQ6B,MAAM,CAAC7B,QAAQ,CAAC,CAAC8B,WAAW,CAAC,CAAC;IACpC,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,SAAS;IACd,KAAK,UAAU;MACb,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF,CAAC;AAEDxE,MAAM,CAACyE,MAAM,GAAG,SAASA,MAAMA,CAAEC,IAAI,EAAExD,MAAM,EAAE;EAC7C,IAAI,CAACpB,OAAO,CAAC4E,IAAI,CAAC,EAAE;IAClB,MAAM,IAAI9C,SAAS,CAAC,6CAA6C,CAAC;EACpE;EAEA,IAAI8C,IAAI,CAACxD,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOlB,MAAM,CAACwC,KAAK,CAAC,CAAC,CAAC;EACxB;EAEA,IAAII,CAAC;EACL,IAAI1B,MAAM,KAAKb,SAAS,EAAE;IACxBa,MAAM,GAAG,CAAC;IACV,KAAK0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,IAAI,CAACxD,MAAM,EAAE,EAAE0B,CAAC,EAAE;MAChC1B,MAAM,IAAIwD,IAAI,CAAC9B,CAAC,CAAC,CAAC1B,MAAM;IAC1B;EACF;EAEA,IAAIwC,MAAM,GAAG1D,MAAM,CAACuB,WAAW,CAACL,MAAM,CAAC;EACvC,IAAIyD,GAAG,GAAG,CAAC;EACX,KAAK/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,IAAI,CAACxD,MAAM,EAAE,EAAE0B,CAAC,EAAE;IAChC,IAAIgC,GAAG,GAAGF,IAAI,CAAC9B,CAAC,CAAC;IACjB,IAAI,CAAC5C,MAAM,CAACuD,QAAQ,CAACqB,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIhD,SAAS,CAAC,6CAA6C,CAAC;IACpE;IACAgD,GAAG,CAACnB,IAAI,CAACC,MAAM,EAAEiB,GAAG,CAAC;IACrBA,GAAG,IAAIC,GAAG,CAAC1D,MAAM;EACnB;EACA,OAAOwC,MAAM;AACf,CAAC;AAED,SAAS5C,UAAUA,CAAEgC,MAAM,EAAEJ,QAAQ,EAAE;EACrC,IAAI1C,MAAM,CAACuD,QAAQ,CAACT,MAAM,CAAC,EAAE;IAC3B,OAAOA,MAAM,CAAC5B,MAAM;EACtB;EACA,IAAI,OAAOW,WAAW,KAAK,WAAW,IAAI,OAAOA,WAAW,CAACgD,MAAM,KAAK,UAAU,KAC7EhD,WAAW,CAACgD,MAAM,CAAC/B,MAAM,CAAC,IAAIA,MAAM,YAAYjB,WAAW,CAAC,EAAE;IACjE,OAAOiB,MAAM,CAAChC,UAAU;EAC1B;EACA,IAAI,OAAOgC,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAG,EAAE,GAAGA,MAAM;EACtB;EAEA,IAAIU,GAAG,GAAGV,MAAM,CAAC5B,MAAM;EACvB,IAAIsC,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;EAEvB;EACA,IAAIsB,WAAW,GAAG,KAAK;EACvB,SAAS;IACP,QAAQpC,QAAQ;MACd,KAAK,OAAO;MACZ,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOc,GAAG;MACZ,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAKnD,SAAS;QACZ,OAAO0E,WAAW,CAACjC,MAAM,CAAC,CAAC5B,MAAM;MACnC,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAOsC,GAAG,GAAG,CAAC;MAChB,KAAK,KAAK;QACR,OAAOA,GAAG,KAAK,CAAC;MAClB,KAAK,QAAQ;QACX,OAAOwB,aAAa,CAAClC,MAAM,CAAC,CAAC5B,MAAM;MACrC;QACE,IAAI4D,WAAW,EAAE,OAAOC,WAAW,CAACjC,MAAM,CAAC,CAAC5B,MAAM,EAAC;QACnDwB,QAAQ,GAAG,CAAC,EAAE,GAAGA,QAAQ,EAAE8B,WAAW,CAAC,CAAC;QACxCM,WAAW,GAAG,IAAI;IACtB;EACF;AACF;AACA9E,MAAM,CAACc,UAAU,GAAGA,UAAU;AAE9B,SAASmE,YAAYA,CAAEvC,QAAQ,EAAEwC,KAAK,EAAEC,GAAG,EAAE;EAC3C,IAAIL,WAAW,GAAG,KAAK;;EAEvB;EACA;;EAEA;EACA;EACA;EACA;EACA,IAAII,KAAK,KAAK7E,SAAS,IAAI6E,KAAK,GAAG,CAAC,EAAE;IACpCA,KAAK,GAAG,CAAC;EACX;EACA;EACA;EACA,IAAIA,KAAK,GAAG,IAAI,CAAChE,MAAM,EAAE;IACvB,OAAO,EAAE;EACX;EAEA,IAAIiE,GAAG,KAAK9E,SAAS,IAAI8E,GAAG,GAAG,IAAI,CAACjE,MAAM,EAAE;IAC1CiE,GAAG,GAAG,IAAI,CAACjE,MAAM;EACnB;EAEA,IAAIiE,GAAG,IAAI,CAAC,EAAE;IACZ,OAAO,EAAE;EACX;;EAEA;EACAA,GAAG,MAAM,CAAC;EACVD,KAAK,MAAM,CAAC;EAEZ,IAAIC,GAAG,IAAID,KAAK,EAAE;IAChB,OAAO,EAAE;EACX;EAEA,IAAI,CAACxC,QAAQ,EAAEA,QAAQ,GAAG,MAAM;EAEhC,OAAO,IAAI,EAAE;IACX,QAAQA,QAAQ;MACd,KAAK,KAAK;QACR,OAAO0C,QAAQ,CAAC,IAAI,EAAEF,KAAK,EAAEC,GAAG,CAAC;MAEnC,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAOE,SAAS,CAAC,IAAI,EAAEH,KAAK,EAAEC,GAAG,CAAC;MAEpC,KAAK,OAAO;QACV,OAAOG,UAAU,CAAC,IAAI,EAAEJ,KAAK,EAAEC,GAAG,CAAC;MAErC,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOI,WAAW,CAAC,IAAI,EAAEL,KAAK,EAAEC,GAAG,CAAC;MAEtC,KAAK,QAAQ;QACX,OAAOK,WAAW,CAAC,IAAI,EAAEN,KAAK,EAAEC,GAAG,CAAC;MAEtC,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAOM,YAAY,CAAC,IAAI,EAAEP,KAAK,EAAEC,GAAG,CAAC;MAEvC;QACE,IAAIL,WAAW,EAAE,MAAM,IAAIlD,SAAS,CAAC,oBAAoB,GAAGc,QAAQ,CAAC;QACrEA,QAAQ,GAAG,CAACA,QAAQ,GAAG,EAAE,EAAE8B,WAAW,CAAC,CAAC;QACxCM,WAAW,GAAG,IAAI;IACtB;EACF;AACF;;AAEA;AACA;AACA9E,MAAM,CAACW,SAAS,CAACqD,SAAS,GAAG,IAAI;AAEjC,SAAS0B,IAAIA,CAAE3B,CAAC,EAAE4B,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIhD,CAAC,GAAGmB,CAAC,CAAC4B,CAAC,CAAC;EACZ5B,CAAC,CAAC4B,CAAC,CAAC,GAAG5B,CAAC,CAAC6B,CAAC,CAAC;EACX7B,CAAC,CAAC6B,CAAC,CAAC,GAAGhD,CAAC;AACV;AAEA5C,MAAM,CAACW,SAAS,CAACkF,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,IAAIrC,GAAG,GAAG,IAAI,CAACtC,MAAM;EACrB,IAAIsC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIrC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,IAAI,CAAC,EAAE;IAC/B8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EACtB;EACA,OAAO,IAAI;AACb,CAAC;AAED5C,MAAM,CAACW,SAAS,CAACmF,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,IAAItC,GAAG,GAAG,IAAI,CAACtC,MAAM;EACrB,IAAIsC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIrC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,IAAI,CAAC,EAAE;IAC/B8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACpB8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,OAAO,IAAI;AACb,CAAC;AAED5C,MAAM,CAACW,SAAS,CAACoF,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,IAAIvC,GAAG,GAAG,IAAI,CAACtC,MAAM;EACrB,IAAIsC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIrC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,IAAI,CAAC,EAAE;IAC/B8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACpB8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACxB8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACxB8C,IAAI,CAAC,IAAI,EAAE9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,OAAO,IAAI;AACb,CAAC;AAED5C,MAAM,CAACW,SAAS,CAACmD,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;EAC/C,IAAI5C,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC;EAC5B,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAC3B,IAAI8E,SAAS,CAAC9E,MAAM,KAAK,CAAC,EAAE,OAAOmE,SAAS,CAAC,IAAI,EAAE,CAAC,EAAEnE,MAAM,CAAC;EAC7D,OAAO+D,YAAY,CAACgB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAC5C,CAAC;AAEDhG,MAAM,CAACW,SAAS,CAACuF,MAAM,GAAG,SAASA,MAAMA,CAAEnC,CAAC,EAAE;EAC5C,IAAI,CAAC/D,MAAM,CAACuD,QAAQ,CAACQ,CAAC,CAAC,EAAE,MAAM,IAAInC,SAAS,CAAC,2BAA2B,CAAC;EACzE,IAAI,IAAI,KAAKmC,CAAC,EAAE,OAAO,IAAI;EAC3B,OAAO/D,MAAM,CAACiE,OAAO,CAAC,IAAI,EAAEF,CAAC,CAAC,KAAK,CAAC;AACtC,CAAC;AAED/D,MAAM,CAACW,SAAS,CAACwF,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EAC7C,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAGtG,OAAO,CAACG,iBAAiB;EACnC,IAAI,IAAI,CAACgB,MAAM,GAAG,CAAC,EAAE;IACnBkF,GAAG,GAAG,IAAI,CAACtC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAEuC,GAAG,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC3D,IAAI,IAAI,CAACrF,MAAM,GAAGmF,GAAG,EAAED,GAAG,IAAI,OAAO;EACvC;EACA,OAAO,UAAU,GAAGA,GAAG,GAAG,GAAG;AAC/B,CAAC;AAEDpG,MAAM,CAACW,SAAS,CAACsD,OAAO,GAAG,SAASA,OAAOA,CAAEuC,MAAM,EAAEtB,KAAK,EAAEC,GAAG,EAAEsB,SAAS,EAAEC,OAAO,EAAE;EACnF,IAAI,CAAC1G,MAAM,CAACuD,QAAQ,CAACiD,MAAM,CAAC,EAAE;IAC5B,MAAM,IAAI5E,SAAS,CAAC,2BAA2B,CAAC;EAClD;EAEA,IAAIsD,KAAK,KAAK7E,SAAS,EAAE;IACvB6E,KAAK,GAAG,CAAC;EACX;EACA,IAAIC,GAAG,KAAK9E,SAAS,EAAE;IACrB8E,GAAG,GAAGqB,MAAM,GAAGA,MAAM,CAACtF,MAAM,GAAG,CAAC;EAClC;EACA,IAAIuF,SAAS,KAAKpG,SAAS,EAAE;IAC3BoG,SAAS,GAAG,CAAC;EACf;EACA,IAAIC,OAAO,KAAKrG,SAAS,EAAE;IACzBqG,OAAO,GAAG,IAAI,CAACxF,MAAM;EACvB;EAEA,IAAIgE,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAGqB,MAAM,CAACtF,MAAM,IAAIuF,SAAS,GAAG,CAAC,IAAIC,OAAO,GAAG,IAAI,CAACxF,MAAM,EAAE;IAC9E,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,IAAIsF,SAAS,IAAIC,OAAO,IAAIxB,KAAK,IAAIC,GAAG,EAAE;IACxC,OAAO,CAAC;EACV;EACA,IAAIsB,SAAS,IAAIC,OAAO,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,IAAIxB,KAAK,IAAIC,GAAG,EAAE;IAChB,OAAO,CAAC;EACV;EAEAD,KAAK,MAAM,CAAC;EACZC,GAAG,MAAM,CAAC;EACVsB,SAAS,MAAM,CAAC;EAChBC,OAAO,MAAM,CAAC;EAEd,IAAI,IAAI,KAAKF,MAAM,EAAE,OAAO,CAAC;EAE7B,IAAIrC,CAAC,GAAGuC,OAAO,GAAGD,SAAS;EAC3B,IAAIrC,CAAC,GAAGe,GAAG,GAAGD,KAAK;EACnB,IAAI1B,GAAG,GAAGa,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC;EAExB,IAAIuC,QAAQ,GAAG,IAAI,CAACzD,KAAK,CAACuD,SAAS,EAAEC,OAAO,CAAC;EAC7C,IAAIE,UAAU,GAAGJ,MAAM,CAACtD,KAAK,CAACgC,KAAK,EAAEC,GAAG,CAAC;EAEzC,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAE,EAAEZ,CAAC,EAAE;IAC5B,IAAI+D,QAAQ,CAAC/D,CAAC,CAAC,KAAKgE,UAAU,CAAChE,CAAC,CAAC,EAAE;MACjCuB,CAAC,GAAGwC,QAAQ,CAAC/D,CAAC,CAAC;MACfwB,CAAC,GAAGwC,UAAU,CAAChE,CAAC,CAAC;MACjB;IACF;EACF;EAEA,IAAIuB,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;EACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;EACnB,OAAO,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,oBAAoBA,CAAEnD,MAAM,EAAEoD,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAEqE,GAAG,EAAE;EACrE;EACA,IAAIrD,MAAM,CAACxC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;;EAElC;EACA,IAAI,OAAOmC,UAAU,KAAK,QAAQ,EAAE;IAClCX,QAAQ,GAAGW,UAAU;IACrBA,UAAU,GAAG,CAAC;EAChB,CAAC,MAAM,IAAIA,UAAU,GAAG,UAAU,EAAE;IAClCA,UAAU,GAAG,UAAU;EACzB,CAAC,MAAM,IAAIA,UAAU,GAAG,CAAC,UAAU,EAAE;IACnCA,UAAU,GAAG,CAAC,UAAU;EAC1B;EACAA,UAAU,GAAG,CAACA,UAAU,EAAE;EAC1B,IAAI2D,KAAK,CAAC3D,UAAU,CAAC,EAAE;IACrB;IACAA,UAAU,GAAG0D,GAAG,GAAG,CAAC,GAAIrD,MAAM,CAACxC,MAAM,GAAG,CAAE;EAC5C;;EAEA;EACA,IAAImC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGK,MAAM,CAACxC,MAAM,GAAGmC,UAAU;EAC3D,IAAIA,UAAU,IAAIK,MAAM,CAACxC,MAAM,EAAE;IAC/B,IAAI6F,GAAG,EAAE,OAAO,CAAC,CAAC,MACb1D,UAAU,GAAGK,MAAM,CAACxC,MAAM,GAAG,CAAC;EACrC,CAAC,MAAM,IAAImC,UAAU,GAAG,CAAC,EAAE;IACzB,IAAI0D,GAAG,EAAE1D,UAAU,GAAG,CAAC,MAClB,OAAO,CAAC,CAAC;EAChB;;EAEA;EACA,IAAI,OAAOyD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG9G,MAAM,CAACwB,IAAI,CAACsF,GAAG,EAAEpE,QAAQ,CAAC;EAClC;;EAEA;EACA,IAAI1C,MAAM,CAACuD,QAAQ,CAACuD,GAAG,CAAC,EAAE;IACxB;IACA,IAAIA,GAAG,CAAC5F,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC;IACX;IACA,OAAO+F,YAAY,CAACvD,MAAM,EAAEoD,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAEqE,GAAG,CAAC;EAC7D,CAAC,MAAM,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAClCA,GAAG,GAAGA,GAAG,GAAG,IAAI,EAAC;IACjB,IAAI9G,MAAM,CAACG,mBAAmB,IAC1B,OAAOM,UAAU,CAACE,SAAS,CAACuG,OAAO,KAAK,UAAU,EAAE;MACtD,IAAIH,GAAG,EAAE;QACP,OAAOtG,UAAU,CAACE,SAAS,CAACuG,OAAO,CAACC,IAAI,CAACzD,MAAM,EAAEoD,GAAG,EAAEzD,UAAU,CAAC;MACnE,CAAC,MAAM;QACL,OAAO5C,UAAU,CAACE,SAAS,CAACyG,WAAW,CAACD,IAAI,CAACzD,MAAM,EAAEoD,GAAG,EAAEzD,UAAU,CAAC;MACvE;IACF;IACA,OAAO4D,YAAY,CAACvD,MAAM,EAAE,CAAEoD,GAAG,CAAE,EAAEzD,UAAU,EAAEX,QAAQ,EAAEqE,GAAG,CAAC;EACjE;EAEA,MAAM,IAAInF,SAAS,CAAC,sCAAsC,CAAC;AAC7D;AAEA,SAASqF,YAAYA,CAAEzG,GAAG,EAAEsG,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAEqE,GAAG,EAAE;EAC1D,IAAIM,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG9G,GAAG,CAACU,MAAM;EAC1B,IAAIqG,SAAS,GAAGT,GAAG,CAAC5F,MAAM;EAE1B,IAAIwB,QAAQ,KAAKrC,SAAS,EAAE;IAC1BqC,QAAQ,GAAG6B,MAAM,CAAC7B,QAAQ,CAAC,CAAC8B,WAAW,CAAC,CAAC;IACzC,IAAI9B,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,IAC3CA,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,UAAU,EAAE;MACrD,IAAIlC,GAAG,CAACU,MAAM,GAAG,CAAC,IAAI4F,GAAG,CAAC5F,MAAM,GAAG,CAAC,EAAE;QACpC,OAAO,CAAC,CAAC;MACX;MACAmG,SAAS,GAAG,CAAC;MACbC,SAAS,IAAI,CAAC;MACdC,SAAS,IAAI,CAAC;MACdlE,UAAU,IAAI,CAAC;IACjB;EACF;EAEA,SAASmE,IAAIA,CAAE5C,GAAG,EAAEhC,CAAC,EAAE;IACrB,IAAIyE,SAAS,KAAK,CAAC,EAAE;MACnB,OAAOzC,GAAG,CAAChC,CAAC,CAAC;IACf,CAAC,MAAM;MACL,OAAOgC,GAAG,CAAC6C,YAAY,CAAC7E,CAAC,GAAGyE,SAAS,CAAC;IACxC;EACF;EAEA,IAAIzE,CAAC;EACL,IAAImE,GAAG,EAAE;IACP,IAAIW,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK9E,CAAC,GAAGS,UAAU,EAAET,CAAC,GAAG0E,SAAS,EAAE1E,CAAC,EAAE,EAAE;MACvC,IAAI4E,IAAI,CAAChH,GAAG,EAAEoC,CAAC,CAAC,KAAK4E,IAAI,CAACV,GAAG,EAAEY,UAAU,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG9E,CAAC,GAAG8E,UAAU,CAAC,EAAE;QACtE,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAEA,UAAU,GAAG9E,CAAC;QACrC,IAAIA,CAAC,GAAG8E,UAAU,GAAG,CAAC,KAAKH,SAAS,EAAE,OAAOG,UAAU,GAAGL,SAAS;MACrE,CAAC,MAAM;QACL,IAAIK,UAAU,KAAK,CAAC,CAAC,EAAE9E,CAAC,IAAIA,CAAC,GAAG8E,UAAU;QAC1CA,UAAU,GAAG,CAAC,CAAC;MACjB;IACF;EACF,CAAC,MAAM;IACL,IAAIrE,UAAU,GAAGkE,SAAS,GAAGD,SAAS,EAAEjE,UAAU,GAAGiE,SAAS,GAAGC,SAAS;IAC1E,KAAK3E,CAAC,GAAGS,UAAU,EAAET,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChC,IAAI+E,KAAK,GAAG,IAAI;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;QAClC,IAAIJ,IAAI,CAAChH,GAAG,EAAEoC,CAAC,GAAGgF,CAAC,CAAC,KAAKJ,IAAI,CAACV,GAAG,EAAEc,CAAC,CAAC,EAAE;UACrCD,KAAK,GAAG,KAAK;UACb;QACF;MACF;MACA,IAAIA,KAAK,EAAE,OAAO/E,CAAC;IACrB;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA5C,MAAM,CAACW,SAAS,CAACkH,QAAQ,GAAG,SAASA,QAAQA,CAAEf,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAE;EACxE,OAAO,IAAI,CAACwE,OAAO,CAACJ,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,CAAC;AAED1C,MAAM,CAACW,SAAS,CAACuG,OAAO,GAAG,SAASA,OAAOA,CAAEJ,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAE;EACtE,OAAOmE,oBAAoB,CAAC,IAAI,EAAEC,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAE,IAAI,CAAC;AACpE,CAAC;AAED1C,MAAM,CAACW,SAAS,CAACyG,WAAW,GAAG,SAASA,WAAWA,CAAEN,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAE;EAC9E,OAAOmE,oBAAoB,CAAC,IAAI,EAAEC,GAAG,EAAEzD,UAAU,EAAEX,QAAQ,EAAE,KAAK,CAAC;AACrE,CAAC;AAED,SAASoF,QAAQA,CAAElD,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EAC9C6G,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;EAC5B,IAAIE,SAAS,GAAGrD,GAAG,CAAC1D,MAAM,GAAG6G,MAAM;EACnC,IAAI,CAAC7G,MAAM,EAAE;IACXA,MAAM,GAAG+G,SAAS;EACpB,CAAC,MAAM;IACL/G,MAAM,GAAG8G,MAAM,CAAC9G,MAAM,CAAC;IACvB,IAAIA,MAAM,GAAG+G,SAAS,EAAE;MACtB/G,MAAM,GAAG+G,SAAS;IACpB;EACF;;EAEA;EACA,IAAIC,MAAM,GAAGpF,MAAM,CAAC5B,MAAM;EAC1B,IAAIgH,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,IAAItG,SAAS,CAAC,oBAAoB,CAAC;EAE/D,IAAIV,MAAM,GAAGgH,MAAM,GAAG,CAAC,EAAE;IACvBhH,MAAM,GAAGgH,MAAM,GAAG,CAAC;EACrB;EACA,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,EAAE,EAAE0B,CAAC,EAAE;IAC/B,IAAIuF,MAAM,GAAGC,QAAQ,CAACtF,MAAM,CAACuF,MAAM,CAACzF,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAClD,IAAIoE,KAAK,CAACmB,MAAM,CAAC,EAAE,OAAOvF,CAAC;IAC3BgC,GAAG,CAACmD,MAAM,GAAGnF,CAAC,CAAC,GAAGuF,MAAM;EAC1B;EACA,OAAOvF,CAAC;AACV;AAEA,SAAS0F,SAASA,CAAE1D,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EAC/C,OAAOqH,UAAU,CAACxD,WAAW,CAACjC,MAAM,EAAE8B,GAAG,CAAC1D,MAAM,GAAG6G,MAAM,CAAC,EAAEnD,GAAG,EAAEmD,MAAM,EAAE7G,MAAM,CAAC;AAClF;AAEA,SAASsH,UAAUA,CAAE5D,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EAChD,OAAOqH,UAAU,CAACE,YAAY,CAAC3F,MAAM,CAAC,EAAE8B,GAAG,EAAEmD,MAAM,EAAE7G,MAAM,CAAC;AAC9D;AAEA,SAASwH,WAAWA,CAAE9D,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EACjD,OAAOsH,UAAU,CAAC5D,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;AAChD;AAEA,SAASyH,WAAWA,CAAE/D,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EACjD,OAAOqH,UAAU,CAACvD,aAAa,CAAClC,MAAM,CAAC,EAAE8B,GAAG,EAAEmD,MAAM,EAAE7G,MAAM,CAAC;AAC/D;AAEA,SAAS0H,SAASA,CAAEhE,GAAG,EAAE9B,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAE;EAC/C,OAAOqH,UAAU,CAACM,cAAc,CAAC/F,MAAM,EAAE8B,GAAG,CAAC1D,MAAM,GAAG6G,MAAM,CAAC,EAAEnD,GAAG,EAAEmD,MAAM,EAAE7G,MAAM,CAAC;AACrF;AAEAlB,MAAM,CAACW,SAAS,CAACsC,KAAK,GAAG,SAASA,KAAKA,CAAEH,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,EAAEwB,QAAQ,EAAE;EACzE;EACA,IAAIqF,MAAM,KAAK1H,SAAS,EAAE;IACxBqC,QAAQ,GAAG,MAAM;IACjBxB,MAAM,GAAG,IAAI,CAACA,MAAM;IACpB6G,MAAM,GAAG,CAAC;IACZ;EACA,CAAC,MAAM,IAAI7G,MAAM,KAAKb,SAAS,IAAI,OAAO0H,MAAM,KAAK,QAAQ,EAAE;IAC7DrF,QAAQ,GAAGqF,MAAM;IACjB7G,MAAM,GAAG,IAAI,CAACA,MAAM;IACpB6G,MAAM,GAAG,CAAC;IACZ;EACA,CAAC,MAAM,IAAIe,QAAQ,CAACf,MAAM,CAAC,EAAE;IAC3BA,MAAM,GAAGA,MAAM,GAAG,CAAC;IACnB,IAAIe,QAAQ,CAAC5H,MAAM,CAAC,EAAE;MACpBA,MAAM,GAAGA,MAAM,GAAG,CAAC;MACnB,IAAIwB,QAAQ,KAAKrC,SAAS,EAAEqC,QAAQ,GAAG,MAAM;IAC/C,CAAC,MAAM;MACLA,QAAQ,GAAGxB,MAAM;MACjBA,MAAM,GAAGb,SAAS;IACpB;IACF;EACA,CAAC,MAAM;IACL,MAAM,IAAIiB,KAAK,CACb,yEACF,CAAC;EACH;EAEA,IAAI2G,SAAS,GAAG,IAAI,CAAC/G,MAAM,GAAG6G,MAAM;EACpC,IAAI7G,MAAM,KAAKb,SAAS,IAAIa,MAAM,GAAG+G,SAAS,EAAE/G,MAAM,GAAG+G,SAAS;EAElE,IAAKnF,MAAM,CAAC5B,MAAM,GAAG,CAAC,KAAKA,MAAM,GAAG,CAAC,IAAI6G,MAAM,GAAG,CAAC,CAAC,IAAKA,MAAM,GAAG,IAAI,CAAC7G,MAAM,EAAE;IAC7E,MAAM,IAAIC,UAAU,CAAC,wCAAwC,CAAC;EAChE;EAEA,IAAI,CAACuB,QAAQ,EAAEA,QAAQ,GAAG,MAAM;EAEhC,IAAIoC,WAAW,GAAG,KAAK;EACvB,SAAS;IACP,QAAQpC,QAAQ;MACd,KAAK,KAAK;QACR,OAAOoF,QAAQ,CAAC,IAAI,EAAEhF,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAE/C,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAOoH,SAAS,CAAC,IAAI,EAAExF,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAEhD,KAAK,OAAO;QACV,OAAOsH,UAAU,CAAC,IAAI,EAAE1F,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAEjD,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOwH,WAAW,CAAC,IAAI,EAAE5F,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAElD,KAAK,QAAQ;QACX;QACA,OAAOyH,WAAW,CAAC,IAAI,EAAE7F,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAElD,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAO0H,SAAS,CAAC,IAAI,EAAE9F,MAAM,EAAEiF,MAAM,EAAE7G,MAAM,CAAC;MAEhD;QACE,IAAI4D,WAAW,EAAE,MAAM,IAAIlD,SAAS,CAAC,oBAAoB,GAAGc,QAAQ,CAAC;QACrEA,QAAQ,GAAG,CAAC,EAAE,GAAGA,QAAQ,EAAE8B,WAAW,CAAC,CAAC;QACxCM,WAAW,GAAG,IAAI;IACtB;EACF;AACF,CAAC;AAED9E,MAAM,CAACW,SAAS,CAACoI,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,OAAO;IACLnF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAEmF,KAAK,CAACrI,SAAS,CAACuC,KAAK,CAACiE,IAAI,CAAC,IAAI,CAAC8B,IAAI,IAAI,IAAI,EAAE,CAAC;EACvD,CAAC;AACH,CAAC;AAED,SAASzD,WAAWA,CAAEZ,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAID,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAKP,GAAG,CAAC1D,MAAM,EAAE;IACrC,OAAOvB,MAAM,CAACuJ,aAAa,CAACtE,GAAG,CAAC;EAClC,CAAC,MAAM;IACL,OAAOjF,MAAM,CAACuJ,aAAa,CAACtE,GAAG,CAAC1B,KAAK,CAACgC,KAAK,EAAEC,GAAG,CAAC,CAAC;EACpD;AACF;AAEA,SAASE,SAASA,CAAET,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EACnCA,GAAG,GAAGd,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC1D,MAAM,EAAEiE,GAAG,CAAC;EAC/B,IAAIgE,GAAG,GAAG,EAAE;EAEZ,IAAIvG,CAAC,GAAGsC,KAAK;EACb,OAAOtC,CAAC,GAAGuC,GAAG,EAAE;IACd,IAAIiE,SAAS,GAAGxE,GAAG,CAAChC,CAAC,CAAC;IACtB,IAAIyG,SAAS,GAAG,IAAI;IACpB,IAAIC,gBAAgB,GAAIF,SAAS,GAAG,IAAI,GAAI,CAAC,GACxCA,SAAS,GAAG,IAAI,GAAI,CAAC,GACrBA,SAAS,GAAG,IAAI,GAAI,CAAC,GACtB,CAAC;IAEL,IAAIxG,CAAC,GAAG0G,gBAAgB,IAAInE,GAAG,EAAE;MAC/B,IAAIoE,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa;MAEpD,QAAQJ,gBAAgB;QACtB,KAAK,CAAC;UACJ,IAAIF,SAAS,GAAG,IAAI,EAAE;YACpBC,SAAS,GAAGD,SAAS;UACvB;UACA;QACF,KAAK,CAAC;UACJG,UAAU,GAAG3E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACvB,IAAI,CAAC2G,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;YAChCG,aAAa,GAAG,CAACN,SAAS,GAAG,IAAI,KAAK,GAAG,GAAIG,UAAU,GAAG,IAAK;YAC/D,IAAIG,aAAa,GAAG,IAAI,EAAE;cACxBL,SAAS,GAAGK,aAAa;YAC3B;UACF;UACA;QACF,KAAK,CAAC;UACJH,UAAU,GAAG3E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACvB4G,SAAS,GAAG5E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACtB,IAAI,CAAC2G,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE;YAC/DE,aAAa,GAAG,CAACN,SAAS,GAAG,GAAG,KAAK,GAAG,GAAG,CAACG,UAAU,GAAG,IAAI,KAAK,GAAG,GAAIC,SAAS,GAAG,IAAK;YAC1F,IAAIE,aAAa,GAAG,KAAK,KAAKA,aAAa,GAAG,MAAM,IAAIA,aAAa,GAAG,MAAM,CAAC,EAAE;cAC/EL,SAAS,GAAGK,aAAa;YAC3B;UACF;UACA;QACF,KAAK,CAAC;UACJH,UAAU,GAAG3E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACvB4G,SAAS,GAAG5E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACtB6G,UAAU,GAAG7E,GAAG,CAAChC,CAAC,GAAG,CAAC,CAAC;UACvB,IAAI,CAAC2G,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,SAAS,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;YAC/FC,aAAa,GAAG,CAACN,SAAS,GAAG,GAAG,KAAK,IAAI,GAAG,CAACG,UAAU,GAAG,IAAI,KAAK,GAAG,GAAG,CAACC,SAAS,GAAG,IAAI,KAAK,GAAG,GAAIC,UAAU,GAAG,IAAK;YACxH,IAAIC,aAAa,GAAG,MAAM,IAAIA,aAAa,GAAG,QAAQ,EAAE;cACtDL,SAAS,GAAGK,aAAa;YAC3B;UACF;MACJ;IACF;IAEA,IAAIL,SAAS,KAAK,IAAI,EAAE;MACtB;MACA;MACAA,SAAS,GAAG,MAAM;MAClBC,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAID,SAAS,GAAG,MAAM,EAAE;MAC7B;MACAA,SAAS,IAAI,OAAO;MACpBF,GAAG,CAACQ,IAAI,CAACN,SAAS,KAAK,EAAE,GAAG,KAAK,GAAG,MAAM,CAAC;MAC3CA,SAAS,GAAG,MAAM,GAAGA,SAAS,GAAG,KAAK;IACxC;IAEAF,GAAG,CAACQ,IAAI,CAACN,SAAS,CAAC;IACnBzG,CAAC,IAAI0G,gBAAgB;EACvB;EAEA,OAAOM,qBAAqB,CAACT,GAAG,CAAC;AACnC;;AAEA;AACA;AACA;AACA,IAAIU,oBAAoB,GAAG,MAAM;AAEjC,SAASD,qBAAqBA,CAAEE,UAAU,EAAE;EAC1C,IAAItG,GAAG,GAAGsG,UAAU,CAAC5I,MAAM;EAC3B,IAAIsC,GAAG,IAAIqG,oBAAoB,EAAE;IAC/B,OAAOtF,MAAM,CAACwF,YAAY,CAAC9D,KAAK,CAAC1B,MAAM,EAAEuF,UAAU,CAAC,EAAC;EACvD;;EAEA;EACA,IAAIX,GAAG,GAAG,EAAE;EACZ,IAAIvG,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGY,GAAG,EAAE;IACd2F,GAAG,IAAI5E,MAAM,CAACwF,YAAY,CAAC9D,KAAK,CAC9B1B,MAAM,EACNuF,UAAU,CAAC5G,KAAK,CAACN,CAAC,EAAEA,CAAC,IAAIiH,oBAAoB,CAC/C,CAAC;EACH;EACA,OAAOV,GAAG;AACZ;AAEA,SAAS7D,UAAUA,CAAEV,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAI6E,GAAG,GAAG,EAAE;EACZ7E,GAAG,GAAGd,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC1D,MAAM,EAAEiE,GAAG,CAAC;EAE/B,KAAK,IAAIvC,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,GAAGuC,GAAG,EAAE,EAAEvC,CAAC,EAAE;IAChCoH,GAAG,IAAIzF,MAAM,CAACwF,YAAY,CAACnF,GAAG,CAAChC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC3C;EACA,OAAOoH,GAAG;AACZ;AAEA,SAASzE,WAAWA,CAAEX,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAI6E,GAAG,GAAG,EAAE;EACZ7E,GAAG,GAAGd,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC1D,MAAM,EAAEiE,GAAG,CAAC;EAE/B,KAAK,IAAIvC,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,GAAGuC,GAAG,EAAE,EAAEvC,CAAC,EAAE;IAChCoH,GAAG,IAAIzF,MAAM,CAACwF,YAAY,CAACnF,GAAG,CAAChC,CAAC,CAAC,CAAC;EACpC;EACA,OAAOoH,GAAG;AACZ;AAEA,SAAS5E,QAAQA,CAAER,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAI3B,GAAG,GAAGoB,GAAG,CAAC1D,MAAM;EAEpB,IAAI,CAACgE,KAAK,IAAIA,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC;EAClC,IAAI,CAACC,GAAG,IAAIA,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG3B,GAAG,EAAE2B,GAAG,GAAG3B,GAAG;EAE3C,IAAIyG,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIrH,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,GAAGuC,GAAG,EAAE,EAAEvC,CAAC,EAAE;IAChCqH,GAAG,IAAIC,KAAK,CAACtF,GAAG,CAAChC,CAAC,CAAC,CAAC;EACtB;EACA,OAAOqH,GAAG;AACZ;AAEA,SAASxE,YAAYA,CAAEb,GAAG,EAAEM,KAAK,EAAEC,GAAG,EAAE;EACtC,IAAIgF,KAAK,GAAGvF,GAAG,CAAC1B,KAAK,CAACgC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAIgE,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,KAAK,CAACjJ,MAAM,EAAE0B,CAAC,IAAI,CAAC,EAAE;IACxCuG,GAAG,IAAI5E,MAAM,CAACwF,YAAY,CAACI,KAAK,CAACvH,CAAC,CAAC,GAAGuH,KAAK,CAACvH,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EAC3D;EACA,OAAOuG,GAAG;AACZ;AAEAnJ,MAAM,CAACW,SAAS,CAACuC,KAAK,GAAG,SAASA,KAAKA,CAAEgC,KAAK,EAAEC,GAAG,EAAE;EACnD,IAAI3B,GAAG,GAAG,IAAI,CAACtC,MAAM;EACrBgE,KAAK,GAAG,CAAC,CAACA,KAAK;EACfC,GAAG,GAAGA,GAAG,KAAK9E,SAAS,GAAGmD,GAAG,GAAG,CAAC,CAAC2B,GAAG;EAErC,IAAID,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,IAAI1B,GAAG;IACZ,IAAI0B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIA,KAAK,GAAG1B,GAAG,EAAE;IACtB0B,KAAK,GAAG1B,GAAG;EACb;EAEA,IAAI2B,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAI3B,GAAG;IACV,IAAI2B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC;EACtB,CAAC,MAAM,IAAIA,GAAG,GAAG3B,GAAG,EAAE;IACpB2B,GAAG,GAAG3B,GAAG;EACX;EAEA,IAAI2B,GAAG,GAAGD,KAAK,EAAEC,GAAG,GAAGD,KAAK;EAE5B,IAAIkF,MAAM;EACV,IAAIpK,MAAM,CAACG,mBAAmB,EAAE;IAC9BiK,MAAM,GAAG,IAAI,CAACvJ,QAAQ,CAACqE,KAAK,EAAEC,GAAG,CAAC;IAClCiF,MAAM,CAAC1J,SAAS,GAAGV,MAAM,CAACW,SAAS;EACrC,CAAC,MAAM;IACL,IAAI0J,QAAQ,GAAGlF,GAAG,GAAGD,KAAK;IAC1BkF,MAAM,GAAG,IAAIpK,MAAM,CAACqK,QAAQ,EAAEhK,SAAS,CAAC;IACxC,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,QAAQ,EAAE,EAAEzH,CAAC,EAAE;MACjCwH,MAAM,CAACxH,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGsC,KAAK,CAAC;IAC7B;EACF;EAEA,OAAOkF,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,SAASE,WAAWA,CAAEvC,MAAM,EAAEwC,GAAG,EAAErJ,MAAM,EAAE;EACzC,IAAK6G,MAAM,GAAG,CAAC,KAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI5G,UAAU,CAAC,oBAAoB,CAAC;EAChF,IAAI4G,MAAM,GAAGwC,GAAG,GAAGrJ,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,uCAAuC,CAAC;AAC1F;AAEAnB,MAAM,CAACW,SAAS,CAAC6J,UAAU,GAAG,SAASA,UAAUA,CAAEzC,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EAC/E1C,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAEjH,UAAU,EAAE,IAAI,CAACI,MAAM,CAAC;EAE3D,IAAI4F,GAAG,GAAG,IAAI,CAACiB,MAAM,CAAC;EACtB,IAAI2C,GAAG,GAAG,CAAC;EACX,IAAI9H,CAAC,GAAG,CAAC;EACT,OAAO,EAAEA,CAAC,GAAG9B,UAAU,KAAK4J,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC5D,GAAG,IAAI,IAAI,CAACiB,MAAM,GAAGnF,CAAC,CAAC,GAAG8H,GAAG;EAC/B;EAEA,OAAO5D,GAAG;AACZ,CAAC;AAED9G,MAAM,CAACW,SAAS,CAACgK,UAAU,GAAG,SAASA,UAAUA,CAAE5C,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EAC/E1C,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAE;IACbH,WAAW,CAACvC,MAAM,EAAEjH,UAAU,EAAE,IAAI,CAACI,MAAM,CAAC;EAC9C;EAEA,IAAI4F,GAAG,GAAG,IAAI,CAACiB,MAAM,GAAG,EAAEjH,UAAU,CAAC;EACrC,IAAI4J,GAAG,GAAG,CAAC;EACX,OAAO5J,UAAU,GAAG,CAAC,KAAK4J,GAAG,IAAI,KAAK,CAAC,EAAE;IACvC5D,GAAG,IAAI,IAAI,CAACiB,MAAM,GAAG,EAAEjH,UAAU,CAAC,GAAG4J,GAAG;EAC1C;EAEA,OAAO5D,GAAG;AACZ,CAAC;AAED9G,MAAM,CAACW,SAAS,CAACiK,SAAS,GAAG,SAASA,SAASA,CAAE7C,MAAM,EAAE0C,QAAQ,EAAE;EACjE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAO,IAAI,CAAC6G,MAAM,CAAC;AACrB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACkK,YAAY,GAAG,SAASA,YAAYA,CAAE9C,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAO,IAAI,CAAC6G,MAAM,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE;AAC/C,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC8G,YAAY,GAAG,SAASA,YAAYA,CAAEM,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAQ,IAAI,CAAC6G,MAAM,CAAC,IAAI,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC;AAC/C,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACmK,YAAY,GAAG,SAASA,YAAYA,CAAE/C,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAElD,OAAO,CAAE,IAAI,CAAC6G,MAAM,CAAC,GAChB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,IACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,GAAG,SAAU;AACpC,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACoK,YAAY,GAAG,SAASA,YAAYA,CAAEhD,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAElD,OAAQ,IAAI,CAAC6G,MAAM,CAAC,GAAG,SAAS,IAC5B,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACqK,SAAS,GAAG,SAASA,SAASA,CAAEjD,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EAC7E1C,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAEjH,UAAU,EAAE,IAAI,CAACI,MAAM,CAAC;EAE3D,IAAI4F,GAAG,GAAG,IAAI,CAACiB,MAAM,CAAC;EACtB,IAAI2C,GAAG,GAAG,CAAC;EACX,IAAI9H,CAAC,GAAG,CAAC;EACT,OAAO,EAAEA,CAAC,GAAG9B,UAAU,KAAK4J,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC5D,GAAG,IAAI,IAAI,CAACiB,MAAM,GAAGnF,CAAC,CAAC,GAAG8H,GAAG;EAC/B;EACAA,GAAG,IAAI,IAAI;EAEX,IAAI5D,GAAG,IAAI4D,GAAG,EAAE5D,GAAG,IAAIzC,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,CAAC;EAElD,OAAOgG,GAAG;AACZ,CAAC;AAED9G,MAAM,CAACW,SAAS,CAACuK,SAAS,GAAG,SAASA,SAASA,CAAEnD,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EAC7E1C,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAEjH,UAAU,EAAE,IAAI,CAACI,MAAM,CAAC;EAE3D,IAAI0B,CAAC,GAAG9B,UAAU;EAClB,IAAI4J,GAAG,GAAG,CAAC;EACX,IAAI5D,GAAG,GAAG,IAAI,CAACiB,MAAM,GAAG,EAAEnF,CAAC,CAAC;EAC5B,OAAOA,CAAC,GAAG,CAAC,KAAK8H,GAAG,IAAI,KAAK,CAAC,EAAE;IAC9B5D,GAAG,IAAI,IAAI,CAACiB,MAAM,GAAG,EAAEnF,CAAC,CAAC,GAAG8H,GAAG;EACjC;EACAA,GAAG,IAAI,IAAI;EAEX,IAAI5D,GAAG,IAAI4D,GAAG,EAAE5D,GAAG,IAAIzC,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,CAAC;EAElD,OAAOgG,GAAG;AACZ,CAAC;AAED9G,MAAM,CAACW,SAAS,CAACwK,QAAQ,GAAG,SAASA,QAAQA,CAAEpD,MAAM,EAAE0C,QAAQ,EAAE;EAC/D,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,IAAI,EAAE,IAAI,CAAC6G,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,OAAQ,IAAI,CAACA,MAAM,CAAC;EAChD,OAAQ,CAAC,IAAI,GAAG,IAAI,CAACA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACyK,WAAW,GAAG,SAASA,WAAWA,CAAErD,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,IAAI4F,GAAG,GAAG,IAAI,CAACiB,MAAM,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE;EAChD,OAAQjB,GAAG,GAAG,MAAM,GAAIA,GAAG,GAAG,UAAU,GAAGA,GAAG;AAChD,CAAC;AAED9G,MAAM,CAACW,SAAS,CAAC0K,WAAW,GAAG,SAASA,WAAWA,CAAEtD,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,IAAI4F,GAAG,GAAG,IAAI,CAACiB,MAAM,GAAG,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,IAAI,CAAE;EAChD,OAAQjB,GAAG,GAAG,MAAM,GAAIA,GAAG,GAAG,UAAU,GAAGA,GAAG;AAChD,CAAC;AAED9G,MAAM,CAACW,SAAS,CAAC2K,WAAW,GAAG,SAASA,WAAWA,CAAEvD,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAElD,OAAQ,IAAI,CAAC6G,MAAM,CAAC,GACjB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG;AAC5B,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC4K,WAAW,GAAG,SAASA,WAAWA,CAAExD,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAElD,OAAQ,IAAI,CAAC6G,MAAM,CAAC,IAAI,EAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAE;AACtB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC6K,WAAW,GAAG,SAASA,WAAWA,CAAEzD,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAOrB,OAAO,CAAC2H,IAAI,CAAC,IAAI,EAAEO,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC8K,WAAW,GAAG,SAASA,WAAWA,CAAE1D,MAAM,EAAE0C,QAAQ,EAAE;EACrE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAOrB,OAAO,CAAC2H,IAAI,CAAC,IAAI,EAAEO,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC+K,YAAY,GAAG,SAASA,YAAYA,CAAE3D,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAOrB,OAAO,CAAC2H,IAAI,CAAC,IAAI,EAAEO,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACgL,YAAY,GAAG,SAASA,YAAYA,CAAE5D,MAAM,EAAE0C,QAAQ,EAAE;EACvE,IAAI,CAACA,QAAQ,EAAEH,WAAW,CAACvC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAAC;EAClD,OAAOrB,OAAO,CAAC2H,IAAI,CAAC,IAAI,EAAEO,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED,SAAS6D,QAAQA,CAAEhH,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEwC,GAAG,EAAElE,GAAG,EAAE/B,GAAG,EAAE;EACpD,IAAI,CAACtE,MAAM,CAACuD,QAAQ,CAACqB,GAAG,CAAC,EAAE,MAAM,IAAIhD,SAAS,CAAC,6CAA6C,CAAC;EAC7F,IAAID,KAAK,GAAG0E,GAAG,IAAI1E,KAAK,GAAG2C,GAAG,EAAE,MAAM,IAAInD,UAAU,CAAC,mCAAmC,CAAC;EACzF,IAAI4G,MAAM,GAAGwC,GAAG,GAAG3F,GAAG,CAAC1D,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;AAC3E;AAEAnB,MAAM,CAACW,SAAS,CAACkL,WAAW,GAAG,SAASA,WAAWA,CAAElK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EACxF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAE;IACb,IAAIqB,QAAQ,GAAGzH,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,CAAC,GAAG,CAAC;IAC9C8K,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAEgL,QAAQ,EAAE,CAAC,CAAC;EACxD;EAEA,IAAIpB,GAAG,GAAG,CAAC;EACX,IAAI9H,CAAC,GAAG,CAAC;EACT,IAAI,CAACmF,MAAM,CAAC,GAAGpG,KAAK,GAAG,IAAI;EAC3B,OAAO,EAAEiB,CAAC,GAAG9B,UAAU,KAAK4J,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC,IAAI,CAAC3C,MAAM,GAAGnF,CAAC,CAAC,GAAIjB,KAAK,GAAG+I,GAAG,GAAI,IAAI;EACzC;EAEA,OAAO3C,MAAM,GAAGjH,UAAU;AAC5B,CAAC;AAEDd,MAAM,CAACW,SAAS,CAACoL,WAAW,GAAG,SAASA,WAAWA,CAAEpK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EACxF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnBjH,UAAU,GAAGA,UAAU,GAAG,CAAC;EAC3B,IAAI,CAAC2J,QAAQ,EAAE;IACb,IAAIqB,QAAQ,GAAGzH,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,CAAC,GAAG,CAAC;IAC9C8K,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAEgL,QAAQ,EAAE,CAAC,CAAC;EACxD;EAEA,IAAIlJ,CAAC,GAAG9B,UAAU,GAAG,CAAC;EACtB,IAAI4J,GAAG,GAAG,CAAC;EACX,IAAI,CAAC3C,MAAM,GAAGnF,CAAC,CAAC,GAAGjB,KAAK,GAAG,IAAI;EAC/B,OAAO,EAAEiB,CAAC,IAAI,CAAC,KAAK8H,GAAG,IAAI,KAAK,CAAC,EAAE;IACjC,IAAI,CAAC3C,MAAM,GAAGnF,CAAC,CAAC,GAAIjB,KAAK,GAAG+I,GAAG,GAAI,IAAI;EACzC;EAEA,OAAO3C,MAAM,GAAGjH,UAAU;AAC5B,CAAC;AAEDd,MAAM,CAACW,SAAS,CAACqL,UAAU,GAAG,SAASA,UAAUA,CAAErK,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC1E9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACxD,IAAI,CAAC/H,MAAM,CAACG,mBAAmB,EAAEwB,KAAK,GAAG0C,IAAI,CAAC4H,KAAK,CAACtK,KAAK,CAAC;EAC1D,IAAI,CAACoG,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;EAC7B,OAAOoG,MAAM,GAAG,CAAC;AACnB,CAAC;AAED,SAASmE,iBAAiBA,CAAEtH,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE;EAC5D,IAAIxK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,MAAM,GAAGA,KAAK,GAAG,CAAC;EACzC,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEgF,CAAC,GAAGvD,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC1D,MAAM,GAAG6G,MAAM,EAAE,CAAC,CAAC,EAAEnF,CAAC,GAAGgF,CAAC,EAAE,EAAEhF,CAAC,EAAE;IAChEgC,GAAG,CAACmD,MAAM,GAAGnF,CAAC,CAAC,GAAG,CAACjB,KAAK,GAAI,IAAI,IAAK,CAAC,IAAIwK,YAAY,GAAGvJ,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAG,MACnE,CAACuJ,YAAY,GAAGvJ,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC;EAClC;AACF;AAEA5C,MAAM,CAACW,SAAS,CAACyL,aAAa,GAAG,SAASA,aAAaA,CAAEzK,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EAC1D,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;IAC7B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;EAClC,CAAC,MAAM;IACLuK,iBAAiB,CAAC,IAAI,EAAEvK,KAAK,EAAEoG,MAAM,EAAE,IAAI,CAAC;EAC9C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC0L,aAAa,GAAG,SAASA,aAAaA,CAAE1K,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EAC1D,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAC5B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,GAAG,IAAK;EACnC,CAAC,MAAM;IACLuK,iBAAiB,CAAC,IAAI,EAAEvK,KAAK,EAAEoG,MAAM,EAAE,KAAK,CAAC;EAC/C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED,SAASuE,iBAAiBA,CAAE1H,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE;EAC5D,IAAIxK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,UAAU,GAAGA,KAAK,GAAG,CAAC;EAC7C,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEgF,CAAC,GAAGvD,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC1D,MAAM,GAAG6G,MAAM,EAAE,CAAC,CAAC,EAAEnF,CAAC,GAAGgF,CAAC,EAAE,EAAEhF,CAAC,EAAE;IAChEgC,GAAG,CAACmD,MAAM,GAAGnF,CAAC,CAAC,GAAIjB,KAAK,KAAK,CAACwK,YAAY,GAAGvJ,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAI,IAAI;EACrE;AACF;AAEA5C,MAAM,CAACW,SAAS,CAAC4L,aAAa,GAAG,SAASA,aAAaA,CAAE5K,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;EAC9D,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;IACjC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;IACjC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAChC,IAAI,CAACoG,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;EAC/B,CAAC,MAAM;IACL2K,iBAAiB,CAAC,IAAI,EAAE3K,KAAK,EAAEoG,MAAM,EAAE,IAAI,CAAC;EAC9C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC6L,aAAa,GAAG,SAASA,aAAaA,CAAE7K,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;EAC9D,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,KAAK,EAAG;IAC7B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;IACjC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAChC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,GAAG,IAAK;EACnC,CAAC,MAAM;IACL2K,iBAAiB,CAAC,IAAI,EAAE3K,KAAK,EAAEoG,MAAM,EAAE,KAAK,CAAC;EAC/C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAAC8L,UAAU,GAAG,SAASA,UAAUA,CAAE9K,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EACtF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAE;IACb,IAAIiC,KAAK,GAAGrI,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,GAAG,CAAC,CAAC;IAE3C8K,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE4L,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC;EAC9D;EAEA,IAAI9J,CAAC,GAAG,CAAC;EACT,IAAI8H,GAAG,GAAG,CAAC;EACX,IAAIiC,GAAG,GAAG,CAAC;EACX,IAAI,CAAC5E,MAAM,CAAC,GAAGpG,KAAK,GAAG,IAAI;EAC3B,OAAO,EAAEiB,CAAC,GAAG9B,UAAU,KAAK4J,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC,IAAI/I,KAAK,GAAG,CAAC,IAAIgL,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC5E,MAAM,GAAGnF,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACxD+J,GAAG,GAAG,CAAC;IACT;IACA,IAAI,CAAC5E,MAAM,GAAGnF,CAAC,CAAC,GAAG,CAAEjB,KAAK,GAAG+I,GAAG,IAAK,CAAC,IAAIiC,GAAG,GAAG,IAAI;EACtD;EAEA,OAAO5E,MAAM,GAAGjH,UAAU;AAC5B,CAAC;AAEDd,MAAM,CAACW,SAAS,CAACiM,UAAU,GAAG,SAASA,UAAUA,CAAEjL,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE2J,QAAQ,EAAE;EACtF9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAE;IACb,IAAIiC,KAAK,GAAGrI,IAAI,CAAC4G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGnK,UAAU,GAAG,CAAC,CAAC;IAE3C8K,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAEjH,UAAU,EAAE4L,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC;EAC9D;EAEA,IAAI9J,CAAC,GAAG9B,UAAU,GAAG,CAAC;EACtB,IAAI4J,GAAG,GAAG,CAAC;EACX,IAAIiC,GAAG,GAAG,CAAC;EACX,IAAI,CAAC5E,MAAM,GAAGnF,CAAC,CAAC,GAAGjB,KAAK,GAAG,IAAI;EAC/B,OAAO,EAAEiB,CAAC,IAAI,CAAC,KAAK8H,GAAG,IAAI,KAAK,CAAC,EAAE;IACjC,IAAI/I,KAAK,GAAG,CAAC,IAAIgL,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC5E,MAAM,GAAGnF,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACxD+J,GAAG,GAAG,CAAC;IACT;IACA,IAAI,CAAC5E,MAAM,GAAGnF,CAAC,CAAC,GAAG,CAAEjB,KAAK,GAAG+I,GAAG,IAAK,CAAC,IAAIiC,GAAG,GAAG,IAAI;EACtD;EAEA,OAAO5E,MAAM,GAAGjH,UAAU;AAC5B,CAAC;AAEDd,MAAM,CAACW,SAAS,CAACkM,SAAS,GAAG,SAASA,SAASA,CAAElL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EACxE9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC;EAC5D,IAAI,CAAC/H,MAAM,CAACG,mBAAmB,EAAEwB,KAAK,GAAG0C,IAAI,CAAC4H,KAAK,CAACtK,KAAK,CAAC;EAC1D,IAAIA,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;EACvC,IAAI,CAACoG,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;EAC7B,OAAOoG,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACmM,YAAY,GAAG,SAASA,YAAYA,CAAEnL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;EAChE,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;IAC7B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;EAClC,CAAC,MAAM;IACLuK,iBAAiB,CAAC,IAAI,EAAEvK,KAAK,EAAEoG,MAAM,EAAE,IAAI,CAAC;EAC9C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACoM,YAAY,GAAG,SAASA,YAAYA,CAAEpL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;EAChE,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAC5B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,GAAG,IAAK;EACnC,CAAC,MAAM;IACLuK,iBAAiB,CAAC,IAAI,EAAEvK,KAAK,EAAEoG,MAAM,EAAE,KAAK,CAAC;EAC/C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACqM,YAAY,GAAG,SAASA,YAAYA,CAAErL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC;EACxE,IAAI/H,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,GAAG,IAAK;IAC7B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAChC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;IACjC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;EACnC,CAAC,MAAM;IACL2K,iBAAiB,CAAC,IAAI,EAAE3K,KAAK,EAAEoG,MAAM,EAAE,IAAI,CAAC;EAC9C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED/H,MAAM,CAACW,SAAS,CAACsM,YAAY,GAAG,SAASA,YAAYA,CAAEtL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E9I,KAAK,GAAG,CAACA,KAAK;EACdoG,MAAM,GAAGA,MAAM,GAAG,CAAC;EACnB,IAAI,CAAC0C,QAAQ,EAAEmB,QAAQ,CAAC,IAAI,EAAEjK,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC;EACxE,IAAIpG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,UAAU,GAAGA,KAAK,GAAG,CAAC;EAC7C,IAAI3B,MAAM,CAACG,mBAAmB,EAAE;IAC9B,IAAI,CAAC4H,MAAM,CAAC,GAAIpG,KAAK,KAAK,EAAG;IAC7B,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,EAAG;IACjC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,KAAK,CAAE;IAChC,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC,GAAIpG,KAAK,GAAG,IAAK;EACnC,CAAC,MAAM;IACL2K,iBAAiB,CAAC,IAAI,EAAE3K,KAAK,EAAEoG,MAAM,EAAE,KAAK,CAAC;EAC/C;EACA,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAED,SAASmF,YAAYA,CAAEtI,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEwC,GAAG,EAAElE,GAAG,EAAE/B,GAAG,EAAE;EACxD,IAAIyD,MAAM,GAAGwC,GAAG,GAAG3F,GAAG,CAAC1D,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EACzE,IAAI4G,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI5G,UAAU,CAAC,oBAAoB,CAAC;AAC5D;AAEA,SAASgM,UAAUA,CAAEvI,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE1B,QAAQ,EAAE;EAC/D,IAAI,CAACA,QAAQ,EAAE;IACbyC,YAAY,CAACtI,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,CAAC,sBAAsB,CAAC;EACtF;EACAlI,OAAO,CAACoD,KAAK,CAAC2B,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;EACtD,OAAOpE,MAAM,GAAG,CAAC;AACnB;AAEA/H,MAAM,CAACW,SAAS,CAACyM,YAAY,GAAG,SAASA,YAAYA,CAAEzL,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E,OAAO0C,UAAU,CAAC,IAAI,EAAExL,KAAK,EAAEoG,MAAM,EAAE,IAAI,EAAE0C,QAAQ,CAAC;AACxD,CAAC;AAEDzK,MAAM,CAACW,SAAS,CAAC0M,YAAY,GAAG,SAASA,YAAYA,CAAE1L,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAC9E,OAAO0C,UAAU,CAAC,IAAI,EAAExL,KAAK,EAAEoG,MAAM,EAAE,KAAK,EAAE0C,QAAQ,CAAC;AACzD,CAAC;AAED,SAAS6C,WAAWA,CAAE1I,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE1B,QAAQ,EAAE;EAChE,IAAI,CAACA,QAAQ,EAAE;IACbyC,YAAY,CAACtI,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAE,CAAC,EAAE,uBAAuB,EAAE,CAAC,uBAAuB,CAAC;EACxF;EACAlI,OAAO,CAACoD,KAAK,CAAC2B,GAAG,EAAEjD,KAAK,EAAEoG,MAAM,EAAEoE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;EACtD,OAAOpE,MAAM,GAAG,CAAC;AACnB;AAEA/H,MAAM,CAACW,SAAS,CAAC4M,aAAa,GAAG,SAASA,aAAaA,CAAE5L,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF,OAAO6C,WAAW,CAAC,IAAI,EAAE3L,KAAK,EAAEoG,MAAM,EAAE,IAAI,EAAE0C,QAAQ,CAAC;AACzD,CAAC;AAEDzK,MAAM,CAACW,SAAS,CAAC6M,aAAa,GAAG,SAASA,aAAaA,CAAE7L,KAAK,EAAEoG,MAAM,EAAE0C,QAAQ,EAAE;EAChF,OAAO6C,WAAW,CAAC,IAAI,EAAE3L,KAAK,EAAEoG,MAAM,EAAE,KAAK,EAAE0C,QAAQ,CAAC;AAC1D,CAAC;;AAED;AACAzK,MAAM,CAACW,SAAS,CAAC8C,IAAI,GAAG,SAASA,IAAIA,CAAE+C,MAAM,EAAEiH,WAAW,EAAEvI,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAI,CAACD,KAAK,EAAEA,KAAK,GAAG,CAAC;EACrB,IAAI,CAACC,GAAG,IAAIA,GAAG,KAAK,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACjE,MAAM;EACxC,IAAIuM,WAAW,IAAIjH,MAAM,CAACtF,MAAM,EAAEuM,WAAW,GAAGjH,MAAM,CAACtF,MAAM;EAC7D,IAAI,CAACuM,WAAW,EAAEA,WAAW,GAAG,CAAC;EACjC,IAAItI,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAGD,KAAK,EAAEC,GAAG,GAAGD,KAAK;;EAEvC;EACA,IAAIC,GAAG,KAAKD,KAAK,EAAE,OAAO,CAAC;EAC3B,IAAIsB,MAAM,CAACtF,MAAM,KAAK,CAAC,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;EAEtD;EACA,IAAIuM,WAAW,GAAG,CAAC,EAAE;IACnB,MAAM,IAAItM,UAAU,CAAC,2BAA2B,CAAC;EACnD;EACA,IAAI+D,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAChE,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,2BAA2B,CAAC;EACxF,IAAIgE,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIhE,UAAU,CAAC,yBAAyB,CAAC;;EAE5D;EACA,IAAIgE,GAAG,GAAG,IAAI,CAACjE,MAAM,EAAEiE,GAAG,GAAG,IAAI,CAACjE,MAAM;EACxC,IAAIsF,MAAM,CAACtF,MAAM,GAAGuM,WAAW,GAAGtI,GAAG,GAAGD,KAAK,EAAE;IAC7CC,GAAG,GAAGqB,MAAM,CAACtF,MAAM,GAAGuM,WAAW,GAAGvI,KAAK;EAC3C;EAEA,IAAI1B,GAAG,GAAG2B,GAAG,GAAGD,KAAK;EACrB,IAAItC,CAAC;EAEL,IAAI,IAAI,KAAK4D,MAAM,IAAItB,KAAK,GAAGuI,WAAW,IAAIA,WAAW,GAAGtI,GAAG,EAAE;IAC/D;IACA,KAAKvC,CAAC,GAAGY,GAAG,GAAG,CAAC,EAAEZ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC7B4D,MAAM,CAAC5D,CAAC,GAAG6K,WAAW,CAAC,GAAG,IAAI,CAAC7K,CAAC,GAAGsC,KAAK,CAAC;IAC3C;EACF,CAAC,MAAM,IAAI1B,GAAG,GAAG,IAAI,IAAI,CAACxD,MAAM,CAACG,mBAAmB,EAAE;IACpD;IACA,KAAKyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAE,EAAEZ,CAAC,EAAE;MACxB4D,MAAM,CAAC5D,CAAC,GAAG6K,WAAW,CAAC,GAAG,IAAI,CAAC7K,CAAC,GAAGsC,KAAK,CAAC;IAC3C;EACF,CAAC,MAAM;IACLzE,UAAU,CAACE,SAAS,CAAC+M,GAAG,CAACvG,IAAI,CAC3BX,MAAM,EACN,IAAI,CAAC3F,QAAQ,CAACqE,KAAK,EAAEA,KAAK,GAAG1B,GAAG,CAAC,EACjCiK,WACF,CAAC;EACH;EAEA,OAAOjK,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACAxD,MAAM,CAACW,SAAS,CAAC8B,IAAI,GAAG,SAASA,IAAIA,CAAEqE,GAAG,EAAE5B,KAAK,EAAEC,GAAG,EAAEzC,QAAQ,EAAE;EAChE;EACA,IAAI,OAAOoE,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAO5B,KAAK,KAAK,QAAQ,EAAE;MAC7BxC,QAAQ,GAAGwC,KAAK;MAChBA,KAAK,GAAG,CAAC;MACTC,GAAG,GAAG,IAAI,CAACjE,MAAM;IACnB,CAAC,MAAM,IAAI,OAAOiE,GAAG,KAAK,QAAQ,EAAE;MAClCzC,QAAQ,GAAGyC,GAAG;MACdA,GAAG,GAAG,IAAI,CAACjE,MAAM;IACnB;IACA,IAAI4F,GAAG,CAAC5F,MAAM,KAAK,CAAC,EAAE;MACpB,IAAIyM,IAAI,GAAG7G,GAAG,CAAC8G,UAAU,CAAC,CAAC,CAAC;MAC5B,IAAID,IAAI,GAAG,GAAG,EAAE;QACd7G,GAAG,GAAG6G,IAAI;MACZ;IACF;IACA,IAAIjL,QAAQ,KAAKrC,SAAS,IAAI,OAAOqC,QAAQ,KAAK,QAAQ,EAAE;MAC1D,MAAM,IAAId,SAAS,CAAC,2BAA2B,CAAC;IAClD;IACA,IAAI,OAAOc,QAAQ,KAAK,QAAQ,IAAI,CAAC1C,MAAM,CAAC+C,UAAU,CAACL,QAAQ,CAAC,EAAE;MAChE,MAAM,IAAId,SAAS,CAAC,oBAAoB,GAAGc,QAAQ,CAAC;IACtD;EACF,CAAC,MAAM,IAAI,OAAOoE,GAAG,KAAK,QAAQ,EAAE;IAClCA,GAAG,GAAGA,GAAG,GAAG,GAAG;EACjB;;EAEA;EACA,IAAI5B,KAAK,GAAG,CAAC,IAAI,IAAI,CAAChE,MAAM,GAAGgE,KAAK,IAAI,IAAI,CAAChE,MAAM,GAAGiE,GAAG,EAAE;IACzD,MAAM,IAAIhE,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,IAAIgE,GAAG,IAAID,KAAK,EAAE;IAChB,OAAO,IAAI;EACb;EAEAA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBC,GAAG,GAAGA,GAAG,KAAK9E,SAAS,GAAG,IAAI,CAACa,MAAM,GAAGiE,GAAG,KAAK,CAAC;EAEjD,IAAI,CAAC2B,GAAG,EAAEA,GAAG,GAAG,CAAC;EAEjB,IAAIlE,CAAC;EACL,IAAI,OAAOkE,GAAG,KAAK,QAAQ,EAAE;IAC3B,KAAKlE,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,GAAGuC,GAAG,EAAE,EAAEvC,CAAC,EAAE;MAC5B,IAAI,CAACA,CAAC,CAAC,GAAGkE,GAAG;IACf;EACF,CAAC,MAAM;IACL,IAAIqD,KAAK,GAAGnK,MAAM,CAACuD,QAAQ,CAACuD,GAAG,CAAC,GAC5BA,GAAG,GACH/B,WAAW,CAAC,IAAI/E,MAAM,CAAC8G,GAAG,EAAEpE,QAAQ,CAAC,CAACoB,QAAQ,CAAC,CAAC,CAAC;IACrD,IAAIN,GAAG,GAAG2G,KAAK,CAACjJ,MAAM;IACtB,KAAK0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,GAAG,GAAGD,KAAK,EAAE,EAAEtC,CAAC,EAAE;MAChC,IAAI,CAACA,CAAC,GAAGsC,KAAK,CAAC,GAAGiF,KAAK,CAACvH,CAAC,GAAGY,GAAG,CAAC;IAClC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;;AAEA,IAAIqK,iBAAiB,GAAG,oBAAoB;AAE5C,SAASC,WAAWA,CAAE1H,GAAG,EAAE;EACzB;EACAA,GAAG,GAAG2H,UAAU,CAAC3H,GAAG,CAAC,CAAC4H,OAAO,CAACH,iBAAiB,EAAE,EAAE,CAAC;EACpD;EACA,IAAIzH,GAAG,CAAClF,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;EAC7B;EACA,OAAOkF,GAAG,CAAClF,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3BkF,GAAG,GAAGA,GAAG,GAAG,GAAG;EACjB;EACA,OAAOA,GAAG;AACZ;AAEA,SAAS2H,UAAUA,CAAE3H,GAAG,EAAE;EACxB,IAAIA,GAAG,CAAC6H,IAAI,EAAE,OAAO7H,GAAG,CAAC6H,IAAI,CAAC,CAAC;EAC/B,OAAO7H,GAAG,CAAC4H,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACtC;AAEA,SAAS9D,KAAKA,CAAEvE,CAAC,EAAE;EACjB,IAAIA,CAAC,GAAG,EAAE,EAAE,OAAO,GAAG,GAAGA,CAAC,CAAC7B,QAAQ,CAAC,EAAE,CAAC;EACvC,OAAO6B,CAAC,CAAC7B,QAAQ,CAAC,EAAE,CAAC;AACvB;AAEA,SAASiB,WAAWA,CAAEjC,MAAM,EAAEoL,KAAK,EAAE;EACnCA,KAAK,GAAGA,KAAK,IAAIC,QAAQ;EACzB,IAAI9E,SAAS;EACb,IAAInI,MAAM,GAAG4B,MAAM,CAAC5B,MAAM;EAC1B,IAAIkN,aAAa,GAAG,IAAI;EACxB,IAAIjE,KAAK,GAAG,EAAE;EAEd,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,EAAE,EAAE0B,CAAC,EAAE;IAC/ByG,SAAS,GAAGvG,MAAM,CAAC8K,UAAU,CAAChL,CAAC,CAAC;;IAEhC;IACA,IAAIyG,SAAS,GAAG,MAAM,IAAIA,SAAS,GAAG,MAAM,EAAE;MAC5C;MACA,IAAI,CAAC+E,aAAa,EAAE;QAClB;QACA,IAAI/E,SAAS,GAAG,MAAM,EAAE;UACtB;UACA,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE/D,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACF,CAAC,MAAM,IAAI/G,CAAC,GAAG,CAAC,KAAK1B,MAAM,EAAE;UAC3B;UACA,IAAI,CAACgN,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE/D,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACF;;QAEA;QACAyE,aAAa,GAAG/E,SAAS;QAEzB;MACF;;MAEA;MACA,IAAIA,SAAS,GAAG,MAAM,EAAE;QACtB,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE/D,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACnDyE,aAAa,GAAG/E,SAAS;QACzB;MACF;;MAEA;MACAA,SAAS,GAAG,CAAC+E,aAAa,GAAG,MAAM,IAAI,EAAE,GAAG/E,SAAS,GAAG,MAAM,IAAI,OAAO;IAC3E,CAAC,MAAM,IAAI+E,aAAa,EAAE;MACxB;MACA,IAAI,CAACF,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE/D,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrD;IAEAyE,aAAa,GAAG,IAAI;;IAEpB;IACA,IAAI/E,SAAS,GAAG,IAAI,EAAE;MACpB,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtB/D,KAAK,CAACR,IAAI,CAACN,SAAS,CAAC;IACvB,CAAC,MAAM,IAAIA,SAAS,GAAG,KAAK,EAAE;MAC5B,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtB/D,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,GAAG,GAAG,IAAI,EACvBA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM,IAAIA,SAAS,GAAG,OAAO,EAAE;MAC9B,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtB/D,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,GAAG,GAAG,IAAI,EACvBA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM,IAAIA,SAAS,GAAG,QAAQ,EAAE;MAC/B,IAAI,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtB/D,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,IAAI,GAAG,IAAI,EACxBA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAI/H,KAAK,CAAC,oBAAoB,CAAC;IACvC;EACF;EAEA,OAAO6I,KAAK;AACd;AAEA,SAAS1B,YAAYA,CAAErC,GAAG,EAAE;EAC1B,IAAIiI,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,GAAG,CAAClF,MAAM,EAAE,EAAE0B,CAAC,EAAE;IACnC;IACAyL,SAAS,CAAC1E,IAAI,CAACvD,GAAG,CAACwH,UAAU,CAAChL,CAAC,CAAC,GAAG,IAAI,CAAC;EAC1C;EACA,OAAOyL,SAAS;AAClB;AAEA,SAASxF,cAAcA,CAAEzC,GAAG,EAAE8H,KAAK,EAAE;EACnC,IAAII,CAAC,EAAEC,EAAE,EAAEC,EAAE;EACb,IAAIH,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,GAAG,CAAClF,MAAM,EAAE,EAAE0B,CAAC,EAAE;IACnC,IAAI,CAACsL,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;IAEtBI,CAAC,GAAGlI,GAAG,CAACwH,UAAU,CAAChL,CAAC,CAAC;IACrB2L,EAAE,GAAGD,CAAC,IAAI,CAAC;IACXE,EAAE,GAAGF,CAAC,GAAG,GAAG;IACZD,SAAS,CAAC1E,IAAI,CAAC6E,EAAE,CAAC;IAClBH,SAAS,CAAC1E,IAAI,CAAC4E,EAAE,CAAC;EACpB;EAEA,OAAOF,SAAS;AAClB;AAEA,SAASrJ,aAAaA,CAAEoB,GAAG,EAAE;EAC3B,OAAOzG,MAAM,CAAC8O,WAAW,CAACX,WAAW,CAAC1H,GAAG,CAAC,CAAC;AAC7C;AAEA,SAASmC,UAAUA,CAAEmG,GAAG,EAAEC,GAAG,EAAE5G,MAAM,EAAE7G,MAAM,EAAE;EAC7C,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,EAAE,EAAE0B,CAAC,EAAE;IAC/B,IAAKA,CAAC,GAAGmF,MAAM,IAAI4G,GAAG,CAACzN,MAAM,IAAM0B,CAAC,IAAI8L,GAAG,CAACxN,MAAO,EAAE;IACrDyN,GAAG,CAAC/L,CAAC,GAAGmF,MAAM,CAAC,GAAG2G,GAAG,CAAC9L,CAAC,CAAC;EAC1B;EACA,OAAOA,CAAC;AACV;AAEA,SAASe,KAAKA,CAAEmD,GAAG,EAAE;EACnB,OAAOA,GAAG,KAAKA,GAAG,EAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}