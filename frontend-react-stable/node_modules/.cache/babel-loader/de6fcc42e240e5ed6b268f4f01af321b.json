{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Paragraph = function Paragraph(props) {\n  var getWidth = function getWidth(index) {\n    var width = props.width,\n      _props$rows = props.rows,\n      rows = _props$rows === void 0 ? 2 : _props$rows;\n    if (Array.isArray(width)) {\n      return width[index];\n    }\n    // last paragraph\n    if (rows - 1 === index) {\n      return width;\n    }\n    return undefined;\n  };\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    rows = props.rows;\n  var rowList = _toConsumableArray(Array(rows)).map(function (_, index) {\n    return (/*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: index,\n        style: {\n          width: getWidth(index)\n        }\n      })\n    );\n  });\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;", "map": {"version": 3, "names": ["_toConsumableArray", "classNames", "React", "Paragraph", "props", "getWidth", "index", "width", "_props$rows", "rows", "Array", "isArray", "undefined", "prefixCls", "className", "style", "rowList", "map", "_", "createElement", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Paragraph.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Paragraph = function Paragraph(props) {\n  var getWidth = function getWidth(index) {\n    var width = props.width,\n      _props$rows = props.rows,\n      rows = _props$rows === void 0 ? 2 : _props$rows;\n    if (Array.isArray(width)) {\n      return width[index];\n    }\n    // last paragraph\n    if (rows - 1 === index) {\n      return width;\n    }\n    return undefined;\n  };\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    rows = props.rows;\n  var rowList = _toConsumableArray(Array(rows)).map(function (_, index) {\n    return (\n      /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: index,\n        style: {\n          width: getWidth(index)\n        }\n      })\n    );\n  });\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;MACrBC,WAAW,GAAGJ,KAAK,CAACK,IAAI;MACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IACjD,IAAIE,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACD,KAAK,CAAC;IACrB;IACA;IACA,IAAIG,IAAI,GAAG,CAAC,KAAKH,KAAK,EAAE;MACtB,OAAOC,KAAK;IACd;IACA,OAAOK,SAAS;EAClB,CAAC;EACD,IAAIC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC7BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBN,IAAI,GAAGL,KAAK,CAACK,IAAI;EACnB,IAAIO,OAAO,GAAGhB,kBAAkB,CAACU,KAAK,CAACD,IAAI,CAAC,CAAC,CAACQ,GAAG,CAAC,UAAUC,CAAC,EAAEZ,KAAK,EAAE;IACpE,QACE;MACA;MACAJ,KAAK,CAACiB,aAAa,CAAC,IAAI,EAAE;QACxBC,GAAG,EAAEd,KAAK;QACVS,KAAK,EAAE;UACLR,KAAK,EAAEF,QAAQ,CAACC,KAAK;QACvB;MACF,CAAC;IAAC;EAEN,CAAC,CAAC;EACF,OAAO,aAAaJ,KAAK,CAACiB,aAAa,CAAC,IAAI,EAAE;IAC5CL,SAAS,EAAEb,UAAU,CAACY,SAAS,EAAEC,SAAS,CAAC;IAC3CC,KAAK,EAAEA;EACT,CAAC,EAAEC,OAAO,CAAC;AACb,CAAC;AACD,eAAeb,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}