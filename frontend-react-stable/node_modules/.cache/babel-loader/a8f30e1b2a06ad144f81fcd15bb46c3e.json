{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.slice = void 0;\nvar slice = Array.prototype.slice;\nexports.slice = slice;\nfunction _default(x) {\n  return typeof x === \"object\" && \"length\" in x ? x // Array, TypedArray, NodeList, array-like\n  : Array.from(x); // Map, Set, iterable, string, or anything else\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "slice", "Array", "prototype", "x", "from"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/array.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.slice = void 0;\nvar slice = Array.prototype.slice;\nexports.slice = slice;\n\nfunction _default(x) {\n  return typeof x === \"object\" && \"length\" in x ? x // Array, TypedArray, NodeList, array-like\n  : Array.from(x); // Map, Set, iterable, string, or anything else\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS,CAACF,KAAK;AACjCJ,OAAO,CAACI,KAAK,GAAGA,KAAK;AAErB,SAASD,QAAQA,CAACI,CAAC,EAAE;EACnB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,QAAQ,IAAIA,CAAC,GAAGA,CAAC,CAAC;EAAA,EAChDF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}