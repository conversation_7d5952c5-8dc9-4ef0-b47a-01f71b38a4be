{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nexport var responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport var responsiveMap = {\n  xs: '(max-width: 575px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  xxl: '(min-width: 1600px)'\n};\nvar subscribers = new Map();\nvar subUid = -1;\nvar screens = {};\nvar responsiveObserve = {\n  matchHandlers: {},\n  dispatch: function dispatch(pointMap) {\n    screens = pointMap;\n    subscribers.forEach(function (func) {\n      return func(screens);\n    });\n    return subscribers.size >= 1;\n  },\n  subscribe: function subscribe(func) {\n    if (!subscribers.size) this.register();\n    subUid += 1;\n    subscribers.set(subUid, func);\n    func(screens);\n    return subUid;\n  },\n  unsubscribe: function unsubscribe(token) {\n    subscribers[\"delete\"](token);\n    if (!subscribers.size) this.unregister();\n  },\n  unregister: function unregister() {\n    var _this = this;\n    Object.keys(responsiveMap).forEach(function (screen) {\n      var matchMediaQuery = responsiveMap[screen];\n      var handler = _this.matchHandlers[matchMediaQuery];\n      handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);\n    });\n    subscribers.clear();\n  },\n  register: function register() {\n    var _this2 = this;\n    Object.keys(responsiveMap).forEach(function (screen) {\n      var matchMediaQuery = responsiveMap[screen];\n      var listener = function listener(_ref) {\n        var matches = _ref.matches;\n        _this2.dispatch(_extends(_extends({}, screens), _defineProperty({}, screen, matches)));\n      };\n      var mql = window.matchMedia(matchMediaQuery);\n      mql.addListener(listener);\n      _this2.matchHandlers[matchMediaQuery] = {\n        mql: mql,\n        listener: listener\n      };\n      listener(mql);\n    });\n  }\n};\nexport default responsiveObserve;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "responsiveArray", "responsiveMap", "xs", "sm", "md", "lg", "xl", "xxl", "subscribers", "Map", "subUid", "screens", "responsiveObserve", "matchHandlers", "dispatch", "pointMap", "for<PERSON>ach", "func", "size", "subscribe", "register", "set", "unsubscribe", "token", "unregister", "_this", "Object", "keys", "screen", "matchMediaQuery", "handler", "mql", "removeListener", "listener", "clear", "_this2", "_ref", "matches", "window", "matchMedia", "addListener"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/responsiveObserve.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nexport var responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport var responsiveMap = {\n  xs: '(max-width: 575px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  xxl: '(min-width: 1600px)'\n};\nvar subscribers = new Map();\nvar subUid = -1;\nvar screens = {};\nvar responsiveObserve = {\n  matchHandlers: {},\n  dispatch: function dispatch(pointMap) {\n    screens = pointMap;\n    subscribers.forEach(function (func) {\n      return func(screens);\n    });\n    return subscribers.size >= 1;\n  },\n  subscribe: function subscribe(func) {\n    if (!subscribers.size) this.register();\n    subUid += 1;\n    subscribers.set(subUid, func);\n    func(screens);\n    return subUid;\n  },\n  unsubscribe: function unsubscribe(token) {\n    subscribers[\"delete\"](token);\n    if (!subscribers.size) this.unregister();\n  },\n  unregister: function unregister() {\n    var _this = this;\n    Object.keys(responsiveMap).forEach(function (screen) {\n      var matchMediaQuery = responsiveMap[screen];\n      var handler = _this.matchHandlers[matchMediaQuery];\n      handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);\n    });\n    subscribers.clear();\n  },\n  register: function register() {\n    var _this2 = this;\n    Object.keys(responsiveMap).forEach(function (screen) {\n      var matchMediaQuery = responsiveMap[screen];\n      var listener = function listener(_ref) {\n        var matches = _ref.matches;\n        _this2.dispatch(_extends(_extends({}, screens), _defineProperty({}, screen, matches)));\n      };\n      var mql = window.matchMedia(matchMediaQuery);\n      mql.addListener(listener);\n      _this2.matchHandlers[matchMediaQuery] = {\n        mql: mql,\n        listener: listener\n      };\n      listener(mql);\n    });\n  }\n};\nexport default responsiveObserve;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,IAAIC,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAClE,OAAO,IAAIC,aAAa,GAAG;EACzBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,qBAAqB;EACzBC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,OAAO,GAAG,CAAC,CAAC;AAChB,IAAIC,iBAAiB,GAAG;EACtBC,aAAa,EAAE,CAAC,CAAC;EACjBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,QAAQ,EAAE;IACpCJ,OAAO,GAAGI,QAAQ;IAClBP,WAAW,CAACQ,OAAO,CAAC,UAAUC,IAAI,EAAE;MAClC,OAAOA,IAAI,CAACN,OAAO,CAAC;IACtB,CAAC,CAAC;IACF,OAAOH,WAAW,CAACU,IAAI,IAAI,CAAC;EAC9B,CAAC;EACDC,SAAS,EAAE,SAASA,SAASA,CAACF,IAAI,EAAE;IAClC,IAAI,CAACT,WAAW,CAACU,IAAI,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC;IACtCV,MAAM,IAAI,CAAC;IACXF,WAAW,CAACa,GAAG,CAACX,MAAM,EAAEO,IAAI,CAAC;IAC7BA,IAAI,CAACN,OAAO,CAAC;IACb,OAAOD,MAAM;EACf,CAAC;EACDY,WAAW,EAAE,SAASA,WAAWA,CAACC,KAAK,EAAE;IACvCf,WAAW,CAAC,QAAQ,CAAC,CAACe,KAAK,CAAC;IAC5B,IAAI,CAACf,WAAW,CAACU,IAAI,EAAE,IAAI,CAACM,UAAU,CAAC,CAAC;EAC1C,CAAC;EACDA,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAIC,KAAK,GAAG,IAAI;IAChBC,MAAM,CAACC,IAAI,CAAC1B,aAAa,CAAC,CAACe,OAAO,CAAC,UAAUY,MAAM,EAAE;MACnD,IAAIC,eAAe,GAAG5B,aAAa,CAAC2B,MAAM,CAAC;MAC3C,IAAIE,OAAO,GAAGL,KAAK,CAACZ,aAAa,CAACgB,eAAe,CAAC;MAClDC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,GAAG,CAACC,cAAc,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,QAAQ,CAAC;IAClJ,CAAC,CAAC;IACFzB,WAAW,CAAC0B,KAAK,CAAC,CAAC;EACrB,CAAC;EACDd,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,IAAIe,MAAM,GAAG,IAAI;IACjBT,MAAM,CAACC,IAAI,CAAC1B,aAAa,CAAC,CAACe,OAAO,CAAC,UAAUY,MAAM,EAAE;MACnD,IAAIC,eAAe,GAAG5B,aAAa,CAAC2B,MAAM,CAAC;MAC3C,IAAIK,QAAQ,GAAG,SAASA,QAAQA,CAACG,IAAI,EAAE;QACrC,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;QAC1BF,MAAM,CAACrB,QAAQ,CAACf,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEY,OAAO,CAAC,EAAEb,eAAe,CAAC,CAAC,CAAC,EAAE8B,MAAM,EAAES,OAAO,CAAC,CAAC,CAAC;MACxF,CAAC;MACD,IAAIN,GAAG,GAAGO,MAAM,CAACC,UAAU,CAACV,eAAe,CAAC;MAC5CE,GAAG,CAACS,WAAW,CAACP,QAAQ,CAAC;MACzBE,MAAM,CAACtB,aAAa,CAACgB,eAAe,CAAC,GAAG;QACtCE,GAAG,EAAEA,GAAG;QACRE,QAAQ,EAAEA;MACZ,CAAC;MACDA,QAAQ,CAACF,GAAG,CAAC;IACf,CAAC,CAAC;EACJ;AACF,CAAC;AACD,eAAenB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}