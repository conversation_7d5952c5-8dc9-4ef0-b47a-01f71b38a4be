{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hclLong = exports.default = void 0;\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\nvar _color = _interopRequireWildcard(require(\"./color.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction hcl(hue) {\n  return function (start, end) {\n    var h = hue((start = (0, _index.hcl)(start)).h, (end = (0, _index.hcl)(end)).h),\n      c = (0, _color.default)(start.c, end.c),\n      l = (0, _color.default)(start.l, end.l),\n      opacity = (0, _color.default)(start.opacity, end.opacity);\n    return function (t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  };\n}\nvar _default = hcl(_color.hue);\nexports.default = _default;\nvar hclLong = hcl(_color.default);\nexports.hclLong = hclLong;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "hclLong", "default", "_index", "require", "_color", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "hcl", "hue", "start", "end", "h", "c", "l", "opacity", "t", "_default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/hcl.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hclLong = exports.default = void 0;\n\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\n\nvar _color = _interopRequireWildcard(require(\"./color.js\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction hcl(hue) {\n  return function (start, end) {\n    var h = hue((start = (0, _index.hcl)(start)).h, (end = (0, _index.hcl)(end)).h),\n        c = (0, _color.default)(start.c, end.c),\n        l = (0, _color.default)(start.l, end.l),\n        opacity = (0, _color.default)(start.opacity, end.opacity);\n    return function (t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  };\n}\n\nvar _default = hcl(_color.hue);\n\nexports.default = _default;\nvar hclLong = hcl(_color.default);\nexports.hclLong = hclLong;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE1C,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,MAAM,GAAGC,uBAAuB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAE3D,SAASG,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASF,uBAAuBA,CAACM,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEV,OAAO,EAAEU;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGrB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACsB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIvB,MAAM,CAACwB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGrB,MAAM,CAACsB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE5B,MAAM,CAACC,cAAc,CAACmB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACf,OAAO,GAAGU,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASS,GAAGA,CAACC,GAAG,EAAE;EAChB,OAAO,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC3B,IAAIC,CAAC,GAAGH,GAAG,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEzB,MAAM,CAACuB,GAAG,EAAEE,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAG,CAAC,CAAC,EAAE1B,MAAM,CAACuB,GAAG,EAAEG,GAAG,CAAC,EAAEC,CAAC,CAAC;MAC3EC,CAAC,GAAG,CAAC,CAAC,EAAE1B,MAAM,CAACH,OAAO,EAAE0B,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;MACvCC,CAAC,GAAG,CAAC,CAAC,EAAE3B,MAAM,CAACH,OAAO,EAAE0B,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;MACvCC,OAAO,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAACH,OAAO,EAAE0B,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;IAC7D,OAAO,UAAUC,CAAC,EAAE;MAClBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;MACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;MACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;MACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;MAC1B,OAAON,KAAK,GAAG,EAAE;IACnB,CAAC;EACH,CAAC;AACH;AAEA,IAAIO,QAAQ,GAAGT,GAAG,CAACrB,MAAM,CAACsB,GAAG,CAAC;AAE9B5B,OAAO,CAACG,OAAO,GAAGiC,QAAQ;AAC1B,IAAIlC,OAAO,GAAGyB,GAAG,CAACrB,MAAM,CAACH,OAAO,CAAC;AACjCH,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}