{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.InternSet = exports.InternMap = void 0;\nclass InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\nexports.InternMap = InternMap;\nclass InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\nexports.InternSet = InternSet;\nfunction intern_get({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\nfunction intern_set({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\nfunction intern_delete({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "InternSet", "InternMap", "Map", "constructor", "entries", "key", "keyof", "defineProperties", "_intern", "_key", "set", "get", "intern_get", "has", "intern_set", "delete", "intern_delete", "Set", "values", "add", "valueOf"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/internmap/src/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.InternSet = exports.InternMap = void 0;\n\nclass InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n\n}\n\nexports.InternMap = InternMap;\n\nclass InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n\n}\n\nexports.InternSet = InternSet;\n\nfunction intern_get({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  if (_intern.has(key)) return _intern.get(key);\n\n  _intern.set(key, value);\n\n  return value;\n}\n\nfunction intern_delete({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n\n    _intern.delete(key);\n  }\n\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,SAAS,GAAG,KAAK,CAAC;AAE9C,MAAMA,SAAS,SAASC,GAAG,CAAC;EAC1BC,WAAWA,CAACC,OAAO,EAAEC,GAAG,GAAGC,KAAK,EAAE;IAChC,KAAK,CAAC,CAAC;IACPV,MAAM,CAACW,gBAAgB,CAAC,IAAI,EAAE;MAC5BC,OAAO,EAAE;QACPT,KAAK,EAAE,IAAIG,GAAG,CAAC;MACjB,CAAC;MACDO,IAAI,EAAE;QACJV,KAAK,EAAEM;MACT;IACF,CAAC,CAAC;IACF,IAAID,OAAO,IAAI,IAAI,EAAE,KAAK,MAAM,CAACC,GAAG,EAAEN,KAAK,CAAC,IAAIK,OAAO,EAAE,IAAI,CAACM,GAAG,CAACL,GAAG,EAAEN,KAAK,CAAC;EAC/E;EAEAY,GAAGA,CAACN,GAAG,EAAE;IACP,OAAO,KAAK,CAACM,GAAG,CAACC,UAAU,CAAC,IAAI,EAAEP,GAAG,CAAC,CAAC;EACzC;EAEAQ,GAAGA,CAACR,GAAG,EAAE;IACP,OAAO,KAAK,CAACQ,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEP,GAAG,CAAC,CAAC;EACzC;EAEAK,GAAGA,CAACL,GAAG,EAAEN,KAAK,EAAE;IACd,OAAO,KAAK,CAACW,GAAG,CAACI,UAAU,CAAC,IAAI,EAAET,GAAG,CAAC,EAAEN,KAAK,CAAC;EAChD;EAEAgB,MAAMA,CAACV,GAAG,EAAE;IACV,OAAO,KAAK,CAACU,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEX,GAAG,CAAC,CAAC;EAC/C;AAEF;AAEAP,OAAO,CAACG,SAAS,GAAGA,SAAS;AAE7B,MAAMD,SAAS,SAASiB,GAAG,CAAC;EAC1Bd,WAAWA,CAACe,MAAM,EAAEb,GAAG,GAAGC,KAAK,EAAE;IAC/B,KAAK,CAAC,CAAC;IACPV,MAAM,CAACW,gBAAgB,CAAC,IAAI,EAAE;MAC5BC,OAAO,EAAE;QACPT,KAAK,EAAE,IAAIG,GAAG,CAAC;MACjB,CAAC;MACDO,IAAI,EAAE;QACJV,KAAK,EAAEM;MACT;IACF,CAAC,CAAC;IACF,IAAIa,MAAM,IAAI,IAAI,EAAE,KAAK,MAAMnB,KAAK,IAAImB,MAAM,EAAE,IAAI,CAACC,GAAG,CAACpB,KAAK,CAAC;EACjE;EAEAc,GAAGA,CAACd,KAAK,EAAE;IACT,OAAO,KAAK,CAACc,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEb,KAAK,CAAC,CAAC;EAC3C;EAEAoB,GAAGA,CAACpB,KAAK,EAAE;IACT,OAAO,KAAK,CAACoB,GAAG,CAACL,UAAU,CAAC,IAAI,EAAEf,KAAK,CAAC,CAAC;EAC3C;EAEAgB,MAAMA,CAAChB,KAAK,EAAE;IACZ,OAAO,KAAK,CAACgB,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEjB,KAAK,CAAC,CAAC;EACjD;AAEF;AAEAD,OAAO,CAACE,SAAS,GAAGA,SAAS;AAE7B,SAASY,UAAUA,CAAC;EAClBJ,OAAO;EACPC;AACF,CAAC,EAAEV,KAAK,EAAE;EACR,MAAMM,GAAG,GAAGI,IAAI,CAACV,KAAK,CAAC;EAEvB,OAAOS,OAAO,CAACK,GAAG,CAACR,GAAG,CAAC,GAAGG,OAAO,CAACG,GAAG,CAACN,GAAG,CAAC,GAAGN,KAAK;AACpD;AAEA,SAASe,UAAUA,CAAC;EAClBN,OAAO;EACPC;AACF,CAAC,EAAEV,KAAK,EAAE;EACR,MAAMM,GAAG,GAAGI,IAAI,CAACV,KAAK,CAAC;EAEvB,IAAIS,OAAO,CAACK,GAAG,CAACR,GAAG,CAAC,EAAE,OAAOG,OAAO,CAACG,GAAG,CAACN,GAAG,CAAC;EAE7CG,OAAO,CAACE,GAAG,CAACL,GAAG,EAAEN,KAAK,CAAC;EAEvB,OAAOA,KAAK;AACd;AAEA,SAASiB,aAAaA,CAAC;EACrBR,OAAO;EACPC;AACF,CAAC,EAAEV,KAAK,EAAE;EACR,MAAMM,GAAG,GAAGI,IAAI,CAACV,KAAK,CAAC;EAEvB,IAAIS,OAAO,CAACK,GAAG,CAACR,GAAG,CAAC,EAAE;IACpBN,KAAK,GAAGS,OAAO,CAACG,GAAG,CAACN,GAAG,CAAC;IAExBG,OAAO,CAACO,MAAM,CAACV,GAAG,CAAC;EACrB;EAEA,OAAON,KAAK;AACd;AAEA,SAASO,KAAKA,CAACP,KAAK,EAAE;EACpB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACqB,OAAO,CAAC,CAAC,GAAGrB,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "script"}