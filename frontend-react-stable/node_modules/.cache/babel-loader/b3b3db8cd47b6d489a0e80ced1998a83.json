{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport DeliveredProcedureOutlinedSvg from \"@ant-design/icons-svg/es/asn/DeliveredProcedureOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar DeliveredProcedureOutlined = function DeliveredProcedureOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: DeliveredProcedureOutlinedSvg\n  }));\n};\nDeliveredProcedureOutlined.displayName = 'DeliveredProcedureOutlined';\nexport default /*#__PURE__*/React.forwardRef(DeliveredProcedureOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "DeliveredProcedureOutlinedSvg", "AntdIcon", "DeliveredProcedureOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/DeliveredProcedureOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport DeliveredProcedureOutlinedSvg from \"@ant-design/icons-svg/es/asn/DeliveredProcedureOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar DeliveredProcedureOutlined = function DeliveredProcedureOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: DeliveredProcedureOutlinedSvg\n  }));\n};\nDeliveredProcedureOutlined.displayName = 'DeliveredProcedureOutlined';\nexport default /*#__PURE__*/React.forwardRef(DeliveredProcedureOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,0BAA0B,GAAG,SAASA,0BAA0BA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/E,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,0BAA0B,CAACK,WAAW,GAAG,4BAA4B;AACrE,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}