{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { isPresetColor } from './utils';\nvar Ribbon = function Ribbon(_ref) {\n  var _classNames;\n  var className = _ref.className,\n    customizePrefixCls = _ref.prefixCls,\n    style = _ref.style,\n    color = _ref.color,\n    children = _ref.children,\n    text = _ref.text,\n    _ref$placement = _ref.placement,\n    placement = _ref$placement === void 0 ? 'end' : _ref$placement;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n  var colorInPreset = isPresetColor(color);\n  var ribbonCls = classNames(prefixCls, \"\".concat(prefixCls, \"-placement-\").concat(placement), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-color-\").concat(color), colorInPreset), _classNames), className);\n  var colorStyle = {};\n  var cornerColorStyle = {};\n  if (color && !colorInPreset) {\n    colorStyle.background = color;\n    cornerColorStyle.color = color;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-wrapper\")\n  }, children, /*#__PURE__*/React.createElement(\"div\", {\n    className: ribbonCls,\n    style: _extends(_extends({}, colorStyle), style)\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-text\")\n  }, text), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-corner\"),\n    style: cornerColorStyle\n  })));\n};\nexport default Ribbon;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "React", "ConfigContext", "isPresetColor", "Ribbon", "_ref", "_classNames", "className", "customizePrefixCls", "prefixCls", "style", "color", "children", "text", "_ref$placement", "placement", "_React$useContext", "useContext", "getPrefixCls", "direction", "colorInPreset", "ribbonCls", "concat", "colorStyle", "cornerColorStyle", "background", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/badge/Ribbon.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { isPresetColor } from './utils';\nvar Ribbon = function Ribbon(_ref) {\n  var _classNames;\n  var className = _ref.className,\n    customizePrefixCls = _ref.prefixCls,\n    style = _ref.style,\n    color = _ref.color,\n    children = _ref.children,\n    text = _ref.text,\n    _ref$placement = _ref.placement,\n    placement = _ref$placement === void 0 ? 'end' : _ref$placement;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n  var colorInPreset = isPresetColor(color);\n  var ribbonCls = classNames(prefixCls, \"\".concat(prefixCls, \"-placement-\").concat(placement), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-color-\").concat(color), colorInPreset), _classNames), className);\n  var colorStyle = {};\n  var cornerColorStyle = {};\n  if (color && !colorInPreset) {\n    colorStyle.background = color;\n    cornerColorStyle.color = color;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-wrapper\")\n  }, children, /*#__PURE__*/React.createElement(\"div\", {\n    className: ribbonCls,\n    style: _extends(_extends({}, colorStyle), style)\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-text\")\n  }, text), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-corner\"),\n    style: cornerColorStyle\n  })));\n};\nexport default Ribbon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,aAAa,QAAQ,SAAS;AACvC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,kBAAkB,GAAGH,IAAI,CAACI,SAAS;IACnCC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,IAAI,GAAGR,IAAI,CAACQ,IAAI;IAChBC,cAAc,GAAGT,IAAI,CAACU,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;EAChE,IAAIE,iBAAiB,GAAGf,KAAK,CAACgB,UAAU,CAACf,aAAa,CAAC;IACrDgB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIV,SAAS,GAAGS,YAAY,CAAC,QAAQ,EAAEV,kBAAkB,CAAC;EAC1D,IAAIY,aAAa,GAAGjB,aAAa,CAACQ,KAAK,CAAC;EACxC,IAAIU,SAAS,GAAGrB,UAAU,CAACS,SAAS,EAAE,EAAE,CAACa,MAAM,CAACb,SAAS,EAAE,aAAa,CAAC,CAACa,MAAM,CAACP,SAAS,CAAC,GAAGT,WAAW,GAAG,CAAC,CAAC,EAAEP,eAAe,CAACO,WAAW,EAAE,EAAE,CAACgB,MAAM,CAACb,SAAS,EAAE,MAAM,CAAC,EAAEU,SAAS,KAAK,KAAK,CAAC,EAAEpB,eAAe,CAACO,WAAW,EAAE,EAAE,CAACgB,MAAM,CAACb,SAAS,EAAE,SAAS,CAAC,CAACa,MAAM,CAACX,KAAK,CAAC,EAAES,aAAa,CAAC,EAAEd,WAAW,GAAGC,SAAS,CAAC;EACrT,IAAIgB,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIb,KAAK,IAAI,CAACS,aAAa,EAAE;IAC3BG,UAAU,CAACE,UAAU,GAAGd,KAAK;IAC7Ba,gBAAgB,CAACb,KAAK,GAAGA,KAAK;EAChC;EACA,OAAO,aAAaV,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAC7CnB,SAAS,EAAE,EAAE,CAACe,MAAM,CAACb,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEG,QAAQ,EAAE,aAAaX,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IACnDnB,SAAS,EAAEc,SAAS;IACpBX,KAAK,EAAEZ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,UAAU,CAAC,EAAEb,KAAK;EACjD,CAAC,EAAE,aAAaT,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;IAC1CnB,SAAS,EAAE,EAAE,CAACe,MAAM,CAACb,SAAS,EAAE,OAAO;EACzC,CAAC,EAAEI,IAAI,CAAC,EAAE,aAAaZ,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAChDnB,SAAS,EAAE,EAAE,CAACe,MAAM,CAACb,SAAS,EAAE,SAAS,CAAC;IAC1CC,KAAK,EAAEc;EACT,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAepB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}