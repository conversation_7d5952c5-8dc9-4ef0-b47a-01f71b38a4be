{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _math = require(\"../math.js\");\nconst ka = 0.89081309152928522810;\nconst kr = (0, _math.sin)(_math.pi / 10) / (0, _math.sin)(7 * _math.pi / 10);\nconst kx = (0, _math.sin)(_math.tau / 10) * kr;\nconst ky = -(0, _math.cos)(_math.tau / 10) * kr;\nvar _default = {\n  draw(context, size) {\n    const r = (0, _math.sqrt)(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (let i = 1; i < 5; ++i) {\n      const a = _math.tau * i / 5;\n      const c = (0, _math.cos)(a);\n      const s = (0, _math.sin)(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_math", "require", "ka", "kr", "sin", "pi", "kx", "tau", "ky", "cos", "_default", "draw", "context", "size", "r", "sqrt", "x", "y", "moveTo", "lineTo", "i", "a", "c", "s", "closePath"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/symbol/star.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _math = require(\"../math.js\");\n\nconst ka = 0.89081309152928522810;\nconst kr = (0, _math.sin)(_math.pi / 10) / (0, _math.sin)(7 * _math.pi / 10);\nconst kx = (0, _math.sin)(_math.tau / 10) * kr;\nconst ky = -(0, _math.cos)(_math.tau / 10) * kr;\nvar _default = {\n  draw(context, size) {\n    const r = (0, _math.sqrt)(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n\n    for (let i = 1; i < 5; ++i) {\n      const a = _math.tau * i / 5;\n      const c = (0, _math.cos)(a);\n      const s = (0, _math.sin)(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n\n    context.closePath();\n  }\n\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,MAAMC,EAAE,GAAG,sBAAsB;AACjC,MAAMC,EAAE,GAAG,CAAC,CAAC,EAAEH,KAAK,CAACI,GAAG,EAAEJ,KAAK,CAACK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACI,GAAG,EAAE,CAAC,GAAGJ,KAAK,CAACK,EAAE,GAAG,EAAE,CAAC;AAC5E,MAAMC,EAAE,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACI,GAAG,EAAEJ,KAAK,CAACO,GAAG,GAAG,EAAE,CAAC,GAAGJ,EAAE;AAC9C,MAAMK,EAAE,GAAG,CAAC,CAAC,CAAC,EAAER,KAAK,CAACS,GAAG,EAAET,KAAK,CAACO,GAAG,GAAG,EAAE,CAAC,GAAGJ,EAAE;AAC/C,IAAIO,QAAQ,GAAG;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAG,CAAC,CAAC,EAAEd,KAAK,CAACe,IAAI,EAAEF,IAAI,GAAGX,EAAE,CAAC;IACpC,MAAMc,CAAC,GAAGV,EAAE,GAAGQ,CAAC;IAChB,MAAMG,CAAC,GAAGT,EAAE,GAAGM,CAAC;IAChBF,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAACJ,CAAC,CAAC;IACrBF,OAAO,CAACO,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;IAEpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,MAAMC,CAAC,GAAGrB,KAAK,CAACO,GAAG,GAAGa,CAAC,GAAG,CAAC;MAC3B,MAAME,CAAC,GAAG,CAAC,CAAC,EAAEtB,KAAK,CAACS,GAAG,EAAEY,CAAC,CAAC;MAC3B,MAAME,CAAC,GAAG,CAAC,CAAC,EAAEvB,KAAK,CAACI,GAAG,EAAEiB,CAAC,CAAC;MAC3BT,OAAO,CAACO,MAAM,CAACI,CAAC,GAAGT,CAAC,EAAE,CAACQ,CAAC,GAAGR,CAAC,CAAC;MAC7BF,OAAO,CAACO,MAAM,CAACG,CAAC,GAAGN,CAAC,GAAGO,CAAC,GAAGN,CAAC,EAAEM,CAAC,GAAGP,CAAC,GAAGM,CAAC,GAAGL,CAAC,CAAC;IAC9C;IAEAL,OAAO,CAACY,SAAS,CAAC,CAAC;EACrB;AAEF,CAAC;AACD3B,OAAO,CAACE,OAAO,GAAGW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}