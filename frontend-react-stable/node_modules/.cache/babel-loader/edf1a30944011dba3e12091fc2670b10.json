{"ast": null, "code": "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n  rsComboMarksRange = '\\\\u0300-\\\\u036f',\n  reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n  rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n  rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n  rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\nmodule.exports = hasUnicode;", "map": {"version": 3, "names": ["rsAstralRange", "rsComboMarksRange", "reComboHalfMarksRange", "rsComboSymbolsRange", "rsComboRange", "rsVarRange", "rsZWJ", "reHasUnicode", "RegExp", "hasUnicode", "string", "test", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_hasUnicode.js"], "sourcesContent": ["/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\n\nmodule.exports = hasUnicode;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,iBAAiB;EACjCC,iBAAiB,GAAG,iBAAiB;EACrCC,qBAAqB,GAAG,iBAAiB;EACzCC,mBAAmB,GAAG,iBAAiB;EACvCC,YAAY,GAAGH,iBAAiB,GAAGC,qBAAqB,GAAGC,mBAAmB;EAC9EE,UAAU,GAAG,gBAAgB;;AAEjC;AACA,IAAIC,KAAK,GAAG,SAAS;;AAErB;AACA,IAAIC,YAAY,GAAGC,MAAM,CAAC,GAAG,GAAGF,KAAK,GAAGN,aAAa,GAAII,YAAY,GAAGC,UAAU,GAAG,GAAG,CAAC;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOH,YAAY,CAACI,IAAI,CAACD,MAAM,CAAC;AAClC;AAEAE,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}