{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar Text = function Text(_a, ref) {\n  var ellipsis = _a.ellipsis,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  var mergedEllipsis = React.useMemo(function () {\n    if (ellipsis && _typeof(ellipsis) === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  process.env.NODE_ENV !== \"production\" ? warning(_typeof(ellipsis) !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'Typography.Text', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "omit", "React", "warning", "Base", "Text", "_a", "ref", "ellipsis", "restProps", "mergedEllipsis", "useMemo", "process", "env", "NODE_ENV", "createElement", "component", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Text.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar Text = function Text(_a, ref) {\n  var ellipsis = _a.ellipsis,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  var mergedEllipsis = React.useMemo(function () {\n    if (ellipsis && _typeof(ellipsis) === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  process.env.NODE_ENV !== \"production\" ? warning(_typeof(ellipsis) !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'Typography.Text', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAChC,IAAIC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;IACxBC,SAAS,GAAGtB,MAAM,CAACmB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACtC,IAAII,cAAc,GAAGR,KAAK,CAACS,OAAO,CAAC,YAAY;IAC7C,IAAIH,QAAQ,IAAItB,OAAO,CAACsB,QAAQ,CAAC,KAAK,QAAQ,EAAE;MAC9C,OAAOP,IAAI,CAACO,QAAQ,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC/C;IACA,OAAOA,QAAQ;EACjB,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,OAAO,CAACjB,OAAO,CAACsB,QAAQ,CAAC,KAAK,QAAQ,IAAI,CAACA,QAAQ,IAAI,EAAE,YAAY,IAAIA,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAIA,QAAQ,CAAC,EAAE,iBAAiB,EAAE,yDAAyD,CAAC,GAAG,KAAK,CAAC;EAC3O,OAAO,aAAaN,KAAK,CAACa,aAAa,CAACX,IAAI,EAAEnB,QAAQ,CAAC;IACrDsB,GAAG,EAAEA;EACP,CAAC,EAAEE,SAAS,EAAE;IACZD,QAAQ,EAAEE,cAAc;IACxBM,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAad,KAAK,CAACe,UAAU,CAACZ,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}