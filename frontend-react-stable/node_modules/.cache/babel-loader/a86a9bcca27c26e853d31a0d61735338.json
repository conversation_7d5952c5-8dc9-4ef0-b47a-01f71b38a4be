{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nfunction Linear(context) {\n  this._context = context;\n}\nLinear.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // falls through\n\n      default:\n        this._context.lineTo(x, y);\n        break;\n    }\n  }\n};\nfunction _default(context) {\n  return new Linear(context);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "Linear", "context", "_context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_point", "lineEnd", "closePath", "point", "x", "y", "lineTo", "moveTo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/curve/linear.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nfunction Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n\n      case 1:\n        this._point = 2;\n      // falls through\n\n      default:\n        this._context.lineTo(x, y);\n\n        break;\n    }\n  }\n};\n\nfunction _default(context) {\n  return new Linear(context);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASC,MAAMA,CAACC,OAAO,EAAE;EACvB,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,MAAM,CAACG,SAAS,GAAG;EACjBC,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,IAAI,CAACL,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE,IAAI,CAACP,QAAQ,CAACS,SAAS,CAAC,CAAC;IAClF,IAAI,CAACN,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDO,KAAK,EAAE,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IACrBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IAEd,QAAQ,IAAI,CAACL,MAAM;MACjB,KAAK,CAAC;QACJ,IAAI,CAACA,MAAM,GAAG,CAAC;QACf,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACa,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACZ,QAAQ,CAACc,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;QACpE;MAEF,KAAK,CAAC;QACJ,IAAI,CAACL,MAAM,GAAG,CAAC;MACjB;;MAEA;QACE,IAAI,CAACP,QAAQ,CAACa,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;QAE1B;IACJ;EACF;AACF,CAAC;AAED,SAASf,QAAQA,CAACE,OAAO,EAAE;EACzB,OAAO,IAAID,MAAM,CAACC,OAAO,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}