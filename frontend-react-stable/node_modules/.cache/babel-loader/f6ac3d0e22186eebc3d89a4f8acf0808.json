{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    containerRef = props.containerRef;\n  // =============================== Render ===============================\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: _objectSpread({}, style),\n    \"aria-modal\": \"true\",\n    role: \"dialog\",\n    ref: containerRef\n  }, children));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;", "map": {"version": 3, "names": ["_objectSpread", "React", "classNames", "<PERSON>er<PERSON><PERSON><PERSON>", "props", "prefixCls", "className", "style", "children", "containerRef", "createElement", "Fragment", "concat", "role", "ref", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-drawer/es/DrawerPanel.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    containerRef = props.containerRef;\n  // =============================== Render ===============================\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: _objectSpread({}, style),\n    \"aria-modal\": \"true\",\n    role: \"dialog\",\n    ref: containerRef\n  }, children));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,YAAY,GAAGL,KAAK,CAACK,YAAY;EACnC;EACA,OAAO,aAAaR,KAAK,CAACS,aAAa,CAACT,KAAK,CAACU,QAAQ,EAAE,IAAI,EAAE,aAAaV,KAAK,CAACS,aAAa,CAAC,KAAK,EAAE;IACpGJ,SAAS,EAAEJ,UAAU,CAAC,EAAE,CAACU,MAAM,CAACP,SAAS,EAAE,UAAU,CAAC,EAAEC,SAAS,CAAC;IAClEC,KAAK,EAAEP,aAAa,CAAC,CAAC,CAAC,EAAEO,KAAK,CAAC;IAC/B,YAAY,EAAE,MAAM;IACpBM,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAEL;EACP,CAAC,EAAED,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCd,WAAW,CAACe,WAAW,GAAG,aAAa;AACzC;AACA,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}