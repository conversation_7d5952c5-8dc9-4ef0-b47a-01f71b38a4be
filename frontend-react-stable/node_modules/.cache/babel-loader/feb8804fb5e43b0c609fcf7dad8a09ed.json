{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport YuqueOutlinedSvg from \"@ant-design/icons-svg/es/asn/YuqueOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar YuqueOutlined = function YuqueOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: YuqueOutlinedSvg\n  }));\n};\nYuqueOutlined.displayName = 'YuqueOutlined';\nexport default /*#__PURE__*/React.forwardRef(YuqueOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "YuqueOutlinedSvg", "AntdIcon", "YuqueOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/YuqueOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport YuqueOutlinedSvg from \"@ant-design/icons-svg/es/asn/YuqueOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar YuqueOutlined = function YuqueOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: YuqueOutlinedSvg\n  }));\n};\nYuqueOutlined.displayName = 'YuqueOutlined';\nexport default /*#__PURE__*/React.forwardRef(YuqueOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,aAAa,CAACK,WAAW,GAAG,eAAe;AAC3C,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}