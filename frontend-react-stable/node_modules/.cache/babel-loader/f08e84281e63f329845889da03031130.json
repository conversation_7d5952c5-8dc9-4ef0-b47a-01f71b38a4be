{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.months = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar month = (0, _interval.default)(function (date) {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setMonth(date.getMonth() + step);\n}, function (start, end) {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, function (date) {\n  return date.getMonth();\n});\nvar _default = month;\nexports.default = _default;\nvar months = month.range;\nexports.months = months;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "months", "default", "_interval", "_interopRequireDefault", "require", "obj", "__esModule", "month", "date", "setDate", "setHours", "step", "setMonth", "getMonth", "start", "end", "getFullYear", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/month.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.months = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar month = (0, _interval.default)(function (date) {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setMonth(date.getMonth() + step);\n}, function (start, end) {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, function (date) {\n  return date.getMonth();\n});\nvar _default = month;\nexports.default = _default;\nvar months = month.range;\nexports.months = months;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAEzC,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;EACjDA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACfD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;EACvBH,IAAI,CAACI,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAGF,IAAI,CAAC;AACvC,CAAC,EAAE,UAAUG,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACF,QAAQ,CAAC,CAAC,GAAGC,KAAK,CAACD,QAAQ,CAAC,CAAC,GAAG,CAACE,GAAG,CAACC,WAAW,CAAC,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC,IAAI,EAAE;AAC3F,CAAC,EAAE,UAAUR,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACK,QAAQ,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,IAAII,QAAQ,GAAGV,KAAK;AACpBT,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,MAAM,GAAGO,KAAK,CAACW,KAAK;AACxBpB,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}