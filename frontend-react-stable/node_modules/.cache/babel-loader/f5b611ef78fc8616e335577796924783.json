{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Typography, Card, Alert, Form, Input, Button, message, Space, Collapse, Table, Tag, Modal, Row, Col } from 'antd';\nimport { LockOutlined, UserAddOutlined, UserOutlined, EyeInvisibleOutlined, EyeTwoTone, TeamOutlined, SafetyOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Panel\n} = Collapse;\nconst {\n  confirm\n} = Modal;\nconst UserManagementPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const currentUser = (user === null || user === void 0 ? void 0 : user.username) || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token]);\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.getUsers(token);\n      if (response.data.users) {\n        setUsers(response.data.users);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n    }\n  };\n\n  // 修改密码\n  const handleChangePassword = async values => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async values => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_, value) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 6) {\n      return Promise.reject(new Error('密码长度至少6位'));\n    }\n    if (!/(?=.*[a-zA-Z])(?=.*\\d)/.test(value)) {\n      return Promise.reject(new Error('密码必须包含字母和数字'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_, value) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    render: username => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '角色',\n    dataIndex: 'is_admin',\n    key: 'is_admin',\n    render: isAdmin => /*#__PURE__*/_jsxDEV(Tag, {\n      color: isAdmin ? 'red' : 'blue',\n      children: isAdmin ? '管理员' : '普通用户'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_time',\n    key: 'created_time',\n    render: time => time ? new Date(time).toLocaleString() : 'N/A'\n  }, {\n    title: '最后登录',\n    dataIndex: 'last_login',\n    key: 'last_login',\n    render: time => time ? new Date(time).toLocaleString() : '从未登录'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), \"\\u7528\\u6237\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4FEE\\u6539\\u5BC6\\u7801\\u3001\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u7B49\\u7528\\u6237\\u7BA1\\u7406\\u529F\\u80FD\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this),\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",\n            description: \"\\u4E3A\\u4E86\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u4FEE\\u6539\\u5BC6\\u7801\\u3002\\u65B0\\u5BC6\\u7801\\u5E94\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF0C\\u957F\\u5EA6\\u81F3\\u5C116\\u4F4D\\u3002\",\n            type: \"info\",\n            showIcon: true,\n            style: {\n              marginBottom: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: changePasswordForm,\n            layout: \"vertical\",\n            onFinish: handleChangePassword,\n            style: {\n              maxWidth: 600\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5F53\\u524D\\u7528\\u6237\",\n              name: \"current_user\",\n              initialValue: currentUser,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 27\n                }, this),\n                disabled: true,\n                value: currentUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u539F\\u5BC6\\u7801\",\n              name: \"old_password\",\n              rules: [{\n                required: true,\n                message: '请输入原密码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u539F\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u65B0\\u5BC6\\u7801\",\n              name: \"new_password\",\n              rules: [{\n                validator: validatePassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",\n              name: \"confirm_password\",\n              rules: [{\n                validator: validateConfirmPassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 25\n                }, this),\n                size: \"large\",\n                children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"red\",\n              children: \"\\u7BA1\\u7406\\u5458\\u4E13\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this),\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Collapse, {\n            defaultActiveKey: ['1'],\n            ghost: true,\n            children: [/*#__PURE__*/_jsxDEV(Panel, {\n              header: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: addUserForm,\n                layout: \"vertical\",\n                onFinish: handleAddUser,\n                style: {\n                  maxWidth: 600\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u65B0\\u7528\\u6237\\u540D\",\n                  name: \"new_username\",\n                  rules: [{\n                    required: true,\n                    message: '请输入用户名'\n                  }, {\n                    min: 3,\n                    message: '用户名至少3位'\n                  }, {\n                    pattern: /^[a-zA-Z0-9_]+$/,\n                    message: '用户名只能包含字母、数字和下划线'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u540D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                  name: \"new_user_password\",\n                  rules: [{\n                    validator: validatePassword\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",\n                    iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 78\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u786E\\u8BA4\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                  name: \"confirm_user_password\",\n                  rules: [{\n                    validator: validateConfirmPassword\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                    iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 78\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    htmlType: \"submit\",\n                    loading: loading,\n                    icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 31\n                    }, this),\n                    size: \"large\",\n                    children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)\n            }, \"1\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u7528\\u6237\\u5217\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [users.length, \" \\u4E2A\\u7528\\u6237\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 16\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 29\n                  }, this),\n                  onClick: fetchUsers,\n                  loading: loading,\n                  children: \"\\u5237\\u65B0\\u7528\\u6237\\u5217\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Table, {\n                columns: userColumns,\n                dataSource: users,\n                rowKey: \"username\",\n                pagination: {\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: total => `共 ${total} 个用户`\n                },\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)]\n            }, \"2\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), !isAdmin && /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6743\\u9650\\u63D0\\u793A\",\n          description: \"\\u60A8\\u5F53\\u524D\\u662F\\u666E\\u901A\\u7528\\u6237\\uFF0C\\u53EA\\u80FD\\u4FEE\\u6539\\u81EA\\u5DF1\\u7684\\u5BC6\\u7801\\u3002\\u5982\\u9700\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u6216\\u67E5\\u770B\\u7528\\u6237\\u5217\\u8868\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u3002\",\n          type: \"warning\",\n          showIcon: true,\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagementPage, \"cXMbJukGmBU6iG5mzp771VjnGwI=\", false, function () {\n  return [Form.useForm, Form.useForm, useSelector];\n});\n_c = UserManagementPage;\nexport default UserManagementPage;\nvar _c;\n$RefreshReg$(_c, \"UserManagementPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "Card", "<PERSON><PERSON>", "Form", "Input", "<PERSON><PERSON>", "message", "Space", "Collapse", "Table", "Tag", "Modal", "Row", "Col", "LockOutlined", "UserAddOutlined", "UserOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "TeamOutlined", "SafetyOutlined", "ExclamationCircleOutlined", "useSelector", "authAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "Panel", "confirm", "UserManagementPage", "_s", "loading", "setLoading", "users", "setUsers", "changePasswordForm", "useForm", "addUserForm", "user", "token", "state", "auth", "currentUser", "username", "isAdmin", "fetchUsers", "localStorage", "getItem", "response", "getUsers", "data", "error", "_error$response", "_error$response$data", "console", "detail", "handleChangePassword", "values", "changePassword", "old_password", "new_password", "confirm_password", "success", "resetFields", "_error$response2", "_error$response2$data", "handleAddUser", "addUser", "new_username", "new_user_password", "confirm_user_password", "_error$response3", "_error$response3$data", "validatePassword", "_", "value", "Promise", "reject", "Error", "length", "test", "resolve", "validateConfirmPassword", "form", "passwordField", "password", "getFieldValue", "userColumns", "title", "dataIndex", "key", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "color", "time", "Date", "toLocaleString", "level", "type", "gutter", "style", "marginTop", "span", "size", "description", "showIcon", "marginBottom", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "initialValue", "prefix", "disabled", "rules", "required", "Password", "placeholder", "iconRender", "visible", "validator", "htmlType", "icon", "defaultActiveKey", "ghost", "header", "min", "pattern", "onClick", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Typography,\n  Card,\n  Alert,\n  Form,\n  Input,\n  Button,\n  message,\n  Space,\n  Divider,\n  Collapse,\n  Table,\n  Tag,\n  Modal,\n  Row,\n  Col\n} from 'antd';\nimport {\n  LockOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  TeamOutlined,\n  SafetyOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { authAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Panel } = Collapse;\nconst { confirm } = Modal;\n\ninterface User {\n  username: string;\n  is_admin: boolean;\n  created_time?: string;\n  last_login?: string;\n}\n\nconst UserManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState<User[]>([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const { user, token } = useSelector((state: RootState) => state.auth);\n  const currentUser = user?.username || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token]);\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.getUsers(token);\n      if (response.data.users) {\n        setUsers(response.data.users);\n      }\n    } catch (error: any) {\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 修改密码\n  const handleChangePassword = async (values: any) => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async (values: any) => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token') || '';\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error: any) {\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_: any, value: string) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 6) {\n      return Promise.reject(new Error('密码长度至少6位'));\n    }\n    if (!/(?=.*[a-zA-Z])(?=.*\\d)/.test(value)) {\n      return Promise.reject(new Error('密码必须包含字母和数字'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_: any, value: string) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (username: string) => (\n        <Space>\n          <UserOutlined />\n          <Text strong>{username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'is_admin',\n      key: 'is_admin',\n      render: (isAdmin: boolean) => (\n        <Tag color={isAdmin ? 'red' : 'blue'}>\n          {isAdmin ? '管理员' : '普通用户'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      render: (time: string) => time ? new Date(time).toLocaleString() : '从未登录',\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={2}>\n        <Space>\n          <TeamOutlined />\n          用户管理\n        </Space>\n      </Title>\n      <Text type=\"secondary\">\n        修改密码、添加新用户等用户管理功能。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* 修改密码 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <LockOutlined />\n                <span>修改密码</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Alert\n              message=\"密码安全提示\"\n              description=\"为了账户安全，建议定期修改密码。新密码应包含字母和数字，长度至少6位。\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n\n            <Form\n              form={changePasswordForm}\n              layout=\"vertical\"\n              onFinish={handleChangePassword}\n              style={{ maxWidth: 600 }}\n            >\n              <Form.Item\n                label=\"当前用户\"\n                name=\"current_user\"\n                initialValue={currentUser}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  disabled\n                  value={currentUser}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"原密码\"\n                name=\"old_password\"\n                rules={[{ required: true, message: '请输入原密码' }]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"请输入原密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"新密码\"\n                name=\"new_password\"\n                rules={[{ validator: validatePassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请输入新密码（至少6位，包含字母和数字）\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"确认新密码\"\n                name=\"confirm_password\"\n                rules={[{ validator: validateConfirmPassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请再次输入新密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LockOutlined />}\n                  size=\"large\"\n                >\n                  修改密码\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 用户管理（仅管理员） */}\n        {isAdmin && (\n          <Col span={24}>\n            <Card\n              title={\n                <Space>\n                  <TeamOutlined />\n                  <span>用户管理</span>\n                  <Tag color=\"red\">管理员专用</Tag>\n                </Space>\n              }\n              size=\"small\"\n            >\n              <Collapse defaultActiveKey={['1']} ghost>\n                <Panel\n                  header={\n                    <Space>\n                      <UserAddOutlined />\n                      <span>添加新用户</span>\n                    </Space>\n                  }\n                  key=\"1\"\n                >\n                  <Form\n                    form={addUserForm}\n                    layout=\"vertical\"\n                    onFinish={handleAddUser}\n                    style={{ maxWidth: 600 }}\n                  >\n                    <Form.Item\n                      label=\"新用户名\"\n                      name=\"new_username\"\n                      rules={[\n                        { required: true, message: '请输入用户名' },\n                        { min: 3, message: '用户名至少3位' },\n                        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }\n                      ]}\n                    >\n                      <Input\n                        prefix={<UserOutlined />}\n                        placeholder=\"请输入新用户名\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"新用户密码\"\n                      name=\"new_user_password\"\n                      rules={[{ validator: validatePassword }]}\n                    >\n                      <Input.Password\n                        prefix={<LockOutlined />}\n                        placeholder=\"请输入新用户密码（至少6位，包含字母和数字）\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"确认新用户密码\"\n                      name=\"confirm_user_password\"\n                      rules={[{ validator: validateConfirmPassword }]}\n                    >\n                      <Input.Password\n                        prefix={<SafetyOutlined />}\n                        placeholder=\"请再次输入新用户密码\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        icon={<UserAddOutlined />}\n                        size=\"large\"\n                      >\n                        添加用户\n                      </Button>\n                    </Form.Item>\n                  </Form>\n                </Panel>\n\n                <Panel\n                  header={\n                    <Space>\n                      <TeamOutlined />\n                      <span>用户列表</span>\n                      <Tag color=\"blue\">{users.length} 个用户</Tag>\n                    </Space>\n                  }\n                  key=\"2\"\n                >\n                  <div style={{ marginBottom: 16 }}>\n                    <Button\n                      icon={<TeamOutlined />}\n                      onClick={fetchUsers}\n                      loading={loading}\n                    >\n                      刷新用户列表\n                    </Button>\n                  </div>\n\n                  <Table\n                    columns={userColumns}\n                    dataSource={users}\n                    rowKey=\"username\"\n                    pagination={{\n                      pageSize: 10,\n                      showSizeChanger: true,\n                      showTotal: (total) => `共 ${total} 个用户`,\n                    }}\n                    size=\"small\"\n                  />\n                </Panel>\n              </Collapse>\n            </Card>\n          </Col>\n        )}\n\n        {/* 非管理员提示 */}\n        {!isAdmin && (\n          <Col span={24}>\n            <Alert\n              message=\"权限提示\"\n              description=\"您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。\"\n              type=\"warning\"\n              showIcon\n              icon={<ExclamationCircleOutlined />}\n            />\n          </Col>\n        )}\n      </Row>\n    </div>\n  );\n};\n\nexport default UserManagementPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EAELC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,EACVC,YAAY,EACZC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAM,CAAC,GAAGpB,QAAQ;AAC1B,MAAM;EAAEqB;AAAQ,CAAC,GAAGlB,KAAK;AASzB,MAAMmB,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACsC,kBAAkB,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACC,WAAW,CAAC,GAAGnC,IAAI,CAACkC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAM;IAAEE,IAAI;IAAEC;EAAM,CAAC,GAAGlB,WAAW,CAAEmB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACrE,MAAMC,WAAW,GAAG,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,QAAQ,KAAI,EAAE;EACxC,MAAMC,OAAO,GAAGF,WAAW,KAAK,OAAO;;EAEvC;EACA5C,SAAS,CAAC,MAAM;IACd,IAAI8C,OAAO,IAAIL,KAAK,EAAE;MACpBM,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACD,OAAO,EAAEL,KAAK,CAAC,CAAC;;EAEpB;EACA,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMN,KAAK,GAAGO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MACjD,MAAMC,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,QAAQ,CAACV,KAAK,CAAC;MAC9C,IAAIS,QAAQ,CAACE,IAAI,CAACjB,KAAK,EAAE;QACvBC,QAAQ,CAACc,QAAQ,CAACE,IAAI,CAACjB,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOkB,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9C,OAAO,CAAC8C,KAAK,CAAC,eAAe,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAIJ,KAAK,CAAC9C,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC;;EAED;EACA,MAAMmD,oBAAoB,GAAG,MAAOC,MAAW,IAAK;IAClDzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMO,KAAK,GAAGO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MACjD,MAAMC,QAAQ,GAAG,MAAM1B,OAAO,CAACoC,cAAc,CAAC;QAC5Cf,QAAQ,EAAED,WAAW;QACrBiB,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCC,YAAY,EAAEH,MAAM,CAACG,YAAY;QACjCC,gBAAgB,EAAEJ,MAAM,CAACI;MAC3B,CAAC,EAAEtB,KAAK,CAAC;MAET,IAAIS,QAAQ,CAACE,IAAI,CAAC7C,OAAO,EAAE;QACzBA,OAAO,CAACyD,OAAO,CAAC,UAAU,CAAC;QAC3B3B,kBAAkB,CAAC4B,WAAW,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAa,gBAAA,EAAAC,qBAAA;MACnBX,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B9C,OAAO,CAAC8C,KAAK,CAAC,aAAa,EAAAa,gBAAA,GAAAb,KAAK,CAACH,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAIJ,KAAK,CAAC9C,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACR2B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAG,MAAOT,MAAW,IAAK;IAC3CzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMO,KAAK,GAAGO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MACjD,MAAMC,QAAQ,GAAG,MAAM1B,OAAO,CAAC6C,OAAO,CAAC;QACrCxB,QAAQ,EAAED,WAAW;QACrB0B,YAAY,EAAEX,MAAM,CAACW,YAAY;QACjCC,iBAAiB,EAAEZ,MAAM,CAACY,iBAAiB;QAC3CC,qBAAqB,EAAEb,MAAM,CAACa;MAChC,CAAC,EAAE/B,KAAK,CAAC;MAET,IAAIS,QAAQ,CAACE,IAAI,CAAC7C,OAAO,EAAE;QACzBA,OAAO,CAACyD,OAAO,CAAC,UAAU,CAAC;QAC3BzB,WAAW,CAAC0B,WAAW,CAAC,CAAC;QACzBlB,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOM,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBlB,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B9C,OAAO,CAAC8C,KAAK,CAAC,aAAa,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAIJ,KAAK,CAAC9C,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACR2B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAGA,CAACC,CAAM,EAAEC,KAAa,KAAK;IAClD,IAAI,CAACA,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC9C;IACA,IAAI,CAAC,wBAAwB,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACzC,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAACR,CAAM,EAAEC,KAAa,KAAK;IACzD,MAAMQ,IAAI,GAAGhD,kBAAkB,IAAIE,WAAW;IAC9C,MAAM+C,aAAa,GAAGD,IAAI,KAAKhD,kBAAkB,GAAG,cAAc,GAAG,mBAAmB;IACxF,MAAMkD,QAAQ,GAAGF,IAAI,CAACG,aAAa,CAACF,aAAa,CAAC;IAElD,IAAI,CAACT,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,KAAKU,QAAQ,EAAE;MACtB,OAAOT,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;IAChD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMM,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGhD,QAAgB,iBACvBnB,OAAA,CAAClB,KAAK;MAAAsF,QAAA,gBACJpE,OAAA,CAACT,YAAY;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBxE,OAAA,CAACE,IAAI;QAACuE,MAAM;QAAAL,QAAA,EAAEjD;MAAQ;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAEX,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAG/C,OAAgB,iBACvBpB,OAAA,CAACf,GAAG;MAACyF,KAAK,EAAEtD,OAAO,GAAG,KAAK,GAAG,MAAO;MAAAgD,QAAA,EAClChD,OAAO,GAAG,KAAK,GAAG;IAAM;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB;EAET,CAAC,EACD;IACER,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGQ,IAAY,IAAKA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG;EACrE,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGQ,IAAY,IAAKA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG;EACrE,CAAC,CACF;EAED,oBACE7E,OAAA;IAAAoE,QAAA,gBACEpE,OAAA,CAACC,KAAK;MAAC6E,KAAK,EAAE,CAAE;MAAAV,QAAA,eACdpE,OAAA,CAAClB,KAAK;QAAAsF,QAAA,gBACJpE,OAAA,CAACN,YAAY;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACRxE,OAAA,CAACE,IAAI;MAAC6E,IAAI,EAAC,WAAW;MAAAX,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxE,OAAA,CAACb,GAAG;MAAC6F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAd,QAAA,gBAE9CpE,OAAA,CAACZ,GAAG;QAAC+F,IAAI,EAAE,EAAG;QAAAf,QAAA,eACZpE,OAAA,CAACxB,IAAI;UACHwF,KAAK,eACHhE,OAAA,CAAClB,KAAK;YAAAsF,QAAA,gBACJpE,OAAA,CAACX,YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChBxE,OAAA;cAAAoE,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UACDY,IAAI,EAAC,OAAO;UAAAhB,QAAA,gBAEZpE,OAAA,CAACvB,KAAK;YACJI,OAAO,EAAC,sCAAQ;YAChBwG,WAAW,EAAC,+MAAqC;YACjDN,IAAI,EAAC,MAAM;YACXO,QAAQ;YACRL,KAAK,EAAE;cAAEM,YAAY,EAAE;YAAG;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEFxE,OAAA,CAACtB,IAAI;YACHiF,IAAI,EAAEhD,kBAAmB;YACzB6E,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAEzD,oBAAqB;YAC/BiD,KAAK,EAAE;cAAES,QAAQ,EAAE;YAAI,CAAE;YAAAtB,QAAA,gBAEzBpE,OAAA,CAACtB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZC,IAAI,EAAC,cAAc;cACnBC,YAAY,EAAE5E,WAAY;cAAAkD,QAAA,eAE1BpE,OAAA,CAACrB,KAAK;gBACJoH,MAAM,eAAE/F,OAAA,CAACT,YAAY;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBwB,QAAQ;gBACR7C,KAAK,EAAEjC;cAAY;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAErH,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAuF,QAAA,eAE/CpE,OAAA,CAACrB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAE/F,OAAA,CAACX,YAAY;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB4B,WAAW,EAAC,sCAAQ;gBACpBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGtG,OAAA,CAACP,UAAU;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,oBAAoB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAEtD;cAAiB,CAAC,CAAE;cAAAmB,QAAA,eAEzCpE,OAAA,CAACrB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAE/F,OAAA,CAACL,cAAc;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B4B,WAAW,EAAC,qHAAsB;gBAClCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGtG,OAAA,CAACP,UAAU;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,oBAAoB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbC,IAAI,EAAC,kBAAkB;cACvBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAE7C;cAAwB,CAAC,CAAE;cAAAU,QAAA,eAEhDpE,OAAA,CAACrB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAE/F,OAAA,CAACL,cAAc;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B4B,WAAW,EAAC,kDAAU;gBACtBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGtG,OAAA,CAACP,UAAU;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,oBAAoB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;cAAAvB,QAAA,eACRpE,OAAA,CAACpB,MAAM;gBACLmG,IAAI,EAAC,SAAS;gBACdyB,QAAQ,EAAC,QAAQ;gBACjBjG,OAAO,EAAEA,OAAQ;gBACjBkG,IAAI,eAAEzG,OAAA,CAACX,YAAY;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBY,IAAI,EAAC,OAAO;gBAAAhB,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGLpD,OAAO,iBACNpB,OAAA,CAACZ,GAAG;QAAC+F,IAAI,EAAE,EAAG;QAAAf,QAAA,eACZpE,OAAA,CAACxB,IAAI;UACHwF,KAAK,eACHhE,OAAA,CAAClB,KAAK;YAAAsF,QAAA,gBACJpE,OAAA,CAACN,YAAY;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChBxE,OAAA;cAAAoE,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBxE,OAAA,CAACf,GAAG;cAACyF,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CACR;UACDY,IAAI,EAAC,OAAO;UAAAhB,QAAA,eAEZpE,OAAA,CAACjB,QAAQ;YAAC2H,gBAAgB,EAAE,CAAC,GAAG,CAAE;YAACC,KAAK;YAAAvC,QAAA,gBACtCpE,OAAA,CAACG,KAAK;cACJyG,MAAM,eACJ5G,OAAA,CAAClB,KAAK;gBAAAsF,QAAA,gBACJpE,OAAA,CAACV,eAAe;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnBxE,OAAA;kBAAAoE,QAAA,EAAM;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACR;cAAAJ,QAAA,eAGDpE,OAAA,CAACtB,IAAI;gBACHiF,IAAI,EAAE9C,WAAY;gBAClB2E,MAAM,EAAC,UAAU;gBACjBC,QAAQ,EAAE/C,aAAc;gBACxBuC,KAAK,EAAE;kBAAES,QAAQ,EAAE;gBAAI,CAAE;gBAAAtB,QAAA,gBAEzBpE,OAAA,CAACtB,IAAI,CAACiH,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,cAAc;kBACnBI,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAErH,OAAO,EAAE;kBAAS,CAAC,EACrC;oBAAEgI,GAAG,EAAE,CAAC;oBAAEhI,OAAO,EAAE;kBAAU,CAAC,EAC9B;oBAAEiI,OAAO,EAAE,iBAAiB;oBAAEjI,OAAO,EAAE;kBAAmB,CAAC,CAC3D;kBAAAuF,QAAA,eAEFpE,OAAA,CAACrB,KAAK;oBACJoH,MAAM,eAAE/F,OAAA,CAACT,YAAY;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB4B,WAAW,EAAC;kBAAS;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;kBACRC,KAAK,EAAC,gCAAO;kBACbC,IAAI,EAAC,mBAAmB;kBACxBI,KAAK,EAAE,CAAC;oBAAEM,SAAS,EAAEtD;kBAAiB,CAAC,CAAE;kBAAAmB,QAAA,eAEzCpE,OAAA,CAACrB,KAAK,CAACwH,QAAQ;oBACbJ,MAAM,eAAE/F,OAAA,CAACX,YAAY;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB4B,WAAW,EAAC,iIAAwB;oBACpCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGtG,OAAA,CAACP,UAAU;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,oBAAoB;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;kBACRC,KAAK,EAAC,4CAAS;kBACfC,IAAI,EAAC,uBAAuB;kBAC5BI,KAAK,EAAE,CAAC;oBAAEM,SAAS,EAAE7C;kBAAwB,CAAC,CAAE;kBAAAU,QAAA,eAEhDpE,OAAA,CAACrB,KAAK,CAACwH,QAAQ;oBACbJ,MAAM,eAAE/F,OAAA,CAACL,cAAc;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC3B4B,WAAW,EAAC,8DAAY;oBACxBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGtG,OAAA,CAACP,UAAU;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,oBAAoB;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZxE,OAAA,CAACtB,IAAI,CAACiH,IAAI;kBAAAvB,QAAA,eACRpE,OAAA,CAACpB,MAAM;oBACLmG,IAAI,EAAC,SAAS;oBACdyB,QAAQ,EAAC,QAAQ;oBACjBjG,OAAO,EAAEA,OAAQ;oBACjBkG,IAAI,eAAEzG,OAAA,CAACV,eAAe;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1BY,IAAI,EAAC,OAAO;oBAAAhB,QAAA,EACb;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GA1DH,GAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DF,CAAC,eAERxE,OAAA,CAACG,KAAK;cACJyG,MAAM,eACJ5G,OAAA,CAAClB,KAAK;gBAAAsF,QAAA,gBACJpE,OAAA,CAACN,YAAY;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChBxE,OAAA;kBAAAoE,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxE,OAAA,CAACf,GAAG;kBAACyF,KAAK,EAAC,MAAM;kBAAAN,QAAA,GAAE3D,KAAK,CAAC8C,MAAM,EAAC,qBAAI;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CACR;cAAAJ,QAAA,gBAGDpE,OAAA;gBAAKiF,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAG,CAAE;gBAAAnB,QAAA,eAC/BpE,OAAA,CAACpB,MAAM;kBACL6H,IAAI,eAAEzG,OAAA,CAACN,YAAY;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBuC,OAAO,EAAE1F,UAAW;kBACpBd,OAAO,EAAEA,OAAQ;kBAAA6D,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENxE,OAAA,CAAChB,KAAK;gBACJgI,OAAO,EAAEjD,WAAY;gBACrBkD,UAAU,EAAExG,KAAM;gBAClByG,MAAM,EAAC,UAAU;gBACjBC,UAAU,EAAE;kBACVC,QAAQ,EAAE,EAAE;kBACZC,eAAe,EAAE,IAAI;kBACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;gBAClC,CAAE;gBACFnC,IAAI,EAAC;cAAO;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA,GAtBE,GAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA,CAACpD,OAAO,iBACPpB,OAAA,CAACZ,GAAG;QAAC+F,IAAI,EAAE,EAAG;QAAAf,QAAA,eACZpE,OAAA,CAACvB,KAAK;UACJI,OAAO,EAAC,0BAAM;UACdwG,WAAW,EAAC,wPAA2C;UACvDN,IAAI,EAAC,SAAS;UACdO,QAAQ;UACRmB,IAAI,eAAEzG,OAAA,CAACJ,yBAAyB;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CA9XID,kBAA4B;EAAA,QAGH3B,IAAI,CAACkC,OAAO,EACnBlC,IAAI,CAACkC,OAAO,EAGVf,WAAW;AAAA;AAAA2H,EAAA,GAP/BnH,kBAA4B;AAgYlC,eAAeA,kBAAkB;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}