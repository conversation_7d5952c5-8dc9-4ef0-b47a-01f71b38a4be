{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Steps = function Steps(props) {\n  var size = props.size,\n    steps = props.steps,\n    _props$percent = props.percent,\n    percent = _props$percent === void 0 ? 0 : _props$percent,\n    _props$strokeWidth = props.strokeWidth,\n    strokeWidth = _props$strokeWidth === void 0 ? 8 : _props$strokeWidth,\n    strokeColor = props.strokeColor,\n    _props$trailColor = props.trailColor,\n    trailColor = _props$trailColor === void 0 ? null : _props$trailColor,\n    prefixCls = props.prefixCls,\n    children = props.children;\n  var current = Math.round(steps * (percent / 100));\n  var stepWidth = size === 'small' ? 2 : 14;\n  var styledSteps = new Array(steps);\n  for (var i = 0; i < steps; i++) {\n    var color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;\n    styledSteps[i] = /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(\"\".concat(prefixCls, \"-steps-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-steps-item-active\"), i <= current - 1)),\n      style: {\n        backgroundColor: i <= current - 1 ? color : trailColor,\n        width: stepWidth,\n        height: strokeWidth\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-steps-outer\")\n  }, styledSteps, children);\n};\nexport default Steps;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "Steps", "props", "size", "steps", "_props$percent", "percent", "_props$strokeWidth", "strokeWidth", "strokeColor", "_props$trailColor", "trailColor", "prefixCls", "children", "current", "Math", "round", "<PERSON><PERSON><PERSON><PERSON>", "styledSteps", "Array", "i", "color", "isArray", "createElement", "key", "className", "concat", "style", "backgroundColor", "width", "height"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/progress/Steps.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Steps = function Steps(props) {\n  var size = props.size,\n    steps = props.steps,\n    _props$percent = props.percent,\n    percent = _props$percent === void 0 ? 0 : _props$percent,\n    _props$strokeWidth = props.strokeWidth,\n    strokeWidth = _props$strokeWidth === void 0 ? 8 : _props$strokeWidth,\n    strokeColor = props.strokeColor,\n    _props$trailColor = props.trailColor,\n    trailColor = _props$trailColor === void 0 ? null : _props$trailColor,\n    prefixCls = props.prefixCls,\n    children = props.children;\n  var current = Math.round(steps * (percent / 100));\n  var stepWidth = size === 'small' ? 2 : 14;\n  var styledSteps = new Array(steps);\n  for (var i = 0; i < steps; i++) {\n    var color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;\n    styledSteps[i] = /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(\"\".concat(prefixCls, \"-steps-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-steps-item-active\"), i <= current - 1)),\n      style: {\n        backgroundColor: i <= current - 1 ? color : trailColor,\n        width: stepWidth,\n        height: strokeWidth\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-steps-outer\")\n  }, styledSteps, children);\n};\nexport default Steps;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,cAAc,GAAGH,KAAK,CAACI,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc;IACxDE,kBAAkB,GAAGL,KAAK,CAACM,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IACpEE,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,iBAAiB,GAAGR,KAAK,CAACS,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;EAC3B,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACZ,KAAK,IAAIE,OAAO,GAAG,GAAG,CAAC,CAAC;EACjD,IAAIW,SAAS,GAAGd,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;EACzC,IAAIe,WAAW,GAAG,IAAIC,KAAK,CAACf,KAAK,CAAC;EAClC,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,KAAK,EAAEgB,CAAC,EAAE,EAAE;IAC9B,IAAIC,KAAK,GAAGF,KAAK,CAACG,OAAO,CAACb,WAAW,CAAC,GAAGA,WAAW,CAACW,CAAC,CAAC,GAAGX,WAAW;IACrES,WAAW,CAACE,CAAC,CAAC,GAAG,aAAapB,KAAK,CAACuB,aAAa,CAAC,KAAK,EAAE;MACvDC,GAAG,EAAEJ,CAAC;MACNK,SAAS,EAAE1B,UAAU,CAAC,EAAE,CAAC2B,MAAM,CAACd,SAAS,EAAE,aAAa,CAAC,EAAEd,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAACd,SAAS,EAAE,oBAAoB,CAAC,EAAEQ,CAAC,IAAIN,OAAO,GAAG,CAAC,CAAC,CAAC;MAC7Ia,KAAK,EAAE;QACLC,eAAe,EAAER,CAAC,IAAIN,OAAO,GAAG,CAAC,GAAGO,KAAK,GAAGV,UAAU;QACtDkB,KAAK,EAAEZ,SAAS;QAChBa,MAAM,EAAEtB;MACV;IACF,CAAC,CAAC;EACJ;EACA,OAAO,aAAaR,KAAK,CAACuB,aAAa,CAAC,KAAK,EAAE;IAC7CE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACd,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEM,WAAW,EAAEL,QAAQ,CAAC;AAC3B,CAAC;AACD,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}