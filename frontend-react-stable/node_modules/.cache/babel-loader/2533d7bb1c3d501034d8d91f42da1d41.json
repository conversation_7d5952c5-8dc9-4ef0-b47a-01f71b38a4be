{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = difference;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction difference(values, ...others) {\n  values = new _index.InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "difference", "_index", "require", "values", "others", "InternSet", "other", "delete"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/difference.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = difference;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction difference(values, ...others) {\n  values = new _index.InternSet(values);\n\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n\n  return values;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,UAAUA,CAACG,MAAM,EAAE,GAAGC,MAAM,EAAE;EACrCD,MAAM,GAAG,IAAIF,MAAM,CAACI,SAAS,CAACF,MAAM,CAAC;EAErC,KAAK,MAAMG,KAAK,IAAIF,MAAM,EAAE;IAC1B,KAAK,MAAMN,KAAK,IAAIQ,KAAK,EAAE;MACzBH,MAAM,CAACI,MAAM,CAACT,KAAK,CAAC;IACtB;EACF;EAEA,OAAOK,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script"}