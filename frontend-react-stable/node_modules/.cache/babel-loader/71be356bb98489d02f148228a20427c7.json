{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series) {\n  return (0, _ascending.default)(series).reverse();\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_ascending", "_interopRequireDefault", "require", "obj", "__esModule", "series", "reverse"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/order/descending.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series) {\n  return (0, _ascending.default)(series).reverse();\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,MAAM,EAAE;EACxB,OAAO,CAAC,CAAC,EAAEL,UAAU,CAACF,OAAO,EAAEO,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}