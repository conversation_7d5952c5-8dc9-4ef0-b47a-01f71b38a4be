{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"prefix\", \"split\", \"notFoundContent\", \"value\", \"defaultValue\", \"children\", \"open\", \"validateSearch\", \"filterOption\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onPressEnter\", \"onSearch\", \"onSelect\", \"onFocus\", \"onBlur\", \"transitionName\", \"placement\", \"direction\", \"getPopupContainer\", \"dropdownClassName\"];\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport TextArea from 'rc-textarea';\nimport KeywordTrigger from './KeywordTrigger';\nimport MentionsContext from './MentionsContext';\nimport Option from './Option';\nimport { filterOption as defaultFilterOption, getBeforeSelectionText, getLastMeasureIndex, replaceWithMeasure, setInputSelection, validateSearch as defaultValidateSearch } from './util';\nimport useEffectState from './hooks/useEffectState';\nvar Mentions = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    prefix = props.prefix,\n    split = props.split,\n    notFoundContent = props.notFoundContent,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    children = props.children,\n    open = props.open,\n    validateSearch = props.validateSearch,\n    filterOption = props.filterOption,\n    onChange = props.onChange,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    onPressEnter = props.onPressEnter,\n    onSearch = props.onSearch,\n    onSelect = props.onSelect,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    transitionName = props.transitionName,\n    placement = props.placement,\n    direction = props.direction,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedPrefix = Array.isArray(prefix) ? prefix : [prefix];\n  var mergedProps = _objectSpread(_objectSpread({}, props), {}, {\n    prefix: mergedPrefix\n  }); // =============================== Refs ===============================\n\n  var textareaRef = useRef(null);\n  var measureRef = useRef(null);\n  var getTextArea = function getTextArea() {\n    var _textareaRef$current, _textareaRef$current$;\n    return (_textareaRef$current = textareaRef.current) === null || _textareaRef$current === void 0 ? void 0 : (_textareaRef$current$ = _textareaRef$current.resizableTextArea) === null || _textareaRef$current$ === void 0 ? void 0 : _textareaRef$current$.textArea;\n  };\n  React.useImperativeHandle(ref, function () {\n    var _textareaRef$current4, _textareaRef$current5;\n    return {\n      focus: function focus() {\n        var _textareaRef$current2;\n        return (_textareaRef$current2 = textareaRef.current) === null || _textareaRef$current2 === void 0 ? void 0 : _textareaRef$current2.focus();\n      },\n      blur: function blur() {\n        var _textareaRef$current3;\n        return (_textareaRef$current3 = textareaRef.current) === null || _textareaRef$current3 === void 0 ? void 0 : _textareaRef$current3.blur();\n      },\n      textarea: (_textareaRef$current4 = textareaRef.current) === null || _textareaRef$current4 === void 0 ? void 0 : (_textareaRef$current5 = _textareaRef$current4.resizableTextArea) === null || _textareaRef$current5 === void 0 ? void 0 : _textareaRef$current5.textArea\n    };\n  }); // ============================== State ===============================\n\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    measuring = _useState2[0],\n    setMeasuring = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    measureText = _useState4[0],\n    setMeasureText = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    measurePrefix = _useState6[0],\n    setMeasurePrefix = _useState6[1];\n  var _useState7 = useState(0),\n    _useState8 = _slicedToArray(_useState7, 2),\n    measureLocation = _useState8[0],\n    setMeasureLocation = _useState8[1];\n  var _useState9 = useState(0),\n    _useState10 = _slicedToArray(_useState9, 2),\n    activeIndex = _useState10[0],\n    setActiveIndex = _useState10[1];\n  var _useState11 = useState(false),\n    _useState12 = _slicedToArray(_useState11, 2),\n    isFocus = _useState12[0],\n    setIsFocus = _useState12[1]; // ============================== Value ===============================\n\n  var _useMergedState = useMergedState('', {\n      defaultValue: defaultValue,\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1]; // =============================== Open ===============================\n\n  useEffect(function () {\n    // Sync measure div top with textarea for rc-trigger usage\n    if (measuring && measureRef.current) {\n      measureRef.current.scrollTop = getTextArea().scrollTop;\n    }\n  }, [measuring]);\n  var _React$useMemo = React.useMemo(function () {\n      if (open) {\n        if (process.env.NODE_ENV !== 'production') {\n          warning(false, '`open` of Mentions is only used for debug usage. Do not use in you production.');\n        }\n        for (var i = 0; i < mergedPrefix.length; i += 1) {\n          var curPrefix = mergedPrefix[i];\n          var index = mergedValue.lastIndexOf(curPrefix);\n          if (index >= 0) {\n            return [true, '', curPrefix, index];\n          }\n        }\n      }\n      return [measuring, measureText, measurePrefix, measureLocation];\n    }, [open, measuring, mergedPrefix, mergedValue, measureText, measurePrefix, measureLocation]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 4),\n    mergedMeasuring = _React$useMemo2[0],\n    mergedMeasureText = _React$useMemo2[1],\n    mergedMeasurePrefix = _React$useMemo2[2],\n    mergedMeasureLocation = _React$useMemo2[3]; // ============================== Option ==============================\n\n  var getOptions = React.useCallback(function (targetMeasureText) {\n    var list = toArray(children).map(function (_ref) {\n      var optionProps = _ref.props,\n        key = _ref.key;\n      return _objectSpread(_objectSpread({}, optionProps), {}, {\n        key: key || optionProps.value\n      });\n    }).filter(function (option) {\n      /** Return all result if `filterOption` is false. */\n      if (filterOption === false) {\n        return true;\n      }\n      return filterOption(targetMeasureText, option);\n    });\n    return list;\n  }, [children, filterOption]);\n  var options = React.useMemo(function () {\n    return getOptions(mergedMeasureText);\n  }, [getOptions, mergedMeasureText]); // ============================= Measure ==============================\n  // Mark that we will reset input selection to target position when user select option\n\n  var onSelectionEffect = useEffectState();\n  var startMeasure = function startMeasure(nextMeasureText, nextMeasurePrefix, nextMeasureLocation) {\n    setMeasuring(true);\n    setMeasureText(nextMeasureText);\n    setMeasurePrefix(nextMeasurePrefix);\n    setMeasureLocation(nextMeasureLocation);\n    setActiveIndex(0);\n  };\n  var stopMeasure = function stopMeasure(callback) {\n    setMeasuring(false);\n    setMeasureLocation(0);\n    setMeasureText('');\n    onSelectionEffect(callback);\n  }; // ============================== Change ==============================\n\n  var triggerChange = function triggerChange(nextValue) {\n    setMergedValue(nextValue);\n    onChange === null || onChange === void 0 ? void 0 : onChange(nextValue);\n  };\n  var onInternalChange = function onInternalChange(_ref2) {\n    var nextValue = _ref2.target.value;\n    triggerChange(nextValue);\n  };\n  var selectOption = function selectOption(option) {\n    var _getTextArea;\n    var _option$value = option.value,\n      mentionValue = _option$value === void 0 ? '' : _option$value;\n    var _replaceWithMeasure = replaceWithMeasure(mergedValue, {\n        measureLocation: mergedMeasureLocation,\n        targetText: mentionValue,\n        prefix: mergedMeasurePrefix,\n        selectionStart: (_getTextArea = getTextArea()) === null || _getTextArea === void 0 ? void 0 : _getTextArea.selectionStart,\n        split: split\n      }),\n      text = _replaceWithMeasure.text,\n      selectionLocation = _replaceWithMeasure.selectionLocation;\n    triggerChange(text);\n    stopMeasure(function () {\n      // We need restore the selection position\n      setInputSelection(getTextArea(), selectionLocation);\n    });\n    onSelect === null || onSelect === void 0 ? void 0 : onSelect(option, mergedMeasurePrefix);\n  }; // ============================= KeyEvent =============================\n  // Check if hit the measure keyword\n\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var which = event.which;\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event); // Skip if not measuring\n\n    if (!mergedMeasuring) {\n      return;\n    }\n    if (which === KeyCode.UP || which === KeyCode.DOWN) {\n      // Control arrow function\n      var optionLen = options.length;\n      var offset = which === KeyCode.UP ? -1 : 1;\n      var newActiveIndex = (activeIndex + offset + optionLen) % optionLen;\n      setActiveIndex(newActiveIndex);\n      event.preventDefault();\n    } else if (which === KeyCode.ESC) {\n      stopMeasure();\n    } else if (which === KeyCode.ENTER) {\n      // Measure hit\n      event.preventDefault();\n      if (!options.length) {\n        stopMeasure();\n        return;\n      }\n      var option = options[activeIndex];\n      selectOption(option);\n    }\n  };\n  /**\n   * When to start measure:\n   * 1. When user press `prefix`\n   * 2. When measureText !== prevMeasureText\n   *  - If measure hit\n   *  - If measuring\n   *\n   * When to stop measure:\n   * 1. Selection is out of range\n   * 2. Contains `space`\n   * 3. ESC or select one\n   */\n\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    var key = event.key,\n      which = event.which;\n    var target = event.target;\n    var selectionStartText = getBeforeSelectionText(target);\n    var _getLastMeasureIndex = getLastMeasureIndex(selectionStartText, mergedPrefix),\n      measureIndex = _getLastMeasureIndex.location,\n      nextMeasurePrefix = _getLastMeasureIndex.prefix; // If the client implements an onKeyUp handler, call it\n\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp(event); // Skip if match the white key list\n\n    if ([KeyCode.ESC, KeyCode.UP, KeyCode.DOWN, KeyCode.ENTER].indexOf(which) !== -1) {\n      return;\n    }\n    if (measureIndex !== -1) {\n      var nextMeasureText = selectionStartText.slice(measureIndex + nextMeasurePrefix.length);\n      var validateMeasure = validateSearch(nextMeasureText, mergedProps);\n      var matchOption = !!getOptions(nextMeasureText).length;\n      if (validateMeasure) {\n        if (key === nextMeasurePrefix || key === 'Shift' || mergedMeasuring || nextMeasureText !== mergedMeasureText && matchOption) {\n          startMeasure(nextMeasureText, nextMeasurePrefix, measureIndex);\n        }\n      } else if (mergedMeasuring) {\n        // Stop if measureText is invalidate\n        stopMeasure();\n      }\n      /**\n       * We will trigger `onSearch` to developer since they may use for async update.\n       * If met `space` means user finished searching.\n       */\n\n      if (onSearch && validateMeasure) {\n        onSearch(nextMeasureText, nextMeasurePrefix);\n      }\n    } else if (mergedMeasuring) {\n      stopMeasure();\n    }\n  };\n  var onInternalPressEnter = function onInternalPressEnter(event) {\n    if (!mergedMeasuring && onPressEnter) {\n      onPressEnter(event);\n    }\n  }; // ============================ Focus Blur ============================\n\n  var focusRef = useRef();\n  var onInternalFocus = function onInternalFocus(event) {\n    window.clearTimeout(focusRef.current);\n    if (!isFocus && event && onFocus) {\n      onFocus(event);\n    }\n    setIsFocus(true);\n  };\n  var onInternalBlur = function onInternalBlur(event) {\n    focusRef.current = window.setTimeout(function () {\n      setIsFocus(false);\n      stopMeasure();\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(event);\n    }, 0);\n  };\n  var onDropdownFocus = function onDropdownFocus() {\n    onInternalFocus();\n  };\n  var onDropdownBlur = function onDropdownBlur() {\n    onInternalBlur();\n  }; // ============================== Render ==============================\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, _extends({\n    ref: textareaRef,\n    value: mergedValue\n  }, restProps, {\n    onChange: onInternalChange,\n    onKeyDown: onInternalKeyDown,\n    onKeyUp: onInternalKeyUp,\n    onPressEnter: onInternalPressEnter,\n    onFocus: onInternalFocus,\n    onBlur: onInternalBlur\n  })), mergedMeasuring && /*#__PURE__*/React.createElement(\"div\", {\n    ref: measureRef,\n    className: \"\".concat(prefixCls, \"-measure\")\n  }, mergedValue.slice(0, mergedMeasureLocation), /*#__PURE__*/React.createElement(MentionsContext.Provider, {\n    value: {\n      notFoundContent: notFoundContent,\n      activeIndex: activeIndex,\n      setActiveIndex: setActiveIndex,\n      selectOption: selectOption,\n      onFocus: onDropdownFocus,\n      onBlur: onDropdownBlur\n    }\n  }, /*#__PURE__*/React.createElement(KeywordTrigger, {\n    prefixCls: prefixCls,\n    transitionName: transitionName,\n    placement: placement,\n    direction: direction,\n    options: options,\n    visible: true,\n    getPopupContainer: getPopupContainer,\n    dropdownClassName: dropdownClassName\n  }, /*#__PURE__*/React.createElement(\"span\", null, mergedMeasurePrefix))), mergedValue.slice(mergedMeasureLocation + mergedMeasurePrefix.length)));\n});\nMentions.defaultProps = {\n  prefixCls: 'rc-mentions',\n  prefix: '@',\n  split: ' ',\n  validateSearch: defaultValidateSearch,\n  filterOption: defaultFilterOption,\n  notFoundContent: 'Not Found',\n  rows: 1\n};\nMentions.Option = Option;\nexport default Mentions;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "classNames", "useMergedState", "toArray", "KeyCode", "warning", "React", "useState", "useRef", "useEffect", "TextArea", "KeywordTrigger", "MentionsContext", "Option", "filterOption", "defaultFilterOption", "getBeforeSelectionText", "getLastMeasureIndex", "replaceWithMeasure", "setInputSelection", "validateSearch", "defaultValidateSearch", "useEffectState", "Mentions", "forwardRef", "props", "ref", "prefixCls", "className", "style", "prefix", "split", "notFoundContent", "value", "defaultValue", "children", "open", "onChange", "onKeyDown", "onKeyUp", "onPressEnter", "onSearch", "onSelect", "onFocus", "onBlur", "transitionName", "placement", "direction", "getPopupContainer", "dropdownClassName", "restProps", "mergedPrefix", "Array", "isArray", "mergedProps", "textareaRef", "measureRef", "getTextArea", "_textareaRef$current", "_textareaRef$current$", "current", "resizableTextArea", "textArea", "useImperativeHandle", "_textareaRef$current4", "_textareaRef$current5", "focus", "_textareaRef$current2", "blur", "_textareaRef$current3", "textarea", "_useState", "_useState2", "measuring", "setMeasuring", "_useState3", "_useState4", "measureText", "setMeasureText", "_useState5", "_useState6", "measurePrefix", "setMeasurePrefix", "_useState7", "_useState8", "measureLocation", "setMeasureLocation", "_useState9", "_useState10", "activeIndex", "setActiveIndex", "_useState11", "_useState12", "isFocus", "setIsFocus", "_useMergedState", "_useMergedState2", "mergedValue", "setMergedValue", "scrollTop", "_React$useMemo", "useMemo", "process", "env", "NODE_ENV", "i", "length", "curPrefix", "index", "lastIndexOf", "_React$useMemo2", "mergedMeasuring", "mergedMeasureText", "mergedMeasurePrefix", "mergedMeasureLocation", "getOptions", "useCallback", "targetMeasureText", "list", "map", "_ref", "optionProps", "key", "filter", "option", "options", "onSelectionEffect", "startMeasure", "nextMeasureText", "nextMeasurePrefix", "nextMeasureLocation", "stopMeasure", "callback", "trigger<PERSON>hange", "nextValue", "onInternalChange", "_ref2", "target", "selectOption", "_getTextArea", "_option$value", "mentionValue", "_replaceWithMeasure", "targetText", "selectionStart", "text", "selectionLocation", "onInternalKeyDown", "event", "which", "UP", "DOWN", "optionLen", "offset", "newActiveIndex", "preventDefault", "ESC", "ENTER", "onInternalKeyUp", "selectionStartText", "_getLastMeasureIndex", "measureIndex", "location", "indexOf", "slice", "validateMeasure", "matchOption", "onInternalPressEnter", "focusRef", "onInternalFocus", "window", "clearTimeout", "onInternalBlur", "setTimeout", "onDropdownFocus", "onDropdownBlur", "createElement", "concat", "Provider", "visible", "defaultProps", "rows"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-mentions/es/Mentions.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"prefix\", \"split\", \"notFoundContent\", \"value\", \"defaultValue\", \"children\", \"open\", \"validateSearch\", \"filterOption\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onPressEnter\", \"onSearch\", \"onSelect\", \"onFocus\", \"onBlur\", \"transitionName\", \"placement\", \"direction\", \"getPopupContainer\", \"dropdownClassName\"];\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport TextArea from 'rc-textarea';\nimport KeywordTrigger from './KeywordTrigger';\nimport MentionsContext from './MentionsContext';\nimport Option from './Option';\nimport { filterOption as defaultFilterOption, getBeforeSelectionText, getLastMeasureIndex, replaceWithMeasure, setInputSelection, validateSearch as defaultValidateSearch } from './util';\nimport useEffectState from './hooks/useEffectState';\nvar Mentions = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      prefix = props.prefix,\n      split = props.split,\n      notFoundContent = props.notFoundContent,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      children = props.children,\n      open = props.open,\n      validateSearch = props.validateSearch,\n      filterOption = props.filterOption,\n      onChange = props.onChange,\n      onKeyDown = props.onKeyDown,\n      onKeyUp = props.onKeyUp,\n      onPressEnter = props.onPressEnter,\n      onSearch = props.onSearch,\n      onSelect = props.onSelect,\n      onFocus = props.onFocus,\n      onBlur = props.onBlur,\n      transitionName = props.transitionName,\n      placement = props.placement,\n      direction = props.direction,\n      getPopupContainer = props.getPopupContainer,\n      dropdownClassName = props.dropdownClassName,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var mergedPrefix = Array.isArray(prefix) ? prefix : [prefix];\n\n  var mergedProps = _objectSpread(_objectSpread({}, props), {}, {\n    prefix: mergedPrefix\n  }); // =============================== Refs ===============================\n\n\n  var textareaRef = useRef(null);\n  var measureRef = useRef(null);\n\n  var getTextArea = function getTextArea() {\n    var _textareaRef$current, _textareaRef$current$;\n\n    return (_textareaRef$current = textareaRef.current) === null || _textareaRef$current === void 0 ? void 0 : (_textareaRef$current$ = _textareaRef$current.resizableTextArea) === null || _textareaRef$current$ === void 0 ? void 0 : _textareaRef$current$.textArea;\n  };\n\n  React.useImperativeHandle(ref, function () {\n    var _textareaRef$current4, _textareaRef$current5;\n\n    return {\n      focus: function focus() {\n        var _textareaRef$current2;\n\n        return (_textareaRef$current2 = textareaRef.current) === null || _textareaRef$current2 === void 0 ? void 0 : _textareaRef$current2.focus();\n      },\n      blur: function blur() {\n        var _textareaRef$current3;\n\n        return (_textareaRef$current3 = textareaRef.current) === null || _textareaRef$current3 === void 0 ? void 0 : _textareaRef$current3.blur();\n      },\n      textarea: (_textareaRef$current4 = textareaRef.current) === null || _textareaRef$current4 === void 0 ? void 0 : (_textareaRef$current5 = _textareaRef$current4.resizableTextArea) === null || _textareaRef$current5 === void 0 ? void 0 : _textareaRef$current5.textArea\n    };\n  }); // ============================== State ===============================\n\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      measuring = _useState2[0],\n      setMeasuring = _useState2[1];\n\n  var _useState3 = useState(''),\n      _useState4 = _slicedToArray(_useState3, 2),\n      measureText = _useState4[0],\n      setMeasureText = _useState4[1];\n\n  var _useState5 = useState(''),\n      _useState6 = _slicedToArray(_useState5, 2),\n      measurePrefix = _useState6[0],\n      setMeasurePrefix = _useState6[1];\n\n  var _useState7 = useState(0),\n      _useState8 = _slicedToArray(_useState7, 2),\n      measureLocation = _useState8[0],\n      setMeasureLocation = _useState8[1];\n\n  var _useState9 = useState(0),\n      _useState10 = _slicedToArray(_useState9, 2),\n      activeIndex = _useState10[0],\n      setActiveIndex = _useState10[1];\n\n  var _useState11 = useState(false),\n      _useState12 = _slicedToArray(_useState11, 2),\n      isFocus = _useState12[0],\n      setIsFocus = _useState12[1]; // ============================== Value ===============================\n\n\n  var _useMergedState = useMergedState('', {\n    defaultValue: defaultValue,\n    value: value\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedValue = _useMergedState2[0],\n      setMergedValue = _useMergedState2[1]; // =============================== Open ===============================\n\n\n  useEffect(function () {\n    // Sync measure div top with textarea for rc-trigger usage\n    if (measuring && measureRef.current) {\n      measureRef.current.scrollTop = getTextArea().scrollTop;\n    }\n  }, [measuring]);\n\n  var _React$useMemo = React.useMemo(function () {\n    if (open) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`open` of Mentions is only used for debug usage. Do not use in you production.');\n      }\n\n      for (var i = 0; i < mergedPrefix.length; i += 1) {\n        var curPrefix = mergedPrefix[i];\n        var index = mergedValue.lastIndexOf(curPrefix);\n\n        if (index >= 0) {\n          return [true, '', curPrefix, index];\n        }\n      }\n    }\n\n    return [measuring, measureText, measurePrefix, measureLocation];\n  }, [open, measuring, mergedPrefix, mergedValue, measureText, measurePrefix, measureLocation]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 4),\n      mergedMeasuring = _React$useMemo2[0],\n      mergedMeasureText = _React$useMemo2[1],\n      mergedMeasurePrefix = _React$useMemo2[2],\n      mergedMeasureLocation = _React$useMemo2[3]; // ============================== Option ==============================\n\n\n  var getOptions = React.useCallback(function (targetMeasureText) {\n    var list = toArray(children).map(function (_ref) {\n      var optionProps = _ref.props,\n          key = _ref.key;\n      return _objectSpread(_objectSpread({}, optionProps), {}, {\n        key: key || optionProps.value\n      });\n    }).filter(function (option) {\n      /** Return all result if `filterOption` is false. */\n      if (filterOption === false) {\n        return true;\n      }\n\n      return filterOption(targetMeasureText, option);\n    });\n    return list;\n  }, [children, filterOption]);\n  var options = React.useMemo(function () {\n    return getOptions(mergedMeasureText);\n  }, [getOptions, mergedMeasureText]); // ============================= Measure ==============================\n  // Mark that we will reset input selection to target position when user select option\n\n  var onSelectionEffect = useEffectState();\n\n  var startMeasure = function startMeasure(nextMeasureText, nextMeasurePrefix, nextMeasureLocation) {\n    setMeasuring(true);\n    setMeasureText(nextMeasureText);\n    setMeasurePrefix(nextMeasurePrefix);\n    setMeasureLocation(nextMeasureLocation);\n    setActiveIndex(0);\n  };\n\n  var stopMeasure = function stopMeasure(callback) {\n    setMeasuring(false);\n    setMeasureLocation(0);\n    setMeasureText('');\n    onSelectionEffect(callback);\n  }; // ============================== Change ==============================\n\n\n  var triggerChange = function triggerChange(nextValue) {\n    setMergedValue(nextValue);\n    onChange === null || onChange === void 0 ? void 0 : onChange(nextValue);\n  };\n\n  var onInternalChange = function onInternalChange(_ref2) {\n    var nextValue = _ref2.target.value;\n    triggerChange(nextValue);\n  };\n\n  var selectOption = function selectOption(option) {\n    var _getTextArea;\n\n    var _option$value = option.value,\n        mentionValue = _option$value === void 0 ? '' : _option$value;\n\n    var _replaceWithMeasure = replaceWithMeasure(mergedValue, {\n      measureLocation: mergedMeasureLocation,\n      targetText: mentionValue,\n      prefix: mergedMeasurePrefix,\n      selectionStart: (_getTextArea = getTextArea()) === null || _getTextArea === void 0 ? void 0 : _getTextArea.selectionStart,\n      split: split\n    }),\n        text = _replaceWithMeasure.text,\n        selectionLocation = _replaceWithMeasure.selectionLocation;\n\n    triggerChange(text);\n    stopMeasure(function () {\n      // We need restore the selection position\n      setInputSelection(getTextArea(), selectionLocation);\n    });\n    onSelect === null || onSelect === void 0 ? void 0 : onSelect(option, mergedMeasurePrefix);\n  }; // ============================= KeyEvent =============================\n  // Check if hit the measure keyword\n\n\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var which = event.which;\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event); // Skip if not measuring\n\n    if (!mergedMeasuring) {\n      return;\n    }\n\n    if (which === KeyCode.UP || which === KeyCode.DOWN) {\n      // Control arrow function\n      var optionLen = options.length;\n      var offset = which === KeyCode.UP ? -1 : 1;\n      var newActiveIndex = (activeIndex + offset + optionLen) % optionLen;\n      setActiveIndex(newActiveIndex);\n      event.preventDefault();\n    } else if (which === KeyCode.ESC) {\n      stopMeasure();\n    } else if (which === KeyCode.ENTER) {\n      // Measure hit\n      event.preventDefault();\n\n      if (!options.length) {\n        stopMeasure();\n        return;\n      }\n\n      var option = options[activeIndex];\n      selectOption(option);\n    }\n  };\n  /**\n   * When to start measure:\n   * 1. When user press `prefix`\n   * 2. When measureText !== prevMeasureText\n   *  - If measure hit\n   *  - If measuring\n   *\n   * When to stop measure:\n   * 1. Selection is out of range\n   * 2. Contains `space`\n   * 3. ESC or select one\n   */\n\n\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    var key = event.key,\n        which = event.which;\n    var target = event.target;\n    var selectionStartText = getBeforeSelectionText(target);\n\n    var _getLastMeasureIndex = getLastMeasureIndex(selectionStartText, mergedPrefix),\n        measureIndex = _getLastMeasureIndex.location,\n        nextMeasurePrefix = _getLastMeasureIndex.prefix; // If the client implements an onKeyUp handler, call it\n\n\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp(event); // Skip if match the white key list\n\n    if ([KeyCode.ESC, KeyCode.UP, KeyCode.DOWN, KeyCode.ENTER].indexOf(which) !== -1) {\n      return;\n    }\n\n    if (measureIndex !== -1) {\n      var nextMeasureText = selectionStartText.slice(measureIndex + nextMeasurePrefix.length);\n      var validateMeasure = validateSearch(nextMeasureText, mergedProps);\n      var matchOption = !!getOptions(nextMeasureText).length;\n\n      if (validateMeasure) {\n        if (key === nextMeasurePrefix || key === 'Shift' || mergedMeasuring || nextMeasureText !== mergedMeasureText && matchOption) {\n          startMeasure(nextMeasureText, nextMeasurePrefix, measureIndex);\n        }\n      } else if (mergedMeasuring) {\n        // Stop if measureText is invalidate\n        stopMeasure();\n      }\n      /**\n       * We will trigger `onSearch` to developer since they may use for async update.\n       * If met `space` means user finished searching.\n       */\n\n\n      if (onSearch && validateMeasure) {\n        onSearch(nextMeasureText, nextMeasurePrefix);\n      }\n    } else if (mergedMeasuring) {\n      stopMeasure();\n    }\n  };\n\n  var onInternalPressEnter = function onInternalPressEnter(event) {\n    if (!mergedMeasuring && onPressEnter) {\n      onPressEnter(event);\n    }\n  }; // ============================ Focus Blur ============================\n\n\n  var focusRef = useRef();\n\n  var onInternalFocus = function onInternalFocus(event) {\n    window.clearTimeout(focusRef.current);\n\n    if (!isFocus && event && onFocus) {\n      onFocus(event);\n    }\n\n    setIsFocus(true);\n  };\n\n  var onInternalBlur = function onInternalBlur(event) {\n    focusRef.current = window.setTimeout(function () {\n      setIsFocus(false);\n      stopMeasure();\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(event);\n    }, 0);\n  };\n\n  var onDropdownFocus = function onDropdownFocus() {\n    onInternalFocus();\n  };\n\n  var onDropdownBlur = function onDropdownBlur() {\n    onInternalBlur();\n  }; // ============================== Render ==============================\n\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, _extends({\n    ref: textareaRef,\n    value: mergedValue\n  }, restProps, {\n    onChange: onInternalChange,\n    onKeyDown: onInternalKeyDown,\n    onKeyUp: onInternalKeyUp,\n    onPressEnter: onInternalPressEnter,\n    onFocus: onInternalFocus,\n    onBlur: onInternalBlur\n  })), mergedMeasuring && /*#__PURE__*/React.createElement(\"div\", {\n    ref: measureRef,\n    className: \"\".concat(prefixCls, \"-measure\")\n  }, mergedValue.slice(0, mergedMeasureLocation), /*#__PURE__*/React.createElement(MentionsContext.Provider, {\n    value: {\n      notFoundContent: notFoundContent,\n      activeIndex: activeIndex,\n      setActiveIndex: setActiveIndex,\n      selectOption: selectOption,\n      onFocus: onDropdownFocus,\n      onBlur: onDropdownBlur\n    }\n  }, /*#__PURE__*/React.createElement(KeywordTrigger, {\n    prefixCls: prefixCls,\n    transitionName: transitionName,\n    placement: placement,\n    direction: direction,\n    options: options,\n    visible: true,\n    getPopupContainer: getPopupContainer,\n    dropdownClassName: dropdownClassName\n  }, /*#__PURE__*/React.createElement(\"span\", null, mergedMeasurePrefix))), mergedValue.slice(mergedMeasureLocation + mergedMeasurePrefix.length)));\n});\nMentions.defaultProps = {\n  prefixCls: 'rc-mentions',\n  prefix: '@',\n  split: ' ',\n  validateSearch: defaultValidateSearch,\n  filterOption: defaultFilterOption,\n  notFoundContent: 'Not Found',\n  rows: 1\n};\nMentions.Option = Option;\nexport default Mentions;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;AAC/V,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,YAAY,IAAIC,mBAAmB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,IAAIC,qBAAqB,QAAQ,QAAQ;AACzL,OAAOC,cAAc,MAAM,wBAAwB;AACnD,IAAIC,QAAQ,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACjE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,eAAe,GAAGP,KAAK,CAACO,eAAe;IACvCC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,IAAI,GAAGX,KAAK,CAACW,IAAI;IACjBhB,cAAc,GAAGK,KAAK,CAACL,cAAc;IACrCN,YAAY,GAAGW,KAAK,CAACX,YAAY;IACjCuB,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,MAAM,GAAGnB,KAAK,CAACmB,MAAM;IACrBC,cAAc,GAAGpB,KAAK,CAACoB,cAAc;IACrCC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,SAAS,GAAGtB,KAAK,CAACsB,SAAS;IAC3BC,iBAAiB,GAAGvB,KAAK,CAACuB,iBAAiB;IAC3CC,iBAAiB,GAAGxB,KAAK,CAACwB,iBAAiB;IAC3CC,SAAS,GAAGnD,wBAAwB,CAAC0B,KAAK,EAAEzB,SAAS,CAAC;EAE1D,IAAImD,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACvB,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EAE5D,IAAIwB,WAAW,GAAGxD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5DK,MAAM,EAAEqB;EACV,CAAC,CAAC,CAAC,CAAC;;EAGJ,IAAII,WAAW,GAAG/C,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAIgD,UAAU,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAE7B,IAAIiD,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIC,oBAAoB,EAAEC,qBAAqB;IAE/C,OAAO,CAACD,oBAAoB,GAAGH,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIF,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,oBAAoB,CAACG,iBAAiB,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,QAAQ;EACpQ,CAAC;EAEDxD,KAAK,CAACyD,mBAAmB,CAACrC,GAAG,EAAE,YAAY;IACzC,IAAIsC,qBAAqB,EAAEC,qBAAqB;IAEhD,OAAO;MACLC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,IAAIC,qBAAqB;QAEzB,OAAO,CAACA,qBAAqB,GAAGZ,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIO,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,KAAK,CAAC,CAAC;MAC5I,CAAC;MACDE,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIC,qBAAqB;QAEzB,OAAO,CAACA,qBAAqB,GAAGd,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIS,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,IAAI,CAAC,CAAC;MAC3I,CAAC;MACDE,QAAQ,EAAE,CAACN,qBAAqB,GAAGT,WAAW,CAACK,OAAO,MAAM,IAAI,IAAII,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,qBAAqB,CAACH,iBAAiB,MAAM,IAAI,IAAII,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACH;IAClQ,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIS,SAAS,GAAGhE,QAAQ,CAAC,KAAK,CAAC;IAC3BiE,UAAU,GAAG3E,cAAc,CAAC0E,SAAS,EAAE,CAAC,CAAC;IACzCE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAIG,UAAU,GAAGpE,QAAQ,CAAC,EAAE,CAAC;IACzBqE,UAAU,GAAG/E,cAAc,CAAC8E,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAElC,IAAIG,UAAU,GAAGxE,QAAQ,CAAC,EAAE,CAAC;IACzByE,UAAU,GAAGnF,cAAc,CAACkF,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIG,UAAU,GAAG5E,QAAQ,CAAC,CAAC,CAAC;IACxB6E,UAAU,GAAGvF,cAAc,CAACsF,UAAU,EAAE,CAAC,CAAC;IAC1CE,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEtC,IAAIG,UAAU,GAAGhF,QAAQ,CAAC,CAAC,CAAC;IACxBiF,WAAW,GAAG3F,cAAc,CAAC0F,UAAU,EAAE,CAAC,CAAC;IAC3CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC5BE,cAAc,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEnC,IAAIG,WAAW,GAAGpF,QAAQ,CAAC,KAAK,CAAC;IAC7BqF,WAAW,GAAG/F,cAAc,CAAC8F,WAAW,EAAE,CAAC,CAAC;IAC5CE,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;IACxBE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGjC,IAAIG,eAAe,GAAG7F,cAAc,CAAC,EAAE,EAAE;MACvCgC,YAAY,EAAEA,YAAY;MAC1BD,KAAK,EAAEA;IACT,CAAC,CAAC;IACE+D,gBAAgB,GAAGnG,cAAc,CAACkG,eAAe,EAAE,CAAC,CAAC;IACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG1CvF,SAAS,CAAC,YAAY;IACpB;IACA,IAAIgE,SAAS,IAAIjB,UAAU,CAACI,OAAO,EAAE;MACnCJ,UAAU,CAACI,OAAO,CAACuC,SAAS,GAAG1C,WAAW,CAAC,CAAC,CAAC0C,SAAS;IACxD;EACF,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EAEf,IAAI2B,cAAc,GAAG9F,KAAK,CAAC+F,OAAO,CAAC,YAAY;MAC7C,IAAIjE,IAAI,EAAE;QACR,IAAIkE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCnG,OAAO,CAAC,KAAK,EAAE,gFAAgF,CAAC;QAClG;QAEA,KAAK,IAAIoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,YAAY,CAACuD,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UAC/C,IAAIE,SAAS,GAAGxD,YAAY,CAACsD,CAAC,CAAC;UAC/B,IAAIG,KAAK,GAAGX,WAAW,CAACY,WAAW,CAACF,SAAS,CAAC;UAE9C,IAAIC,KAAK,IAAI,CAAC,EAAE;YACd,OAAO,CAAC,IAAI,EAAE,EAAE,EAAED,SAAS,EAAEC,KAAK,CAAC;UACrC;QACF;MACF;MAEA,OAAO,CAACnC,SAAS,EAAEI,WAAW,EAAEI,aAAa,EAAEI,eAAe,CAAC;IACjE,CAAC,EAAE,CAACjD,IAAI,EAAEqC,SAAS,EAAEtB,YAAY,EAAE8C,WAAW,EAAEpB,WAAW,EAAEI,aAAa,EAAEI,eAAe,CAAC,CAAC;IACzFyB,eAAe,GAAGjH,cAAc,CAACuG,cAAc,EAAE,CAAC,CAAC;IACnDW,eAAe,GAAGD,eAAe,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC;IACtCG,mBAAmB,GAAGH,eAAe,CAAC,CAAC,CAAC;IACxCI,qBAAqB,GAAGJ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGhD,IAAIK,UAAU,GAAG7G,KAAK,CAAC8G,WAAW,CAAC,UAAUC,iBAAiB,EAAE;IAC9D,IAAIC,IAAI,GAAGnH,OAAO,CAACgC,QAAQ,CAAC,CAACoF,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC/C,IAAIC,WAAW,GAAGD,IAAI,CAAC/F,KAAK;QACxBiG,GAAG,GAAGF,IAAI,CAACE,GAAG;MAClB,OAAO5H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2H,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;QACvDC,GAAG,EAAEA,GAAG,IAAID,WAAW,CAACxF;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC0F,MAAM,CAAC,UAAUC,MAAM,EAAE;MAC1B;MACA,IAAI9G,YAAY,KAAK,KAAK,EAAE;QAC1B,OAAO,IAAI;MACb;MAEA,OAAOA,YAAY,CAACuG,iBAAiB,EAAEO,MAAM,CAAC;IAChD,CAAC,CAAC;IACF,OAAON,IAAI;EACb,CAAC,EAAE,CAACnF,QAAQ,EAAErB,YAAY,CAAC,CAAC;EAC5B,IAAI+G,OAAO,GAAGvH,KAAK,CAAC+F,OAAO,CAAC,YAAY;IACtC,OAAOc,UAAU,CAACH,iBAAiB,CAAC;EACtC,CAAC,EAAE,CAACG,UAAU,EAAEH,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA,IAAIc,iBAAiB,GAAGxG,cAAc,CAAC,CAAC;EAExC,IAAIyG,YAAY,GAAG,SAASA,YAAYA,CAACC,eAAe,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;IAChGxD,YAAY,CAAC,IAAI,CAAC;IAClBI,cAAc,CAACkD,eAAe,CAAC;IAC/B9C,gBAAgB,CAAC+C,iBAAiB,CAAC;IACnC3C,kBAAkB,CAAC4C,mBAAmB,CAAC;IACvCxC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,IAAIyC,WAAW,GAAG,SAASA,WAAWA,CAACC,QAAQ,EAAE;IAC/C1D,YAAY,CAAC,KAAK,CAAC;IACnBY,kBAAkB,CAAC,CAAC,CAAC;IACrBR,cAAc,CAAC,EAAE,CAAC;IAClBgD,iBAAiB,CAACM,QAAQ,CAAC;EAC7B,CAAC,CAAC,CAAC;;EAGH,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAE;IACpDpC,cAAc,CAACoC,SAAS,CAAC;IACzBjG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiG,SAAS,CAAC;EACzE,CAAC;EAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE;IACtD,IAAIF,SAAS,GAAGE,KAAK,CAACC,MAAM,CAACxG,KAAK;IAClCoG,aAAa,CAACC,SAAS,CAAC;EAC1B,CAAC;EAED,IAAII,YAAY,GAAG,SAASA,YAAYA,CAACd,MAAM,EAAE;IAC/C,IAAIe,YAAY;IAEhB,IAAIC,aAAa,GAAGhB,MAAM,CAAC3F,KAAK;MAC5B4G,YAAY,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;IAEhE,IAAIE,mBAAmB,GAAG5H,kBAAkB,CAAC+E,WAAW,EAAE;QACxDZ,eAAe,EAAE6B,qBAAqB;QACtC6B,UAAU,EAAEF,YAAY;QACxB/G,MAAM,EAAEmF,mBAAmB;QAC3B+B,cAAc,EAAE,CAACL,YAAY,GAAGlF,WAAW,CAAC,CAAC,MAAM,IAAI,IAAIkF,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACK,cAAc;QACzHjH,KAAK,EAAEA;MACT,CAAC,CAAC;MACEkH,IAAI,GAAGH,mBAAmB,CAACG,IAAI;MAC/BC,iBAAiB,GAAGJ,mBAAmB,CAACI,iBAAiB;IAE7Db,aAAa,CAACY,IAAI,CAAC;IACnBd,WAAW,CAAC,YAAY;MACtB;MACAhH,iBAAiB,CAACsC,WAAW,CAAC,CAAC,EAAEyF,iBAAiB,CAAC;IACrD,CAAC,CAAC;IACFxG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkF,MAAM,EAAEX,mBAAmB,CAAC;EAC3F,CAAC,CAAC,CAAC;EACH;;EAGA,IAAIkC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACvB/G,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC8G,KAAK,CAAC,CAAC,CAAC;;IAExE,IAAI,CAACrC,eAAe,EAAE;MACpB;IACF;IAEA,IAAIsC,KAAK,KAAKjJ,OAAO,CAACkJ,EAAE,IAAID,KAAK,KAAKjJ,OAAO,CAACmJ,IAAI,EAAE;MAClD;MACA,IAAIC,SAAS,GAAG3B,OAAO,CAACnB,MAAM;MAC9B,IAAI+C,MAAM,GAAGJ,KAAK,KAAKjJ,OAAO,CAACkJ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,IAAII,cAAc,GAAG,CAACjE,WAAW,GAAGgE,MAAM,GAAGD,SAAS,IAAIA,SAAS;MACnE9D,cAAc,CAACgE,cAAc,CAAC;MAC9BN,KAAK,CAACO,cAAc,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIN,KAAK,KAAKjJ,OAAO,CAACwJ,GAAG,EAAE;MAChCzB,WAAW,CAAC,CAAC;IACf,CAAC,MAAM,IAAIkB,KAAK,KAAKjJ,OAAO,CAACyJ,KAAK,EAAE;MAClC;MACAT,KAAK,CAACO,cAAc,CAAC,CAAC;MAEtB,IAAI,CAAC9B,OAAO,CAACnB,MAAM,EAAE;QACnByB,WAAW,CAAC,CAAC;QACb;MACF;MAEA,IAAIP,MAAM,GAAGC,OAAO,CAACpC,WAAW,CAAC;MACjCiD,YAAY,CAACd,MAAM,CAAC;IACtB;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,IAAIkC,eAAe,GAAG,SAASA,eAAeA,CAACV,KAAK,EAAE;IACpD,IAAI1B,GAAG,GAAG0B,KAAK,CAAC1B,GAAG;MACf2B,KAAK,GAAGD,KAAK,CAACC,KAAK;IACvB,IAAIZ,MAAM,GAAGW,KAAK,CAACX,MAAM;IACzB,IAAIsB,kBAAkB,GAAG/I,sBAAsB,CAACyH,MAAM,CAAC;IAEvD,IAAIuB,oBAAoB,GAAG/I,mBAAmB,CAAC8I,kBAAkB,EAAE5G,YAAY,CAAC;MAC5E8G,YAAY,GAAGD,oBAAoB,CAACE,QAAQ;MAC5CjC,iBAAiB,GAAG+B,oBAAoB,CAAClI,MAAM,CAAC,CAAC;;IAGrDS,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6G,KAAK,CAAC,CAAC,CAAC;;IAElE,IAAI,CAAChJ,OAAO,CAACwJ,GAAG,EAAExJ,OAAO,CAACkJ,EAAE,EAAElJ,OAAO,CAACmJ,IAAI,EAAEnJ,OAAO,CAACyJ,KAAK,CAAC,CAACM,OAAO,CAACd,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MAChF;IACF;IAEA,IAAIY,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,IAAIjC,eAAe,GAAG+B,kBAAkB,CAACK,KAAK,CAACH,YAAY,GAAGhC,iBAAiB,CAACvB,MAAM,CAAC;MACvF,IAAI2D,eAAe,GAAGjJ,cAAc,CAAC4G,eAAe,EAAE1E,WAAW,CAAC;MAClE,IAAIgH,WAAW,GAAG,CAAC,CAACnD,UAAU,CAACa,eAAe,CAAC,CAACtB,MAAM;MAEtD,IAAI2D,eAAe,EAAE;QACnB,IAAI3C,GAAG,KAAKO,iBAAiB,IAAIP,GAAG,KAAK,OAAO,IAAIX,eAAe,IAAIiB,eAAe,KAAKhB,iBAAiB,IAAIsD,WAAW,EAAE;UAC3HvC,YAAY,CAACC,eAAe,EAAEC,iBAAiB,EAAEgC,YAAY,CAAC;QAChE;MACF,CAAC,MAAM,IAAIlD,eAAe,EAAE;QAC1B;QACAoB,WAAW,CAAC,CAAC;MACf;MACA;AACN;AACA;AACA;;MAGM,IAAI1F,QAAQ,IAAI4H,eAAe,EAAE;QAC/B5H,QAAQ,CAACuF,eAAe,EAAEC,iBAAiB,CAAC;MAC9C;IACF,CAAC,MAAM,IAAIlB,eAAe,EAAE;MAC1BoB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAIoC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACnB,KAAK,EAAE;IAC9D,IAAI,CAACrC,eAAe,IAAIvE,YAAY,EAAE;MACpCA,YAAY,CAAC4G,KAAK,CAAC;IACrB;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIoB,QAAQ,GAAGhK,MAAM,CAAC,CAAC;EAEvB,IAAIiK,eAAe,GAAG,SAASA,eAAeA,CAACrB,KAAK,EAAE;IACpDsB,MAAM,CAACC,YAAY,CAACH,QAAQ,CAAC5G,OAAO,CAAC;IAErC,IAAI,CAACiC,OAAO,IAAIuD,KAAK,IAAIzG,OAAO,EAAE;MAChCA,OAAO,CAACyG,KAAK,CAAC;IAChB;IAEAtD,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI8E,cAAc,GAAG,SAASA,cAAcA,CAACxB,KAAK,EAAE;IAClDoB,QAAQ,CAAC5G,OAAO,GAAG8G,MAAM,CAACG,UAAU,CAAC,YAAY;MAC/C/E,UAAU,CAAC,KAAK,CAAC;MACjBqC,WAAW,CAAC,CAAC;MACbvF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACwG,KAAK,CAAC;IAC/D,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,IAAI0B,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CL,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,IAAIM,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CH,cAAc,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC;;EAGH,OAAO,aAAatK,KAAK,CAAC0K,aAAa,CAAC,KAAK,EAAE;IAC7CpJ,SAAS,EAAE3B,UAAU,CAAC0B,SAAS,EAAEC,SAAS,CAAC;IAC3CC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAavB,KAAK,CAAC0K,aAAa,CAACtK,QAAQ,EAAEd,QAAQ,CAAC;IACrD8B,GAAG,EAAE6B,WAAW;IAChBtB,KAAK,EAAEgE;EACT,CAAC,EAAE/C,SAAS,EAAE;IACZb,QAAQ,EAAEkG,gBAAgB;IAC1BjG,SAAS,EAAE6G,iBAAiB;IAC5B5G,OAAO,EAAEuH,eAAe;IACxBtH,YAAY,EAAE+H,oBAAoB;IAClC5H,OAAO,EAAE8H,eAAe;IACxB7H,MAAM,EAAEgI;EACV,CAAC,CAAC,CAAC,EAAE7D,eAAe,IAAI,aAAazG,KAAK,CAAC0K,aAAa,CAAC,KAAK,EAAE;IAC9DtJ,GAAG,EAAE8B,UAAU;IACf5B,SAAS,EAAE,EAAE,CAACqJ,MAAM,CAACtJ,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEsE,WAAW,CAACmE,KAAK,CAAC,CAAC,EAAElD,qBAAqB,CAAC,EAAE,aAAa5G,KAAK,CAAC0K,aAAa,CAACpK,eAAe,CAACsK,QAAQ,EAAE;IACzGjJ,KAAK,EAAE;MACLD,eAAe,EAAEA,eAAe;MAChCyD,WAAW,EAAEA,WAAW;MACxBC,cAAc,EAAEA,cAAc;MAC9BgD,YAAY,EAAEA,YAAY;MAC1B/F,OAAO,EAAEmI,eAAe;MACxBlI,MAAM,EAAEmI;IACV;EACF,CAAC,EAAE,aAAazK,KAAK,CAAC0K,aAAa,CAACrK,cAAc,EAAE;IAClDgB,SAAS,EAAEA,SAAS;IACpBkB,cAAc,EAAEA,cAAc;IAC9BC,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpB8E,OAAO,EAAEA,OAAO;IAChBsD,OAAO,EAAE,IAAI;IACbnI,iBAAiB,EAAEA,iBAAiB;IACpCC,iBAAiB,EAAEA;EACrB,CAAC,EAAE,aAAa3C,KAAK,CAAC0K,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE/D,mBAAmB,CAAC,CAAC,CAAC,EAAEhB,WAAW,CAACmE,KAAK,CAAClD,qBAAqB,GAAGD,mBAAmB,CAACP,MAAM,CAAC,CAAC,CAAC;AACnJ,CAAC,CAAC;AACFnF,QAAQ,CAAC6J,YAAY,GAAG;EACtBzJ,SAAS,EAAE,aAAa;EACxBG,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,GAAG;EACVX,cAAc,EAAEC,qBAAqB;EACrCP,YAAY,EAAEC,mBAAmB;EACjCiB,eAAe,EAAE,WAAW;EAC5BqJ,IAAI,EAAE;AACR,CAAC;AACD9J,QAAQ,CAACV,MAAM,GAAGA,MAAM;AACxB,eAAeU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}