{"ast": null, "code": "import _isFunction from \"lodash/isFunction\";\nimport _range from \"lodash/range\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Brush\n */\nimport React, { PureComponent, Children } from 'react';\nimport classNames from 'classnames';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nvar createScale = function createScale(_ref) {\n  var data = _ref.data,\n    startIndex = _ref.startIndex,\n    endIndex = _ref.endIndex,\n    x = _ref.x,\n    width = _ref.width,\n    travellerWidth = _ref.travellerWidth;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(_range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(function (entry) {\n    return scale(entry);\n  });\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale: scale,\n    scaleValues: scaleValues\n  };\n};\nvar isTouch = function isTouch(e) {\n  return e.changedTouches && !!e.changedTouches.length;\n};\nexport var Brush = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Brush, _PureComponent);\n  var _super = _createSuper(Brush);\n  function Brush(props) {\n    var _this;\n    _classCallCheck(this, Brush);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"handleDrag\", function (e) {\n      if (_this.leaveTimer) {\n        clearTimeout(_this.leaveTimer);\n        _this.leaveTimer = null;\n      }\n      if (_this.state.isTravellerMoving) {\n        _this.handleTravellerMove(e);\n      } else if (_this.state.isSlideMoving) {\n        _this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleTouchMove\", function (e) {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        _this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleDragEnd\", function () {\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      });\n      _this.detachDragEndListener();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleLeaveWrapper\", function () {\n      if (_this.state.isTravellerMoving || _this.state.isSlideMoving) {\n        _this.leaveTimer = window.setTimeout(_this.handleDragEnd, _this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleEnterSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleLeaveSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleSlideDragStart\", function (e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      _this.attachDragEndListener();\n    });\n    _this.travellerDragStartHandlers = {\n      startX: _this.handleTravellerDragStart.bind(_assertThisInitialized(_this), 'startX'),\n      endX: _this.handleTravellerDragStart.bind(_assertThisInitialized(_this), 'endX')\n    };\n    _this.state = {};\n    return _this;\n  }\n  _createClass(Brush, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      this.detachDragEndListener();\n    }\n  }, {\n    key: \"getIndex\",\n    value: function getIndex(_ref2) {\n      var startX = _ref2.startX,\n        endX = _ref2.endX;\n      var scaleValues = this.state.scaleValues;\n      var _this$props = this.props,\n        gap = _this$props.gap,\n        data = _this$props.data;\n      var lastIndex = data.length - 1;\n      var min = Math.min(startX, endX);\n      var max = Math.max(startX, endX);\n      var minIndex = Brush.getIndexInRange(scaleValues, min);\n      var maxIndex = Brush.getIndexInRange(scaleValues, max);\n      return {\n        startIndex: minIndex - minIndex % gap,\n        endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n      };\n    }\n  }, {\n    key: \"getTextOfTick\",\n    value: function getTextOfTick(index) {\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        tickFormatter = _this$props2.tickFormatter,\n        dataKey = _this$props2.dataKey;\n      var text = getValueByDataKey(data[index], dataKey, index);\n      return _isFunction(tickFormatter) ? tickFormatter(text, index) : text;\n    }\n  }, {\n    key: \"attachDragEndListener\",\n    value: function attachDragEndListener() {\n      window.addEventListener('mouseup', this.handleDragEnd, true);\n      window.addEventListener('touchend', this.handleDragEnd, true);\n      window.addEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"detachDragEndListener\",\n    value: function detachDragEndListener() {\n      window.removeEventListener('mouseup', this.handleDragEnd, true);\n      window.removeEventListener('touchend', this.handleDragEnd, true);\n      window.removeEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"handleSlideDrag\",\n    value: function handleSlideDrag(e) {\n      var _this$state = this.state,\n        slideMoveStartX = _this$state.slideMoveStartX,\n        startX = _this$state.startX,\n        endX = _this$state.endX;\n      var _this$props3 = this.props,\n        x = _this$props3.x,\n        width = _this$props3.width,\n        travellerWidth = _this$props3.travellerWidth,\n        startIndex = _this$props3.startIndex,\n        endIndex = _this$props3.endIndex,\n        onChange = _this$props3.onChange;\n      var delta = e.pageX - slideMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - startX, x - endX);\n      }\n      var newIndex = this.getIndex({\n        startX: startX + delta,\n        endX: endX + delta\n      });\n      if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n        onChange(newIndex);\n      }\n      this.setState({\n        startX: startX + delta,\n        endX: endX + delta,\n        slideMoveStartX: e.pageX\n      });\n    }\n  }, {\n    key: \"handleTravellerDragStart\",\n    value: function handleTravellerDragStart(id, e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isSlideMoving: false,\n        isTravellerMoving: true,\n        movingTravellerId: id,\n        brushMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    }\n  }, {\n    key: \"handleTravellerMove\",\n    value: function handleTravellerMove(e) {\n      var _this$setState;\n      var _this$state2 = this.state,\n        brushMoveStartX = _this$state2.brushMoveStartX,\n        movingTravellerId = _this$state2.movingTravellerId,\n        endX = _this$state2.endX,\n        startX = _this$state2.startX;\n      var prevValue = this.state[movingTravellerId];\n      var _this$props4 = this.props,\n        x = _this$props4.x,\n        width = _this$props4.width,\n        travellerWidth = _this$props4.travellerWidth,\n        onChange = _this$props4.onChange,\n        gap = _this$props4.gap,\n        data = _this$props4.data;\n      var params = {\n        startX: this.state.startX,\n        endX: this.state.endX\n      };\n      var delta = e.pageX - brushMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - prevValue);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - prevValue);\n      }\n      params[movingTravellerId] = prevValue + delta;\n      var newIndex = this.getIndex(params);\n      var startIndex = newIndex.startIndex,\n        endIndex = newIndex.endIndex;\n      var isFullGap = function isFullGap() {\n        var lastIndex = data.length - 1;\n        if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n          return true;\n        }\n        return false;\n      };\n      this.setState((_this$setState = {}, _defineProperty(_this$setState, movingTravellerId, prevValue + delta), _defineProperty(_this$setState, \"brushMoveStartX\", e.pageX), _this$setState), function () {\n        if (onChange) {\n          if (isFullGap()) {\n            onChange(newIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        y = _this$props5.y,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        fill = _this$props5.fill,\n        stroke = _this$props5.stroke;\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        stroke: stroke,\n        fill: fill,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderPanorama\",\n    value: function renderPanorama() {\n      var _this$props6 = this.props,\n        x = _this$props6.x,\n        y = _this$props6.y,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        data = _this$props6.data,\n        children = _this$props6.children,\n        padding = _this$props6.padding;\n      var chartElement = Children.only(children);\n      if (!chartElement) {\n        return null;\n      }\n      return /*#__PURE__*/React.cloneElement(chartElement, {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        margin: padding,\n        compact: true,\n        data: data\n      });\n    }\n  }, {\n    key: \"renderTravellerLayer\",\n    value: function renderTravellerLayer(travellerX, id) {\n      var _this$props7 = this.props,\n        y = _this$props7.y,\n        travellerWidth = _this$props7.travellerWidth,\n        height = _this$props7.height,\n        traveller = _this$props7.traveller;\n      var x = Math.max(travellerX, this.props.x);\n      var travellerProps = _objectSpread(_objectSpread({}, filterProps(this.props)), {}, {\n        x: x,\n        y: y,\n        width: travellerWidth,\n        height: height\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-traveller\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.travellerDragStartHandlers[id],\n        onTouchStart: this.travellerDragStartHandlers[id],\n        style: {\n          cursor: 'col-resize'\n        }\n      }, Brush.renderTraveller(traveller, travellerProps));\n    }\n  }, {\n    key: \"renderSlide\",\n    value: function renderSlide(startX, endX) {\n      var _this$props8 = this.props,\n        y = _this$props8.y,\n        height = _this$props8.height,\n        stroke = _this$props8.stroke,\n        travellerWidth = _this$props8.travellerWidth;\n      var x = Math.min(startX, endX) + travellerWidth;\n      var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        className: \"recharts-brush-slide\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.handleSlideDragStart,\n        onTouchStart: this.handleSlideDragStart,\n        style: {\n          cursor: 'move'\n        },\n        stroke: \"none\",\n        fill: stroke,\n        fillOpacity: 0.2,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderText\",\n    value: function renderText() {\n      var _this$props9 = this.props,\n        startIndex = _this$props9.startIndex,\n        endIndex = _this$props9.endIndex,\n        y = _this$props9.y,\n        height = _this$props9.height,\n        travellerWidth = _this$props9.travellerWidth,\n        stroke = _this$props9.stroke;\n      var _this$state3 = this.state,\n        startX = _this$state3.startX,\n        endX = _this$state3.endX;\n      var offset = 5;\n      var attrs = {\n        pointerEvents: 'none',\n        fill: stroke\n      };\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-texts\"\n      }, /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"end\",\n        verticalAnchor: \"middle\",\n        x: Math.min(startX, endX) - offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(startIndex)), /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"start\",\n        verticalAnchor: \"middle\",\n        x: Math.max(startX, endX) + travellerWidth + offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(endIndex)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props10 = this.props,\n        data = _this$props10.data,\n        className = _this$props10.className,\n        children = _this$props10.children,\n        x = _this$props10.x,\n        y = _this$props10.y,\n        width = _this$props10.width,\n        height = _this$props10.height,\n        alwaysShowText = _this$props10.alwaysShowText;\n      var _this$state4 = this.state,\n        startX = _this$state4.startX,\n        endX = _this$state4.endX,\n        isTextActive = _this$state4.isTextActive,\n        isSlideMoving = _this$state4.isSlideMoving,\n        isTravellerMoving = _this$state4.isTravellerMoving;\n      if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n        return null;\n      }\n      var layerClass = classNames('recharts-brush', className);\n      var isPanoramic = React.Children.count(children) === 1;\n      var style = generatePrefixStyle('userSelect', 'none');\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        onMouseLeave: this.handleLeaveWrapper,\n        onTouchMove: this.handleTouchMove,\n        style: style\n      }, this.renderBackground(), isPanoramic && this.renderPanorama(), this.renderSlide(startX, endX), this.renderTravellerLayer(startX, 'startX'), this.renderTravellerLayer(endX, 'endX'), (isTextActive || isSlideMoving || isTravellerMoving || alwaysShowText) && this.renderText());\n    }\n  }], [{\n    key: \"renderDefaultTraveller\",\n    value: function renderDefaultTraveller(props) {\n      var x = props.x,\n        y = props.y,\n        width = props.width,\n        height = props.height,\n        stroke = props.stroke;\n      var lineY = Math.floor(y + height / 2) - 1;\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        fill: stroke,\n        stroke: \"none\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY,\n        x2: x + width - 1,\n        y2: lineY,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY + 2,\n        x2: x + width - 1,\n        y2: lineY + 2,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }));\n    }\n  }, {\n    key: \"renderTraveller\",\n    value: function renderTraveller(option, props) {\n      var rectangle;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = Brush.renderDefaultTraveller(props);\n      }\n      return rectangle;\n    }\n  }, {\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        x = nextProps.x,\n        travellerWidth = nextProps.travellerWidth,\n        updateId = nextProps.updateId,\n        startIndex = nextProps.startIndex,\n        endIndex = nextProps.endIndex;\n      if (data !== prevState.prevData || updateId !== prevState.prevUpdateId) {\n        return _objectSpread({\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width\n        }, data && data.length ? createScale({\n          data: data,\n          width: width,\n          x: x,\n          travellerWidth: travellerWidth,\n          startIndex: startIndex,\n          endIndex: endIndex\n        }) : {\n          scale: null,\n          scaleValues: null\n        });\n      }\n      if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n        prevState.scale.range([x, x + width - travellerWidth]);\n        var scaleValues = prevState.scale.domain().map(function (entry) {\n          return prevState.scale(entry);\n        });\n        return {\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width,\n          startX: prevState.scale(nextProps.startIndex),\n          endX: prevState.scale(nextProps.endIndex),\n          scaleValues: scaleValues\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getIndexInRange\",\n    value: function getIndexInRange(range, x) {\n      var len = range.length;\n      var start = 0;\n      var end = len - 1;\n      while (end - start > 1) {\n        var middle = Math.floor((start + end) / 2);\n        if (range[middle] > x) {\n          end = middle;\n        } else {\n          start = middle;\n        }\n      }\n      return x >= range[end] ? end : start;\n    }\n  }]);\n  return Brush;\n}(PureComponent);\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});", "map": {"version": 3, "names": ["_isFunction", "_range", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "Children", "classNames", "scalePoint", "Layer", "Text", "getValueByDataKey", "isNumber", "generatePrefixStyle", "filterProps", "createScale", "_ref", "data", "startIndex", "endIndex", "x", "width", "traveller<PERSON><PERSON><PERSON>", "len", "scale", "domain", "range", "scaleValues", "map", "entry", "isTextActive", "isSlideMoving", "isTravellerMoving", "startX", "endX", "is<PERSON><PERSON>ch", "changedTouches", "Brush", "_PureComponent", "_super", "_this", "leaveTimer", "clearTimeout", "state", "handleTravellerMove", "handleSlideDrag", "handleDrag", "setState", "detachDragEndListener", "window", "setTimeout", "handleDragEnd", "leaveTimeOut", "event", "slideMoveStartX", "pageX", "attachDragEndListener", "travellerDragStartHandlers", "handleTravellerDragStart", "componentWillUnmount", "getIndex", "_ref2", "_this$props", "gap", "lastIndex", "min", "Math", "max", "minIndex", "getIndexInRange", "maxIndex", "getTextOfTick", "index", "_this$props2", "tick<PERSON><PERSON><PERSON><PERSON>", "dataKey", "text", "addEventListener", "removeEventListener", "_this$state", "_this$props3", "onChange", "delta", "newIndex", "id", "movingTravellerId", "brushMoveStartX", "_this$setState", "_this$state2", "prevValue", "_this$props4", "params", "isFullGap", "renderBackground", "_this$props5", "y", "height", "fill", "stroke", "createElement", "renderPanorama", "_this$props6", "children", "padding", "chartElement", "only", "cloneElement", "margin", "compact", "renderTravellerLayer", "travellerX", "_this$props7", "traveller", "travellerProps", "className", "onMouseEnter", "handleEnterSlideOrTraveller", "onMouseLeave", "handleLeaveSlideOrTraveller", "onMouseDown", "onTouchStart", "style", "cursor", "renderTraveller", "renderSlide", "_this$props8", "abs", "handleSlideDragStart", "fillOpacity", "renderText", "_this$props9", "_this$state3", "offset", "attrs", "pointerEvents", "textAnchor", "verticalAnchor", "render", "_this$props10", "alwaysShowText", "_this$state4", "layerClass", "isPanoramic", "count", "handleLeaveWrapper", "onTouchMove", "handleTouchMove", "renderDefaultTraveller", "lineY", "floor", "Fragment", "x1", "y1", "x2", "y2", "option", "rectangle", "isValidElement", "getDerivedStateFromProps", "nextProps", "prevState", "updateId", "prevData", "prevUpdateId", "prevTravellerWidth", "prevX", "prevWidth", "start", "end", "middle", "top", "right", "bottom", "left"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/Brush.js"], "sourcesContent": ["import _isFunction from \"lodash/isFunction\";\nimport _range from \"lodash/range\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Brush\n */\nimport React, { PureComponent, Children } from 'react';\nimport classNames from 'classnames';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nvar createScale = function createScale(_ref) {\n  var data = _ref.data,\n    startIndex = _ref.startIndex,\n    endIndex = _ref.endIndex,\n    x = _ref.x,\n    width = _ref.width,\n    travellerWidth = _ref.travellerWidth;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(_range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(function (entry) {\n    return scale(entry);\n  });\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale: scale,\n    scaleValues: scaleValues\n  };\n};\nvar isTouch = function isTouch(e) {\n  return e.changedTouches && !!e.changedTouches.length;\n};\nexport var Brush = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Brush, _PureComponent);\n  var _super = _createSuper(Brush);\n  function Brush(props) {\n    var _this;\n    _classCallCheck(this, Brush);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"handleDrag\", function (e) {\n      if (_this.leaveTimer) {\n        clearTimeout(_this.leaveTimer);\n        _this.leaveTimer = null;\n      }\n      if (_this.state.isTravellerMoving) {\n        _this.handleTravellerMove(e);\n      } else if (_this.state.isSlideMoving) {\n        _this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleTouchMove\", function (e) {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        _this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleDragEnd\", function () {\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      });\n      _this.detachDragEndListener();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleLeaveWrapper\", function () {\n      if (_this.state.isTravellerMoving || _this.state.isSlideMoving) {\n        _this.leaveTimer = window.setTimeout(_this.handleDragEnd, _this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleEnterSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleLeaveSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleSlideDragStart\", function (e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      _this.attachDragEndListener();\n    });\n    _this.travellerDragStartHandlers = {\n      startX: _this.handleTravellerDragStart.bind(_assertThisInitialized(_this), 'startX'),\n      endX: _this.handleTravellerDragStart.bind(_assertThisInitialized(_this), 'endX')\n    };\n    _this.state = {};\n    return _this;\n  }\n  _createClass(Brush, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      this.detachDragEndListener();\n    }\n  }, {\n    key: \"getIndex\",\n    value: function getIndex(_ref2) {\n      var startX = _ref2.startX,\n        endX = _ref2.endX;\n      var scaleValues = this.state.scaleValues;\n      var _this$props = this.props,\n        gap = _this$props.gap,\n        data = _this$props.data;\n      var lastIndex = data.length - 1;\n      var min = Math.min(startX, endX);\n      var max = Math.max(startX, endX);\n      var minIndex = Brush.getIndexInRange(scaleValues, min);\n      var maxIndex = Brush.getIndexInRange(scaleValues, max);\n      return {\n        startIndex: minIndex - minIndex % gap,\n        endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n      };\n    }\n  }, {\n    key: \"getTextOfTick\",\n    value: function getTextOfTick(index) {\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        tickFormatter = _this$props2.tickFormatter,\n        dataKey = _this$props2.dataKey;\n      var text = getValueByDataKey(data[index], dataKey, index);\n      return _isFunction(tickFormatter) ? tickFormatter(text, index) : text;\n    }\n  }, {\n    key: \"attachDragEndListener\",\n    value: function attachDragEndListener() {\n      window.addEventListener('mouseup', this.handleDragEnd, true);\n      window.addEventListener('touchend', this.handleDragEnd, true);\n      window.addEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"detachDragEndListener\",\n    value: function detachDragEndListener() {\n      window.removeEventListener('mouseup', this.handleDragEnd, true);\n      window.removeEventListener('touchend', this.handleDragEnd, true);\n      window.removeEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"handleSlideDrag\",\n    value: function handleSlideDrag(e) {\n      var _this$state = this.state,\n        slideMoveStartX = _this$state.slideMoveStartX,\n        startX = _this$state.startX,\n        endX = _this$state.endX;\n      var _this$props3 = this.props,\n        x = _this$props3.x,\n        width = _this$props3.width,\n        travellerWidth = _this$props3.travellerWidth,\n        startIndex = _this$props3.startIndex,\n        endIndex = _this$props3.endIndex,\n        onChange = _this$props3.onChange;\n      var delta = e.pageX - slideMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - startX, x - endX);\n      }\n      var newIndex = this.getIndex({\n        startX: startX + delta,\n        endX: endX + delta\n      });\n      if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n        onChange(newIndex);\n      }\n      this.setState({\n        startX: startX + delta,\n        endX: endX + delta,\n        slideMoveStartX: e.pageX\n      });\n    }\n  }, {\n    key: \"handleTravellerDragStart\",\n    value: function handleTravellerDragStart(id, e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isSlideMoving: false,\n        isTravellerMoving: true,\n        movingTravellerId: id,\n        brushMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    }\n  }, {\n    key: \"handleTravellerMove\",\n    value: function handleTravellerMove(e) {\n      var _this$setState;\n      var _this$state2 = this.state,\n        brushMoveStartX = _this$state2.brushMoveStartX,\n        movingTravellerId = _this$state2.movingTravellerId,\n        endX = _this$state2.endX,\n        startX = _this$state2.startX;\n      var prevValue = this.state[movingTravellerId];\n      var _this$props4 = this.props,\n        x = _this$props4.x,\n        width = _this$props4.width,\n        travellerWidth = _this$props4.travellerWidth,\n        onChange = _this$props4.onChange,\n        gap = _this$props4.gap,\n        data = _this$props4.data;\n      var params = {\n        startX: this.state.startX,\n        endX: this.state.endX\n      };\n      var delta = e.pageX - brushMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - prevValue);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - prevValue);\n      }\n      params[movingTravellerId] = prevValue + delta;\n      var newIndex = this.getIndex(params);\n      var startIndex = newIndex.startIndex,\n        endIndex = newIndex.endIndex;\n      var isFullGap = function isFullGap() {\n        var lastIndex = data.length - 1;\n        if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n          return true;\n        }\n        return false;\n      };\n      this.setState((_this$setState = {}, _defineProperty(_this$setState, movingTravellerId, prevValue + delta), _defineProperty(_this$setState, \"brushMoveStartX\", e.pageX), _this$setState), function () {\n        if (onChange) {\n          if (isFullGap()) {\n            onChange(newIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        y = _this$props5.y,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        fill = _this$props5.fill,\n        stroke = _this$props5.stroke;\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        stroke: stroke,\n        fill: fill,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderPanorama\",\n    value: function renderPanorama() {\n      var _this$props6 = this.props,\n        x = _this$props6.x,\n        y = _this$props6.y,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        data = _this$props6.data,\n        children = _this$props6.children,\n        padding = _this$props6.padding;\n      var chartElement = Children.only(children);\n      if (!chartElement) {\n        return null;\n      }\n      return /*#__PURE__*/React.cloneElement(chartElement, {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        margin: padding,\n        compact: true,\n        data: data\n      });\n    }\n  }, {\n    key: \"renderTravellerLayer\",\n    value: function renderTravellerLayer(travellerX, id) {\n      var _this$props7 = this.props,\n        y = _this$props7.y,\n        travellerWidth = _this$props7.travellerWidth,\n        height = _this$props7.height,\n        traveller = _this$props7.traveller;\n      var x = Math.max(travellerX, this.props.x);\n      var travellerProps = _objectSpread(_objectSpread({}, filterProps(this.props)), {}, {\n        x: x,\n        y: y,\n        width: travellerWidth,\n        height: height\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-traveller\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.travellerDragStartHandlers[id],\n        onTouchStart: this.travellerDragStartHandlers[id],\n        style: {\n          cursor: 'col-resize'\n        }\n      }, Brush.renderTraveller(traveller, travellerProps));\n    }\n  }, {\n    key: \"renderSlide\",\n    value: function renderSlide(startX, endX) {\n      var _this$props8 = this.props,\n        y = _this$props8.y,\n        height = _this$props8.height,\n        stroke = _this$props8.stroke,\n        travellerWidth = _this$props8.travellerWidth;\n      var x = Math.min(startX, endX) + travellerWidth;\n      var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        className: \"recharts-brush-slide\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.handleSlideDragStart,\n        onTouchStart: this.handleSlideDragStart,\n        style: {\n          cursor: 'move'\n        },\n        stroke: \"none\",\n        fill: stroke,\n        fillOpacity: 0.2,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderText\",\n    value: function renderText() {\n      var _this$props9 = this.props,\n        startIndex = _this$props9.startIndex,\n        endIndex = _this$props9.endIndex,\n        y = _this$props9.y,\n        height = _this$props9.height,\n        travellerWidth = _this$props9.travellerWidth,\n        stroke = _this$props9.stroke;\n      var _this$state3 = this.state,\n        startX = _this$state3.startX,\n        endX = _this$state3.endX;\n      var offset = 5;\n      var attrs = {\n        pointerEvents: 'none',\n        fill: stroke\n      };\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-texts\"\n      }, /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"end\",\n        verticalAnchor: \"middle\",\n        x: Math.min(startX, endX) - offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(startIndex)), /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"start\",\n        verticalAnchor: \"middle\",\n        x: Math.max(startX, endX) + travellerWidth + offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(endIndex)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props10 = this.props,\n        data = _this$props10.data,\n        className = _this$props10.className,\n        children = _this$props10.children,\n        x = _this$props10.x,\n        y = _this$props10.y,\n        width = _this$props10.width,\n        height = _this$props10.height,\n        alwaysShowText = _this$props10.alwaysShowText;\n      var _this$state4 = this.state,\n        startX = _this$state4.startX,\n        endX = _this$state4.endX,\n        isTextActive = _this$state4.isTextActive,\n        isSlideMoving = _this$state4.isSlideMoving,\n        isTravellerMoving = _this$state4.isTravellerMoving;\n      if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n        return null;\n      }\n      var layerClass = classNames('recharts-brush', className);\n      var isPanoramic = React.Children.count(children) === 1;\n      var style = generatePrefixStyle('userSelect', 'none');\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        onMouseLeave: this.handleLeaveWrapper,\n        onTouchMove: this.handleTouchMove,\n        style: style\n      }, this.renderBackground(), isPanoramic && this.renderPanorama(), this.renderSlide(startX, endX), this.renderTravellerLayer(startX, 'startX'), this.renderTravellerLayer(endX, 'endX'), (isTextActive || isSlideMoving || isTravellerMoving || alwaysShowText) && this.renderText());\n    }\n  }], [{\n    key: \"renderDefaultTraveller\",\n    value: function renderDefaultTraveller(props) {\n      var x = props.x,\n        y = props.y,\n        width = props.width,\n        height = props.height,\n        stroke = props.stroke;\n      var lineY = Math.floor(y + height / 2) - 1;\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        fill: stroke,\n        stroke: \"none\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY,\n        x2: x + width - 1,\n        y2: lineY,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY + 2,\n        x2: x + width - 1,\n        y2: lineY + 2,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }));\n    }\n  }, {\n    key: \"renderTraveller\",\n    value: function renderTraveller(option, props) {\n      var rectangle;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = Brush.renderDefaultTraveller(props);\n      }\n      return rectangle;\n    }\n  }, {\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        x = nextProps.x,\n        travellerWidth = nextProps.travellerWidth,\n        updateId = nextProps.updateId,\n        startIndex = nextProps.startIndex,\n        endIndex = nextProps.endIndex;\n      if (data !== prevState.prevData || updateId !== prevState.prevUpdateId) {\n        return _objectSpread({\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width\n        }, data && data.length ? createScale({\n          data: data,\n          width: width,\n          x: x,\n          travellerWidth: travellerWidth,\n          startIndex: startIndex,\n          endIndex: endIndex\n        }) : {\n          scale: null,\n          scaleValues: null\n        });\n      }\n      if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n        prevState.scale.range([x, x + width - travellerWidth]);\n        var scaleValues = prevState.scale.domain().map(function (entry) {\n          return prevState.scale(entry);\n        });\n        return {\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width,\n          startX: prevState.scale(nextProps.startIndex),\n          endX: prevState.scale(nextProps.endIndex),\n          scaleValues: scaleValues\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getIndexInRange\",\n    value: function getIndexInRange(range, x) {\n      var len = range.length;\n      var start = 0;\n      var end = len - 1;\n      while (end - start > 1) {\n        var middle = Math.floor((start + end) / 2);\n        if (range[middle] > x) {\n          end = middle;\n        } else {\n          start = middle;\n        }\n      }\n      return x >= range[end] ? end : start;\n    }\n  }]);\n  return Brush;\n}(PureComponent);\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAACpB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGQ,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAEiB,eAAe,CAACtB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACxB,MAAM,EAAEH,MAAM,CAAC0B,yBAAyB,CAACnB,MAAM,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAER,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACoB,wBAAwB,CAACb,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC9B,MAAM,EAAE+B,KAAK,EAAE;EAAE,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,KAAK,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI+B,UAAU,GAAGD,KAAK,CAAC9B,CAAC,CAAC;IAAE+B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAErC,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEmC,cAAc,CAACH,UAAU,CAAC3B,GAAG,CAAC,EAAE2B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACjC,SAAS,EAAE0C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEzC,MAAM,CAAC4B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAAC7C,SAAS,GAAGE,MAAM,CAAC6C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC9C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEiD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEpC,MAAM,CAAC4B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAG/C,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAAChD,IAAI,CAAC,CAAC,GAAG,SAAS6C,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC7D,WAAW;MAAE8D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEpD,SAAS,EAAEuD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC9C,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAO0D,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEtD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIsB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACvE,SAAS,CAACwE,OAAO,CAAC5D,IAAI,CAACmD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG1D,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACwE,cAAc,CAACtE,IAAI,CAAC,CAAC,GAAG,SAASwD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAInD,MAAM,CAACwE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC/B,GAAG,EAAEc,GAAG,EAAEsC,KAAK,EAAE;EAAEtC,GAAG,GAAG8B,cAAc,CAAC9B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC4B,cAAc,CAAClC,GAAG,EAAEc,GAAG,EAAE;MAAEsC,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE3C,GAAG,CAACc,GAAG,CAAC,GAAGsC,KAAK;EAAE;EAAE,OAAOpD,GAAG;AAAE;AAC3O,SAAS4C,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAIjE,GAAG,GAAGkE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGmE,MAAM,CAACnE,GAAG,CAAC;AAAE;AAC5H,SAASkE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpF,OAAO,CAACmF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjF,MAAM,CAACoF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACpE,IAAI,CAACkE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpF,OAAO,CAACwF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACtD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,CAAC,GAAGJ,IAAI,CAACI,CAAC;IACVC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,cAAc,GAAGN,IAAI,CAACM,cAAc;EACtC,IAAI,CAACL,IAAI,IAAI,CAACA,IAAI,CAAC1F,MAAM,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;EACA,IAAIgG,GAAG,GAAGN,IAAI,CAAC1F,MAAM;EACrB,IAAIiG,KAAK,GAAGhB,UAAU,CAAC,CAAC,CAACiB,MAAM,CAAChH,MAAM,CAAC,CAAC,EAAE8G,GAAG,CAAC,CAAC,CAACG,KAAK,CAAC,CAACN,CAAC,EAAEA,CAAC,GAAGC,KAAK,GAAGC,cAAc,CAAC,CAAC;EACtF,IAAIK,WAAW,GAAGH,KAAK,CAACC,MAAM,CAAC,CAAC,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;IACpD,OAAOL,KAAK,CAACK,KAAK,CAAC;EACrB,CAAC,CAAC;EACF,OAAO;IACLC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,MAAM,EAAET,KAAK,CAACN,UAAU,CAAC;IACzBgB,IAAI,EAAEV,KAAK,CAACL,QAAQ,CAAC;IACrBK,KAAK,EAAEA,KAAK;IACZG,WAAW,EAAEA;EACf,CAAC;AACH,CAAC;AACD,IAAIQ,OAAO,GAAG,SAASA,OAAOA,CAAC3C,CAAC,EAAE;EAChC,OAAOA,CAAC,CAAC4C,cAAc,IAAI,CAAC,CAAC5C,CAAC,CAAC4C,cAAc,CAAC7G,MAAM;AACtD,CAAC;AACD,OAAO,IAAI8G,KAAK,GAAG,aAAa,UAAUC,cAAc,EAAE;EACxD3E,SAAS,CAAC0E,KAAK,EAAEC,cAAc,CAAC;EAChC,IAAIC,MAAM,GAAGlE,YAAY,CAACgE,KAAK,CAAC;EAChC,SAASA,KAAKA,CAAClF,KAAK,EAAE;IACpB,IAAIqF,KAAK;IACT1F,eAAe,CAAC,IAAI,EAAEuF,KAAK,CAAC;IAC5BG,KAAK,GAAGD,MAAM,CAAC5G,IAAI,CAAC,IAAI,EAAEwB,KAAK,CAAC;IAChCT,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,YAAY,EAAE,UAAUhD,CAAC,EAAE;MACxE,IAAIgD,KAAK,CAACC,UAAU,EAAE;QACpBC,YAAY,CAACF,KAAK,CAACC,UAAU,CAAC;QAC9BD,KAAK,CAACC,UAAU,GAAG,IAAI;MACzB;MACA,IAAID,KAAK,CAACG,KAAK,CAACX,iBAAiB,EAAE;QACjCQ,KAAK,CAACI,mBAAmB,CAACpD,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAIgD,KAAK,CAACG,KAAK,CAACZ,aAAa,EAAE;QACpCS,KAAK,CAACK,eAAe,CAACrD,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IACF9C,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUhD,CAAC,EAAE;MAC7E,IAAIA,CAAC,CAAC4C,cAAc,IAAI,IAAI,IAAI5C,CAAC,CAAC4C,cAAc,CAAC7G,MAAM,GAAG,CAAC,EAAE;QAC3DiH,KAAK,CAACM,UAAU,CAACtD,CAAC,CAAC4C,cAAc,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC,CAAC;IACF1F,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,eAAe,EAAE,YAAY;MAC1EA,KAAK,CAACO,QAAQ,CAAC;QACbf,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE;MACjB,CAAC,CAAC;MACFS,KAAK,CAACQ,qBAAqB,CAAC,CAAC;IAC/B,CAAC,CAAC;IACFtG,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIA,KAAK,CAACG,KAAK,CAACX,iBAAiB,IAAIQ,KAAK,CAACG,KAAK,CAACZ,aAAa,EAAE;QAC9DS,KAAK,CAACC,UAAU,GAAGQ,MAAM,CAACC,UAAU,CAACV,KAAK,CAACW,aAAa,EAAEX,KAAK,CAACrF,KAAK,CAACiG,YAAY,CAAC;MACrF;IACF,CAAC,CAAC;IACF1G,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,6BAA6B,EAAE,YAAY;MACxFA,KAAK,CAACO,QAAQ,CAAC;QACbjB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFpF,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,6BAA6B,EAAE,YAAY;MACxFA,KAAK,CAACO,QAAQ,CAAC;QACbjB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFpF,eAAe,CAACwC,sBAAsB,CAACsD,KAAK,CAAC,EAAE,sBAAsB,EAAE,UAAUhD,CAAC,EAAE;MAClF,IAAI6D,KAAK,GAAGlB,OAAO,CAAC3C,CAAC,CAAC,GAAGA,CAAC,CAAC4C,cAAc,CAAC,CAAC,CAAC,GAAG5C,CAAC;MAChDgD,KAAK,CAACO,QAAQ,CAAC;QACbf,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE,IAAI;QACnBuB,eAAe,EAAED,KAAK,CAACE;MACzB,CAAC,CAAC;MACFf,KAAK,CAACgB,qBAAqB,CAAC,CAAC;IAC/B,CAAC,CAAC;IACFhB,KAAK,CAACiB,0BAA0B,GAAG;MACjCxB,MAAM,EAAEO,KAAK,CAACkB,wBAAwB,CAACvI,IAAI,CAAC+D,sBAAsB,CAACsD,KAAK,CAAC,EAAE,QAAQ,CAAC;MACpFN,IAAI,EAAEM,KAAK,CAACkB,wBAAwB,CAACvI,IAAI,CAAC+D,sBAAsB,CAACsD,KAAK,CAAC,EAAE,MAAM;IACjF,CAAC;IACDA,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC;IAChB,OAAOH,KAAK;EACd;EACAhF,YAAY,CAAC6E,KAAK,EAAE,CAAC;IACnB5G,GAAG,EAAE,sBAAsB;IAC3BsC,KAAK,EAAE,SAAS4F,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAAClB,UAAU,EAAE;QACnBC,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;QAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;MACxB;MACA,IAAI,CAACO,qBAAqB,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE;IACDvH,GAAG,EAAE,UAAU;IACfsC,KAAK,EAAE,SAAS6F,QAAQA,CAACC,KAAK,EAAE;MAC9B,IAAI5B,MAAM,GAAG4B,KAAK,CAAC5B,MAAM;QACvBC,IAAI,GAAG2B,KAAK,CAAC3B,IAAI;MACnB,IAAIP,WAAW,GAAG,IAAI,CAACgB,KAAK,CAAChB,WAAW;MACxC,IAAImC,WAAW,GAAG,IAAI,CAAC3G,KAAK;QAC1B4G,GAAG,GAAGD,WAAW,CAACC,GAAG;QACrB9C,IAAI,GAAG6C,WAAW,CAAC7C,IAAI;MACzB,IAAI+C,SAAS,GAAG/C,IAAI,CAAC1F,MAAM,GAAG,CAAC;MAC/B,IAAI0I,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAChC,MAAM,EAAEC,IAAI,CAAC;MAChC,IAAIiC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAClC,MAAM,EAAEC,IAAI,CAAC;MAChC,IAAIkC,QAAQ,GAAG/B,KAAK,CAACgC,eAAe,CAAC1C,WAAW,EAAEsC,GAAG,CAAC;MACtD,IAAIK,QAAQ,GAAGjC,KAAK,CAACgC,eAAe,CAAC1C,WAAW,EAAEwC,GAAG,CAAC;MACtD,OAAO;QACLjD,UAAU,EAAEkD,QAAQ,GAAGA,QAAQ,GAAGL,GAAG;QACrC5C,QAAQ,EAAEmD,QAAQ,KAAKN,SAAS,GAAGA,SAAS,GAAGM,QAAQ,GAAGA,QAAQ,GAAGP;MACvE,CAAC;IACH;EACF,CAAC,EAAE;IACDtI,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,SAASwG,aAAaA,CAACC,KAAK,EAAE;MACnC,IAAIC,YAAY,GAAG,IAAI,CAACtH,KAAK;QAC3B8D,IAAI,GAAGwD,YAAY,CAACxD,IAAI;QACxByD,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAIC,IAAI,GAAGjE,iBAAiB,CAACM,IAAI,CAACuD,KAAK,CAAC,EAAEG,OAAO,EAAEH,KAAK,CAAC;MACzD,OAAOhK,WAAW,CAACkK,aAAa,CAAC,GAAGA,aAAa,CAACE,IAAI,EAAEJ,KAAK,CAAC,GAAGI,IAAI;IACvE;EACF,CAAC,EAAE;IACDnJ,GAAG,EAAE,uBAAuB;IAC5BsC,KAAK,EAAE,SAASyF,qBAAqBA,CAAA,EAAG;MACtCP,MAAM,CAAC4B,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC;MAC5DF,MAAM,CAAC4B,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC;MAC7DF,MAAM,CAAC4B,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC/B,UAAU,EAAE,IAAI,CAAC;IAC7D;EACF,CAAC,EAAE;IACDrH,GAAG,EAAE,uBAAuB;IAC5BsC,KAAK,EAAE,SAASiF,qBAAqBA,CAAA,EAAG;MACtCC,MAAM,CAAC6B,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;MAC/DF,MAAM,CAAC6B,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;MAChEF,MAAM,CAAC6B,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAChC,UAAU,EAAE,IAAI,CAAC;IAChE;EACF,CAAC,EAAE;IACDrH,GAAG,EAAE,iBAAiB;IACtBsC,KAAK,EAAE,SAAS8E,eAAeA,CAACrD,CAAC,EAAE;MACjC,IAAIuF,WAAW,GAAG,IAAI,CAACpC,KAAK;QAC1BW,eAAe,GAAGyB,WAAW,CAACzB,eAAe;QAC7CrB,MAAM,GAAG8C,WAAW,CAAC9C,MAAM;QAC3BC,IAAI,GAAG6C,WAAW,CAAC7C,IAAI;MACzB,IAAI8C,YAAY,GAAG,IAAI,CAAC7H,KAAK;QAC3BiE,CAAC,GAAG4D,YAAY,CAAC5D,CAAC;QAClBC,KAAK,GAAG2D,YAAY,CAAC3D,KAAK;QAC1BC,cAAc,GAAG0D,YAAY,CAAC1D,cAAc;QAC5CJ,UAAU,GAAG8D,YAAY,CAAC9D,UAAU;QACpCC,QAAQ,GAAG6D,YAAY,CAAC7D,QAAQ;QAChC8D,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAClC,IAAIC,KAAK,GAAG1F,CAAC,CAAC+D,KAAK,GAAGD,eAAe;MACrC,IAAI4B,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAGhB,IAAI,CAACD,GAAG,CAACiB,KAAK,EAAE9D,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGY,IAAI,EAAEd,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGW,MAAM,CAAC;MACjG,CAAC,MAAM,IAAIiD,KAAK,GAAG,CAAC,EAAE;QACpBA,KAAK,GAAGhB,IAAI,CAACC,GAAG,CAACe,KAAK,EAAE9D,CAAC,GAAGa,MAAM,EAAEb,CAAC,GAAGc,IAAI,CAAC;MAC/C;MACA,IAAIiD,QAAQ,GAAG,IAAI,CAACvB,QAAQ,CAAC;QAC3B3B,MAAM,EAAEA,MAAM,GAAGiD,KAAK;QACtBhD,IAAI,EAAEA,IAAI,GAAGgD;MACf,CAAC,CAAC;MACF,IAAI,CAACC,QAAQ,CAACjE,UAAU,KAAKA,UAAU,IAAIiE,QAAQ,CAAChE,QAAQ,KAAKA,QAAQ,KAAK8D,QAAQ,EAAE;QACtFA,QAAQ,CAACE,QAAQ,CAAC;MACpB;MACA,IAAI,CAACpC,QAAQ,CAAC;QACZd,MAAM,EAAEA,MAAM,GAAGiD,KAAK;QACtBhD,IAAI,EAAEA,IAAI,GAAGgD,KAAK;QAClB5B,eAAe,EAAE9D,CAAC,CAAC+D;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9H,GAAG,EAAE,0BAA0B;IAC/BsC,KAAK,EAAE,SAAS2F,wBAAwBA,CAAC0B,EAAE,EAAE5F,CAAC,EAAE;MAC9C,IAAI6D,KAAK,GAAGlB,OAAO,CAAC3C,CAAC,CAAC,GAAGA,CAAC,CAAC4C,cAAc,CAAC,CAAC,CAAC,GAAG5C,CAAC;MAChD,IAAI,CAACuD,QAAQ,CAAC;QACZhB,aAAa,EAAE,KAAK;QACpBC,iBAAiB,EAAE,IAAI;QACvBqD,iBAAiB,EAAED,EAAE;QACrBE,eAAe,EAAEjC,KAAK,CAACE;MACzB,CAAC,CAAC;MACF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE;IACD/H,GAAG,EAAE,qBAAqB;IAC1BsC,KAAK,EAAE,SAAS6E,mBAAmBA,CAACpD,CAAC,EAAE;MACrC,IAAI+F,cAAc;MAClB,IAAIC,YAAY,GAAG,IAAI,CAAC7C,KAAK;QAC3B2C,eAAe,GAAGE,YAAY,CAACF,eAAe;QAC9CD,iBAAiB,GAAGG,YAAY,CAACH,iBAAiB;QAClDnD,IAAI,GAAGsD,YAAY,CAACtD,IAAI;QACxBD,MAAM,GAAGuD,YAAY,CAACvD,MAAM;MAC9B,IAAIwD,SAAS,GAAG,IAAI,CAAC9C,KAAK,CAAC0C,iBAAiB,CAAC;MAC7C,IAAIK,YAAY,GAAG,IAAI,CAACvI,KAAK;QAC3BiE,CAAC,GAAGsE,YAAY,CAACtE,CAAC;QAClBC,KAAK,GAAGqE,YAAY,CAACrE,KAAK;QAC1BC,cAAc,GAAGoE,YAAY,CAACpE,cAAc;QAC5C2D,QAAQ,GAAGS,YAAY,CAACT,QAAQ;QAChClB,GAAG,GAAG2B,YAAY,CAAC3B,GAAG;QACtB9C,IAAI,GAAGyE,YAAY,CAACzE,IAAI;MAC1B,IAAI0E,MAAM,GAAG;QACX1D,MAAM,EAAE,IAAI,CAACU,KAAK,CAACV,MAAM;QACzBC,IAAI,EAAE,IAAI,CAACS,KAAK,CAACT;MACnB,CAAC;MACD,IAAIgD,KAAK,GAAG1F,CAAC,CAAC+D,KAAK,GAAG+B,eAAe;MACrC,IAAIJ,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAGhB,IAAI,CAACD,GAAG,CAACiB,KAAK,EAAE9D,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGmE,SAAS,CAAC;MACjE,CAAC,MAAM,IAAIP,KAAK,GAAG,CAAC,EAAE;QACpBA,KAAK,GAAGhB,IAAI,CAACC,GAAG,CAACe,KAAK,EAAE9D,CAAC,GAAGqE,SAAS,CAAC;MACxC;MACAE,MAAM,CAACN,iBAAiB,CAAC,GAAGI,SAAS,GAAGP,KAAK;MAC7C,IAAIC,QAAQ,GAAG,IAAI,CAACvB,QAAQ,CAAC+B,MAAM,CAAC;MACpC,IAAIzE,UAAU,GAAGiE,QAAQ,CAACjE,UAAU;QAClCC,QAAQ,GAAGgE,QAAQ,CAAChE,QAAQ;MAC9B,IAAIyE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;QACnC,IAAI5B,SAAS,GAAG/C,IAAI,CAAC1F,MAAM,GAAG,CAAC;QAC/B,IAAI8J,iBAAiB,KAAK,QAAQ,KAAKnD,IAAI,GAAGD,MAAM,GAAGf,UAAU,GAAG6C,GAAG,KAAK,CAAC,GAAG5C,QAAQ,GAAG4C,GAAG,KAAK,CAAC,CAAC,IAAI7B,IAAI,GAAGD,MAAM,IAAId,QAAQ,KAAK6C,SAAS,IAAIqB,iBAAiB,KAAK,MAAM,KAAKnD,IAAI,GAAGD,MAAM,GAAGd,QAAQ,GAAG4C,GAAG,KAAK,CAAC,GAAG7C,UAAU,GAAG6C,GAAG,KAAK,CAAC,CAAC,IAAI7B,IAAI,GAAGD,MAAM,IAAId,QAAQ,KAAK6C,SAAS,EAAE;UAC9R,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MACD,IAAI,CAACjB,QAAQ,EAAEwC,cAAc,GAAG,CAAC,CAAC,EAAE7I,eAAe,CAAC6I,cAAc,EAAEF,iBAAiB,EAAEI,SAAS,GAAGP,KAAK,CAAC,EAAExI,eAAe,CAAC6I,cAAc,EAAE,iBAAiB,EAAE/F,CAAC,CAAC+D,KAAK,CAAC,EAAEgC,cAAc,GAAG,YAAY;QACnM,IAAIN,QAAQ,EAAE;UACZ,IAAIW,SAAS,CAAC,CAAC,EAAE;YACfX,QAAQ,CAACE,QAAQ,CAAC;UACpB;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1J,GAAG,EAAE,kBAAkB;IACvBsC,KAAK,EAAE,SAAS8H,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,YAAY,GAAG,IAAI,CAAC3I,KAAK;QAC3BiE,CAAC,GAAG0E,YAAY,CAAC1E,CAAC;QAClB2E,CAAC,GAAGD,YAAY,CAACC,CAAC;QAClB1E,KAAK,GAAGyE,YAAY,CAACzE,KAAK;QAC1B2E,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5BC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBC,MAAM,GAAGJ,YAAY,CAACI,MAAM;MAC9B,OAAO,aAAa9F,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;QAC9CD,MAAM,EAAEA,MAAM;QACdD,IAAI,EAAEA,IAAI;QACV7E,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA,CAAC;QACJ1E,KAAK,EAAEA,KAAK;QACZ2E,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvK,GAAG,EAAE,gBAAgB;IACrBsC,KAAK,EAAE,SAASqI,cAAcA,CAAA,EAAG;MAC/B,IAAIC,YAAY,GAAG,IAAI,CAAClJ,KAAK;QAC3BiE,CAAC,GAAGiF,YAAY,CAACjF,CAAC;QAClB2E,CAAC,GAAGM,YAAY,CAACN,CAAC;QAClB1E,KAAK,GAAGgF,YAAY,CAAChF,KAAK;QAC1B2E,MAAM,GAAGK,YAAY,CAACL,MAAM;QAC5B/E,IAAI,GAAGoF,YAAY,CAACpF,IAAI;QACxBqF,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAIC,YAAY,GAAGlG,QAAQ,CAACmG,IAAI,CAACH,QAAQ,CAAC;MAC1C,IAAI,CAACE,YAAY,EAAE;QACjB,OAAO,IAAI;MACb;MACA,OAAO,aAAapG,KAAK,CAACsG,YAAY,CAACF,YAAY,EAAE;QACnDpF,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA,CAAC;QACJ1E,KAAK,EAAEA,KAAK;QACZ2E,MAAM,EAAEA,MAAM;QACdW,MAAM,EAAEJ,OAAO;QACfK,OAAO,EAAE,IAAI;QACb3F,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDxF,GAAG,EAAE,sBAAsB;IAC3BsC,KAAK,EAAE,SAAS8I,oBAAoBA,CAACC,UAAU,EAAE1B,EAAE,EAAE;MACnD,IAAI2B,YAAY,GAAG,IAAI,CAAC5J,KAAK;QAC3B4I,CAAC,GAAGgB,YAAY,CAAChB,CAAC;QAClBzE,cAAc,GAAGyF,YAAY,CAACzF,cAAc;QAC5C0E,MAAM,GAAGe,YAAY,CAACf,MAAM;QAC5BgB,SAAS,GAAGD,YAAY,CAACC,SAAS;MACpC,IAAI5F,CAAC,GAAG8C,IAAI,CAACC,GAAG,CAAC2C,UAAU,EAAE,IAAI,CAAC3J,KAAK,CAACiE,CAAC,CAAC;MAC1C,IAAI6F,cAAc,GAAGzK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,WAAW,CAAC,IAAI,CAAC3D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACjFiE,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA,CAAC;QACJ1E,KAAK,EAAEC,cAAc;QACrB0E,MAAM,EAAEA;MACV,CAAC,CAAC;MACF,OAAO,aAAa5F,KAAK,CAAC+F,aAAa,CAAC1F,KAAK,EAAE;QAC7CyG,SAAS,EAAE,0BAA0B;QACrCC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,WAAW,EAAE,IAAI,CAAC9D,0BAA0B,CAAC2B,EAAE,CAAC;QAChDoC,YAAY,EAAE,IAAI,CAAC/D,0BAA0B,CAAC2B,EAAE,CAAC;QACjDqC,KAAK,EAAE;UACLC,MAAM,EAAE;QACV;MACF,CAAC,EAAErF,KAAK,CAACsF,eAAe,CAACX,SAAS,EAAEC,cAAc,CAAC,CAAC;IACtD;EACF,CAAC,EAAE;IACDxL,GAAG,EAAE,aAAa;IAClBsC,KAAK,EAAE,SAAS6J,WAAWA,CAAC3F,MAAM,EAAEC,IAAI,EAAE;MACxC,IAAI2F,YAAY,GAAG,IAAI,CAAC1K,KAAK;QAC3B4I,CAAC,GAAG8B,YAAY,CAAC9B,CAAC;QAClBC,MAAM,GAAG6B,YAAY,CAAC7B,MAAM;QAC5BE,MAAM,GAAG2B,YAAY,CAAC3B,MAAM;QAC5B5E,cAAc,GAAGuG,YAAY,CAACvG,cAAc;MAC9C,IAAIF,CAAC,GAAG8C,IAAI,CAACD,GAAG,CAAChC,MAAM,EAAEC,IAAI,CAAC,GAAGZ,cAAc;MAC/C,IAAID,KAAK,GAAG6C,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC4D,GAAG,CAAC5F,IAAI,GAAGD,MAAM,CAAC,GAAGX,cAAc,EAAE,CAAC,CAAC;MACjE,OAAO,aAAalB,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;QAC9Ce,SAAS,EAAE,sBAAsB;QACjCC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,WAAW,EAAE,IAAI,CAACQ,oBAAoB;QACtCP,YAAY,EAAE,IAAI,CAACO,oBAAoB;QACvCN,KAAK,EAAE;UACLC,MAAM,EAAE;QACV,CAAC;QACDxB,MAAM,EAAE,MAAM;QACdD,IAAI,EAAEC,MAAM;QACZ8B,WAAW,EAAE,GAAG;QAChB5G,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA,CAAC;QACJ1E,KAAK,EAAEA,KAAK;QACZ2E,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvK,GAAG,EAAE,YAAY;IACjBsC,KAAK,EAAE,SAASkK,UAAUA,CAAA,EAAG;MAC3B,IAAIC,YAAY,GAAG,IAAI,CAAC/K,KAAK;QAC3B+D,UAAU,GAAGgH,YAAY,CAAChH,UAAU;QACpCC,QAAQ,GAAG+G,YAAY,CAAC/G,QAAQ;QAChC4E,CAAC,GAAGmC,YAAY,CAACnC,CAAC;QAClBC,MAAM,GAAGkC,YAAY,CAAClC,MAAM;QAC5B1E,cAAc,GAAG4G,YAAY,CAAC5G,cAAc;QAC5C4E,MAAM,GAAGgC,YAAY,CAAChC,MAAM;MAC9B,IAAIiC,YAAY,GAAG,IAAI,CAACxF,KAAK;QAC3BV,MAAM,GAAGkG,YAAY,CAAClG,MAAM;QAC5BC,IAAI,GAAGiG,YAAY,CAACjG,IAAI;MAC1B,IAAIkG,MAAM,GAAG,CAAC;MACd,IAAIC,KAAK,GAAG;QACVC,aAAa,EAAE,MAAM;QACrBrC,IAAI,EAAEC;MACR,CAAC;MACD,OAAO,aAAa9F,KAAK,CAAC+F,aAAa,CAAC1F,KAAK,EAAE;QAC7CyG,SAAS,EAAE;MACb,CAAC,EAAE,aAAa9G,KAAK,CAAC+F,aAAa,CAACzF,IAAI,EAAE1F,QAAQ,CAAC;QACjDuN,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,QAAQ;QACxBpH,CAAC,EAAE8C,IAAI,CAACD,GAAG,CAAChC,MAAM,EAAEC,IAAI,CAAC,GAAGkG,MAAM;QAClCrC,CAAC,EAAEA,CAAC,GAAGC,MAAM,GAAG;MAClB,CAAC,EAAEqC,KAAK,CAAC,EAAE,IAAI,CAAC9D,aAAa,CAACrD,UAAU,CAAC,CAAC,EAAE,aAAad,KAAK,CAAC+F,aAAa,CAACzF,IAAI,EAAE1F,QAAQ,CAAC;QAC1FuN,UAAU,EAAE,OAAO;QACnBC,cAAc,EAAE,QAAQ;QACxBpH,CAAC,EAAE8C,IAAI,CAACC,GAAG,CAAClC,MAAM,EAAEC,IAAI,CAAC,GAAGZ,cAAc,GAAG8G,MAAM;QACnDrC,CAAC,EAAEA,CAAC,GAAGC,MAAM,GAAG;MAClB,CAAC,EAAEqC,KAAK,CAAC,EAAE,IAAI,CAAC9D,aAAa,CAACpD,QAAQ,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE;IACD1F,GAAG,EAAE,QAAQ;IACbsC,KAAK,EAAE,SAAS0K,MAAMA,CAAA,EAAG;MACvB,IAAIC,aAAa,GAAG,IAAI,CAACvL,KAAK;QAC5B8D,IAAI,GAAGyH,aAAa,CAACzH,IAAI;QACzBiG,SAAS,GAAGwB,aAAa,CAACxB,SAAS;QACnCZ,QAAQ,GAAGoC,aAAa,CAACpC,QAAQ;QACjClF,CAAC,GAAGsH,aAAa,CAACtH,CAAC;QACnB2E,CAAC,GAAG2C,aAAa,CAAC3C,CAAC;QACnB1E,KAAK,GAAGqH,aAAa,CAACrH,KAAK;QAC3B2E,MAAM,GAAG0C,aAAa,CAAC1C,MAAM;QAC7B2C,cAAc,GAAGD,aAAa,CAACC,cAAc;MAC/C,IAAIC,YAAY,GAAG,IAAI,CAACjG,KAAK;QAC3BV,MAAM,GAAG2G,YAAY,CAAC3G,MAAM;QAC5BC,IAAI,GAAG0G,YAAY,CAAC1G,IAAI;QACxBJ,YAAY,GAAG8G,YAAY,CAAC9G,YAAY;QACxCC,aAAa,GAAG6G,YAAY,CAAC7G,aAAa;QAC1CC,iBAAiB,GAAG4G,YAAY,CAAC5G,iBAAiB;MACpD,IAAI,CAACf,IAAI,IAAI,CAACA,IAAI,CAAC1F,MAAM,IAAI,CAACqF,QAAQ,CAACQ,CAAC,CAAC,IAAI,CAACR,QAAQ,CAACmF,CAAC,CAAC,IAAI,CAACnF,QAAQ,CAACS,KAAK,CAAC,IAAI,CAACT,QAAQ,CAACoF,MAAM,CAAC,IAAI3E,KAAK,IAAI,CAAC,IAAI2E,MAAM,IAAI,CAAC,EAAE;QAC/H,OAAO,IAAI;MACb;MACA,IAAI6C,UAAU,GAAGtI,UAAU,CAAC,gBAAgB,EAAE2G,SAAS,CAAC;MACxD,IAAI4B,WAAW,GAAG1I,KAAK,CAACE,QAAQ,CAACyI,KAAK,CAACzC,QAAQ,CAAC,KAAK,CAAC;MACtD,IAAImB,KAAK,GAAG5G,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC;MACrD,OAAO,aAAaT,KAAK,CAAC+F,aAAa,CAAC1F,KAAK,EAAE;QAC7CyG,SAAS,EAAE2B,UAAU;QACrBxB,YAAY,EAAE,IAAI,CAAC2B,kBAAkB;QACrCC,WAAW,EAAE,IAAI,CAACC,eAAe;QACjCzB,KAAK,EAAEA;MACT,CAAC,EAAE,IAAI,CAAC5B,gBAAgB,CAAC,CAAC,EAAEiD,WAAW,IAAI,IAAI,CAAC1C,cAAc,CAAC,CAAC,EAAE,IAAI,CAACwB,WAAW,CAAC3F,MAAM,EAAEC,IAAI,CAAC,EAAE,IAAI,CAAC2E,oBAAoB,CAAC5E,MAAM,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC4E,oBAAoB,CAAC3E,IAAI,EAAE,MAAM,CAAC,EAAE,CAACJ,YAAY,IAAIC,aAAa,IAAIC,iBAAiB,IAAI2G,cAAc,KAAK,IAAI,CAACV,UAAU,CAAC,CAAC,CAAC;IACtR;EACF,CAAC,CAAC,EAAE,CAAC;IACHxM,GAAG,EAAE,wBAAwB;IAC7BsC,KAAK,EAAE,SAASoL,sBAAsBA,CAAChM,KAAK,EAAE;MAC5C,IAAIiE,CAAC,GAAGjE,KAAK,CAACiE,CAAC;QACb2E,CAAC,GAAG5I,KAAK,CAAC4I,CAAC;QACX1E,KAAK,GAAGlE,KAAK,CAACkE,KAAK;QACnB2E,MAAM,GAAG7I,KAAK,CAAC6I,MAAM;QACrBE,MAAM,GAAG/I,KAAK,CAAC+I,MAAM;MACvB,IAAIkD,KAAK,GAAGlF,IAAI,CAACmF,KAAK,CAACtD,CAAC,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,OAAO,aAAa5F,KAAK,CAAC+F,aAAa,CAAC/F,KAAK,CAACkJ,QAAQ,EAAE,IAAI,EAAE,aAAalJ,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;QACrG/E,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA,CAAC;QACJ1E,KAAK,EAAEA,KAAK;QACZ2E,MAAM,EAAEA,MAAM;QACdC,IAAI,EAAEC,MAAM;QACZA,MAAM,EAAE;MACV,CAAC,CAAC,EAAE,aAAa9F,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;QAC3CoD,EAAE,EAAEnI,CAAC,GAAG,CAAC;QACToI,EAAE,EAAEJ,KAAK;QACTK,EAAE,EAAErI,CAAC,GAAGC,KAAK,GAAG,CAAC;QACjBqI,EAAE,EAAEN,KAAK;QACTnD,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE;MACV,CAAC,CAAC,EAAE,aAAa9F,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;QAC3CoD,EAAE,EAAEnI,CAAC,GAAG,CAAC;QACToI,EAAE,EAAEJ,KAAK,GAAG,CAAC;QACbK,EAAE,EAAErI,CAAC,GAAGC,KAAK,GAAG,CAAC;QACjBqI,EAAE,EAAEN,KAAK,GAAG,CAAC;QACbnD,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDzK,GAAG,EAAE,iBAAiB;IACtBsC,KAAK,EAAE,SAAS4J,eAAeA,CAACgC,MAAM,EAAExM,KAAK,EAAE;MAC7C,IAAIyM,SAAS;MACb,IAAK,aAAaxJ,KAAK,CAACyJ,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,SAAS,GAAG,aAAaxJ,KAAK,CAACsG,YAAY,CAACiD,MAAM,EAAExM,KAAK,CAAC;MAC5D,CAAC,MAAM,IAAI3C,WAAW,CAACmP,MAAM,CAAC,EAAE;QAC9BC,SAAS,GAAGD,MAAM,CAACxM,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLyM,SAAS,GAAGvH,KAAK,CAAC8G,sBAAsB,CAAChM,KAAK,CAAC;MACjD;MACA,OAAOyM,SAAS;IAClB;EACF,CAAC,EAAE;IACDnO,GAAG,EAAE,0BAA0B;IAC/BsC,KAAK,EAAE,SAAS+L,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAI/I,IAAI,GAAG8I,SAAS,CAAC9I,IAAI;QACvBI,KAAK,GAAG0I,SAAS,CAAC1I,KAAK;QACvBD,CAAC,GAAG2I,SAAS,CAAC3I,CAAC;QACfE,cAAc,GAAGyI,SAAS,CAACzI,cAAc;QACzC2I,QAAQ,GAAGF,SAAS,CAACE,QAAQ;QAC7B/I,UAAU,GAAG6I,SAAS,CAAC7I,UAAU;QACjCC,QAAQ,GAAG4I,SAAS,CAAC5I,QAAQ;MAC/B,IAAIF,IAAI,KAAK+I,SAAS,CAACE,QAAQ,IAAID,QAAQ,KAAKD,SAAS,CAACG,YAAY,EAAE;QACtE,OAAO3N,aAAa,CAAC;UACnB0N,QAAQ,EAAEjJ,IAAI;UACdmJ,kBAAkB,EAAE9I,cAAc;UAClC6I,YAAY,EAAEF,QAAQ;UACtBI,KAAK,EAAEjJ,CAAC;UACRkJ,SAAS,EAAEjJ;QACb,CAAC,EAAEJ,IAAI,IAAIA,IAAI,CAAC1F,MAAM,GAAGwF,WAAW,CAAC;UACnCE,IAAI,EAAEA,IAAI;UACVI,KAAK,EAAEA,KAAK;UACZD,CAAC,EAAEA,CAAC;UACJE,cAAc,EAAEA,cAAc;UAC9BJ,UAAU,EAAEA,UAAU;UACtBC,QAAQ,EAAEA;QACZ,CAAC,CAAC,GAAG;UACHK,KAAK,EAAE,IAAI;UACXG,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;MACA,IAAIqI,SAAS,CAACxI,KAAK,KAAKH,KAAK,KAAK2I,SAAS,CAACM,SAAS,IAAIlJ,CAAC,KAAK4I,SAAS,CAACK,KAAK,IAAI/I,cAAc,KAAK0I,SAAS,CAACI,kBAAkB,CAAC,EAAE;QAClIJ,SAAS,CAACxI,KAAK,CAACE,KAAK,CAAC,CAACN,CAAC,EAAEA,CAAC,GAAGC,KAAK,GAAGC,cAAc,CAAC,CAAC;QACtD,IAAIK,WAAW,GAAGqI,SAAS,CAACxI,KAAK,CAACC,MAAM,CAAC,CAAC,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;UAC9D,OAAOmI,SAAS,CAACxI,KAAK,CAACK,KAAK,CAAC;QAC/B,CAAC,CAAC;QACF,OAAO;UACLqI,QAAQ,EAAEjJ,IAAI;UACdmJ,kBAAkB,EAAE9I,cAAc;UAClC6I,YAAY,EAAEF,QAAQ;UACtBI,KAAK,EAAEjJ,CAAC;UACRkJ,SAAS,EAAEjJ,KAAK;UAChBY,MAAM,EAAE+H,SAAS,CAACxI,KAAK,CAACuI,SAAS,CAAC7I,UAAU,CAAC;UAC7CgB,IAAI,EAAE8H,SAAS,CAACxI,KAAK,CAACuI,SAAS,CAAC5I,QAAQ,CAAC;UACzCQ,WAAW,EAAEA;QACf,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDlG,GAAG,EAAE,iBAAiB;IACtBsC,KAAK,EAAE,SAASsG,eAAeA,CAAC3C,KAAK,EAAEN,CAAC,EAAE;MACxC,IAAIG,GAAG,GAAGG,KAAK,CAACnG,MAAM;MACtB,IAAIgP,KAAK,GAAG,CAAC;MACb,IAAIC,GAAG,GAAGjJ,GAAG,GAAG,CAAC;MACjB,OAAOiJ,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAE;QACtB,IAAIE,MAAM,GAAGvG,IAAI,CAACmF,KAAK,CAAC,CAACkB,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;QAC1C,IAAI9I,KAAK,CAAC+I,MAAM,CAAC,GAAGrJ,CAAC,EAAE;UACrBoJ,GAAG,GAAGC,MAAM;QACd,CAAC,MAAM;UACLF,KAAK,GAAGE,MAAM;QAChB;MACF;MACA,OAAOrJ,CAAC,IAAIM,KAAK,CAAC8I,GAAG,CAAC,GAAGA,GAAG,GAAGD,KAAK;IACtC;EACF,CAAC,CAAC,CAAC;EACH,OAAOlI,KAAK;AACd,CAAC,CAAChC,aAAa,CAAC;AAChB3D,eAAe,CAAC2F,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;AAC9C3F,eAAe,CAAC2F,KAAK,EAAE,cAAc,EAAE;EACrC2D,MAAM,EAAE,EAAE;EACV1E,cAAc,EAAE,CAAC;EACjByC,GAAG,EAAE,CAAC;EACNkC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,MAAM;EACdK,OAAO,EAAE;IACPmE,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDzH,YAAY,EAAE,IAAI;EAClBuF,cAAc,EAAE;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}