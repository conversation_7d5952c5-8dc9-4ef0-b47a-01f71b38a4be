{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport DrawerPanel from './DrawerPanel';\n// import type ScrollLocker from 'rc-util/lib/Dom/scrollLocker';\nimport DrawerContext from './context';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { parseWidthHeight } from './util';\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none',\n  position: 'absolute'\n};\nexport default function DrawerPopup(props) {\n  var _ref, _pushConfig$distance, _pushConfig, _classNames;\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    placement = props.placement,\n    inline = props.inline,\n    push = props.push,\n    forceRender = props.forceRender,\n    autoFocus = props.autoFocus,\n    keyboard = props.keyboard,\n    rootClassName = props.rootClassName,\n    rootStyle = props.rootStyle,\n    zIndex = props.zIndex,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    width = props.width,\n    height = props.height,\n    children = props.children,\n    contentWrapperStyle = props.contentWrapperStyle,\n    mask = props.mask,\n    maskClosable = props.maskClosable,\n    maskMotion = props.maskMotion,\n    maskClassName = props.maskClassName,\n    maskStyle = props.maskStyle,\n    afterOpenChange = props.afterOpenChange,\n    onClose = props.onClose;\n  // ================================ Refs ================================\n  var panelRef = React.useRef();\n  var sentinelStartRef = React.useRef();\n  var sentinelEndRef = React.useRef();\n  var onPanelKeyDown = function onPanelKeyDown(event) {\n    var keyCode = event.keyCode,\n      shiftKey = event.shiftKey;\n    switch (keyCode) {\n      // Tab active\n      case KeyCode.TAB:\n        {\n          if (keyCode === KeyCode.TAB) {\n            if (!shiftKey && document.activeElement === sentinelEndRef.current) {\n              var _sentinelStartRef$cur;\n              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus({\n                preventScroll: true\n              });\n            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {\n              var _sentinelEndRef$curre;\n              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 ? void 0 : _sentinelEndRef$curre.focus({\n                preventScroll: true\n              });\n            }\n          }\n          break;\n        }\n      // Close\n      case KeyCode.ESC:\n        {\n          if (onClose && keyboard) {\n            onClose(event);\n          }\n          break;\n        }\n    }\n  };\n  // ========================== Control ===========================\n  // Auto Focus\n  React.useEffect(function () {\n    if (open && autoFocus) {\n      var _panelRef$current;\n      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 ? void 0 : _panelRef$current.focus({\n        preventScroll: true\n      });\n    }\n  }, [open, autoFocus]);\n  // ============================ Push ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pushed = _React$useState2[0],\n    setPushed = _React$useState2[1];\n  var parentContext = React.useContext(DrawerContext);\n  // Merge push distance\n  var pushConfig;\n  if (push === false) {\n    pushConfig = {\n      distance: 0\n    };\n  } else if (push === true) {\n    pushConfig = {};\n  } else {\n    pushConfig = push || {};\n  }\n  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;\n  var mergedContext = React.useMemo(function () {\n    return {\n      pushDistance: pushDistance,\n      push: function push() {\n        setPushed(true);\n      },\n      pull: function pull() {\n        setPushed(false);\n      }\n    };\n  }, [pushDistance]);\n  // ========================= ScrollLock =========================\n  // Tell parent to push\n  React.useEffect(function () {\n    if (open) {\n      var _parentContext$push;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 ? void 0 : _parentContext$push.call(parentContext);\n    } else {\n      var _parentContext$pull;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 ? void 0 : _parentContext$pull.call(parentContext);\n    }\n  }, [open]);\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      var _parentContext$pull2;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 ? void 0 : _parentContext$pull2.call(parentContext);\n    };\n  }, []);\n  // ============================ Mask ============================\n  var maskNode = mask && /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"mask\"\n  }, maskMotion, {\n    visible: open\n  }), function (_ref2, maskRef) {\n    var motionMaskClassName = _ref2.className,\n      motionMaskStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionMaskClassName, maskClassName),\n      style: _objectSpread(_objectSpread({}, motionMaskStyle), maskStyle),\n      onClick: maskClosable ? onClose : undefined,\n      ref: maskRef\n    });\n  });\n  // =========================== Panel ============================\n  var motionProps = typeof motion === 'function' ? motion(placement) : motion;\n  var wrapperStyle = {};\n  if (pushed && pushDistance) {\n    switch (placement) {\n      case 'top':\n        wrapperStyle.transform = \"translateY(\".concat(pushDistance, \"px)\");\n        break;\n      case 'bottom':\n        wrapperStyle.transform = \"translateY(\".concat(-pushDistance, \"px)\");\n        break;\n      case 'left':\n        wrapperStyle.transform = \"translateX(\".concat(pushDistance, \"px)\");\n        break;\n      default:\n        wrapperStyle.transform = \"translateX(\".concat(-pushDistance, \"px)\");\n        break;\n    }\n  }\n  if (placement === 'left' || placement === 'right') {\n    wrapperStyle.width = parseWidthHeight(width);\n  } else {\n    wrapperStyle.height = parseWidthHeight(height);\n  }\n  var panelNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"panel\"\n  }, motionProps, {\n    visible: open,\n    forceRender: forceRender,\n    onVisibleChanged: function onVisibleChanged(nextVisible) {\n      afterOpenChange === null || afterOpenChange === void 0 ? void 0 : afterOpenChange(nextVisible);\n    },\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-wrapper-hidden\")\n  }), function (_ref3, motionRef) {\n    var motionClassName = _ref3.className,\n      motionStyle = _ref3.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-content-wrapper\"), motionClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, wrapperStyle), motionStyle), contentWrapperStyle)\n    }, /*#__PURE__*/React.createElement(DrawerPanel, {\n      containerRef: motionRef,\n      prefixCls: prefixCls,\n      className: className,\n      style: style\n    }, children));\n  });\n  // =========================== Render ===========================\n  var containerStyle = _objectSpread({}, rootStyle);\n  if (zIndex) {\n    containerStyle.zIndex = zIndex;\n  }\n  return /*#__PURE__*/React.createElement(DrawerContext.Provider, {\n    value: mergedContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-open\"), open), _defineProperty(_classNames, \"\".concat(prefixCls, \"-inline\"), inline), _classNames)),\n    style: containerStyle,\n    tabIndex: -1,\n    ref: panelRef,\n    onKeyDown: onPanelKeyDown\n  }, maskNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"start\"\n  }), panelNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"end\"\n  })));\n}", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectSpread", "_slicedToArray", "React", "classNames", "CSSMotion", "<PERSON>er<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KeyCode", "parseWidthHeight", "sentinelStyle", "width", "height", "overflow", "outline", "position", "Drawer<PERSON><PERSON><PERSON>", "props", "_ref", "_pushConfig$distance", "_pushConfig", "_classNames", "prefixCls", "open", "placement", "inline", "push", "forceRender", "autoFocus", "keyboard", "rootClassName", "rootStyle", "zIndex", "className", "style", "motion", "children", "contentWrapperStyle", "mask", "maskClosable", "maskMotion", "maskClassName", "maskStyle", "afterOpenChange", "onClose", "panelRef", "useRef", "sentinelStartRef", "sentinelEndRef", "onPanelKeyDown", "event", "keyCode", "shift<PERSON>ey", "TAB", "document", "activeElement", "current", "_sentinelStartRef$cur", "focus", "preventScroll", "_sentinelEndRef$curre", "ESC", "useEffect", "_panelRef$current", "_React$useState", "useState", "_React$useState2", "pushed", "setPushed", "parentContext", "useContext", "pushConfig", "distance", "pushDistance", "mergedContext", "useMemo", "pull", "_parentContext$push", "call", "_parentContext$pull", "_parentContext$pull2", "maskNode", "createElement", "key", "visible", "_ref2", "maskRef", "motionMaskClassName", "motionMaskStyle", "concat", "onClick", "undefined", "ref", "motionProps", "wrapperStyle", "transform", "panelNode", "onVisibleChanged", "nextVisible", "removeOnLeave", "leavedClassName", "_ref3", "motionRef", "motionClassName", "motionStyle", "containerRef", "containerStyle", "Provider", "value", "tabIndex", "onKeyDown"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-drawer/es/DrawerPopup.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport DrawerPanel from './DrawerPanel';\n// import type ScrollLocker from 'rc-util/lib/Dom/scrollLocker';\nimport DrawerContext from './context';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { parseWidthHeight } from './util';\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none',\n  position: 'absolute'\n};\nexport default function DrawerPopup(props) {\n  var _ref, _pushConfig$distance, _pushConfig, _classNames;\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    placement = props.placement,\n    inline = props.inline,\n    push = props.push,\n    forceRender = props.forceRender,\n    autoFocus = props.autoFocus,\n    keyboard = props.keyboard,\n    rootClassName = props.rootClassName,\n    rootStyle = props.rootStyle,\n    zIndex = props.zIndex,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    width = props.width,\n    height = props.height,\n    children = props.children,\n    contentWrapperStyle = props.contentWrapperStyle,\n    mask = props.mask,\n    maskClosable = props.maskClosable,\n    maskMotion = props.maskMotion,\n    maskClassName = props.maskClassName,\n    maskStyle = props.maskStyle,\n    afterOpenChange = props.afterOpenChange,\n    onClose = props.onClose;\n  // ================================ Refs ================================\n  var panelRef = React.useRef();\n  var sentinelStartRef = React.useRef();\n  var sentinelEndRef = React.useRef();\n  var onPanelKeyDown = function onPanelKeyDown(event) {\n    var keyCode = event.keyCode,\n      shiftKey = event.shiftKey;\n    switch (keyCode) {\n      // Tab active\n      case KeyCode.TAB:\n        {\n          if (keyCode === KeyCode.TAB) {\n            if (!shiftKey && document.activeElement === sentinelEndRef.current) {\n              var _sentinelStartRef$cur;\n              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus({\n                preventScroll: true\n              });\n            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {\n              var _sentinelEndRef$curre;\n              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 ? void 0 : _sentinelEndRef$curre.focus({\n                preventScroll: true\n              });\n            }\n          }\n          break;\n        }\n      // Close\n      case KeyCode.ESC:\n        {\n          if (onClose && keyboard) {\n            onClose(event);\n          }\n          break;\n        }\n    }\n  };\n  // ========================== Control ===========================\n  // Auto Focus\n  React.useEffect(function () {\n    if (open && autoFocus) {\n      var _panelRef$current;\n      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 ? void 0 : _panelRef$current.focus({\n        preventScroll: true\n      });\n    }\n  }, [open, autoFocus]);\n  // ============================ Push ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pushed = _React$useState2[0],\n    setPushed = _React$useState2[1];\n  var parentContext = React.useContext(DrawerContext);\n  // Merge push distance\n  var pushConfig;\n  if (push === false) {\n    pushConfig = {\n      distance: 0\n    };\n  } else if (push === true) {\n    pushConfig = {};\n  } else {\n    pushConfig = push || {};\n  }\n  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;\n  var mergedContext = React.useMemo(function () {\n    return {\n      pushDistance: pushDistance,\n      push: function push() {\n        setPushed(true);\n      },\n      pull: function pull() {\n        setPushed(false);\n      }\n    };\n  }, [pushDistance]);\n  // ========================= ScrollLock =========================\n  // Tell parent to push\n  React.useEffect(function () {\n    if (open) {\n      var _parentContext$push;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 ? void 0 : _parentContext$push.call(parentContext);\n    } else {\n      var _parentContext$pull;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 ? void 0 : _parentContext$pull.call(parentContext);\n    }\n  }, [open]);\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      var _parentContext$pull2;\n      parentContext === null || parentContext === void 0 ? void 0 : (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 ? void 0 : _parentContext$pull2.call(parentContext);\n    };\n  }, []);\n  // ============================ Mask ============================\n  var maskNode = mask && /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"mask\"\n  }, maskMotion, {\n    visible: open\n  }), function (_ref2, maskRef) {\n    var motionMaskClassName = _ref2.className,\n      motionMaskStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionMaskClassName, maskClassName),\n      style: _objectSpread(_objectSpread({}, motionMaskStyle), maskStyle),\n      onClick: maskClosable ? onClose : undefined,\n      ref: maskRef\n    });\n  });\n  // =========================== Panel ============================\n  var motionProps = typeof motion === 'function' ? motion(placement) : motion;\n  var wrapperStyle = {};\n  if (pushed && pushDistance) {\n    switch (placement) {\n      case 'top':\n        wrapperStyle.transform = \"translateY(\".concat(pushDistance, \"px)\");\n        break;\n      case 'bottom':\n        wrapperStyle.transform = \"translateY(\".concat(-pushDistance, \"px)\");\n        break;\n      case 'left':\n        wrapperStyle.transform = \"translateX(\".concat(pushDistance, \"px)\");\n        break;\n      default:\n        wrapperStyle.transform = \"translateX(\".concat(-pushDistance, \"px)\");\n        break;\n    }\n  }\n  if (placement === 'left' || placement === 'right') {\n    wrapperStyle.width = parseWidthHeight(width);\n  } else {\n    wrapperStyle.height = parseWidthHeight(height);\n  }\n  var panelNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"panel\"\n  }, motionProps, {\n    visible: open,\n    forceRender: forceRender,\n    onVisibleChanged: function onVisibleChanged(nextVisible) {\n      afterOpenChange === null || afterOpenChange === void 0 ? void 0 : afterOpenChange(nextVisible);\n    },\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-wrapper-hidden\")\n  }), function (_ref3, motionRef) {\n    var motionClassName = _ref3.className,\n      motionStyle = _ref3.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-content-wrapper\"), motionClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, wrapperStyle), motionStyle), contentWrapperStyle)\n    }, /*#__PURE__*/React.createElement(DrawerPanel, {\n      containerRef: motionRef,\n      prefixCls: prefixCls,\n      className: className,\n      style: style\n    }, children));\n  });\n  // =========================== Render ===========================\n  var containerStyle = _objectSpread({}, rootStyle);\n  if (zIndex) {\n    containerStyle.zIndex = zIndex;\n  }\n  return /*#__PURE__*/React.createElement(DrawerContext.Provider, {\n    value: mergedContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-open\"), open), _defineProperty(_classNames, \"\".concat(prefixCls, \"-inline\"), inline), _classNames)),\n    style: containerStyle,\n    tabIndex: -1,\n    ref: panelRef,\n    onKeyDown: onPanelKeyDown\n  }, maskNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"start\"\n  }), panelNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"end\"\n  })));\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,gBAAgB,QAAQ,QAAQ;AACzC,IAAIC,aAAa,GAAG;EAClBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE;AACZ,CAAC;AACD,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,IAAI,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,WAAW;EACxD,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC7BC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,aAAa,GAAGb,KAAK,CAACa,aAAa;IACnCC,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,KAAK,GAAGjB,KAAK,CAACiB,KAAK;IACnBC,MAAM,GAAGlB,KAAK,CAACkB,MAAM;IACrBxB,KAAK,GAAGM,KAAK,CAACN,KAAK;IACnBC,MAAM,GAAGK,KAAK,CAACL,MAAM;IACrBwB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;IACzBC,mBAAmB,GAAGpB,KAAK,CAACoB,mBAAmB;IAC/CC,IAAI,GAAGrB,KAAK,CAACqB,IAAI;IACjBC,YAAY,GAAGtB,KAAK,CAACsB,YAAY;IACjCC,UAAU,GAAGvB,KAAK,CAACuB,UAAU;IAC7BC,aAAa,GAAGxB,KAAK,CAACwB,aAAa;IACnCC,SAAS,GAAGzB,KAAK,CAACyB,SAAS;IAC3BC,eAAe,GAAG1B,KAAK,CAAC0B,eAAe;IACvCC,OAAO,GAAG3B,KAAK,CAAC2B,OAAO;EACzB;EACA,IAAIC,QAAQ,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,CAAC;EAC7B,IAAIC,gBAAgB,GAAG5C,KAAK,CAAC2C,MAAM,CAAC,CAAC;EACrC,IAAIE,cAAc,GAAG7C,KAAK,CAAC2C,MAAM,CAAC,CAAC;EACnC,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;IAClD,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;MACzBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3B,QAAQD,OAAO;MACb;MACA,KAAK3C,OAAO,CAAC6C,GAAG;QACd;UACE,IAAIF,OAAO,KAAK3C,OAAO,CAAC6C,GAAG,EAAE;YAC3B,IAAI,CAACD,QAAQ,IAAIE,QAAQ,CAACC,aAAa,KAAKP,cAAc,CAACQ,OAAO,EAAE;cAClE,IAAIC,qBAAqB;cACzB,CAACA,qBAAqB,GAAGV,gBAAgB,CAACS,OAAO,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,KAAK,CAAC;gBACrIC,aAAa,EAAE;cACjB,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIP,QAAQ,IAAIE,QAAQ,CAACC,aAAa,KAAKR,gBAAgB,CAACS,OAAO,EAAE;cAC1E,IAAII,qBAAqB;cACzB,CAACA,qBAAqB,GAAGZ,cAAc,CAACQ,OAAO,MAAM,IAAI,IAAII,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACF,KAAK,CAAC;gBACnIC,aAAa,EAAE;cACjB,CAAC,CAAC;YACJ;UACF;UACA;QACF;MACF;MACA,KAAKnD,OAAO,CAACqD,GAAG;QACd;UACE,IAAIjB,OAAO,IAAIf,QAAQ,EAAE;YACvBe,OAAO,CAACM,KAAK,CAAC;UAChB;UACA;QACF;IACJ;EACF,CAAC;EACD;EACA;EACA/C,KAAK,CAAC2D,SAAS,CAAC,YAAY;IAC1B,IAAIvC,IAAI,IAAIK,SAAS,EAAE;MACrB,IAAImC,iBAAiB;MACrB,CAACA,iBAAiB,GAAGlB,QAAQ,CAACW,OAAO,MAAM,IAAI,IAAIO,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACL,KAAK,CAAC;QACjHC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpC,IAAI,EAAEK,SAAS,CAAC,CAAC;EACrB;EACA,IAAIoC,eAAe,GAAG7D,KAAK,CAAC8D,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhE,cAAc,CAAC8D,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,aAAa,GAAGlE,KAAK,CAACmE,UAAU,CAAC/D,aAAa,CAAC;EACnD;EACA,IAAIgE,UAAU;EACd,IAAI7C,IAAI,KAAK,KAAK,EAAE;IAClB6C,UAAU,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC,MAAM,IAAI9C,IAAI,KAAK,IAAI,EAAE;IACxB6C,UAAU,GAAG,CAAC,CAAC;EACjB,CAAC,MAAM;IACLA,UAAU,GAAG7C,IAAI,IAAI,CAAC,CAAC;EACzB;EACA,IAAI+C,YAAY,GAAG,CAACvD,IAAI,GAAG,CAACC,oBAAoB,GAAG,CAACC,WAAW,GAAGmD,UAAU,MAAM,IAAI,IAAInD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACoD,QAAQ,MAAM,IAAI,IAAIrD,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGkD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,YAAY,MAAM,IAAI,IAAIvD,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,GAAG;EACzV,IAAIwD,aAAa,GAAGvE,KAAK,CAACwE,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLF,YAAY,EAAEA,YAAY;MAC1B/C,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB0C,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC;MACDQ,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBR,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;EACH,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;EAClB;EACA;EACAtE,KAAK,CAAC2D,SAAS,CAAC,YAAY;IAC1B,IAAIvC,IAAI,EAAE;MACR,IAAIsD,mBAAmB;MACvBR,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACQ,mBAAmB,GAAGR,aAAa,CAAC3C,IAAI,MAAM,IAAI,IAAImD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,IAAI,CAACT,aAAa,CAAC;IACxM,CAAC,MAAM;MACL,IAAIU,mBAAmB;MACvBV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACU,mBAAmB,GAAGV,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIG,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACD,IAAI,CAACT,aAAa,CAAC;IACxM;EACF,CAAC,EAAE,CAAC9C,IAAI,CAAC,CAAC;EACV;EACApB,KAAK,CAAC2D,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB,IAAIkB,oBAAoB;MACxBX,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACW,oBAAoB,GAAGX,aAAa,CAACO,IAAI,MAAM,IAAI,IAAII,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACF,IAAI,CAACT,aAAa,CAAC;IAC3M,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN;EACA,IAAIY,QAAQ,GAAG3C,IAAI,IAAI,aAAanC,KAAK,CAAC+E,aAAa,CAAC7E,SAAS,EAAEL,QAAQ,CAAC;IAC1EmF,GAAG,EAAE;EACP,CAAC,EAAE3C,UAAU,EAAE;IACb4C,OAAO,EAAE7D;EACX,CAAC,CAAC,EAAE,UAAU8D,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAIC,mBAAmB,GAAGF,KAAK,CAACpD,SAAS;MACvCuD,eAAe,GAAGH,KAAK,CAACnD,KAAK;IAC/B,OAAO,aAAa/B,KAAK,CAAC+E,aAAa,CAAC,KAAK,EAAE;MAC7CjD,SAAS,EAAE7B,UAAU,CAAC,EAAE,CAACqF,MAAM,CAACnE,SAAS,EAAE,OAAO,CAAC,EAAEiE,mBAAmB,EAAE9C,aAAa,CAAC;MACxFP,KAAK,EAAEjC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,eAAe,CAAC,EAAE9C,SAAS,CAAC;MACnEgD,OAAO,EAAEnD,YAAY,GAAGK,OAAO,GAAG+C,SAAS;MAC3CC,GAAG,EAAEN;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACA,IAAIO,WAAW,GAAG,OAAO1D,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACX,SAAS,CAAC,GAAGW,MAAM;EAC3E,IAAI2D,YAAY,GAAG,CAAC,CAAC;EACrB,IAAI3B,MAAM,IAAIM,YAAY,EAAE;IAC1B,QAAQjD,SAAS;MACf,KAAK,KAAK;QACRsE,YAAY,CAACC,SAAS,GAAG,aAAa,CAACN,MAAM,CAAChB,YAAY,EAAE,KAAK,CAAC;QAClE;MACF,KAAK,QAAQ;QACXqB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACN,MAAM,CAAC,CAAChB,YAAY,EAAE,KAAK,CAAC;QACnE;MACF,KAAK,MAAM;QACTqB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACN,MAAM,CAAChB,YAAY,EAAE,KAAK,CAAC;QAClE;MACF;QACEqB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACN,MAAM,CAAC,CAAChB,YAAY,EAAE,KAAK,CAAC;QACnE;IACJ;EACF;EACA,IAAIjD,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;IACjDsE,YAAY,CAACnF,KAAK,GAAGF,gBAAgB,CAACE,KAAK,CAAC;EAC9C,CAAC,MAAM;IACLmF,YAAY,CAAClF,MAAM,GAAGH,gBAAgB,CAACG,MAAM,CAAC;EAChD;EACA,IAAIoF,SAAS,GAAG,aAAa7F,KAAK,CAAC+E,aAAa,CAAC7E,SAAS,EAAEL,QAAQ,CAAC;IACnEmF,GAAG,EAAE;EACP,CAAC,EAAEU,WAAW,EAAE;IACdT,OAAO,EAAE7D,IAAI;IACbI,WAAW,EAAEA,WAAW;IACxBsE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,WAAW,EAAE;MACvDvD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACuD,WAAW,CAAC;IAChG,CAAC;IACDC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,EAAE,CAACX,MAAM,CAACnE,SAAS,EAAE,yBAAyB;EACjE,CAAC,CAAC,EAAE,UAAU+E,KAAK,EAAEC,SAAS,EAAE;IAC9B,IAAIC,eAAe,GAAGF,KAAK,CAACpE,SAAS;MACnCuE,WAAW,GAAGH,KAAK,CAACnE,KAAK;IAC3B,OAAO,aAAa/B,KAAK,CAAC+E,aAAa,CAAC,KAAK,EAAE;MAC7CjD,SAAS,EAAE7B,UAAU,CAAC,EAAE,CAACqF,MAAM,CAACnE,SAAS,EAAE,kBAAkB,CAAC,EAAEiF,eAAe,CAAC;MAChFrE,KAAK,EAAEjC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,YAAY,CAAC,EAAEU,WAAW,CAAC,EAAEnE,mBAAmB;IACvG,CAAC,EAAE,aAAalC,KAAK,CAAC+E,aAAa,CAAC5E,WAAW,EAAE;MAC/CmG,YAAY,EAAEH,SAAS;MACvBhF,SAAS,EAAEA,SAAS;MACpBW,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA;IACT,CAAC,EAAEE,QAAQ,CAAC,CAAC;EACf,CAAC,CAAC;EACF;EACA,IAAIsE,cAAc,GAAGzG,aAAa,CAAC,CAAC,CAAC,EAAE8B,SAAS,CAAC;EACjD,IAAIC,MAAM,EAAE;IACV0E,cAAc,CAAC1E,MAAM,GAAGA,MAAM;EAChC;EACA,OAAO,aAAa7B,KAAK,CAAC+E,aAAa,CAAC3E,aAAa,CAACoG,QAAQ,EAAE;IAC9DC,KAAK,EAAElC;EACT,CAAC,EAAE,aAAavE,KAAK,CAAC+E,aAAa,CAAC,KAAK,EAAE;IACzCjD,SAAS,EAAE7B,UAAU,CAACkB,SAAS,EAAE,EAAE,CAACmE,MAAM,CAACnE,SAAS,EAAE,GAAG,CAAC,CAACmE,MAAM,CAACjE,SAAS,CAAC,EAAEM,aAAa,GAAGT,WAAW,GAAG,CAAC,CAAC,EAAEtB,eAAe,CAACsB,WAAW,EAAE,EAAE,CAACoE,MAAM,CAACnE,SAAS,EAAE,OAAO,CAAC,EAAEC,IAAI,CAAC,EAAExB,eAAe,CAACsB,WAAW,EAAE,EAAE,CAACoE,MAAM,CAACnE,SAAS,EAAE,SAAS,CAAC,EAAEG,MAAM,CAAC,EAAEJ,WAAW,CAAC,CAAC;IACvQa,KAAK,EAAEwE,cAAc;IACrBG,QAAQ,EAAE,CAAC,CAAC;IACZjB,GAAG,EAAE/C,QAAQ;IACbiE,SAAS,EAAE7D;EACb,CAAC,EAAEgC,QAAQ,EAAE,aAAa9E,KAAK,CAAC+E,aAAa,CAAC,KAAK,EAAE;IACnD2B,QAAQ,EAAE,CAAC;IACXjB,GAAG,EAAE7C,gBAAgB;IACrBb,KAAK,EAAExB,aAAa;IACpB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE;EACnB,CAAC,CAAC,EAAEsF,SAAS,EAAE,aAAa7F,KAAK,CAAC+E,aAAa,CAAC,KAAK,EAAE;IACrD2B,QAAQ,EAAE,CAAC;IACXjB,GAAG,EAAE5C,cAAc;IACnBd,KAAK,EAAExB,aAAa;IACpB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module"}