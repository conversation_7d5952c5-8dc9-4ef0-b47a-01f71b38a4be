{"ast": null, "code": "/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nconst friendlySyntaxErrorLabel = 'Syntax error:';\nfunction isLikelyASyntaxError(message) {\n  return message.indexOf(friendlySyntaxErrorLabel) !== -1;\n}\n\n// Cleans up webpack error messages.\nfunction formatMessage(message) {\n  let lines = message.split('\\n');\n\n  // Strip webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter(line => !/Module [A-z ]+\\(from/.test(line));\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map(line => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n    if (!parsingError) {\n      return line;\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError;\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`;\n  });\n  message = lines.join('\\n');\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(/SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g, `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`);\n  // Clean up export errors\n  message = message.replace(/^.*export '(.+?)' was not found in '(.+?)'.*$/gm, `Attempted import error: '$1' is not exported from '$2'.`);\n  message = message.replace(/^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, `Attempted import error: '$2' does not contain a default export (imported as '$1').`);\n  message = message.replace(/^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`);\n  lines = message.split('\\n');\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1);\n  }\n  // Clean up file name\n  lines[0] = lines[0].replace(/^(.*) \\d+:\\d+-\\d+$/, '$1');\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].indexOf('Module not found: ') === 0) {\n    lines = [lines[0], lines[1].replace('Error: ', '').replace('Module not found: Cannot find file:', 'Cannot find file:')];\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+node-sass/)) {\n    lines[1] = 'To import Sass files, you first need to install node-sass.\\n';\n    lines[1] += 'Run `npm install node-sass` or `yarn add node-sass` inside your workspace.';\n  }\n  message = lines.join('\\n');\n  // Internal stacks are generally useless so we strip them... with the\n  // exception of stacks containing `webpack:` because they're normally\n  // from user code generated by webpack. For more information see\n  // https://github.com/facebook/create-react-app/pull/1050\n  message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, ''); // at ... ...:x:y\n  message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, ''); // at <anonymous>\n  lines = message.split('\\n');\n\n  // Remove duplicated newlines\n  lines = lines.filter((line, index, arr) => index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim());\n\n  // Reassemble the message\n  message = lines.join('\\n');\n  return message.trim();\n}\nfunction formatWebpackMessages(json) {\n  const formattedErrors = json.errors.map(formatMessage);\n  const formattedWarnings = json.warnings.map(formatMessage);\n  const result = {\n    errors: formattedErrors,\n    warnings: formattedWarnings\n  };\n  if (result.errors.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError);\n  }\n  return result;\n}\nmodule.exports = formatWebpackMessages;", "map": {"version": 3, "names": ["friendlySyntaxErrorLabel", "isLikelyASyntaxError", "message", "indexOf", "formatMessage", "lines", "split", "filter", "line", "test", "map", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "join", "replace", "length", "trim", "splice", "match", "index", "arr", "formatWebpackMessages", "json", "formattedErrors", "errors", "formattedWarnings", "warnings", "result", "some", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-dev-utils/formatWebpackMessages.js"], "sourcesContent": ["/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nconst friendlySyntaxErrorLabel = 'Syntax error:';\n\nfunction isLikelyASyntaxError(message) {\n  return message.indexOf(friendlySyntaxErrorLabel) !== -1;\n}\n\n// Cleans up webpack error messages.\nfunction formatMessage(message) {\n  let lines = message.split('\\n');\n\n  // Strip webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter(line => !/Module [A-z ]+\\(from/.test(line));\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map(line => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(\n      line\n    );\n    if (!parsingError) {\n      return line;\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError;\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`;\n  });\n\n  message = lines.join('\\n');\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  );\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  );\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  );\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  );\n  lines = message.split('\\n');\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1);\n  }\n  // Clean up file name\n  lines[0] = lines[0].replace(/^(.*) \\d+:\\d+-\\d+$/, '$1');\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].indexOf('Module not found: ') === 0) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n    ];\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+node-sass/)) {\n    lines[1] = 'To import Sass files, you first need to install node-sass.\\n';\n    lines[1] +=\n      'Run `npm install node-sass` or `yarn add node-sass` inside your workspace.';\n  }\n\n  message = lines.join('\\n');\n  // Internal stacks are generally useless so we strip them... with the\n  // exception of stacks containing `webpack:` because they're normally\n  // from user code generated by webpack. For more information see\n  // https://github.com/facebook/create-react-app/pull/1050\n  message = message.replace(\n    /^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm,\n    ''\n  ); // at ... ...:x:y\n  message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, ''); // at <anonymous>\n  lines = message.split('\\n');\n\n  // Remove duplicated newlines\n  lines = lines.filter(\n    (line, index, arr) =>\n      index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  );\n\n  // Reassemble the message\n  message = lines.join('\\n');\n  return message.trim();\n}\n\nfunction formatWebpackMessages(json) {\n  const formattedErrors = json.errors.map(formatMessage);\n  const formattedWarnings = json.warnings.map(formatMessage);\n  const result = { errors: formattedErrors, warnings: formattedWarnings };\n  if (result.errors.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError);\n  }\n  return result;\n}\n\nmodule.exports = formatWebpackMessages;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,wBAAwB,GAAG,eAAe;AAEhD,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EACrC,OAAOA,OAAO,CAACC,OAAO,CAACH,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACzD;;AAEA;AACA,SAASI,aAAaA,CAACF,OAAO,EAAE;EAC9B,IAAIG,KAAK,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;;EAE/B;EACA;EACAD,KAAK,GAAGA,KAAK,CAACE,MAAM,CAACC,IAAI,IAAI,CAAC,sBAAsB,CAACC,IAAI,CAACD,IAAI,CAAC,CAAC;;EAEhE;EACA;EACAH,KAAK,GAAGA,KAAK,CAACK,GAAG,CAACF,IAAI,IAAI;IACxB,MAAMG,YAAY,GAAG,+CAA+C,CAACC,IAAI,CACvEJ,IACF,CAAC;IACD,IAAI,CAACG,YAAY,EAAE;MACjB,OAAOH,IAAI;IACb;IACA,MAAM,GAAGK,SAAS,EAAEC,WAAW,EAAEC,YAAY,CAAC,GAAGJ,YAAY;IAC7D,OAAO,GAAGX,wBAAwB,IAAIe,YAAY,KAAKF,SAAS,IAAIC,WAAW,GAAG;EACpF,CAAC,CAAC;EAEFZ,OAAO,GAAGG,KAAK,CAACW,IAAI,CAAC,IAAI,CAAC;EAC1B;EACAd,OAAO,GAAGA,OAAO,CAACe,OAAO,CACvB,0CAA0C,EAC1C,GAAGjB,wBAAwB,eAC7B,CAAC;EACD;EACAE,OAAO,GAAGA,OAAO,CAACe,OAAO,CACvB,iDAAiD,EACjD,yDACF,CAAC;EACDf,OAAO,GAAGA,OAAO,CAACe,OAAO,CACvB,2EAA2E,EAC3E,oFACF,CAAC;EACDf,OAAO,GAAGA,OAAO,CAACe,OAAO,CACvB,yEAAyE,EACzE,4EACF,CAAC;EACDZ,KAAK,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;;EAE3B;EACA,IAAID,KAAK,CAACa,MAAM,GAAG,CAAC,IAAIb,KAAK,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9Cd,KAAK,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACpB;EACA;EACAf,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC;;EAEvD;EACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;IAC5DE,KAAK,GAAG,CACNA,KAAK,CAAC,CAAC,CAAC,EACRA,KAAK,CAAC,CAAC,CAAC,CACLY,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBA,OAAO,CAAC,qCAAqC,EAAE,mBAAmB,CAAC,CACvE;EACH;;EAEA;EACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,KAAK,CAAC,+BAA+B,CAAC,EAAE;IAC/DhB,KAAK,CAAC,CAAC,CAAC,GAAG,8DAA8D;IACzEA,KAAK,CAAC,CAAC,CAAC,IACN,4EAA4E;EAChF;EAEAH,OAAO,GAAGG,KAAK,CAACW,IAAI,CAAC,IAAI,CAAC;EAC1B;EACA;EACA;EACA;EACAd,OAAO,GAAGA,OAAO,CAACe,OAAO,CACvB,gDAAgD,EAChD,EACF,CAAC,CAAC,CAAC;EACHf,OAAO,GAAGA,OAAO,CAACe,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC,CAAC;EAC9DZ,KAAK,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;;EAE3B;EACAD,KAAK,GAAGA,KAAK,CAACE,MAAM,CAClB,CAACC,IAAI,EAAEc,KAAK,EAAEC,GAAG,KACfD,KAAK,KAAK,CAAC,IAAId,IAAI,CAACW,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIX,IAAI,CAACW,IAAI,CAAC,CAAC,KAAKI,GAAG,CAACD,KAAK,GAAG,CAAC,CAAC,CAACH,IAAI,CAAC,CAC7E,CAAC;;EAED;EACAjB,OAAO,GAAGG,KAAK,CAACW,IAAI,CAAC,IAAI,CAAC;EAC1B,OAAOd,OAAO,CAACiB,IAAI,CAAC,CAAC;AACvB;AAEA,SAASK,qBAAqBA,CAACC,IAAI,EAAE;EACnC,MAAMC,eAAe,GAAGD,IAAI,CAACE,MAAM,CAACjB,GAAG,CAACN,aAAa,CAAC;EACtD,MAAMwB,iBAAiB,GAAGH,IAAI,CAACI,QAAQ,CAACnB,GAAG,CAACN,aAAa,CAAC;EAC1D,MAAM0B,MAAM,GAAG;IAAEH,MAAM,EAAED,eAAe;IAAEG,QAAQ,EAAED;EAAkB,CAAC;EACvE,IAAIE,MAAM,CAACH,MAAM,CAACI,IAAI,CAAC9B,oBAAoB,CAAC,EAAE;IAC5C;IACA6B,MAAM,CAACH,MAAM,GAAGG,MAAM,CAACH,MAAM,CAACpB,MAAM,CAACN,oBAAoB,CAAC;EAC5D;EACA,OAAO6B,MAAM;AACf;AAEAE,MAAM,CAACC,OAAO,GAAGT,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}