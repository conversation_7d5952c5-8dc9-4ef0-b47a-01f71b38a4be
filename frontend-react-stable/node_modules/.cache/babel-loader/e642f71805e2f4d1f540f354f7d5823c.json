{"ast": null, "code": "import React, { useMemo } from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nfunction Provider({\n  store,\n  context,\n  children,\n  serverState\n}) {\n  const contextValue = useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined\n    };\n  }, [store, serverState]);\n  const previousState = useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nexport default Provider;", "map": {"version": 3, "names": ["React", "useMemo", "ReactReduxContext", "createSubscription", "useIsomorphicLayoutEffect", "Provider", "store", "context", "children", "serverState", "contextValue", "subscription", "getServerState", "undefined", "previousState", "getState", "onStateChange", "notifyNestedSubs", "trySubscribe", "tryUnsubscribe", "Context", "createElement", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/components/Provider.js"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider({\n  store,\n  context,\n  children,\n  serverState\n}) {\n  const contextValue = useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined\n    };\n  }, [store, serverState]);\n  const previousState = useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nexport default Provider;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,iBAAiB,QAAQ,WAAW;AAC7C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,QAAQA,CAAC;EAChBC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC;AACF,CAAC,EAAE;EACD,MAAMC,YAAY,GAAGT,OAAO,CAAC,MAAM;IACjC,MAAMU,YAAY,GAAGR,kBAAkB,CAACG,KAAK,CAAC;IAC9C,OAAO;MACLA,KAAK;MACLK,YAAY;MACZC,cAAc,EAAEH,WAAW,GAAG,MAAMA,WAAW,GAAGI;IACpD,CAAC;EACH,CAAC,EAAE,CAACP,KAAK,EAAEG,WAAW,CAAC,CAAC;EACxB,MAAMK,aAAa,GAAGb,OAAO,CAAC,MAAMK,KAAK,CAACS,QAAQ,CAAC,CAAC,EAAE,CAACT,KAAK,CAAC,CAAC;EAC9DF,yBAAyB,CAAC,MAAM;IAC9B,MAAM;MACJO;IACF,CAAC,GAAGD,YAAY;IAChBC,YAAY,CAACK,aAAa,GAAGL,YAAY,CAACM,gBAAgB;IAC1DN,YAAY,CAACO,YAAY,CAAC,CAAC;IAE3B,IAAIJ,aAAa,KAAKR,KAAK,CAACS,QAAQ,CAAC,CAAC,EAAE;MACtCJ,YAAY,CAACM,gBAAgB,CAAC,CAAC;IACjC;IAEA,OAAO,MAAM;MACXN,YAAY,CAACQ,cAAc,CAAC,CAAC;MAC7BR,YAAY,CAACK,aAAa,GAAGH,SAAS;IACxC,CAAC;EACH,CAAC,EAAE,CAACH,YAAY,EAAEI,aAAa,CAAC,CAAC;EACjC,MAAMM,OAAO,GAAGb,OAAO,IAAIL,iBAAiB,CAAC,CAAC;;EAE9C,OAAO,aAAaF,KAAK,CAACqB,aAAa,CAACD,OAAO,CAACf,QAAQ,EAAE;IACxDiB,KAAK,EAAEZ;EACT,CAAC,EAAEF,QAAQ,CAAC;AACd;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}