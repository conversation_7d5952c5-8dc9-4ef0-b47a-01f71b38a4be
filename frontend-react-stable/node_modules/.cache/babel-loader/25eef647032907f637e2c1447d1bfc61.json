{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcHours = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nvar _duration = require(\"./duration.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar utcHour = (0, _interval.default)(function (date) {\n  date.setUTCMinutes(0, 0, 0);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationHour);\n}, function (start, end) {\n  return (end - start) / _duration.durationHour;\n}, function (date) {\n  return date.getUTCHours();\n});\nvar _default = utcHour;\nexports.default = _default;\nvar utcHours = utcHour.range;\nexports.utcHours = utcHours;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcHours", "default", "_interval", "_interopRequireDefault", "require", "_duration", "obj", "__esModule", "utcHour", "date", "setUTCMinutes", "step", "setTime", "durationHour", "start", "end", "getUTCHours", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/utcHour.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcHours = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nvar _duration = require(\"./duration.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar utcHour = (0, _interval.default)(function (date) {\n  date.setUTCMinutes(0, 0, 0);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationHour);\n}, function (start, end) {\n  return (end - start) / _duration.durationHour;\n}, function (date) {\n  return date.getUTCHours();\n});\nvar _default = utcHour;\nexports.default = _default;\nvar utcHours = utcHour.range;\nexports.utcHours = utcHours;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE3C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,OAAO,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACD,OAAO,EAAE,UAAUQ,IAAI,EAAE;EACnDA,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,CAAC,EAAE,UAAUD,IAAI,EAAEE,IAAI,EAAE;EACvBF,IAAI,CAACG,OAAO,CAAC,CAACH,IAAI,GAAGE,IAAI,GAAGN,SAAS,CAACQ,YAAY,CAAC;AACrD,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIT,SAAS,CAACQ,YAAY;AAC/C,CAAC,EAAE,UAAUJ,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACO,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGT,OAAO;AACtBV,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,QAAQ,GAAGQ,OAAO,CAACU,KAAK;AAC5BpB,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}