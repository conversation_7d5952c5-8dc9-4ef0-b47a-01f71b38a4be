{"ast": null, "code": "/**\n * @fileOverview X Axis\n */\n\n/** Define of XAxis props */\n\nexport var XAxis = function XAxis() {\n  return null;\n};\nXAxis.displayName = 'XAxis';\nXAxis.defaultProps = {\n  allowDecimals: true,\n  hide: false,\n  orientation: 'bottom',\n  width: 0,\n  height: 30,\n  mirror: false,\n  xAxisId: 0,\n  tickCount: 5,\n  type: 'category',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  allowDataOverflow: false,\n  scale: 'auto',\n  reversed: false,\n  allowDuplicatedCategory: true\n};", "map": {"version": 3, "names": ["XAxis", "displayName", "defaultProps", "allowDecimals", "hide", "orientation", "width", "height", "mirror", "xAxisId", "tickCount", "type", "padding", "left", "right", "allowDataOverflow", "scale", "reversed", "allowDuplicatedCategory"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/XAxis.js"], "sourcesContent": ["/**\n * @fileOverview X Axis\n */\n\n/** Define of XAxis props */\n\nexport var XAxis = function XAxis() {\n  return null;\n};\nXAxis.displayName = 'XAxis';\nXAxis.defaultProps = {\n  allowDecimals: true,\n  hide: false,\n  orientation: 'bottom',\n  width: 0,\n  height: 30,\n  mirror: false,\n  xAxisId: 0,\n  tickCount: 5,\n  type: 'category',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  allowDataOverflow: false,\n  scale: 'auto',\n  reversed: false,\n  allowDuplicatedCategory: true\n};"], "mappings": "AAAA;AACA;AACA;;AAEA;;AAEA,OAAO,IAAIA,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAClC,OAAO,IAAI;AACb,CAAC;AACDA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,YAAY,GAAG;EACnBC,aAAa,EAAE,IAAI;EACnBC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,QAAQ;EACrBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC;EACDC,iBAAiB,EAAE,KAAK;EACxBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,KAAK;EACfC,uBAAuB,EAAE;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}