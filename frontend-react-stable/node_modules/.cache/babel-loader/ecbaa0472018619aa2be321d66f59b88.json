{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = group;\nexports.flatGroup = flatGroup;\nexports.flatRollup = flatRollup;\nexports.groups = groups;\nexports.index = index;\nexports.indexes = indexes;\nexports.rollup = rollup;\nexports.rollups = rollups;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction group(values, ...keys) {\n  return nest(values, _identity.default, _identity.default, keys);\n}\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, _identity.default, keys);\n}\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\nfunction flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\nfunction flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, _identity.default, reduce, keys);\n}\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\nfunction index(values, ...keys) {\n  return nest(values, _identity.default, unique, keys);\n}\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new _index.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  }(values, 0);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "group", "flatGroup", "flatRollup", "groups", "index", "indexes", "rollup", "rollups", "_index", "require", "_identity", "_interopRequireDefault", "obj", "__esModule", "values", "keys", "nest", "Array", "from", "flatten", "i", "n", "length", "flatMap", "g", "pop", "map", "key", "reduce", "unique", "Error", "regroup", "InternMap", "keyof", "get", "push", "set"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/group.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = group;\nexports.flatGroup = flatGroup;\nexports.flatRollup = flatRollup;\nexports.groups = groups;\nexports.index = index;\nexports.indexes = indexes;\nexports.rollup = rollup;\nexports.rollups = rollups;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction group(values, ...keys) {\n  return nest(values, _identity.default, _identity.default, keys);\n}\n\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, _identity.default, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n\n  return groups;\n}\n\nfunction flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nfunction flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, _identity.default, reduce, keys);\n}\n\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nfunction index(values, ...keys) {\n  return nest(values, _identity.default, unique, keys);\n}\n\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new _index.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n\n    return map(groups);\n  }(values, 0);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AACvBH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAC7BJ,OAAO,CAACK,UAAU,GAAGA,UAAU;AAC/BL,OAAO,CAACM,MAAM,GAAGA,MAAM;AACvBN,OAAO,CAACO,KAAK,GAAGA,KAAK;AACrBP,OAAO,CAACQ,OAAO,GAAGA,OAAO;AACzBR,OAAO,CAACS,MAAM,GAAGA,MAAM;AACvBT,OAAO,CAACU,OAAO,GAAGA,OAAO;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,IAAIC,SAAS,GAAGC,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEb,OAAO,EAAEa;EAAI,CAAC;AAAE;AAE9F,SAASZ,KAAKA,CAACc,MAAM,EAAE,GAAGC,IAAI,EAAE;EAC9B,OAAOC,IAAI,CAACF,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAEW,SAAS,CAACX,OAAO,EAAEgB,IAAI,CAAC;AACjE;AAEA,SAASZ,MAAMA,CAACW,MAAM,EAAE,GAAGC,IAAI,EAAE;EAC/B,OAAOC,IAAI,CAACF,MAAM,EAAEG,KAAK,CAACC,IAAI,EAAER,SAAS,CAACX,OAAO,EAAEgB,IAAI,CAAC;AAC1D;AAEA,SAASI,OAAOA,CAAChB,MAAM,EAAEY,IAAI,EAAE;EAC7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3CjB,MAAM,GAAGA,MAAM,CAACoB,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAE7B,KAAK,CAAC,KAAK,CAAC,GAAG0B,CAAC,EAAEG,GAAG,EAAE7B,KAAK,CAAC,CAAC,CAAC;EACjF;EAEA,OAAOK,MAAM;AACf;AAEA,SAASF,SAASA,CAACa,MAAM,EAAE,GAAGC,IAAI,EAAE;EAClC,OAAOI,OAAO,CAAChB,MAAM,CAACW,MAAM,EAAE,GAAGC,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC/C;AAEA,SAASb,UAAUA,CAACY,MAAM,EAAEc,MAAM,EAAE,GAAGb,IAAI,EAAE;EAC3C,OAAOI,OAAO,CAACZ,OAAO,CAACO,MAAM,EAAEc,MAAM,EAAE,GAAGb,IAAI,CAAC,EAAEA,IAAI,CAAC;AACxD;AAEA,SAAST,MAAMA,CAACQ,MAAM,EAAEc,MAAM,EAAE,GAAGb,IAAI,EAAE;EACvC,OAAOC,IAAI,CAACF,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAE6B,MAAM,EAAEb,IAAI,CAAC;AACtD;AAEA,SAASR,OAAOA,CAACO,MAAM,EAAEc,MAAM,EAAE,GAAGb,IAAI,EAAE;EACxC,OAAOC,IAAI,CAACF,MAAM,EAAEG,KAAK,CAACC,IAAI,EAAEU,MAAM,EAAEb,IAAI,CAAC;AAC/C;AAEA,SAASX,KAAKA,CAACU,MAAM,EAAE,GAAGC,IAAI,EAAE;EAC9B,OAAOC,IAAI,CAACF,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAE8B,MAAM,EAAEd,IAAI,CAAC;AACtD;AAEA,SAASV,OAAOA,CAACS,MAAM,EAAE,GAAGC,IAAI,EAAE;EAChC,OAAOC,IAAI,CAACF,MAAM,EAAEG,KAAK,CAACC,IAAI,EAAEW,MAAM,EAAEd,IAAI,CAAC;AAC/C;AAEA,SAASc,MAAMA,CAACf,MAAM,EAAE;EACtB,IAAIA,MAAM,CAACQ,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIQ,KAAK,CAAC,eAAe,CAAC;EACzD,OAAOhB,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,SAASE,IAAIA,CAACF,MAAM,EAAEY,GAAG,EAAEE,MAAM,EAAEb,IAAI,EAAE;EACvC,OAAO,SAASgB,OAAOA,CAACjB,MAAM,EAAEM,CAAC,EAAE;IACjC,IAAIA,CAAC,IAAIL,IAAI,CAACO,MAAM,EAAE,OAAOM,MAAM,CAACd,MAAM,CAAC;IAC3C,MAAMX,MAAM,GAAG,IAAIK,MAAM,CAACwB,SAAS,CAAC,CAAC;IACrC,MAAMC,KAAK,GAAGlB,IAAI,CAACK,CAAC,EAAE,CAAC;IACvB,IAAIhB,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,MAAMN,KAAK,IAAIgB,MAAM,EAAE;MAC1B,MAAMa,GAAG,GAAGM,KAAK,CAACnC,KAAK,EAAE,EAAEM,KAAK,EAAEU,MAAM,CAAC;MACzC,MAAMd,KAAK,GAAGG,MAAM,CAAC+B,GAAG,CAACP,GAAG,CAAC;MAC7B,IAAI3B,KAAK,EAAEA,KAAK,CAACmC,IAAI,CAACrC,KAAK,CAAC,CAAC,KAAKK,MAAM,CAACiC,GAAG,CAACT,GAAG,EAAE,CAAC7B,KAAK,CAAC,CAAC;IAC5D;IAEA,KAAK,MAAM,CAAC6B,GAAG,EAAEb,MAAM,CAAC,IAAIX,MAAM,EAAE;MAClCA,MAAM,CAACiC,GAAG,CAACT,GAAG,EAAEI,OAAO,CAACjB,MAAM,EAAEM,CAAC,CAAC,CAAC;IACrC;IAEA,OAAOM,GAAG,CAACvB,MAAM,CAAC;EACpB,CAAC,CAACW,MAAM,EAAE,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}