{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { supportRef, useComposeRef, getNodeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { CollectionContext } from \"../Collection\";\nimport { observe, unobserve } from \"../utils/observerUtil\";\nimport DomWrapper from \"./DomWrapper\";\nfunction SingleObserver(props, ref) {\n  var children = props.children,\n    disabled = props.disabled;\n  var elementRef = React.useRef(null);\n  var wrapperRef = React.useRef(null);\n  var onCollectionResize = React.useContext(CollectionContext);\n\n  // =========================== Children ===========================\n  var isRenderProps = typeof children === 'function';\n  var mergedChildren = isRenderProps ? children(elementRef) : children;\n\n  // ============================= Size =============================\n  var sizeRef = React.useRef({\n    width: -1,\n    height: -1,\n    offsetWidth: -1,\n    offsetHeight: -1\n  });\n\n  // ============================= Ref ==============================\n  var canRef = !isRenderProps && /*#__PURE__*/React.isValidElement(mergedChildren) && supportRef(mergedChildren);\n  var originRef = canRef ? getNodeRef(mergedChildren) : null;\n  var mergedRef = useComposeRef(originRef, elementRef);\n  var getDom = function getDom() {\n    var _elementRef$current;\n    return findDOMNode(elementRef.current) || (\n    // Support `nativeElement` format\n    elementRef.current && _typeof(elementRef.current) === 'object' ? findDOMNode((_elementRef$current = elementRef.current) === null || _elementRef$current === void 0 ? void 0 : _elementRef$current.nativeElement) : null) || findDOMNode(wrapperRef.current);\n  };\n  React.useImperativeHandle(ref, function () {\n    return getDom();\n  });\n\n  // =========================== Observe ============================\n  var propsRef = React.useRef(props);\n  propsRef.current = props;\n\n  // Handler\n  var onInternalResize = React.useCallback(function (target) {\n    var _propsRef$current = propsRef.current,\n      onResize = _propsRef$current.onResize,\n      data = _propsRef$current.data;\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n      width = _target$getBoundingCl.width,\n      height = _target$getBoundingCl.height;\n    var offsetWidth = target.offsetWidth,\n      offsetHeight = target.offsetHeight;\n\n    /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n      var size = {\n        width: fixedWidth,\n        height: fixedHeight,\n        offsetWidth: offsetWidth,\n        offsetHeight: offsetHeight\n      };\n      sizeRef.current = size;\n\n      // IE is strange, right?\n      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n      var sizeInfo = _objectSpread(_objectSpread({}, size), {}, {\n        offsetWidth: mergedOffsetWidth,\n        offsetHeight: mergedOffsetHeight\n      });\n\n      // Let collection know what happened\n      onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(sizeInfo, target, data);\n      if (onResize) {\n        // defer the callback but not defer to next frame\n        Promise.resolve().then(function () {\n          onResize(sizeInfo, target);\n        });\n      }\n    }\n  }, []);\n\n  // Dynamic observe\n  React.useEffect(function () {\n    var currentElement = getDom();\n    if (currentElement && !disabled) {\n      observe(currentElement, onInternalResize);\n    }\n    return function () {\n      return unobserve(currentElement, onInternalResize);\n    };\n  }, [elementRef.current, disabled]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(mergedChildren, {\n    ref: mergedRef\n  }) : mergedChildren);\n}\nvar RefSingleObserver = /*#__PURE__*/React.forwardRef(SingleObserver);\nif (process.env.NODE_ENV !== 'production') {\n  RefSingleObserver.displayName = 'SingleObserver';\n}\nexport default RefSingleObserver;", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "findDOMNode", "supportRef", "useComposeRef", "getNodeRef", "React", "CollectionContext", "observe", "unobserve", "DomWrapper", "SingleObserver", "props", "ref", "children", "disabled", "elementRef", "useRef", "wrapperRef", "onCollectionResize", "useContext", "isRenderProps", "mergedChildren", "sizeRef", "width", "height", "offsetWidth", "offsetHeight", "canRef", "isValidElement", "originRef", "mergedRef", "getDom", "_elementRef$current", "current", "nativeElement", "useImperativeHandle", "propsRef", "onInternalResize", "useCallback", "target", "_propsRef$current", "onResize", "data", "_target$getBoundingCl", "getBoundingClientRect", "fixedWidth", "Math", "floor", "fixedHeight", "size", "mergedOffsetWidth", "round", "mergedOffsetHeight", "sizeInfo", "Promise", "resolve", "then", "useEffect", "currentElement", "createElement", "cloneElement", "RefSingleObserver", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-resize-observer/es/SingleObserver/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { supportRef, useComposeRef, getNodeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { CollectionContext } from \"../Collection\";\nimport { observe, unobserve } from \"../utils/observerUtil\";\nimport DomWrapper from \"./DomWrapper\";\nfunction SingleObserver(props, ref) {\n  var children = props.children,\n    disabled = props.disabled;\n  var elementRef = React.useRef(null);\n  var wrapperRef = React.useRef(null);\n  var onCollectionResize = React.useContext(CollectionContext);\n\n  // =========================== Children ===========================\n  var isRenderProps = typeof children === 'function';\n  var mergedChildren = isRenderProps ? children(elementRef) : children;\n\n  // ============================= Size =============================\n  var sizeRef = React.useRef({\n    width: -1,\n    height: -1,\n    offsetWidth: -1,\n    offsetHeight: -1\n  });\n\n  // ============================= Ref ==============================\n  var canRef = !isRenderProps && /*#__PURE__*/React.isValidElement(mergedChildren) && supportRef(mergedChildren);\n  var originRef = canRef ? getNodeRef(mergedChildren) : null;\n  var mergedRef = useComposeRef(originRef, elementRef);\n  var getDom = function getDom() {\n    var _elementRef$current;\n    return findDOMNode(elementRef.current) || (\n    // Support `nativeElement` format\n    elementRef.current && _typeof(elementRef.current) === 'object' ? findDOMNode((_elementRef$current = elementRef.current) === null || _elementRef$current === void 0 ? void 0 : _elementRef$current.nativeElement) : null) || findDOMNode(wrapperRef.current);\n  };\n  React.useImperativeHandle(ref, function () {\n    return getDom();\n  });\n\n  // =========================== Observe ============================\n  var propsRef = React.useRef(props);\n  propsRef.current = props;\n\n  // Handler\n  var onInternalResize = React.useCallback(function (target) {\n    var _propsRef$current = propsRef.current,\n      onResize = _propsRef$current.onResize,\n      data = _propsRef$current.data;\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n      width = _target$getBoundingCl.width,\n      height = _target$getBoundingCl.height;\n    var offsetWidth = target.offsetWidth,\n      offsetHeight = target.offsetHeight;\n\n    /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n      var size = {\n        width: fixedWidth,\n        height: fixedHeight,\n        offsetWidth: offsetWidth,\n        offsetHeight: offsetHeight\n      };\n      sizeRef.current = size;\n\n      // IE is strange, right?\n      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n      var sizeInfo = _objectSpread(_objectSpread({}, size), {}, {\n        offsetWidth: mergedOffsetWidth,\n        offsetHeight: mergedOffsetHeight\n      });\n\n      // Let collection know what happened\n      onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(sizeInfo, target, data);\n      if (onResize) {\n        // defer the callback but not defer to next frame\n        Promise.resolve().then(function () {\n          onResize(sizeInfo, target);\n        });\n      }\n    }\n  }, []);\n\n  // Dynamic observe\n  React.useEffect(function () {\n    var currentElement = getDom();\n    if (currentElement && !disabled) {\n      observe(currentElement, onInternalResize);\n    }\n    return function () {\n      return unobserve(currentElement, onInternalResize);\n    };\n  }, [elementRef.current, disabled]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(mergedChildren, {\n    ref: mergedRef\n  }) : mergedChildren);\n}\nvar RefSingleObserver = /*#__PURE__*/React.forwardRef(SingleObserver);\nif (process.env.NODE_ENV !== 'production') {\n  RefSingleObserver.displayName = 'SingleObserver';\n}\nexport default RefSingleObserver;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,UAAU,EAAEC,aAAa,EAAEC,UAAU,QAAQ,gBAAgB;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,OAAO,EAAEC,SAAS,QAAQ,uBAAuB;AAC1D,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;EAC3B,IAAIC,UAAU,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAGZ,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,kBAAkB,GAAGb,KAAK,CAACc,UAAU,CAACb,iBAAiB,CAAC;;EAE5D;EACA,IAAIc,aAAa,GAAG,OAAOP,QAAQ,KAAK,UAAU;EAClD,IAAIQ,cAAc,GAAGD,aAAa,GAAGP,QAAQ,CAACE,UAAU,CAAC,GAAGF,QAAQ;;EAEpE;EACA,IAAIS,OAAO,GAAGjB,KAAK,CAACW,MAAM,CAAC;IACzBO,KAAK,EAAE,CAAC,CAAC;IACTC,MAAM,EAAE,CAAC,CAAC;IACVC,WAAW,EAAE,CAAC,CAAC;IACfC,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC;;EAEF;EACA,IAAIC,MAAM,GAAG,CAACP,aAAa,IAAI,aAAaf,KAAK,CAACuB,cAAc,CAACP,cAAc,CAAC,IAAInB,UAAU,CAACmB,cAAc,CAAC;EAC9G,IAAIQ,SAAS,GAAGF,MAAM,GAAGvB,UAAU,CAACiB,cAAc,CAAC,GAAG,IAAI;EAC1D,IAAIS,SAAS,GAAG3B,aAAa,CAAC0B,SAAS,EAAEd,UAAU,CAAC;EACpD,IAAIgB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7B,IAAIC,mBAAmB;IACvB,OAAO/B,WAAW,CAACc,UAAU,CAACkB,OAAO,CAAC;IACtC;IACAlB,UAAU,CAACkB,OAAO,IAAIjC,OAAO,CAACe,UAAU,CAACkB,OAAO,CAAC,KAAK,QAAQ,GAAGhC,WAAW,CAAC,CAAC+B,mBAAmB,GAAGjB,UAAU,CAACkB,OAAO,MAAM,IAAI,IAAID,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACE,aAAa,CAAC,GAAG,IAAI,CAAC,IAAIjC,WAAW,CAACgB,UAAU,CAACgB,OAAO,CAAC;EAC7P,CAAC;EACD5B,KAAK,CAAC8B,mBAAmB,CAACvB,GAAG,EAAE,YAAY;IACzC,OAAOmB,MAAM,CAAC,CAAC;EACjB,CAAC,CAAC;;EAEF;EACA,IAAIK,QAAQ,GAAG/B,KAAK,CAACW,MAAM,CAACL,KAAK,CAAC;EAClCyB,QAAQ,CAACH,OAAO,GAAGtB,KAAK;;EAExB;EACA,IAAI0B,gBAAgB,GAAGhC,KAAK,CAACiC,WAAW,CAAC,UAAUC,MAAM,EAAE;IACzD,IAAIC,iBAAiB,GAAGJ,QAAQ,CAACH,OAAO;MACtCQ,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;MACrCC,IAAI,GAAGF,iBAAiB,CAACE,IAAI;IAC/B,IAAIC,qBAAqB,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MACxDrB,KAAK,GAAGoB,qBAAqB,CAACpB,KAAK;MACnCC,MAAM,GAAGmB,qBAAqB,CAACnB,MAAM;IACvC,IAAIC,WAAW,GAAGc,MAAM,CAACd,WAAW;MAClCC,YAAY,GAAGa,MAAM,CAACb,YAAY;;IAEpC;AACJ;AACA;AACA;AACA;IACI,IAAImB,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACxB,KAAK,CAAC;IAClC,IAAIyB,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACvB,MAAM,CAAC;IACpC,IAAIF,OAAO,CAACW,OAAO,CAACV,KAAK,KAAKsB,UAAU,IAAIvB,OAAO,CAACW,OAAO,CAACT,MAAM,KAAKwB,WAAW,IAAI1B,OAAO,CAACW,OAAO,CAACR,WAAW,KAAKA,WAAW,IAAIH,OAAO,CAACW,OAAO,CAACP,YAAY,KAAKA,YAAY,EAAE;MAClL,IAAIuB,IAAI,GAAG;QACT1B,KAAK,EAAEsB,UAAU;QACjBrB,MAAM,EAAEwB,WAAW;QACnBvB,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA;MAChB,CAAC;MACDJ,OAAO,CAACW,OAAO,GAAGgB,IAAI;;MAEtB;MACA,IAAIC,iBAAiB,GAAGzB,WAAW,KAAKqB,IAAI,CAACK,KAAK,CAAC5B,KAAK,CAAC,GAAGA,KAAK,GAAGE,WAAW;MAC/E,IAAI2B,kBAAkB,GAAG1B,YAAY,KAAKoB,IAAI,CAACK,KAAK,CAAC3B,MAAM,CAAC,GAAGA,MAAM,GAAGE,YAAY;MACpF,IAAI2B,QAAQ,GAAGtD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACxDxB,WAAW,EAAEyB,iBAAiB;QAC9BxB,YAAY,EAAE0B;MAChB,CAAC,CAAC;;MAEF;MACAlC,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACmC,QAAQ,EAAEd,MAAM,EAAEG,IAAI,CAAC;MAC1G,IAAID,QAAQ,EAAE;QACZ;QACAa,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;UACjCf,QAAQ,CAACY,QAAQ,EAAEd,MAAM,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlC,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAIC,cAAc,GAAG3B,MAAM,CAAC,CAAC;IAC7B,IAAI2B,cAAc,IAAI,CAAC5C,QAAQ,EAAE;MAC/BP,OAAO,CAACmD,cAAc,EAAErB,gBAAgB,CAAC;IAC3C;IACA,OAAO,YAAY;MACjB,OAAO7B,SAAS,CAACkD,cAAc,EAAErB,gBAAgB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACtB,UAAU,CAACkB,OAAO,EAAEnB,QAAQ,CAAC,CAAC;;EAElC;EACA,OAAO,aAAaT,KAAK,CAACsD,aAAa,CAAClD,UAAU,EAAE;IAClDG,GAAG,EAAEK;EACP,CAAC,EAAEU,MAAM,GAAG,aAAatB,KAAK,CAACuD,YAAY,CAACvC,cAAc,EAAE;IAC1DT,GAAG,EAAEkB;EACP,CAAC,CAAC,GAAGT,cAAc,CAAC;AACtB;AACA,IAAIwC,iBAAiB,GAAG,aAAaxD,KAAK,CAACyD,UAAU,CAACpD,cAAc,CAAC;AACrE,IAAIqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,iBAAiB,CAACK,WAAW,GAAG,gBAAgB;AAClD;AACA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}