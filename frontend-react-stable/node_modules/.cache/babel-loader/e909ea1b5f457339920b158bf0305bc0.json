{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = count;\nfunction count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "count", "values", "valueof", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/count.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = count;\n\nfunction count(values, valueof) {\n  let count = 0;\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n\n  return count;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9B,IAAIF,KAAK,GAAG,CAAC;EAEb,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,IAAIL,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAIH,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9C,EAAEE,KAAK;MACT;IACF;EACF,CAAC,MAAM;IACL,IAAII,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIN,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,KAAK,IAAI,IAAI,CAACH,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClF,EAAEE,KAAK;MACT;IACF;EACF;EAEA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}