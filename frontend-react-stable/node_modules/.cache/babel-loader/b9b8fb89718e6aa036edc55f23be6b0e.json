{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport uiReducer from './slices/uiSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    ui: uiReducer\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST']\n    }\n  })\n});", "map": {"version": 3, "names": ["configureStore", "authReducer", "uiReducer", "store", "reducer", "auth", "ui", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["/home/<USER>/frontend-react-stable/src/store/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport uiReducer from './slices/uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    ui: uiReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,SAAS,MAAM,kBAAkB;AAExC,OAAO,MAAMC,KAAK,GAAGH,cAAc,CAAC;EAClCI,OAAO,EAAE;IACPC,IAAI,EAAEJ,WAAW;IACjBK,EAAE,EAAEJ;EACN,CAAC;EACDK,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB;IACpC;EACF,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}