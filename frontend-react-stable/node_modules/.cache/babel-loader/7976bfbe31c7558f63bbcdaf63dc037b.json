{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _formatDecimal = require(\"./formatDecimal.js\");\nfunction _default(x) {\n  return x = (0, _formatDecimal.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_formatDecimal", "require", "x", "formatDecimalParts", "Math", "abs", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/exponent.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _formatDecimal = require(\"./formatDecimal.js\");\n\nfunction _default(x) {\n  return x = (0, _formatDecimal.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,cAAc,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAElD,SAASF,QAAQA,CAACG,CAAC,EAAE;EACnB,OAAOA,CAAC,GAAG,CAAC,CAAC,EAAEF,cAAc,CAACG,kBAAkB,EAAEC,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,CAAC,EAAEA,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}