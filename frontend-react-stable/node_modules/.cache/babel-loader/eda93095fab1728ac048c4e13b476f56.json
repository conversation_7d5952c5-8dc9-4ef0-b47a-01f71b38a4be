{"ast": null, "code": "var locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nexport default locale;", "map": {"version": 3, "names": ["locale", "placeholder", "rangePlaceholder"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/time-picker/locale/en_US.js"], "sourcesContent": ["var locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nexport default locale;"], "mappings": "AAAA,IAAIA,MAAM,GAAG;EACXC,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU;AAC7C,CAAC;AACD,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}