{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    style = _ref.style,\n    id = _ref.id,\n    active = _ref.active,\n    tabKey = _ref.tabKey,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;", "map": {"version": 3, "names": ["React", "classNames", "TabPane", "forwardRef", "_ref", "ref", "prefixCls", "className", "style", "id", "active", "tabKey", "children", "createElement", "concat", "role", "tabIndex", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabPanelList/TabPane.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n      className = _ref.className,\n      style = _ref.style,\n      id = _ref.id,\n      active = _ref.active,\n      tabKey = _ref.tabKey,\n      children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\n\nexport default TabPane;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,OAAO,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EAC/D,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,EAAE,GAAGL,IAAI,CAACK,EAAE;IACZC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,MAAM,GAAGP,IAAI,CAACO,MAAM;IACpBC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;EAC5B,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IAC7CJ,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACK,MAAM,CAACL,EAAE,EAAE,SAAS,CAAC,CAACK,MAAM,CAACH,MAAM,CAAC;IACjDI,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAEN,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,iBAAiB,EAAED,EAAE,IAAI,EAAE,CAACK,MAAM,CAACL,EAAE,EAAE,OAAO,CAAC,CAACK,MAAM,CAACH,MAAM,CAAC;IAC9D,aAAa,EAAE,CAACD,MAAM;IACtBF,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEN,UAAU,CAACK,SAAS,EAAEI,MAAM,IAAI,EAAE,CAACI,MAAM,CAACR,SAAS,EAAE,SAAS,CAAC,EAAEC,SAAS,CAAC;IACtFF,GAAG,EAAEA;EACP,CAAC,EAAEO,QAAQ,CAAC;AACd,CAAC,CAAC;AAEF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,OAAO,CAACkB,WAAW,GAAG,SAAS;AACjC;AAEA,eAAelB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}