{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport RcTable, { Summary } from 'rc-table';\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport { INTERNAL_HOOKS } from \"rc-table/es/Table\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider/context';\nimport defaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport SizeContext from '../config-provider/SizeContext';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport defaultLocale from '../locale/en_US';\nimport Pagination from '../pagination';\nimport Spin from '../spin';\nimport scrollTo from '../_util/scrollTo';\nimport warning from '../_util/warning';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport renderExpandIcon from './ExpandIcon';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useSelection, { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useTitleColumns from './hooks/useTitleColumns';\nvar EMPTY_LIST = [];\nfunction InternalTable(props, ref) {\n  var _classNames3;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    customizeSize = props.size,\n    bordered = props.bordered,\n    customizeDropdownPrefixCls = props.dropdownPrefixCls,\n    dataSource = props.dataSource,\n    pagination = props.pagination,\n    rowSelection = props.rowSelection,\n    _props$rowKey = props.rowKey,\n    rowKey = _props$rowKey === void 0 ? 'key' : _props$rowKey,\n    rowClassName = props.rowClassName,\n    columns = props.columns,\n    children = props.children,\n    legacyChildrenColumnName = props.childrenColumnName,\n    onChange = props.onChange,\n    getPopupContainer = props.getPopupContainer,\n    loading = props.loading,\n    expandIcon = props.expandIcon,\n    expandable = props.expandable,\n    expandedRowRender = props.expandedRowRender,\n    expandIconColumnIndex = props.expandIconColumnIndex,\n    indentSize = props.indentSize,\n    scroll = props.scroll,\n    sortDirections = props.sortDirections,\n    locale = props.locale,\n    _props$showSorterTool = props.showSorterTooltip,\n    showSorterTooltip = _props$showSorterTool === void 0 ? true : _props$showSorterTool;\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof rowKey === 'function' && rowKey.length > 1), 'Table', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.') : void 0;\n  [['filterDropdownVisible', 'filterDropdownOpen'], ['onFilterDropdownVisibleChange', 'onFilterDropdownOpenChange']].forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      deprecatedName = _ref2[0],\n      newName = _ref2[1];\n    process.env.NODE_ENV !== \"production\" ? warning(!(deprecatedName in props), 'Table', \"`\".concat(deprecatedName, \"` is deprecated which will be removed in next major version.Please use `\").concat(newName, \"` instead. \")) : void 0;\n  });\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]);\n  var needResponsive = React.useMemo(function () {\n    return baseColumns.some(function (col) {\n      return col.responsive;\n    });\n  }, [baseColumns]);\n  var screens = useBreakpoint(needResponsive);\n  var mergedColumns = React.useMemo(function () {\n    var matched = new Set(Object.keys(screens).filter(function (m) {\n      return screens[m];\n    }));\n    return baseColumns.filter(function (c) {\n      return !c.responsive || c.responsive.some(function (r) {\n        return matched.has(r);\n      });\n    });\n  }, [baseColumns, screens]);\n  var tableProps = omit(props, ['className', 'style', 'columns']);\n  var size = React.useContext(SizeContext);\n  var _React$useContext = React.useContext(ConfigContext),\n    _React$useContext$loc = _React$useContext.locale,\n    contextLocale = _React$useContext$loc === void 0 ? defaultLocale : _React$useContext$loc,\n    renderEmpty = _React$useContext.renderEmpty,\n    direction = _React$useContext.direction;\n  var mergedSize = customizeSize || size;\n  var tableLocale = _extends(_extends({}, contextLocale.Table), locale);\n  var rawData = dataSource || EMPTY_LIST;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var prefixCls = getPrefixCls('table', customizePrefixCls);\n  var dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n  var mergedExpandable = _extends({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex: expandIconColumnIndex\n  }, expandable);\n  var _mergedExpandable$chi = mergedExpandable.childrenColumnName,\n    childrenColumnName = _mergedExpandable$chi === void 0 ? 'children' : _mergedExpandable$chi;\n  var expandType = React.useMemo(function () {\n    if (rawData.some(function (item) {\n      return item === null || item === void 0 ? void 0 : item[childrenColumnName];\n    })) {\n      return 'nest';\n    }\n    if (expandedRowRender || expandable && expandable.expandedRowRender) {\n      return 'row';\n    }\n    return null;\n  }, [rawData]);\n  var internalRefs = {\n    body: React.useRef()\n  };\n  // ============================ RowKey ============================\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      return record === null || record === void 0 ? void 0 : record[rowKey];\n    };\n  }, [rowKey]);\n  var _useLazyKVMap = useLazyKVMap(rawData, childrenColumnName, getRowKey),\n    _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1),\n    getRecordByKey = _useLazyKVMap2[0];\n  // ============================ Events =============================\n  var changeEventInfo = {};\n  var triggerOnChange = function triggerOnChange(info, action) {\n    var reset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var changeInfo = _extends(_extends({}, changeEventInfo), info);\n    if (reset) {\n      changeEventInfo.resetPagination();\n      // Reset event param\n      if (changeInfo.pagination.current) {\n        changeInfo.pagination.current = 1;\n      }\n      // Trigger pagination events\n      if (pagination && pagination.onChange) {\n        pagination.onChange(1, changeInfo.pagination.pageSize);\n      }\n    }\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: function getContainer() {\n          return internalRefs.body.current;\n        }\n      });\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates),\n      action: action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big\n   * version.\n   */\n  // ============================ Sorter =============================\n  var onSorterChange = function onSorterChange(sorter, sorterStates) {\n    triggerOnChange({\n      sorter: sorter,\n      sorterStates: sorterStates\n    }, 'sort', false);\n  };\n  var _useSorter = useSorter({\n      prefixCls: prefixCls,\n      mergedColumns: mergedColumns,\n      onSorterChange: onSorterChange,\n      sortDirections: sortDirections || ['ascend', 'descend'],\n      tableLocale: tableLocale,\n      showSorterTooltip: showSorterTooltip\n    }),\n    _useSorter2 = _slicedToArray(_useSorter, 4),\n    transformSorterColumns = _useSorter2[0],\n    sortStates = _useSorter2[1],\n    sorterTitleProps = _useSorter2[2],\n    getSorters = _useSorter2[3];\n  var sortedData = React.useMemo(function () {\n    return getSortData(rawData, sortStates, childrenColumnName);\n  }, [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates;\n  // ============================ Filter ============================\n  var onFilterChange = function onFilterChange(filters, filterStates) {\n    triggerOnChange({\n      filters: filters,\n      filterStates: filterStates\n    }, 'filter', true);\n  };\n  var _useFilter = useFilter({\n      prefixCls: prefixCls,\n      locale: tableLocale,\n      dropdownPrefixCls: dropdownPrefixCls,\n      mergedColumns: mergedColumns,\n      onFilterChange: onFilterChange,\n      getPopupContainer: getPopupContainer\n    }),\n    _useFilter2 = _slicedToArray(_useFilter, 3),\n    transformFilterColumns = _useFilter2[0],\n    filterStates = _useFilter2[1],\n    filters = _useFilter2[2];\n  var mergedData = getFilterData(sortedData, filterStates);\n  changeEventInfo.filters = filters;\n  changeEventInfo.filterStates = filterStates;\n  // ============================ Column ============================\n  var columnTitleProps = React.useMemo(function () {\n    var mergedFilters = {};\n    Object.keys(filters).forEach(function (filterKey) {\n      if (filters[filterKey] !== null) {\n        mergedFilters[filterKey] = filters[filterKey];\n      }\n    });\n    return _extends(_extends({}, sorterTitleProps), {\n      filters: mergedFilters\n    });\n  }, [sorterTitleProps, filters]);\n  var _useTitleColumns = useTitleColumns(columnTitleProps),\n    _useTitleColumns2 = _slicedToArray(_useTitleColumns, 1),\n    transformTitleColumns = _useTitleColumns2[0];\n  // ========================== Pagination ==========================\n  var onPaginationChange = function onPaginationChange(current, pageSize) {\n    triggerOnChange({\n      pagination: _extends(_extends({}, changeEventInfo.pagination), {\n        current: current,\n        pageSize: pageSize\n      })\n    }, 'paginate');\n  };\n  var _usePagination = usePagination(mergedData.length, pagination, onPaginationChange),\n    _usePagination2 = _slicedToArray(_usePagination, 2),\n    mergedPagination = _usePagination2[0],\n    resetPagination = _usePagination2[1];\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(pagination, mergedPagination);\n  changeEventInfo.resetPagination = resetPagination;\n  // ============================= Data =============================\n  var pageData = React.useMemo(function () {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n    var _mergedPagination$cur = mergedPagination.current,\n      current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur,\n      total = mergedPagination.total,\n      _mergedPagination$pag = mergedPagination.pageSize,\n      pageSize = _mergedPagination$pag === void 0 ? DEFAULT_PAGE_SIZE : _mergedPagination$pag;\n    process.env.NODE_ENV !== \"production\" ? warning(current > 0, 'Table', '`current` should be positive number.') : void 0;\n    // Dynamic table data\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'Table', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.') : void 0;\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n      return mergedData;\n    }\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination && mergedPagination.current, mergedPagination && mergedPagination.pageSize, mergedPagination && mergedPagination.total]);\n  // ========================== Selections ==========================\n  var _useSelection = useSelection(rowSelection, {\n      prefixCls: prefixCls,\n      data: mergedData,\n      pageData: pageData,\n      getRowKey: getRowKey,\n      getRecordByKey: getRecordByKey,\n      expandType: expandType,\n      childrenColumnName: childrenColumnName,\n      locale: tableLocale,\n      getPopupContainer: getPopupContainer\n    }),\n    _useSelection2 = _slicedToArray(_useSelection, 2),\n    transformSelectionColumns = _useSelection2[0],\n    selectedKeySet = _useSelection2[1];\n  var internalRowClassName = function internalRowClassName(record, index, indent) {\n    var mergedRowClassName;\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n    return classNames(_defineProperty({}, \"\".concat(prefixCls, \"-row-selected\"), selectedKeySet.has(getRowKey(record, index))), mergedRowClassName);\n  };\n  // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon;\n  // Customize expandable icon\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale);\n  // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  }\n  // Indent size\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  }\n  // ============================ Render ============================\n  var transformColumns = React.useCallback(function (innerColumns) {\n    return transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns))));\n  }, [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  var topPaginationNode;\n  var bottomPaginationNode;\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    var paginationSize;\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n    var renderPagination = function renderPagination(position) {\n      return /*#__PURE__*/React.createElement(Pagination, _extends({}, mergedPagination, {\n        className: classNames(\"\".concat(prefixCls, \"-pagination \").concat(prefixCls, \"-pagination-\").concat(position), mergedPagination.className),\n        size: paginationSize\n      }));\n    };\n    var defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    var position = mergedPagination.position;\n    if (position !== null && Array.isArray(position)) {\n      var topPos = position.find(function (p) {\n        return p.includes('top');\n      });\n      var bottomPos = position.find(function (p) {\n        return p.includes('bottom');\n      });\n      var isDisable = position.every(function (p) {\n        return \"\".concat(p) === 'none';\n      });\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  }\n  // >>>>>>>>> Spinning\n  var spinProps;\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (_typeof(loading) === 'object') {\n    spinProps = _extends({\n      spinning: true\n    }, loading);\n  }\n  var wrapperClassNames = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-wrapper-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: wrapperClassNames,\n    style: style\n  }, /*#__PURE__*/React.createElement(Spin, _extends({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(RcTable, _extends({}, tableProps, {\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-middle\"), mergedSize === 'middle'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-small\"), mergedSize === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-bordered\"), bordered), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-empty\"), rawData.length === 0), _classNames3)),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: locale && locale.emptyText || (renderEmpty || defaultRenderEmpty)('Table'),\n    // Internal\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns\n  })), bottomPaginationNode));\n}\nvar ForwardTable = /*#__PURE__*/React.forwardRef(InternalTable);\nvar Table = ForwardTable;\nTable.SELECTION_COLUMN = SELECTION_COLUMN;\nTable.EXPAND_COLUMN = RcTable.EXPAND_COLUMN;\nTable.SELECTION_ALL = SELECTION_ALL;\nTable.SELECTION_INVERT = SELECTION_INVERT;\nTable.SELECTION_NONE = SELECTION_NONE;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = Summary;\nexport default Table;", "map": {"version": 3, "names": ["_typeof", "_defineProperty", "_extends", "_slicedToArray", "classNames", "RcTable", "Summary", "convertChildrenToColumns", "INTERNAL_HOOKS", "omit", "React", "ConfigContext", "defaultRenderEmpty", "SizeContext", "useBreakpoint", "defaultLocale", "Pagination", "Spin", "scrollTo", "warning", "Column", "ColumnGroup", "renderExpandIcon", "useFilter", "getFilterData", "useLazyKVMap", "usePagination", "DEFAULT_PAGE_SIZE", "getPaginationParam", "useSelection", "SELECTION_ALL", "SELECTION_COLUMN", "SELECTION_INVERT", "SELECTION_NONE", "useSorter", "getSortData", "useTitleColumns", "EMPTY_LIST", "InternalTable", "props", "ref", "_classNames3", "customizePrefixCls", "prefixCls", "className", "style", "customizeSize", "size", "bordered", "customizeDropdownPrefixCls", "dropdownPrefixCls", "dataSource", "pagination", "rowSelection", "_props$rowKey", "<PERSON><PERSON><PERSON>", "rowClassName", "columns", "children", "legacyChildrenColumnName", "childrenColumnName", "onChange", "getPopupContainer", "loading", "expandIcon", "expandable", "expandedRowRender", "expandIconColumnIndex", "indentSize", "scroll", "sortDirections", "locale", "_props$showSorterTool", "showSorterTooltip", "process", "env", "NODE_ENV", "length", "for<PERSON>ach", "_ref", "_ref2", "deprecatedName", "newName", "concat", "baseColumns", "useMemo", "needResponsive", "some", "col", "responsive", "screens", "mergedColumns", "matched", "Set", "Object", "keys", "filter", "m", "c", "r", "has", "tableProps", "useContext", "_React$useContext", "_React$useContext$loc", "contextLocale", "renderEmpty", "direction", "mergedSize", "tableLocale", "Table", "rawData", "_React$useContext2", "getPrefixCls", "mergedExpandable", "_mergedExpandable$chi", "expandType", "item", "internalRefs", "body", "useRef", "getRowKey", "record", "_useLazyKVMap", "_useLazyKVMap2", "getRecordByKey", "changeEventInfo", "triggerOnChange", "info", "action", "reset", "arguments", "undefined", "changeInfo", "resetPagination", "current", "pageSize", "scrollToFirstRowOnChange", "getContainer", "filters", "sorter", "currentDataSource", "sorterStates", "filterStates", "onSorterChange", "_useSorter", "_useSorter2", "transformSorterColumns", "sortStates", "sorterTitleProps", "getSorters", "sortedData", "onFilterChange", "_useFilter", "_useFilter2", "transformFilterColumns", "mergedData", "columnTitleProps", "mergedFilters", "<PERSON><PERSON><PERSON>", "_useTitleColumns", "_useTitleColumns2", "transformTitleColumns", "onPaginationChange", "_usePagination", "_usePagination2", "mergedPagination", "pageData", "_mergedPagination$cur", "total", "_mergedPagination$pag", "slice", "_useSelection", "data", "_useSelection2", "transformSelectionColumns", "selectedKeySet", "internalRowClassName", "index", "indent", "mergedRowClassName", "__PARENT_RENDER_ICON__", "transformColumns", "useCallback", "innerColumns", "topPaginationNode", "bottomPaginationNode", "paginationSize", "renderPagination", "position", "createElement", "defaultPosition", "Array", "isArray", "topPos", "find", "p", "includes", "bottomPos", "isDisable", "every", "toLowerCase", "replace", "spinProps", "spinning", "wrapperClassNames", "emptyText", "internalHooks", "ForwardTable", "forwardRef", "EXPAND_COLUMN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/Table.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport RcTable, { Summary } from 'rc-table';\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport { INTERNAL_HOOKS } from \"rc-table/es/Table\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider/context';\nimport defaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport SizeContext from '../config-provider/SizeContext';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport defaultLocale from '../locale/en_US';\nimport Pagination from '../pagination';\nimport Spin from '../spin';\nimport scrollTo from '../_util/scrollTo';\nimport warning from '../_util/warning';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport renderExpandIcon from './ExpandIcon';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useSelection, { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useTitleColumns from './hooks/useTitleColumns';\nvar EMPTY_LIST = [];\nfunction InternalTable(props, ref) {\n  var _classNames3;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    customizeSize = props.size,\n    bordered = props.bordered,\n    customizeDropdownPrefixCls = props.dropdownPrefixCls,\n    dataSource = props.dataSource,\n    pagination = props.pagination,\n    rowSelection = props.rowSelection,\n    _props$rowKey = props.rowKey,\n    rowKey = _props$rowKey === void 0 ? 'key' : _props$rowKey,\n    rowClassName = props.rowClassName,\n    columns = props.columns,\n    children = props.children,\n    legacyChildrenColumnName = props.childrenColumnName,\n    onChange = props.onChange,\n    getPopupContainer = props.getPopupContainer,\n    loading = props.loading,\n    expandIcon = props.expandIcon,\n    expandable = props.expandable,\n    expandedRowRender = props.expandedRowRender,\n    expandIconColumnIndex = props.expandIconColumnIndex,\n    indentSize = props.indentSize,\n    scroll = props.scroll,\n    sortDirections = props.sortDirections,\n    locale = props.locale,\n    _props$showSorterTool = props.showSorterTooltip,\n    showSorterTooltip = _props$showSorterTool === void 0 ? true : _props$showSorterTool;\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof rowKey === 'function' && rowKey.length > 1), 'Table', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.') : void 0;\n  [['filterDropdownVisible', 'filterDropdownOpen'], ['onFilterDropdownVisibleChange', 'onFilterDropdownOpenChange']].forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      deprecatedName = _ref2[0],\n      newName = _ref2[1];\n    process.env.NODE_ENV !== \"production\" ? warning(!(deprecatedName in props), 'Table', \"`\".concat(deprecatedName, \"` is deprecated which will be removed in next major version.Please use `\").concat(newName, \"` instead. \")) : void 0;\n  });\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]);\n  var needResponsive = React.useMemo(function () {\n    return baseColumns.some(function (col) {\n      return col.responsive;\n    });\n  }, [baseColumns]);\n  var screens = useBreakpoint(needResponsive);\n  var mergedColumns = React.useMemo(function () {\n    var matched = new Set(Object.keys(screens).filter(function (m) {\n      return screens[m];\n    }));\n    return baseColumns.filter(function (c) {\n      return !c.responsive || c.responsive.some(function (r) {\n        return matched.has(r);\n      });\n    });\n  }, [baseColumns, screens]);\n  var tableProps = omit(props, ['className', 'style', 'columns']);\n  var size = React.useContext(SizeContext);\n  var _React$useContext = React.useContext(ConfigContext),\n    _React$useContext$loc = _React$useContext.locale,\n    contextLocale = _React$useContext$loc === void 0 ? defaultLocale : _React$useContext$loc,\n    renderEmpty = _React$useContext.renderEmpty,\n    direction = _React$useContext.direction;\n  var mergedSize = customizeSize || size;\n  var tableLocale = _extends(_extends({}, contextLocale.Table), locale);\n  var rawData = dataSource || EMPTY_LIST;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var prefixCls = getPrefixCls('table', customizePrefixCls);\n  var dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n  var mergedExpandable = _extends({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex: expandIconColumnIndex\n  }, expandable);\n  var _mergedExpandable$chi = mergedExpandable.childrenColumnName,\n    childrenColumnName = _mergedExpandable$chi === void 0 ? 'children' : _mergedExpandable$chi;\n  var expandType = React.useMemo(function () {\n    if (rawData.some(function (item) {\n      return item === null || item === void 0 ? void 0 : item[childrenColumnName];\n    })) {\n      return 'nest';\n    }\n    if (expandedRowRender || expandable && expandable.expandedRowRender) {\n      return 'row';\n    }\n    return null;\n  }, [rawData]);\n  var internalRefs = {\n    body: React.useRef()\n  };\n  // ============================ RowKey ============================\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      return record === null || record === void 0 ? void 0 : record[rowKey];\n    };\n  }, [rowKey]);\n  var _useLazyKVMap = useLazyKVMap(rawData, childrenColumnName, getRowKey),\n    _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1),\n    getRecordByKey = _useLazyKVMap2[0];\n  // ============================ Events =============================\n  var changeEventInfo = {};\n  var triggerOnChange = function triggerOnChange(info, action) {\n    var reset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var changeInfo = _extends(_extends({}, changeEventInfo), info);\n    if (reset) {\n      changeEventInfo.resetPagination();\n      // Reset event param\n      if (changeInfo.pagination.current) {\n        changeInfo.pagination.current = 1;\n      }\n      // Trigger pagination events\n      if (pagination && pagination.onChange) {\n        pagination.onChange(1, changeInfo.pagination.pageSize);\n      }\n    }\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: function getContainer() {\n          return internalRefs.body.current;\n        }\n      });\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates),\n      action: action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big\n   * version.\n   */\n  // ============================ Sorter =============================\n  var onSorterChange = function onSorterChange(sorter, sorterStates) {\n    triggerOnChange({\n      sorter: sorter,\n      sorterStates: sorterStates\n    }, 'sort', false);\n  };\n  var _useSorter = useSorter({\n      prefixCls: prefixCls,\n      mergedColumns: mergedColumns,\n      onSorterChange: onSorterChange,\n      sortDirections: sortDirections || ['ascend', 'descend'],\n      tableLocale: tableLocale,\n      showSorterTooltip: showSorterTooltip\n    }),\n    _useSorter2 = _slicedToArray(_useSorter, 4),\n    transformSorterColumns = _useSorter2[0],\n    sortStates = _useSorter2[1],\n    sorterTitleProps = _useSorter2[2],\n    getSorters = _useSorter2[3];\n  var sortedData = React.useMemo(function () {\n    return getSortData(rawData, sortStates, childrenColumnName);\n  }, [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates;\n  // ============================ Filter ============================\n  var onFilterChange = function onFilterChange(filters, filterStates) {\n    triggerOnChange({\n      filters: filters,\n      filterStates: filterStates\n    }, 'filter', true);\n  };\n  var _useFilter = useFilter({\n      prefixCls: prefixCls,\n      locale: tableLocale,\n      dropdownPrefixCls: dropdownPrefixCls,\n      mergedColumns: mergedColumns,\n      onFilterChange: onFilterChange,\n      getPopupContainer: getPopupContainer\n    }),\n    _useFilter2 = _slicedToArray(_useFilter, 3),\n    transformFilterColumns = _useFilter2[0],\n    filterStates = _useFilter2[1],\n    filters = _useFilter2[2];\n  var mergedData = getFilterData(sortedData, filterStates);\n  changeEventInfo.filters = filters;\n  changeEventInfo.filterStates = filterStates;\n  // ============================ Column ============================\n  var columnTitleProps = React.useMemo(function () {\n    var mergedFilters = {};\n    Object.keys(filters).forEach(function (filterKey) {\n      if (filters[filterKey] !== null) {\n        mergedFilters[filterKey] = filters[filterKey];\n      }\n    });\n    return _extends(_extends({}, sorterTitleProps), {\n      filters: mergedFilters\n    });\n  }, [sorterTitleProps, filters]);\n  var _useTitleColumns = useTitleColumns(columnTitleProps),\n    _useTitleColumns2 = _slicedToArray(_useTitleColumns, 1),\n    transformTitleColumns = _useTitleColumns2[0];\n  // ========================== Pagination ==========================\n  var onPaginationChange = function onPaginationChange(current, pageSize) {\n    triggerOnChange({\n      pagination: _extends(_extends({}, changeEventInfo.pagination), {\n        current: current,\n        pageSize: pageSize\n      })\n    }, 'paginate');\n  };\n  var _usePagination = usePagination(mergedData.length, pagination, onPaginationChange),\n    _usePagination2 = _slicedToArray(_usePagination, 2),\n    mergedPagination = _usePagination2[0],\n    resetPagination = _usePagination2[1];\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(pagination, mergedPagination);\n  changeEventInfo.resetPagination = resetPagination;\n  // ============================= Data =============================\n  var pageData = React.useMemo(function () {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n    var _mergedPagination$cur = mergedPagination.current,\n      current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur,\n      total = mergedPagination.total,\n      _mergedPagination$pag = mergedPagination.pageSize,\n      pageSize = _mergedPagination$pag === void 0 ? DEFAULT_PAGE_SIZE : _mergedPagination$pag;\n    process.env.NODE_ENV !== \"production\" ? warning(current > 0, 'Table', '`current` should be positive number.') : void 0;\n    // Dynamic table data\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'Table', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.') : void 0;\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n      return mergedData;\n    }\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination && mergedPagination.current, mergedPagination && mergedPagination.pageSize, mergedPagination && mergedPagination.total]);\n  // ========================== Selections ==========================\n  var _useSelection = useSelection(rowSelection, {\n      prefixCls: prefixCls,\n      data: mergedData,\n      pageData: pageData,\n      getRowKey: getRowKey,\n      getRecordByKey: getRecordByKey,\n      expandType: expandType,\n      childrenColumnName: childrenColumnName,\n      locale: tableLocale,\n      getPopupContainer: getPopupContainer\n    }),\n    _useSelection2 = _slicedToArray(_useSelection, 2),\n    transformSelectionColumns = _useSelection2[0],\n    selectedKeySet = _useSelection2[1];\n  var internalRowClassName = function internalRowClassName(record, index, indent) {\n    var mergedRowClassName;\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n    return classNames(_defineProperty({}, \"\".concat(prefixCls, \"-row-selected\"), selectedKeySet.has(getRowKey(record, index))), mergedRowClassName);\n  };\n  // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon;\n  // Customize expandable icon\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale);\n  // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  }\n  // Indent size\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  }\n  // ============================ Render ============================\n  var transformColumns = React.useCallback(function (innerColumns) {\n    return transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns))));\n  }, [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  var topPaginationNode;\n  var bottomPaginationNode;\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    var paginationSize;\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n    var renderPagination = function renderPagination(position) {\n      return /*#__PURE__*/React.createElement(Pagination, _extends({}, mergedPagination, {\n        className: classNames(\"\".concat(prefixCls, \"-pagination \").concat(prefixCls, \"-pagination-\").concat(position), mergedPagination.className),\n        size: paginationSize\n      }));\n    };\n    var defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    var position = mergedPagination.position;\n    if (position !== null && Array.isArray(position)) {\n      var topPos = position.find(function (p) {\n        return p.includes('top');\n      });\n      var bottomPos = position.find(function (p) {\n        return p.includes('bottom');\n      });\n      var isDisable = position.every(function (p) {\n        return \"\".concat(p) === 'none';\n      });\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  }\n  // >>>>>>>>> Spinning\n  var spinProps;\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (_typeof(loading) === 'object') {\n    spinProps = _extends({\n      spinning: true\n    }, loading);\n  }\n  var wrapperClassNames = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-wrapper-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: wrapperClassNames,\n    style: style\n  }, /*#__PURE__*/React.createElement(Spin, _extends({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(RcTable, _extends({}, tableProps, {\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-middle\"), mergedSize === 'middle'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-small\"), mergedSize === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-bordered\"), bordered), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-empty\"), rawData.length === 0), _classNames3)),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: locale && locale.emptyText || (renderEmpty || defaultRenderEmpty)('Table'),\n    // Internal\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns\n  })), bottomPaginationNode));\n}\nvar ForwardTable = /*#__PURE__*/React.forwardRef(InternalTable);\nvar Table = ForwardTable;\nTable.SELECTION_COLUMN = SELECTION_COLUMN;\nTable.EXPAND_COLUMN = RcTable.EXPAND_COLUMN;\nTable.SELECTION_ALL = SELECTION_ALL;\nTable.SELECTION_INVERT = SELECTION_INVERT;\nTable.SELECTION_NONE = SELECTION_NONE;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = Summary;\nexport default Table;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,IAAIC,OAAO,QAAQ,UAAU;AAC3C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,SAAS,IAAIC,aAAa,QAAQ,mBAAmB;AAC5D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,IAAIC,iBAAiB,EAAEC,kBAAkB,QAAQ,uBAAuB;AAC5F,OAAOC,YAAY,IAAIC,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,sBAAsB;AACtH,OAAOC,SAAS,IAAIC,WAAW,QAAQ,mBAAmB;AAC1D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,IAAIC,UAAU,GAAG,EAAE;AACnB,SAASC,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjC,IAAIC,YAAY;EAChB,IAAIC,kBAAkB,GAAGH,KAAK,CAACI,SAAS;IACtCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,aAAa,GAAGP,KAAK,CAACQ,IAAI;IAC1BC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,0BAA0B,GAAGV,KAAK,CAACW,iBAAiB;IACpDC,UAAU,GAAGZ,KAAK,CAACY,UAAU;IAC7BC,UAAU,GAAGb,KAAK,CAACa,UAAU;IAC7BC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,aAAa,GAAGf,KAAK,CAACgB,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,aAAa;IACzDE,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;IACzBC,wBAAwB,GAAGpB,KAAK,CAACqB,kBAAkB;IACnDC,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,iBAAiB,GAAGvB,KAAK,CAACuB,iBAAiB;IAC3CC,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBC,UAAU,GAAGzB,KAAK,CAACyB,UAAU;IAC7BC,UAAU,GAAG1B,KAAK,CAAC0B,UAAU;IAC7BC,iBAAiB,GAAG3B,KAAK,CAAC2B,iBAAiB;IAC3CC,qBAAqB,GAAG5B,KAAK,CAAC4B,qBAAqB;IACnDC,UAAU,GAAG7B,KAAK,CAAC6B,UAAU;IAC7BC,MAAM,GAAG9B,KAAK,CAAC8B,MAAM;IACrBC,cAAc,GAAG/B,KAAK,CAAC+B,cAAc;IACrCC,MAAM,GAAGhC,KAAK,CAACgC,MAAM;IACrBC,qBAAqB,GAAGjC,KAAK,CAACkC,iBAAiB;IAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;EACrFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,OAAO,CAAC,EAAE,OAAOoC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACsB,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,4GAA4G,CAAC,GAAG,KAAK,CAAC;EACrO,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,CAAC,EAAE,CAAC,+BAA+B,EAAE,4BAA4B,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;IACzI,IAAIC,KAAK,GAAG7E,cAAc,CAAC4E,IAAI,EAAE,CAAC,CAAC;MACjCE,cAAc,GAAGD,KAAK,CAAC,CAAC,CAAC;MACzBE,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC;IACpBN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,OAAO,CAAC,EAAE8D,cAAc,IAAI1C,KAAK,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC4C,MAAM,CAACF,cAAc,EAAE,0EAA0E,CAAC,CAACE,MAAM,CAACD,OAAO,EAAE,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC;EACtO,CAAC,CAAC;EACF,IAAIE,WAAW,GAAG1E,KAAK,CAAC2E,OAAO,CAAC,YAAY;IAC1C,OAAO5B,OAAO,IAAIlD,wBAAwB,CAACmD,QAAQ,CAAC;EACtD,CAAC,EAAE,CAACD,OAAO,EAAEC,QAAQ,CAAC,CAAC;EACvB,IAAI4B,cAAc,GAAG5E,KAAK,CAAC2E,OAAO,CAAC,YAAY;IAC7C,OAAOD,WAAW,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;MACrC,OAAOA,GAAG,CAACC,UAAU;IACvB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EACjB,IAAIM,OAAO,GAAG5E,aAAa,CAACwE,cAAc,CAAC;EAC3C,IAAIK,aAAa,GAAGjF,KAAK,CAAC2E,OAAO,CAAC,YAAY;IAC5C,IAAIO,OAAO,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAAC,UAAUC,CAAC,EAAE;MAC7D,OAAOP,OAAO,CAACO,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IACH,OAAOb,WAAW,CAACY,MAAM,CAAC,UAAUE,CAAC,EAAE;MACrC,OAAO,CAACA,CAAC,CAACT,UAAU,IAAIS,CAAC,CAACT,UAAU,CAACF,IAAI,CAAC,UAAUY,CAAC,EAAE;QACrD,OAAOP,OAAO,CAACQ,GAAG,CAACD,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACf,WAAW,EAAEM,OAAO,CAAC,CAAC;EAC1B,IAAIW,UAAU,GAAG5F,IAAI,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EAC/D,IAAIQ,IAAI,GAAGrC,KAAK,CAAC4F,UAAU,CAACzF,WAAW,CAAC;EACxC,IAAI0F,iBAAiB,GAAG7F,KAAK,CAAC4F,UAAU,CAAC3F,aAAa,CAAC;IACrD6F,qBAAqB,GAAGD,iBAAiB,CAAChC,MAAM;IAChDkC,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGzF,aAAa,GAAGyF,qBAAqB;IACxFE,WAAW,GAAGH,iBAAiB,CAACG,WAAW;IAC3CC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;EACzC,IAAIC,UAAU,GAAG9D,aAAa,IAAIC,IAAI;EACtC,IAAI8D,WAAW,GAAG3G,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuG,aAAa,CAACK,KAAK,CAAC,EAAEvC,MAAM,CAAC;EACrE,IAAIwC,OAAO,GAAG5D,UAAU,IAAId,UAAU;EACtC,IAAI2E,kBAAkB,GAAGtG,KAAK,CAAC4F,UAAU,CAAC3F,aAAa,CAAC;IACtDsG,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAChD,IAAItE,SAAS,GAAGsE,YAAY,CAAC,OAAO,EAAEvE,kBAAkB,CAAC;EACzD,IAAIQ,iBAAiB,GAAG+D,YAAY,CAAC,UAAU,EAAEhE,0BAA0B,CAAC;EAC5E,IAAIiE,gBAAgB,GAAGhH,QAAQ,CAAC;IAC9B0D,kBAAkB,EAAED,wBAAwB;IAC5CQ,qBAAqB,EAAEA;EACzB,CAAC,EAAEF,UAAU,CAAC;EACd,IAAIkD,qBAAqB,GAAGD,gBAAgB,CAACtD,kBAAkB;IAC7DA,kBAAkB,GAAGuD,qBAAqB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,qBAAqB;EAC5F,IAAIC,UAAU,GAAG1G,KAAK,CAAC2E,OAAO,CAAC,YAAY;IACzC,IAAI0B,OAAO,CAACxB,IAAI,CAAC,UAAU8B,IAAI,EAAE;MAC/B,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACzD,kBAAkB,CAAC;IAC7E,CAAC,CAAC,EAAE;MACF,OAAO,MAAM;IACf;IACA,IAAIM,iBAAiB,IAAID,UAAU,IAAIA,UAAU,CAACC,iBAAiB,EAAE;MACnE,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAAC6C,OAAO,CAAC,CAAC;EACb,IAAIO,YAAY,GAAG;IACjBC,IAAI,EAAE7G,KAAK,CAAC8G,MAAM,CAAC;EACrB,CAAC;EACD;EACA,IAAIC,SAAS,GAAG/G,KAAK,CAAC2E,OAAO,CAAC,YAAY;IACxC,IAAI,OAAO9B,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IACA,OAAO,UAAUmE,MAAM,EAAE;MACvB,OAAOA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnE,MAAM,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAIoE,aAAa,GAAGlG,YAAY,CAACsF,OAAO,EAAEnD,kBAAkB,EAAE6D,SAAS,CAAC;IACtEG,cAAc,GAAGzH,cAAc,CAACwH,aAAa,EAAE,CAAC,CAAC;IACjDE,cAAc,GAAGD,cAAc,CAAC,CAAC,CAAC;EACpC;EACA,IAAIE,eAAe,GAAG,CAAC,CAAC;EACxB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC3D,IAAIC,KAAK,GAAGC,SAAS,CAACtD,MAAM,GAAG,CAAC,IAAIsD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACrF,IAAIE,UAAU,GAAGnI,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4H,eAAe,CAAC,EAAEE,IAAI,CAAC;IAC9D,IAAIE,KAAK,EAAE;MACTJ,eAAe,CAACQ,eAAe,CAAC,CAAC;MACjC;MACA,IAAID,UAAU,CAACjF,UAAU,CAACmF,OAAO,EAAE;QACjCF,UAAU,CAACjF,UAAU,CAACmF,OAAO,GAAG,CAAC;MACnC;MACA;MACA,IAAInF,UAAU,IAAIA,UAAU,CAACS,QAAQ,EAAE;QACrCT,UAAU,CAACS,QAAQ,CAAC,CAAC,EAAEwE,UAAU,CAACjF,UAAU,CAACoF,QAAQ,CAAC;MACxD;IACF;IACA,IAAInE,MAAM,IAAIA,MAAM,CAACoE,wBAAwB,KAAK,KAAK,IAAInB,YAAY,CAACC,IAAI,CAACgB,OAAO,EAAE;MACpFrH,QAAQ,CAAC,CAAC,EAAE;QACVwH,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;UACpC,OAAOpB,YAAY,CAACC,IAAI,CAACgB,OAAO;QAClC;MACF,CAAC,CAAC;IACJ;IACA1E,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwE,UAAU,CAACjF,UAAU,EAAEiF,UAAU,CAACM,OAAO,EAAEN,UAAU,CAACO,MAAM,EAAE;MACzHC,iBAAiB,EAAErH,aAAa,CAACW,WAAW,CAAC4E,OAAO,EAAEsB,UAAU,CAACS,YAAY,EAAElF,kBAAkB,CAAC,EAAEyE,UAAU,CAACU,YAAY,CAAC;MAC5Hd,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE;EACA,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACJ,MAAM,EAAEE,YAAY,EAAE;IACjEf,eAAe,CAAC;MACda,MAAM,EAAEA,MAAM;MACdE,YAAY,EAAEA;IAChB,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;EACnB,CAAC;EACD,IAAIG,UAAU,GAAG/G,SAAS,CAAC;MACvBS,SAAS,EAAEA,SAAS;MACpBgD,aAAa,EAAEA,aAAa;MAC5BqD,cAAc,EAAEA,cAAc;MAC9B1E,cAAc,EAAEA,cAAc,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;MACvDuC,WAAW,EAAEA,WAAW;MACxBpC,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACFyE,WAAW,GAAG/I,cAAc,CAAC8I,UAAU,EAAE,CAAC,CAAC;IAC3CE,sBAAsB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACvCE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC3BG,gBAAgB,GAAGH,WAAW,CAAC,CAAC,CAAC;IACjCI,UAAU,GAAGJ,WAAW,CAAC,CAAC,CAAC;EAC7B,IAAIK,UAAU,GAAG7I,KAAK,CAAC2E,OAAO,CAAC,YAAY;IACzC,OAAOlD,WAAW,CAAC4E,OAAO,EAAEqC,UAAU,EAAExF,kBAAkB,CAAC;EAC7D,CAAC,EAAE,CAACmD,OAAO,EAAEqC,UAAU,CAAC,CAAC;EACzBtB,eAAe,CAACc,MAAM,GAAGU,UAAU,CAAC,CAAC;EACrCxB,eAAe,CAACgB,YAAY,GAAGM,UAAU;EACzC;EACA,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACb,OAAO,EAAEI,YAAY,EAAE;IAClEhB,eAAe,CAAC;MACdY,OAAO,EAAEA,OAAO;MAChBI,YAAY,EAAEA;IAChB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;EACpB,CAAC;EACD,IAAIU,UAAU,GAAGlI,SAAS,CAAC;MACvBoB,SAAS,EAAEA,SAAS;MACpB4B,MAAM,EAAEsC,WAAW;MACnB3D,iBAAiB,EAAEA,iBAAiB;MACpCyC,aAAa,EAAEA,aAAa;MAC5B6D,cAAc,EAAEA,cAAc;MAC9B1F,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACF4F,WAAW,GAAGvJ,cAAc,CAACsJ,UAAU,EAAE,CAAC,CAAC;IAC3CE,sBAAsB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACvCX,YAAY,GAAGW,WAAW,CAAC,CAAC,CAAC;IAC7Bf,OAAO,GAAGe,WAAW,CAAC,CAAC,CAAC;EAC1B,IAAIE,UAAU,GAAGpI,aAAa,CAAC+H,UAAU,EAAER,YAAY,CAAC;EACxDjB,eAAe,CAACa,OAAO,GAAGA,OAAO;EACjCb,eAAe,CAACiB,YAAY,GAAGA,YAAY;EAC3C;EACA,IAAIc,gBAAgB,GAAGnJ,KAAK,CAAC2E,OAAO,CAAC,YAAY;IAC/C,IAAIyE,aAAa,GAAG,CAAC,CAAC;IACtBhE,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAAC7D,OAAO,CAAC,UAAUiF,SAAS,EAAE;MAChD,IAAIpB,OAAO,CAACoB,SAAS,CAAC,KAAK,IAAI,EAAE;QAC/BD,aAAa,CAACC,SAAS,CAAC,GAAGpB,OAAO,CAACoB,SAAS,CAAC;MAC/C;IACF,CAAC,CAAC;IACF,OAAO7J,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmJ,gBAAgB,CAAC,EAAE;MAC9CV,OAAO,EAAEmB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,gBAAgB,EAAEV,OAAO,CAAC,CAAC;EAC/B,IAAIqB,gBAAgB,GAAG5H,eAAe,CAACyH,gBAAgB,CAAC;IACtDI,iBAAiB,GAAG9J,cAAc,CAAC6J,gBAAgB,EAAE,CAAC,CAAC;IACvDE,qBAAqB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EAC9C;EACA,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC5B,OAAO,EAAEC,QAAQ,EAAE;IACtET,eAAe,CAAC;MACd3E,UAAU,EAAElD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4H,eAAe,CAAC1E,UAAU,CAAC,EAAE;QAC7DmF,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,EAAE,UAAU,CAAC;EAChB,CAAC;EACD,IAAI4B,cAAc,GAAG1I,aAAa,CAACkI,UAAU,CAAC/E,MAAM,EAAEzB,UAAU,EAAE+G,kBAAkB,CAAC;IACnFE,eAAe,GAAGlK,cAAc,CAACiK,cAAc,EAAE,CAAC,CAAC;IACnDE,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACrC/B,eAAe,GAAG+B,eAAe,CAAC,CAAC,CAAC;EACtCvC,eAAe,CAAC1E,UAAU,GAAGA,UAAU,KAAK,KAAK,GAAG,CAAC,CAAC,GAAGxB,kBAAkB,CAACwB,UAAU,EAAEkH,gBAAgB,CAAC;EACzGxC,eAAe,CAACQ,eAAe,GAAGA,eAAe;EACjD;EACA,IAAIiC,QAAQ,GAAG7J,KAAK,CAAC2E,OAAO,CAAC,YAAY;IACvC,IAAIjC,UAAU,KAAK,KAAK,IAAI,CAACkH,gBAAgB,CAAC9B,QAAQ,EAAE;MACtD,OAAOoB,UAAU;IACnB;IACA,IAAIY,qBAAqB,GAAGF,gBAAgB,CAAC/B,OAAO;MAClDA,OAAO,GAAGiC,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;MACtEC,KAAK,GAAGH,gBAAgB,CAACG,KAAK;MAC9BC,qBAAqB,GAAGJ,gBAAgB,CAAC9B,QAAQ;MACjDA,QAAQ,GAAGkC,qBAAqB,KAAK,KAAK,CAAC,GAAG/I,iBAAiB,GAAG+I,qBAAqB;IACzFhG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,OAAO,CAACoH,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE,sCAAsC,CAAC,GAAG,KAAK,CAAC;IACtH;IACA,IAAIqB,UAAU,CAAC/E,MAAM,GAAG4F,KAAK,EAAE;MAC7B,IAAIb,UAAU,CAAC/E,MAAM,GAAG2D,QAAQ,EAAE;QAChC9D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,sJAAsJ,CAAC,GAAG,KAAK,CAAC;QAChO,OAAOyI,UAAU,CAACe,KAAK,CAAC,CAACpC,OAAO,GAAG,CAAC,IAAIC,QAAQ,EAAED,OAAO,GAAGC,QAAQ,CAAC;MACvE;MACA,OAAOoB,UAAU;IACnB;IACA,OAAOA,UAAU,CAACe,KAAK,CAAC,CAACpC,OAAO,GAAG,CAAC,IAAIC,QAAQ,EAAED,OAAO,GAAGC,QAAQ,CAAC;EACvE,CAAC,EAAE,CAAC,CAAC,CAACpF,UAAU,EAAEwG,UAAU,EAAEU,gBAAgB,IAAIA,gBAAgB,CAAC/B,OAAO,EAAE+B,gBAAgB,IAAIA,gBAAgB,CAAC9B,QAAQ,EAAE8B,gBAAgB,IAAIA,gBAAgB,CAACG,KAAK,CAAC,CAAC;EACvK;EACA,IAAIG,aAAa,GAAG/I,YAAY,CAACwB,YAAY,EAAE;MAC3CV,SAAS,EAAEA,SAAS;MACpBkI,IAAI,EAAEjB,UAAU;MAChBW,QAAQ,EAAEA,QAAQ;MAClB9C,SAAS,EAAEA,SAAS;MACpBI,cAAc,EAAEA,cAAc;MAC9BT,UAAU,EAAEA,UAAU;MACtBxD,kBAAkB,EAAEA,kBAAkB;MACtCW,MAAM,EAAEsC,WAAW;MACnB/C,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACFgH,cAAc,GAAG3K,cAAc,CAACyK,aAAa,EAAE,CAAC,CAAC;IACjDG,yBAAyB,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7CE,cAAc,GAAGF,cAAc,CAAC,CAAC,CAAC;EACpC,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACvD,MAAM,EAAEwD,KAAK,EAAEC,MAAM,EAAE;IAC9E,IAAIC,kBAAkB;IACtB,IAAI,OAAO5H,YAAY,KAAK,UAAU,EAAE;MACtC4H,kBAAkB,GAAGhL,UAAU,CAACoD,YAAY,CAACkE,MAAM,EAAEwD,KAAK,EAAEC,MAAM,CAAC,CAAC;IACtE,CAAC,MAAM;MACLC,kBAAkB,GAAGhL,UAAU,CAACoD,YAAY,CAAC;IAC/C;IACA,OAAOpD,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkF,MAAM,CAACxC,SAAS,EAAE,eAAe,CAAC,EAAEqI,cAAc,CAAC5E,GAAG,CAACqB,SAAS,CAACC,MAAM,EAAEwD,KAAK,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC;EACjJ,CAAC;EACD;EACA;EACAlE,gBAAgB,CAACmE,sBAAsB,GAAGnE,gBAAgB,CAAClD,UAAU;EACrE;EACAkD,gBAAgB,CAAClD,UAAU,GAAGkD,gBAAgB,CAAClD,UAAU,IAAIA,UAAU,IAAI1C,gBAAgB,CAACuF,WAAW,CAAC;EACxG;EACA,IAAIO,UAAU,KAAK,MAAM,IAAIF,gBAAgB,CAAC/C,qBAAqB,KAAKiE,SAAS,EAAE;IACjFlB,gBAAgB,CAAC/C,qBAAqB,GAAGd,YAAY,GAAG,CAAC,GAAG,CAAC;EAC/D,CAAC,MAAM,IAAI6D,gBAAgB,CAAC/C,qBAAqB,GAAG,CAAC,IAAId,YAAY,EAAE;IACrE6D,gBAAgB,CAAC/C,qBAAqB,IAAI,CAAC;EAC7C;EACA;EACA,IAAI,OAAO+C,gBAAgB,CAAC9C,UAAU,KAAK,QAAQ,EAAE;IACnD8C,gBAAgB,CAAC9C,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE;EAChF;EACA;EACA,IAAIkH,gBAAgB,GAAG5K,KAAK,CAAC6K,WAAW,CAAC,UAAUC,YAAY,EAAE;IAC/D,OAAOtB,qBAAqB,CAACa,yBAAyB,CAACpB,sBAAsB,CAACR,sBAAsB,CAACqC,YAAY,CAAC,CAAC,CAAC,CAAC;EACvH,CAAC,EAAE,CAACrC,sBAAsB,EAAEQ,sBAAsB,EAAEoB,yBAAyB,CAAC,CAAC;EAC/E,IAAIU,iBAAiB;EACrB,IAAIC,oBAAoB;EACxB,IAAItI,UAAU,KAAK,KAAK,KAAKkH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG,KAAK,CAAC,EAAE;IACxH,IAAIkB,cAAc;IAClB,IAAIrB,gBAAgB,CAACvH,IAAI,EAAE;MACzB4I,cAAc,GAAGrB,gBAAgB,CAACvH,IAAI;IACxC,CAAC,MAAM;MACL4I,cAAc,GAAG/E,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,QAAQ,GAAG,OAAO,GAAGwB,SAAS;IAC1F;IACA,IAAIwD,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,QAAQ,EAAE;MACzD,OAAO,aAAanL,KAAK,CAACoL,aAAa,CAAC9K,UAAU,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAEoK,gBAAgB,EAAE;QACjF1H,SAAS,EAAExC,UAAU,CAAC,EAAE,CAAC+E,MAAM,CAACxC,SAAS,EAAE,cAAc,CAAC,CAACwC,MAAM,CAACxC,SAAS,EAAE,cAAc,CAAC,CAACwC,MAAM,CAAC0G,QAAQ,CAAC,EAAEvB,gBAAgB,CAAC1H,SAAS,CAAC;QAC1IG,IAAI,EAAE4I;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IACD,IAAII,eAAe,GAAGpF,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5D,IAAIkF,QAAQ,GAAGvB,gBAAgB,CAACuB,QAAQ;IACxC,IAAIA,QAAQ,KAAK,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;MAChD,IAAIK,MAAM,GAAGL,QAAQ,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAE;QACtC,OAAOA,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;MAC1B,CAAC,CAAC;MACF,IAAIC,SAAS,GAAGT,QAAQ,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAE;QACzC,OAAOA,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC;MAC7B,CAAC,CAAC;MACF,IAAIE,SAAS,GAAGV,QAAQ,CAACW,KAAK,CAAC,UAAUJ,CAAC,EAAE;QAC1C,OAAO,EAAE,CAACjH,MAAM,CAACiH,CAAC,CAAC,KAAK,MAAM;MAChC,CAAC,CAAC;MACF,IAAI,CAACF,MAAM,IAAI,CAACI,SAAS,IAAI,CAACC,SAAS,EAAE;QACvCb,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;MAC1D;MACA,IAAIG,MAAM,EAAE;QACVT,iBAAiB,GAAGG,gBAAgB,CAACM,MAAM,CAACO,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/E;MACA,IAAIJ,SAAS,EAAE;QACbZ,oBAAoB,GAAGE,gBAAgB,CAACU,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACxF;IACF,CAAC,MAAM;MACLhB,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;IAC1D;EACF;EACA;EACA,IAAIY,SAAS;EACb,IAAI,OAAO5I,OAAO,KAAK,SAAS,EAAE;IAChC4I,SAAS,GAAG;MACVC,QAAQ,EAAE7I;IACZ,CAAC;EACH,CAAC,MAAM,IAAI/D,OAAO,CAAC+D,OAAO,CAAC,KAAK,QAAQ,EAAE;IACxC4I,SAAS,GAAGzM,QAAQ,CAAC;MACnB0M,QAAQ,EAAE;IACZ,CAAC,EAAE7I,OAAO,CAAC;EACb;EACA,IAAI8I,iBAAiB,GAAGzM,UAAU,CAAC,EAAE,CAAC+E,MAAM,CAACxC,SAAS,EAAE,UAAU,CAAC,EAAE1C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkF,MAAM,CAACxC,SAAS,EAAE,cAAc,CAAC,EAAEgE,SAAS,KAAK,KAAK,CAAC,EAAE/D,SAAS,CAAC;EAC/J,OAAO,aAAalC,KAAK,CAACoL,aAAa,CAAC,KAAK,EAAE;IAC7CtJ,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEiK,iBAAiB;IAC5BhK,KAAK,EAAEA;EACT,CAAC,EAAE,aAAanC,KAAK,CAACoL,aAAa,CAAC7K,IAAI,EAAEf,QAAQ,CAAC;IACjD0M,QAAQ,EAAE;EACZ,CAAC,EAAED,SAAS,CAAC,EAAElB,iBAAiB,EAAE,aAAa/K,KAAK,CAACoL,aAAa,CAACzL,OAAO,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEmG,UAAU,EAAE;IACnG5C,OAAO,EAAEkC,aAAa;IACtBgB,SAAS,EAAEA,SAAS;IACpB1C,UAAU,EAAEiD,gBAAgB;IAC5BvE,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAExC,UAAU,EAAEqC,YAAY,GAAG,CAAC,CAAC,EAAExC,eAAe,CAACwC,YAAY,EAAE,EAAE,CAAC0C,MAAM,CAACxC,SAAS,EAAE,SAAS,CAAC,EAAEiE,UAAU,KAAK,QAAQ,CAAC,EAAE3G,eAAe,CAACwC,YAAY,EAAE,EAAE,CAAC0C,MAAM,CAACxC,SAAS,EAAE,QAAQ,CAAC,EAAEiE,UAAU,KAAK,OAAO,CAAC,EAAE3G,eAAe,CAACwC,YAAY,EAAE,EAAE,CAAC0C,MAAM,CAACxC,SAAS,EAAE,WAAW,CAAC,EAAEK,QAAQ,CAAC,EAAE/C,eAAe,CAACwC,YAAY,EAAE,EAAE,CAAC0C,MAAM,CAACxC,SAAS,EAAE,QAAQ,CAAC,EAAEoE,OAAO,CAAClC,MAAM,KAAK,CAAC,CAAC,EAAEpC,YAAY,CAAC,CAAC;IACzYoI,IAAI,EAAEN,QAAQ;IACdhH,MAAM,EAAEkE,SAAS;IACjBjE,YAAY,EAAEyH,oBAAoB;IAClC6B,SAAS,EAAEvI,MAAM,IAAIA,MAAM,CAACuI,SAAS,IAAI,CAACpG,WAAW,IAAI9F,kBAAkB,EAAE,OAAO,CAAC;IACrF;IACAmM,aAAa,EAAEvM,cAAc;IAC7B8G,YAAY,EAAEA,YAAY;IAC1BgE,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,EAAEI,oBAAoB,CAAC,CAAC;AAC7B;AACA,IAAIsB,YAAY,GAAG,aAAatM,KAAK,CAACuM,UAAU,CAAC3K,aAAa,CAAC;AAC/D,IAAIwE,KAAK,GAAGkG,YAAY;AACxBlG,KAAK,CAAC/E,gBAAgB,GAAGA,gBAAgB;AACzC+E,KAAK,CAACoG,aAAa,GAAG7M,OAAO,CAAC6M,aAAa;AAC3CpG,KAAK,CAAChF,aAAa,GAAGA,aAAa;AACnCgF,KAAK,CAAC9E,gBAAgB,GAAGA,gBAAgB;AACzC8E,KAAK,CAAC7E,cAAc,GAAGA,cAAc;AACrC6E,KAAK,CAAC1F,MAAM,GAAGA,MAAM;AACrB0F,KAAK,CAACzF,WAAW,GAAGA,WAAW;AAC/ByF,KAAK,CAACxG,OAAO,GAAGA,OAAO;AACvB,eAAewG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}