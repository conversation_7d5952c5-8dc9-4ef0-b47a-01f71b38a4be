{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport canUseDom from \"./canUseDom\";\nimport contains from \"./contains\";\nvar APPEND_ORDER = 'data-rc-order';\nvar APPEND_PRIORITY = 'data-rc-priority';\nvar MARK_KEY = \"rc-util-key\";\nvar containerCache = new Map();\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    mark = _ref.mark;\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  var head = document.querySelector('head');\n  return head || document.body;\n}\nfunction getOrder(prepend) {\n  if (prepend === 'queue') {\n    return 'prependQueue';\n  }\n  return prepend ? 'prepend' : 'append';\n}\n\n/**\n * Find style which inject by rc-util\n */\nfunction findStyles(container) {\n  return Array.from((containerCache.get(container) || container).children).filter(function (node) {\n    return node.tagName === 'STYLE';\n  });\n}\nexport function injectCSS(css) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!canUseDom()) {\n    return null;\n  }\n  var csp = option.csp,\n    prepend = option.prepend,\n    _option$priority = option.priority,\n    priority = _option$priority === void 0 ? 0 : _option$priority;\n  var mergedOrder = getOrder(prepend);\n  var isPrependQueue = mergedOrder === 'prependQueue';\n  var styleNode = document.createElement('style');\n  styleNode.setAttribute(APPEND_ORDER, mergedOrder);\n  if (isPrependQueue && priority) {\n    styleNode.setAttribute(APPEND_PRIORITY, \"\".concat(priority));\n  }\n  if (csp !== null && csp !== void 0 && csp.nonce) {\n    styleNode.nonce = csp === null || csp === void 0 ? void 0 : csp.nonce;\n  }\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n  if (prepend) {\n    // If is queue `prepend`, it will prepend first style and then append rest style\n    if (isPrependQueue) {\n      var existStyle = (option.styles || findStyles(container)).filter(function (node) {\n        // Ignore style which not injected by rc-util with prepend\n        if (!['prepend', 'prependQueue'].includes(node.getAttribute(APPEND_ORDER))) {\n          return false;\n        }\n\n        // Ignore style which priority less then new style\n        var nodePriority = Number(node.getAttribute(APPEND_PRIORITY) || 0);\n        return priority >= nodePriority;\n      });\n      if (existStyle.length) {\n        container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);\n        return styleNode;\n      }\n    }\n\n    // Use `insertBefore` as `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return (option.styles || findStyles(container)).find(function (node) {\n    return node.getAttribute(getMark(option)) === key;\n  });\n}\nexport function removeCSS(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var container = getContainer(option);\n    container.removeChild(existNode);\n  }\n}\n\n/**\n * qiankun will inject `appendChild` to insert into other\n */\nfunction syncRealContainer(container, option) {\n  var cachedRealContainer = containerCache.get(container);\n\n  // Find real container when not cached or cached container removed\n  if (!cachedRealContainer || !contains(document, cachedRealContainer)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    container.removeChild(placeholderStyle);\n  }\n}\n\n/**\n * manually clear container cache to avoid global cache in unit testes\n */\nexport function clearContainerCache() {\n  containerCache.clear();\n}\nexport function updateCSS(css, key) {\n  var originOption = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(originOption);\n  var styles = findStyles(container);\n  var option = _objectSpread(_objectSpread({}, originOption), {}, {\n    styles: styles\n  });\n\n  // Sync real parent\n  syncRealContainer(container, option);\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var _option$csp, _option$csp2;\n    if ((_option$csp = option.csp) !== null && _option$csp !== void 0 && _option$csp.nonce && existNode.nonce !== ((_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce)) {\n      var _option$csp3;\n      existNode.nonce = (_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}", "map": {"version": 3, "names": ["_objectSpread", "canUseDom", "contains", "APPEND_ORDER", "APPEND_PRIORITY", "MARK_KEY", "containerCache", "Map", "getMark", "_ref", "arguments", "length", "undefined", "mark", "startsWith", "concat", "getContainer", "option", "attachTo", "head", "document", "querySelector", "body", "getOrder", "prepend", "findStyles", "container", "Array", "from", "get", "children", "filter", "node", "tagName", "injectCSS", "css", "csp", "_option$priority", "priority", "mergedOrder", "isPrependQueue", "styleNode", "createElement", "setAttribute", "nonce", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "existStyle", "styles", "includes", "getAttribute", "nodePriority", "Number", "insertBefore", "nextS<PERSON>ling", "append<PERSON><PERSON><PERSON>", "findExistNode", "key", "find", "removeCSS", "existNode", "<PERSON><PERSON><PERSON><PERSON>", "syncRealContainer", "cachedRealContainer", "placeholder<PERSON><PERSON><PERSON>", "parentNode", "set", "clearContainerCache", "clear", "updateCSS", "originOption", "_option$csp", "_option$csp2", "_option$csp3", "newNode"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/Dom/dynamicCSS.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport canUseDom from \"./canUseDom\";\nimport contains from \"./contains\";\nvar APPEND_ORDER = 'data-rc-order';\nvar APPEND_PRIORITY = 'data-rc-priority';\nvar MARK_KEY = \"rc-util-key\";\nvar containerCache = new Map();\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    mark = _ref.mark;\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  var head = document.querySelector('head');\n  return head || document.body;\n}\nfunction getOrder(prepend) {\n  if (prepend === 'queue') {\n    return 'prependQueue';\n  }\n  return prepend ? 'prepend' : 'append';\n}\n\n/**\n * Find style which inject by rc-util\n */\nfunction findStyles(container) {\n  return Array.from((containerCache.get(container) || container).children).filter(function (node) {\n    return node.tagName === 'STYLE';\n  });\n}\nexport function injectCSS(css) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!canUseDom()) {\n    return null;\n  }\n  var csp = option.csp,\n    prepend = option.prepend,\n    _option$priority = option.priority,\n    priority = _option$priority === void 0 ? 0 : _option$priority;\n  var mergedOrder = getOrder(prepend);\n  var isPrependQueue = mergedOrder === 'prependQueue';\n  var styleNode = document.createElement('style');\n  styleNode.setAttribute(APPEND_ORDER, mergedOrder);\n  if (isPrependQueue && priority) {\n    styleNode.setAttribute(APPEND_PRIORITY, \"\".concat(priority));\n  }\n  if (csp !== null && csp !== void 0 && csp.nonce) {\n    styleNode.nonce = csp === null || csp === void 0 ? void 0 : csp.nonce;\n  }\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n  if (prepend) {\n    // If is queue `prepend`, it will prepend first style and then append rest style\n    if (isPrependQueue) {\n      var existStyle = (option.styles || findStyles(container)).filter(function (node) {\n        // Ignore style which not injected by rc-util with prepend\n        if (!['prepend', 'prependQueue'].includes(node.getAttribute(APPEND_ORDER))) {\n          return false;\n        }\n\n        // Ignore style which priority less then new style\n        var nodePriority = Number(node.getAttribute(APPEND_PRIORITY) || 0);\n        return priority >= nodePriority;\n      });\n      if (existStyle.length) {\n        container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);\n        return styleNode;\n      }\n    }\n\n    // Use `insertBefore` as `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return (option.styles || findStyles(container)).find(function (node) {\n    return node.getAttribute(getMark(option)) === key;\n  });\n}\nexport function removeCSS(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var container = getContainer(option);\n    container.removeChild(existNode);\n  }\n}\n\n/**\n * qiankun will inject `appendChild` to insert into other\n */\nfunction syncRealContainer(container, option) {\n  var cachedRealContainer = containerCache.get(container);\n\n  // Find real container when not cached or cached container removed\n  if (!cachedRealContainer || !contains(document, cachedRealContainer)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    container.removeChild(placeholderStyle);\n  }\n}\n\n/**\n * manually clear container cache to avoid global cache in unit testes\n */\nexport function clearContainerCache() {\n  containerCache.clear();\n}\nexport function updateCSS(css, key) {\n  var originOption = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(originOption);\n  var styles = findStyles(container);\n  var option = _objectSpread(_objectSpread({}, originOption), {}, {\n    styles: styles\n  });\n\n  // Sync real parent\n  syncRealContainer(container, option);\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var _option$csp, _option$csp2;\n    if ((_option$csp = option.csp) !== null && _option$csp !== void 0 && _option$csp.nonce && existNode.nonce !== ((_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce)) {\n      var _option$csp3;\n      existNode.nonce = (_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,IAAIC,YAAY,GAAG,eAAe;AAClC,IAAIC,eAAe,GAAG,kBAAkB;AACxC,IAAIC,QAAQ,GAAG,aAAa;AAC5B,IAAIC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC9B,SAASC,OAAOA,CAAA,EAAG;EACjB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/EG,IAAI,GAAGJ,IAAI,CAACI,IAAI;EAClB,IAAIA,IAAI,EAAE;IACR,OAAOA,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC,GAAGD,IAAI,GAAG,OAAO,CAACE,MAAM,CAACF,IAAI,CAAC;EAC/D;EACA,OAAOR,QAAQ;AACjB;AACA,SAASW,YAAYA,CAACC,MAAM,EAAE;EAC5B,IAAIA,MAAM,CAACC,QAAQ,EAAE;IACnB,OAAOD,MAAM,CAACC,QAAQ;EACxB;EACA,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EACzC,OAAOF,IAAI,IAAIC,QAAQ,CAACE,IAAI;AAC9B;AACA,SAASC,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIA,OAAO,KAAK,OAAO,EAAE;IACvB,OAAO,cAAc;EACvB;EACA,OAAOA,OAAO,GAAG,SAAS,GAAG,QAAQ;AACvC;;AAEA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC7B,OAAOC,KAAK,CAACC,IAAI,CAAC,CAACtB,cAAc,CAACuB,GAAG,CAACH,SAAS,CAAC,IAAIA,SAAS,EAAEI,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC9F,OAAOA,IAAI,CAACC,OAAO,KAAK,OAAO;EACjC,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,SAASA,CAACC,GAAG,EAAE;EAC7B,IAAIlB,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAI,CAACT,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAImC,GAAG,GAAGnB,MAAM,CAACmB,GAAG;IAClBZ,OAAO,GAAGP,MAAM,CAACO,OAAO;IACxBa,gBAAgB,GAAGpB,MAAM,CAACqB,QAAQ;IAClCA,QAAQ,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;EAC/D,IAAIE,WAAW,GAAGhB,QAAQ,CAACC,OAAO,CAAC;EACnC,IAAIgB,cAAc,GAAGD,WAAW,KAAK,cAAc;EACnD,IAAIE,SAAS,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,OAAO,CAAC;EAC/CD,SAAS,CAACE,YAAY,CAACxC,YAAY,EAAEoC,WAAW,CAAC;EACjD,IAAIC,cAAc,IAAIF,QAAQ,EAAE;IAC9BG,SAAS,CAACE,YAAY,CAACvC,eAAe,EAAE,EAAE,CAACW,MAAM,CAACuB,QAAQ,CAAC,CAAC;EAC9D;EACA,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,IAAIA,GAAG,CAACQ,KAAK,EAAE;IAC/CH,SAAS,CAACG,KAAK,GAAGR,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACQ,KAAK;EACvE;EACAH,SAAS,CAACI,SAAS,GAAGV,GAAG;EACzB,IAAIT,SAAS,GAAGV,YAAY,CAACC,MAAM,CAAC;EACpC,IAAI6B,UAAU,GAAGpB,SAAS,CAACoB,UAAU;EACrC,IAAItB,OAAO,EAAE;IACX;IACA,IAAIgB,cAAc,EAAE;MAClB,IAAIO,UAAU,GAAG,CAAC9B,MAAM,CAAC+B,MAAM,IAAIvB,UAAU,CAACC,SAAS,CAAC,EAAEK,MAAM,CAAC,UAAUC,IAAI,EAAE;QAC/E;QACA,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAACiB,QAAQ,CAACjB,IAAI,CAACkB,YAAY,CAAC/C,YAAY,CAAC,CAAC,EAAE;UAC1E,OAAO,KAAK;QACd;;QAEA;QACA,IAAIgD,YAAY,GAAGC,MAAM,CAACpB,IAAI,CAACkB,YAAY,CAAC9C,eAAe,CAAC,IAAI,CAAC,CAAC;QAClE,OAAOkC,QAAQ,IAAIa,YAAY;MACjC,CAAC,CAAC;MACF,IAAIJ,UAAU,CAACpC,MAAM,EAAE;QACrBe,SAAS,CAAC2B,YAAY,CAACZ,SAAS,EAAEM,UAAU,CAACA,UAAU,CAACpC,MAAM,GAAG,CAAC,CAAC,CAAC2C,WAAW,CAAC;QAChF,OAAOb,SAAS;MAClB;IACF;;IAEA;IACAf,SAAS,CAAC2B,YAAY,CAACZ,SAAS,EAAEK,UAAU,CAAC;EAC/C,CAAC,MAAM;IACLpB,SAAS,CAAC6B,WAAW,CAACd,SAAS,CAAC;EAClC;EACA,OAAOA,SAAS;AAClB;AACA,SAASe,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAIxC,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIgB,SAAS,GAAGV,YAAY,CAACC,MAAM,CAAC;EACpC,OAAO,CAACA,MAAM,CAAC+B,MAAM,IAAIvB,UAAU,CAACC,SAAS,CAAC,EAAEgC,IAAI,CAAC,UAAU1B,IAAI,EAAE;IACnE,OAAOA,IAAI,CAACkB,YAAY,CAAC1C,OAAO,CAACS,MAAM,CAAC,CAAC,KAAKwC,GAAG;EACnD,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,SAASA,CAACF,GAAG,EAAE;EAC7B,IAAIxC,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIkD,SAAS,GAAGJ,aAAa,CAACC,GAAG,EAAExC,MAAM,CAAC;EAC1C,IAAI2C,SAAS,EAAE;IACb,IAAIlC,SAAS,GAAGV,YAAY,CAACC,MAAM,CAAC;IACpCS,SAAS,CAACmC,WAAW,CAACD,SAAS,CAAC;EAClC;AACF;;AAEA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACpC,SAAS,EAAET,MAAM,EAAE;EAC5C,IAAI8C,mBAAmB,GAAGzD,cAAc,CAACuB,GAAG,CAACH,SAAS,CAAC;;EAEvD;EACA,IAAI,CAACqC,mBAAmB,IAAI,CAAC7D,QAAQ,CAACkB,QAAQ,EAAE2C,mBAAmB,CAAC,EAAE;IACpE,IAAIC,gBAAgB,GAAG9B,SAAS,CAAC,EAAE,EAAEjB,MAAM,CAAC;IAC5C,IAAIgD,UAAU,GAAGD,gBAAgB,CAACC,UAAU;IAC5C3D,cAAc,CAAC4D,GAAG,CAACxC,SAAS,EAAEuC,UAAU,CAAC;IACzCvC,SAAS,CAACmC,WAAW,CAACG,gBAAgB,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASG,mBAAmBA,CAAA,EAAG;EACpC7D,cAAc,CAAC8D,KAAK,CAAC,CAAC;AACxB;AACA,OAAO,SAASC,SAASA,CAAClC,GAAG,EAAEsB,GAAG,EAAE;EAClC,IAAIa,YAAY,GAAG5D,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACzF,IAAIgB,SAAS,GAAGV,YAAY,CAACsD,YAAY,CAAC;EAC1C,IAAItB,MAAM,GAAGvB,UAAU,CAACC,SAAS,CAAC;EAClC,IAAIT,MAAM,GAAGjB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;IAC9DtB,MAAM,EAAEA;EACV,CAAC,CAAC;;EAEF;EACAc,iBAAiB,CAACpC,SAAS,EAAET,MAAM,CAAC;EACpC,IAAI2C,SAAS,GAAGJ,aAAa,CAACC,GAAG,EAAExC,MAAM,CAAC;EAC1C,IAAI2C,SAAS,EAAE;IACb,IAAIW,WAAW,EAAEC,YAAY;IAC7B,IAAI,CAACD,WAAW,GAAGtD,MAAM,CAACmB,GAAG,MAAM,IAAI,IAAImC,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC3B,KAAK,IAAIgB,SAAS,CAAChB,KAAK,MAAM,CAAC4B,YAAY,GAAGvD,MAAM,CAACmB,GAAG,MAAM,IAAI,IAAIoC,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC5B,KAAK,CAAC,EAAE;MAC7M,IAAI6B,YAAY;MAChBb,SAAS,CAAChB,KAAK,GAAG,CAAC6B,YAAY,GAAGxD,MAAM,CAACmB,GAAG,MAAM,IAAI,IAAIqC,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC7B,KAAK;IACjH;IACA,IAAIgB,SAAS,CAACf,SAAS,KAAKV,GAAG,EAAE;MAC/ByB,SAAS,CAACf,SAAS,GAAGV,GAAG;IAC3B;IACA,OAAOyB,SAAS;EAClB;EACA,IAAIc,OAAO,GAAGxC,SAAS,CAACC,GAAG,EAAElB,MAAM,CAAC;EACpCyD,OAAO,CAAC/B,YAAY,CAACnC,OAAO,CAACS,MAAM,CAAC,EAAEwC,GAAG,CAAC;EAC1C,OAAOiB,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}