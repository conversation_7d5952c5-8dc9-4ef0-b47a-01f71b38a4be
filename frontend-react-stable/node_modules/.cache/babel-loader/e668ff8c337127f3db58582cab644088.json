{"ast": null, "code": "/* eslint-disable import/prefer-default-export */\nimport React from 'react';\nexport function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? \"\".concat(pos, \"-\").concat(index) : \"\".concat(index);\n}\n/**\n * Get first text content in Element\n *\n * @param node\n * @returns\n */\nfunction getElementFirstTextContent(node) {\n  var _a, _b, _c;\n  if (!node || !node.props || !node.props.children) return '';\n  if (typeof node.props.children === 'string') return node.props.children;\n  return ((_c = (_b = (_a = node.props.children) === null || _a === void 0 ? void 0 : _a.map) === null || _b === void 0 ? void 0 : _b.call(_a, function (item) {\n    return getElementFirstTextContent(item);\n  })) === null || _c === void 0 ? void 0 : _c[0]) || '';\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  // fix: #38155\n  if (/*#__PURE__*/React.isValidElement(title)) {\n    // if title is a React Element, we should get first text content as result,\n    // if there has not text content in React Element, return origin title\n    return getElementFirstTextContent(title) || title;\n  }\n  return title;\n}", "map": {"version": 3, "names": ["React", "getColumnKey", "column", "defaultKey", "key", "undefined", "dataIndex", "Array", "isArray", "join", "getColumnPos", "index", "pos", "concat", "getElementFirstTextContent", "node", "_a", "_b", "_c", "props", "children", "map", "call", "item", "renderColumnTitle", "title", "isValidElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/util.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nimport React from 'react';\nexport function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? \"\".concat(pos, \"-\").concat(index) : \"\".concat(index);\n}\n/**\n * Get first text content in Element\n *\n * @param node\n * @returns\n */\nfunction getElementFirstTextContent(node) {\n  var _a, _b, _c;\n  if (!node || !node.props || !node.props.children) return '';\n  if (typeof node.props.children === 'string') return node.props.children;\n  return ((_c = (_b = (_a = node.props.children) === null || _a === void 0 ? void 0 : _a.map) === null || _b === void 0 ? void 0 : _b.call(_a, function (item) {\n    return getElementFirstTextContent(item);\n  })) === null || _c === void 0 ? void 0 : _c[0]) || '';\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  // fix: #38155\n  if ( /*#__PURE__*/React.isValidElement(title)) {\n    // if title is a React Element, we should get first text content as result,\n    // if there has not text content in React Element, return origin title\n    return getElementFirstTextContent(title) || title;\n  }\n  return title;\n}"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC/C,IAAI,KAAK,IAAID,MAAM,IAAIA,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIH,MAAM,CAACE,GAAG,KAAK,IAAI,EAAE;IACtE,OAAOF,MAAM,CAACE,GAAG;EACnB;EACA,IAAIF,MAAM,CAACI,SAAS,EAAE;IACpB,OAAOC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACI,SAAS,CAAC,GAAGJ,MAAM,CAACI,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGP,MAAM,CAACI,SAAS;EACxF;EACA,OAAOH,UAAU;AACnB;AACA,OAAO,SAASO,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvC,OAAOA,GAAG,GAAG,EAAE,CAACC,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,CAACF,KAAK,CAAC,GAAG,EAAE,CAACE,MAAM,CAACF,KAAK,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,0BAA0BA,CAACC,IAAI,EAAE;EACxC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,IAAI,CAACH,IAAI,IAAI,CAACA,IAAI,CAACI,KAAK,IAAI,CAACJ,IAAI,CAACI,KAAK,CAACC,QAAQ,EAAE,OAAO,EAAE;EAC3D,IAAI,OAAOL,IAAI,CAACI,KAAK,CAACC,QAAQ,KAAK,QAAQ,EAAE,OAAOL,IAAI,CAACI,KAAK,CAACC,QAAQ;EACvE,OAAO,CAAC,CAACF,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACI,KAAK,CAACC,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,GAAG,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAACN,EAAE,EAAE,UAAUO,IAAI,EAAE;IAC3J,OAAOT,0BAA0B,CAACS,IAAI,CAAC;EACzC,CAAC,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE;AACvD;AACA,OAAO,SAASM,iBAAiBA,CAACC,KAAK,EAAEN,KAAK,EAAE;EAC9C,IAAI,OAAOM,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAOA,KAAK,CAACN,KAAK,CAAC;EACrB;EACA;EACA,IAAK,aAAanB,KAAK,CAAC0B,cAAc,CAACD,KAAK,CAAC,EAAE;IAC7C;IACA;IACA,OAAOX,0BAA0B,CAACW,KAAK,CAAC,IAAIA,KAAK;EACnD;EACA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}