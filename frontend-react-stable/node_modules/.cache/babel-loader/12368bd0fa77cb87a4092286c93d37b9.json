{"ast": null, "code": "import _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Render a group of scatters\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { Symbols } from '../shape/Symbols';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { uniqueId, interpolateNumber, getLinearRegression } from '../util/DataUtils';\nimport { getValueByDataKey, getCateCoordinateOfLine } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Scatter = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Scatter, _PureComponent);\n  var _super = _createSuper(Scatter);\n  function Scatter() {\n    var _this;\n    _classCallCheck(this, Scatter);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-scatter-'));\n    return _this;\n  }\n  _createClass(Scatter, [{\n    key: \"renderSymbolsStatically\",\n    value: function renderSymbolsStatically(points) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        activeShape = _this$props.activeShape,\n        activeIndex = _this$props.activeIndex;\n      var baseProps = filterProps(this.props);\n      return points.map(function (entry, i) {\n        var props = _objectSpread(_objectSpread({\n          key: \"symbol-\".concat(i)\n        }, baseProps), entry);\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-scatter-symbol\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"symbol-\".concat(i) // eslint-disable-line react/no-array-index-key\n          ,\n\n          role: \"img\"\n        }), Scatter.renderSymbolItem(activeIndex === i ? activeShape : shape, props));\n      });\n    }\n  }, {\n    key: \"renderSymbolsWithAnimation\",\n    value: function renderSymbolsWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        points = _this$props2.points,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevPoints = this.state.prevPoints;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = points.map(function (entry, index) {\n          var prev = prevPoints && prevPoints[index];\n          if (prev) {\n            var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n            var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n            var interpolatorSize = interpolateNumber(prev.size, entry.size);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              cx: interpolatorCx(t),\n              cy: interpolatorCy(t),\n              size: interpolatorSize(t)\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.size);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            size: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSymbolsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSymbols\",\n    value: function renderSymbols() {\n      var _this$props3 = this.props,\n        points = _this$props3.points,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevPoints = this.state.prevPoints;\n      if (isAnimationActive && points && points.length && (!prevPoints || !_isEqual(prevPoints, points))) {\n        return this.renderSymbolsWithAnimation();\n      }\n      return this.renderSymbolsStatically(points);\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar() {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        children = _this$props4.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      function dataPointFormatterY(dataPoint, dataKey) {\n        return {\n          x: dataPoint.cx,\n          y: dataPoint.cy,\n          value: +dataPoint.node.y,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      function dataPointFormatterX(dataPoint, dataKey) {\n        return {\n          x: dataPoint.cx,\n          y: dataPoint.cy,\n          value: +dataPoint.node.x,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      return errorBarItems.map(function (item, i) {\n        var direction = item.props.direction;\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: i,\n          // eslint-disable-line react/no-array-index-key\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: direction === 'x' ? 'vertical' : 'horizontal',\n          dataPointFormatter: direction === 'x' ? dataPointFormatterX : dataPointFormatterY\n        });\n      });\n    }\n  }, {\n    key: \"renderLine\",\n    value: function renderLine() {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        line = _this$props5.line,\n        lineType = _this$props5.lineType,\n        lineJointType = _this$props5.lineJointType;\n      var scatterProps = filterProps(this.props);\n      var customLineProps = filterProps(line);\n      var linePoints, lineItem;\n      if (lineType === 'joint') {\n        linePoints = points.map(function (entry) {\n          return {\n            x: entry.cx,\n            y: entry.cy\n          };\n        });\n      } else if (lineType === 'fitting') {\n        var _getLinearRegression = getLinearRegression(points),\n          xmin = _getLinearRegression.xmin,\n          xmax = _getLinearRegression.xmax,\n          a = _getLinearRegression.a,\n          b = _getLinearRegression.b;\n        var linearExp = function linearExp(x) {\n          return a * x + b;\n        };\n        linePoints = [{\n          x: xmin,\n          y: linearExp(xmin)\n        }, {\n          x: xmax,\n          y: linearExp(xmax)\n        }];\n      }\n      var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n        fill: 'none',\n        stroke: scatterProps && scatterProps.fill\n      }, customLineProps), {}, {\n        points: linePoints\n      });\n      if (/*#__PURE__*/React.isValidElement(line)) {\n        lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n      } else if (_isFunction(line)) {\n        lineItem = line(lineProps);\n      } else {\n        lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n          type: lineJointType\n        }));\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-scatter-line\",\n        key: \"recharts-scatter-line\"\n      }, lineItem);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        points = _this$props6.points,\n        line = _this$props6.line,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        id = _this$props6.id,\n        isAnimationActive = _this$props6.isAnimationActive;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = classNames('recharts-scatter', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, line && this.renderLine(), this.renderErrorBar(), /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-scatter-symbols\"\n      }, this.renderSymbols()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"renderSymbolItem\",\n    value: function renderSymbolItem(option, props) {\n      var symbol;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        symbol = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        symbol = option(props);\n      } else if (typeof option === 'string') {\n        symbol = /*#__PURE__*/React.createElement(Symbols, _extends({}, props, {\n          type: option\n        }));\n      }\n      return symbol;\n    }\n  }]);\n  return Scatter;\n}(PureComponent);\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n});\n_defineProperty(Scatter, \"getComposedData\", function (_ref2) {\n  var xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    zAxis = _ref2.zAxis,\n    item = _ref2.item,\n    displayedData = _ref2.displayedData,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    offset = _ref2.offset;\n  var tooltipType = item.props.tooltipType;\n  var cells = findAllByType(item.props.children, Cell);\n  var xAxisDataKey = _isNil(xAxis.dataKey) ? item.props.dataKey : xAxis.dataKey;\n  var yAxisDataKey = _isNil(yAxis.dataKey) ? item.props.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  var points = displayedData.map(function (entry, index) {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !_isNil(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      name: _isNil(xAxis.dataKey) ? item.props.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: tooltipType\n    }, {\n      name: _isNil(yAxis.dataKey) ? item.props.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry: entry,\n      index: index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry: entry,\n      index: index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx: cx,\n      cy: cy,\n      x: cx - radius,\n      y: cy - radius,\n      xAxis: xAxis,\n      yAxis: yAxis,\n      zAxis: zAxis,\n      width: 2 * radius,\n      height: 2 * radius,\n      size: size,\n      node: {\n        x: x,\n        y: y,\n        z: z\n      },\n      tooltipPayload: tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n  return _objectSpread({\n    points: points\n  }, offset);\n});", "map": {"version": 3, "names": ["_isEqual", "_isFunction", "_isNil", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "Animate", "classNames", "Layer", "LabelList", "findAllByType", "filterProps", "Global", "ZAxis", "Curve", "Symbols", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cell", "uniqueId", "interpolateNumber", "getLinearRegression", "getValueByDataKey", "getCateCoordinateOfLine", "adaptEventsOfChild", "<PERSON><PERSON><PERSON>", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "setState", "renderSymbolsStatically", "points", "_this2", "_this$props", "shape", "activeShape", "activeIndex", "baseProps", "map", "entry", "createElement", "className", "role", "renderSymbolItem", "renderSymbolsWithAnimation", "_this3", "_this$props2", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animationId", "prevPoints", "state", "begin", "duration", "isActive", "easing", "from", "t", "to", "onAnimationEnd", "handleAnimationEnd", "onAnimationStart", "handleAnimationStart", "_ref", "stepData", "index", "prev", "interpolatorCx", "cx", "interpolatorCy", "cy", "interpolatorSize", "size", "interpolator", "renderSymbols", "_this$props3", "renderErrorBar", "_this$props4", "xAxis", "yAxis", "children", "errorBarItems", "dataPointFormatterY", "dataPoint", "dataKey", "x", "y", "node", "errorVal", "dataPointFormatterX", "item", "direction", "cloneElement", "data", "layout", "dataPointFormatter", "renderLine", "_this$props5", "line", "lineType", "lineJointType", "scatterProps", "customLineProps", "linePoints", "lineItem", "_getLinearRegression", "xmin", "xmax", "a", "b", "linearExp", "lineProps", "fill", "stroke", "isValidElement", "type", "render", "_this$props6", "hide", "left", "top", "width", "height", "id", "layerClass", "needClip", "allowDataOverflow", "clipPathId", "clipPath", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "option", "symbol", "xAxisId", "yAxisId", "zAxisId", "legendType", "isSsr", "_ref2", "zAxis", "displayedData", "xAxisTicks", "yAxisTicks", "offset", "tooltipType", "cells", "xAxisDataKey", "yAxisDataKey", "zAxisDataKey", "defaultRangeZ", "range", "defaultProps", "defaultZ", "xBandSize", "scale", "bandwidth", "yBandSize", "z", "tooltipPayload", "name", "unit", "payload", "axis", "ticks", "bandSize", "radius", "Math", "sqrt", "max", "PI", "tooltipPosition"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/Scatter.js"], "sourcesContent": ["import _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Render a group of scatters\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { Symbols } from '../shape/Symbols';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { uniqueId, interpolateNumber, getLinearRegression } from '../util/DataUtils';\nimport { getValueByDataKey, getCateCoordinateOfLine } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Scatter = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Scatter, _PureComponent);\n  var _super = _createSuper(Scatter);\n  function Scatter() {\n    var _this;\n    _classCallCheck(this, Scatter);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-scatter-'));\n    return _this;\n  }\n  _createClass(Scatter, [{\n    key: \"renderSymbolsStatically\",\n    value: function renderSymbolsStatically(points) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        activeShape = _this$props.activeShape,\n        activeIndex = _this$props.activeIndex;\n      var baseProps = filterProps(this.props);\n      return points.map(function (entry, i) {\n        var props = _objectSpread(_objectSpread({\n          key: \"symbol-\".concat(i)\n        }, baseProps), entry);\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-scatter-symbol\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"symbol-\".concat(i) // eslint-disable-line react/no-array-index-key\n          ,\n          role: \"img\"\n        }), Scatter.renderSymbolItem(activeIndex === i ? activeShape : shape, props));\n      });\n    }\n  }, {\n    key: \"renderSymbolsWithAnimation\",\n    value: function renderSymbolsWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        points = _this$props2.points,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevPoints = this.state.prevPoints;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = points.map(function (entry, index) {\n          var prev = prevPoints && prevPoints[index];\n          if (prev) {\n            var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n            var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n            var interpolatorSize = interpolateNumber(prev.size, entry.size);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              cx: interpolatorCx(t),\n              cy: interpolatorCy(t),\n              size: interpolatorSize(t)\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.size);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            size: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSymbolsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSymbols\",\n    value: function renderSymbols() {\n      var _this$props3 = this.props,\n        points = _this$props3.points,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevPoints = this.state.prevPoints;\n      if (isAnimationActive && points && points.length && (!prevPoints || !_isEqual(prevPoints, points))) {\n        return this.renderSymbolsWithAnimation();\n      }\n      return this.renderSymbolsStatically(points);\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar() {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        children = _this$props4.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      function dataPointFormatterY(dataPoint, dataKey) {\n        return {\n          x: dataPoint.cx,\n          y: dataPoint.cy,\n          value: +dataPoint.node.y,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      function dataPointFormatterX(dataPoint, dataKey) {\n        return {\n          x: dataPoint.cx,\n          y: dataPoint.cy,\n          value: +dataPoint.node.x,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      }\n      return errorBarItems.map(function (item, i) {\n        var direction = item.props.direction;\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: i,\n          // eslint-disable-line react/no-array-index-key\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: direction === 'x' ? 'vertical' : 'horizontal',\n          dataPointFormatter: direction === 'x' ? dataPointFormatterX : dataPointFormatterY\n        });\n      });\n    }\n  }, {\n    key: \"renderLine\",\n    value: function renderLine() {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        line = _this$props5.line,\n        lineType = _this$props5.lineType,\n        lineJointType = _this$props5.lineJointType;\n      var scatterProps = filterProps(this.props);\n      var customLineProps = filterProps(line);\n      var linePoints, lineItem;\n      if (lineType === 'joint') {\n        linePoints = points.map(function (entry) {\n          return {\n            x: entry.cx,\n            y: entry.cy\n          };\n        });\n      } else if (lineType === 'fitting') {\n        var _getLinearRegression = getLinearRegression(points),\n          xmin = _getLinearRegression.xmin,\n          xmax = _getLinearRegression.xmax,\n          a = _getLinearRegression.a,\n          b = _getLinearRegression.b;\n        var linearExp = function linearExp(x) {\n          return a * x + b;\n        };\n        linePoints = [{\n          x: xmin,\n          y: linearExp(xmin)\n        }, {\n          x: xmax,\n          y: linearExp(xmax)\n        }];\n      }\n      var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n        fill: 'none',\n        stroke: scatterProps && scatterProps.fill\n      }, customLineProps), {}, {\n        points: linePoints\n      });\n      if ( /*#__PURE__*/React.isValidElement(line)) {\n        lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n      } else if (_isFunction(line)) {\n        lineItem = line(lineProps);\n      } else {\n        lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n          type: lineJointType\n        }));\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-scatter-line\",\n        key: \"recharts-scatter-line\"\n      }, lineItem);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        points = _this$props6.points,\n        line = _this$props6.line,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        id = _this$props6.id,\n        isAnimationActive = _this$props6.isAnimationActive;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = classNames('recharts-scatter', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, line && this.renderLine(), this.renderErrorBar(), /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-scatter-symbols\"\n      }, this.renderSymbols()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"renderSymbolItem\",\n    value: function renderSymbolItem(option, props) {\n      var symbol;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        symbol = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        symbol = option(props);\n      } else if (typeof option === 'string') {\n        symbol = /*#__PURE__*/React.createElement(Symbols, _extends({}, props, {\n          type: option\n        }));\n      }\n      return symbol;\n    }\n  }]);\n  return Scatter;\n}(PureComponent);\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n});\n_defineProperty(Scatter, \"getComposedData\", function (_ref2) {\n  var xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    zAxis = _ref2.zAxis,\n    item = _ref2.item,\n    displayedData = _ref2.displayedData,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    offset = _ref2.offset;\n  var tooltipType = item.props.tooltipType;\n  var cells = findAllByType(item.props.children, Cell);\n  var xAxisDataKey = _isNil(xAxis.dataKey) ? item.props.dataKey : xAxis.dataKey;\n  var yAxisDataKey = _isNil(yAxis.dataKey) ? item.props.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  var points = displayedData.map(function (entry, index) {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !_isNil(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      name: _isNil(xAxis.dataKey) ? item.props.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: tooltipType\n    }, {\n      name: _isNil(yAxis.dataKey) ? item.props.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry: entry,\n      index: index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry: entry,\n      index: index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx: cx,\n      cy: cy,\n      x: cx - radius,\n      y: cy - radius,\n      xAxis: xAxis,\n      yAxis: yAxis,\n      zAxis: zAxis,\n      width: 2 * radius,\n      height: 2 * radius,\n      size: size,\n      node: {\n        x: x,\n        y: y,\n        z: z\n      },\n      tooltipPayload: tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n  return _objectSpread({\n    points: points\n  }, offset);\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAACpB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGQ,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAEiB,eAAe,CAACtB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACxB,MAAM,EAAEH,MAAM,CAAC0B,yBAAyB,CAACnB,MAAM,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAER,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACoB,wBAAwB,CAACb,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC9B,MAAM,EAAE+B,KAAK,EAAE;EAAE,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,KAAK,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI+B,UAAU,GAAGD,KAAK,CAAC9B,CAAC,CAAC;IAAE+B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAErC,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEmC,cAAc,CAACH,UAAU,CAAC3B,GAAG,CAAC,EAAE2B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACjC,SAAS,EAAE0C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEzC,MAAM,CAAC4B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAAC7C,SAAS,GAAGE,MAAM,CAAC6C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC9C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEiD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEpC,MAAM,CAAC4B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAG/C,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAAChD,IAAI,CAAC,CAAC,GAAG,SAAS6C,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC7D,WAAW;MAAE8D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEpD,SAAS,EAAEuD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC9C,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAO0D,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEtD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIsB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACvE,SAAS,CAACwE,OAAO,CAAC5D,IAAI,CAACmD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG1D,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACwE,cAAc,CAACtE,IAAI,CAAC,CAAC,GAAG,SAASwD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAInD,MAAM,CAACwE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC/B,GAAG,EAAEc,GAAG,EAAEsC,KAAK,EAAE;EAAEtC,GAAG,GAAG8B,cAAc,CAAC9B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC4B,cAAc,CAAClC,GAAG,EAAEc,GAAG,EAAE;MAAEsC,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE3C,GAAG,CAACc,GAAG,CAAC,GAAGsC,KAAK;EAAE;EAAE,OAAOpD,GAAG;AAAE;AAC3O,SAAS4C,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAIjE,GAAG,GAAGkE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGmE,MAAM,CAACnE,GAAG,CAAC;AAAE;AAC5H,SAASkE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpF,OAAO,CAACmF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjF,MAAM,CAACoF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACpE,IAAI,CAACkE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpF,OAAO,CAACwF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAQ,mBAAmB;AACpF,SAASC,iBAAiB,EAAEC,uBAAuB,QAAQ,oBAAoB;AAC/E,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAO,IAAIC,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC1D9D,SAAS,CAAC6D,OAAO,EAAEC,cAAc,CAAC;EAClC,IAAIC,MAAM,GAAGrD,YAAY,CAACmD,OAAO,CAAC;EAClC,SAASA,OAAOA,CAAA,EAAG;IACjB,IAAIG,KAAK;IACT7E,eAAe,CAAC,IAAI,EAAE0E,OAAO,CAAC;IAC9B,KAAK,IAAII,IAAI,GAAGtG,SAAS,CAACC,MAAM,EAAEsG,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGzG,SAAS,CAACyG,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAAC/F,IAAI,CAACC,KAAK,CAAC8F,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDnF,eAAe,CAACwC,sBAAsB,CAACyC,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACFvF,eAAe,CAACwC,sBAAsB,CAACyC,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/EA,KAAK,CAACO,QAAQ,CAAC;QACbD,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFvF,eAAe,CAACwC,sBAAsB,CAACyC,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjFA,KAAK,CAACO,QAAQ,CAAC;QACbD,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFvF,eAAe,CAACwC,sBAAsB,CAACyC,KAAK,CAAC,EAAE,IAAI,EAAET,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IACnF,OAAOS,KAAK;EACd;EACAnE,YAAY,CAACgE,OAAO,EAAE,CAAC;IACrB/F,GAAG,EAAE,yBAAyB;IAC9BsC,KAAK,EAAE,SAASoE,uBAAuBA,CAACC,MAAM,EAAE;MAC9C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,WAAW,GAAG,IAAI,CAACnF,KAAK;QAC1BoF,KAAK,GAAGD,WAAW,CAACC,KAAK;QACzBC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,WAAW,GAAGH,WAAW,CAACG,WAAW;MACvC,IAAIC,SAAS,GAAG/B,WAAW,CAAC,IAAI,CAACxD,KAAK,CAAC;MACvC,OAAOiF,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAEvH,CAAC,EAAE;QACpC,IAAI8B,KAAK,GAAGX,aAAa,CAACA,aAAa,CAAC;UACtCf,GAAG,EAAE,SAAS,CAACuG,MAAM,CAAC3G,CAAC;QACzB,CAAC,EAAEqH,SAAS,CAAC,EAAEE,KAAK,CAAC;QACrB,OAAO,aAAaxC,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAExF,QAAQ,CAAC;UACtD8H,SAAS,EAAE;QACb,CAAC,EAAEvB,kBAAkB,CAACc,MAAM,CAAClF,KAAK,EAAEyF,KAAK,EAAEvH,CAAC,CAAC,EAAE;UAC7CI,GAAG,EAAE,SAAS,CAACuG,MAAM,CAAC3G,CAAC,CAAC,CAAC;UAAA;;UAEzB0H,IAAI,EAAE;QACR,CAAC,CAAC,EAAEvB,OAAO,CAACwB,gBAAgB,CAACP,WAAW,KAAKpH,CAAC,GAAGmH,WAAW,GAAGD,KAAK,EAAEpF,KAAK,CAAC,CAAC;MAC/E,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,4BAA4B;IACjCsC,KAAK,EAAE,SAASkF,0BAA0BA,CAAA,EAAG;MAC3C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAChG,KAAK;QAC3BiF,MAAM,GAAGe,YAAY,CAACf,MAAM;QAC5BgB,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;QAClDC,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,WAAW,GAAGL,YAAY,CAACK,WAAW;MACxC,IAAIC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACD,UAAU;MACtC,OAAO,aAAarD,KAAK,CAACyC,aAAa,CAACvC,OAAO,EAAE;QAC/CqD,KAAK,EAAEN,cAAc;QACrBO,QAAQ,EAAEN,iBAAiB;QAC3BO,QAAQ,EAAET,iBAAiB;QAC3BU,MAAM,EAAEP,eAAe;QACvBQ,IAAI,EAAE;UACJC,CAAC,EAAE;QACL,CAAC;QACDC,EAAE,EAAE;UACFD,CAAC,EAAE;QACL,CAAC;QACDvI,GAAG,EAAE,MAAM,CAACuG,MAAM,CAACwB,WAAW,CAAC;QAC/BU,cAAc,EAAE,IAAI,CAACC,kBAAkB;QACvCC,gBAAgB,EAAE,IAAI,CAACC;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIN,CAAC,GAAGM,IAAI,CAACN,CAAC;QACd,IAAIO,QAAQ,GAAGnC,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE4B,KAAK,EAAE;UAChD,IAAIC,IAAI,GAAGhB,UAAU,IAAIA,UAAU,CAACe,KAAK,CAAC;UAC1C,IAAIC,IAAI,EAAE;YACR,IAAIC,cAAc,GAAGvD,iBAAiB,CAACsD,IAAI,CAACE,EAAE,EAAE/B,KAAK,CAAC+B,EAAE,CAAC;YACzD,IAAIC,cAAc,GAAGzD,iBAAiB,CAACsD,IAAI,CAACI,EAAE,EAAEjC,KAAK,CAACiC,EAAE,CAAC;YACzD,IAAIC,gBAAgB,GAAG3D,iBAAiB,CAACsD,IAAI,CAACM,IAAI,EAAEnC,KAAK,CAACmC,IAAI,CAAC;YAC/D,OAAOvI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjD+B,EAAE,EAAED,cAAc,CAACV,CAAC,CAAC;cACrBa,EAAE,EAAED,cAAc,CAACZ,CAAC,CAAC;cACrBe,IAAI,EAAED,gBAAgB,CAACd,CAAC;YAC1B,CAAC,CAAC;UACJ;UACA,IAAIgB,YAAY,GAAG7D,iBAAiB,CAAC,CAAC,EAAEyB,KAAK,CAACmC,IAAI,CAAC;UACnD,OAAOvI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDmC,IAAI,EAAEC,YAAY,CAAChB,CAAC;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAa5D,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAE,IAAI,EAAE0C,MAAM,CAACf,uBAAuB,CAACoC,QAAQ,CAAC,CAAC;MAChG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,SAASkH,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAAC/H,KAAK;QAC3BiF,MAAM,GAAG8C,YAAY,CAAC9C,MAAM;QAC5BgB,iBAAiB,GAAG8B,YAAY,CAAC9B,iBAAiB;MACpD,IAAIK,UAAU,GAAG,IAAI,CAACC,KAAK,CAACD,UAAU;MACtC,IAAIL,iBAAiB,IAAIhB,MAAM,IAAIA,MAAM,CAAC7G,MAAM,KAAK,CAACkI,UAAU,IAAI,CAAClJ,QAAQ,CAACkJ,UAAU,EAAErB,MAAM,CAAC,CAAC,EAAE;QAClG,OAAO,IAAI,CAACa,0BAA0B,CAAC,CAAC;MAC1C;MACA,OAAO,IAAI,CAACd,uBAAuB,CAACC,MAAM,CAAC;IAC7C;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,gBAAgB;IACrBsC,KAAK,EAAE,SAASoH,cAAcA,CAAA,EAAG;MAC/B,IAAI/B,iBAAiB,GAAG,IAAI,CAACjG,KAAK,CAACiG,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACM,KAAK,CAACzB,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAImD,YAAY,GAAG,IAAI,CAACjI,KAAK;QAC3BiF,MAAM,GAAGgD,YAAY,CAAChD,MAAM;QAC5BiD,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,KAAK,GAAGF,YAAY,CAACE,KAAK;QAC1BC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;MAClC,IAAIC,aAAa,GAAG9E,aAAa,CAAC6E,QAAQ,EAAEvE,QAAQ,CAAC;MACrD,IAAI,CAACwE,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,OAAO,EAAE;QAC/C,OAAO;UACLC,CAAC,EAAEF,SAAS,CAACf,EAAE;UACfkB,CAAC,EAAEH,SAAS,CAACb,EAAE;UACf9G,KAAK,EAAE,CAAC2H,SAAS,CAACI,IAAI,CAACD,CAAC;UACxBE,QAAQ,EAAE1E,iBAAiB,CAACqE,SAAS,EAAEC,OAAO;QAChD,CAAC;MACH;MACA,SAASK,mBAAmBA,CAACN,SAAS,EAAEC,OAAO,EAAE;QAC/C,OAAO;UACLC,CAAC,EAAEF,SAAS,CAACf,EAAE;UACfkB,CAAC,EAAEH,SAAS,CAACb,EAAE;UACf9G,KAAK,EAAE,CAAC2H,SAAS,CAACI,IAAI,CAACF,CAAC;UACxBG,QAAQ,EAAE1E,iBAAiB,CAACqE,SAAS,EAAEC,OAAO;QAChD,CAAC;MACH;MACA,OAAOH,aAAa,CAAC7C,GAAG,CAAC,UAAUsD,IAAI,EAAE5K,CAAC,EAAE;QAC1C,IAAI6K,SAAS,GAAGD,IAAI,CAAC9I,KAAK,CAAC+I,SAAS;QACpC,OAAO,aAAa9F,KAAK,CAAC+F,YAAY,CAACF,IAAI,EAAE;UAC3CxK,GAAG,EAAEJ,CAAC;UACN;UACA+K,IAAI,EAAEhE,MAAM;UACZiD,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZe,MAAM,EAAEH,SAAS,KAAK,GAAG,GAAG,UAAU,GAAG,YAAY;UACrDI,kBAAkB,EAAEJ,SAAS,KAAK,GAAG,GAAGF,mBAAmB,GAAGP;QAChE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhK,GAAG,EAAE,YAAY;IACjBsC,KAAK,EAAE,SAASwI,UAAUA,CAAA,EAAG;MAC3B,IAAIC,YAAY,GAAG,IAAI,CAACrJ,KAAK;QAC3BiF,MAAM,GAAGoE,YAAY,CAACpE,MAAM;QAC5BqE,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCC,aAAa,GAAGH,YAAY,CAACG,aAAa;MAC5C,IAAIC,YAAY,GAAGjG,WAAW,CAAC,IAAI,CAACxD,KAAK,CAAC;MAC1C,IAAI0J,eAAe,GAAGlG,WAAW,CAAC8F,IAAI,CAAC;MACvC,IAAIK,UAAU,EAAEC,QAAQ;MACxB,IAAIL,QAAQ,KAAK,OAAO,EAAE;QACxBI,UAAU,GAAG1E,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE;UACvC,OAAO;YACLgD,CAAC,EAAEhD,KAAK,CAAC+B,EAAE;YACXkB,CAAC,EAAEjD,KAAK,CAACiC;UACX,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI6B,QAAQ,KAAK,SAAS,EAAE;QACjC,IAAIM,oBAAoB,GAAG5F,mBAAmB,CAACgB,MAAM,CAAC;UACpD6E,IAAI,GAAGD,oBAAoB,CAACC,IAAI;UAChCC,IAAI,GAAGF,oBAAoB,CAACE,IAAI;UAChCC,CAAC,GAAGH,oBAAoB,CAACG,CAAC;UAC1BC,CAAC,GAAGJ,oBAAoB,CAACI,CAAC;QAC5B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACzB,CAAC,EAAE;UACpC,OAAOuB,CAAC,GAAGvB,CAAC,GAAGwB,CAAC;QAClB,CAAC;QACDN,UAAU,GAAG,CAAC;UACZlB,CAAC,EAAEqB,IAAI;UACPpB,CAAC,EAAEwB,SAAS,CAACJ,IAAI;QACnB,CAAC,EAAE;UACDrB,CAAC,EAAEsB,IAAI;UACPrB,CAAC,EAAEwB,SAAS,CAACH,IAAI;QACnB,CAAC,CAAC;MACJ;MACA,IAAII,SAAS,GAAG9K,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoK,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/EW,IAAI,EAAE,MAAM;QACZC,MAAM,EAAEZ,YAAY,IAAIA,YAAY,CAACW;MACvC,CAAC,EAAEV,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;QACvBzE,MAAM,EAAE0E;MACV,CAAC,CAAC;MACF,IAAK,aAAa1G,KAAK,CAACqH,cAAc,CAAChB,IAAI,CAAC,EAAE;QAC5CM,QAAQ,GAAG,aAAa3G,KAAK,CAAC+F,YAAY,CAACM,IAAI,EAAEa,SAAS,CAAC;MAC7D,CAAC,MAAM,IAAI9M,WAAW,CAACiM,IAAI,CAAC,EAAE;QAC5BM,QAAQ,GAAGN,IAAI,CAACa,SAAS,CAAC;MAC5B,CAAC,MAAM;QACLP,QAAQ,GAAG,aAAa3G,KAAK,CAACyC,aAAa,CAAC/B,KAAK,EAAE9F,QAAQ,CAAC,CAAC,CAAC,EAAEsM,SAAS,EAAE;UACzEI,IAAI,EAAEf;QACR,CAAC,CAAC,CAAC;MACL;MACA,OAAO,aAAavG,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAE;QAC7CsC,SAAS,EAAE,uBAAuB;QAClCrH,GAAG,EAAE;MACP,CAAC,EAAEsL,QAAQ,CAAC;IACd;EACF,CAAC,EAAE;IACDtL,GAAG,EAAE,QAAQ;IACbsC,KAAK,EAAE,SAAS4J,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACzK,KAAK;QAC3B0K,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBzF,MAAM,GAAGwF,YAAY,CAACxF,MAAM;QAC5BqE,IAAI,GAAGmB,YAAY,CAACnB,IAAI;QACxB3D,SAAS,GAAG8E,YAAY,CAAC9E,SAAS;QAClCuC,KAAK,GAAGuC,YAAY,CAACvC,KAAK;QAC1BC,KAAK,GAAGsC,YAAY,CAACtC,KAAK;QAC1BwC,IAAI,GAAGF,YAAY,CAACE,IAAI;QACxBC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBC,KAAK,GAAGJ,YAAY,CAACI,KAAK;QAC1BC,MAAM,GAAGL,YAAY,CAACK,MAAM;QAC5BC,EAAE,GAAGN,YAAY,CAACM,EAAE;QACpB9E,iBAAiB,GAAGwE,YAAY,CAACxE,iBAAiB;MACpD,IAAIyE,IAAI,IAAI,CAACzF,MAAM,IAAI,CAACA,MAAM,CAAC7G,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAI0G,mBAAmB,GAAG,IAAI,CAACyB,KAAK,CAACzB,mBAAmB;MACxD,IAAIkG,UAAU,GAAG5H,UAAU,CAAC,kBAAkB,EAAEuC,SAAS,CAAC;MAC1D,IAAIsF,QAAQ,GAAG/C,KAAK,IAAIA,KAAK,CAACgD,iBAAiB,IAAI/C,KAAK,IAAIA,KAAK,CAAC+C,iBAAiB;MACnF,IAAIC,UAAU,GAAG7N,MAAM,CAACyN,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MAC1C,OAAO,aAAa9H,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAE;QAC7CsC,SAAS,EAAEqF,UAAU;QACrBI,QAAQ,EAAEH,QAAQ,GAAG,gBAAgB,CAACpG,MAAM,CAACsG,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAEF,QAAQ,GAAG,aAAahI,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAazC,KAAK,CAACyC,aAAa,CAAC,UAAU,EAAE;QACxGqF,EAAE,EAAE,WAAW,CAAClG,MAAM,CAACsG,UAAU;MACnC,CAAC,EAAE,aAAalI,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;QAC1C+C,CAAC,EAAEkC,IAAI;QACPjC,CAAC,EAAEkC,GAAG;QACNC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAExB,IAAI,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAE,IAAI,CAACpB,cAAc,CAAC,CAAC,EAAE,aAAa/E,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAE;QACrG/E,GAAG,EAAE;MACP,CAAC,EAAE,IAAI,CAACwJ,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC7B,iBAAiB,IAAInB,mBAAmB,KAAKxB,SAAS,CAAC+H,kBAAkB,CAAC,IAAI,CAACrL,KAAK,EAAEiF,MAAM,CAAC,CAAC;IAC5H;EACF,CAAC,CAAC,EAAE,CAAC;IACH3G,GAAG,EAAE,0BAA0B;IAC/BsC,KAAK,EAAE,SAAS0K,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAClF,WAAW,KAAKmF,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAClF,WAAW;UACtCqF,SAAS,EAAEH,SAAS,CAACtG,MAAM;UAC3BqB,UAAU,EAAEkF,SAAS,CAACE;QACxB,CAAC;MACH;MACA,IAAIH,SAAS,CAACtG,MAAM,KAAKuG,SAAS,CAACE,SAAS,EAAE;QAC5C,OAAO;UACLA,SAAS,EAAEH,SAAS,CAACtG;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,kBAAkB;IACvBsC,KAAK,EAAE,SAASiF,gBAAgBA,CAAC8F,MAAM,EAAE3L,KAAK,EAAE;MAC9C,IAAI4L,MAAM;MACV,IAAK,aAAa3I,KAAK,CAACqH,cAAc,CAACqB,MAAM,CAAC,EAAE;QAC9CC,MAAM,GAAG,aAAa3I,KAAK,CAAC+F,YAAY,CAAC2C,MAAM,EAAE3L,KAAK,CAAC;MACzD,CAAC,MAAM,IAAI3C,WAAW,CAACsO,MAAM,CAAC,EAAE;QAC9BC,MAAM,GAAGD,MAAM,CAAC3L,KAAK,CAAC;MACxB,CAAC,MAAM,IAAI,OAAO2L,MAAM,KAAK,QAAQ,EAAE;QACrCC,MAAM,GAAG,aAAa3I,KAAK,CAACyC,aAAa,CAAC9B,OAAO,EAAE/F,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;UACrEuK,IAAI,EAAEoB;QACR,CAAC,CAAC,CAAC;MACL;MACA,OAAOC,MAAM;IACf;EACF,CAAC,CAAC,CAAC;EACH,OAAOvH,OAAO;AAChB,CAAC,CAACnB,aAAa,CAAC;AAChB3D,eAAe,CAAC8E,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClD9E,eAAe,CAAC8E,OAAO,EAAE,cAAc,EAAE;EACvCwH,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,QAAQ;EACpBzC,QAAQ,EAAE,OAAO;EACjBC,aAAa,EAAE,QAAQ;EACvBP,IAAI,EAAE,EAAE;EACR7D,KAAK,EAAE,QAAQ;EACfsF,IAAI,EAAE,KAAK;EACXzE,iBAAiB,EAAE,CAACxC,MAAM,CAACwI,KAAK;EAChC/F,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF7G,eAAe,CAAC8E,OAAO,EAAE,iBAAiB,EAAE,UAAU6H,KAAK,EAAE;EAC3D,IAAIhE,KAAK,GAAGgE,KAAK,CAAChE,KAAK;IACrBC,KAAK,GAAG+D,KAAK,CAAC/D,KAAK;IACnBgE,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBrD,IAAI,GAAGoD,KAAK,CAACpD,IAAI;IACjBsD,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAIC,WAAW,GAAG1D,IAAI,CAAC9I,KAAK,CAACwM,WAAW;EACxC,IAAIC,KAAK,GAAGlJ,aAAa,CAACuF,IAAI,CAAC9I,KAAK,CAACoI,QAAQ,EAAEtE,IAAI,CAAC;EACpD,IAAI4I,YAAY,GAAGpP,MAAM,CAAC4K,KAAK,CAACM,OAAO,CAAC,GAAGM,IAAI,CAAC9I,KAAK,CAACwI,OAAO,GAAGN,KAAK,CAACM,OAAO;EAC7E,IAAImE,YAAY,GAAGrP,MAAM,CAAC6K,KAAK,CAACK,OAAO,CAAC,GAAGM,IAAI,CAAC9I,KAAK,CAACwI,OAAO,GAAGL,KAAK,CAACK,OAAO;EAC7E,IAAIoE,YAAY,GAAGT,KAAK,IAAIA,KAAK,CAAC3D,OAAO;EACzC,IAAIqE,aAAa,GAAGV,KAAK,GAAGA,KAAK,CAACW,KAAK,GAAGpJ,KAAK,CAACqJ,YAAY,CAACD,KAAK;EAClE,IAAIE,QAAQ,GAAGH,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC;EAChD,IAAII,SAAS,GAAG/E,KAAK,CAACgF,KAAK,CAACC,SAAS,GAAGjF,KAAK,CAACgF,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,IAAIC,SAAS,GAAGjF,KAAK,CAAC+E,KAAK,CAACC,SAAS,GAAGhF,KAAK,CAAC+E,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,IAAIlI,MAAM,GAAGmH,aAAa,CAAC5G,GAAG,CAAC,UAAUC,KAAK,EAAE4B,KAAK,EAAE;IACrD,IAAIoB,CAAC,GAAGvE,iBAAiB,CAACuB,KAAK,EAAEiH,YAAY,CAAC;IAC9C,IAAIhE,CAAC,GAAGxE,iBAAiB,CAACuB,KAAK,EAAEkH,YAAY,CAAC;IAC9C,IAAIU,CAAC,GAAG,CAAC/P,MAAM,CAACsP,YAAY,CAAC,IAAI1I,iBAAiB,CAACuB,KAAK,EAAEmH,YAAY,CAAC,IAAI,GAAG;IAC9E,IAAIU,cAAc,GAAG,CAAC;MACpBC,IAAI,EAAEjQ,MAAM,CAAC4K,KAAK,CAACM,OAAO,CAAC,GAAGM,IAAI,CAAC9I,KAAK,CAACuN,IAAI,GAAGrF,KAAK,CAACqF,IAAI,IAAIrF,KAAK,CAACM,OAAO;MAC3EgF,IAAI,EAAEtF,KAAK,CAACsF,IAAI,IAAI,EAAE;MACtB5M,KAAK,EAAE6H,CAAC;MACRgF,OAAO,EAAEhI,KAAK;MACd+C,OAAO,EAAEkE,YAAY;MACrBnC,IAAI,EAAEiC;IACR,CAAC,EAAE;MACDe,IAAI,EAAEjQ,MAAM,CAAC6K,KAAK,CAACK,OAAO,CAAC,GAAGM,IAAI,CAAC9I,KAAK,CAACuN,IAAI,GAAGpF,KAAK,CAACoF,IAAI,IAAIpF,KAAK,CAACK,OAAO;MAC3EgF,IAAI,EAAErF,KAAK,CAACqF,IAAI,IAAI,EAAE;MACtB5M,KAAK,EAAE8H,CAAC;MACR+E,OAAO,EAAEhI,KAAK;MACd+C,OAAO,EAAEmE,YAAY;MACrBpC,IAAI,EAAEiC;IACR,CAAC,CAAC;IACF,IAAIa,CAAC,KAAK,GAAG,EAAE;MACbC,cAAc,CAAClO,IAAI,CAAC;QAClBmO,IAAI,EAAEpB,KAAK,CAACoB,IAAI,IAAIpB,KAAK,CAAC3D,OAAO;QACjCgF,IAAI,EAAErB,KAAK,CAACqB,IAAI,IAAI,EAAE;QACtB5M,KAAK,EAAEyM,CAAC;QACRI,OAAO,EAAEhI,KAAK;QACd+C,OAAO,EAAEoE,YAAY;QACrBrC,IAAI,EAAEiC;MACR,CAAC,CAAC;IACJ;IACA,IAAIhF,EAAE,GAAGrD,uBAAuB,CAAC;MAC/BuJ,IAAI,EAAExF,KAAK;MACXyF,KAAK,EAAEtB,UAAU;MACjBuB,QAAQ,EAAEX,SAAS;MACnBxH,KAAK,EAAEA,KAAK;MACZ4B,KAAK,EAAEA,KAAK;MACZmB,OAAO,EAAEkE;IACX,CAAC,CAAC;IACF,IAAIhF,EAAE,GAAGvD,uBAAuB,CAAC;MAC/BuJ,IAAI,EAAEvF,KAAK;MACXwF,KAAK,EAAErB,UAAU;MACjBsB,QAAQ,EAAER,SAAS;MACnB3H,KAAK,EAAEA,KAAK;MACZ4B,KAAK,EAAEA,KAAK;MACZmB,OAAO,EAAEmE;IACX,CAAC,CAAC;IACF,IAAI/E,IAAI,GAAGyF,CAAC,KAAK,GAAG,GAAGlB,KAAK,CAACe,KAAK,CAACG,CAAC,CAAC,GAAGL,QAAQ;IAChD,IAAIa,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACpG,IAAI,EAAE,CAAC,CAAC,GAAGkG,IAAI,CAACG,EAAE,CAAC;IACnD,OAAO5O,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjD+B,EAAE,EAAEA,EAAE;MACNE,EAAE,EAAEA,EAAE;MACNe,CAAC,EAAEjB,EAAE,GAAGqG,MAAM;MACdnF,CAAC,EAAEhB,EAAE,GAAGmG,MAAM;MACd3F,KAAK,EAAEA,KAAK;MACZC,KAAK,EAAEA,KAAK;MACZgE,KAAK,EAAEA,KAAK;MACZtB,KAAK,EAAE,CAAC,GAAGgD,MAAM;MACjB/C,MAAM,EAAE,CAAC,GAAG+C,MAAM;MAClBjG,IAAI,EAAEA,IAAI;MACVe,IAAI,EAAE;QACJF,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJ2E,CAAC,EAAEA;MACL,CAAC;MACDC,cAAc,EAAEA,cAAc;MAC9BY,eAAe,EAAE;QACfzF,CAAC,EAAEjB,EAAE;QACLkB,CAAC,EAAEhB;MACL,CAAC;MACD+F,OAAO,EAAEhI;IACX,CAAC,EAAEgH,KAAK,IAAIA,KAAK,CAACpF,KAAK,CAAC,IAAIoF,KAAK,CAACpF,KAAK,CAAC,CAACrH,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,OAAOX,aAAa,CAAC;IACnB4F,MAAM,EAAEA;EACV,CAAC,EAAEsH,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}