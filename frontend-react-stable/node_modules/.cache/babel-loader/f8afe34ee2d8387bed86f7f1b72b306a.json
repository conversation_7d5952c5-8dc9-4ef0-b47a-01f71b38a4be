{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport * as React from 'react';\nimport Affix from '../affix';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport AnchorContext from './context';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  var rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      container = element.ownerDocument.documentElement;\n      return rect.top - container.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nvar sharpMatcherRegx = /#([\\S ]+)$/;\nvar AnchorContent = function AnchorContent(props) {\n  var _a;\n  var prefixCls = props.anchorPrefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    style = props.style,\n    offsetTop = props.offsetTop,\n    _props$affix = props.affix,\n    affix = _props$affix === void 0 ? true : _props$affix,\n    _props$showInkInFixed = props.showInkInFixed,\n    showInkInFixed = _props$showInkInFixed === void 0 ? false : _props$showInkInFixed,\n    children = props.children,\n    bounds = props.bounds,\n    targetOffset = props.targetOffset,\n    onClick = props.onClick,\n    onChange = props.onChange,\n    getContainer = props.getContainer,\n    getCurrentAnchor = props.getCurrentAnchor;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    links = _React$useState2[0],\n    setLinks = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeLink = _React$useState4[0],\n    setActiveLink = _React$useState4[1];\n  var activeLinkRef = React.useRef(activeLink);\n  var wrapperRef = React.useRef(null);\n  var spanLinkNode = React.useRef(null);\n  var animating = React.useRef(false);\n  var _React$useContext = React.useContext(ConfigContext),\n    direction = _React$useContext.direction,\n    getTargetContainer = _React$useContext.getTargetContainer;\n  var getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  var dependencyListItem = JSON.stringify(links);\n  var registerLink = React.useCallback(function (link) {\n    if (!links.includes(link)) {\n      setLinks(function (prev) {\n        return [].concat(_toConsumableArray(prev), [link]);\n      });\n    }\n  }, [dependencyListItem]);\n  var unregisterLink = React.useCallback(function (link) {\n    if (links.includes(link)) {\n      setLinks(function (prev) {\n        return prev.filter(function (i) {\n          return i !== link;\n        });\n      });\n    }\n  }, [dependencyListItem]);\n  var updateInk = function updateInk() {\n    var _a;\n    var linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(\".\".concat(prefixCls, \"-link-title-active\"));\n    if (linkNode && spanLinkNode.current) {\n      spanLinkNode.current.style.top = \"\".concat(linkNode.offsetTop + linkNode.clientHeight / 2 - 4.5, \"px\");\n    }\n  };\n  var getInternalCurrentAnchor = function getInternalCurrentAnchor(_links) {\n    var _offsetTop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var _bounds = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5;\n    var linkSections = [];\n    var container = getCurrentContainer();\n    _links.forEach(function (link) {\n      var sharpLinkMatch = sharpMatcherRegx.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      var target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        var top = getOffsetTop(target, container);\n        if (top < _offsetTop + _bounds) {\n          linkSections.push({\n            link: link,\n            top: top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      var maxSection = linkSections.reduce(function (prev, curr) {\n        return curr.top > prev.top ? curr : prev;\n      });\n      return maxSection.link;\n    }\n    return '';\n  };\n  var setCurrentActiveLink = function setCurrentActiveLink(link) {\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    var newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  };\n  var handleScroll = React.useCallback(function () {\n    if (animating.current) {\n      return;\n    }\n    if (typeof getCurrentAnchor === 'function') {\n      return;\n    }\n    var currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  var handleScrollTo = React.useCallback(function (link) {\n    setCurrentActiveLink(link);\n    var container = getCurrentContainer();\n    var scrollTop = getScroll(container, true);\n    var sharpLinkMatch = sharpMatcherRegx.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    var targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    var eleOffsetTop = getOffsetTop(targetElement, container);\n    var y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback: function callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  var inkClass = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-ink-ball-visible\"), activeLink), \"\".concat(prefixCls, \"-ink-ball\"));\n  var wrapperClass = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  var anchorClass = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-fixed\"), !affix && !showInkInFixed));\n  var wrapperStyle = _extends({\n    maxHeight: offsetTop ? \"calc(100vh - \".concat(offsetTop, \"px)\") : '100vh'\n  }, style);\n  var anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-ink\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  })), children));\n  React.useEffect(function () {\n    var scrollContainer = getCurrentContainer();\n    var scrollEvent = addEventListener(scrollContainer, 'scroll', handleScroll);\n    handleScroll();\n    return function () {\n      scrollEvent === null || scrollEvent === void 0 ? void 0 : scrollEvent.remove();\n    };\n  }, [dependencyListItem]);\n  React.useEffect(function () {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(function () {\n    updateInk();\n  }, [getCurrentAnchor, dependencyListItem, activeLink]);\n  var memoizedContextValue = React.useMemo(function () {\n    return {\n      registerLink: registerLink,\n      unregisterLink: unregisterLink,\n      scrollTo: handleScrollTo,\n      activeLink: activeLink,\n      onClick: onClick\n    };\n  }, [activeLink, onClick, handleScrollTo]);\n  return /*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? /*#__PURE__*/React.createElement(Affix, {\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, anchorContent) : anchorContent);\n};\nvar Anchor = function Anchor(props) {\n  var customizePrefixCls = props.prefixCls;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var anchorPrefixCls = getPrefixCls('anchor', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(AnchorContent, _extends({}, props, {\n    anchorPrefixCls: anchorPrefixCls\n  }));\n};\nexport default Anchor;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "_slicedToArray", "classNames", "addEventListener", "React", "Affix", "ConfigContext", "getScroll", "scrollTo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDefaultContainer", "window", "getOffsetTop", "element", "container", "getClientRects", "length", "rect", "getBoundingClientRect", "width", "height", "ownerDocument", "documentElement", "top", "clientTop", "sharpMatcherRegx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "_a", "prefixCls", "anchorPrefixCls", "_props$className", "className", "style", "offsetTop", "_props$affix", "affix", "_props$showInkInFixed", "showInkInFixed", "children", "bounds", "targetOffset", "onClick", "onChange", "getContainer", "getCurrentAnchor", "_React$useState", "useState", "_React$useState2", "links", "setLinks", "_React$useState3", "_React$useState4", "activeLink", "setActiveLink", "activeLinkRef", "useRef", "wrapperRef", "spanLinkNode", "animating", "_React$useContext", "useContext", "direction", "getTargetContainer", "getCurrentContainer", "dependencyListItem", "JSON", "stringify", "registerLink", "useCallback", "link", "includes", "prev", "concat", "unregisterLink", "filter", "i", "updateInk", "linkNode", "current", "querySelector", "clientHeight", "getInternalCurrentAnchor", "_links", "_offsetTop", "arguments", "undefined", "_bounds", "linkSections", "for<PERSON>ach", "sharpLinkMatch", "exec", "toString", "target", "document", "getElementById", "push", "maxSection", "reduce", "curr", "setCurrentActiveLink", "newLink", "handleScroll", "currentActiveLink", "handleScrollTo", "scrollTop", "targetElement", "eleOffsetTop", "y", "callback", "inkClass", "wrapperClass", "anchorClass", "wrapperStyle", "maxHeight", "anchorContent", "createElement", "ref", "useEffect", "scrollContainer", "scrollEvent", "remove", "memoizedContextValue", "useMemo", "Provider", "value", "<PERSON><PERSON>", "customizePrefixCls", "_React$useContext2", "getPrefixCls"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/anchor/Anchor.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport * as React from 'react';\nimport Affix from '../affix';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport AnchorContext from './context';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  var rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      container = element.ownerDocument.documentElement;\n      return rect.top - container.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nvar sharpMatcherRegx = /#([\\S ]+)$/;\nvar AnchorContent = function AnchorContent(props) {\n  var _a;\n  var prefixCls = props.anchorPrefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    style = props.style,\n    offsetTop = props.offsetTop,\n    _props$affix = props.affix,\n    affix = _props$affix === void 0 ? true : _props$affix,\n    _props$showInkInFixed = props.showInkInFixed,\n    showInkInFixed = _props$showInkInFixed === void 0 ? false : _props$showInkInFixed,\n    children = props.children,\n    bounds = props.bounds,\n    targetOffset = props.targetOffset,\n    onClick = props.onClick,\n    onChange = props.onChange,\n    getContainer = props.getContainer,\n    getCurrentAnchor = props.getCurrentAnchor;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    links = _React$useState2[0],\n    setLinks = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeLink = _React$useState4[0],\n    setActiveLink = _React$useState4[1];\n  var activeLinkRef = React.useRef(activeLink);\n  var wrapperRef = React.useRef(null);\n  var spanLinkNode = React.useRef(null);\n  var animating = React.useRef(false);\n  var _React$useContext = React.useContext(ConfigContext),\n    direction = _React$useContext.direction,\n    getTargetContainer = _React$useContext.getTargetContainer;\n  var getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  var dependencyListItem = JSON.stringify(links);\n  var registerLink = React.useCallback(function (link) {\n    if (!links.includes(link)) {\n      setLinks(function (prev) {\n        return [].concat(_toConsumableArray(prev), [link]);\n      });\n    }\n  }, [dependencyListItem]);\n  var unregisterLink = React.useCallback(function (link) {\n    if (links.includes(link)) {\n      setLinks(function (prev) {\n        return prev.filter(function (i) {\n          return i !== link;\n        });\n      });\n    }\n  }, [dependencyListItem]);\n  var updateInk = function updateInk() {\n    var _a;\n    var linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(\".\".concat(prefixCls, \"-link-title-active\"));\n    if (linkNode && spanLinkNode.current) {\n      spanLinkNode.current.style.top = \"\".concat(linkNode.offsetTop + linkNode.clientHeight / 2 - 4.5, \"px\");\n    }\n  };\n  var getInternalCurrentAnchor = function getInternalCurrentAnchor(_links) {\n    var _offsetTop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var _bounds = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5;\n    var linkSections = [];\n    var container = getCurrentContainer();\n    _links.forEach(function (link) {\n      var sharpLinkMatch = sharpMatcherRegx.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      var target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        var top = getOffsetTop(target, container);\n        if (top < _offsetTop + _bounds) {\n          linkSections.push({\n            link: link,\n            top: top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      var maxSection = linkSections.reduce(function (prev, curr) {\n        return curr.top > prev.top ? curr : prev;\n      });\n      return maxSection.link;\n    }\n    return '';\n  };\n  var setCurrentActiveLink = function setCurrentActiveLink(link) {\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    var newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  };\n  var handleScroll = React.useCallback(function () {\n    if (animating.current) {\n      return;\n    }\n    if (typeof getCurrentAnchor === 'function') {\n      return;\n    }\n    var currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  var handleScrollTo = React.useCallback(function (link) {\n    setCurrentActiveLink(link);\n    var container = getCurrentContainer();\n    var scrollTop = getScroll(container, true);\n    var sharpLinkMatch = sharpMatcherRegx.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    var targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    var eleOffsetTop = getOffsetTop(targetElement, container);\n    var y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback: function callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  var inkClass = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-ink-ball-visible\"), activeLink), \"\".concat(prefixCls, \"-ink-ball\"));\n  var wrapperClass = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  var anchorClass = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-fixed\"), !affix && !showInkInFixed));\n  var wrapperStyle = _extends({\n    maxHeight: offsetTop ? \"calc(100vh - \".concat(offsetTop, \"px)\") : '100vh'\n  }, style);\n  var anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-ink\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  })), children));\n  React.useEffect(function () {\n    var scrollContainer = getCurrentContainer();\n    var scrollEvent = addEventListener(scrollContainer, 'scroll', handleScroll);\n    handleScroll();\n    return function () {\n      scrollEvent === null || scrollEvent === void 0 ? void 0 : scrollEvent.remove();\n    };\n  }, [dependencyListItem]);\n  React.useEffect(function () {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(function () {\n    updateInk();\n  }, [getCurrentAnchor, dependencyListItem, activeLink]);\n  var memoizedContextValue = React.useMemo(function () {\n    return {\n      registerLink: registerLink,\n      unregisterLink: unregisterLink,\n      scrollTo: handleScrollTo,\n      activeLink: activeLink,\n      onClick: onClick\n    };\n  }, [activeLink, onClick, handleScrollTo]);\n  return /*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? /*#__PURE__*/React.createElement(Affix, {\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, anchorContent) : anchorContent);\n};\nvar Anchor = function Anchor(props) {\n  var customizePrefixCls = props.prefixCls;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var anchorPrefixCls = getPrefixCls('anchor', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(AnchorContent, _extends({}, props, {\n    anchorPrefixCls: anchorPrefixCls\n  }));\n};\nexport default Anchor;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,aAAa,MAAM,WAAW;AACrC,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOC,MAAM;AACf;AACA,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACxC,IAAI,CAACD,OAAO,CAACE,cAAc,CAAC,CAAC,CAACC,MAAM,EAAE;IACpC,OAAO,CAAC;EACV;EACA,IAAIC,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;EAC1C,IAAID,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;IAC7B,IAAIN,SAAS,KAAKH,MAAM,EAAE;MACxBG,SAAS,GAAGD,OAAO,CAACQ,aAAa,CAACC,eAAe;MACjD,OAAOL,IAAI,CAACM,GAAG,GAAGT,SAAS,CAACU,SAAS;IACvC;IACA,OAAOP,IAAI,CAACM,GAAG,GAAGT,SAAS,CAACI,qBAAqB,CAAC,CAAC,CAACK,GAAG;EACzD;EACA,OAAON,IAAI,CAACM,GAAG;AACjB;AACA,IAAIE,gBAAgB,GAAG,YAAY;AACnC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,EAAE;EACN,IAAIC,SAAS,GAAGF,KAAK,CAACG,eAAe;IACnCC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,YAAY,GAAGR,KAAK,CAACS,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,YAAY;IACrDE,qBAAqB,GAAGV,KAAK,CAACW,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACjFE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,MAAM,GAAGb,KAAK,CAACa,MAAM;IACrBC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,OAAO,GAAGf,KAAK,CAACe,OAAO;IACvBC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,gBAAgB,GAAGlB,KAAK,CAACkB,gBAAgB;EAC3C,IAAIC,eAAe,GAAG1C,KAAK,CAAC2C,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAG/C,cAAc,CAAC6C,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,gBAAgB,GAAG/C,KAAK,CAAC2C,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAGnD,cAAc,CAACkD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,aAAa,GAAGnD,KAAK,CAACoD,MAAM,CAACH,UAAU,CAAC;EAC5C,IAAII,UAAU,GAAGrD,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,YAAY,GAAGtD,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIG,SAAS,GAAGvD,KAAK,CAACoD,MAAM,CAAC,KAAK,CAAC;EACnC,IAAII,iBAAiB,GAAGxD,KAAK,CAACyD,UAAU,CAACvD,aAAa,CAAC;IACrDwD,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,kBAAkB,GAAGH,iBAAiB,CAACG,kBAAkB;EAC3D,IAAIC,mBAAmB,GAAG,CAACpC,EAAE,GAAGgB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGmB,kBAAkB,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGlB,mBAAmB;EAC1K,IAAIuD,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CAAClB,KAAK,CAAC;EAC9C,IAAImB,YAAY,GAAGhE,KAAK,CAACiE,WAAW,CAAC,UAAUC,IAAI,EAAE;IACnD,IAAI,CAACrB,KAAK,CAACsB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACzBpB,QAAQ,CAAC,UAAUsB,IAAI,EAAE;QACvB,OAAO,EAAE,CAACC,MAAM,CAACzE,kBAAkB,CAACwE,IAAI,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC;MACpD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACL,kBAAkB,CAAC,CAAC;EACxB,IAAIS,cAAc,GAAGtE,KAAK,CAACiE,WAAW,CAAC,UAAUC,IAAI,EAAE;IACrD,IAAIrB,KAAK,CAACsB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACxBpB,QAAQ,CAAC,UAAUsB,IAAI,EAAE;QACvB,OAAOA,IAAI,CAACG,MAAM,CAAC,UAAUC,CAAC,EAAE;UAC9B,OAAOA,CAAC,KAAKN,IAAI;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACL,kBAAkB,CAAC,CAAC;EACxB,IAAIY,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIjD,EAAE;IACN,IAAIkD,QAAQ,GAAG,CAAClD,EAAE,GAAG6B,UAAU,CAACsB,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoD,aAAa,CAAC,GAAG,CAACP,MAAM,CAAC5C,SAAS,EAAE,oBAAoB,CAAC,CAAC;IAC3I,IAAIiD,QAAQ,IAAIpB,YAAY,CAACqB,OAAO,EAAE;MACpCrB,YAAY,CAACqB,OAAO,CAAC9C,KAAK,CAACV,GAAG,GAAG,EAAE,CAACkD,MAAM,CAACK,QAAQ,CAAC5C,SAAS,GAAG4C,QAAQ,CAACG,YAAY,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;IACxG;EACF,CAAC;EACD,IAAIC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,MAAM,EAAE;IACvE,IAAIC,UAAU,GAAGC,SAAS,CAACrE,MAAM,GAAG,CAAC,IAAIqE,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACtF,IAAIE,OAAO,GAAGF,SAAS,CAACrE,MAAM,GAAG,CAAC,IAAIqE,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACnF,IAAIG,YAAY,GAAG,EAAE;IACrB,IAAI1E,SAAS,GAAGkD,mBAAmB,CAAC,CAAC;IACrCmB,MAAM,CAACM,OAAO,CAAC,UAAUnB,IAAI,EAAE;MAC7B,IAAIoB,cAAc,GAAGjE,gBAAgB,CAACkE,IAAI,CAACrB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACsB,QAAQ,CAAC,CAAC,CAAC;MACvG,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACA,IAAIG,MAAM,GAAGC,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;MACvD,IAAIG,MAAM,EAAE;QACV,IAAItE,GAAG,GAAGX,YAAY,CAACiF,MAAM,EAAE/E,SAAS,CAAC;QACzC,IAAIS,GAAG,GAAG6D,UAAU,GAAGG,OAAO,EAAE;UAC9BC,YAAY,CAACQ,IAAI,CAAC;YAChB1B,IAAI,EAAEA,IAAI;YACV/C,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IACF,IAAIiE,YAAY,CAACxE,MAAM,EAAE;MACvB,IAAIiF,UAAU,GAAGT,YAAY,CAACU,MAAM,CAAC,UAAU1B,IAAI,EAAE2B,IAAI,EAAE;QACzD,OAAOA,IAAI,CAAC5E,GAAG,GAAGiD,IAAI,CAACjD,GAAG,GAAG4E,IAAI,GAAG3B,IAAI;MAC1C,CAAC,CAAC;MACF,OAAOyB,UAAU,CAAC3B,IAAI;IACxB;IACA,OAAO,EAAE;EACX,CAAC;EACD,IAAI8B,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC9B,IAAI,EAAE;IAC7D,IAAIf,aAAa,CAACwB,OAAO,KAAKT,IAAI,EAAE;MAClC;IACF;IACA;IACA,IAAI+B,OAAO,GAAG,OAAOxD,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACyB,IAAI,CAAC,GAAGA,IAAI;IACpFhB,aAAa,CAAC+C,OAAO,CAAC;IACtB9C,aAAa,CAACwB,OAAO,GAAGsB,OAAO;IAC/B;IACA;IACA1D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2B,IAAI,CAAC;EACpE,CAAC;EACD,IAAIgC,YAAY,GAAGlG,KAAK,CAACiE,WAAW,CAAC,YAAY;IAC/C,IAAIV,SAAS,CAACoB,OAAO,EAAE;MACrB;IACF;IACA,IAAI,OAAOlC,gBAAgB,KAAK,UAAU,EAAE;MAC1C;IACF;IACA,IAAI0D,iBAAiB,GAAGrB,wBAAwB,CAACjC,KAAK,EAAER,YAAY,KAAK6C,SAAS,GAAG7C,YAAY,GAAGP,SAAS,IAAI,CAAC,EAAEM,MAAM,CAAC;IAC3H4D,oBAAoB,CAACG,iBAAiB,CAAC;EACzC,CAAC,EAAE,CAACtC,kBAAkB,EAAExB,YAAY,EAAEP,SAAS,CAAC,CAAC;EACjD,IAAIsE,cAAc,GAAGpG,KAAK,CAACiE,WAAW,CAAC,UAAUC,IAAI,EAAE;IACrD8B,oBAAoB,CAAC9B,IAAI,CAAC;IAC1B,IAAIxD,SAAS,GAAGkD,mBAAmB,CAAC,CAAC;IACrC,IAAIyC,SAAS,GAAGlG,SAAS,CAACO,SAAS,EAAE,IAAI,CAAC;IAC1C,IAAI4E,cAAc,GAAGjE,gBAAgB,CAACkE,IAAI,CAACrB,IAAI,CAAC;IAChD,IAAI,CAACoB,cAAc,EAAE;MACnB;IACF;IACA,IAAIgB,aAAa,GAAGZ,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACgB,aAAa,EAAE;MAClB;IACF;IACA,IAAIC,YAAY,GAAG/F,YAAY,CAAC8F,aAAa,EAAE5F,SAAS,CAAC;IACzD,IAAI8F,CAAC,GAAGH,SAAS,GAAGE,YAAY;IAChCC,CAAC,IAAInE,YAAY,KAAK6C,SAAS,GAAG7C,YAAY,GAAGP,SAAS,IAAI,CAAC;IAC/DyB,SAAS,CAACoB,OAAO,GAAG,IAAI;IACxBvE,QAAQ,CAACoG,CAAC,EAAE;MACVhE,YAAY,EAAEoB,mBAAmB;MACjC6C,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5BlD,SAAS,CAACoB,OAAO,GAAG,KAAK;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtC,YAAY,EAAEP,SAAS,CAAC,CAAC;EAC7B,IAAI4E,QAAQ,GAAG5G,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0E,MAAM,CAAC5C,SAAS,EAAE,mBAAmB,CAAC,EAAEwB,UAAU,CAAC,EAAE,EAAE,CAACoB,MAAM,CAAC5C,SAAS,EAAE,WAAW,CAAC,CAAC;EACxI,IAAIkF,YAAY,GAAG7G,UAAU,CAAC,EAAE,CAACuE,MAAM,CAAC5C,SAAS,EAAE,UAAU,CAAC,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0E,MAAM,CAAC5C,SAAS,EAAE,MAAM,CAAC,EAAEiC,SAAS,KAAK,KAAK,CAAC,EAAE9B,SAAS,CAAC;EAClJ,IAAIgF,WAAW,GAAG9G,UAAU,CAAC2B,SAAS,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0E,MAAM,CAAC5C,SAAS,EAAE,QAAQ,CAAC,EAAE,CAACO,KAAK,IAAI,CAACE,cAAc,CAAC,CAAC;EACvH,IAAI2E,YAAY,GAAGnH,QAAQ,CAAC;IAC1BoH,SAAS,EAAEhF,SAAS,GAAG,eAAe,CAACuC,MAAM,CAACvC,SAAS,EAAE,KAAK,CAAC,GAAG;EACpE,CAAC,EAAED,KAAK,CAAC;EACT,IAAIkF,aAAa,GAAG,aAAa/G,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;IAC1DC,GAAG,EAAE5D,UAAU;IACfzB,SAAS,EAAE+E,YAAY;IACvB9E,KAAK,EAAEgF;EACT,CAAC,EAAE,aAAa7G,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;IACzCpF,SAAS,EAAEgF;EACb,CAAC,EAAE,aAAa5G,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;IACzCpF,SAAS,EAAE,EAAE,CAACyC,MAAM,CAAC5C,SAAS,EAAE,MAAM;EACxC,CAAC,EAAE,aAAazB,KAAK,CAACgH,aAAa,CAAC,MAAM,EAAE;IAC1CpF,SAAS,EAAE8E,QAAQ;IACnBO,GAAG,EAAE3D;EACP,CAAC,CAAC,CAAC,EAAEnB,QAAQ,CAAC,CAAC;EACfnC,KAAK,CAACkH,SAAS,CAAC,YAAY;IAC1B,IAAIC,eAAe,GAAGvD,mBAAmB,CAAC,CAAC;IAC3C,IAAIwD,WAAW,GAAGrH,gBAAgB,CAACoH,eAAe,EAAE,QAAQ,EAAEjB,YAAY,CAAC;IAC3EA,YAAY,CAAC,CAAC;IACd,OAAO,YAAY;MACjBkB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,MAAM,CAAC,CAAC;IAChF,CAAC;EACH,CAAC,EAAE,CAACxD,kBAAkB,CAAC,CAAC;EACxB7D,KAAK,CAACkH,SAAS,CAAC,YAAY;IAC1B,IAAI,OAAOzE,gBAAgB,KAAK,UAAU,EAAE;MAC1CuD,oBAAoB,CAACvD,gBAAgB,CAACU,aAAa,CAACwB,OAAO,IAAI,EAAE,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAAClC,gBAAgB,CAAC,CAAC;EACtBzC,KAAK,CAACkH,SAAS,CAAC,YAAY;IAC1BzC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAChC,gBAAgB,EAAEoB,kBAAkB,EAAEZ,UAAU,CAAC,CAAC;EACtD,IAAIqE,oBAAoB,GAAGtH,KAAK,CAACuH,OAAO,CAAC,YAAY;IACnD,OAAO;MACLvD,YAAY,EAAEA,YAAY;MAC1BM,cAAc,EAAEA,cAAc;MAC9BlE,QAAQ,EAAEgG,cAAc;MACxBnD,UAAU,EAAEA,UAAU;MACtBX,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,EAAE,CAACW,UAAU,EAAEX,OAAO,EAAE8D,cAAc,CAAC,CAAC;EACzC,OAAO,aAAapG,KAAK,CAACgH,aAAa,CAAC3G,aAAa,CAACmH,QAAQ,EAAE;IAC9DC,KAAK,EAAEH;EACT,CAAC,EAAEtF,KAAK,GAAG,aAAahC,KAAK,CAACgH,aAAa,CAAC/G,KAAK,EAAE;IACjD6B,SAAS,EAAEA,SAAS;IACpB2D,MAAM,EAAE7B;EACV,CAAC,EAAEmD,aAAa,CAAC,GAAGA,aAAa,CAAC;AACpC,CAAC;AACD,IAAIW,MAAM,GAAG,SAASA,MAAMA,CAACnG,KAAK,EAAE;EAClC,IAAIoG,kBAAkB,GAAGpG,KAAK,CAACE,SAAS;EACxC,IAAImG,kBAAkB,GAAG5H,KAAK,CAACyD,UAAU,CAACvD,aAAa,CAAC;IACtD2H,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAChD,IAAInG,eAAe,GAAGmG,YAAY,CAAC,QAAQ,EAAEF,kBAAkB,CAAC;EAChE,OAAO,aAAa3H,KAAK,CAACgH,aAAa,CAAC1F,aAAa,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACzEG,eAAe,EAAEA;EACnB,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAegG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}