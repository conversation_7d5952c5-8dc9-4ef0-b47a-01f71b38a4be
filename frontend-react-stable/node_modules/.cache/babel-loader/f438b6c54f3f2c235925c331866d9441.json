{"ast": null, "code": "/**\n * Gets the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the last element of `array`.\n * @example\n *\n * _.last([1, 2, 3]);\n * // => 3\n */\nfunction last(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? array[length - 1] : undefined;\n}\nmodule.exports = last;", "map": {"version": 3, "names": ["last", "array", "length", "undefined", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/last.js"], "sourcesContent": ["/**\n * Gets the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the last element of `array`.\n * @example\n *\n * _.last([1, 2, 3]);\n * // => 3\n */\nfunction last(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? array[length - 1] : undefined;\n}\n\nmodule.exports = last;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGD,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,GAAGC,SAAS;AAC/C;AAEAC,MAAM,CAACC,OAAO,GAAGL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script"}