{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * TODO: 4.0\n *\n * - Remove `dataSource`\n * - `size` not work with customizeInput\n * - CustomizeInput not feedback `ENTER` key since accessibility enhancement\n */\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport Select from '../select';\nimport { isValidElement } from '../_util/reactNode';\nimport warning from '../_util/warning';\nvar Option = Select.Option;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return child && child.type && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nvar AutoComplete = function AutoComplete(props, ref) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    popupClassName = props.popupClassName,\n    dropdownClassName = props.dropdownClassName,\n    children = props.children,\n    dataSource = props.dataSource;\n  var childNodes = toArray(children);\n  // ============================= Input =============================\n  var customizeInput;\n  if (childNodes.length === 1 && isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    var _childNodes = _slicedToArray(childNodes, 1);\n    customizeInput = _childNodes[0];\n  }\n  var getInputElement = customizeInput ? function () {\n    return customizeInput;\n  } : undefined;\n  // ============================ Options ============================\n  var optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(function (item) {\n      if (isValidElement(item)) {\n        return item;\n      }\n      switch (_typeof(item)) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            var optionValue = item.value;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          process.env.NODE_ENV !== \"production\" ? warning(false, 'AutoComplete', '`dataSource` is only supports type `string[] | Object[]`.') : void 0;\n          return undefined;\n      }\n    }) : [];\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!('dataSource' in props), 'AutoComplete', '`dataSource` is deprecated, please use `options` instead.') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!dropdownClassName, 'AutoComplete', '`dropdownClassName` is deprecated which will be removed in next major version. Please use `popupClassName` instead.') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'AutoComplete', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefixCls = getPrefixCls('select', customizePrefixCls);\n    return /*#__PURE__*/React.createElement(Select, _extends({\n      ref: ref\n    }, omit(props, ['dataSource']), {\n      prefixCls: prefixCls,\n      popupClassName: popupClassName || dropdownClassName,\n      className: classNames(\"\".concat(prefixCls, \"-auto-complete\"), className),\n      mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE\n    }, {\n      // Internal api\n      getInputElement: getInputElement\n    }), optionChildren);\n  });\n};\nvar RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nRefAutoComplete.Option = Option;\nexport default RefAutoComplete;", "map": {"version": 3, "names": ["_extends", "_typeof", "_slicedToArray", "classNames", "toArray", "omit", "React", "ConfigConsumer", "Select", "isValidElement", "warning", "Option", "isSelectOptionOrSelectOptGroup", "child", "type", "isSelectOption", "isSelectOptGroup", "AutoComplete", "props", "ref", "customizePrefixCls", "prefixCls", "className", "popupClassName", "dropdownClassName", "children", "dataSource", "childNodes", "customizeInput", "length", "_childNodes", "getInputElement", "undefined", "option<PERSON><PERSON><PERSON>n", "map", "item", "createElement", "key", "value", "optionValue", "text", "process", "env", "NODE_ENV", "_ref", "getPrefixCls", "concat", "mode", "SECRET_COMBOBOX_MODE_DO_NOT_USE", "RefAutoComplete", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/auto-complete/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * TODO: 4.0\n *\n * - Remove `dataSource`\n * - `size` not work with customizeInput\n * - CustomizeInput not feedback `ENTER` key since accessibility enhancement\n */\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport Select from '../select';\nimport { isValidElement } from '../_util/reactNode';\nimport warning from '../_util/warning';\nvar Option = Select.Option;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return child && child.type && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nvar AutoComplete = function AutoComplete(props, ref) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    popupClassName = props.popupClassName,\n    dropdownClassName = props.dropdownClassName,\n    children = props.children,\n    dataSource = props.dataSource;\n  var childNodes = toArray(children);\n  // ============================= Input =============================\n  var customizeInput;\n  if (childNodes.length === 1 && isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    var _childNodes = _slicedToArray(childNodes, 1);\n    customizeInput = _childNodes[0];\n  }\n  var getInputElement = customizeInput ? function () {\n    return customizeInput;\n  } : undefined;\n  // ============================ Options ============================\n  var optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(function (item) {\n      if (isValidElement(item)) {\n        return item;\n      }\n      switch (_typeof(item)) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            var optionValue = item.value;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          process.env.NODE_ENV !== \"production\" ? warning(false, 'AutoComplete', '`dataSource` is only supports type `string[] | Object[]`.') : void 0;\n          return undefined;\n      }\n    }) : [];\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!('dataSource' in props), 'AutoComplete', '`dataSource` is deprecated, please use `options` instead.') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!dropdownClassName, 'AutoComplete', '`dropdownClassName` is deprecated which will be removed in next major version. Please use `popupClassName` instead.') : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'AutoComplete', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefixCls = getPrefixCls('select', customizePrefixCls);\n    return /*#__PURE__*/React.createElement(Select, _extends({\n      ref: ref\n    }, omit(props, ['dataSource']), {\n      prefixCls: prefixCls,\n      popupClassName: popupClassName || dropdownClassName,\n      className: classNames(\"\".concat(prefixCls, \"-auto-complete\"), className),\n      mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE\n    }, {\n      // Internal api\n      getInputElement: getInputElement\n    }), optionChildren);\n  });\n};\nvar RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nRefAutoComplete.Option = Option;\nexport default RefAutoComplete;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;AAC1B,SAASC,8BAA8BA,CAACC,KAAK,EAAE;EAC7C,OAAOA,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAKD,KAAK,CAACC,IAAI,CAACC,cAAc,IAAIF,KAAK,CAACC,IAAI,CAACE,gBAAgB,CAAC;AAC1F;AACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACtCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,iBAAiB,GAAGN,KAAK,CAACM,iBAAiB;IAC3CC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,UAAU,GAAGR,KAAK,CAACQ,UAAU;EAC/B,IAAIC,UAAU,GAAGvB,OAAO,CAACqB,QAAQ,CAAC;EAClC;EACA,IAAIG,cAAc;EAClB,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,IAAIpB,cAAc,CAACkB,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAACf,8BAA8B,CAACe,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9G,IAAIG,WAAW,GAAG5B,cAAc,CAACyB,UAAU,EAAE,CAAC,CAAC;IAC/CC,cAAc,GAAGE,WAAW,CAAC,CAAC,CAAC;EACjC;EACA,IAAIC,eAAe,GAAGH,cAAc,GAAG,YAAY;IACjD,OAAOA,cAAc;EACvB,CAAC,GAAGI,SAAS;EACb;EACA,IAAIC,cAAc;EAClB;EACA,IAAIN,UAAU,CAACE,MAAM,IAAIjB,8BAA8B,CAACe,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACtEM,cAAc,GAAGR,QAAQ;EAC3B,CAAC,MAAM;IACLQ,cAAc,GAAGP,UAAU,GAAGA,UAAU,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC3D,IAAI1B,cAAc,CAAC0B,IAAI,CAAC,EAAE;QACxB,OAAOA,IAAI;MACb;MACA,QAAQlC,OAAO,CAACkC,IAAI,CAAC;QACnB,KAAK,QAAQ;UACX,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAACzB,MAAM,EAAE;YAC9C0B,GAAG,EAAEF,IAAI;YACTG,KAAK,EAAEH;UACT,CAAC,EAAEA,IAAI,CAAC;QACV,KAAK,QAAQ;UACX;YACE,IAAII,WAAW,GAAGJ,IAAI,CAACG,KAAK;YAC5B,OAAO,aAAahC,KAAK,CAAC8B,aAAa,CAACzB,MAAM,EAAE;cAC9C0B,GAAG,EAAEE,WAAW;cAChBD,KAAK,EAAEC;YACT,CAAC,EAAEJ,IAAI,CAACK,IAAI,CAAC;UACf;QACF;UACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,2DAA2D,CAAC,GAAG,KAAK,CAAC;UAC5I,OAAOsB,SAAS;MACpB;IACF,CAAC,CAAC,GAAG,EAAE;EACT;EACAS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,OAAO,CAAC,EAAE,YAAY,IAAIQ,KAAK,CAAC,EAAE,cAAc,EAAE,2DAA2D,CAAC,GAAG,KAAK,CAAC;EAC/JuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,OAAO,CAAC,CAACc,iBAAiB,EAAE,cAAc,EAAE,qHAAqH,CAAC,GAAG,KAAK,CAAC;EACnNiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,OAAO,CAAC,CAACkB,cAAc,IAAI,EAAE,MAAM,IAAIV,KAAK,CAAC,EAAE,cAAc,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;EACvM,OAAO,aAAaZ,KAAK,CAAC8B,aAAa,CAAC7B,cAAc,EAAE,IAAI,EAAE,UAAUqC,IAAI,EAAE;IAC5E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIxB,SAAS,GAAGwB,YAAY,CAAC,QAAQ,EAAEzB,kBAAkB,CAAC;IAC1D,OAAO,aAAad,KAAK,CAAC8B,aAAa,CAAC5B,MAAM,EAAER,QAAQ,CAAC;MACvDmB,GAAG,EAAEA;IACP,CAAC,EAAEd,IAAI,CAACa,KAAK,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE;MAC9BG,SAAS,EAAEA,SAAS;MACpBE,cAAc,EAAEA,cAAc,IAAIC,iBAAiB;MACnDF,SAAS,EAAEnB,UAAU,CAAC,EAAE,CAAC2C,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAEC,SAAS,CAAC;MACxEyB,IAAI,EAAEvC,MAAM,CAACwC;IACf,CAAC,EAAE;MACD;MACAjB,eAAe,EAAEA;IACnB,CAAC,CAAC,EAAEE,cAAc,CAAC;EACrB,CAAC,CAAC;AACJ,CAAC;AACD,IAAIgB,eAAe,GAAG,aAAa3C,KAAK,CAAC4C,UAAU,CAACjC,YAAY,CAAC;AACjEgC,eAAe,CAACtC,MAAM,GAAGA,MAAM;AAC/B,eAAesC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}