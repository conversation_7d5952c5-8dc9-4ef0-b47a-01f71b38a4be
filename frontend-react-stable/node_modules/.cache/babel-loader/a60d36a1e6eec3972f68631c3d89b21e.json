{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = function (calc, node, precision) {\n  var str = stringify(node, precision);\n  if (node.type === \"MathExpression\") {\n    // if calc expression couldn't be resolved to a single value, re-wrap it as\n    // a calc()\n    str = calc + \"(\" + str + \")\";\n  }\n  return str;\n};\nvar _reducer = require(\"./reducer\");\nvar order = {\n  \"*\": 0,\n  \"/\": 0,\n  \"+\": 1,\n  \"-\": 1\n};\nfunction round(value, prec) {\n  if (prec !== false) {\n    var precision = Math.pow(10, prec);\n    return Math.round(value * precision) / precision;\n  }\n  return value;\n}\nfunction stringify(node, prec) {\n  switch (node.type) {\n    case \"MathExpression\":\n      {\n        var left = node.left,\n          right = node.right,\n          op = node.operator;\n        var str = \"\";\n        if (left.type === 'MathExpression' && order[op] < order[left.operator]) str += \"(\" + stringify(left, prec) + \")\";else str += stringify(left, prec);\n        str += \" \" + node.operator + \" \";\n        if (right.type === 'MathExpression' && order[op] < order[right.operator]) {\n          str += \"(\" + stringify(right, prec) + \")\";\n        } else if (right.type === 'MathExpression' && op === \"-\" && [\"+\", \"-\"].includes(right.operator)) {\n          // fix #52 : a-(b+c) = a-b-c\n          right.operator = (0, _reducer.flip)(right.operator);\n          str += stringify(right, prec);\n        } else {\n          str += stringify(right, prec);\n        }\n        return str;\n      }\n    case \"Value\":\n      return round(node.value, prec);\n    case 'CssVariable':\n      if (node.fallback) {\n        return \"var(\" + node.value + \", \" + stringify(node.fallback, prec, true) + \")\";\n      }\n      return \"var(\" + node.value + \")\";\n    case 'Calc':\n      if (node.prefix) {\n        return \"-\" + node.prefix + \"-calc(\" + stringify(node.value, prec) + \")\";\n      }\n      return \"calc(\" + stringify(node.value, prec) + \")\";\n    default:\n      return round(node.value, prec) + node.unit;\n  }\n}\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "calc", "node", "precision", "str", "stringify", "type", "_reducer", "require", "order", "round", "prec", "Math", "pow", "left", "right", "op", "operator", "includes", "flip", "fallback", "prefix", "unit", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/dist/lib/stringifier.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports.default = function (calc, node, precision) {\n  var str = stringify(node, precision);\n\n  if (node.type === \"MathExpression\") {\n    // if calc expression couldn't be resolved to a single value, re-wrap it as\n    // a calc()\n    str = calc + \"(\" + str + \")\";\n  }\n  return str;\n};\n\nvar _reducer = require(\"./reducer\");\n\nvar order = {\n  \"*\": 0,\n  \"/\": 0,\n  \"+\": 1,\n  \"-\": 1\n};\n\nfunction round(value, prec) {\n  if (prec !== false) {\n    var precision = Math.pow(10, prec);\n    return Math.round(value * precision) / precision;\n  }\n  return value;\n}\n\nfunction stringify(node, prec) {\n  switch (node.type) {\n    case \"MathExpression\":\n      {\n        var left = node.left,\n            right = node.right,\n            op = node.operator;\n\n        var str = \"\";\n\n        if (left.type === 'MathExpression' && order[op] < order[left.operator]) str += \"(\" + stringify(left, prec) + \")\";else str += stringify(left, prec);\n\n        str += \" \" + node.operator + \" \";\n\n        if (right.type === 'MathExpression' && order[op] < order[right.operator]) {\n          str += \"(\" + stringify(right, prec) + \")\";\n        } else if (right.type === 'MathExpression' && op === \"-\" && [\"+\", \"-\"].includes(right.operator)) {\n          // fix #52 : a-(b+c) = a-b-c\n          right.operator = (0, _reducer.flip)(right.operator);\n          str += stringify(right, prec);\n        } else {\n          str += stringify(right, prec);\n        }\n\n        return str;\n      }\n    case \"Value\":\n      return round(node.value, prec);\n    case 'CssVariable':\n      if (node.fallback) {\n        return \"var(\" + node.value + \", \" + stringify(node.fallback, prec, true) + \")\";\n      }\n      return \"var(\" + node.value + \")\";\n    case 'Calc':\n      if (node.prefix) {\n        return \"-\" + node.prefix + \"-calc(\" + stringify(node.value, prec) + \")\";\n      }\n      return \"calc(\" + stringify(node.value, prec) + \")\";\n    default:\n      return round(node.value, prec) + node.unit;\n  }\n}\n\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEFD,OAAO,CAACE,OAAO,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACjD,IAAIC,GAAG,GAAGC,SAAS,CAACH,IAAI,EAAEC,SAAS,CAAC;EAEpC,IAAID,IAAI,CAACI,IAAI,KAAK,gBAAgB,EAAE;IAClC;IACA;IACAF,GAAG,GAAGH,IAAI,GAAG,GAAG,GAAGG,GAAG,GAAG,GAAG;EAC9B;EACA,OAAOA,GAAG;AACZ,CAAC;AAED,IAAIG,QAAQ,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIC,KAAK,GAAG;EACV,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE;AACP,CAAC;AAED,SAASC,KAAKA,CAACX,KAAK,EAAEY,IAAI,EAAE;EAC1B,IAAIA,IAAI,KAAK,KAAK,EAAE;IAClB,IAAIR,SAAS,GAAGS,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEF,IAAI,CAAC;IAClC,OAAOC,IAAI,CAACF,KAAK,CAACX,KAAK,GAAGI,SAAS,CAAC,GAAGA,SAAS;EAClD;EACA,OAAOJ,KAAK;AACd;AAEA,SAASM,SAASA,CAACH,IAAI,EAAES,IAAI,EAAE;EAC7B,QAAQT,IAAI,CAACI,IAAI;IACf,KAAK,gBAAgB;MACnB;QACE,IAAIQ,IAAI,GAAGZ,IAAI,CAACY,IAAI;UAChBC,KAAK,GAAGb,IAAI,CAACa,KAAK;UAClBC,EAAE,GAAGd,IAAI,CAACe,QAAQ;QAEtB,IAAIb,GAAG,GAAG,EAAE;QAEZ,IAAIU,IAAI,CAACR,IAAI,KAAK,gBAAgB,IAAIG,KAAK,CAACO,EAAE,CAAC,GAAGP,KAAK,CAACK,IAAI,CAACG,QAAQ,CAAC,EAAEb,GAAG,IAAI,GAAG,GAAGC,SAAS,CAACS,IAAI,EAAEH,IAAI,CAAC,GAAG,GAAG,CAAC,KAAKP,GAAG,IAAIC,SAAS,CAACS,IAAI,EAAEH,IAAI,CAAC;QAElJP,GAAG,IAAI,GAAG,GAAGF,IAAI,CAACe,QAAQ,GAAG,GAAG;QAEhC,IAAIF,KAAK,CAACT,IAAI,KAAK,gBAAgB,IAAIG,KAAK,CAACO,EAAE,CAAC,GAAGP,KAAK,CAACM,KAAK,CAACE,QAAQ,CAAC,EAAE;UACxEb,GAAG,IAAI,GAAG,GAAGC,SAAS,CAACU,KAAK,EAAEJ,IAAI,CAAC,GAAG,GAAG;QAC3C,CAAC,MAAM,IAAII,KAAK,CAACT,IAAI,KAAK,gBAAgB,IAAIU,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACE,QAAQ,CAACH,KAAK,CAACE,QAAQ,CAAC,EAAE;UAC/F;UACAF,KAAK,CAACE,QAAQ,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAACY,IAAI,EAAEJ,KAAK,CAACE,QAAQ,CAAC;UACnDb,GAAG,IAAIC,SAAS,CAACU,KAAK,EAAEJ,IAAI,CAAC;QAC/B,CAAC,MAAM;UACLP,GAAG,IAAIC,SAAS,CAACU,KAAK,EAAEJ,IAAI,CAAC;QAC/B;QAEA,OAAOP,GAAG;MACZ;IACF,KAAK,OAAO;MACV,OAAOM,KAAK,CAACR,IAAI,CAACH,KAAK,EAAEY,IAAI,CAAC;IAChC,KAAK,aAAa;MAChB,IAAIT,IAAI,CAACkB,QAAQ,EAAE;QACjB,OAAO,MAAM,GAAGlB,IAAI,CAACH,KAAK,GAAG,IAAI,GAAGM,SAAS,CAACH,IAAI,CAACkB,QAAQ,EAAET,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG;MAChF;MACA,OAAO,MAAM,GAAGT,IAAI,CAACH,KAAK,GAAG,GAAG;IAClC,KAAK,MAAM;MACT,IAAIG,IAAI,CAACmB,MAAM,EAAE;QACf,OAAO,GAAG,GAAGnB,IAAI,CAACmB,MAAM,GAAG,QAAQ,GAAGhB,SAAS,CAACH,IAAI,CAACH,KAAK,EAAEY,IAAI,CAAC,GAAG,GAAG;MACzE;MACA,OAAO,OAAO,GAAGN,SAAS,CAACH,IAAI,CAACH,KAAK,EAAEY,IAAI,CAAC,GAAG,GAAG;IACpD;MACE,OAAOD,KAAK,CAACR,IAAI,CAACH,KAAK,EAAEY,IAAI,CAAC,GAAGT,IAAI,CAACoB,IAAI;EAC9C;AACF;AAEAC,MAAM,CAACzB,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}