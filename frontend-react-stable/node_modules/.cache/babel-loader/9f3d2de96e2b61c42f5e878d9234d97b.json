{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from \"./Input\";\nimport { getTitle } from \"../utils/commonUtil\";\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]);\n\n  // Not show text when closed expect combobox mode\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n\n  // Get title\n  var title = getTitle(item);\n  var renderPlaceholder = function renderPlaceholder() {\n    if (item) {\n      return null;\n    }\n    var hiddenStyle = hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hiddenStyle\n    }, placeholder);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: title\n    // 当 Select 已经选中选项时，还需 selection 隐藏但留在原地占位\n    // https://github.com/ant-design/ant-design/issues/27688\n    // https://github.com/ant-design/ant-design/issues/41530\n    ,\n\n    style: hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined\n  }, item.label) : null, renderPlaceholder());\n};\nexport default SingleSelector;", "map": {"version": 3, "names": ["_slicedToArray", "React", "pickAttrs", "Input", "getTitle", "SingleSelector", "props", "inputElement", "prefixCls", "id", "inputRef", "disabled", "autoFocus", "autoComplete", "activeDescendantId", "mode", "open", "values", "placeholder", "tabIndex", "showSearch", "searchValue", "activeValue", "max<PERSON><PERSON><PERSON>", "onInputKeyDown", "onInputMouseDown", "onInputChange", "onInputPaste", "onInputCompositionStart", "onInputCompositionEnd", "_React$useState", "useState", "_React$useState2", "inputChanged", "setInputChanged", "combobox", "inputEditable", "item", "inputValue", "useEffect", "hasTextInput", "title", "renderPlaceholder", "hiddenStyle", "visibility", "undefined", "createElement", "className", "concat", "style", "Fragment", "ref", "editable", "value", "onKeyDown", "onMouseDown", "onChange", "e", "onPaste", "onCompositionStart", "onCompositionEnd", "attrs", "label"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/Selector/SingleSelector.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from \"./Input\";\nimport { getTitle } from \"../utils/commonUtil\";\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]);\n\n  // Not show text when closed expect combobox mode\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n\n  // Get title\n  var title = getTitle(item);\n  var renderPlaceholder = function renderPlaceholder() {\n    if (item) {\n      return null;\n    }\n    var hiddenStyle = hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hiddenStyle\n    }, placeholder);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: title\n    // 当 Select 已经选中选项时，还需 selection 隐藏但留在原地占位\n    // https://github.com/ant-design/ant-design/issues/27688\n    // https://github.com/ant-design/ant-design/issues/41530\n    ,\n    style: hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined\n  }, item.label) : null, renderPlaceholder());\n};\nexport default SingleSelector;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACnCC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,cAAc,GAAGlB,KAAK,CAACkB,cAAc;IACrCC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,aAAa,GAAGpB,KAAK,CAACoB,aAAa;IACnCC,YAAY,GAAGrB,KAAK,CAACqB,YAAY;IACjCC,uBAAuB,GAAGtB,KAAK,CAACsB,uBAAuB;IACvDC,qBAAqB,GAAGvB,KAAK,CAACuB,qBAAqB;EACrD,IAAIC,eAAe,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,QAAQ,GAAGpB,IAAI,KAAK,UAAU;EAClC,IAAIqB,aAAa,GAAGD,QAAQ,IAAIf,UAAU;EAC1C,IAAIiB,IAAI,GAAGpB,MAAM,CAAC,CAAC,CAAC;EACpB,IAAIqB,UAAU,GAAGjB,WAAW,IAAI,EAAE;EAClC,IAAIc,QAAQ,IAAIb,WAAW,IAAI,CAACW,YAAY,EAAE;IAC5CK,UAAU,GAAGhB,WAAW;EAC1B;EACArB,KAAK,CAACsC,SAAS,CAAC,YAAY;IAC1B,IAAIJ,QAAQ,EAAE;MACZD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEb,WAAW,CAAC,CAAC;;EAE3B;EACA,IAAIkB,YAAY,GAAGzB,IAAI,KAAK,UAAU,IAAI,CAACC,IAAI,IAAI,CAACI,UAAU,GAAG,KAAK,GAAG,CAAC,CAACkB,UAAU;;EAErF;EACA,IAAIG,KAAK,GAAGrC,QAAQ,CAACiC,IAAI,CAAC;EAC1B,IAAIK,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIL,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAIM,WAAW,GAAGH,YAAY,GAAG;MAC/BI,UAAU,EAAE;IACd,CAAC,GAAGC,SAAS;IACb,OAAO,aAAa5C,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACxC,SAAS,EAAE,wBAAwB,CAAC;MACzDyC,KAAK,EAAEN;IACT,CAAC,EAAEzB,WAAW,CAAC;EACjB,CAAC;EACD,OAAO,aAAajB,KAAK,CAAC6C,aAAa,CAAC7C,KAAK,CAACiD,QAAQ,EAAE,IAAI,EAAE,aAAajD,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;IACrGC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACxC,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAE,aAAaP,KAAK,CAAC6C,aAAa,CAAC3C,KAAK,EAAE;IACzCgD,GAAG,EAAEzC,QAAQ;IACbF,SAAS,EAAEA,SAAS;IACpBC,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAEA,IAAI;IACVT,YAAY,EAAEA,YAAY;IAC1BI,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BuC,QAAQ,EAAEhB,aAAa;IACvBtB,kBAAkB,EAAEA,kBAAkB;IACtCuC,KAAK,EAAEf,UAAU;IACjBgB,SAAS,EAAE9B,cAAc;IACzB+B,WAAW,EAAE9B,gBAAgB;IAC7B+B,QAAQ,EAAE,SAASA,QAAQA,CAACC,CAAC,EAAE;MAC7BvB,eAAe,CAAC,IAAI,CAAC;MACrBR,aAAa,CAAC+B,CAAC,CAAC;IAClB,CAAC;IACDC,OAAO,EAAE/B,YAAY;IACrBgC,kBAAkB,EAAE/B,uBAAuB;IAC3CgC,gBAAgB,EAAE/B,qBAAqB;IACvCV,QAAQ,EAAEA,QAAQ;IAClB0C,KAAK,EAAE3D,SAAS,CAACI,KAAK,EAAE,IAAI,CAAC;IAC7BiB,SAAS,EAAEY,QAAQ,GAAGZ,SAAS,GAAGsB;EACpC,CAAC,CAAC,CAAC,EAAE,CAACV,QAAQ,IAAIE,IAAI,GAAG,aAAapC,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;IAChEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACxC,SAAS,EAAE,iBAAiB,CAAC;IAClDiC,KAAK,EAAEA;IACP;IACA;IACA;IAAA;;IAEAQ,KAAK,EAAET,YAAY,GAAG;MACpBI,UAAU,EAAE;IACd,CAAC,GAAGC;EACN,CAAC,EAAER,IAAI,CAACyB,KAAK,CAAC,GAAG,IAAI,EAAEpB,iBAAiB,CAAC,CAAC,CAAC;AAC7C,CAAC;AACD,eAAerC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}