{"ast": null, "code": "// This icon file is generated automatically.\nvar DeleteRowOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M819.8 512l102.4-122.9a8.06 8.06 0 00-6.1-13.2h-54.7c-2.4 0-4.6 1.1-6.1 2.9L782 466.7l-73.1-87.8a8.1 8.1 0 00-6.1-2.9H648c-1.9 0-3.7.7-5.1 1.9a7.97 7.97 0 00-1 11.3L744.2 512 641.8 634.9a8.06 8.06 0 006.1 13.2h54.7c2.4 0 4.6-1.1 6.1-2.9l73.1-87.8 73.1 87.8a8.1 8.1 0 006.1 2.9h55c1.9 0 3.7-.7 5.1-1.9 3.4-2.8 3.9-7.9 1-11.3L819.8 512zM536 464H120c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8h416c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8zm-84 204h-60c-3.3 0-6 2.7-6 6v166H136c-3.3 0-6 2.7-6 6v60c0 3.3 2.7 6 6 6h292c16.6 0 30-13.4 30-30V674c0-3.3-2.7-6-6-6zM136 184h250v166c0 3.3 2.7 6 6 6h60c3.3 0 6-2.7 6-6V142c0-16.6-13.4-30-30-30H136c-3.3 0-6 2.7-6 6v60c0 3.3 2.7 6 6 6z\"\n      }\n    }]\n  },\n  \"name\": \"delete-row\",\n  \"theme\": \"outlined\"\n};\nexport default DeleteRowOutlined;", "map": {"version": 3, "names": ["DeleteRowOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/DeleteRowOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar DeleteRowOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M819.8 512l102.4-122.9a8.06 8.06 0 00-6.1-13.2h-54.7c-2.4 0-4.6 1.1-6.1 2.9L782 466.7l-73.1-87.8a8.1 8.1 0 00-6.1-2.9H648c-1.9 0-3.7.7-5.1 1.9a7.97 7.97 0 00-1 11.3L744.2 512 641.8 634.9a8.06 8.06 0 006.1 13.2h54.7c2.4 0 4.6-1.1 6.1-2.9l73.1-87.8 73.1 87.8a8.1 8.1 0 006.1 2.9h55c1.9 0 3.7-.7 5.1-1.9 3.4-2.8 3.9-7.9 1-11.3L819.8 512zM536 464H120c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8h416c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8zm-84 204h-60c-3.3 0-6 2.7-6 6v166H136c-3.3 0-6 2.7-6 6v60c0 3.3 2.7 6 6 6h292c16.6 0 30-13.4 30-30V674c0-3.3-2.7-6-6-6zM136 184h250v166c0 3.3 2.7 6 6 6h60c3.3 0 6-2.7 6-6V142c0-16.6-13.4-30-30-30H136c-3.3 0-6 2.7-6 6v60c0 3.3 2.7 6 6 6z\" } }] }, \"name\": \"delete-row\", \"theme\": \"outlined\" };\nexport default DeleteRowOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,iBAAiB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAspB;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,YAAY;EAAE,OAAO,EAAE;AAAW,CAAC;AACv7B,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}