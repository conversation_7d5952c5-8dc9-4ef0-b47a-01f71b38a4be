{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.flip = flip;\nvar _convert = require(\"./convert\");\nvar _convert2 = _interopRequireDefault(_convert);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction reduce(node, precision) {\n  if (node.type === \"MathExpression\") return reduceMathExpression(node, precision);\n  if (node.type === \"Calc\") return reduce(node.value, precision);\n  return node;\n}\nfunction isEqual(left, right) {\n  return left.type === right.type && left.value === right.value;\n}\nfunction isValueType(type) {\n  switch (type) {\n    case 'LengthValue':\n    case 'AngleValue':\n    case 'TimeValue':\n    case 'FrequencyValue':\n    case 'ResolutionValue':\n    case 'EmValue':\n    case 'ExValue':\n    case 'ChValue':\n    case 'RemValue':\n    case 'VhValue':\n    case 'VwValue':\n    case 'VminValue':\n    case 'VmaxValue':\n    case 'PercentageValue':\n    case 'Value':\n      return true;\n  }\n  return false;\n}\nfunction convertMathExpression(node, precision) {\n  var nodes = (0, _convert2.default)(node.left, node.right, precision);\n  var left = reduce(nodes.left, precision);\n  var right = reduce(nodes.right, precision);\n  if (left.type === \"MathExpression\" && right.type === \"MathExpression\") {\n    if (left.operator === '/' && right.operator === '*' || left.operator === '-' && right.operator === '+' || left.operator === '*' && right.operator === '/' || left.operator === '+' && right.operator === '-') {\n      if (isEqual(left.right, right.right)) nodes = (0, _convert2.default)(left.left, right.left, precision);else if (isEqual(left.right, right.left)) nodes = (0, _convert2.default)(left.left, right.right, precision);\n      left = reduce(nodes.left, precision);\n      right = reduce(nodes.right, precision);\n    }\n  }\n  node.left = left;\n  node.right = right;\n  return node;\n}\nfunction flip(operator) {\n  return operator === '+' ? '-' : '+';\n}\nfunction flipValue(node) {\n  if (isValueType(node.type)) node.value = -node.value;else if (node.type == 'MathExpression') {\n    node.left = flipValue(node.left);\n    node.right = flipValue(node.right);\n  }\n  return node;\n}\nfunction reduceAddSubExpression(node, precision) {\n  var _node = node,\n    left = _node.left,\n    right = _node.right,\n    op = _node.operator;\n  if (left.type === 'CssVariable' || right.type === 'CssVariable') return node;\n\n  // something + 0 => something\n  // something - 0 => something\n  if (right.value === 0) return left;\n\n  // 0 + something => something\n  if (left.value === 0 && op === \"+\") return right;\n\n  // 0 - something => -something\n  if (left.value === 0 && op === \"-\") return flipValue(right);\n\n  // value + value\n  // value - value\n  if (left.type === right.type && isValueType(left.type)) {\n    node = Object.assign({}, left);\n    if (op === \"+\") node.value = left.value + right.value;else node.value = left.value - right.value;\n  }\n\n  // value <op> (expr)\n  if (isValueType(left.type) && (right.operator === '+' || right.operator === '-') && right.type === 'MathExpression') {\n    // value + (value + something) => (value + value) + something\n    // value + (value - something) => (value + value) - something\n    // value - (value + something) => (value - value) - something\n    // value - (value - something) => (value - value) + something\n    if (left.type === right.left.type) {\n      node = Object.assign({}, node);\n      node.left = reduce({\n        type: 'MathExpression',\n        operator: op,\n        left: left,\n        right: right.left\n      }, precision);\n      node.right = right.right;\n      node.operator = op === '-' ? flip(right.operator) : right.operator;\n      return reduce(node, precision);\n    }\n    // value + (something + value) => (value + value) + something\n    // value + (something - value) => (value - value) + something\n    // value - (something + value) => (value - value) - something\n    // value - (something - value) => (value + value) - something\n    else if (left.type === right.right.type) {\n      node = Object.assign({}, node);\n      node.left = reduce({\n        type: 'MathExpression',\n        operator: op === '-' ? flip(right.operator) : right.operator,\n        left: left,\n        right: right.right\n      }, precision);\n      node.right = right.left;\n      return reduce(node, precision);\n    }\n  }\n\n  // (expr) <op> value\n  if (left.type === 'MathExpression' && (left.operator === '+' || left.operator === '-') && isValueType(right.type)) {\n    // (value + something) + value => (value + value) + something\n    // (value - something) + value => (value + value) - something\n    // (value + something) - value => (value - value) + something\n    // (value - something) - value => (value - value) - something\n    if (right.type === left.left.type) {\n      node = Object.assign({}, left);\n      node.left = reduce({\n        type: 'MathExpression',\n        operator: op,\n        left: left.left,\n        right: right\n      }, precision);\n      return reduce(node, precision);\n    }\n    // (something + value) + value => something + (value + value)\n    // (something - value1) + value2 => something - (value2 - value1)\n    // (something + value) - value => something + (value - value)\n    // (something - value) - value => something - (value + value)\n    else if (right.type === left.right.type) {\n      node = Object.assign({}, left);\n      if (left.operator === '-') {\n        node.right = reduce({\n          type: 'MathExpression',\n          operator: op === '-' ? '+' : '-',\n          left: right,\n          right: left.right\n        }, precision);\n        node.operator = op === '-' ? '-' : '+';\n      } else {\n        node.right = reduce({\n          type: 'MathExpression',\n          operator: op,\n          left: left.right,\n          right: right\n        }, precision);\n      }\n      if (node.right.value < 0) {\n        node.right.value *= -1;\n        node.operator = node.operator === '-' ? '+' : '-';\n      }\n      return reduce(node, precision);\n    }\n  }\n  return node;\n}\nfunction reduceDivisionExpression(node, precision) {\n  if (!isValueType(node.right.type)) return node;\n  if (node.right.type !== 'Value') throw new Error(\"Cannot divide by \\\"\" + node.right.unit + \"\\\", number expected\");\n  if (node.right.value === 0) throw new Error('Cannot divide by zero');\n\n  // (expr) / value\n  if (node.left.type === 'MathExpression') {\n    if (isValueType(node.left.left.type) && isValueType(node.left.right.type)) {\n      node.left.left.value /= node.right.value;\n      node.left.right.value /= node.right.value;\n      return reduce(node.left, precision);\n    }\n    return node;\n  }\n  // something / value\n  else if (isValueType(node.left.type)) {\n    node.left.value /= node.right.value;\n    return node.left;\n  }\n  return node;\n}\nfunction reduceMultiplicationExpression(node) {\n  // (expr) * value\n  if (node.left.type === 'MathExpression' && node.right.type === 'Value') {\n    if (isValueType(node.left.left.type) && isValueType(node.left.right.type)) {\n      node.left.left.value *= node.right.value;\n      node.left.right.value *= node.right.value;\n      return node.left;\n    }\n  }\n  // something * value\n  else if (isValueType(node.left.type) && node.right.type === 'Value') {\n    node.left.value *= node.right.value;\n    return node.left;\n  }\n  // value * (expr)\n  else if (node.left.type === 'Value' && node.right.type === 'MathExpression') {\n    if (isValueType(node.right.left.type) && isValueType(node.right.right.type)) {\n      node.right.left.value *= node.left.value;\n      node.right.right.value *= node.left.value;\n      return node.right;\n    }\n  }\n  // value * something\n  else if (node.left.type === 'Value' && isValueType(node.right.type)) {\n    node.right.value *= node.left.value;\n    return node.right;\n  }\n  return node;\n}\nfunction reduceMathExpression(node, precision) {\n  node = convertMathExpression(node, precision);\n  switch (node.operator) {\n    case \"+\":\n    case \"-\":\n      return reduceAddSubExpression(node, precision);\n    case \"/\":\n      return reduceDivisionExpression(node, precision);\n    case \"*\":\n      return reduceMultiplicationExpression(node);\n  }\n  return node;\n}\nexports.default = reduce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "flip", "_convert", "require", "_convert2", "_interopRequireDefault", "obj", "__esModule", "default", "reduce", "node", "precision", "type", "reduceMathExpression", "isEqual", "left", "right", "isValueType", "convertMathExpression", "nodes", "operator", "flipValue", "reduceAddSubExpression", "_node", "op", "assign", "reduceDivisionExpression", "Error", "unit", "reduceMultiplicationExpression"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/dist/lib/reducer.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.flip = flip;\n\nvar _convert = require(\"./convert\");\n\nvar _convert2 = _interopRequireDefault(_convert);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction reduce(node, precision) {\n  if (node.type === \"MathExpression\") return reduceMathExpression(node, precision);\n  if (node.type === \"Calc\") return reduce(node.value, precision);\n\n  return node;\n}\n\nfunction isEqual(left, right) {\n  return left.type === right.type && left.value === right.value;\n}\n\nfunction isValueType(type) {\n  switch (type) {\n    case 'LengthValue':\n    case 'AngleValue':\n    case 'TimeValue':\n    case 'FrequencyValue':\n    case 'ResolutionValue':\n    case 'EmValue':\n    case 'ExValue':\n    case 'ChValue':\n    case 'RemValue':\n    case 'VhValue':\n    case 'VwValue':\n    case 'VminValue':\n    case 'VmaxValue':\n    case 'PercentageValue':\n    case 'Value':\n      return true;\n  }\n  return false;\n}\n\nfunction convertMathExpression(node, precision) {\n  var nodes = (0, _convert2.default)(node.left, node.right, precision);\n  var left = reduce(nodes.left, precision);\n  var right = reduce(nodes.right, precision);\n\n  if (left.type === \"MathExpression\" && right.type === \"MathExpression\") {\n\n    if (left.operator === '/' && right.operator === '*' || left.operator === '-' && right.operator === '+' || left.operator === '*' && right.operator === '/' || left.operator === '+' && right.operator === '-') {\n\n      if (isEqual(left.right, right.right)) nodes = (0, _convert2.default)(left.left, right.left, precision);else if (isEqual(left.right, right.left)) nodes = (0, _convert2.default)(left.left, right.right, precision);\n\n      left = reduce(nodes.left, precision);\n      right = reduce(nodes.right, precision);\n    }\n  }\n\n  node.left = left;\n  node.right = right;\n  return node;\n}\n\nfunction flip(operator) {\n  return operator === '+' ? '-' : '+';\n}\n\nfunction flipValue(node) {\n  if (isValueType(node.type)) node.value = -node.value;else if (node.type == 'MathExpression') {\n    node.left = flipValue(node.left);\n    node.right = flipValue(node.right);\n  }\n  return node;\n}\n\nfunction reduceAddSubExpression(node, precision) {\n  var _node = node,\n      left = _node.left,\n      right = _node.right,\n      op = _node.operator;\n\n\n  if (left.type === 'CssVariable' || right.type === 'CssVariable') return node;\n\n  // something + 0 => something\n  // something - 0 => something\n  if (right.value === 0) return left;\n\n  // 0 + something => something\n  if (left.value === 0 && op === \"+\") return right;\n\n  // 0 - something => -something\n  if (left.value === 0 && op === \"-\") return flipValue(right);\n\n  // value + value\n  // value - value\n  if (left.type === right.type && isValueType(left.type)) {\n    node = Object.assign({}, left);\n    if (op === \"+\") node.value = left.value + right.value;else node.value = left.value - right.value;\n  }\n\n  // value <op> (expr)\n  if (isValueType(left.type) && (right.operator === '+' || right.operator === '-') && right.type === 'MathExpression') {\n    // value + (value + something) => (value + value) + something\n    // value + (value - something) => (value + value) - something\n    // value - (value + something) => (value - value) - something\n    // value - (value - something) => (value - value) + something\n    if (left.type === right.left.type) {\n      node = Object.assign({}, node);\n      node.left = reduce({\n        type: 'MathExpression',\n        operator: op,\n        left: left,\n        right: right.left\n      }, precision);\n      node.right = right.right;\n      node.operator = op === '-' ? flip(right.operator) : right.operator;\n      return reduce(node, precision);\n    }\n    // value + (something + value) => (value + value) + something\n    // value + (something - value) => (value - value) + something\n    // value - (something + value) => (value - value) - something\n    // value - (something - value) => (value + value) - something\n    else if (left.type === right.right.type) {\n        node = Object.assign({}, node);\n        node.left = reduce({\n          type: 'MathExpression',\n          operator: op === '-' ? flip(right.operator) : right.operator,\n          left: left,\n          right: right.right\n        }, precision);\n        node.right = right.left;\n        return reduce(node, precision);\n      }\n  }\n\n  // (expr) <op> value\n  if (left.type === 'MathExpression' && (left.operator === '+' || left.operator === '-') && isValueType(right.type)) {\n    // (value + something) + value => (value + value) + something\n    // (value - something) + value => (value + value) - something\n    // (value + something) - value => (value - value) + something\n    // (value - something) - value => (value - value) - something\n    if (right.type === left.left.type) {\n      node = Object.assign({}, left);\n      node.left = reduce({\n        type: 'MathExpression',\n        operator: op,\n        left: left.left,\n        right: right\n      }, precision);\n      return reduce(node, precision);\n    }\n    // (something + value) + value => something + (value + value)\n    // (something - value1) + value2 => something - (value2 - value1)\n    // (something + value) - value => something + (value - value)\n    // (something - value) - value => something - (value + value)\n    else if (right.type === left.right.type) {\n        node = Object.assign({}, left);\n        if (left.operator === '-') {\n          node.right = reduce({\n            type: 'MathExpression',\n            operator: op === '-' ? '+' : '-',\n            left: right,\n            right: left.right\n          }, precision);\n          node.operator = op === '-' ? '-' : '+';\n        } else {\n          node.right = reduce({\n            type: 'MathExpression',\n            operator: op,\n            left: left.right,\n            right: right\n          }, precision);\n        }\n        if (node.right.value < 0) {\n          node.right.value *= -1;\n          node.operator = node.operator === '-' ? '+' : '-';\n        }\n        return reduce(node, precision);\n      }\n  }\n  return node;\n}\n\nfunction reduceDivisionExpression(node, precision) {\n  if (!isValueType(node.right.type)) return node;\n\n  if (node.right.type !== 'Value') throw new Error(\"Cannot divide by \\\"\" + node.right.unit + \"\\\", number expected\");\n\n  if (node.right.value === 0) throw new Error('Cannot divide by zero');\n\n  // (expr) / value\n  if (node.left.type === 'MathExpression') {\n    if (isValueType(node.left.left.type) && isValueType(node.left.right.type)) {\n      node.left.left.value /= node.right.value;\n      node.left.right.value /= node.right.value;\n      return reduce(node.left, precision);\n    }\n    return node;\n  }\n  // something / value\n  else if (isValueType(node.left.type)) {\n      node.left.value /= node.right.value;\n      return node.left;\n    }\n  return node;\n}\n\nfunction reduceMultiplicationExpression(node) {\n  // (expr) * value\n  if (node.left.type === 'MathExpression' && node.right.type === 'Value') {\n    if (isValueType(node.left.left.type) && isValueType(node.left.right.type)) {\n      node.left.left.value *= node.right.value;\n      node.left.right.value *= node.right.value;\n      return node.left;\n    }\n  }\n  // something * value\n  else if (isValueType(node.left.type) && node.right.type === 'Value') {\n      node.left.value *= node.right.value;\n      return node.left;\n    }\n    // value * (expr)\n    else if (node.left.type === 'Value' && node.right.type === 'MathExpression') {\n        if (isValueType(node.right.left.type) && isValueType(node.right.right.type)) {\n          node.right.left.value *= node.left.value;\n          node.right.right.value *= node.left.value;\n          return node.right;\n        }\n      }\n      // value * something\n      else if (node.left.type === 'Value' && isValueType(node.right.type)) {\n          node.right.value *= node.left.value;\n          return node.right;\n        }\n  return node;\n}\n\nfunction reduceMathExpression(node, precision) {\n  node = convertMathExpression(node, precision);\n\n  switch (node.operator) {\n    case \"+\":\n    case \"-\":\n      return reduceAddSubExpression(node, precision);\n    case \"/\":\n      return reduceDivisionExpression(node, precision);\n    case \"*\":\n      return reduceMultiplicationExpression(node);\n  }\n  return node;\n}\n\nexports.default = reduce;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGA,IAAI;AAEnB,IAAIC,QAAQ,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIC,SAAS,GAAGC,sBAAsB,CAACH,QAAQ,CAAC;AAEhD,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC/B,IAAID,IAAI,CAACE,IAAI,KAAK,gBAAgB,EAAE,OAAOC,oBAAoB,CAACH,IAAI,EAAEC,SAAS,CAAC;EAChF,IAAID,IAAI,CAACE,IAAI,KAAK,MAAM,EAAE,OAAOH,MAAM,CAACC,IAAI,CAACV,KAAK,EAAEW,SAAS,CAAC;EAE9D,OAAOD,IAAI;AACb;AAEA,SAASI,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC5B,OAAOD,IAAI,CAACH,IAAI,KAAKI,KAAK,CAACJ,IAAI,IAAIG,IAAI,CAACf,KAAK,KAAKgB,KAAK,CAAChB,KAAK;AAC/D;AAEA,SAASiB,WAAWA,CAACL,IAAI,EAAE;EACzB,QAAQA,IAAI;IACV,KAAK,aAAa;IAClB,KAAK,YAAY;IACjB,KAAK,WAAW;IAChB,KAAK,gBAAgB;IACrB,KAAK,iBAAiB;IACtB,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,WAAW;IAChB,KAAK,WAAW;IAChB,KAAK,iBAAiB;IACtB,KAAK,OAAO;MACV,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd;AAEA,SAASM,qBAAqBA,CAACR,IAAI,EAAEC,SAAS,EAAE;EAC9C,IAAIQ,KAAK,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACI,OAAO,EAAEE,IAAI,CAACK,IAAI,EAAEL,IAAI,CAACM,KAAK,EAAEL,SAAS,CAAC;EACpE,IAAII,IAAI,GAAGN,MAAM,CAACU,KAAK,CAACJ,IAAI,EAAEJ,SAAS,CAAC;EACxC,IAAIK,KAAK,GAAGP,MAAM,CAACU,KAAK,CAACH,KAAK,EAAEL,SAAS,CAAC;EAE1C,IAAII,IAAI,CAACH,IAAI,KAAK,gBAAgB,IAAII,KAAK,CAACJ,IAAI,KAAK,gBAAgB,EAAE;IAErE,IAAIG,IAAI,CAACK,QAAQ,KAAK,GAAG,IAAIJ,KAAK,CAACI,QAAQ,KAAK,GAAG,IAAIL,IAAI,CAACK,QAAQ,KAAK,GAAG,IAAIJ,KAAK,CAACI,QAAQ,KAAK,GAAG,IAAIL,IAAI,CAACK,QAAQ,KAAK,GAAG,IAAIJ,KAAK,CAACI,QAAQ,KAAK,GAAG,IAAIL,IAAI,CAACK,QAAQ,KAAK,GAAG,IAAIJ,KAAK,CAACI,QAAQ,KAAK,GAAG,EAAE;MAE5M,IAAIN,OAAO,CAACC,IAAI,CAACC,KAAK,EAAEA,KAAK,CAACA,KAAK,CAAC,EAAEG,KAAK,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACI,OAAO,EAAEO,IAAI,CAACA,IAAI,EAAEC,KAAK,CAACD,IAAI,EAAEJ,SAAS,CAAC,CAAC,KAAK,IAAIG,OAAO,CAACC,IAAI,CAACC,KAAK,EAAEA,KAAK,CAACD,IAAI,CAAC,EAAEI,KAAK,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACI,OAAO,EAAEO,IAAI,CAACA,IAAI,EAAEC,KAAK,CAACA,KAAK,EAAEL,SAAS,CAAC;MAElNI,IAAI,GAAGN,MAAM,CAACU,KAAK,CAACJ,IAAI,EAAEJ,SAAS,CAAC;MACpCK,KAAK,GAAGP,MAAM,CAACU,KAAK,CAACH,KAAK,EAAEL,SAAS,CAAC;IACxC;EACF;EAEAD,IAAI,CAACK,IAAI,GAAGA,IAAI;EAChBL,IAAI,CAACM,KAAK,GAAGA,KAAK;EAClB,OAAON,IAAI;AACb;AAEA,SAAST,IAAIA,CAACmB,QAAQ,EAAE;EACtB,OAAOA,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACrC;AAEA,SAASC,SAASA,CAACX,IAAI,EAAE;EACvB,IAAIO,WAAW,CAACP,IAAI,CAACE,IAAI,CAAC,EAAEF,IAAI,CAACV,KAAK,GAAG,CAACU,IAAI,CAACV,KAAK,CAAC,KAAK,IAAIU,IAAI,CAACE,IAAI,IAAI,gBAAgB,EAAE;IAC3FF,IAAI,CAACK,IAAI,GAAGM,SAAS,CAACX,IAAI,CAACK,IAAI,CAAC;IAChCL,IAAI,CAACM,KAAK,GAAGK,SAAS,CAACX,IAAI,CAACM,KAAK,CAAC;EACpC;EACA,OAAON,IAAI;AACb;AAEA,SAASY,sBAAsBA,CAACZ,IAAI,EAAEC,SAAS,EAAE;EAC/C,IAAIY,KAAK,GAAGb,IAAI;IACZK,IAAI,GAAGQ,KAAK,CAACR,IAAI;IACjBC,KAAK,GAAGO,KAAK,CAACP,KAAK;IACnBQ,EAAE,GAAGD,KAAK,CAACH,QAAQ;EAGvB,IAAIL,IAAI,CAACH,IAAI,KAAK,aAAa,IAAII,KAAK,CAACJ,IAAI,KAAK,aAAa,EAAE,OAAOF,IAAI;;EAE5E;EACA;EACA,IAAIM,KAAK,CAAChB,KAAK,KAAK,CAAC,EAAE,OAAOe,IAAI;;EAElC;EACA,IAAIA,IAAI,CAACf,KAAK,KAAK,CAAC,IAAIwB,EAAE,KAAK,GAAG,EAAE,OAAOR,KAAK;;EAEhD;EACA,IAAID,IAAI,CAACf,KAAK,KAAK,CAAC,IAAIwB,EAAE,KAAK,GAAG,EAAE,OAAOH,SAAS,CAACL,KAAK,CAAC;;EAE3D;EACA;EACA,IAAID,IAAI,CAACH,IAAI,KAAKI,KAAK,CAACJ,IAAI,IAAIK,WAAW,CAACF,IAAI,CAACH,IAAI,CAAC,EAAE;IACtDF,IAAI,GAAGb,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAEV,IAAI,CAAC;IAC9B,IAAIS,EAAE,KAAK,GAAG,EAAEd,IAAI,CAACV,KAAK,GAAGe,IAAI,CAACf,KAAK,GAAGgB,KAAK,CAAChB,KAAK,CAAC,KAAKU,IAAI,CAACV,KAAK,GAAGe,IAAI,CAACf,KAAK,GAAGgB,KAAK,CAAChB,KAAK;EAClG;;EAEA;EACA,IAAIiB,WAAW,CAACF,IAAI,CAACH,IAAI,CAAC,KAAKI,KAAK,CAACI,QAAQ,KAAK,GAAG,IAAIJ,KAAK,CAACI,QAAQ,KAAK,GAAG,CAAC,IAAIJ,KAAK,CAACJ,IAAI,KAAK,gBAAgB,EAAE;IACnH;IACA;IACA;IACA;IACA,IAAIG,IAAI,CAACH,IAAI,KAAKI,KAAK,CAACD,IAAI,CAACH,IAAI,EAAE;MACjCF,IAAI,GAAGb,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;MAC9BA,IAAI,CAACK,IAAI,GAAGN,MAAM,CAAC;QACjBG,IAAI,EAAE,gBAAgB;QACtBQ,QAAQ,EAAEI,EAAE;QACZT,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAEA,KAAK,CAACD;MACf,CAAC,EAAEJ,SAAS,CAAC;MACbD,IAAI,CAACM,KAAK,GAAGA,KAAK,CAACA,KAAK;MACxBN,IAAI,CAACU,QAAQ,GAAGI,EAAE,KAAK,GAAG,GAAGvB,IAAI,CAACe,KAAK,CAACI,QAAQ,CAAC,GAAGJ,KAAK,CAACI,QAAQ;MAClE,OAAOX,MAAM,CAACC,IAAI,EAAEC,SAAS,CAAC;IAChC;IACA;IACA;IACA;IACA;IAAA,KACK,IAAII,IAAI,CAACH,IAAI,KAAKI,KAAK,CAACA,KAAK,CAACJ,IAAI,EAAE;MACrCF,IAAI,GAAGb,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;MAC9BA,IAAI,CAACK,IAAI,GAAGN,MAAM,CAAC;QACjBG,IAAI,EAAE,gBAAgB;QACtBQ,QAAQ,EAAEI,EAAE,KAAK,GAAG,GAAGvB,IAAI,CAACe,KAAK,CAACI,QAAQ,CAAC,GAAGJ,KAAK,CAACI,QAAQ;QAC5DL,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAEA,KAAK,CAACA;MACf,CAAC,EAAEL,SAAS,CAAC;MACbD,IAAI,CAACM,KAAK,GAAGA,KAAK,CAACD,IAAI;MACvB,OAAON,MAAM,CAACC,IAAI,EAAEC,SAAS,CAAC;IAChC;EACJ;;EAEA;EACA,IAAII,IAAI,CAACH,IAAI,KAAK,gBAAgB,KAAKG,IAAI,CAACK,QAAQ,KAAK,GAAG,IAAIL,IAAI,CAACK,QAAQ,KAAK,GAAG,CAAC,IAAIH,WAAW,CAACD,KAAK,CAACJ,IAAI,CAAC,EAAE;IACjH;IACA;IACA;IACA;IACA,IAAII,KAAK,CAACJ,IAAI,KAAKG,IAAI,CAACA,IAAI,CAACH,IAAI,EAAE;MACjCF,IAAI,GAAGb,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAEV,IAAI,CAAC;MAC9BL,IAAI,CAACK,IAAI,GAAGN,MAAM,CAAC;QACjBG,IAAI,EAAE,gBAAgB;QACtBQ,QAAQ,EAAEI,EAAE;QACZT,IAAI,EAAEA,IAAI,CAACA,IAAI;QACfC,KAAK,EAAEA;MACT,CAAC,EAAEL,SAAS,CAAC;MACb,OAAOF,MAAM,CAACC,IAAI,EAAEC,SAAS,CAAC;IAChC;IACA;IACA;IACA;IACA;IAAA,KACK,IAAIK,KAAK,CAACJ,IAAI,KAAKG,IAAI,CAACC,KAAK,CAACJ,IAAI,EAAE;MACrCF,IAAI,GAAGb,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAEV,IAAI,CAAC;MAC9B,IAAIA,IAAI,CAACK,QAAQ,KAAK,GAAG,EAAE;QACzBV,IAAI,CAACM,KAAK,GAAGP,MAAM,CAAC;UAClBG,IAAI,EAAE,gBAAgB;UACtBQ,QAAQ,EAAEI,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;UAChCT,IAAI,EAAEC,KAAK;UACXA,KAAK,EAAED,IAAI,CAACC;QACd,CAAC,EAAEL,SAAS,CAAC;QACbD,IAAI,CAACU,QAAQ,GAAGI,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;MACxC,CAAC,MAAM;QACLd,IAAI,CAACM,KAAK,GAAGP,MAAM,CAAC;UAClBG,IAAI,EAAE,gBAAgB;UACtBQ,QAAQ,EAAEI,EAAE;UACZT,IAAI,EAAEA,IAAI,CAACC,KAAK;UAChBA,KAAK,EAAEA;QACT,CAAC,EAAEL,SAAS,CAAC;MACf;MACA,IAAID,IAAI,CAACM,KAAK,CAAChB,KAAK,GAAG,CAAC,EAAE;QACxBU,IAAI,CAACM,KAAK,CAAChB,KAAK,IAAI,CAAC,CAAC;QACtBU,IAAI,CAACU,QAAQ,GAAGV,IAAI,CAACU,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;MACnD;MACA,OAAOX,MAAM,CAACC,IAAI,EAAEC,SAAS,CAAC;IAChC;EACJ;EACA,OAAOD,IAAI;AACb;AAEA,SAASgB,wBAAwBA,CAAChB,IAAI,EAAEC,SAAS,EAAE;EACjD,IAAI,CAACM,WAAW,CAACP,IAAI,CAACM,KAAK,CAACJ,IAAI,CAAC,EAAE,OAAOF,IAAI;EAE9C,IAAIA,IAAI,CAACM,KAAK,CAACJ,IAAI,KAAK,OAAO,EAAE,MAAM,IAAIe,KAAK,CAAC,qBAAqB,GAAGjB,IAAI,CAACM,KAAK,CAACY,IAAI,GAAG,qBAAqB,CAAC;EAEjH,IAAIlB,IAAI,CAACM,KAAK,CAAChB,KAAK,KAAK,CAAC,EAAE,MAAM,IAAI2B,KAAK,CAAC,uBAAuB,CAAC;;EAEpE;EACA,IAAIjB,IAAI,CAACK,IAAI,CAACH,IAAI,KAAK,gBAAgB,EAAE;IACvC,IAAIK,WAAW,CAACP,IAAI,CAACK,IAAI,CAACA,IAAI,CAACH,IAAI,CAAC,IAAIK,WAAW,CAACP,IAAI,CAACK,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,EAAE;MACzEF,IAAI,CAACK,IAAI,CAACA,IAAI,CAACf,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;MACxCU,IAAI,CAACK,IAAI,CAACC,KAAK,CAAChB,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;MACzC,OAAOS,MAAM,CAACC,IAAI,CAACK,IAAI,EAAEJ,SAAS,CAAC;IACrC;IACA,OAAOD,IAAI;EACb;EACA;EAAA,KACK,IAAIO,WAAW,CAACP,IAAI,CAACK,IAAI,CAACH,IAAI,CAAC,EAAE;IAClCF,IAAI,CAACK,IAAI,CAACf,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;IACnC,OAAOU,IAAI,CAACK,IAAI;EAClB;EACF,OAAOL,IAAI;AACb;AAEA,SAASmB,8BAA8BA,CAACnB,IAAI,EAAE;EAC5C;EACA,IAAIA,IAAI,CAACK,IAAI,CAACH,IAAI,KAAK,gBAAgB,IAAIF,IAAI,CAACM,KAAK,CAACJ,IAAI,KAAK,OAAO,EAAE;IACtE,IAAIK,WAAW,CAACP,IAAI,CAACK,IAAI,CAACA,IAAI,CAACH,IAAI,CAAC,IAAIK,WAAW,CAACP,IAAI,CAACK,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,EAAE;MACzEF,IAAI,CAACK,IAAI,CAACA,IAAI,CAACf,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;MACxCU,IAAI,CAACK,IAAI,CAACC,KAAK,CAAChB,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;MACzC,OAAOU,IAAI,CAACK,IAAI;IAClB;EACF;EACA;EAAA,KACK,IAAIE,WAAW,CAACP,IAAI,CAACK,IAAI,CAACH,IAAI,CAAC,IAAIF,IAAI,CAACM,KAAK,CAACJ,IAAI,KAAK,OAAO,EAAE;IACjEF,IAAI,CAACK,IAAI,CAACf,KAAK,IAAIU,IAAI,CAACM,KAAK,CAAChB,KAAK;IACnC,OAAOU,IAAI,CAACK,IAAI;EAClB;EACA;EAAA,KACK,IAAIL,IAAI,CAACK,IAAI,CAACH,IAAI,KAAK,OAAO,IAAIF,IAAI,CAACM,KAAK,CAACJ,IAAI,KAAK,gBAAgB,EAAE;IACzE,IAAIK,WAAW,CAACP,IAAI,CAACM,KAAK,CAACD,IAAI,CAACH,IAAI,CAAC,IAAIK,WAAW,CAACP,IAAI,CAACM,KAAK,CAACA,KAAK,CAACJ,IAAI,CAAC,EAAE;MAC3EF,IAAI,CAACM,KAAK,CAACD,IAAI,CAACf,KAAK,IAAIU,IAAI,CAACK,IAAI,CAACf,KAAK;MACxCU,IAAI,CAACM,KAAK,CAACA,KAAK,CAAChB,KAAK,IAAIU,IAAI,CAACK,IAAI,CAACf,KAAK;MACzC,OAAOU,IAAI,CAACM,KAAK;IACnB;EACF;EACA;EAAA,KACK,IAAIN,IAAI,CAACK,IAAI,CAACH,IAAI,KAAK,OAAO,IAAIK,WAAW,CAACP,IAAI,CAACM,KAAK,CAACJ,IAAI,CAAC,EAAE;IACjEF,IAAI,CAACM,KAAK,CAAChB,KAAK,IAAIU,IAAI,CAACK,IAAI,CAACf,KAAK;IACnC,OAAOU,IAAI,CAACM,KAAK;EACnB;EACN,OAAON,IAAI;AACb;AAEA,SAASG,oBAAoBA,CAACH,IAAI,EAAEC,SAAS,EAAE;EAC7CD,IAAI,GAAGQ,qBAAqB,CAACR,IAAI,EAAEC,SAAS,CAAC;EAE7C,QAAQD,IAAI,CAACU,QAAQ;IACnB,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAOE,sBAAsB,CAACZ,IAAI,EAAEC,SAAS,CAAC;IAChD,KAAK,GAAG;MACN,OAAOe,wBAAwB,CAAChB,IAAI,EAAEC,SAAS,CAAC;IAClD,KAAK,GAAG;MACN,OAAOkB,8BAA8B,CAACnB,IAAI,CAAC;EAC/C;EACA,OAAOA,IAAI;AACb;AAEAX,OAAO,CAACS,OAAO,GAAGC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}