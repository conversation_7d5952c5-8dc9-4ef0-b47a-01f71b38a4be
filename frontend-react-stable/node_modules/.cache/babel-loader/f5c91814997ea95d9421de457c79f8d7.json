{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _number = _interopRequireDefault(require(\"./number.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n  reB = new RegExp(reA.source, \"g\");\nfunction zero(b) {\n  return function () {\n    return b;\n  };\n}\nfunction one(b) {\n  return function (t) {\n    return b(t) + \"\";\n  };\n}\nfunction _default(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0,\n    // scan index for next number in b\n    am,\n    // current match in a\n    bm,\n    // current match in b\n    bs,\n    // string preceding current number in b, if any\n    i = -1,\n    // index in s\n    s = [],\n    // string constants and placeholders\n    q = []; // number interpolators\n  // Coerce inputs to strings.\n\n  a = a + \"\", b = b + \"\"; // Interpolate pairs of numbers in a & b.\n\n  while ((am = reA.exec(a)) && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) {\n      // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) {\n      // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else {\n      // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({\n        i: i,\n        x: (0, _number.default)(am, bm)\n      });\n    }\n    bi = reB.lastIndex;\n  } // Add remains of b.\n\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  } // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n\n  return s.length < 2 ? q[0] ? one(q[0].x) : zero(b) : (b = q.length, function (t) {\n    for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n    return s.join(\"\");\n  });\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_number", "_interopRequireDefault", "require", "obj", "__esModule", "reA", "reB", "RegExp", "source", "zero", "b", "one", "t", "a", "bi", "lastIndex", "am", "bm", "bs", "i", "s", "q", "exec", "index", "slice", "push", "x", "length", "o", "join"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/string.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _number = _interopRequireDefault(require(\"./number.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function () {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function (t) {\n    return b(t) + \"\";\n  };\n}\n\nfunction _default(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0,\n      // scan index for next number in b\n  am,\n      // current match in a\n  bm,\n      // current match in b\n  bs,\n      // string preceding current number in b, if any\n  i = -1,\n      // index in s\n  s = [],\n      // string constants and placeholders\n  q = []; // number interpolators\n  // Coerce inputs to strings.\n\n  a = a + \"\", b = b + \"\"; // Interpolate pairs of numbers in a & b.\n\n  while ((am = reA.exec(a)) && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) {\n      // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n\n    if ((am = am[0]) === (bm = bm[0])) {\n      // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else {\n      // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({\n        i: i,\n        x: (0, _number.default)(am, bm)\n      });\n    }\n\n    bi = reB.lastIndex;\n  } // Add remains of b.\n\n\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  } // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n\n\n  return s.length < 2 ? q[0] ? one(q[0].x) : zero(b) : (b = q.length, function (t) {\n    for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n\n    return s.join(\"\");\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,OAAO,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,GAAG,GAAG,6CAA6C;EACnDC,GAAG,GAAG,IAAIC,MAAM,CAACF,GAAG,CAACG,MAAM,EAAE,GAAG,CAAC;AAErC,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,YAAY;IACjB,OAAOA,CAAC;EACV,CAAC;AACH;AAEA,SAASC,GAAGA,CAACD,CAAC,EAAE;EACd,OAAO,UAAUE,CAAC,EAAE;IAClB,OAAOF,CAAC,CAACE,CAAC,CAAC,GAAG,EAAE;EAClB,CAAC;AACH;AAEA,SAASb,QAAQA,CAACc,CAAC,EAAEH,CAAC,EAAE;EACtB,IAAII,EAAE,GAAGT,GAAG,CAACU,SAAS,GAAGT,GAAG,CAACS,SAAS,GAAG,CAAC;IACtC;IACJC,EAAE;IACE;IACJC,EAAE;IACE;IACJC,EAAE;IACE;IACJC,CAAC,GAAG,CAAC,CAAC;IACF;IACJC,CAAC,GAAG,EAAE;IACF;IACJC,CAAC,GAAG,EAAE,CAAC,CAAC;EACR;;EAEAR,CAAC,GAAGA,CAAC,GAAG,EAAE,EAAEH,CAAC,GAAGA,CAAC,GAAG,EAAE,CAAC,CAAC;;EAExB,OAAO,CAACM,EAAE,GAAGX,GAAG,CAACiB,IAAI,CAACT,CAAC,CAAC,MAAMI,EAAE,GAAGX,GAAG,CAACgB,IAAI,CAACZ,CAAC,CAAC,CAAC,EAAE;IAC/C,IAAI,CAACQ,EAAE,GAAGD,EAAE,CAACM,KAAK,IAAIT,EAAE,EAAE;MACxB;MACAI,EAAE,GAAGR,CAAC,CAACc,KAAK,CAACV,EAAE,EAAEI,EAAE,CAAC;MACpB,IAAIE,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAID,EAAE,CAAC,CAAC;MAAA,KACjBE,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGD,EAAE;IAClB;IAEA,IAAI,CAACF,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,OAAOC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC;MACA,IAAIG,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAIF,EAAE,CAAC,CAAC;MAAA,KACjBG,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGF,EAAE;IAClB,CAAC,MAAM;MACL;MACAG,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,IAAI;MACbE,CAAC,CAACI,IAAI,CAAC;QACLN,CAAC,EAAEA,CAAC;QACJO,CAAC,EAAE,CAAC,CAAC,EAAE1B,OAAO,CAACF,OAAO,EAAEkB,EAAE,EAAEC,EAAE;MAChC,CAAC,CAAC;IACJ;IAEAH,EAAE,GAAGR,GAAG,CAACS,SAAS;EACpB,CAAC,CAAC;;EAGF,IAAID,EAAE,GAAGJ,CAAC,CAACiB,MAAM,EAAE;IACjBT,EAAE,GAAGR,CAAC,CAACc,KAAK,CAACV,EAAE,CAAC;IAChB,IAAIM,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAID,EAAE,CAAC,CAAC;IAAA,KACjBE,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGD,EAAE;EAClB,CAAC,CAAC;EACF;;EAGA,OAAOE,CAAC,CAACO,MAAM,GAAG,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC,GAAGV,GAAG,CAACU,CAAC,CAAC,CAAC,CAAC,CAACK,CAAC,CAAC,GAAGjB,IAAI,CAACC,CAAC,CAAC,IAAIA,CAAC,GAAGW,CAAC,CAACM,MAAM,EAAE,UAAUf,CAAC,EAAE;IAC/E,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAES,CAAC,EAAET,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAEC,CAAC,CAAC,CAACQ,CAAC,GAAGP,CAAC,CAACF,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAGS,CAAC,CAACF,CAAC,CAACd,CAAC,CAAC;IAEvD,OAAOQ,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}