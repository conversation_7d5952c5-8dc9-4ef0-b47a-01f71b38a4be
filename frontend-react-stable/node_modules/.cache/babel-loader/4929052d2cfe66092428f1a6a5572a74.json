{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { withConfigConsumer } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nimport StatisticNumber from './Number';\nvar Statistic = function Statistic(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    valueStyle = props.valueStyle,\n    _props$value = props.value,\n    value = _props$value === void 0 ? 0 : _props$value,\n    title = props.title,\n    valueRender = props.valueRender,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    _props$loading = props.loading,\n    loading = _props$loading === void 0 ? false : _props$loading,\n    direction = props.direction,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    _props$decimalSeparat = props.decimalSeparator,\n    decimalSeparator = _props$decimalSeparat === void 0 ? '.' : _props$decimalSeparat,\n    _props$groupSeparator = props.groupSeparator,\n    groupSeparator = _props$groupSeparator === void 0 ? ',' : _props$groupSeparator;\n  var valueNode = /*#__PURE__*/React.createElement(StatisticNumber, _extends({\n    decimalSeparator: decimalSeparator,\n    groupSeparator: groupSeparator\n  }, props, {\n    value: value\n  }));\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, title && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), /*#__PURE__*/React.createElement(Skeleton, {\n    paragraph: false,\n    loading: loading,\n    className: \"\".concat(prefixCls, \"-skeleton\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: valueStyle,\n    className: \"\".concat(prefixCls, \"-content\")\n  }, prefix && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-prefix\")\n  }, prefix), valueRender ? valueRender(valueNode) : valueNode, suffix && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-suffix\")\n  }, suffix))));\n};\nvar WrapperStatistic = withConfigConsumer({\n  prefixCls: 'statistic'\n})(Statistic);\nexport default WrapperStatistic;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "classNames", "React", "withConfigConsumer", "Skeleton", "StatisticNumber", "Statistic", "props", "prefixCls", "className", "style", "valueStyle", "_props$value", "value", "title", "valueRender", "prefix", "suffix", "_props$loading", "loading", "direction", "onMouseEnter", "onMouseLeave", "_props$decimalSeparat", "decimalSeparator", "_props$groupSeparator", "groupSeparator", "valueNode", "createElement", "cls", "concat", "paragraph", "WrapperStatistic"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/statistic/Statistic.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { withConfigConsumer } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nimport StatisticNumber from './Number';\nvar Statistic = function Statistic(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    valueStyle = props.valueStyle,\n    _props$value = props.value,\n    value = _props$value === void 0 ? 0 : _props$value,\n    title = props.title,\n    valueRender = props.valueRender,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    _props$loading = props.loading,\n    loading = _props$loading === void 0 ? false : _props$loading,\n    direction = props.direction,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    _props$decimalSeparat = props.decimalSeparator,\n    decimalSeparator = _props$decimalSeparat === void 0 ? '.' : _props$decimalSeparat,\n    _props$groupSeparator = props.groupSeparator,\n    groupSeparator = _props$groupSeparator === void 0 ? ',' : _props$groupSeparator;\n  var valueNode = /*#__PURE__*/React.createElement(StatisticNumber, _extends({\n    decimalSeparator: decimalSeparator,\n    groupSeparator: groupSeparator\n  }, props, {\n    value: value\n  }));\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, title && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), /*#__PURE__*/React.createElement(Skeleton, {\n    paragraph: false,\n    loading: loading,\n    className: \"\".concat(prefixCls, \"-skeleton\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: valueStyle,\n    className: \"\".concat(prefixCls, \"-content\")\n  }, prefix && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-prefix\")\n  }, prefix), valueRender ? valueRender(valueNode) : valueNode, suffix && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-suffix\")\n  }, suffix))));\n};\nvar WrapperStatistic = withConfigConsumer({\n  prefixCls: 'statistic'\n})(Statistic);\nexport default WrapperStatistic;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,eAAe,MAAM,UAAU;AACtC,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,YAAY,GAAGL,KAAK,CAACM,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IAClDE,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,MAAM,GAAGT,KAAK,CAACS,MAAM;IACrBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,cAAc,GAAGX,KAAK,CAACY,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;IAC5DE,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,qBAAqB,GAAGhB,KAAK,CAACiB,gBAAgB;IAC9CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IACjFE,qBAAqB,GAAGlB,KAAK,CAACmB,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;EACjF,IAAIE,SAAS,GAAG,aAAazB,KAAK,CAAC0B,aAAa,CAACvB,eAAe,EAAEL,QAAQ,CAAC;IACzEwB,gBAAgB,EAAEA,gBAAgB;IAClCE,cAAc,EAAEA;EAClB,CAAC,EAAEnB,KAAK,EAAE;IACRM,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;EACH,IAAIgB,GAAG,GAAG5B,UAAU,CAACO,SAAS,EAAET,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAACtB,SAAS,EAAE,MAAM,CAAC,EAAEY,SAAS,KAAK,KAAK,CAAC,EAAEX,SAAS,CAAC;EAClH,OAAO,aAAaP,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IAC7CnB,SAAS,EAAEoB,GAAG;IACdnB,KAAK,EAAEA,KAAK;IACZW,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA;EAChB,CAAC,EAAER,KAAK,IAAI,aAAaZ,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IAClDnB,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACtB,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEM,KAAK,CAAC,EAAE,aAAaZ,KAAK,CAAC0B,aAAa,CAACxB,QAAQ,EAAE;IACpD2B,SAAS,EAAE,KAAK;IAChBZ,OAAO,EAAEA,OAAO;IAChBV,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACtB,SAAS,EAAE,WAAW;EAC7C,CAAC,EAAE,aAAaN,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IACzClB,KAAK,EAAEC,UAAU;IACjBF,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACtB,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEQ,MAAM,IAAI,aAAad,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;IACpDnB,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACtB,SAAS,EAAE,iBAAiB;EACnD,CAAC,EAAEQ,MAAM,CAAC,EAAED,WAAW,GAAGA,WAAW,CAACY,SAAS,CAAC,GAAGA,SAAS,EAAEV,MAAM,IAAI,aAAaf,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;IAC/GnB,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACtB,SAAS,EAAE,iBAAiB;EACnD,CAAC,EAAES,MAAM,CAAC,CAAC,CAAC,CAAC;AACf,CAAC;AACD,IAAIe,gBAAgB,GAAG7B,kBAAkB,CAAC;EACxCK,SAAS,EAAE;AACb,CAAC,CAAC,CAACF,SAAS,CAAC;AACb,eAAe0B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}