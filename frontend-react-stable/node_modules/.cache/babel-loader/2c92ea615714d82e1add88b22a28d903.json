{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport SkeletonNode from './Node';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport Paragraph from './Paragraph';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && _typeof(prop) === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  var basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nvar Skeleton = function Skeleton(props) {\n  var customizePrefixCls = props.prefixCls,\n    loading = props.loading,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    _props$avatar = props.avatar,\n    avatar = _props$avatar === void 0 ? false : _props$avatar,\n    _props$title = props.title,\n    title = _props$title === void 0 ? true : _props$title,\n    _props$paragraph = props.paragraph,\n    paragraph = _props$paragraph === void 0 ? true : _props$paragraph,\n    active = props.active,\n    round = props.round;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  if (loading || !('loading' in props)) {\n    var _classNames;\n    var hasAvatar = !!avatar;\n    var hasTitle = !!title;\n    var hasParagraph = !!paragraph;\n    // Avatar\n    var avatarNode;\n    if (hasAvatar) {\n      var avatarProps = _extends(_extends({\n        prefixCls: \"\".concat(prefixCls, \"-avatar\")\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-header\")\n      }, /*#__PURE__*/React.createElement(Element, _extends({}, avatarProps)));\n    }\n    var contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      var $title;\n      if (hasTitle) {\n        var titleProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-title\")\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, _extends({}, titleProps));\n      }\n      // Paragraph\n      var paragraphNode;\n      if (hasParagraph) {\n        var paragraphProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-paragraph\")\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, _extends({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\")\n      }, $title, paragraphNode);\n    }\n    var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-avatar\"), hasAvatar), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-round\"), round), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, avatarNode, contentNode);\n  }\n  return typeof children !== 'undefined' ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nexport default Skeleton;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_typeof", "classNames", "React", "ConfigContext", "SkeletonAvatar", "SkeletonButton", "SkeletonNode", "Element", "SkeletonImage", "SkeletonInput", "Paragraph", "Title", "getComponentProps", "prop", "getAvatarBasicProps", "hasTitle", "hasParagraph", "size", "shape", "getTitleBasicProps", "has<PERSON><PERSON><PERSON>", "width", "getParagraphBasicProps", "basicProps", "rows", "Skeleton", "props", "customizePrefixCls", "prefixCls", "loading", "className", "style", "children", "_props$avatar", "avatar", "_props$title", "title", "_props$paragraph", "paragraph", "active", "round", "_React$useContext", "useContext", "getPrefixCls", "direction", "_classNames", "avatarNode", "avatarProps", "concat", "createElement", "contentNode", "$title", "titleProps", "paragraphNode", "paragraphProps", "cls", "<PERSON><PERSON>", "Avatar", "Input", "Image", "Node"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Skeleton.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport SkeletonNode from './Node';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport Paragraph from './Paragraph';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && _typeof(prop) === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  var basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nvar Skeleton = function Skeleton(props) {\n  var customizePrefixCls = props.prefixCls,\n    loading = props.loading,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    _props$avatar = props.avatar,\n    avatar = _props$avatar === void 0 ? false : _props$avatar,\n    _props$title = props.title,\n    title = _props$title === void 0 ? true : _props$title,\n    _props$paragraph = props.paragraph,\n    paragraph = _props$paragraph === void 0 ? true : _props$paragraph,\n    active = props.active,\n    round = props.round;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  if (loading || !('loading' in props)) {\n    var _classNames;\n    var hasAvatar = !!avatar;\n    var hasTitle = !!title;\n    var hasParagraph = !!paragraph;\n    // Avatar\n    var avatarNode;\n    if (hasAvatar) {\n      var avatarProps = _extends(_extends({\n        prefixCls: \"\".concat(prefixCls, \"-avatar\")\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-header\")\n      }, /*#__PURE__*/React.createElement(Element, _extends({}, avatarProps)));\n    }\n    var contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      var $title;\n      if (hasTitle) {\n        var titleProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-title\")\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, _extends({}, titleProps));\n      }\n      // Paragraph\n      var paragraphNode;\n      if (hasParagraph) {\n        var paragraphProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-paragraph\")\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, _extends({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\")\n      }, $title, paragraphNode);\n    }\n    var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-avatar\"), hasAvatar), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-round\"), round), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, avatarNode, contentNode);\n  }\n  return typeof children !== 'undefined' ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nexport default Skeleton;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,YAAY,MAAM,QAAQ;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIA,IAAI,IAAIb,OAAO,CAACa,IAAI,CAAC,KAAK,QAAQ,EAAE;IACtC,OAAOA,IAAI;EACb;EACA,OAAO,CAAC,CAAC;AACX;AACA,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EACnD,IAAID,QAAQ,IAAI,CAACC,YAAY,EAAE;IAC7B;IACA,OAAO;MACLC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC;EACH;EACA,OAAO;IACLD,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;AACH;AACA,SAASC,kBAAkBA,CAACC,SAAS,EAAEJ,YAAY,EAAE;EACnD,IAAI,CAACI,SAAS,IAAIJ,YAAY,EAAE;IAC9B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAID,SAAS,IAAIJ,YAAY,EAAE;IAC7B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EACA,OAAO,CAAC,CAAC;AACX;AACA,SAASC,sBAAsBA,CAACF,SAAS,EAAEL,QAAQ,EAAE;EACnD,IAAIQ,UAAU,GAAG,CAAC,CAAC;EACnB;EACA,IAAI,CAACH,SAAS,IAAI,CAACL,QAAQ,EAAE;IAC3BQ,UAAU,CAACF,KAAK,GAAG,KAAK;EAC1B;EACA;EACA,IAAI,CAACD,SAAS,IAAIL,QAAQ,EAAE;IAC1BQ,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB,CAAC,MAAM;IACLD,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB;EACA,OAAOD,UAAU;AACnB;AACA,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,aAAa,GAAGP,KAAK,CAACQ,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,aAAa;IACzDE,YAAY,GAAGT,KAAK,CAACU,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,YAAY;IACrDE,gBAAgB,GAAGX,KAAK,CAACY,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,MAAM,GAAGb,KAAK,CAACa,MAAM;IACrBC,KAAK,GAAGd,KAAK,CAACc,KAAK;EACrB,IAAIC,iBAAiB,GAAGvC,KAAK,CAACwC,UAAU,CAACvC,aAAa,CAAC;IACrDwC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIhB,SAAS,GAAGe,YAAY,CAAC,UAAU,EAAEhB,kBAAkB,CAAC;EAC5D,IAAIE,OAAO,IAAI,EAAE,SAAS,IAAIH,KAAK,CAAC,EAAE;IACpC,IAAImB,WAAW;IACf,IAAIzB,SAAS,GAAG,CAAC,CAACc,MAAM;IACxB,IAAInB,QAAQ,GAAG,CAAC,CAACqB,KAAK;IACtB,IAAIpB,YAAY,GAAG,CAAC,CAACsB,SAAS;IAC9B;IACA,IAAIQ,UAAU;IACd,IAAI1B,SAAS,EAAE;MACb,IAAI2B,WAAW,GAAGhD,QAAQ,CAACA,QAAQ,CAAC;QAClC6B,SAAS,EAAE,EAAE,CAACoB,MAAM,CAACpB,SAAS,EAAE,SAAS;MAC3C,CAAC,EAAEd,mBAAmB,CAACC,QAAQ,EAAEC,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACsB,MAAM,CAAC,CAAC;MAC3E;MACAY,UAAU,GAAG,aAAa5C,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE;QACnDnB,SAAS,EAAE,EAAE,CAACkB,MAAM,CAACpB,SAAS,EAAE,SAAS;MAC3C,CAAC,EAAE,aAAa1B,KAAK,CAAC+C,aAAa,CAAC1C,OAAO,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEgD,WAAW,CAAC,CAAC,CAAC;IAC1E;IACA,IAAIG,WAAW;IACf,IAAInC,QAAQ,IAAIC,YAAY,EAAE;MAC5B;MACA,IAAImC,MAAM;MACV,IAAIpC,QAAQ,EAAE;QACZ,IAAIqC,UAAU,GAAGrD,QAAQ,CAACA,QAAQ,CAAC;UACjC6B,SAAS,EAAE,EAAE,CAACoB,MAAM,CAACpB,SAAS,EAAE,QAAQ;QAC1C,CAAC,EAAET,kBAAkB,CAACC,SAAS,EAAEJ,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACwB,KAAK,CAAC,CAAC;QAC1Ee,MAAM,GAAG,aAAajD,KAAK,CAAC+C,aAAa,CAACtC,KAAK,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEqD,UAAU,CAAC,CAAC;MAC5E;MACA;MACA,IAAIC,aAAa;MACjB,IAAIrC,YAAY,EAAE;QAChB,IAAIsC,cAAc,GAAGvD,QAAQ,CAACA,QAAQ,CAAC;UACrC6B,SAAS,EAAE,EAAE,CAACoB,MAAM,CAACpB,SAAS,EAAE,YAAY;QAC9C,CAAC,EAAEN,sBAAsB,CAACF,SAAS,EAAEL,QAAQ,CAAC,CAAC,EAAEH,iBAAiB,CAAC0B,SAAS,CAAC,CAAC;QAC9Ee,aAAa,GAAG,aAAanD,KAAK,CAAC+C,aAAa,CAACvC,SAAS,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEuD,cAAc,CAAC,CAAC;MAC3F;MACAJ,WAAW,GAAG,aAAahD,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE;QACpDnB,SAAS,EAAE,EAAE,CAACkB,MAAM,CAACpB,SAAS,EAAE,UAAU;MAC5C,CAAC,EAAEuB,MAAM,EAAEE,aAAa,CAAC;IAC3B;IACA,IAAIE,GAAG,GAAGtD,UAAU,CAAC2B,SAAS,GAAGiB,WAAW,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACpB,SAAS,EAAE,cAAc,CAAC,EAAER,SAAS,CAAC,EAAEtB,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACpB,SAAS,EAAE,SAAS,CAAC,EAAEW,MAAM,CAAC,EAAEzC,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACpB,SAAS,EAAE,MAAM,CAAC,EAAEgB,SAAS,KAAK,KAAK,CAAC,EAAE9C,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACpB,SAAS,EAAE,QAAQ,CAAC,EAAEY,KAAK,CAAC,EAAEK,WAAW,GAAGf,SAAS,CAAC;IACvX,OAAO,aAAa5B,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE;MAC7CnB,SAAS,EAAEyB,GAAG;MACdxB,KAAK,EAAEA;IACT,CAAC,EAAEe,UAAU,EAAEI,WAAW,CAAC;EAC7B;EACA,OAAO,OAAOlB,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI;AAC1D,CAAC;AACDP,QAAQ,CAAC+B,MAAM,GAAGnD,cAAc;AAChCoB,QAAQ,CAACgC,MAAM,GAAGrD,cAAc;AAChCqB,QAAQ,CAACiC,KAAK,GAAGjD,aAAa;AAC9BgB,QAAQ,CAACkC,KAAK,GAAGnD,aAAa;AAC9BiB,QAAQ,CAACmC,IAAI,GAAGtD,YAAY;AAC5B,eAAemB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}