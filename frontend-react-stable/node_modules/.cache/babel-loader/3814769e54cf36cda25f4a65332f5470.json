{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _exponent = _interopRequireDefault(require(\"./exponent.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, (0, _exponent.default)(max) - (0, _exponent.default)(step)) + 1;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_exponent", "_interopRequireDefault", "require", "obj", "__esModule", "step", "max", "Math", "abs"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/precisionRound.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _exponent = _interopRequireDefault(require(\"./exponent.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, (0, _exponent.default)(max) - (0, _exponent.default)(step)) + 1;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,IAAI,EAAEC,GAAG,EAAE;EAC3BD,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,GAAGD,IAAI;EACjD,OAAOE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEN,SAAS,CAACF,OAAO,EAAEQ,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACF,OAAO,EAAEO,IAAI,CAAC,CAAC,GAAG,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}