{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantile;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _init = require(\"./init.js\");\nfunction quantile() {\n  var domain = [],\n    range = [],\n    thresholds = [],\n    unknown;\n  function rescale() {\n    var i = 0,\n      n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = (0, _index.quantileSorted)(domain, i / n);\n    return scale;\n  }\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[(0, _index.bisect)(thresholds, x)];\n  }\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [i > 0 ? thresholds[i - 1] : domain[0], i < thresholds.length ? thresholds[i] : domain[domain.length - 1]];\n  };\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(_index.ascending);\n    return rescale();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.quantiles = function () {\n    return thresholds.slice();\n  };\n  scale.copy = function () {\n    return quantile().domain(domain).range(range).unknown(unknown);\n  };\n  return _init.initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "quantile", "_index", "require", "_init", "domain", "range", "thresholds", "unknown", "rescale", "i", "n", "Math", "max", "length", "Array", "quantileSorted", "scale", "x", "isNaN", "bisect", "invertExtent", "y", "indexOf", "NaN", "_", "arguments", "slice", "d", "push", "sort", "ascending", "from", "quantiles", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/quantile.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quantile;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0,\n        n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n\n    while (++i < n) thresholds[i - 1] = (0, _index.quantileSorted)(domain, i / n);\n\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[(0, _index.bisect)(thresholds, x)];\n  }\n\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [i > 0 ? thresholds[i - 1] : domain[0], i < thresholds.length ? thresholds[i] : domain[domain.length - 1]];\n  };\n\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n\n    domain.sort(_index.ascending);\n    return rescale();\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function () {\n    return thresholds.slice();\n  };\n\n  scale.copy = function () {\n    return quantile().domain(domain).range(range).unknown(unknown);\n  };\n\n  return _init.initRange.apply(scale, arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASF,QAAQA,CAAA,EAAG;EAClB,IAAII,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,EAAE;IACVC,UAAU,GAAG,EAAE;IACfC,OAAO;EAEX,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG,CAAC;MACLC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,CAACQ,MAAM,CAAC;IACjCP,UAAU,GAAG,IAAIQ,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC;IAE7B,OAAO,EAAED,CAAC,GAAGC,CAAC,EAAEJ,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACc,cAAc,EAAEX,MAAM,EAAEK,CAAC,GAAGC,CAAC,CAAC;IAE7E,OAAOM,KAAK;EACd;EAEA,SAASA,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGV,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAACkB,MAAM,EAAEb,UAAU,EAAEW,CAAC,CAAC,CAAC;EACxF;EAEAD,KAAK,CAACI,YAAY,GAAG,UAAUC,CAAC,EAAE;IAChC,IAAIZ,CAAC,GAAGJ,KAAK,CAACiB,OAAO,CAACD,CAAC,CAAC;IACxB,OAAOZ,CAAC,GAAG,CAAC,GAAG,CAACc,GAAG,EAAEA,GAAG,CAAC,GAAG,CAACd,CAAC,GAAG,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAEK,CAAC,GAAGH,UAAU,CAACO,MAAM,GAAGP,UAAU,CAACG,CAAC,CAAC,GAAGL,MAAM,CAACA,MAAM,CAACS,MAAM,GAAG,CAAC,CAAC,CAAC;EACxI,CAAC;EAEDG,KAAK,CAACZ,MAAM,GAAG,UAAUoB,CAAC,EAAE;IAC1B,IAAI,CAACC,SAAS,CAACZ,MAAM,EAAE,OAAOT,MAAM,CAACsB,KAAK,CAAC,CAAC;IAC5CtB,MAAM,GAAG,EAAE;IAEX,KAAK,IAAIuB,CAAC,IAAIH,CAAC,EAAE,IAAIG,CAAC,IAAI,IAAI,IAAI,CAACT,KAAK,CAACS,CAAC,GAAG,CAACA,CAAC,CAAC,EAAEvB,MAAM,CAACwB,IAAI,CAACD,CAAC,CAAC;IAEhEvB,MAAM,CAACyB,IAAI,CAAC5B,MAAM,CAAC6B,SAAS,CAAC;IAC7B,OAAOtB,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDQ,KAAK,CAACX,KAAK,GAAG,UAAUmB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACZ,MAAM,IAAIR,KAAK,GAAGS,KAAK,CAACiB,IAAI,CAACP,CAAC,CAAC,EAAEhB,OAAO,CAAC,CAAC,IAAIH,KAAK,CAACqB,KAAK,CAAC,CAAC;EAC9E,CAAC;EAEDV,KAAK,CAACT,OAAO,GAAG,UAAUiB,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACZ,MAAM,IAAIN,OAAO,GAAGiB,CAAC,EAAER,KAAK,IAAIT,OAAO;EAC1D,CAAC;EAEDS,KAAK,CAACgB,SAAS,GAAG,YAAY;IAC5B,OAAO1B,UAAU,CAACoB,KAAK,CAAC,CAAC;EAC3B,CAAC;EAEDV,KAAK,CAACiB,IAAI,GAAG,YAAY;IACvB,OAAOjC,QAAQ,CAAC,CAAC,CAACI,MAAM,CAACA,MAAM,CAAC,CAACC,KAAK,CAACA,KAAK,CAAC,CAACE,OAAO,CAACA,OAAO,CAAC;EAChE,CAAC;EAED,OAAOJ,KAAK,CAAC+B,SAAS,CAACC,KAAK,CAACnB,KAAK,EAAES,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}