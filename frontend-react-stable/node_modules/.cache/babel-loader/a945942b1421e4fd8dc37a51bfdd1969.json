{"ast": null, "code": "var parse = require(\"./parse\");\nvar walk = require(\"./walk\");\nvar stringify = require(\"./stringify\");\nfunction ValueParser(value) {\n  if (this instanceof ValueParser) {\n    this.nodes = parse(value);\n    return this;\n  }\n  return new ValueParser(value);\n}\nValueParser.prototype.toString = function () {\n  return Array.isArray(this.nodes) ? stringify(this.nodes) : \"\";\n};\nValueParser.prototype.walk = function (cb, bubble) {\n  walk(this.nodes, cb, bubble);\n  return this;\n};\nValueParser.unit = require(\"./unit\");\nValueParser.walk = walk;\nValueParser.stringify = stringify;\nmodule.exports = ValueParser;", "map": {"version": 3, "names": ["parse", "require", "walk", "stringify", "ValueParser", "value", "nodes", "prototype", "toString", "Array", "isArray", "cb", "bubble", "unit", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/node_modules/postcss-value-parser/lib/index.js"], "sourcesContent": ["var parse = require(\"./parse\");\nvar walk = require(\"./walk\");\nvar stringify = require(\"./stringify\");\n\nfunction ValueParser(value) {\n  if (this instanceof ValueParser) {\n    this.nodes = parse(value);\n    return this;\n  }\n  return new ValueParser(value);\n}\n\nValueParser.prototype.toString = function() {\n  return Array.isArray(this.nodes) ? stringify(this.nodes) : \"\";\n};\n\nValueParser.prototype.walk = function(cb, bubble) {\n  walk(this.nodes, cb, bubble);\n  return this;\n};\n\nValueParser.unit = require(\"./unit\");\n\nValueParser.walk = walk;\n\nValueParser.stringify = stringify;\n\nmodule.exports = ValueParser;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAIE,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;AAEtC,SAASG,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI,IAAI,YAAYD,WAAW,EAAE;IAC/B,IAAI,CAACE,KAAK,GAAGN,KAAK,CAACK,KAAK,CAAC;IACzB,OAAO,IAAI;EACb;EACA,OAAO,IAAID,WAAW,CAACC,KAAK,CAAC;AAC/B;AAEAD,WAAW,CAACG,SAAS,CAACC,QAAQ,GAAG,YAAW;EAC1C,OAAOC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACJ,KAAK,CAAC,GAAGH,SAAS,CAAC,IAAI,CAACG,KAAK,CAAC,GAAG,EAAE;AAC/D,CAAC;AAEDF,WAAW,CAACG,SAAS,CAACL,IAAI,GAAG,UAASS,EAAE,EAAEC,MAAM,EAAE;EAChDV,IAAI,CAAC,IAAI,CAACI,KAAK,EAAEK,EAAE,EAAEC,MAAM,CAAC;EAC5B,OAAO,IAAI;AACb,CAAC;AAEDR,WAAW,CAACS,IAAI,GAAGZ,OAAO,CAAC,QAAQ,CAAC;AAEpCG,WAAW,CAACF,IAAI,GAAGA,IAAI;AAEvBE,WAAW,CAACD,SAAS,GAAGA,SAAS;AAEjCW,MAAM,CAACC,OAAO,GAAGX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}