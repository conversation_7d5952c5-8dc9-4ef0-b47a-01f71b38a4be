{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = rank;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _sort = require(\"./sort.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction rank(values, valueof = _ascending.default) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending.default;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  Uint32Array.from(V, (_, i) => i).sort(valueof === _ascending.default ? (i, j) => (0, _sort.ascendingDefined)(V[i], V[j]) : (0, _sort.compareDefined)(compareIndex)).forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "rank", "_ascending", "_interopRequireDefault", "require", "_sort", "obj", "__esModule", "values", "valueof", "Symbol", "iterator", "TypeError", "V", "Array", "from", "R", "Float64Array", "length", "map", "compareIndex", "i", "j", "k", "r", "Uint32Array", "_", "sort", "ascendingDefined", "compareDefined", "for<PERSON>ach", "c", "undefined", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/rank.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = rank;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _sort = require(\"./sort.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction rank(values, valueof = _ascending.default) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending.default;\n\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n\n  let k, r;\n  Uint32Array.from(V, (_, i) => i).sort(valueof === _ascending.default ? (i, j) => (0, _sort.ascendingDefined)(V[i], V[j]) : (0, _sort.compareDefined)(compareIndex)).forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,IAAIA,CAACO,MAAM,EAAEC,OAAO,GAAGP,UAAU,CAACF,OAAO,EAAE;EAClD,IAAI,OAAOQ,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAIC,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACP,MAAM,CAAC;EAC1B,MAAMQ,CAAC,GAAG,IAAIC,YAAY,CAACJ,CAAC,CAACK,MAAM,CAAC;EACpC,IAAIT,OAAO,CAACS,MAAM,KAAK,CAAC,EAAEL,CAAC,GAAGA,CAAC,CAACM,GAAG,CAACV,OAAO,CAAC,EAAEA,OAAO,GAAGP,UAAU,CAACF,OAAO;EAE1E,MAAMoB,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKb,OAAO,CAACI,CAAC,CAACQ,CAAC,CAAC,EAAER,CAAC,CAACS,CAAC,CAAC,CAAC;EAElD,IAAIC,CAAC,EAAEC,CAAC;EACRC,WAAW,CAACV,IAAI,CAACF,CAAC,EAAE,CAACa,CAAC,EAAEL,CAAC,KAAKA,CAAC,CAAC,CAACM,IAAI,CAAClB,OAAO,KAAKP,UAAU,CAACF,OAAO,GAAG,CAACqB,CAAC,EAAEC,CAAC,KAAK,CAAC,CAAC,EAAEjB,KAAK,CAACuB,gBAAgB,EAAEf,CAAC,CAACQ,CAAC,CAAC,EAAER,CAAC,CAACS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEjB,KAAK,CAACwB,cAAc,EAAET,YAAY,CAAC,CAAC,CAACU,OAAO,CAAC,CAACR,CAAC,EAAED,CAAC,KAAK;IACpL,MAAMU,CAAC,GAAGX,YAAY,CAACE,CAAC,EAAEC,CAAC,KAAKS,SAAS,GAAGV,CAAC,GAAGC,CAAC,CAAC;IAElD,IAAIQ,CAAC,IAAI,CAAC,EAAE;MACV,IAAIR,CAAC,KAAKS,SAAS,IAAID,CAAC,GAAG,CAAC,EAAER,CAAC,GAAGD,CAAC,EAAEE,CAAC,GAAGH,CAAC;MAC1CL,CAAC,CAACM,CAAC,CAAC,GAAGE,CAAC;IACV,CAAC,MAAM;MACLR,CAAC,CAACM,CAAC,CAAC,GAAGW,GAAG;IACZ;EACF,CAAC,CAAC;EACF,OAAOjB,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}