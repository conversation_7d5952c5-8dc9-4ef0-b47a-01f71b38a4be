{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n/**\n * Same as React.useState but `setState` accept `ignoreDestroy` param to not to setState after destroyed.\n * We do not make this auto is to avoid real memory leak.\n * Dev<PERSON>per should confirm it's safe to ignore themselves.\n */\nexport default function useSafeState(defaultValue) {\n  var destroyRef = React.useRef(false);\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    value = _React$useState2[0],\n    setValue = _React$useState2[1];\n  React.useEffect(function () {\n    destroyRef.current = false;\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  function safeSetState(updater, ignoreDestroy) {\n    if (ignoreDestroy && destroyRef.current) {\n      return;\n    }\n    setValue(updater);\n  }\n  return [value, safeSetState];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useSafeState", "defaultValue", "destroyRef", "useRef", "_React$useState", "useState", "_React$useState2", "value", "setValue", "useEffect", "current", "safeSetState", "updater", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/hooks/useState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n/**\n * Same as React.useState but `setState` accept `ignoreDestroy` param to not to setState after destroyed.\n * We do not make this auto is to avoid real memory leak.\n * Dev<PERSON>per should confirm it's safe to ignore themselves.\n */\nexport default function useSafeState(defaultValue) {\n  var destroyRef = React.useRef(false);\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    value = _React$useState2[0],\n    setValue = _React$useState2[1];\n  React.useEffect(function () {\n    destroyRef.current = false;\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  function safeSetState(updater, ignoreDestroy) {\n    if (ignoreDestroy && destroyRef.current) {\n      return;\n    }\n    setValue(updater);\n  }\n  return [value, safeSetState];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAE;EACjD,IAAIC,UAAU,GAAGH,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,eAAe,GAAGL,KAAK,CAACM,QAAQ,CAACJ,YAAY,CAAC;IAChDK,gBAAgB,GAAGR,cAAc,CAACM,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChCP,KAAK,CAACU,SAAS,CAAC,YAAY;IAC1BP,UAAU,CAACQ,OAAO,GAAG,KAAK;IAC1B,OAAO,YAAY;MACjBR,UAAU,CAACQ,OAAO,GAAG,IAAI;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,SAASC,YAAYA,CAACC,OAAO,EAAEC,aAAa,EAAE;IAC5C,IAAIA,aAAa,IAAIX,UAAU,CAACQ,OAAO,EAAE;MACvC;IACF;IACAF,QAAQ,CAACI,OAAO,CAAC;EACnB;EACA,OAAO,CAACL,KAAK,EAAEI,YAAY,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}