{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(function (item) {\n    return item;\n  });\n}\nexport default function useLegacyItems(items, children) {\n  if (items) {\n    return items;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!children, 'Tabs', 'Tabs.TabPane is deprecated. Please use `items` directly.') : void 0;\n  var childrenItems = toArray(children).map(function (node) {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      var key = node.key,\n        props = node.props;\n      var _a = props || {},\n        tab = _a.tab,\n        restProps = __rest(_a, [\"tab\"]);\n      var item = _extends(_extends({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "toArray", "warning", "filter", "items", "item", "useLegacyItems", "children", "process", "env", "NODE_ENV", "childrenItems", "map", "node", "isValidElement", "key", "props", "_a", "tab", "restProps", "String", "label"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tabs/hooks/useLegacyItems.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(function (item) {\n    return item;\n  });\n}\nexport default function useLegacyItems(items, children) {\n  if (items) {\n    return items;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!children, 'Tabs', 'Tabs.TabPane is deprecated. Please use `items` directly.') : void 0;\n  var childrenItems = toArray(children).map(function (node) {\n    if ( /*#__PURE__*/React.isValidElement(node)) {\n      var key = node.key,\n        props = node.props;\n      var _a = props || {},\n        tab = _a.tab,\n        restProps = __rest(_a, [\"tab\"]);\n      var item = _extends(_extends({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,CAAC,UAAUE,IAAI,EAAE;IAClC,OAAOA,IAAI;EACb,CAAC,CAAC;AACJ;AACA,eAAe,SAASC,cAAcA,CAACF,KAAK,EAAEG,QAAQ,EAAE;EACtD,IAAIH,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EACAI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,OAAO,CAAC,CAACK,QAAQ,EAAE,MAAM,EAAE,0DAA0D,CAAC,GAAG,KAAK,CAAC;EACvI,IAAII,aAAa,GAAGV,OAAO,CAACM,QAAQ,CAAC,CAACK,GAAG,CAAC,UAAUC,IAAI,EAAE;IACxD,IAAK,aAAab,KAAK,CAACc,cAAc,CAACD,IAAI,CAAC,EAAE;MAC5C,IAAIE,GAAG,GAAGF,IAAI,CAACE,GAAG;QAChBC,KAAK,GAAGH,IAAI,CAACG,KAAK;MACpB,IAAIC,EAAE,GAAGD,KAAK,IAAI,CAAC,CAAC;QAClBE,GAAG,GAAGD,EAAE,CAACC,GAAG;QACZC,SAAS,GAAGjC,MAAM,CAAC+B,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;MACjC,IAAIZ,IAAI,GAAGpB,QAAQ,CAACA,QAAQ,CAAC;QAC3B8B,GAAG,EAAEK,MAAM,CAACL,GAAG;MACjB,CAAC,EAAEI,SAAS,CAAC,EAAE;QACbE,KAAK,EAAEH;MACT,CAAC,CAAC;MACF,OAAOb,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF,OAAOF,MAAM,CAACQ,aAAa,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}