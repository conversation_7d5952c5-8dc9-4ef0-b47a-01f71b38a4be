{"ast": null, "code": "import _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"selectable\", \"checkable\", \"expandedKeys\", \"selectedKeys\", \"checkedKeys\", \"loadedKeys\", \"loadingKeys\", \"halfCheckedKeys\", \"keyEntities\", \"disabled\", \"dragging\", \"dragOverNodeKey\", \"dropPosition\", \"motion\", \"height\", \"itemHeight\", \"virtual\", \"focusable\", \"activeItem\", \"focused\", \"tabIndex\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onActiveChange\", \"onListChangeStart\", \"onListChangeEnd\"];\n/**\n * Handle virtual list of the TreeNodes.\n */\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport MotionTreeNode from './MotionTreeNode';\nimport { findExpandedKeys, getExpandRange } from './utils/diffUtil';\nimport { getKey, getTreeNodeProps } from './utils/treeUtil';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar noop = function noop() {};\nexport var MOTION_KEY = \"RC_TREE_MOTION_\".concat(Math.random());\nvar MotionNode = {\n  key: MOTION_KEY\n};\nexport var MotionEntity = {\n  key: MOTION_KEY,\n  level: 0,\n  index: 0,\n  pos: '0',\n  node: MotionNode,\n  nodes: [MotionNode]\n};\nvar MotionFlattenData = {\n  parent: null,\n  children: [],\n  pos: MotionEntity.pos,\n  data: MotionNode,\n  title: null,\n  key: MOTION_KEY,\n  /** Hold empty list here since we do not use it */\n  isStart: [],\n  isEnd: []\n};\n/**\n * We only need get visible content items to play the animation.\n */\nexport function getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {\n  if (virtual === false || !height) {\n    return list;\n  }\n  return list.slice(0, Math.ceil(height / itemHeight) + 1);\n}\nfunction itemKey(item) {\n  var key = item.key,\n    pos = item.pos;\n  return getKey(key, pos);\n}\nfunction getAccessibilityPath(item) {\n  var path = String(item.data.key);\n  var current = item;\n  while (current.parent) {\n    current = current.parent;\n    path = \"\".concat(current.data.key, \" > \").concat(path);\n  }\n  return path;\n}\nvar NodeList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    data = props.data,\n    selectable = props.selectable,\n    checkable = props.checkable,\n    expandedKeys = props.expandedKeys,\n    selectedKeys = props.selectedKeys,\n    checkedKeys = props.checkedKeys,\n    loadedKeys = props.loadedKeys,\n    loadingKeys = props.loadingKeys,\n    halfCheckedKeys = props.halfCheckedKeys,\n    keyEntities = props.keyEntities,\n    disabled = props.disabled,\n    dragging = props.dragging,\n    dragOverNodeKey = props.dragOverNodeKey,\n    dropPosition = props.dropPosition,\n    motion = props.motion,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    virtual = props.virtual,\n    focusable = props.focusable,\n    activeItem = props.activeItem,\n    focused = props.focused,\n    tabIndex = props.tabIndex,\n    onKeyDown = props.onKeyDown,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onActiveChange = props.onActiveChange,\n    onListChangeStart = props.onListChangeStart,\n    onListChangeEnd = props.onListChangeEnd,\n    domProps = _objectWithoutProperties(props, _excluded);\n  // =============================== Ref ================================\n  var listRef = React.useRef(null);\n  var indentMeasurerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: function scrollTo(scroll) {\n        listRef.current.scrollTo(scroll);\n      },\n      getIndentWidth: function getIndentWidth() {\n        return indentMeasurerRef.current.offsetWidth;\n      }\n    };\n  });\n  // ============================== Motion ==============================\n  var _React$useState = React.useState(expandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevExpandedKeys = _React$useState2[0],\n    setPrevExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(data),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevData = _React$useState4[0],\n    setPrevData = _React$useState4[1];\n  var _React$useState5 = React.useState(data),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    transitionData = _React$useState6[0],\n    setTransitionData = _React$useState6[1];\n  var _React$useState7 = React.useState([]),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    transitionRange = _React$useState8[0],\n    setTransitionRange = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    motionType = _React$useState10[0],\n    setMotionType = _React$useState10[1];\n  // When motion end but data change, this will makes data back to previous one\n  var dataRef = React.useRef(data);\n  dataRef.current = data;\n  function onMotionEnd() {\n    var latestData = dataRef.current;\n    setPrevData(latestData);\n    setTransitionData(latestData);\n    setTransitionRange([]);\n    setMotionType(null);\n    onListChangeEnd();\n  }\n  // Do animation if expanded keys changed\n  // layoutEffect here to avoid blink of node removing\n  useLayoutEffect(function () {\n    setPrevExpandedKeys(expandedKeys);\n    var diffExpanded = findExpandedKeys(prevExpandedKeys, expandedKeys);\n    if (diffExpanded.key !== null) {\n      if (diffExpanded.add) {\n        var keyIndex = prevData.findIndex(function (_ref) {\n          var key = _ref.key;\n          return key === diffExpanded.key;\n        });\n        var rangeNodes = getMinimumRangeTransitionRange(getExpandRange(prevData, data, diffExpanded.key), virtual, height, itemHeight);\n        var newTransitionData = prevData.slice();\n        newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(newTransitionData);\n        setTransitionRange(rangeNodes);\n        setMotionType('show');\n      } else {\n        var _keyIndex = data.findIndex(function (_ref2) {\n          var key = _ref2.key;\n          return key === diffExpanded.key;\n        });\n        var _rangeNodes = getMinimumRangeTransitionRange(getExpandRange(data, prevData, diffExpanded.key), virtual, height, itemHeight);\n        var _newTransitionData = data.slice();\n        _newTransitionData.splice(_keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(_newTransitionData);\n        setTransitionRange(_rangeNodes);\n        setMotionType('hide');\n      }\n    } else if (prevData !== data) {\n      // If whole data changed, we just refresh the list\n      setPrevData(data);\n      setTransitionData(data);\n    }\n  }, [expandedKeys, data]);\n  // We should clean up motion if is changed by dragging\n  React.useEffect(function () {\n    if (!dragging) {\n      onMotionEnd();\n    }\n  }, [dragging]);\n  var mergedData = motion ? transitionData : data;\n  var treeNodeRequiredProps = {\n    expandedKeys: expandedKeys,\n    selectedKeys: selectedKeys,\n    loadedKeys: loadedKeys,\n    loadingKeys: loadingKeys,\n    checkedKeys: checkedKeys,\n    halfCheckedKeys: halfCheckedKeys,\n    dragOverNodeKey: dragOverNodeKey,\n    dropPosition: dropPosition,\n    keyEntities: keyEntities\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, focused && activeItem && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, getAccessibilityPath(activeItem)), /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"input\", {\n    style: HIDDEN_STYLE,\n    disabled: focusable === false || disabled,\n    tabIndex: focusable !== false ? tabIndex : null,\n    onKeyDown: onKeyDown,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    value: \"\",\n    onChange: noop,\n    \"aria-label\": \"for screen reader\"\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-treenode\"),\n    \"aria-hidden\": true,\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      visibility: 'hidden',\n      height: 0,\n      overflow: 'hidden',\n      border: 0,\n      padding: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: indentMeasurerRef,\n    className: \"\".concat(prefixCls, \"-indent-unit\")\n  }))), /*#__PURE__*/React.createElement(VirtualList, _extends({}, domProps, {\n    data: mergedData,\n    itemKey: itemKey,\n    height: height,\n    fullHeight: false,\n    virtual: virtual,\n    itemHeight: itemHeight,\n    prefixCls: \"\".concat(prefixCls, \"-list\"),\n    ref: listRef,\n    onVisibleChange: function onVisibleChange(originList, fullList) {\n      var originSet = new Set(originList);\n      var restList = fullList.filter(function (item) {\n        return !originSet.has(item);\n      });\n      // Motion node is not render. Skip motion\n      if (restList.some(function (item) {\n        return itemKey(item) === MOTION_KEY;\n      })) {\n        onMotionEnd();\n      }\n    }\n  }), function (treeNode) {\n    var pos = treeNode.pos,\n      restProps = _extends({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n      title = treeNode.title,\n      key = treeNode.key,\n      isStart = treeNode.isStart,\n      isEnd = treeNode.isEnd;\n    var mergedKey = getKey(key, pos);\n    delete restProps.key;\n    delete restProps.children;\n    var treeNodeProps = getTreeNodeProps(mergedKey, treeNodeRequiredProps);\n    return /*#__PURE__*/React.createElement(MotionTreeNode, _extends({}, restProps, treeNodeProps, {\n      title: title,\n      active: !!activeItem && key === activeItem.key,\n      pos: pos,\n      data: treeNode.data,\n      isStart: isStart,\n      isEnd: isEnd,\n      motion: motion,\n      motionNodes: key === MOTION_KEY ? transitionRange : null,\n      motionType: motionType,\n      onMotionStart: onListChangeStart,\n      onMotionEnd: onMotionEnd,\n      treeNodeRequiredProps: treeNodeRequiredProps,\n      onMouseMove: function onMouseMove() {\n        onActiveChange(null);\n      }\n    }));\n  }));\n});\nNodeList.displayName = 'NodeList';\nexport default NodeList;", "map": {"version": 3, "names": ["_objectDestructuringEmpty", "_extends", "_slicedToArray", "_objectWithoutProperties", "_excluded", "useLayoutEffect", "VirtualList", "React", "MotionTreeNode", "findExpandedKeys", "getExpandRange", "<PERSON><PERSON><PERSON>", "getTreeNodeProps", "HIDDEN_STYLE", "width", "height", "display", "overflow", "opacity", "border", "padding", "margin", "noop", "MOTION_KEY", "concat", "Math", "random", "MotionNode", "key", "MotionEntity", "level", "index", "pos", "node", "nodes", "MotionFlattenData", "parent", "children", "data", "title", "isStart", "isEnd", "getMinimumRangeTransitionRange", "list", "virtual", "itemHeight", "slice", "ceil", "itemKey", "item", "getAccessibilityPath", "path", "String", "current", "NodeList", "forwardRef", "props", "ref", "prefixCls", "selectable", "checkable", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON>", "checked<PERSON>eys", "loadedKeys", "loadingKeys", "halfC<PERSON>cked<PERSON>eys", "keyEntities", "disabled", "dragging", "dragOverNodeKey", "dropPosition", "motion", "focusable", "activeItem", "focused", "tabIndex", "onKeyDown", "onFocus", "onBlur", "onActiveChange", "onListChangeStart", "onListChangeEnd", "domProps", "listRef", "useRef", "indentMeasurerRef", "useImperativeHandle", "scrollTo", "scroll", "getIndentWidth", "offsetWidth", "_React$useState", "useState", "_React$useState2", "prevExpandedKeys", "setPrevExpandedKeys", "_React$useState3", "_React$useState4", "prevData", "setPrevData", "_React$useState5", "_React$useState6", "transitionData", "setTransitionData", "_React$useState7", "_React$useState8", "transitionRange", "setTransitionRange", "_React$useState9", "_React$useState10", "motionType", "setMotionType", "dataRef", "onMotionEnd", "latestData", "diffExpanded", "add", "keyIndex", "findIndex", "_ref", "rangeNodes", "newTransitionData", "splice", "_keyIndex", "_ref2", "_rangeNodes", "_newTransitionData", "useEffect", "mergedData", "treeNodeRequiredProps", "createElement", "Fragment", "style", "value", "onChange", "className", "position", "pointerEvents", "visibility", "fullHeight", "onVisibleChange", "originList", "fullList", "originSet", "Set", "restList", "filter", "has", "some", "treeNode", "restProps", "mergedKey", "treeNodeProps", "active", "motionNodes", "onMotionStart", "onMouseMove", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/NodeList.js"], "sourcesContent": ["import _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"selectable\", \"checkable\", \"expandedKeys\", \"selectedKeys\", \"checkedKeys\", \"loadedKeys\", \"loadingKeys\", \"halfCheckedKeys\", \"keyEntities\", \"disabled\", \"dragging\", \"dragOverNodeKey\", \"dropPosition\", \"motion\", \"height\", \"itemHeight\", \"virtual\", \"focusable\", \"activeItem\", \"focused\", \"tabIndex\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onActiveChange\", \"onListChangeStart\", \"onListChangeEnd\"];\n/**\n * Handle virtual list of the TreeNodes.\n */\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport MotionTreeNode from './MotionTreeNode';\nimport { findExpandedKeys, getExpandRange } from './utils/diffUtil';\nimport { getKey, getTreeNodeProps } from './utils/treeUtil';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar noop = function noop() {};\nexport var MOTION_KEY = \"RC_TREE_MOTION_\".concat(Math.random());\nvar MotionNode = {\n  key: MOTION_KEY\n};\nexport var MotionEntity = {\n  key: MOTION_KEY,\n  level: 0,\n  index: 0,\n  pos: '0',\n  node: MotionNode,\n  nodes: [MotionNode]\n};\nvar MotionFlattenData = {\n  parent: null,\n  children: [],\n  pos: MotionEntity.pos,\n  data: MotionNode,\n  title: null,\n  key: MOTION_KEY,\n  /** Hold empty list here since we do not use it */\n  isStart: [],\n  isEnd: []\n};\n/**\n * We only need get visible content items to play the animation.\n */\nexport function getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {\n  if (virtual === false || !height) {\n    return list;\n  }\n  return list.slice(0, Math.ceil(height / itemHeight) + 1);\n}\nfunction itemKey(item) {\n  var key = item.key,\n    pos = item.pos;\n  return getKey(key, pos);\n}\nfunction getAccessibilityPath(item) {\n  var path = String(item.data.key);\n  var current = item;\n  while (current.parent) {\n    current = current.parent;\n    path = \"\".concat(current.data.key, \" > \").concat(path);\n  }\n  return path;\n}\nvar NodeList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    data = props.data,\n    selectable = props.selectable,\n    checkable = props.checkable,\n    expandedKeys = props.expandedKeys,\n    selectedKeys = props.selectedKeys,\n    checkedKeys = props.checkedKeys,\n    loadedKeys = props.loadedKeys,\n    loadingKeys = props.loadingKeys,\n    halfCheckedKeys = props.halfCheckedKeys,\n    keyEntities = props.keyEntities,\n    disabled = props.disabled,\n    dragging = props.dragging,\n    dragOverNodeKey = props.dragOverNodeKey,\n    dropPosition = props.dropPosition,\n    motion = props.motion,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    virtual = props.virtual,\n    focusable = props.focusable,\n    activeItem = props.activeItem,\n    focused = props.focused,\n    tabIndex = props.tabIndex,\n    onKeyDown = props.onKeyDown,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onActiveChange = props.onActiveChange,\n    onListChangeStart = props.onListChangeStart,\n    onListChangeEnd = props.onListChangeEnd,\n    domProps = _objectWithoutProperties(props, _excluded);\n  // =============================== Ref ================================\n  var listRef = React.useRef(null);\n  var indentMeasurerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: function scrollTo(scroll) {\n        listRef.current.scrollTo(scroll);\n      },\n      getIndentWidth: function getIndentWidth() {\n        return indentMeasurerRef.current.offsetWidth;\n      }\n    };\n  });\n  // ============================== Motion ==============================\n  var _React$useState = React.useState(expandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevExpandedKeys = _React$useState2[0],\n    setPrevExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(data),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevData = _React$useState4[0],\n    setPrevData = _React$useState4[1];\n  var _React$useState5 = React.useState(data),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    transitionData = _React$useState6[0],\n    setTransitionData = _React$useState6[1];\n  var _React$useState7 = React.useState([]),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    transitionRange = _React$useState8[0],\n    setTransitionRange = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    motionType = _React$useState10[0],\n    setMotionType = _React$useState10[1];\n  // When motion end but data change, this will makes data back to previous one\n  var dataRef = React.useRef(data);\n  dataRef.current = data;\n  function onMotionEnd() {\n    var latestData = dataRef.current;\n    setPrevData(latestData);\n    setTransitionData(latestData);\n    setTransitionRange([]);\n    setMotionType(null);\n    onListChangeEnd();\n  }\n  // Do animation if expanded keys changed\n  // layoutEffect here to avoid blink of node removing\n  useLayoutEffect(function () {\n    setPrevExpandedKeys(expandedKeys);\n    var diffExpanded = findExpandedKeys(prevExpandedKeys, expandedKeys);\n    if (diffExpanded.key !== null) {\n      if (diffExpanded.add) {\n        var keyIndex = prevData.findIndex(function (_ref) {\n          var key = _ref.key;\n          return key === diffExpanded.key;\n        });\n        var rangeNodes = getMinimumRangeTransitionRange(getExpandRange(prevData, data, diffExpanded.key), virtual, height, itemHeight);\n        var newTransitionData = prevData.slice();\n        newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(newTransitionData);\n        setTransitionRange(rangeNodes);\n        setMotionType('show');\n      } else {\n        var _keyIndex = data.findIndex(function (_ref2) {\n          var key = _ref2.key;\n          return key === diffExpanded.key;\n        });\n        var _rangeNodes = getMinimumRangeTransitionRange(getExpandRange(data, prevData, diffExpanded.key), virtual, height, itemHeight);\n        var _newTransitionData = data.slice();\n        _newTransitionData.splice(_keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(_newTransitionData);\n        setTransitionRange(_rangeNodes);\n        setMotionType('hide');\n      }\n    } else if (prevData !== data) {\n      // If whole data changed, we just refresh the list\n      setPrevData(data);\n      setTransitionData(data);\n    }\n  }, [expandedKeys, data]);\n  // We should clean up motion if is changed by dragging\n  React.useEffect(function () {\n    if (!dragging) {\n      onMotionEnd();\n    }\n  }, [dragging]);\n  var mergedData = motion ? transitionData : data;\n  var treeNodeRequiredProps = {\n    expandedKeys: expandedKeys,\n    selectedKeys: selectedKeys,\n    loadedKeys: loadedKeys,\n    loadingKeys: loadingKeys,\n    checkedKeys: checkedKeys,\n    halfCheckedKeys: halfCheckedKeys,\n    dragOverNodeKey: dragOverNodeKey,\n    dropPosition: dropPosition,\n    keyEntities: keyEntities\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, focused && activeItem && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, getAccessibilityPath(activeItem)), /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"input\", {\n    style: HIDDEN_STYLE,\n    disabled: focusable === false || disabled,\n    tabIndex: focusable !== false ? tabIndex : null,\n    onKeyDown: onKeyDown,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    value: \"\",\n    onChange: noop,\n    \"aria-label\": \"for screen reader\"\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-treenode\"),\n    \"aria-hidden\": true,\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      visibility: 'hidden',\n      height: 0,\n      overflow: 'hidden',\n      border: 0,\n      padding: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: indentMeasurerRef,\n    className: \"\".concat(prefixCls, \"-indent-unit\")\n  }))), /*#__PURE__*/React.createElement(VirtualList, _extends({}, domProps, {\n    data: mergedData,\n    itemKey: itemKey,\n    height: height,\n    fullHeight: false,\n    virtual: virtual,\n    itemHeight: itemHeight,\n    prefixCls: \"\".concat(prefixCls, \"-list\"),\n    ref: listRef,\n    onVisibleChange: function onVisibleChange(originList, fullList) {\n      var originSet = new Set(originList);\n      var restList = fullList.filter(function (item) {\n        return !originSet.has(item);\n      });\n      // Motion node is not render. Skip motion\n      if (restList.some(function (item) {\n        return itemKey(item) === MOTION_KEY;\n      })) {\n        onMotionEnd();\n      }\n    }\n  }), function (treeNode) {\n    var pos = treeNode.pos,\n      restProps = _extends({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n      title = treeNode.title,\n      key = treeNode.key,\n      isStart = treeNode.isStart,\n      isEnd = treeNode.isEnd;\n    var mergedKey = getKey(key, pos);\n    delete restProps.key;\n    delete restProps.children;\n    var treeNodeProps = getTreeNodeProps(mergedKey, treeNodeRequiredProps);\n    return /*#__PURE__*/React.createElement(MotionTreeNode, _extends({}, restProps, treeNodeProps, {\n      title: title,\n      active: !!activeItem && key === activeItem.key,\n      pos: pos,\n      data: treeNode.data,\n      isStart: isStart,\n      isEnd: isEnd,\n      motion: motion,\n      motionNodes: key === MOTION_KEY ? transitionRange : null,\n      motionType: motionType,\n      onMotionStart: onListChangeStart,\n      onMotionEnd: onMotionEnd,\n      treeNodeRequiredProps: treeNodeRequiredProps,\n      onMouseMove: function onMouseMove() {\n        onActiveChange(null);\n      }\n    }));\n  }));\n});\nNodeList.displayName = 'NodeList';\nexport default NodeList;"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;AACpa;AACA;AACA;AACA,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,kBAAkB;AACnE,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC3D,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG,CAAC,CAAC;AAC7B,OAAO,IAAIC,UAAU,GAAG,iBAAiB,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;AAC/D,IAAIC,UAAU,GAAG;EACfC,GAAG,EAAEL;AACP,CAAC;AACD,OAAO,IAAIM,YAAY,GAAG;EACxBD,GAAG,EAAEL,UAAU;EACfO,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAEN,UAAU;EAChBO,KAAK,EAAE,CAACP,UAAU;AACpB,CAAC;AACD,IAAIQ,iBAAiB,GAAG;EACtBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,EAAE;EACZL,GAAG,EAAEH,YAAY,CAACG,GAAG;EACrBM,IAAI,EAAEX,UAAU;EAChBY,KAAK,EAAE,IAAI;EACXX,GAAG,EAAEL,UAAU;EACf;EACAiB,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAEC,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,EAAE;EAChF,IAAID,OAAO,KAAK,KAAK,IAAI,CAAC7B,MAAM,EAAE;IAChC,OAAO4B,IAAI;EACb;EACA,OAAOA,IAAI,CAACG,KAAK,CAAC,CAAC,EAAErB,IAAI,CAACsB,IAAI,CAAChC,MAAM,GAAG8B,UAAU,CAAC,GAAG,CAAC,CAAC;AAC1D;AACA,SAASG,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIrB,GAAG,GAAGqB,IAAI,CAACrB,GAAG;IAChBI,GAAG,GAAGiB,IAAI,CAACjB,GAAG;EAChB,OAAOrB,MAAM,CAACiB,GAAG,EAAEI,GAAG,CAAC;AACzB;AACA,SAASkB,oBAAoBA,CAACD,IAAI,EAAE;EAClC,IAAIE,IAAI,GAAGC,MAAM,CAACH,IAAI,CAACX,IAAI,CAACV,GAAG,CAAC;EAChC,IAAIyB,OAAO,GAAGJ,IAAI;EAClB,OAAOI,OAAO,CAACjB,MAAM,EAAE;IACrBiB,OAAO,GAAGA,OAAO,CAACjB,MAAM;IACxBe,IAAI,GAAG,EAAE,CAAC3B,MAAM,CAAC6B,OAAO,CAACf,IAAI,CAACV,GAAG,EAAE,KAAK,CAAC,CAACJ,MAAM,CAAC2B,IAAI,CAAC;EACxD;EACA,OAAOA,IAAI;AACb;AACA,IAAIG,QAAQ,GAAG,aAAa/C,KAAK,CAACgD,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACjE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BpB,IAAI,GAAGkB,KAAK,CAAClB,IAAI;IACjBqB,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,eAAe,GAAGV,KAAK,CAACU,eAAe;IACvCC,WAAW,GAAGX,KAAK,CAACW,WAAW;IAC/BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,eAAe,GAAGd,KAAK,CAACc,eAAe;IACvCC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IACrBzD,MAAM,GAAGyC,KAAK,CAACzC,MAAM;IACrB8B,UAAU,GAAGW,KAAK,CAACX,UAAU;IAC7BD,OAAO,GAAGY,KAAK,CAACZ,OAAO;IACvB6B,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,OAAO,GAAGnB,KAAK,CAACmB,OAAO;IACvBC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,OAAO,GAAGtB,KAAK,CAACsB,OAAO;IACvBC,MAAM,GAAGvB,KAAK,CAACuB,MAAM;IACrBC,cAAc,GAAGxB,KAAK,CAACwB,cAAc;IACrCC,iBAAiB,GAAGzB,KAAK,CAACyB,iBAAiB;IAC3CC,eAAe,GAAG1B,KAAK,CAAC0B,eAAe;IACvCC,QAAQ,GAAGhF,wBAAwB,CAACqD,KAAK,EAAEpD,SAAS,CAAC;EACvD;EACA,IAAIgF,OAAO,GAAG7E,KAAK,CAAC8E,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,iBAAiB,GAAG/E,KAAK,CAAC8E,MAAM,CAAC,IAAI,CAAC;EAC1C9E,KAAK,CAACgF,mBAAmB,CAAC9B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL+B,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClCL,OAAO,CAAC/B,OAAO,CAACmC,QAAQ,CAACC,MAAM,CAAC;MAClC,CAAC;MACDC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;QACxC,OAAOJ,iBAAiB,CAACjC,OAAO,CAACsC,WAAW;MAC9C;IACF,CAAC;EACH,CAAC,CAAC;EACF;EACA,IAAIC,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAChC,YAAY,CAAC;IAChDiC,gBAAgB,GAAG5F,cAAc,CAAC0F,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3C,IAAIG,gBAAgB,GAAG1F,KAAK,CAACsF,QAAQ,CAACvD,IAAI,CAAC;IACzC4D,gBAAgB,GAAGhG,cAAc,CAAC+F,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,gBAAgB,GAAG9F,KAAK,CAACsF,QAAQ,CAACvD,IAAI,CAAC;IACzCgE,gBAAgB,GAAGpG,cAAc,CAACmG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,gBAAgB,GAAGlG,KAAK,CAACsF,QAAQ,CAAC,EAAE,CAAC;IACvCa,gBAAgB,GAAGxG,cAAc,CAACuG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,gBAAgB,GAAGtG,KAAK,CAACsF,QAAQ,CAAC,IAAI,CAAC;IACzCiB,iBAAiB,GAAG5G,cAAc,CAAC2G,gBAAgB,EAAE,CAAC,CAAC;IACvDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,aAAa,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACtC;EACA,IAAIG,OAAO,GAAG1G,KAAK,CAAC8E,MAAM,CAAC/C,IAAI,CAAC;EAChC2E,OAAO,CAAC5D,OAAO,GAAGf,IAAI;EACtB,SAAS4E,WAAWA,CAAA,EAAG;IACrB,IAAIC,UAAU,GAAGF,OAAO,CAAC5D,OAAO;IAChC+C,WAAW,CAACe,UAAU,CAAC;IACvBX,iBAAiB,CAACW,UAAU,CAAC;IAC7BP,kBAAkB,CAAC,EAAE,CAAC;IACtBI,aAAa,CAAC,IAAI,CAAC;IACnB9B,eAAe,CAAC,CAAC;EACnB;EACA;EACA;EACA7E,eAAe,CAAC,YAAY;IAC1B2F,mBAAmB,CAACnC,YAAY,CAAC;IACjC,IAAIuD,YAAY,GAAG3G,gBAAgB,CAACsF,gBAAgB,EAAElC,YAAY,CAAC;IACnE,IAAIuD,YAAY,CAACxF,GAAG,KAAK,IAAI,EAAE;MAC7B,IAAIwF,YAAY,CAACC,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAGnB,QAAQ,CAACoB,SAAS,CAAC,UAAUC,IAAI,EAAE;UAChD,IAAI5F,GAAG,GAAG4F,IAAI,CAAC5F,GAAG;UAClB,OAAOA,GAAG,KAAKwF,YAAY,CAACxF,GAAG;QACjC,CAAC,CAAC;QACF,IAAI6F,UAAU,GAAG/E,8BAA8B,CAAChC,cAAc,CAACyF,QAAQ,EAAE7D,IAAI,EAAE8E,YAAY,CAACxF,GAAG,CAAC,EAAEgB,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,CAAC;QAC9H,IAAI6E,iBAAiB,GAAGvB,QAAQ,CAACrD,KAAK,CAAC,CAAC;QACxC4E,iBAAiB,CAACC,MAAM,CAACL,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAEnF,iBAAiB,CAAC;QAC5DqE,iBAAiB,CAACkB,iBAAiB,CAAC;QACpCd,kBAAkB,CAACa,UAAU,CAAC;QAC9BT,aAAa,CAAC,MAAM,CAAC;MACvB,CAAC,MAAM;QACL,IAAIY,SAAS,GAAGtF,IAAI,CAACiF,SAAS,CAAC,UAAUM,KAAK,EAAE;UAC9C,IAAIjG,GAAG,GAAGiG,KAAK,CAACjG,GAAG;UACnB,OAAOA,GAAG,KAAKwF,YAAY,CAACxF,GAAG;QACjC,CAAC,CAAC;QACF,IAAIkG,WAAW,GAAGpF,8BAA8B,CAAChC,cAAc,CAAC4B,IAAI,EAAE6D,QAAQ,EAAEiB,YAAY,CAACxF,GAAG,CAAC,EAAEgB,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,CAAC;QAC/H,IAAIkF,kBAAkB,GAAGzF,IAAI,CAACQ,KAAK,CAAC,CAAC;QACrCiF,kBAAkB,CAACJ,MAAM,CAACC,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEzF,iBAAiB,CAAC;QAC9DqE,iBAAiB,CAACuB,kBAAkB,CAAC;QACrCnB,kBAAkB,CAACkB,WAAW,CAAC;QAC/Bd,aAAa,CAAC,MAAM,CAAC;MACvB;IACF,CAAC,MAAM,IAAIb,QAAQ,KAAK7D,IAAI,EAAE;MAC5B;MACA8D,WAAW,CAAC9D,IAAI,CAAC;MACjBkE,iBAAiB,CAAClE,IAAI,CAAC;IACzB;EACF,CAAC,EAAE,CAACuB,YAAY,EAAEvB,IAAI,CAAC,CAAC;EACxB;EACA/B,KAAK,CAACyH,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC3D,QAAQ,EAAE;MACb6C,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC7C,QAAQ,CAAC,CAAC;EACd,IAAI4D,UAAU,GAAGzD,MAAM,GAAG+B,cAAc,GAAGjE,IAAI;EAC/C,IAAI4F,qBAAqB,GAAG;IAC1BrE,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BE,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA,WAAW;IACxBF,WAAW,EAAEA,WAAW;IACxBG,eAAe,EAAEA,eAAe;IAChCI,eAAe,EAAEA,eAAe;IAChCC,YAAY,EAAEA,YAAY;IAC1BJ,WAAW,EAAEA;EACf,CAAC;EACD,OAAO,aAAa5D,KAAK,CAAC4H,aAAa,CAAC5H,KAAK,CAAC6H,QAAQ,EAAE,IAAI,EAAEzD,OAAO,IAAID,UAAU,IAAI,aAAanE,KAAK,CAAC4H,aAAa,CAAC,MAAM,EAAE;IAC9HE,KAAK,EAAExH,YAAY;IACnB,WAAW,EAAE;EACf,CAAC,EAAEqC,oBAAoB,CAACwB,UAAU,CAAC,CAAC,EAAE,aAAanE,KAAK,CAAC4H,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa5H,KAAK,CAAC4H,aAAa,CAAC,OAAO,EAAE;IAC5HE,KAAK,EAAExH,YAAY;IACnBuD,QAAQ,EAAEK,SAAS,KAAK,KAAK,IAAIL,QAAQ;IACzCQ,QAAQ,EAAEH,SAAS,KAAK,KAAK,GAAGG,QAAQ,GAAG,IAAI;IAC/CC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACduD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAEjH,IAAI;IACd,YAAY,EAAE;EAChB,CAAC,CAAC,CAAC,EAAE,aAAaf,KAAK,CAAC4H,aAAa,CAAC,KAAK,EAAE;IAC3CK,SAAS,EAAE,EAAE,CAAChH,MAAM,CAACkC,SAAS,EAAE,WAAW,CAAC;IAC5C,aAAa,EAAE,IAAI;IACnB2E,KAAK,EAAE;MACLI,QAAQ,EAAE,UAAU;MACpBC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE,QAAQ;MACpB5H,MAAM,EAAE,CAAC;MACTE,QAAQ,EAAE,QAAQ;MAClBE,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE;IACX;EACF,CAAC,EAAE,aAAab,KAAK,CAAC4H,aAAa,CAAC,KAAK,EAAE;IACzCK,SAAS,EAAE,EAAE,CAAChH,MAAM,CAACkC,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAanD,KAAK,CAAC4H,aAAa,CAAC,KAAK,EAAE;IACzC1E,GAAG,EAAE6B,iBAAiB;IACtBkD,SAAS,EAAE,EAAE,CAAChH,MAAM,CAACkC,SAAS,EAAE,cAAc;EAChD,CAAC,CAAC,CAAC,CAAC,EAAE,aAAanD,KAAK,CAAC4H,aAAa,CAAC7H,WAAW,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEkF,QAAQ,EAAE;IACzE7C,IAAI,EAAE2F,UAAU;IAChBjF,OAAO,EAAEA,OAAO;IAChBjC,MAAM,EAAEA,MAAM;IACd6H,UAAU,EAAE,KAAK;IACjBhG,OAAO,EAAEA,OAAO;IAChBC,UAAU,EAAEA,UAAU;IACtBa,SAAS,EAAE,EAAE,CAAClC,MAAM,CAACkC,SAAS,EAAE,OAAO,CAAC;IACxCD,GAAG,EAAE2B,OAAO;IACZyD,eAAe,EAAE,SAASA,eAAeA,CAACC,UAAU,EAAEC,QAAQ,EAAE;MAC9D,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAACH,UAAU,CAAC;MACnC,IAAII,QAAQ,GAAGH,QAAQ,CAACI,MAAM,CAAC,UAAUlG,IAAI,EAAE;QAC7C,OAAO,CAAC+F,SAAS,CAACI,GAAG,CAACnG,IAAI,CAAC;MAC7B,CAAC,CAAC;MACF;MACA,IAAIiG,QAAQ,CAACG,IAAI,CAAC,UAAUpG,IAAI,EAAE;QAChC,OAAOD,OAAO,CAACC,IAAI,CAAC,KAAK1B,UAAU;MACrC,CAAC,CAAC,EAAE;QACF2F,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,CAAC,EAAE,UAAUoC,QAAQ,EAAE;IACtB,IAAItH,GAAG,GAAGsH,QAAQ,CAACtH,GAAG;MACpBuH,SAAS,GAAGtJ,QAAQ,CAAC,CAAC,CAAC,GAAGD,yBAAyB,CAACsJ,QAAQ,CAAChH,IAAI,CAAC,EAAEgH,QAAQ,CAAChH,IAAI,CAAC,CAAC;MACnFC,KAAK,GAAG+G,QAAQ,CAAC/G,KAAK;MACtBX,GAAG,GAAG0H,QAAQ,CAAC1H,GAAG;MAClBY,OAAO,GAAG8G,QAAQ,CAAC9G,OAAO;MAC1BC,KAAK,GAAG6G,QAAQ,CAAC7G,KAAK;IACxB,IAAI+G,SAAS,GAAG7I,MAAM,CAACiB,GAAG,EAAEI,GAAG,CAAC;IAChC,OAAOuH,SAAS,CAAC3H,GAAG;IACpB,OAAO2H,SAAS,CAAClH,QAAQ;IACzB,IAAIoH,aAAa,GAAG7I,gBAAgB,CAAC4I,SAAS,EAAEtB,qBAAqB,CAAC;IACtE,OAAO,aAAa3H,KAAK,CAAC4H,aAAa,CAAC3H,cAAc,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEsJ,SAAS,EAAEE,aAAa,EAAE;MAC7FlH,KAAK,EAAEA,KAAK;MACZmH,MAAM,EAAE,CAAC,CAAChF,UAAU,IAAI9C,GAAG,KAAK8C,UAAU,CAAC9C,GAAG;MAC9CI,GAAG,EAAEA,GAAG;MACRM,IAAI,EAAEgH,QAAQ,CAAChH,IAAI;MACnBE,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA,KAAK;MACZ+B,MAAM,EAAEA,MAAM;MACdmF,WAAW,EAAE/H,GAAG,KAAKL,UAAU,GAAGoF,eAAe,GAAG,IAAI;MACxDI,UAAU,EAAEA,UAAU;MACtB6C,aAAa,EAAE3E,iBAAiB;MAChCiC,WAAW,EAAEA,WAAW;MACxBgB,qBAAqB,EAAEA,qBAAqB;MAC5C2B,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC7E,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF1B,QAAQ,CAACwG,WAAW,GAAG,UAAU;AACjC,eAAexG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}