{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _value = _interopRequireDefault(require(\"./value.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(a, b) {\n  var i = {},\n    c = {},\n    k;\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n  for (k in b) {\n    if (k in a) {\n      i[k] = (0, _value.default)(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n  return function (t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_value", "_interopRequireDefault", "require", "obj", "__esModule", "a", "b", "i", "c", "k", "t"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/object.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _value = _interopRequireDefault(require(\"./value.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(a, b) {\n  var i = {},\n      c = {},\n      k;\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = (0, _value.default)(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function (t) {\n    for (k in i) c[k] = i[k](t);\n\n    return c;\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC;EACL,IAAIJ,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAIC,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAEA,CAAC,GAAG,CAAC,CAAC;EAE/C,KAAKG,CAAC,IAAIH,CAAC,EAAE;IACX,IAAIG,CAAC,IAAIJ,CAAC,EAAE;MACVE,CAAC,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAET,MAAM,CAACF,OAAO,EAAEO,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC;IACxC,CAAC,MAAM;MACLD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;IACb;EACF;EAEA,OAAO,UAAUC,CAAC,EAAE;IAClB,KAAKD,CAAC,IAAIF,CAAC,EAAEC,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAACC,CAAC,CAAC;IAE3B,OAAOF,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}