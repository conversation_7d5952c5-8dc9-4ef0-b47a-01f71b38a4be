{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = every;\nfunction every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "every", "values", "test", "TypeError", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/every.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = every;\n\nfunction every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n\n  return true;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,SAASA,KAAKA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC3B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,MAAMN,KAAK,IAAIG,MAAM,EAAE;IAC1B,IAAI,CAACC,IAAI,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,EAAE;MACjC,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script"}