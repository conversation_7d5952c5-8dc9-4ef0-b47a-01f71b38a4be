{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nfunction _default(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\":\n        i0 = i1 = i;\n        break;\n      case \"0\":\n        if (i0 === 0) i0 = i;\n        i1 = i;\n        break;\n      default:\n        if (!+s[i]) break out;\n        if (i0 > 0) i0 = 0;\n        break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "s", "out", "n", "length", "i", "i0", "i1", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatTrim.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nfunction _default(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\":\n        i0 = i1 = i;\n        break;\n\n      case \"0\":\n        if (i0 === 0) i0 = i;\n        i1 = i;\n        break;\n\n      default:\n        if (!+s[i]) break out;\n        if (i0 > 0) i0 = 0;\n        break;\n    }\n  }\n\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;;AAE1B;AACA,SAASA,QAAQA,CAACC,CAAC,EAAE;EACnBC,GAAG,EAAE,KAAK,IAAIC,CAAC,GAAGF,CAAC,CAACG,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,EAAEC,EAAE,EAAEF,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IAC1D,QAAQJ,CAAC,CAACI,CAAC,CAAC;MACV,KAAK,GAAG;QACNC,EAAE,GAAGC,EAAE,GAAGF,CAAC;QACX;MAEF,KAAK,GAAG;QACN,IAAIC,EAAE,KAAK,CAAC,EAAEA,EAAE,GAAGD,CAAC;QACpBE,EAAE,GAAGF,CAAC;QACN;MAEF;QACE,IAAI,CAAC,CAACJ,CAAC,CAACI,CAAC,CAAC,EAAE,MAAMH,GAAG;QACrB,IAAII,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC;QAClB;IACJ;EACF;EAEA,OAAOA,EAAE,GAAG,CAAC,GAAGL,CAAC,CAACO,KAAK,CAAC,CAAC,EAAEF,EAAE,CAAC,GAAGL,CAAC,CAACO,KAAK,CAACD,EAAE,GAAG,CAAC,CAAC,GAAGN,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}