{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = mode;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction mode(values, valueof) {\n  const counts = new _index.InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "mode", "_index", "require", "values", "valueof", "counts", "InternMap", "undefined", "set", "get", "index", "modeValue", "modeCount", "count"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/mode.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = mode;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction mode(values, valueof) {\n  const counts = new _index.InternMap();\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n\n  let modeValue;\n  let modeCount = 0;\n\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n\n  return modeValue;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,IAAIA,CAACG,MAAM,EAAEC,OAAO,EAAE;EAC7B,MAAMC,MAAM,GAAG,IAAIJ,MAAM,CAACK,SAAS,CAAC,CAAC;EAErC,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,IAAIT,KAAK,IAAIK,MAAM,EAAE;MACxB,IAAIL,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAIA,KAAK,EAAE;QACnCO,MAAM,CAACG,GAAG,CAACV,KAAK,EAAE,CAACO,MAAM,CAACI,GAAG,CAACX,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjD;IACF;EACF,CAAC,MAAM;IACL,IAAIY,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIZ,KAAK,IAAIK,MAAM,EAAE;MACxB,IAAI,CAACL,KAAK,GAAGM,OAAO,CAACN,KAAK,EAAE,EAAEY,KAAK,EAAEP,MAAM,CAAC,KAAK,IAAI,IAAIL,KAAK,IAAIA,KAAK,EAAE;QACvEO,MAAM,CAACG,GAAG,CAACV,KAAK,EAAE,CAACO,MAAM,CAACI,GAAG,CAACX,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjD;IACF;EACF;EAEA,IAAIa,SAAS;EACb,IAAIC,SAAS,GAAG,CAAC;EAEjB,KAAK,MAAM,CAACd,KAAK,EAAEe,KAAK,CAAC,IAAIR,MAAM,EAAE;IACnC,IAAIQ,KAAK,GAAGD,SAAS,EAAE;MACrBA,SAAS,GAAGC,KAAK;MACjBF,SAAS,GAAGb,KAAK;IACnB;EACF;EAEA,OAAOa,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}