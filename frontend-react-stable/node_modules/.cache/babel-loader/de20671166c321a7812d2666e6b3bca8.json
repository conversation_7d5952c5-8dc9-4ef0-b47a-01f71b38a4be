{"ast": null, "code": "import Collapse from './Collapse';\nexport default Collapse;\nvar Panel = Collapse.Panel;\nexport { Panel };", "map": {"version": 3, "names": ["Collapse", "Panel"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-collapse/es/index.js"], "sourcesContent": ["import Collapse from './Collapse';\nexport default Collapse;\nvar Panel = Collapse.Panel;\nexport { Panel };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ;AACvB,IAAIC,KAAK,GAAGD,QAAQ,CAACC,KAAK;AAC1B,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}