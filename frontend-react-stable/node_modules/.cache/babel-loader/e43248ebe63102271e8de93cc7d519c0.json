{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { PresetColorTypes, PresetStatusColorTypes } from '../_util/colors';\nimport Wave from '../_util/wave';\nimport warning from '../_util/warning';\nimport CheckableTag from './CheckableTag';\nvar PresetColorRegex = new RegExp(\"^(\".concat(PresetColorTypes.join('|'), \")(-inverse)?$\"));\nvar PresetStatusColorRegex = new RegExp(\"^(\".concat(PresetStatusColorTypes.join('|'), \")$\"));\nvar InternalTag = function InternalTag(_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    style = _a.style,\n    children = _a.children,\n    icon = _a.icon,\n    color = _a.color,\n    onClose = _a.onClose,\n    closeIcon = _a.closeIcon,\n    _a$closable = _a.closable,\n    closable = _a$closable === void 0 ? false : _a$closable,\n    props = __rest(_a, [\"prefixCls\", \"className\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"closeIcon\", \"closable\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!('visible' in props), 'Tag', '`visible` will be removed in next major version, please use `visible && <Tag />` instead.') : void 0;\n  }\n  React.useEffect(function () {\n    if ('visible' in props) {\n      setVisible(props.visible);\n    }\n  }, [props.visible]);\n  var isPresetColor = function isPresetColor() {\n    if (!color) {\n      return false;\n    }\n    return PresetColorRegex.test(color) || PresetStatusColorRegex.test(color);\n  };\n  var tagStyle = _extends({\n    backgroundColor: color && !isPresetColor() ? color : undefined\n  }, style);\n  var presetColor = isPresetColor();\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var tagClassName = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(color), presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-color\"), color && !presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-hidden\"), !visible), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var handleCloseClick = function handleCloseClick(e) {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    if (!('visible' in props)) {\n      setVisible(false);\n    }\n  };\n  var renderCloseIcon = function renderCloseIcon() {\n    if (closable) {\n      return closeIcon ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      }, closeIcon) : /*#__PURE__*/React.createElement(CloseOutlined, {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      });\n    }\n    return null;\n  };\n  var isNeedWave = 'onClick' in props || children && children.type === 'a';\n  var tagProps = omit(props, ['visible']);\n  var iconNode = icon || null;\n  var kids = iconNode ? /*#__PURE__*/React.createElement(React.Fragment, null, iconNode, /*#__PURE__*/React.createElement(\"span\", null, children)) : children;\n  var tagNode = /*#__PURE__*/React.createElement(\"span\", _extends({}, tagProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, renderCloseIcon());\n  return isNeedWave ? /*#__PURE__*/React.createElement(Wave, null, tagNode) : tagNode;\n};\nvar Tag = /*#__PURE__*/React.forwardRef(InternalTag);\nif (process.env.NODE_ENV !== 'production') {\n  Tag.displayName = 'Tag';\n}\nTag.CheckableTag = CheckableTag;\nexport default Tag;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "CloseOutlined", "classNames", "omit", "React", "ConfigContext", "PresetColorTypes", "PresetStatusColorTypes", "Wave", "warning", "CheckableTag", "PresetColorRegex", "RegExp", "concat", "join", "PresetStatusColorRegex", "InternalTag", "_a", "ref", "_classNames", "customizePrefixCls", "prefixCls", "className", "style", "children", "icon", "color", "onClose", "closeIcon", "_a$closable", "closable", "props", "_React$useContext", "useContext", "getPrefixCls", "direction", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "process", "env", "NODE_ENV", "useEffect", "isPresetColor", "test", "tagStyle", "backgroundColor", "undefined", "presetColor", "tagClassName", "handleCloseClick", "stopPropagation", "defaultPrevented", "renderCloseIcon", "createElement", "onClick", "isNeedWave", "type", "tagProps", "iconNode", "kids", "Fragment", "tagNode", "Tag", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tag/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { PresetColorTypes, PresetStatusColorTypes } from '../_util/colors';\nimport Wave from '../_util/wave';\nimport warning from '../_util/warning';\nimport CheckableTag from './CheckableTag';\nvar PresetColorRegex = new RegExp(\"^(\".concat(PresetColorTypes.join('|'), \")(-inverse)?$\"));\nvar PresetStatusColorRegex = new RegExp(\"^(\".concat(PresetStatusColorTypes.join('|'), \")$\"));\nvar InternalTag = function InternalTag(_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    style = _a.style,\n    children = _a.children,\n    icon = _a.icon,\n    color = _a.color,\n    onClose = _a.onClose,\n    closeIcon = _a.closeIcon,\n    _a$closable = _a.closable,\n    closable = _a$closable === void 0 ? false : _a$closable,\n    props = __rest(_a, [\"prefixCls\", \"className\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"closeIcon\", \"closable\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!('visible' in props), 'Tag', '`visible` will be removed in next major version, please use `visible && <Tag />` instead.') : void 0;\n  }\n  React.useEffect(function () {\n    if ('visible' in props) {\n      setVisible(props.visible);\n    }\n  }, [props.visible]);\n  var isPresetColor = function isPresetColor() {\n    if (!color) {\n      return false;\n    }\n    return PresetColorRegex.test(color) || PresetStatusColorRegex.test(color);\n  };\n  var tagStyle = _extends({\n    backgroundColor: color && !isPresetColor() ? color : undefined\n  }, style);\n  var presetColor = isPresetColor();\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var tagClassName = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(color), presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-color\"), color && !presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-hidden\"), !visible), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var handleCloseClick = function handleCloseClick(e) {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    if (!('visible' in props)) {\n      setVisible(false);\n    }\n  };\n  var renderCloseIcon = function renderCloseIcon() {\n    if (closable) {\n      return closeIcon ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      }, closeIcon) : /*#__PURE__*/React.createElement(CloseOutlined, {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      });\n    }\n    return null;\n  };\n  var isNeedWave = 'onClick' in props || children && children.type === 'a';\n  var tagProps = omit(props, ['visible']);\n  var iconNode = icon || null;\n  var kids = iconNode ? /*#__PURE__*/React.createElement(React.Fragment, null, iconNode, /*#__PURE__*/React.createElement(\"span\", null, children)) : children;\n  var tagNode = /*#__PURE__*/React.createElement(\"span\", _extends({}, tagProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, renderCloseIcon());\n  return isNeedWave ? /*#__PURE__*/React.createElement(Wave, null, tagNode) : tagNode;\n};\nvar Tag = /*#__PURE__*/React.forwardRef(InternalTag);\nif (process.env.NODE_ENV !== 'production') {\n  Tag.displayName = 'Tag';\n}\nTag.CheckableTag = CheckableTag;\nexport default Tag;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,iBAAiB;AAC1E,OAAOC,IAAI,MAAM,eAAe;AAChC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,gBAAgB,GAAG,IAAIC,MAAM,CAAC,IAAI,CAACC,MAAM,CAACP,gBAAgB,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;AAC3F,IAAIC,sBAAsB,GAAG,IAAIH,MAAM,CAAC,IAAI,CAACC,MAAM,CAACN,sBAAsB,CAACO,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5F,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAC9C,IAAIC,WAAW;EACf,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACnCC,SAAS,GAAGL,EAAE,CAACK,SAAS;IACxBC,KAAK,GAAGN,EAAE,CAACM,KAAK;IAChBC,QAAQ,GAAGP,EAAE,CAACO,QAAQ;IACtBC,IAAI,GAAGR,EAAE,CAACQ,IAAI;IACdC,KAAK,GAAGT,EAAE,CAACS,KAAK;IAChBC,OAAO,GAAGV,EAAE,CAACU,OAAO;IACpBC,SAAS,GAAGX,EAAE,CAACW,SAAS;IACxBC,WAAW,GAAGZ,EAAE,CAACa,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACvDE,KAAK,GAAG5C,MAAM,CAAC8B,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EAC1H,IAAIe,iBAAiB,GAAG5B,KAAK,CAAC6B,UAAU,CAAC5B,aAAa,CAAC;IACrD6B,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,eAAe,GAAGhC,KAAK,CAACiC,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGpD,cAAc,CAACkD,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlC,OAAO,CAAC,EAAE,SAAS,IAAIsB,KAAK,CAAC,EAAE,KAAK,EAAE,2FAA2F,CAAC,GAAG,KAAK,CAAC;EACrL;EACA3B,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,IAAI,SAAS,IAAIb,KAAK,EAAE;MACtBS,UAAU,CAACT,KAAK,CAACQ,OAAO,CAAC;IAC3B;EACF,CAAC,EAAE,CAACR,KAAK,CAACQ,OAAO,CAAC,CAAC;EACnB,IAAIM,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI,CAACnB,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAOf,gBAAgB,CAACmC,IAAI,CAACpB,KAAK,CAAC,IAAIX,sBAAsB,CAAC+B,IAAI,CAACpB,KAAK,CAAC;EAC3E,CAAC;EACD,IAAIqB,QAAQ,GAAG9D,QAAQ,CAAC;IACtB+D,eAAe,EAAEtB,KAAK,IAAI,CAACmB,aAAa,CAAC,CAAC,GAAGnB,KAAK,GAAGuB;EACvD,CAAC,EAAE1B,KAAK,CAAC;EACT,IAAI2B,WAAW,GAAGL,aAAa,CAAC,CAAC;EACjC,IAAIxB,SAAS,GAAGa,YAAY,CAAC,KAAK,EAAEd,kBAAkB,CAAC;EACvD,IAAI+B,YAAY,GAAGjD,UAAU,CAACmB,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAEnC,eAAe,CAACmC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,GAAG,CAAC,CAACR,MAAM,CAACa,KAAK,CAAC,EAAEwB,WAAW,CAAC,EAAElE,eAAe,CAACmC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,YAAY,CAAC,EAAEK,KAAK,IAAI,CAACwB,WAAW,CAAC,EAAElE,eAAe,CAACmC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,SAAS,CAAC,EAAE,CAACkB,OAAO,CAAC,EAAEvD,eAAe,CAACmC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,MAAM,CAAC,EAAEc,SAAS,KAAK,KAAK,CAAC,EAAEhB,WAAW,GAAGG,SAAS,CAAC;EAC3Z,IAAI8B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC/D,CAAC,EAAE;IAClDA,CAAC,CAACgE,eAAe,CAAC,CAAC;IACnB1B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACtC,CAAC,CAAC;IAC5D,IAAIA,CAAC,CAACiE,gBAAgB,EAAE;MACtB;IACF;IACA,IAAI,EAAE,SAAS,IAAIvB,KAAK,CAAC,EAAE;MACzBS,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,IAAIe,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIzB,QAAQ,EAAE;MACZ,OAAOF,SAAS,GAAG,aAAaxB,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE;QAC1DlC,SAAS,EAAE,EAAE,CAACT,MAAM,CAACQ,SAAS,EAAE,aAAa,CAAC;QAC9CoC,OAAO,EAAEL;MACX,CAAC,EAAExB,SAAS,CAAC,GAAG,aAAaxB,KAAK,CAACoD,aAAa,CAACvD,aAAa,EAAE;QAC9DqB,SAAS,EAAE,EAAE,CAACT,MAAM,CAACQ,SAAS,EAAE,aAAa,CAAC;QAC9CoC,OAAO,EAAEL;MACX,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIM,UAAU,GAAG,SAAS,IAAI3B,KAAK,IAAIP,QAAQ,IAAIA,QAAQ,CAACmC,IAAI,KAAK,GAAG;EACxE,IAAIC,QAAQ,GAAGzD,IAAI,CAAC4B,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC;EACvC,IAAI8B,QAAQ,GAAGpC,IAAI,IAAI,IAAI;EAC3B,IAAIqC,IAAI,GAAGD,QAAQ,GAAG,aAAazD,KAAK,CAACoD,aAAa,CAACpD,KAAK,CAAC2D,QAAQ,EAAE,IAAI,EAAEF,QAAQ,EAAE,aAAazD,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEhC,QAAQ,CAAC,CAAC,GAAGA,QAAQ;EAC3J,IAAIwC,OAAO,GAAG,aAAa5D,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAEvE,QAAQ,CAAC,CAAC,CAAC,EAAE2E,QAAQ,EAAE;IAC5E1C,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAE6B,YAAY;IACvB5B,KAAK,EAAEwB;EACT,CAAC,CAAC,EAAEe,IAAI,EAAEP,eAAe,CAAC,CAAC,CAAC;EAC5B,OAAOG,UAAU,GAAG,aAAatD,KAAK,CAACoD,aAAa,CAAChD,IAAI,EAAE,IAAI,EAAEwD,OAAO,CAAC,GAAGA,OAAO;AACrF,CAAC;AACD,IAAIC,GAAG,GAAG,aAAa7D,KAAK,CAAC8D,UAAU,CAAClD,WAAW,CAAC;AACpD,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCsB,GAAG,CAACE,WAAW,GAAG,KAAK;AACzB;AACAF,GAAG,CAACvD,YAAY,GAAGA,YAAY;AAC/B,eAAeuD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}