{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _noop = _interopRequireDefault(require(\"../noop.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction LinearClosed(context) {\n  this._context = context;\n}\nLinearClosed.prototype = {\n  areaStart: _noop.default,\n  areaEnd: _noop.default,\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._point) this._context.closePath();\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);else this._point = 1, this._context.moveTo(x, y);\n  }\n};\nfunction _default(context) {\n  return new LinearClosed(context);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_noop", "_interopRequireDefault", "require", "obj", "__esModule", "LinearClosed", "context", "_context", "prototype", "areaStart", "areaEnd", "lineStart", "_point", "lineEnd", "closePath", "point", "x", "y", "lineTo", "moveTo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/curve/linearClosed.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _noop = _interopRequireDefault(require(\"../noop.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: _noop.default,\n  areaEnd: _noop.default,\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._point) this._context.closePath();\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nfunction _default(context) {\n  return new LinearClosed(context);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,KAAK,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEzD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASE,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,YAAY,CAACG,SAAS,GAAG;EACvBC,SAAS,EAAET,KAAK,CAACF,OAAO;EACxBY,OAAO,EAAEV,KAAK,CAACF,OAAO;EACtBa,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,IAAI,CAACD,MAAM,EAAE,IAAI,CAACL,QAAQ,CAACO,SAAS,CAAC,CAAC;EAC5C,CAAC;EACDC,KAAK,EAAE,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IACrBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,IAAI,IAAI,CAACL,MAAM,EAAE,IAAI,CAACL,QAAQ,CAACW,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC,KAAK,IAAI,CAACL,MAAM,GAAG,CAAC,EAAE,IAAI,CAACL,QAAQ,CAACY,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;EAC9F;AACF,CAAC;AAED,SAASlB,QAAQA,CAACO,OAAO,EAAE;EACzB,OAAO,IAAID,YAAY,CAACC,OAAO,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}