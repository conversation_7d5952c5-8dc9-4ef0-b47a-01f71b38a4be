{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport AnchorContext from './context';\nvar AnchorLink = function AnchorLink(props) {\n  var _props$href = props.href,\n    href = _props$href === void 0 ? '#' : _props$href,\n    title = props.title,\n    customizePrefixCls = props.prefixCls,\n    children = props.children,\n    className = props.className,\n    target = props.target;\n  var context = React.useContext(AnchorContext);\n  var _ref = context || {},\n    registerLink = _ref.registerLink,\n    unregisterLink = _ref.unregisterLink,\n    scrollTo = _ref.scrollTo,\n    onClick = _ref.onClick,\n    activeLink = _ref.activeLink;\n  React.useEffect(function () {\n    registerLink === null || registerLink === void 0 ? void 0 : registerLink(href);\n    return function () {\n      unregisterLink === null || unregisterLink === void 0 ? void 0 : unregisterLink(href);\n    };\n  }, [href, registerLink, unregisterLink]);\n  var handleClick = function handleClick(e) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n      title: title,\n      href: href\n    });\n    scrollTo === null || scrollTo === void 0 ? void 0 : scrollTo(href);\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref2) {\n    var getPrefixCls = _ref2.getPrefixCls;\n    var prefixCls = getPrefixCls('anchor', customizePrefixCls);\n    var active = activeLink === href;\n    var wrapperClassName = classNames(\"\".concat(prefixCls, \"-link\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-link-active\"), active));\n    var titleClassName = classNames(\"\".concat(prefixCls, \"-link-title\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-title-active\"), active));\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: wrapperClassName\n    }, /*#__PURE__*/React.createElement(\"a\", {\n      className: titleClassName,\n      href: href,\n      title: typeof title === 'string' ? title : '',\n      target: target,\n      onClick: handleClick\n    }, title), children);\n  });\n};\nexport default AnchorLink;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "ConfigConsumer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AnchorLink", "props", "_props$href", "href", "title", "customizePrefixCls", "prefixCls", "children", "className", "target", "context", "useContext", "_ref", "registerLink", "unregisterLink", "scrollTo", "onClick", "activeLink", "useEffect", "handleClick", "e", "createElement", "_ref2", "getPrefixCls", "active", "wrapperClassName", "concat", "titleClassName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/anchor/AnchorLink.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nimport AnchorContext from './context';\nvar AnchorLink = function AnchorLink(props) {\n  var _props$href = props.href,\n    href = _props$href === void 0 ? '#' : _props$href,\n    title = props.title,\n    customizePrefixCls = props.prefixCls,\n    children = props.children,\n    className = props.className,\n    target = props.target;\n  var context = React.useContext(AnchorContext);\n  var _ref = context || {},\n    registerLink = _ref.registerLink,\n    unregisterLink = _ref.unregisterLink,\n    scrollTo = _ref.scrollTo,\n    onClick = _ref.onClick,\n    activeLink = _ref.activeLink;\n  React.useEffect(function () {\n    registerLink === null || registerLink === void 0 ? void 0 : registerLink(href);\n    return function () {\n      unregisterLink === null || unregisterLink === void 0 ? void 0 : unregisterLink(href);\n    };\n  }, [href, registerLink, unregisterLink]);\n  var handleClick = function handleClick(e) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n      title: title,\n      href: href\n    });\n    scrollTo === null || scrollTo === void 0 ? void 0 : scrollTo(href);\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref2) {\n    var getPrefixCls = _ref2.getPrefixCls;\n    var prefixCls = getPrefixCls('anchor', customizePrefixCls);\n    var active = activeLink === href;\n    var wrapperClassName = classNames(\"\".concat(prefixCls, \"-link\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-link-active\"), active));\n    var titleClassName = classNames(\"\".concat(prefixCls, \"-link-title\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-title-active\"), active));\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: wrapperClassName\n    }, /*#__PURE__*/React.createElement(\"a\", {\n      className: titleClassName,\n      href: href,\n      title: typeof title === 'string' ? title : '',\n      target: target,\n      onClick: handleClick\n    }, title), children);\n  });\n};\nexport default AnchorLink;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,aAAa,MAAM,WAAW;AACrC,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,WAAW,GAAGD,KAAK,CAACE,IAAI;IAC1BA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,WAAW;IACjDE,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,kBAAkB,GAAGJ,KAAK,CAACK,SAAS;IACpCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,MAAM,GAAGR,KAAK,CAACQ,MAAM;EACvB,IAAIC,OAAO,GAAGb,KAAK,CAACc,UAAU,CAACZ,aAAa,CAAC;EAC7C,IAAIa,IAAI,GAAGF,OAAO,IAAI,CAAC,CAAC;IACtBG,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACtBC,UAAU,GAAGL,IAAI,CAACK,UAAU;EAC9BpB,KAAK,CAACqB,SAAS,CAAC,YAAY;IAC1BL,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACV,IAAI,CAAC;IAC9E,OAAO,YAAY;MACjBW,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACX,IAAI,CAAC;IACtF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,EAAEU,YAAY,EAAEC,cAAc,CAAC,CAAC;EACxC,IAAIK,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;IACxCJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,CAAC,EAAE;MAC3DhB,KAAK,EAAEA,KAAK;MACZD,IAAI,EAAEA;IACR,CAAC,CAAC;IACFY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACZ,IAAI,CAAC;EACpE,CAAC;EACD,OAAO,aAAaN,KAAK,CAACwB,aAAa,CAACvB,cAAc,EAAE,IAAI,EAAE,UAAUwB,KAAK,EAAE;IAC7E,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACrC,IAAIjB,SAAS,GAAGiB,YAAY,CAAC,QAAQ,EAAElB,kBAAkB,CAAC;IAC1D,IAAImB,MAAM,GAAGP,UAAU,KAAKd,IAAI;IAChC,IAAIsB,gBAAgB,GAAG7B,UAAU,CAAC,EAAE,CAAC8B,MAAM,CAACpB,SAAS,EAAE,OAAO,CAAC,EAAEE,SAAS,EAAEb,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAACpB,SAAS,EAAE,cAAc,CAAC,EAAEkB,MAAM,CAAC,CAAC;IAC9I,IAAIG,cAAc,GAAG/B,UAAU,CAAC,EAAE,CAAC8B,MAAM,CAACpB,SAAS,EAAE,aAAa,CAAC,EAAEX,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAACpB,SAAS,EAAE,oBAAoB,CAAC,EAAEkB,MAAM,CAAC,CAAC;IAC7I,OAAO,aAAa3B,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;MAC7Cb,SAAS,EAAEiB;IACb,CAAC,EAAE,aAAa5B,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;MACvCb,SAAS,EAAEmB,cAAc;MACzBxB,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;MAC7CK,MAAM,EAAEA,MAAM;MACdO,OAAO,EAAEG;IACX,CAAC,EAAEf,KAAK,CAAC,EAAEG,QAAQ,CAAC;EACtB,CAAC,CAAC;AACJ,CAAC;AACD,eAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}