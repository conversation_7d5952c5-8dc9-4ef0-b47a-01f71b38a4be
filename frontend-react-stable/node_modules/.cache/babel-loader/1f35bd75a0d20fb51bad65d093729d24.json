{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ticks;\nexports.tickIncrement = tickIncrement;\nexports.tickStep = tickStep;\nvar e10 = Math.sqrt(50),\n  e5 = Math.sqrt(10),\n  e2 = Math.sqrt(2);\nfunction ticks(start, stop, count) {\n  var reverse,\n    i = -1,\n    n,\n    ticks,\n    step;\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n  if (step > 0) {\n    let r0 = Math.round(start / step),\n      r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step),\n      r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n  if (reverse) ticks.reverse();\n  return ticks;\n}\nfunction tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n    power = Math.floor(Math.log(step) / Math.LN10),\n    error = step / Math.pow(10, power);\n  return power >= 0 ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power) : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\nfunction tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n    step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n    error = step0 / step1;\n  if (error >= e10) step1 *= 10;else if (error >= e5) step1 *= 5;else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ticks", "tickIncrement", "tickStep", "e10", "Math", "sqrt", "e5", "e2", "start", "stop", "count", "reverse", "i", "n", "step", "isFinite", "r0", "round", "r1", "Array", "max", "power", "floor", "log", "LN10", "error", "pow", "step0", "abs", "step1"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/ticks.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ticks;\nexports.tickIncrement = tickIncrement;\nexports.tickStep = tickStep;\nvar e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction ticks(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    let r0 = Math.round(start / step),\n        r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step),\n        r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n  return ticks;\n}\n\nfunction tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0 ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power) : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nfunction tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;else if (error >= e5) step1 *= 5;else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AACvBH,OAAO,CAACI,aAAa,GAAGA,aAAa;AACrCJ,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3B,IAAIC,GAAG,GAAGC,IAAI,CAACC,IAAI,CAAC,EAAE,CAAC;EACnBC,EAAE,GAAGF,IAAI,CAACC,IAAI,CAAC,EAAE,CAAC;EAClBE,EAAE,GAAGH,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;AAErB,SAASL,KAAKA,CAACQ,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAIC,OAAO;IACPC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC;IACDb,KAAK;IACLc,IAAI;EACRL,IAAI,GAAG,CAACA,IAAI,EAAED,KAAK,GAAG,CAACA,KAAK,EAAEE,KAAK,GAAG,CAACA,KAAK;EAC5C,IAAIF,KAAK,KAAKC,IAAI,IAAIC,KAAK,GAAG,CAAC,EAAE,OAAO,CAACF,KAAK,CAAC;EAC/C,IAAIG,OAAO,GAAGF,IAAI,GAAGD,KAAK,EAAEK,CAAC,GAAGL,KAAK,EAAEA,KAAK,GAAGC,IAAI,EAAEA,IAAI,GAAGI,CAAC;EAC7D,IAAI,CAACC,IAAI,GAAGb,aAAa,CAACO,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,MAAM,CAAC,IAAI,CAACK,QAAQ,CAACD,IAAI,CAAC,EAAE,OAAO,EAAE;EAElF,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZ,IAAIE,EAAE,GAAGZ,IAAI,CAACa,KAAK,CAACT,KAAK,GAAGM,IAAI,CAAC;MAC7BI,EAAE,GAAGd,IAAI,CAACa,KAAK,CAACR,IAAI,GAAGK,IAAI,CAAC;IAChC,IAAIE,EAAE,GAAGF,IAAI,GAAGN,KAAK,EAAE,EAAEQ,EAAE;IAC3B,IAAIE,EAAE,GAAGJ,IAAI,GAAGL,IAAI,EAAE,EAAES,EAAE;IAC1BlB,KAAK,GAAG,IAAImB,KAAK,CAACN,CAAC,GAAGK,EAAE,GAAGF,EAAE,GAAG,CAAC,CAAC;IAElC,OAAO,EAAEJ,CAAC,GAAGC,CAAC,EAAEb,KAAK,CAACY,CAAC,CAAC,GAAG,CAACI,EAAE,GAAGJ,CAAC,IAAIE,IAAI;EAC5C,CAAC,MAAM;IACLA,IAAI,GAAG,CAACA,IAAI;IACZ,IAAIE,EAAE,GAAGZ,IAAI,CAACa,KAAK,CAACT,KAAK,GAAGM,IAAI,CAAC;MAC7BI,EAAE,GAAGd,IAAI,CAACa,KAAK,CAACR,IAAI,GAAGK,IAAI,CAAC;IAChC,IAAIE,EAAE,GAAGF,IAAI,GAAGN,KAAK,EAAE,EAAEQ,EAAE;IAC3B,IAAIE,EAAE,GAAGJ,IAAI,GAAGL,IAAI,EAAE,EAAES,EAAE;IAC1BlB,KAAK,GAAG,IAAImB,KAAK,CAACN,CAAC,GAAGK,EAAE,GAAGF,EAAE,GAAG,CAAC,CAAC;IAElC,OAAO,EAAEJ,CAAC,GAAGC,CAAC,EAAEb,KAAK,CAACY,CAAC,CAAC,GAAG,CAACI,EAAE,GAAGJ,CAAC,IAAIE,IAAI;EAC5C;EAEA,IAAIH,OAAO,EAAEX,KAAK,CAACW,OAAO,CAAC,CAAC;EAC5B,OAAOX,KAAK;AACd;AAEA,SAASC,aAAaA,CAACO,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACzC,IAAII,IAAI,GAAG,CAACL,IAAI,GAAGD,KAAK,IAAIJ,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEV,KAAK,CAAC;IAC1CW,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAAClB,IAAI,CAACmB,GAAG,CAACT,IAAI,CAAC,GAAGV,IAAI,CAACoB,IAAI,CAAC;IAC9CC,KAAK,GAAGX,IAAI,GAAGV,IAAI,CAACsB,GAAG,CAAC,EAAE,EAAEL,KAAK,CAAC;EACtC,OAAOA,KAAK,IAAI,CAAC,GAAG,CAACI,KAAK,IAAItB,GAAG,GAAG,EAAE,GAAGsB,KAAK,IAAInB,EAAE,GAAG,CAAC,GAAGmB,KAAK,IAAIlB,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIH,IAAI,CAACsB,GAAG,CAAC,EAAE,EAAEL,KAAK,CAAC,GAAG,CAACjB,IAAI,CAACsB,GAAG,CAAC,EAAE,EAAE,CAACL,KAAK,CAAC,IAAII,KAAK,IAAItB,GAAG,GAAG,EAAE,GAAGsB,KAAK,IAAInB,EAAE,GAAG,CAAC,GAAGmB,KAAK,IAAIlB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7L;AAEA,SAASL,QAAQA,CAACM,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACpC,IAAIiB,KAAK,GAAGvB,IAAI,CAACwB,GAAG,CAACnB,IAAI,GAAGD,KAAK,CAAC,GAAGJ,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEV,KAAK,CAAC;IACnDmB,KAAK,GAAGzB,IAAI,CAACsB,GAAG,CAAC,EAAE,EAAEtB,IAAI,CAACkB,KAAK,CAAClB,IAAI,CAACmB,GAAG,CAACI,KAAK,CAAC,GAAGvB,IAAI,CAACoB,IAAI,CAAC,CAAC;IAC7DC,KAAK,GAAGE,KAAK,GAAGE,KAAK;EACzB,IAAIJ,KAAK,IAAItB,GAAG,EAAE0B,KAAK,IAAI,EAAE,CAAC,KAAK,IAAIJ,KAAK,IAAInB,EAAE,EAAEuB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAIJ,KAAK,IAAIlB,EAAE,EAAEsB,KAAK,IAAI,CAAC;EAC/F,OAAOpB,IAAI,GAAGD,KAAK,GAAG,CAACqB,KAAK,GAAGA,KAAK;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}