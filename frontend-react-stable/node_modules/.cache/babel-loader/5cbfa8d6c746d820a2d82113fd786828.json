{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport SingleNumber from './SingleNumber';\nvar ScrollNumber = function ScrollNumber(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    count = _a.count,\n    className = _a.className,\n    motionClassName = _a.motionClassName,\n    style = _a.style,\n    title = _a.title,\n    show = _a.show,\n    _a$component = _a.component,\n    component = _a$component === void 0 ? 'sup' : _a$component,\n    children = _a.children,\n    restProps = __rest(_a, [\"prefixCls\", \"count\", \"className\", \"motionClassName\", \"style\", \"title\", \"show\", \"component\", \"children\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('scroll-number', customizePrefixCls);\n  // ============================ Render ============================\n  var newProps = _extends(_extends({}, restProps), {\n    'data-show': show,\n    style: style,\n    className: classNames(prefixCls, className, motionClassName),\n    title: title\n  });\n  // Only integer need motion\n  var numberNodes = count;\n  if (count && Number(count) % 1 === 0) {\n    var numberList = String(count).split('');\n    numberNodes = numberList.map(function (num, i) {\n      return /*#__PURE__*/React.createElement(SingleNumber, {\n        prefixCls: prefixCls,\n        count: Number(count),\n        value: num,\n        // eslint-disable-next-line react/no-array-index-key\n        key: numberList.length - i\n      });\n    });\n  }\n  // allow specify the border\n  // mock border-color by box-shadow for compatible with old usage:\n  // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />\n  if (style && style.borderColor) {\n    newProps.style = _extends(_extends({}, style), {\n      boxShadow: \"0 0 0 1px \".concat(style.borderColor, \" inset\")\n    });\n  }\n  if (children) {\n    return cloneElement(children, function (oriProps) {\n      return {\n        className: classNames(\"\".concat(prefixCls, \"-custom-component\"), oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)\n      };\n    });\n  }\n  return /*#__PURE__*/React.createElement(component, newProps, numberNodes);\n};\nexport default ScrollNumber;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "ConfigContext", "cloneElement", "SingleNumber", "ScrollNumber", "_a", "customizePrefixCls", "prefixCls", "count", "className", "motionClassName", "style", "title", "show", "_a$component", "component", "children", "restProps", "_React$useContext", "useContext", "getPrefixCls", "newProps", "numberNodes", "Number", "numberList", "String", "split", "map", "num", "createElement", "value", "key", "borderColor", "boxShadow", "concat", "oriProps"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/badge/ScrollNumber.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport SingleNumber from './SingleNumber';\nvar ScrollNumber = function ScrollNumber(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    count = _a.count,\n    className = _a.className,\n    motionClassName = _a.motionClassName,\n    style = _a.style,\n    title = _a.title,\n    show = _a.show,\n    _a$component = _a.component,\n    component = _a$component === void 0 ? 'sup' : _a$component,\n    children = _a.children,\n    restProps = __rest(_a, [\"prefixCls\", \"count\", \"className\", \"motionClassName\", \"style\", \"title\", \"show\", \"component\", \"children\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('scroll-number', customizePrefixCls);\n  // ============================ Render ============================\n  var newProps = _extends(_extends({}, restProps), {\n    'data-show': show,\n    style: style,\n    className: classNames(prefixCls, className, motionClassName),\n    title: title\n  });\n  // Only integer need motion\n  var numberNodes = count;\n  if (count && Number(count) % 1 === 0) {\n    var numberList = String(count).split('');\n    numberNodes = numberList.map(function (num, i) {\n      return /*#__PURE__*/React.createElement(SingleNumber, {\n        prefixCls: prefixCls,\n        count: Number(count),\n        value: num,\n        // eslint-disable-next-line react/no-array-index-key\n        key: numberList.length - i\n      });\n    });\n  }\n  // allow specify the border\n  // mock border-color by box-shadow for compatible with old usage:\n  // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />\n  if (style && style.borderColor) {\n    newProps.style = _extends(_extends({}, style), {\n      boxShadow: \"0 0 0 1px \".concat(style.borderColor, \" inset\")\n    });\n  }\n  if (children) {\n    return cloneElement(children, function (oriProps) {\n      return {\n        className: classNames(\"\".concat(prefixCls, \"-custom-component\"), oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)\n      };\n    });\n  }\n  return /*#__PURE__*/React.createElement(component, newProps, numberNodes);\n};\nexport default ScrollNumber;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,EAAE,EAAE;EAC3C,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCC,KAAK,GAAGH,EAAE,CAACG,KAAK;IAChBC,SAAS,GAAGJ,EAAE,CAACI,SAAS;IACxBC,eAAe,GAAGL,EAAE,CAACK,eAAe;IACpCC,KAAK,GAAGN,EAAE,CAACM,KAAK;IAChBC,KAAK,GAAGP,EAAE,CAACO,KAAK;IAChBC,IAAI,GAAGR,EAAE,CAACQ,IAAI;IACdC,YAAY,GAAGT,EAAE,CAACU,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IAC1DE,QAAQ,GAAGX,EAAE,CAACW,QAAQ;IACtBC,SAAS,GAAGhC,MAAM,CAACoB,EAAE,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EACnI,IAAIa,iBAAiB,GAAGlB,KAAK,CAACmB,UAAU,CAAClB,aAAa,CAAC;IACrDmB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIb,SAAS,GAAGa,YAAY,CAAC,eAAe,EAAEd,kBAAkB,CAAC;EACjE;EACA,IAAIe,QAAQ,GAAGrC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiC,SAAS,CAAC,EAAE;IAC/C,WAAW,EAAEJ,IAAI;IACjBF,KAAK,EAAEA,KAAK;IACZF,SAAS,EAAEV,UAAU,CAACQ,SAAS,EAAEE,SAAS,EAAEC,eAAe,CAAC;IAC5DE,KAAK,EAAEA;EACT,CAAC,CAAC;EACF;EACA,IAAIU,WAAW,GAAGd,KAAK;EACvB,IAAIA,KAAK,IAAIe,MAAM,CAACf,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACpC,IAAIgB,UAAU,GAAGC,MAAM,CAACjB,KAAK,CAAC,CAACkB,KAAK,CAAC,EAAE,CAAC;IACxCJ,WAAW,GAAGE,UAAU,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAEhC,CAAC,EAAE;MAC7C,OAAO,aAAaI,KAAK,CAAC6B,aAAa,CAAC1B,YAAY,EAAE;QACpDI,SAAS,EAAEA,SAAS;QACpBC,KAAK,EAAEe,MAAM,CAACf,KAAK,CAAC;QACpBsB,KAAK,EAAEF,GAAG;QACV;QACAG,GAAG,EAAEP,UAAU,CAAC3B,MAAM,GAAGD;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA;EACA;EACA;EACA,IAAIe,KAAK,IAAIA,KAAK,CAACqB,WAAW,EAAE;IAC9BX,QAAQ,CAACV,KAAK,GAAG3B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAAC,EAAE;MAC7CsB,SAAS,EAAE,YAAY,CAACC,MAAM,CAACvB,KAAK,CAACqB,WAAW,EAAE,QAAQ;IAC5D,CAAC,CAAC;EACJ;EACA,IAAIhB,QAAQ,EAAE;IACZ,OAAOd,YAAY,CAACc,QAAQ,EAAE,UAAUmB,QAAQ,EAAE;MAChD,OAAO;QACL1B,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACmC,MAAM,CAAC3B,SAAS,EAAE,mBAAmB,CAAC,EAAE4B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC1B,SAAS,EAAEC,eAAe;MAC1J,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAO,aAAaV,KAAK,CAAC6B,aAAa,CAACd,SAAS,EAAEM,QAAQ,EAAEC,WAAW,CAAC;AAC3E,CAAC;AACD,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}