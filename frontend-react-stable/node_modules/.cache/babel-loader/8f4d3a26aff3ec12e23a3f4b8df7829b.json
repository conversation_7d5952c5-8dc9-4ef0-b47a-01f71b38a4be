{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\nmodule.exports = hashClear;", "map": {"version": 3, "names": ["nativeCreate", "require", "hashClear", "__data__", "size", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_hashClear.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\nmodule.exports = hashClear;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,CAACC,QAAQ,GAAGH,YAAY,GAAGA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACtD,IAAI,CAACI,IAAI,GAAG,CAAC;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}