{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pairs;\nexports.pair = pair;\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\nfunction pair(a, b) {\n  return [a, b];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "pairs", "pair", "values", "pairof", "previous", "first", "push", "a", "b"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/pairs.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pairs;\nexports.pair = pair;\n\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n\n  return pairs;\n}\n\nfunction pair(a, b) {\n  return [a, b];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AACvBH,OAAO,CAACI,IAAI,GAAGA,IAAI;AAEnB,SAASD,KAAKA,CAACE,MAAM,EAAEC,MAAM,GAAGF,IAAI,EAAE;EACpC,MAAMD,KAAK,GAAG,EAAE;EAChB,IAAII,QAAQ;EACZ,IAAIC,KAAK,GAAG,KAAK;EAEjB,KAAK,MAAMP,KAAK,IAAII,MAAM,EAAE;IAC1B,IAAIG,KAAK,EAAEL,KAAK,CAACM,IAAI,CAACH,MAAM,CAACC,QAAQ,EAAEN,KAAK,CAAC,CAAC;IAC9CM,QAAQ,GAAGN,KAAK;IAChBO,KAAK,GAAG,IAAI;EACd;EAEA,OAAOL,KAAK;AACd;AAEA,SAASC,IAAIA,CAACM,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script"}