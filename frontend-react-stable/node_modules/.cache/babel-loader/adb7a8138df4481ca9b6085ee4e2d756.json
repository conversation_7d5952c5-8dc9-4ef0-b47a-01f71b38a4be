{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport React, { Children, forwardRef, useContext } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { Col } from '../grid';\nimport { cloneElement } from '../_util/reactNode';\nimport { ListContext } from './index';\nexport var Meta = function Meta(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    avatar = _a.avatar,\n    title = _a.title,\n    description = _a.description,\n    others = __rest(_a, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var classString = classNames(\"\".concat(prefixCls, \"-item-meta\"), className);\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-content\")\n  }, title && /*#__PURE__*/React.createElement(\"h4\", {\n    className: \"\".concat(prefixCls, \"-item-meta-title\")\n  }, title), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-description\")\n  }, description));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n    className: classString\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-avatar\")\n  }, avatar), (title || description) && content);\n};\nvar InternalItem = function InternalItem(_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    actions = _a.actions,\n    extra = _a.extra,\n    className = _a.className,\n    colStyle = _a.colStyle,\n    others = __rest(_a, [\"prefixCls\", \"children\", \"actions\", \"extra\", \"className\", \"colStyle\"]);\n  var _useContext2 = useContext(ListContext),\n    grid = _useContext2.grid,\n    itemLayout = _useContext2.itemLayout;\n  var _useContext3 = useContext(ConfigContext),\n    getPrefixCls = _useContext3.getPrefixCls;\n  var isItemContainsTextNodeAndNotSingular = function isItemContainsTextNodeAndNotSingular() {\n    var result;\n    Children.forEach(children, function (element) {\n      if (typeof element === 'string') {\n        result = true;\n      }\n    });\n    return result && Children.count(children) > 1;\n  };\n  var isFlexMode = function isFlexMode() {\n    if (itemLayout === 'vertical') {\n      return !!extra;\n    }\n    return !isItemContainsTextNodeAndNotSingular();\n  };\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var actionsContent = actions && actions.length > 0 && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-item-action\"),\n    key: \"actions\"\n  }, actions.map(function (action, i) {\n    return (/*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: \"\".concat(prefixCls, \"-item-action-\").concat(i)\n      }, action, i !== actions.length - 1 && /*#__PURE__*/React.createElement(\"em\", {\n        className: \"\".concat(prefixCls, \"-item-action-split\")\n      }))\n    );\n  }));\n  var Element = grid ? 'div' : 'li';\n  var itemChildren = /*#__PURE__*/React.createElement(Element, _extends({}, others, !grid ? {\n    ref: ref\n  } : {}, {\n    className: classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-item-no-flex\"), !isFlexMode()), className)\n  }), itemLayout === 'vertical' && extra ? [/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-main\"),\n    key: \"content\"\n  }, children, actionsContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-extra\"),\n    key: \"extra\"\n  }, extra)] : [children, actionsContent, cloneElement(extra, {\n    key: 'extra'\n  })]);\n  return grid ? /*#__PURE__*/React.createElement(Col, {\n    ref: ref,\n    flex: 1,\n    style: colStyle\n  }, itemChildren) : itemChildren;\n};\nvar Item = /*#__PURE__*/forwardRef(InternalItem);\nItem.Meta = Meta;\nexport default Item;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "Children", "forwardRef", "useContext", "ConfigContext", "Col", "cloneElement", "ListContext", "Meta", "_a", "customizePrefixCls", "prefixCls", "className", "avatar", "title", "description", "others", "_useContext", "getPrefixCls", "classString", "concat", "content", "createElement", "InternalItem", "ref", "children", "actions", "extra", "colStyle", "_useContext2", "grid", "itemLayout", "_useContext3", "isItemContainsTextNodeAndNotSingular", "result", "for<PERSON>ach", "element", "count", "isFlexMode", "actionsContent", "key", "map", "action", "Element", "itemChildren", "flex", "style", "<PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/list/Item.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport React, { Children, forwardRef, useContext } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { Col } from '../grid';\nimport { cloneElement } from '../_util/reactNode';\nimport { ListContext } from './index';\nexport var Meta = function Meta(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    avatar = _a.avatar,\n    title = _a.title,\n    description = _a.description,\n    others = __rest(_a, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var classString = classNames(\"\".concat(prefixCls, \"-item-meta\"), className);\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-content\")\n  }, title && /*#__PURE__*/React.createElement(\"h4\", {\n    className: \"\".concat(prefixCls, \"-item-meta-title\")\n  }, title), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-description\")\n  }, description));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n    className: classString\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-avatar\")\n  }, avatar), (title || description) && content);\n};\nvar InternalItem = function InternalItem(_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    actions = _a.actions,\n    extra = _a.extra,\n    className = _a.className,\n    colStyle = _a.colStyle,\n    others = __rest(_a, [\"prefixCls\", \"children\", \"actions\", \"extra\", \"className\", \"colStyle\"]);\n  var _useContext2 = useContext(ListContext),\n    grid = _useContext2.grid,\n    itemLayout = _useContext2.itemLayout;\n  var _useContext3 = useContext(ConfigContext),\n    getPrefixCls = _useContext3.getPrefixCls;\n  var isItemContainsTextNodeAndNotSingular = function isItemContainsTextNodeAndNotSingular() {\n    var result;\n    Children.forEach(children, function (element) {\n      if (typeof element === 'string') {\n        result = true;\n      }\n    });\n    return result && Children.count(children) > 1;\n  };\n  var isFlexMode = function isFlexMode() {\n    if (itemLayout === 'vertical') {\n      return !!extra;\n    }\n    return !isItemContainsTextNodeAndNotSingular();\n  };\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var actionsContent = actions && actions.length > 0 && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-item-action\"),\n    key: \"actions\"\n  }, actions.map(function (action, i) {\n    return (\n      /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: \"\".concat(prefixCls, \"-item-action-\").concat(i)\n      }, action, i !== actions.length - 1 && /*#__PURE__*/React.createElement(\"em\", {\n        className: \"\".concat(prefixCls, \"-item-action-split\")\n      }))\n    );\n  }));\n  var Element = grid ? 'div' : 'li';\n  var itemChildren = /*#__PURE__*/React.createElement(Element, _extends({}, others, !grid ? {\n    ref: ref\n  } : {}, {\n    className: classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-item-no-flex\"), !isFlexMode()), className)\n  }), itemLayout === 'vertical' && extra ? [/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-main\"),\n    key: \"content\"\n  }, children, actionsContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-extra\"),\n    key: \"extra\"\n  }, extra)] : [children, actionsContent, cloneElement(extra, {\n    key: 'extra'\n  })]);\n  return grid ? /*#__PURE__*/React.createElement(Col, {\n    ref: ref,\n    flex: 1,\n    style: colStyle\n  }, itemChildren) : itemChildren;\n};\nvar Item = /*#__PURE__*/forwardRef(InternalItem);\nItem.Meta = Meta;\nexport default Item;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,GAAG,QAAQ,SAAS;AAC7B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,WAAW,QAAQ,SAAS;AACrC,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;EAClC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCC,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBC,MAAM,GAAGJ,EAAE,CAACI,MAAM;IAClBC,KAAK,GAAGL,EAAE,CAACK,KAAK;IAChBC,WAAW,GAAGN,EAAE,CAACM,WAAW;IAC5BC,MAAM,GAAG/B,MAAM,CAACwB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;EACnF,IAAIQ,WAAW,GAAGd,UAAU,CAACC,aAAa,CAAC;IACzCc,YAAY,GAAGD,WAAW,CAACC,YAAY;EACzC,IAAIP,SAAS,GAAGO,YAAY,CAAC,MAAM,EAAER,kBAAkB,CAAC;EACxD,IAAIS,WAAW,GAAGpB,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACT,SAAS,EAAE,YAAY,CAAC,EAAEC,SAAS,CAAC;EAC3E,IAAIS,OAAO,GAAG,aAAarB,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IACpDV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,oBAAoB;EACtD,CAAC,EAAEG,KAAK,IAAI,aAAad,KAAK,CAACsB,aAAa,CAAC,IAAI,EAAE;IACjDV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,kBAAkB;EACpD,CAAC,EAAEG,KAAK,CAAC,EAAEC,WAAW,IAAI,aAAaf,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IAChEV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,wBAAwB;EAC1D,CAAC,EAAEI,WAAW,CAAC,CAAC;EAChB,OAAO,aAAaf,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,MAAM,EAAE;IAClEJ,SAAS,EAAEO;EACb,CAAC,CAAC,EAAEN,MAAM,IAAI,aAAab,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IACpDV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAEE,MAAM,CAAC,EAAE,CAACC,KAAK,IAAIC,WAAW,KAAKM,OAAO,CAAC;AAChD,CAAC;AACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACd,EAAE,EAAEe,GAAG,EAAE;EAChD,IAAId,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCc,QAAQ,GAAGhB,EAAE,CAACgB,QAAQ;IACtBC,OAAO,GAAGjB,EAAE,CAACiB,OAAO;IACpBC,KAAK,GAAGlB,EAAE,CAACkB,KAAK;IAChBf,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBgB,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ;IACtBZ,MAAM,GAAG/B,MAAM,CAACwB,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EAC7F,IAAIoB,YAAY,GAAG1B,UAAU,CAACI,WAAW,CAAC;IACxCuB,IAAI,GAAGD,YAAY,CAACC,IAAI;IACxBC,UAAU,GAAGF,YAAY,CAACE,UAAU;EACtC,IAAIC,YAAY,GAAG7B,UAAU,CAACC,aAAa,CAAC;IAC1Cc,YAAY,GAAGc,YAAY,CAACd,YAAY;EAC1C,IAAIe,oCAAoC,GAAG,SAASA,oCAAoCA,CAAA,EAAG;IACzF,IAAIC,MAAM;IACVjC,QAAQ,CAACkC,OAAO,CAACV,QAAQ,EAAE,UAAUW,OAAO,EAAE;MAC5C,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BF,MAAM,GAAG,IAAI;MACf;IACF,CAAC,CAAC;IACF,OAAOA,MAAM,IAAIjC,QAAQ,CAACoC,KAAK,CAACZ,QAAQ,CAAC,GAAG,CAAC;EAC/C,CAAC;EACD,IAAIa,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIP,UAAU,KAAK,UAAU,EAAE;MAC7B,OAAO,CAAC,CAACJ,KAAK;IAChB;IACA,OAAO,CAACM,oCAAoC,CAAC,CAAC;EAChD,CAAC;EACD,IAAItB,SAAS,GAAGO,YAAY,CAAC,MAAM,EAAER,kBAAkB,CAAC;EACxD,IAAI6B,cAAc,GAAGb,OAAO,IAAIA,OAAO,CAAC7B,MAAM,GAAG,CAAC,IAAI,aAAaG,KAAK,CAACsB,aAAa,CAAC,IAAI,EAAE;IAC3FV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,cAAc,CAAC;IAC/C6B,GAAG,EAAE;EACP,CAAC,EAAEd,OAAO,CAACe,GAAG,CAAC,UAAUC,MAAM,EAAE9C,CAAC,EAAE;IAClC,QACE;MACA;MACAI,KAAK,CAACsB,aAAa,CAAC,IAAI,EAAE;QACxBkB,GAAG,EAAE,EAAE,CAACpB,MAAM,CAACT,SAAS,EAAE,eAAe,CAAC,CAACS,MAAM,CAACxB,CAAC;MACrD,CAAC,EAAE8C,MAAM,EAAE9C,CAAC,KAAK8B,OAAO,CAAC7B,MAAM,GAAG,CAAC,IAAI,aAAaG,KAAK,CAACsB,aAAa,CAAC,IAAI,EAAE;QAC5EV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,oBAAoB;MACtD,CAAC,CAAC;IAAC;EAEP,CAAC,CAAC,CAAC;EACH,IAAIgC,OAAO,GAAGb,IAAI,GAAG,KAAK,GAAG,IAAI;EACjC,IAAIc,YAAY,GAAG,aAAa5C,KAAK,CAACsB,aAAa,CAACqB,OAAO,EAAE3D,QAAQ,CAAC,CAAC,CAAC,EAAEgC,MAAM,EAAE,CAACc,IAAI,GAAG;IACxFN,GAAG,EAAEA;EACP,CAAC,GAAG,CAAC,CAAC,EAAE;IACNZ,SAAS,EAAEb,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACT,SAAS,EAAE,OAAO,CAAC,EAAE5B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqC,MAAM,CAACT,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC2B,UAAU,CAAC,CAAC,CAAC,EAAE1B,SAAS;EAC3I,CAAC,CAAC,EAAEmB,UAAU,KAAK,UAAU,IAAIJ,KAAK,GAAG,CAAC,aAAa3B,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IAChFV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,YAAY,CAAC;IAC7C6B,GAAG,EAAE;EACP,CAAC,EAAEf,QAAQ,EAAEc,cAAc,CAAC,EAAE,aAAavC,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IACpEV,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACT,SAAS,EAAE,aAAa,CAAC;IAC9C6B,GAAG,EAAE;EACP,CAAC,EAAEb,KAAK,CAAC,CAAC,GAAG,CAACF,QAAQ,EAAEc,cAAc,EAAEjC,YAAY,CAACqB,KAAK,EAAE;IAC1Da,GAAG,EAAE;EACP,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOV,IAAI,GAAG,aAAa9B,KAAK,CAACsB,aAAa,CAACjB,GAAG,EAAE;IAClDmB,GAAG,EAAEA,GAAG;IACRqB,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB;EACT,CAAC,EAAEgB,YAAY,CAAC,GAAGA,YAAY;AACjC,CAAC;AACD,IAAIG,IAAI,GAAG,aAAa7C,UAAU,CAACqB,YAAY,CAAC;AAChDwB,IAAI,CAACvC,IAAI,GAAGA,IAAI;AAChB,eAAeuC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}