{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport VerticalAlignMiddleOutlinedSvg from \"@ant-design/icons-svg/es/asn/VerticalAlignMiddleOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar VerticalAlignMiddleOutlined = function VerticalAlignMiddleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: VerticalAlignMiddleOutlinedSvg\n  }));\n};\nVerticalAlignMiddleOutlined.displayName = 'VerticalAlignMiddleOutlined';\nexport default /*#__PURE__*/React.forwardRef(VerticalAlignMiddleOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "VerticalAlignMiddleOutlinedSvg", "AntdIcon", "VerticalAlignMiddleOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/VerticalAlignMiddleOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport VerticalAlignMiddleOutlinedSvg from \"@ant-design/icons-svg/es/asn/VerticalAlignMiddleOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar VerticalAlignMiddleOutlined = function VerticalAlignMiddleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: VerticalAlignMiddleOutlinedSvg\n  }));\n};\nVerticalAlignMiddleOutlined.displayName = 'VerticalAlignMiddleOutlined';\nexport default /*#__PURE__*/React.forwardRef(VerticalAlignMiddleOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,8BAA8B,MAAM,0DAA0D;AACrG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjF,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,2BAA2B,CAACK,WAAW,GAAG,6BAA6B;AACvE,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}