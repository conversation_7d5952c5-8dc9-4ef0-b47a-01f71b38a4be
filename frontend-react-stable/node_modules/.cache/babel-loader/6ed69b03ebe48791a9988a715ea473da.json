{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _cssUnitConverter = require('css-unit-converter');\nvar _cssUnitConverter2 = _interopRequireDefault(_cssUnitConverter);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction convertNodes(left, right, precision) {\n  switch (left.type) {\n    case 'LengthValue':\n    case 'AngleValue':\n    case 'TimeValue':\n    case 'FrequencyValue':\n    case 'ResolutionValue':\n      return convertAbsoluteLength(left, right, precision);\n    default:\n      return {\n        left: left,\n        right: right\n      };\n  }\n}\nfunction convertAbsoluteLength(left, right, precision) {\n  if (right.type === left.type) {\n    right = {\n      type: left.type,\n      value: (0, _cssUnitConverter2.default)(right.value, right.unit, left.unit, precision),\n      unit: left.unit\n    };\n  }\n  return {\n    left: left,\n    right: right\n  };\n}\nexports.default = convertNodes;\nmodule.exports = exports['default'];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_cssUnitConverter", "require", "_cssUnitConverter2", "_interopRequireDefault", "obj", "__esModule", "default", "convertNodes", "left", "right", "precision", "type", "convertAbsoluteLength", "unit", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/dist/lib/convert.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _cssUnitConverter = require('css-unit-converter');\n\nvar _cssUnitConverter2 = _interopRequireDefault(_cssUnitConverter);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction convertNodes(left, right, precision) {\n  switch (left.type) {\n    case 'LengthValue':\n    case 'AngleValue':\n    case 'TimeValue':\n    case 'FrequencyValue':\n    case 'ResolutionValue':\n      return convertAbsoluteLength(left, right, precision);\n    default:\n      return { left: left, right: right };\n  }\n}\n\nfunction convertAbsoluteLength(left, right, precision) {\n  if (right.type === left.type) {\n    right = {\n      type: left.type,\n      value: (0, _cssUnitConverter2.default)(right.value, right.unit, left.unit, precision),\n      unit: left.unit\n    };\n  }\n  return { left: left, right: right };\n}\n\nexports.default = convertNodes;\nmodule.exports = exports['default'];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,iBAAiB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAErD,IAAIC,kBAAkB,GAAGC,sBAAsB,CAACH,iBAAiB,CAAC;AAElE,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5C,QAAQF,IAAI,CAACG,IAAI;IACf,KAAK,aAAa;IAClB,KAAK,YAAY;IACjB,KAAK,WAAW;IAChB,KAAK,gBAAgB;IACrB,KAAK,iBAAiB;MACpB,OAAOC,qBAAqB,CAACJ,IAAI,EAAEC,KAAK,EAAEC,SAAS,CAAC;IACtD;MACE,OAAO;QAAEF,IAAI,EAAEA,IAAI;QAAEC,KAAK,EAAEA;MAAM,CAAC;EACvC;AACF;AAEA,SAASG,qBAAqBA,CAACJ,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACrD,IAAID,KAAK,CAACE,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;IAC5BF,KAAK,GAAG;MACNE,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfZ,KAAK,EAAE,CAAC,CAAC,EAAEG,kBAAkB,CAACI,OAAO,EAAEG,KAAK,CAACV,KAAK,EAAEU,KAAK,CAACI,IAAI,EAAEL,IAAI,CAACK,IAAI,EAAEH,SAAS,CAAC;MACrFG,IAAI,EAAEL,IAAI,CAACK;IACb,CAAC;EACH;EACA,OAAO;IAAEL,IAAI,EAAEA,IAAI;IAAEC,KAAK,EAAEA;EAAM,CAAC;AACrC;AAEAX,OAAO,CAACQ,OAAO,GAAGC,YAAY;AAC9BO,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}