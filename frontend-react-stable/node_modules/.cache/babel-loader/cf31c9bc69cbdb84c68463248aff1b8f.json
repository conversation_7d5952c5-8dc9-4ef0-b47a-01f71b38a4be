{"ast": null, "code": "import { useEffect, useRef } from 'react';\nexport default function useRemovePasswordTimeout(inputRef, triggerOnMount) {\n  var removePasswordTimeoutRef = useRef([]);\n  var removePasswordTimeout = function removePasswordTimeout() {\n    removePasswordTimeoutRef.current.push(window.setTimeout(function () {\n      var _a, _b, _c, _d;\n      if (((_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.input) && ((_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.input.getAttribute('type')) === 'password' && ((_c = inputRef.current) === null || _c === void 0 ? void 0 : _c.input.hasAttribute('value'))) {\n        (_d = inputRef.current) === null || _d === void 0 ? void 0 : _d.input.removeAttribute('value');\n      }\n    }));\n  };\n  useEffect(function () {\n    if (triggerOnMount) {\n      removePasswordTimeout();\n    }\n    return function () {\n      return removePasswordTimeoutRef.current.forEach(function (item) {\n        return window.clearTimeout(item);\n      });\n    };\n  }, []);\n  return removePasswordTimeout;\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useRemovePasswordTimeout", "inputRef", "triggerOnMount", "removePasswordTimeoutRef", "removePasswordTimeout", "current", "push", "window", "setTimeout", "_a", "_b", "_c", "_d", "input", "getAttribute", "hasAttribute", "removeAttribute", "for<PERSON>ach", "item", "clearTimeout"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/hooks/useRemovePasswordTimeout.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport default function useRemovePasswordTimeout(inputRef, triggerOnMount) {\n  var removePasswordTimeoutRef = useRef([]);\n  var removePasswordTimeout = function removePasswordTimeout() {\n    removePasswordTimeoutRef.current.push(window.setTimeout(function () {\n      var _a, _b, _c, _d;\n      if (((_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.input) && ((_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.input.getAttribute('type')) === 'password' && ((_c = inputRef.current) === null || _c === void 0 ? void 0 : _c.input.hasAttribute('value'))) {\n        (_d = inputRef.current) === null || _d === void 0 ? void 0 : _d.input.removeAttribute('value');\n      }\n    }));\n  };\n  useEffect(function () {\n    if (triggerOnMount) {\n      removePasswordTimeout();\n    }\n    return function () {\n      return removePasswordTimeoutRef.current.forEach(function (item) {\n        return window.clearTimeout(item);\n      });\n    };\n  }, []);\n  return removePasswordTimeout;\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,eAAe,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,cAAc,EAAE;EACzE,IAAIC,wBAAwB,GAAGJ,MAAM,CAAC,EAAE,CAAC;EACzC,IAAIK,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3DD,wBAAwB,CAACE,OAAO,CAACC,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,YAAY;MAClE,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAClB,IAAI,CAAC,CAACH,EAAE,GAAGR,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,KAAK,KAAK,CAAC,CAACH,EAAE,GAAGT,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,CAACC,YAAY,CAAC,MAAM,CAAC,MAAM,UAAU,KAAK,CAACH,EAAE,GAAGV,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK,CAACE,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE;QAC3R,CAACH,EAAE,GAAGX,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,KAAK,CAACG,eAAe,CAAC,OAAO,CAAC;MAChG;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EACDlB,SAAS,CAAC,YAAY;IACpB,IAAII,cAAc,EAAE;MAClBE,qBAAqB,CAAC,CAAC;IACzB;IACA,OAAO,YAAY;MACjB,OAAOD,wBAAwB,CAACE,OAAO,CAACY,OAAO,CAAC,UAAUC,IAAI,EAAE;QAC9D,OAAOX,MAAM,CAACY,YAAY,CAACD,IAAI,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOd,qBAAqB;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}