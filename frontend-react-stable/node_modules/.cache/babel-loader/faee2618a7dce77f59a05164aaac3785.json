{"ast": null, "code": "import _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nvar _excluded = [\"viewBox\"],\n  _excluded2 = [\"viewBox\"],\n  _excluded3 = [\"ticks\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Cartesian Axis\n */\nimport React, { Component } from 'react';\nimport classNames from 'classnames';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { isNumber } from '../util/DataUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTicks } from './getTicks';\nexport var CartesianAxis = /*#__PURE__*/function (_Component) {\n  _inherits(CartesianAxis, _Component);\n  var _super = _createSuper(CartesianAxis);\n  function CartesianAxis(props) {\n    var _this;\n    _classCallCheck(this, CartesianAxis);\n    _this = _super.call(this, props);\n    _this.state = {\n      fontSize: '',\n      letterSpacing: ''\n    };\n    return _this;\n  }\n  _createClass(CartesianAxis, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(_ref, nextState) {\n      var viewBox = _ref.viewBox,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      // props.viewBox is sometimes generated every time -\n      // check that specially as object equality is likely to fail\n      var _this$props = this.props,\n        viewBoxOld = _this$props.viewBox,\n        restPropsOld = _objectWithoutProperties(_this$props, _excluded2);\n      return !shallowEqual(viewBox, viewBoxOld) || !shallowEqual(restProps, restPropsOld) || !shallowEqual(nextState, this.state);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var htmlLayer = this.layerReference;\n      if (!htmlLayer) return;\n      var tick = htmlLayer.getElementsByClassName('recharts-cartesian-axis-tick-value')[0];\n      if (tick) {\n        this.setState({\n          fontSize: window.getComputedStyle(tick).fontSize,\n          letterSpacing: window.getComputedStyle(tick).letterSpacing\n        });\n      }\n    }\n\n    /**\n     * Calculate the coordinates of endpoints in ticks\n     * @param  {Object} data The data of a simple tick\n     * @return {Object} (x1, y1): The coordinate of endpoint close to tick text\n     *  (x2, y2): The coordinate of endpoint close to axis\n     */\n  }, {\n    key: \"getTickLineCoord\",\n    value: function getTickLineCoord(data) {\n      var _this$props2 = this.props,\n        x = _this$props2.x,\n        y = _this$props2.y,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        orientation = _this$props2.orientation,\n        tickSize = _this$props2.tickSize,\n        mirror = _this$props2.mirror,\n        tickMargin = _this$props2.tickMargin;\n      var x1, x2, y1, y2, tx, ty;\n      var sign = mirror ? -1 : 1;\n      var finalTickSize = data.tickSize || tickSize;\n      var tickCoord = isNumber(data.tickCoord) ? data.tickCoord : data.coordinate;\n      switch (orientation) {\n        case 'top':\n          x1 = x2 = data.coordinate;\n          y2 = y + +!mirror * height;\n          y1 = y2 - sign * finalTickSize;\n          ty = y1 - sign * tickMargin;\n          tx = tickCoord;\n          break;\n        case 'left':\n          y1 = y2 = data.coordinate;\n          x2 = x + +!mirror * width;\n          x1 = x2 - sign * finalTickSize;\n          tx = x1 - sign * tickMargin;\n          ty = tickCoord;\n          break;\n        case 'right':\n          y1 = y2 = data.coordinate;\n          x2 = x + +mirror * width;\n          x1 = x2 + sign * finalTickSize;\n          tx = x1 + sign * tickMargin;\n          ty = tickCoord;\n          break;\n        default:\n          x1 = x2 = data.coordinate;\n          y2 = y + +mirror * height;\n          y1 = y2 + sign * finalTickSize;\n          ty = y1 + sign * tickMargin;\n          tx = tickCoord;\n          break;\n      }\n      return {\n        line: {\n          x1: x1,\n          y1: y1,\n          x2: x2,\n          y2: y2\n        },\n        tick: {\n          x: tx,\n          y: ty\n        }\n      };\n    }\n  }, {\n    key: \"getTickTextAnchor\",\n    value: function getTickTextAnchor() {\n      var _this$props3 = this.props,\n        orientation = _this$props3.orientation,\n        mirror = _this$props3.mirror;\n      var textAnchor;\n      switch (orientation) {\n        case 'left':\n          textAnchor = mirror ? 'start' : 'end';\n          break;\n        case 'right':\n          textAnchor = mirror ? 'end' : 'start';\n          break;\n        default:\n          textAnchor = 'middle';\n          break;\n      }\n      return textAnchor;\n    }\n  }, {\n    key: \"getTickVerticalAnchor\",\n    value: function getTickVerticalAnchor() {\n      var _this$props4 = this.props,\n        orientation = _this$props4.orientation,\n        mirror = _this$props4.mirror;\n      var verticalAnchor = 'end';\n      switch (orientation) {\n        case 'left':\n        case 'right':\n          verticalAnchor = 'middle';\n          break;\n        case 'top':\n          verticalAnchor = mirror ? 'start' : 'end';\n          break;\n        default:\n          verticalAnchor = mirror ? 'end' : 'start';\n          break;\n      }\n      return verticalAnchor;\n    }\n  }, {\n    key: \"renderAxisLine\",\n    value: function renderAxisLine() {\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        y = _this$props5.y,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        orientation = _this$props5.orientation,\n        mirror = _this$props5.mirror,\n        axisLine = _this$props5.axisLine;\n      var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props)), filterProps(axisLine)), {}, {\n        fill: 'none'\n      });\n      if (orientation === 'top' || orientation === 'bottom') {\n        var needHeight = +(orientation === 'top' && !mirror || orientation === 'bottom' && mirror);\n        props = _objectSpread(_objectSpread({}, props), {}, {\n          x1: x,\n          y1: y + needHeight * height,\n          x2: x + width,\n          y2: y + needHeight * height\n        });\n      } else {\n        var needWidth = +(orientation === 'left' && !mirror || orientation === 'right' && mirror);\n        props = _objectSpread(_objectSpread({}, props), {}, {\n          x1: x + needWidth * width,\n          y1: y,\n          x2: x + needWidth * width,\n          y2: y + height\n        });\n      }\n      return /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n        className: classNames('recharts-cartesian-axis-line', _get(axisLine, 'className'))\n      }));\n    }\n  }, {\n    key: \"renderTicks\",\n    value:\n    /**\n     * render the ticks\n     * @param {Array} ticks The ticks to actually render (overrides what was passed in props)\n     * @param {string} fontSize Fontsize to consider for tick spacing\n     * @param {string} letterSpacing Letterspacing to consider for tick spacing\n     * @return {ReactComponent} renderedTicks\n     */\n    function renderTicks(ticks, fontSize, letterSpacing) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        tickLine = _this$props6.tickLine,\n        stroke = _this$props6.stroke,\n        tick = _this$props6.tick,\n        tickFormatter = _this$props6.tickFormatter,\n        unit = _this$props6.unit;\n      var finalTicks = getTicks(_objectSpread(_objectSpread({}, this.props), {}, {\n        ticks: ticks\n      }), fontSize, letterSpacing);\n      var textAnchor = this.getTickTextAnchor();\n      var verticalAnchor = this.getTickVerticalAnchor();\n      var axisProps = filterProps(this.props);\n      var customTickProps = filterProps(tick);\n      var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n        fill: 'none'\n      }, filterProps(tickLine));\n      var items = finalTicks.map(function (entry, i) {\n        var _this2$getTickLineCoo = _this2.getTickLineCoord(entry),\n          lineCoord = _this2$getTickLineCoo.line,\n          tickCoord = _this2$getTickLineCoo.tick;\n        var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          textAnchor: textAnchor,\n          verticalAnchor: verticalAnchor\n        }, axisProps), {}, {\n          stroke: 'none',\n          fill: stroke\n        }, customTickProps), tickCoord), {}, {\n          index: i,\n          payload: entry,\n          visibleTicksCount: finalTicks.length,\n          tickFormatter: tickFormatter\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-cartesian-axis-tick\",\n          key: \"tick-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }, adaptEventsOfChild(_this2.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({}, tickLineProps, lineCoord, {\n          className: classNames('recharts-cartesian-axis-tick-line', _get(tickLine, 'className'))\n        })), tick && CartesianAxis.renderTickItem(tick, tickProps, \"\".concat(_isFunction(tickFormatter) ? tickFormatter(entry.value, i) : entry.value).concat(unit || '')));\n      });\n      return /*#__PURE__*/React.createElement(\"g\", {\n        className: \"recharts-cartesian-axis-ticks\"\n      }, items);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        axisLine = _this$props7.axisLine,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        ticksGenerator = _this$props7.ticksGenerator,\n        className = _this$props7.className,\n        hide = _this$props7.hide;\n      if (hide) {\n        return null;\n      }\n      var _this$props8 = this.props,\n        ticks = _this$props8.ticks,\n        noTicksProps = _objectWithoutProperties(_this$props8, _excluded3);\n      var finalTicks = ticks;\n      if (_isFunction(ticksGenerator)) {\n        finalTicks = ticks && ticks.length > 0 ? ticksGenerator(this.props) : ticksGenerator(noTicksProps);\n      }\n      if (width <= 0 || height <= 0 || !finalTicks || !finalTicks.length) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: classNames('recharts-cartesian-axis', className),\n        ref: function ref(_ref2) {\n          _this3.layerReference = _ref2;\n        }\n      }, axisLine && this.renderAxisLine(), this.renderTicks(finalTicks, this.state.fontSize, this.state.letterSpacing), Label.renderCallByParent(this.props));\n    }\n  }], [{\n    key: \"renderTickItem\",\n    value: function renderTickItem(option, props, value) {\n      var tickItem;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        tickItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        tickItem = option(props);\n      } else {\n        tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n          className: \"recharts-cartesian-axis-tick-value\"\n        }), value);\n      }\n      return tickItem;\n    }\n  }]);\n  return CartesianAxis;\n}(Component);\n_defineProperty(CartesianAxis, \"displayName\", 'CartesianAxis');\n_defineProperty(CartesianAxis, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  viewBox: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  // The orientation of axis\n  orientation: 'bottom',\n  // The ticks\n  ticks: [],\n  stroke: '#666',\n  tickLine: true,\n  axisLine: true,\n  tick: true,\n  mirror: false,\n  minTickGap: 5,\n  // The width or height of tick\n  tickSize: 6,\n  tickMargin: 2,\n  interval: 'preserveEnd'\n});", "map": {"version": 3, "names": ["_isFunction", "_get", "_excluded", "_excluded2", "_excluded3", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "Component", "classNames", "shallowEqual", "Layer", "Text", "Label", "isNumber", "adaptEventsOfChild", "filterProps", "getTicks", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "_Component", "_super", "_this", "state", "fontSize", "letterSpacing", "shouldComponentUpdate", "_ref", "nextState", "viewBox", "restProps", "_this$props", "viewBoxOld", "restPropsOld", "componentDidMount", "htmlLayer", "layerReference", "tick", "getElementsByClassName", "setState", "window", "getComputedStyle", "getTickLineCoord", "data", "_this$props2", "x", "y", "width", "height", "orientation", "tickSize", "mirror", "tick<PERSON>argin", "x1", "x2", "y1", "y2", "tx", "ty", "sign", "finalTickSize", "tickCoord", "coordinate", "line", "getTickTextAnchor", "_this$props3", "textAnchor", "getTickVerticalAnchor", "_this$props4", "verticalAnchor", "renderAxisLine", "_this$props5", "axisLine", "fill", "needHeight", "needWidth", "createElement", "className", "renderTicks", "ticks", "_this2", "_this$props6", "tickLine", "stroke", "tick<PERSON><PERSON><PERSON><PERSON>", "unit", "finalTicks", "axisProps", "customTickProps", "tickLineProps", "items", "map", "entry", "_this2$getTickLineCoo", "lineCoord", "tickProps", "index", "payload", "visibleTicksCount", "concat", "renderTickItem", "render", "_this3", "_this$props7", "ticksGenerator", "hide", "_this$props8", "noTicksProps", "ref", "_ref2", "renderCallByParent", "option", "tickItem", "isValidElement", "cloneElement", "minTickGap", "interval"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/CartesianAxis.js"], "sourcesContent": ["import _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nvar _excluded = [\"viewBox\"],\n  _excluded2 = [\"viewBox\"],\n  _excluded3 = [\"ticks\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Cartesian Axis\n */\nimport React, { Component } from 'react';\nimport classNames from 'classnames';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { isNumber } from '../util/DataUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTicks } from './getTicks';\nexport var CartesianAxis = /*#__PURE__*/function (_Component) {\n  _inherits(CartesianAxis, _Component);\n  var _super = _createSuper(CartesianAxis);\n  function CartesianAxis(props) {\n    var _this;\n    _classCallCheck(this, CartesianAxis);\n    _this = _super.call(this, props);\n    _this.state = {\n      fontSize: '',\n      letterSpacing: ''\n    };\n    return _this;\n  }\n  _createClass(CartesianAxis, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(_ref, nextState) {\n      var viewBox = _ref.viewBox,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      // props.viewBox is sometimes generated every time -\n      // check that specially as object equality is likely to fail\n      var _this$props = this.props,\n        viewBoxOld = _this$props.viewBox,\n        restPropsOld = _objectWithoutProperties(_this$props, _excluded2);\n      return !shallowEqual(viewBox, viewBoxOld) || !shallowEqual(restProps, restPropsOld) || !shallowEqual(nextState, this.state);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var htmlLayer = this.layerReference;\n      if (!htmlLayer) return;\n      var tick = htmlLayer.getElementsByClassName('recharts-cartesian-axis-tick-value')[0];\n      if (tick) {\n        this.setState({\n          fontSize: window.getComputedStyle(tick).fontSize,\n          letterSpacing: window.getComputedStyle(tick).letterSpacing\n        });\n      }\n    }\n\n    /**\n     * Calculate the coordinates of endpoints in ticks\n     * @param  {Object} data The data of a simple tick\n     * @return {Object} (x1, y1): The coordinate of endpoint close to tick text\n     *  (x2, y2): The coordinate of endpoint close to axis\n     */\n  }, {\n    key: \"getTickLineCoord\",\n    value: function getTickLineCoord(data) {\n      var _this$props2 = this.props,\n        x = _this$props2.x,\n        y = _this$props2.y,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        orientation = _this$props2.orientation,\n        tickSize = _this$props2.tickSize,\n        mirror = _this$props2.mirror,\n        tickMargin = _this$props2.tickMargin;\n      var x1, x2, y1, y2, tx, ty;\n      var sign = mirror ? -1 : 1;\n      var finalTickSize = data.tickSize || tickSize;\n      var tickCoord = isNumber(data.tickCoord) ? data.tickCoord : data.coordinate;\n      switch (orientation) {\n        case 'top':\n          x1 = x2 = data.coordinate;\n          y2 = y + +!mirror * height;\n          y1 = y2 - sign * finalTickSize;\n          ty = y1 - sign * tickMargin;\n          tx = tickCoord;\n          break;\n        case 'left':\n          y1 = y2 = data.coordinate;\n          x2 = x + +!mirror * width;\n          x1 = x2 - sign * finalTickSize;\n          tx = x1 - sign * tickMargin;\n          ty = tickCoord;\n          break;\n        case 'right':\n          y1 = y2 = data.coordinate;\n          x2 = x + +mirror * width;\n          x1 = x2 + sign * finalTickSize;\n          tx = x1 + sign * tickMargin;\n          ty = tickCoord;\n          break;\n        default:\n          x1 = x2 = data.coordinate;\n          y2 = y + +mirror * height;\n          y1 = y2 + sign * finalTickSize;\n          ty = y1 + sign * tickMargin;\n          tx = tickCoord;\n          break;\n      }\n      return {\n        line: {\n          x1: x1,\n          y1: y1,\n          x2: x2,\n          y2: y2\n        },\n        tick: {\n          x: tx,\n          y: ty\n        }\n      };\n    }\n  }, {\n    key: \"getTickTextAnchor\",\n    value: function getTickTextAnchor() {\n      var _this$props3 = this.props,\n        orientation = _this$props3.orientation,\n        mirror = _this$props3.mirror;\n      var textAnchor;\n      switch (orientation) {\n        case 'left':\n          textAnchor = mirror ? 'start' : 'end';\n          break;\n        case 'right':\n          textAnchor = mirror ? 'end' : 'start';\n          break;\n        default:\n          textAnchor = 'middle';\n          break;\n      }\n      return textAnchor;\n    }\n  }, {\n    key: \"getTickVerticalAnchor\",\n    value: function getTickVerticalAnchor() {\n      var _this$props4 = this.props,\n        orientation = _this$props4.orientation,\n        mirror = _this$props4.mirror;\n      var verticalAnchor = 'end';\n      switch (orientation) {\n        case 'left':\n        case 'right':\n          verticalAnchor = 'middle';\n          break;\n        case 'top':\n          verticalAnchor = mirror ? 'start' : 'end';\n          break;\n        default:\n          verticalAnchor = mirror ? 'end' : 'start';\n          break;\n      }\n      return verticalAnchor;\n    }\n  }, {\n    key: \"renderAxisLine\",\n    value: function renderAxisLine() {\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        y = _this$props5.y,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        orientation = _this$props5.orientation,\n        mirror = _this$props5.mirror,\n        axisLine = _this$props5.axisLine;\n      var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props)), filterProps(axisLine)), {}, {\n        fill: 'none'\n      });\n      if (orientation === 'top' || orientation === 'bottom') {\n        var needHeight = +(orientation === 'top' && !mirror || orientation === 'bottom' && mirror);\n        props = _objectSpread(_objectSpread({}, props), {}, {\n          x1: x,\n          y1: y + needHeight * height,\n          x2: x + width,\n          y2: y + needHeight * height\n        });\n      } else {\n        var needWidth = +(orientation === 'left' && !mirror || orientation === 'right' && mirror);\n        props = _objectSpread(_objectSpread({}, props), {}, {\n          x1: x + needWidth * width,\n          y1: y,\n          x2: x + needWidth * width,\n          y2: y + height\n        });\n      }\n      return /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n        className: classNames('recharts-cartesian-axis-line', _get(axisLine, 'className'))\n      }));\n    }\n  }, {\n    key: \"renderTicks\",\n    value:\n    /**\n     * render the ticks\n     * @param {Array} ticks The ticks to actually render (overrides what was passed in props)\n     * @param {string} fontSize Fontsize to consider for tick spacing\n     * @param {string} letterSpacing Letterspacing to consider for tick spacing\n     * @return {ReactComponent} renderedTicks\n     */\n    function renderTicks(ticks, fontSize, letterSpacing) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        tickLine = _this$props6.tickLine,\n        stroke = _this$props6.stroke,\n        tick = _this$props6.tick,\n        tickFormatter = _this$props6.tickFormatter,\n        unit = _this$props6.unit;\n      var finalTicks = getTicks(_objectSpread(_objectSpread({}, this.props), {}, {\n        ticks: ticks\n      }), fontSize, letterSpacing);\n      var textAnchor = this.getTickTextAnchor();\n      var verticalAnchor = this.getTickVerticalAnchor();\n      var axisProps = filterProps(this.props);\n      var customTickProps = filterProps(tick);\n      var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n        fill: 'none'\n      }, filterProps(tickLine));\n      var items = finalTicks.map(function (entry, i) {\n        var _this2$getTickLineCoo = _this2.getTickLineCoord(entry),\n          lineCoord = _this2$getTickLineCoo.line,\n          tickCoord = _this2$getTickLineCoo.tick;\n        var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          textAnchor: textAnchor,\n          verticalAnchor: verticalAnchor\n        }, axisProps), {}, {\n          stroke: 'none',\n          fill: stroke\n        }, customTickProps), tickCoord), {}, {\n          index: i,\n          payload: entry,\n          visibleTicksCount: finalTicks.length,\n          tickFormatter: tickFormatter\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-cartesian-axis-tick\",\n          key: \"tick-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }, adaptEventsOfChild(_this2.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({}, tickLineProps, lineCoord, {\n          className: classNames('recharts-cartesian-axis-tick-line', _get(tickLine, 'className'))\n        })), tick && CartesianAxis.renderTickItem(tick, tickProps, \"\".concat(_isFunction(tickFormatter) ? tickFormatter(entry.value, i) : entry.value).concat(unit || '')));\n      });\n      return /*#__PURE__*/React.createElement(\"g\", {\n        className: \"recharts-cartesian-axis-ticks\"\n      }, items);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        axisLine = _this$props7.axisLine,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        ticksGenerator = _this$props7.ticksGenerator,\n        className = _this$props7.className,\n        hide = _this$props7.hide;\n      if (hide) {\n        return null;\n      }\n      var _this$props8 = this.props,\n        ticks = _this$props8.ticks,\n        noTicksProps = _objectWithoutProperties(_this$props8, _excluded3);\n      var finalTicks = ticks;\n      if (_isFunction(ticksGenerator)) {\n        finalTicks = ticks && ticks.length > 0 ? ticksGenerator(this.props) : ticksGenerator(noTicksProps);\n      }\n      if (width <= 0 || height <= 0 || !finalTicks || !finalTicks.length) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: classNames('recharts-cartesian-axis', className),\n        ref: function ref(_ref2) {\n          _this3.layerReference = _ref2;\n        }\n      }, axisLine && this.renderAxisLine(), this.renderTicks(finalTicks, this.state.fontSize, this.state.letterSpacing), Label.renderCallByParent(this.props));\n    }\n  }], [{\n    key: \"renderTickItem\",\n    value: function renderTickItem(option, props, value) {\n      var tickItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        tickItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        tickItem = option(props);\n      } else {\n        tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n          className: \"recharts-cartesian-axis-tick-value\"\n        }), value);\n      }\n      return tickItem;\n    }\n  }]);\n  return CartesianAxis;\n}(Component);\n_defineProperty(CartesianAxis, \"displayName\", 'CartesianAxis');\n_defineProperty(CartesianAxis, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  viewBox: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  // The orientation of axis\n  orientation: 'bottom',\n  // The ticks\n  ticks: [],\n  stroke: '#666',\n  tickLine: true,\n  axisLine: true,\n  tick: true,\n  mirror: false,\n  minTickGap: 5,\n  // The width or height of tick\n  tickSize: 6,\n  tickMargin: 2,\n  interval: 'preserveEnd'\n});"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,SAAS,GAAG,CAAC,SAAS,CAAC;EACzBC,UAAU,GAAG,CAAC,SAAS,CAAC;EACxBC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAACpB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGQ,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAEiB,eAAe,CAACtB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACxB,MAAM,EAAEH,MAAM,CAAC0B,yBAAyB,CAACnB,MAAM,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAER,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACoB,wBAAwB,CAACb,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0B,wBAAwBA,CAACtB,MAAM,EAAEuB,QAAQ,EAAE;EAAE,IAAIvB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG4B,6BAA6B,CAACxB,MAAM,EAAEuB,QAAQ,CAAC;EAAE,IAAItB,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIgB,gBAAgB,GAAGhC,MAAM,CAACgB,qBAAqB,CAACT,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,gBAAgB,CAAC1B,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGwB,gBAAgB,CAAC5B,CAAC,CAAC;MAAE,IAAI0B,QAAQ,CAACG,OAAO,CAACzB,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACoC,oBAAoB,CAACxB,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAAS4B,6BAA6BA,CAACxB,MAAM,EAAEuB,QAAQ,EAAE;EAAE,IAAIvB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIgC,UAAU,GAAGnC,MAAM,CAACe,IAAI,CAACR,MAAM,CAAC;EAAE,IAAIC,GAAG,EAAEJ,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,UAAU,CAAC7B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAEI,GAAG,GAAG2B,UAAU,CAAC/B,CAAC,CAAC;IAAE,IAAI0B,QAAQ,CAACG,OAAO,CAACzB,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AAClT,SAASiC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACrC,MAAM,EAAEsC,KAAK,EAAE;EAAE,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,KAAK,CAACnC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIsC,UAAU,GAAGD,KAAK,CAACrC,CAAC,CAAC;IAAEsC,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IAAEqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE5C,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAE0C,cAAc,CAACH,UAAU,CAAClC,GAAG,CAAC,EAAEkC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACxC,SAAS,EAAEiD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEhD,MAAM,CAAC4B,cAAc,CAACU,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAACpD,SAAS,GAAGE,MAAM,CAACoD,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACrD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEwD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE3C,MAAM,CAAC4B,cAAc,CAACsB,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGtD,MAAM,CAACyD,cAAc,GAAGzD,MAAM,CAACyD,cAAc,CAACvD,IAAI,CAAC,CAAC,GAAG,SAASoD,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACpE,WAAW;MAAEqE,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE3D,SAAS,EAAE8D,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACrD,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAOiE,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE7D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI6B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAAC9E,SAAS,CAAC+E,OAAO,CAACnE,IAAI,CAAC0D,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAGjE,MAAM,CAACyD,cAAc,GAAGzD,MAAM,CAAC+E,cAAc,CAAC7E,IAAI,CAAC,CAAC,GAAG,SAAS+D,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAI1D,MAAM,CAAC+E,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAAS9B,eAAeA,CAAC/B,GAAG,EAAEc,GAAG,EAAE6C,KAAK,EAAE;EAAE7C,GAAG,GAAGqC,cAAc,CAACrC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC4B,cAAc,CAAClC,GAAG,EAAEc,GAAG,EAAE;MAAE6C,KAAK,EAAEA,KAAK;MAAEhC,UAAU,EAAE,IAAI;MAAEsB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAElD,GAAG,CAACc,GAAG,CAAC,GAAG6C,KAAK;EAAE;EAAE,OAAO3D,GAAG;AAAE;AAC3O,SAASmD,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAIxE,GAAG,GAAGyE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOvF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAG0E,MAAM,CAAC1E,GAAG,CAAC;AAAE;AAC5H,SAASyE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI3F,OAAO,CAAC0F,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACxF,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC3E,IAAI,CAACyE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI3F,OAAO,CAAC+F,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,IAAIC,aAAa,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC5DrD,SAAS,CAACoD,aAAa,EAAEC,UAAU,CAAC;EACpC,IAAIC,MAAM,GAAG5C,YAAY,CAAC0C,aAAa,CAAC;EACxC,SAASA,aAAaA,CAAC5D,KAAK,EAAE;IAC5B,IAAI+D,KAAK;IACTpE,eAAe,CAAC,IAAI,EAAEiE,aAAa,CAAC;IACpCG,KAAK,GAAGD,MAAM,CAAC7F,IAAI,CAAC,IAAI,EAAE+B,KAAK,CAAC;IAChC+D,KAAK,CAACC,KAAK,GAAG;MACZC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE;IACjB,CAAC;IACD,OAAOH,KAAK;EACd;EACA1D,YAAY,CAACuD,aAAa,EAAE,CAAC;IAC3B7F,GAAG,EAAE,uBAAuB;IAC5B6C,KAAK,EAAE,SAASuD,qBAAqBA,CAACC,IAAI,EAAEC,SAAS,EAAE;MACrD,IAAIC,OAAO,GAAGF,IAAI,CAACE,OAAO;QACxBC,SAAS,GAAGnF,wBAAwB,CAACgF,IAAI,EAAEvH,SAAS,CAAC;MACvD;MACA;MACA,IAAI2H,WAAW,GAAG,IAAI,CAACxE,KAAK;QAC1ByE,UAAU,GAAGD,WAAW,CAACF,OAAO;QAChCI,YAAY,GAAGtF,wBAAwB,CAACoF,WAAW,EAAE1H,UAAU,CAAC;MAClE,OAAO,CAACsG,YAAY,CAACkB,OAAO,EAAEG,UAAU,CAAC,IAAI,CAACrB,YAAY,CAACmB,SAAS,EAAEG,YAAY,CAAC,IAAI,CAACtB,YAAY,CAACiB,SAAS,EAAE,IAAI,CAACL,KAAK,CAAC;IAC7H;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,mBAAmB;IACxB6C,KAAK,EAAE,SAAS+D,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,SAAS,GAAG,IAAI,CAACC,cAAc;MACnC,IAAI,CAACD,SAAS,EAAE;MAChB,IAAIE,IAAI,GAAGF,SAAS,CAACG,sBAAsB,CAAC,oCAAoC,CAAC,CAAC,CAAC,CAAC;MACpF,IAAID,IAAI,EAAE;QACR,IAAI,CAACE,QAAQ,CAAC;UACZf,QAAQ,EAAEgB,MAAM,CAACC,gBAAgB,CAACJ,IAAI,CAAC,CAACb,QAAQ;UAChDC,aAAa,EAAEe,MAAM,CAACC,gBAAgB,CAACJ,IAAI,CAAC,CAACZ;QAC/C,CAAC,CAAC;MACJ;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;EACE,CAAC,EAAE;IACDnG,GAAG,EAAE,kBAAkB;IACvB6C,KAAK,EAAE,SAASuE,gBAAgBA,CAACC,IAAI,EAAE;MACrC,IAAIC,YAAY,GAAG,IAAI,CAACrF,KAAK;QAC3BsF,CAAC,GAAGD,YAAY,CAACC,CAAC;QAClBC,CAAC,GAAGF,YAAY,CAACE,CAAC;QAClBC,KAAK,GAAGH,YAAY,CAACG,KAAK;QAC1BC,MAAM,GAAGJ,YAAY,CAACI,MAAM;QAC5BC,WAAW,GAAGL,YAAY,CAACK,WAAW;QACtCC,QAAQ,GAAGN,YAAY,CAACM,QAAQ;QAChCC,MAAM,GAAGP,YAAY,CAACO,MAAM;QAC5BC,UAAU,GAAGR,YAAY,CAACQ,UAAU;MACtC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAC1B,IAAIC,IAAI,GAAGR,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1B,IAAIS,aAAa,GAAGjB,IAAI,CAACO,QAAQ,IAAIA,QAAQ;MAC7C,IAAIW,SAAS,GAAG9C,QAAQ,CAAC4B,IAAI,CAACkB,SAAS,CAAC,GAAGlB,IAAI,CAACkB,SAAS,GAAGlB,IAAI,CAACmB,UAAU;MAC3E,QAAQb,WAAW;QACjB,KAAK,KAAK;UACRI,EAAE,GAAGC,EAAE,GAAGX,IAAI,CAACmB,UAAU;UACzBN,EAAE,GAAGV,CAAC,GAAG,CAAC,CAACK,MAAM,GAAGH,MAAM;UAC1BO,EAAE,GAAGC,EAAE,GAAGG,IAAI,GAAGC,aAAa;UAC9BF,EAAE,GAAGH,EAAE,GAAGI,IAAI,GAAGP,UAAU;UAC3BK,EAAE,GAAGI,SAAS;UACd;QACF,KAAK,MAAM;UACTN,EAAE,GAAGC,EAAE,GAAGb,IAAI,CAACmB,UAAU;UACzBR,EAAE,GAAGT,CAAC,GAAG,CAAC,CAACM,MAAM,GAAGJ,KAAK;UACzBM,EAAE,GAAGC,EAAE,GAAGK,IAAI,GAAGC,aAAa;UAC9BH,EAAE,GAAGJ,EAAE,GAAGM,IAAI,GAAGP,UAAU;UAC3BM,EAAE,GAAGG,SAAS;UACd;QACF,KAAK,OAAO;UACVN,EAAE,GAAGC,EAAE,GAAGb,IAAI,CAACmB,UAAU;UACzBR,EAAE,GAAGT,CAAC,GAAG,CAACM,MAAM,GAAGJ,KAAK;UACxBM,EAAE,GAAGC,EAAE,GAAGK,IAAI,GAAGC,aAAa;UAC9BH,EAAE,GAAGJ,EAAE,GAAGM,IAAI,GAAGP,UAAU;UAC3BM,EAAE,GAAGG,SAAS;UACd;QACF;UACER,EAAE,GAAGC,EAAE,GAAGX,IAAI,CAACmB,UAAU;UACzBN,EAAE,GAAGV,CAAC,GAAG,CAACK,MAAM,GAAGH,MAAM;UACzBO,EAAE,GAAGC,EAAE,GAAGG,IAAI,GAAGC,aAAa;UAC9BF,EAAE,GAAGH,EAAE,GAAGI,IAAI,GAAGP,UAAU;UAC3BK,EAAE,GAAGI,SAAS;UACd;MACJ;MACA,OAAO;QACLE,IAAI,EAAE;UACJV,EAAE,EAAEA,EAAE;UACNE,EAAE,EAAEA,EAAE;UACND,EAAE,EAAEA,EAAE;UACNE,EAAE,EAAEA;QACN,CAAC;QACDnB,IAAI,EAAE;UACJQ,CAAC,EAAEY,EAAE;UACLX,CAAC,EAAEY;QACL;MACF,CAAC;IACH;EACF,CAAC,EAAE;IACDpI,GAAG,EAAE,mBAAmB;IACxB6C,KAAK,EAAE,SAAS6F,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,YAAY,GAAG,IAAI,CAAC1G,KAAK;QAC3B0F,WAAW,GAAGgB,YAAY,CAAChB,WAAW;QACtCE,MAAM,GAAGc,YAAY,CAACd,MAAM;MAC9B,IAAIe,UAAU;MACd,QAAQjB,WAAW;QACjB,KAAK,MAAM;UACTiB,UAAU,GAAGf,MAAM,GAAG,OAAO,GAAG,KAAK;UACrC;QACF,KAAK,OAAO;UACVe,UAAU,GAAGf,MAAM,GAAG,KAAK,GAAG,OAAO;UACrC;QACF;UACEe,UAAU,GAAG,QAAQ;UACrB;MACJ;MACA,OAAOA,UAAU;IACnB;EACF,CAAC,EAAE;IACD5I,GAAG,EAAE,uBAAuB;IAC5B6C,KAAK,EAAE,SAASgG,qBAAqBA,CAAA,EAAG;MACtC,IAAIC,YAAY,GAAG,IAAI,CAAC7G,KAAK;QAC3B0F,WAAW,GAAGmB,YAAY,CAACnB,WAAW;QACtCE,MAAM,GAAGiB,YAAY,CAACjB,MAAM;MAC9B,IAAIkB,cAAc,GAAG,KAAK;MAC1B,QAAQpB,WAAW;QACjB,KAAK,MAAM;QACX,KAAK,OAAO;UACVoB,cAAc,GAAG,QAAQ;UACzB;QACF,KAAK,KAAK;UACRA,cAAc,GAAGlB,MAAM,GAAG,OAAO,GAAG,KAAK;UACzC;QACF;UACEkB,cAAc,GAAGlB,MAAM,GAAG,KAAK,GAAG,OAAO;UACzC;MACJ;MACA,OAAOkB,cAAc;IACvB;EACF,CAAC,EAAE;IACD/I,GAAG,EAAE,gBAAgB;IACrB6C,KAAK,EAAE,SAASmG,cAAcA,CAAA,EAAG;MAC/B,IAAIC,YAAY,GAAG,IAAI,CAAChH,KAAK;QAC3BsF,CAAC,GAAG0B,YAAY,CAAC1B,CAAC;QAClBC,CAAC,GAAGyB,YAAY,CAACzB,CAAC;QAClBC,KAAK,GAAGwB,YAAY,CAACxB,KAAK;QAC1BC,MAAM,GAAGuB,YAAY,CAACvB,MAAM;QAC5BC,WAAW,GAAGsB,YAAY,CAACtB,WAAW;QACtCE,MAAM,GAAGoB,YAAY,CAACpB,MAAM;QAC5BqB,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAClC,IAAIjH,KAAK,GAAGlB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4E,WAAW,CAAC,IAAI,CAAC1D,KAAK,CAAC,CAAC,EAAE0D,WAAW,CAACuD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9GC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAIxB,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,EAAE;QACrD,IAAIyB,UAAU,GAAG,EAAEzB,WAAW,KAAK,KAAK,IAAI,CAACE,MAAM,IAAIF,WAAW,KAAK,QAAQ,IAAIE,MAAM,CAAC;QAC1F5F,KAAK,GAAGlB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClD8F,EAAE,EAAER,CAAC;UACLU,EAAE,EAAET,CAAC,GAAG4B,UAAU,GAAG1B,MAAM;UAC3BM,EAAE,EAAET,CAAC,GAAGE,KAAK;UACbS,EAAE,EAAEV,CAAC,GAAG4B,UAAU,GAAG1B;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI2B,SAAS,GAAG,EAAE1B,WAAW,KAAK,MAAM,IAAI,CAACE,MAAM,IAAIF,WAAW,KAAK,OAAO,IAAIE,MAAM,CAAC;QACzF5F,KAAK,GAAGlB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClD8F,EAAE,EAAER,CAAC,GAAG8B,SAAS,GAAG5B,KAAK;UACzBQ,EAAE,EAAET,CAAC;UACLQ,EAAE,EAAET,CAAC,GAAG8B,SAAS,GAAG5B,KAAK;UACzBS,EAAE,EAAEV,CAAC,GAAGE;QACV,CAAC,CAAC;MACJ;MACA,OAAO,aAAaxC,KAAK,CAACoE,aAAa,CAAC,MAAM,EAAE/J,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,EAAE;QAClEsH,SAAS,EAAEnE,UAAU,CAAC,8BAA8B,EAAEvG,IAAI,CAACqK,QAAQ,EAAE,WAAW,CAAC;MACnF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDlJ,GAAG,EAAE,aAAa;IAClB6C,KAAK;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAAS2G,WAAWA,CAACC,KAAK,EAAEvD,QAAQ,EAAEC,aAAa,EAAE;MACnD,IAAIuD,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC1H,KAAK;QAC3B2H,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5B9C,IAAI,GAAG4C,YAAY,CAAC5C,IAAI;QACxB+C,aAAa,GAAGH,YAAY,CAACG,aAAa;QAC1CC,IAAI,GAAGJ,YAAY,CAACI,IAAI;MAC1B,IAAIC,UAAU,GAAGpE,QAAQ,CAAC7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzEwH,KAAK,EAAEA;MACT,CAAC,CAAC,EAAEvD,QAAQ,EAAEC,aAAa,CAAC;MAC5B,IAAIyC,UAAU,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;MACzC,IAAIK,cAAc,GAAG,IAAI,CAACF,qBAAqB,CAAC,CAAC;MACjD,IAAIoB,SAAS,GAAGtE,WAAW,CAAC,IAAI,CAAC1D,KAAK,CAAC;MACvC,IAAIiI,eAAe,GAAGvE,WAAW,CAACoB,IAAI,CAAC;MACvC,IAAIoD,aAAa,GAAGpJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkJ,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QAClEd,IAAI,EAAE;MACR,CAAC,EAAExD,WAAW,CAACiE,QAAQ,CAAC,CAAC;MACzB,IAAIQ,KAAK,GAAGJ,UAAU,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE1K,CAAC,EAAE;QAC7C,IAAI2K,qBAAqB,GAAGb,MAAM,CAACtC,gBAAgB,CAACkD,KAAK,CAAC;UACxDE,SAAS,GAAGD,qBAAqB,CAAC9B,IAAI;UACtCF,SAAS,GAAGgC,qBAAqB,CAACxD,IAAI;QACxC,IAAI0D,SAAS,GAAG1J,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACtE6H,UAAU,EAAEA,UAAU;UACtBG,cAAc,EAAEA;QAClB,CAAC,EAAEkB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACjBJ,MAAM,EAAE,MAAM;UACdV,IAAI,EAAEU;QACR,CAAC,EAAEK,eAAe,CAAC,EAAE3B,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACnCmC,KAAK,EAAE9K,CAAC;UACR+K,OAAO,EAAEL,KAAK;UACdM,iBAAiB,EAAEZ,UAAU,CAAClK,MAAM;UACpCgK,aAAa,EAAEA;QACjB,CAAC,CAAC;QACF,OAAO,aAAa5E,KAAK,CAACoE,aAAa,CAAChE,KAAK,EAAE/F,QAAQ,CAAC;UACtDgK,SAAS,EAAE,8BAA8B;UACzCvJ,GAAG,EAAE,OAAO,CAAC6K,MAAM,CAACjL,CAAC,CAAC,CAAC;QACzB,CAAC,EAAE8F,kBAAkB,CAACgE,MAAM,CAACzH,KAAK,EAAEqI,KAAK,EAAE1K,CAAC,CAAC,CAAC,EAAEgK,QAAQ,IAAI,aAAa1E,KAAK,CAACoE,aAAa,CAAC,MAAM,EAAE/J,QAAQ,CAAC,CAAC,CAAC,EAAE4K,aAAa,EAAEK,SAAS,EAAE;UAC1IjB,SAAS,EAAEnE,UAAU,CAAC,mCAAmC,EAAEvG,IAAI,CAAC+K,QAAQ,EAAE,WAAW,CAAC;QACxF,CAAC,CAAC,CAAC,EAAE7C,IAAI,IAAIlB,aAAa,CAACiF,cAAc,CAAC/D,IAAI,EAAE0D,SAAS,EAAE,EAAE,CAACI,MAAM,CAACjM,WAAW,CAACkL,aAAa,CAAC,GAAGA,aAAa,CAACQ,KAAK,CAACzH,KAAK,EAAEjD,CAAC,CAAC,GAAG0K,KAAK,CAACzH,KAAK,CAAC,CAACgI,MAAM,CAACd,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;MACrK,CAAC,CAAC;MACF,OAAO,aAAa7E,KAAK,CAACoE,aAAa,CAAC,GAAG,EAAE;QAC3CC,SAAS,EAAE;MACb,CAAC,EAAEa,KAAK,CAAC;IACX;EACF,CAAC,EAAE;IACDpK,GAAG,EAAE,QAAQ;IACb6C,KAAK,EAAE,SAASkI,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAChJ,KAAK;QAC3BiH,QAAQ,GAAG+B,YAAY,CAAC/B,QAAQ;QAChCzB,KAAK,GAAGwD,YAAY,CAACxD,KAAK;QAC1BC,MAAM,GAAGuD,YAAY,CAACvD,MAAM;QAC5BwD,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5C3B,SAAS,GAAG0B,YAAY,CAAC1B,SAAS;QAClC4B,IAAI,GAAGF,YAAY,CAACE,IAAI;MAC1B,IAAIA,IAAI,EAAE;QACR,OAAO,IAAI;MACb;MACA,IAAIC,YAAY,GAAG,IAAI,CAACnJ,KAAK;QAC3BwH,KAAK,GAAG2B,YAAY,CAAC3B,KAAK;QAC1B4B,YAAY,GAAGhK,wBAAwB,CAAC+J,YAAY,EAAEpM,UAAU,CAAC;MACnE,IAAIgL,UAAU,GAAGP,KAAK;MACtB,IAAI7K,WAAW,CAACsM,cAAc,CAAC,EAAE;QAC/BlB,UAAU,GAAGP,KAAK,IAAIA,KAAK,CAAC3J,MAAM,GAAG,CAAC,GAAGoL,cAAc,CAAC,IAAI,CAACjJ,KAAK,CAAC,GAAGiJ,cAAc,CAACG,YAAY,CAAC;MACpG;MACA,IAAI5D,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,CAAC,IAAI,CAACsC,UAAU,IAAI,CAACA,UAAU,CAAClK,MAAM,EAAE;QAClE,OAAO,IAAI;MACb;MACA,OAAO,aAAaoF,KAAK,CAACoE,aAAa,CAAChE,KAAK,EAAE;QAC7CiE,SAAS,EAAEnE,UAAU,CAAC,yBAAyB,EAAEmE,SAAS,CAAC;QAC3D+B,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;UACvBP,MAAM,CAAClE,cAAc,GAAGyE,KAAK;QAC/B;MACF,CAAC,EAAErC,QAAQ,IAAI,IAAI,CAACF,cAAc,CAAC,CAAC,EAAE,IAAI,CAACQ,WAAW,CAACQ,UAAU,EAAE,IAAI,CAAC/D,KAAK,CAACC,QAAQ,EAAE,IAAI,CAACD,KAAK,CAACE,aAAa,CAAC,EAAEX,KAAK,CAACgG,kBAAkB,CAAC,IAAI,CAACvJ,KAAK,CAAC,CAAC;IAC1J;EACF,CAAC,CAAC,EAAE,CAAC;IACHjC,GAAG,EAAE,gBAAgB;IACrB6C,KAAK,EAAE,SAASiI,cAAcA,CAACW,MAAM,EAAExJ,KAAK,EAAEY,KAAK,EAAE;MACnD,IAAI6I,QAAQ;MACZ,IAAK,aAAaxG,KAAK,CAACyG,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,QAAQ,GAAG,aAAaxG,KAAK,CAAC0G,YAAY,CAACH,MAAM,EAAExJ,KAAK,CAAC;MAC3D,CAAC,MAAM,IAAIrD,WAAW,CAAC6M,MAAM,CAAC,EAAE;QAC9BC,QAAQ,GAAGD,MAAM,CAACxJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLyJ,QAAQ,GAAG,aAAaxG,KAAK,CAACoE,aAAa,CAAC/D,IAAI,EAAEhG,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,EAAE;UACpEsH,SAAS,EAAE;QACb,CAAC,CAAC,EAAE1G,KAAK,CAAC;MACZ;MACA,OAAO6I,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAO7F,aAAa;AACtB,CAAC,CAACV,SAAS,CAAC;AACZlE,eAAe,CAAC4E,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC9D5E,eAAe,CAAC4E,aAAa,EAAE,cAAc,EAAE;EAC7C0B,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTnB,OAAO,EAAE;IACPgB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACD;EACAC,WAAW,EAAE,QAAQ;EACrB;EACA8B,KAAK,EAAE,EAAE;EACTI,MAAM,EAAE,MAAM;EACdD,QAAQ,EAAE,IAAI;EACdV,QAAQ,EAAE,IAAI;EACdnC,IAAI,EAAE,IAAI;EACVc,MAAM,EAAE,KAAK;EACbgE,UAAU,EAAE,CAAC;EACb;EACAjE,QAAQ,EAAE,CAAC;EACXE,UAAU,EAAE,CAAC;EACbgE,QAAQ,EAAE;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}