{"ast": null, "code": "/**\n * BaseSelect provide some parsed data into context.\n * You can use this hooks to get them.\n */\n\nimport * as React from 'react';\nexport var BaseSelectContext = /*#__PURE__*/React.createContext(null);\nexport default function useBaseProps() {\n  return React.useContext(BaseSelectContext);\n}", "map": {"version": 3, "names": ["React", "BaseSelectContext", "createContext", "useBaseProps", "useContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/hooks/useBaseProps.js"], "sourcesContent": ["/**\n * BaseSelect provide some parsed data into context.\n * You can use this hooks to get them.\n */\n\nimport * as React from 'react';\nexport var BaseSelectContext = /*#__PURE__*/React.createContext(null);\nexport default function useBaseProps() {\n  return React.useContext(BaseSelectContext);\n}"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACrE,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,OAAOH,KAAK,CAACI,UAAU,CAACH,iBAAiB,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}