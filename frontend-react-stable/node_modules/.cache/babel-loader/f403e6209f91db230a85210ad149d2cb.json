{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport TextArea from '../input/TextArea';\nimport { cloneElement } from '../_util/reactNode';\nvar Editable = function Editable(_ref) {\n  var prefixCls = _ref.prefixCls,\n    ariaLabel = _ref['aria-label'],\n    className = _ref.className,\n    style = _ref.style,\n    direction = _ref.direction,\n    maxLength = _ref.maxLength,\n    _ref$autoSize = _ref.autoSize,\n    autoSize = _ref$autoSize === void 0 ? true : _ref$autoSize,\n    value = _ref.value,\n    onSave = _ref.onSave,\n    onCancel = _ref.onCancel,\n    onEnd = _ref.onEnd,\n    component = _ref.component,\n    _ref$enterIcon = _ref.enterIcon,\n    enterIcon = _ref$enterIcon === void 0 ? /*#__PURE__*/React.createElement(EnterOutlined, null) : _ref$enterIcon;\n  var ref = React.useRef(null);\n  var inComposition = React.useRef(false);\n  var lastKeyCode = React.useRef();\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    current = _React$useState2[0],\n    setCurrent = _React$useState2[1];\n  React.useEffect(function () {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(function () {\n    if (ref.current && ref.current.resizableTextArea) {\n      var textArea = ref.current.resizableTextArea.textArea;\n      textArea.focus();\n      var length = textArea.value.length;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  var onChange = function onChange(_ref2) {\n    var target = _ref2.target;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  var onCompositionStart = function onCompositionStart() {\n    inComposition.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    inComposition.current = false;\n  };\n  var onKeyDown = function onKeyDown(_ref3) {\n    var keyCode = _ref3.keyCode;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  var confirmChange = function confirmChange() {\n    onSave(current.trim());\n  };\n  var onKeyUp = function onKeyUp(_ref4) {\n    var keyCode = _ref4.keyCode,\n      ctrlKey = _ref4.ctrlKey,\n      altKey = _ref4.altKey,\n      metaKey = _ref4.metaKey,\n      shiftKey = _ref4.shiftKey;\n    // Check if it's a real key\n    if (lastKeyCode.current === keyCode && !inComposition.current && !ctrlKey && !altKey && !metaKey && !shiftKey) {\n      if (keyCode === KeyCode.ENTER) {\n        confirmChange();\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      } else if (keyCode === KeyCode.ESC) {\n        onCancel();\n      }\n    }\n  };\n  var onBlur = function onBlur() {\n    confirmChange();\n  };\n  var textClassName = component ? \"\".concat(prefixCls, \"-\").concat(component) : '';\n  var textAreaClassName = classNames(prefixCls, \"\".concat(prefixCls, \"-edit-content\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className, textClassName);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: \"\".concat(prefixCls, \"-edit-content-confirm\")\n  }) : null);\n};\nexport default Editable;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "EnterOutlined", "classNames", "KeyCode", "React", "TextArea", "cloneElement", "Editable", "_ref", "prefixCls", "aria<PERSON><PERSON><PERSON>", "className", "style", "direction", "max<PERSON><PERSON><PERSON>", "_ref$autoSize", "autoSize", "value", "onSave", "onCancel", "onEnd", "component", "_ref$enterIcon", "enterIcon", "createElement", "ref", "useRef", "inComposition", "lastKeyCode", "_React$useState", "useState", "_React$useState2", "current", "setCurrent", "useEffect", "resizableTextArea", "textArea", "focus", "length", "setSelectionRange", "onChange", "_ref2", "target", "replace", "onCompositionStart", "onCompositionEnd", "onKeyDown", "_ref3", "keyCode", "confirmChange", "trim", "onKeyUp", "_ref4", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "ENTER", "ESC", "onBlur", "textClassName", "concat", "textAreaClassName", "rows"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Editable.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport TextArea from '../input/TextArea';\nimport { cloneElement } from '../_util/reactNode';\nvar Editable = function Editable(_ref) {\n  var prefixCls = _ref.prefixCls,\n    ariaLabel = _ref['aria-label'],\n    className = _ref.className,\n    style = _ref.style,\n    direction = _ref.direction,\n    maxLength = _ref.maxLength,\n    _ref$autoSize = _ref.autoSize,\n    autoSize = _ref$autoSize === void 0 ? true : _ref$autoSize,\n    value = _ref.value,\n    onSave = _ref.onSave,\n    onCancel = _ref.onCancel,\n    onEnd = _ref.onEnd,\n    component = _ref.component,\n    _ref$enterIcon = _ref.enterIcon,\n    enterIcon = _ref$enterIcon === void 0 ? /*#__PURE__*/React.createElement(EnterOutlined, null) : _ref$enterIcon;\n  var ref = React.useRef(null);\n  var inComposition = React.useRef(false);\n  var lastKeyCode = React.useRef();\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    current = _React$useState2[0],\n    setCurrent = _React$useState2[1];\n  React.useEffect(function () {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(function () {\n    if (ref.current && ref.current.resizableTextArea) {\n      var textArea = ref.current.resizableTextArea.textArea;\n      textArea.focus();\n      var length = textArea.value.length;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  var onChange = function onChange(_ref2) {\n    var target = _ref2.target;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  var onCompositionStart = function onCompositionStart() {\n    inComposition.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    inComposition.current = false;\n  };\n  var onKeyDown = function onKeyDown(_ref3) {\n    var keyCode = _ref3.keyCode;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  var confirmChange = function confirmChange() {\n    onSave(current.trim());\n  };\n  var onKeyUp = function onKeyUp(_ref4) {\n    var keyCode = _ref4.keyCode,\n      ctrlKey = _ref4.ctrlKey,\n      altKey = _ref4.altKey,\n      metaKey = _ref4.metaKey,\n      shiftKey = _ref4.shiftKey;\n    // Check if it's a real key\n    if (lastKeyCode.current === keyCode && !inComposition.current && !ctrlKey && !altKey && !metaKey && !shiftKey) {\n      if (keyCode === KeyCode.ENTER) {\n        confirmChange();\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      } else if (keyCode === KeyCode.ESC) {\n        onCancel();\n      }\n    }\n  };\n  var onBlur = function onBlur() {\n    confirmChange();\n  };\n  var textClassName = component ? \"\".concat(prefixCls, \"-\").concat(component) : '';\n  var textAreaClassName = classNames(prefixCls, \"\".concat(prefixCls, \"-edit-content\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className, textClassName);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: \"\".concat(prefixCls, \"-edit-content-confirm\")\n  }) : null);\n};\nexport default Editable;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,SAAS,GAAGF,IAAI,CAAC,YAAY,CAAC;IAC9BG,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC1BC,SAAS,GAAGN,IAAI,CAACM,SAAS;IAC1BC,aAAa,GAAGP,IAAI,CAACQ,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;IAC1DE,KAAK,GAAGT,IAAI,CAACS,KAAK;IAClBC,MAAM,GAAGV,IAAI,CAACU,MAAM;IACpBC,QAAQ,GAAGX,IAAI,CAACW,QAAQ;IACxBC,KAAK,GAAGZ,IAAI,CAACY,KAAK;IAClBC,SAAS,GAAGb,IAAI,CAACa,SAAS;IAC1BC,cAAc,GAAGd,IAAI,CAACe,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,aAAalB,KAAK,CAACoB,aAAa,CAACvB,aAAa,EAAE,IAAI,CAAC,GAAGqB,cAAc;EAChH,IAAIG,GAAG,GAAGrB,KAAK,CAACsB,MAAM,CAAC,IAAI,CAAC;EAC5B,IAAIC,aAAa,GAAGvB,KAAK,CAACsB,MAAM,CAAC,KAAK,CAAC;EACvC,IAAIE,WAAW,GAAGxB,KAAK,CAACsB,MAAM,CAAC,CAAC;EAChC,IAAIG,eAAe,GAAGzB,KAAK,CAAC0B,QAAQ,CAACb,KAAK,CAAC;IACzCc,gBAAgB,GAAG/B,cAAc,CAAC6B,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC3B,KAAK,CAAC8B,SAAS,CAAC,YAAY;IAC1BD,UAAU,CAAChB,KAAK,CAAC;EACnB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXb,KAAK,CAAC8B,SAAS,CAAC,YAAY;IAC1B,IAAIT,GAAG,CAACO,OAAO,IAAIP,GAAG,CAACO,OAAO,CAACG,iBAAiB,EAAE;MAChD,IAAIC,QAAQ,GAAGX,GAAG,CAACO,OAAO,CAACG,iBAAiB,CAACC,QAAQ;MACrDA,QAAQ,CAACC,KAAK,CAAC,CAAC;MAChB,IAAIC,MAAM,GAAGF,QAAQ,CAACnB,KAAK,CAACqB,MAAM;MAClCF,QAAQ,CAACG,iBAAiB,CAACD,MAAM,EAAEA,MAAM,CAAC;IAC5C;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzBT,UAAU,CAACS,MAAM,CAACzB,KAAK,CAAC0B,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;EACjD,CAAC;EACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrDjB,aAAa,CAACK,OAAO,GAAG,IAAI;EAC9B,CAAC;EACD,IAAIa,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDlB,aAAa,CAACK,OAAO,GAAG,KAAK;EAC/B,CAAC;EACD,IAAIc,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC3B;IACA,IAAIrB,aAAa,CAACK,OAAO,EAAE;IAC3BJ,WAAW,CAACI,OAAO,GAAGgB,OAAO;EAC/B,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C/B,MAAM,CAACc,OAAO,CAACkB,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpC,IAAIJ,OAAO,GAAGI,KAAK,CAACJ,OAAO;MACzBK,OAAO,GAAGD,KAAK,CAACC,OAAO;MACvBC,MAAM,GAAGF,KAAK,CAACE,MAAM;MACrBC,OAAO,GAAGH,KAAK,CAACG,OAAO;MACvBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3B;IACA,IAAI5B,WAAW,CAACI,OAAO,KAAKgB,OAAO,IAAI,CAACrB,aAAa,CAACK,OAAO,IAAI,CAACqB,OAAO,IAAI,CAACC,MAAM,IAAI,CAACC,OAAO,IAAI,CAACC,QAAQ,EAAE;MAC7G,IAAIR,OAAO,KAAK7C,OAAO,CAACsD,KAAK,EAAE;QAC7BR,aAAa,CAAC,CAAC;QACf7B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;MACvD,CAAC,MAAM,IAAI4B,OAAO,KAAK7C,OAAO,CAACuD,GAAG,EAAE;QAClCvC,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EACD,IAAIwC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7BV,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,IAAIW,aAAa,GAAGvC,SAAS,GAAG,EAAE,CAACwC,MAAM,CAACpD,SAAS,EAAE,GAAG,CAAC,CAACoD,MAAM,CAACxC,SAAS,CAAC,GAAG,EAAE;EAChF,IAAIyC,iBAAiB,GAAG5D,UAAU,CAACO,SAAS,EAAE,EAAE,CAACoD,MAAM,CAACpD,SAAS,EAAE,eAAe,CAAC,EAAEV,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8D,MAAM,CAACpD,SAAS,EAAE,MAAM,CAAC,EAAEI,SAAS,KAAK,KAAK,CAAC,EAAEF,SAAS,EAAEiD,aAAa,CAAC;EACtL,OAAO,aAAaxD,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7Cb,SAAS,EAAEmD,iBAAiB;IAC5BlD,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaR,KAAK,CAACoB,aAAa,CAACnB,QAAQ,EAAE;IAC5CoB,GAAG,EAAEA,GAAG;IACRX,SAAS,EAAEA,SAAS;IACpBG,KAAK,EAAEe,OAAO;IACdQ,QAAQ,EAAEA,QAAQ;IAClBM,SAAS,EAAEA,SAAS;IACpBK,OAAO,EAAEA,OAAO;IAChBP,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAClCc,MAAM,EAAEA,MAAM;IACd,YAAY,EAAEjD,SAAS;IACvBqD,IAAI,EAAE,CAAC;IACP/C,QAAQ,EAAEA;EACZ,CAAC,CAAC,EAAEO,SAAS,KAAK,IAAI,GAAGjB,YAAY,CAACiB,SAAS,EAAE;IAC/CZ,SAAS,EAAE,EAAE,CAACkD,MAAM,CAACpD,SAAS,EAAE,uBAAuB;EACzD,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ,CAAC;AACD,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}