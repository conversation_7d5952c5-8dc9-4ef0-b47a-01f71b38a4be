{"ast": null, "code": "/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\nmodule.exports = baseIsNaN;", "map": {"version": 3, "names": ["baseIsNaN", "value", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseIsNaN.js"], "sourcesContent": ["/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\n\nmodule.exports = baseIsNaN;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOA,KAAK,KAAKA,KAAK;AACxB;AAEAC,MAAM,CAACC,OAAO,GAAGH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}