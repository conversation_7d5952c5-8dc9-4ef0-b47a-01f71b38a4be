{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = identity;\nfunction identity(x) {\n  return x;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "identity", "x"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/identity.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = identity;\n\nfunction identity(x) {\n  return x;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}