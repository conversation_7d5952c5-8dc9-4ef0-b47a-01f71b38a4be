{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nimport Item from './Item';\nimport Compact from './Compact';\nexport var SpaceContext = /*#__PURE__*/React.createContext({\n  latestIndex: 0,\n  horizontalSize: 0,\n  verticalSize: 0,\n  supportFlexGap: false\n});\nvar spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\nvar Space = function Space(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    space = _React$useContext.space,\n    directionConfig = _React$useContext.direction;\n  var _props$size = props.size,\n    size = _props$size === void 0 ? (space === null || space === void 0 ? void 0 : space.size) || 'small' : _props$size,\n    align = props.align,\n    className = props.className,\n    children = props.children,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'horizontal' : _props$direction,\n    customizePrefixCls = props.prefixCls,\n    split = props.split,\n    style = props.style,\n    _props$wrap = props.wrap,\n    wrap = _props$wrap === void 0 ? false : _props$wrap,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\"]);\n  var supportFlexGap = useFlexGapSupport();\n  var _React$useMemo = React.useMemo(function () {\n      return (Array.isArray(size) ? size : [size, size]).map(function (item) {\n        return getNumberSize(item);\n      });\n    }, [size]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    horizontalSize = _React$useMemo2[0],\n    verticalSize = _React$useMemo2[1];\n  var childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  var mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  var prefixCls = getPrefixCls('space', customizePrefixCls);\n  var cn = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-align-\").concat(mergedAlign), mergedAlign), _classNames), className);\n  var itemClassName = \"\".concat(prefixCls, \"-item\");\n  var marginDirection = directionConfig === 'rtl' ? 'marginLeft' : 'marginRight';\n  // Calculate latest one\n  var latestIndex = 0;\n  var nodes = childNodes.map(function (child, i) {\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    var key = child && child.key || \"\".concat(itemClassName, \"-\").concat(i);\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      direction: direction,\n      index: i,\n      marginDirection: marginDirection,\n      split: split,\n      wrap: wrap\n    }, child);\n  });\n  var spaceContext = React.useMemo(function () {\n    return {\n      horizontalSize: horizontalSize,\n      verticalSize: verticalSize,\n      latestIndex: latestIndex,\n      supportFlexGap: supportFlexGap\n    };\n  }, [horizontalSize, verticalSize, latestIndex, supportFlexGap]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  var gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n    // Patch for gap not support\n    if (!supportFlexGap) {\n      gapStyle.marginBottom = -verticalSize;\n    }\n  }\n  if (supportFlexGap) {\n    gapStyle.columnGap = horizontalSize;\n    gapStyle.rowGap = verticalSize;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: cn,\n    style: _extends(_extends({}, gapStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContext.Provider, {\n    value: spaceContext\n  }, nodes));\n};\nvar CompoundedSpace = Space;\nCompoundedSpace.Compact = Compact;\nexport default CompoundedSpace;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "toArray", "React", "ConfigContext", "useFlexGapSupport", "<PERSON><PERSON>", "Compact", "SpaceContext", "createContext", "latestIndex", "horizontalSize", "verticalSize", "supportFlexGap", "spaceSize", "small", "middle", "large", "getNumberSize", "size", "Space", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "space", "directionConfig", "direction", "_props$size", "align", "className", "children", "_props$direction", "customizePrefixCls", "prefixCls", "split", "style", "_props$wrap", "wrap", "otherProps", "_React$useMemo", "useMemo", "Array", "isArray", "map", "item", "_React$useMemo2", "childNodes", "keepEmpty", "mergedAlign", "undefined", "cn", "concat", "itemClassName", "marginDirection", "nodes", "child", "key", "createElement", "index", "spaceContext", "gapStyle", "flexWrap", "marginBottom", "columnGap", "rowGap", "Provider", "value", "CompoundedSpace"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/space/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nimport Item from './Item';\nimport Compact from './Compact';\nexport var SpaceContext = /*#__PURE__*/React.createContext({\n  latestIndex: 0,\n  horizontalSize: 0,\n  verticalSize: 0,\n  supportFlexGap: false\n});\nvar spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\nvar Space = function Space(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    space = _React$useContext.space,\n    directionConfig = _React$useContext.direction;\n  var _props$size = props.size,\n    size = _props$size === void 0 ? (space === null || space === void 0 ? void 0 : space.size) || 'small' : _props$size,\n    align = props.align,\n    className = props.className,\n    children = props.children,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'horizontal' : _props$direction,\n    customizePrefixCls = props.prefixCls,\n    split = props.split,\n    style = props.style,\n    _props$wrap = props.wrap,\n    wrap = _props$wrap === void 0 ? false : _props$wrap,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\"]);\n  var supportFlexGap = useFlexGapSupport();\n  var _React$useMemo = React.useMemo(function () {\n      return (Array.isArray(size) ? size : [size, size]).map(function (item) {\n        return getNumberSize(item);\n      });\n    }, [size]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    horizontalSize = _React$useMemo2[0],\n    verticalSize = _React$useMemo2[1];\n  var childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  var mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  var prefixCls = getPrefixCls('space', customizePrefixCls);\n  var cn = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-align-\").concat(mergedAlign), mergedAlign), _classNames), className);\n  var itemClassName = \"\".concat(prefixCls, \"-item\");\n  var marginDirection = directionConfig === 'rtl' ? 'marginLeft' : 'marginRight';\n  // Calculate latest one\n  var latestIndex = 0;\n  var nodes = childNodes.map(function (child, i) {\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    var key = child && child.key || \"\".concat(itemClassName, \"-\").concat(i);\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      direction: direction,\n      index: i,\n      marginDirection: marginDirection,\n      split: split,\n      wrap: wrap\n    }, child);\n  });\n  var spaceContext = React.useMemo(function () {\n    return {\n      horizontalSize: horizontalSize,\n      verticalSize: verticalSize,\n      latestIndex: latestIndex,\n      supportFlexGap: supportFlexGap\n    };\n  }, [horizontalSize, verticalSize, latestIndex, supportFlexGap]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  var gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n    // Patch for gap not support\n    if (!supportFlexGap) {\n      gapStyle.marginBottom = -verticalSize;\n    }\n  }\n  if (supportFlexGap) {\n    gapStyle.columnGap = horizontalSize;\n    gapStyle.rowGap = verticalSize;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: cn,\n    style: _extends(_extends({}, gapStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContext.Provider, {\n    value: spaceContext\n  }, nodes));\n};\nvar CompoundedSpace = Space;\nCompoundedSpace.Compact = Compact;\nexport default CompoundedSpace;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAO,IAAIC,YAAY,GAAG,aAAaL,KAAK,CAACM,aAAa,CAAC;EACzDC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC;AACF,IAAIC,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAGL,SAAS,CAACK,IAAI,CAAC,GAAGA,IAAI,IAAI,CAAC;AAC/D;AACA,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,WAAW;EACf,IAAIC,iBAAiB,GAAGpB,KAAK,CAACqB,UAAU,CAACpB,aAAa,CAAC;IACrDqB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,KAAK,GAAGH,iBAAiB,CAACG,KAAK;IAC/BC,eAAe,GAAGJ,iBAAiB,CAACK,SAAS;EAC/C,IAAIC,WAAW,GAAGR,KAAK,CAACF,IAAI;IAC1BA,IAAI,GAAGU,WAAW,KAAK,KAAK,CAAC,GAAG,CAACH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACP,IAAI,KAAK,OAAO,GAAGU,WAAW;IACnHC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,gBAAgB,GAAGZ,KAAK,CAACO,SAAS;IAClCA,SAAS,GAAGK,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEC,kBAAkB,GAAGb,KAAK,CAACc,SAAS;IACpCC,KAAK,GAAGf,KAAK,CAACe,KAAK;IACnBC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,WAAW,GAAGjB,KAAK,CAACkB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACnDE,UAAU,GAAGrD,MAAM,CAACkC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC5H,IAAIR,cAAc,GAAGR,iBAAiB,CAAC,CAAC;EACxC,IAAIoC,cAAc,GAAGtC,KAAK,CAACuC,OAAO,CAAC,YAAY;MAC3C,OAAO,CAACC,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC,EAAE0B,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrE,OAAO5B,aAAa,CAAC4B,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC3B,IAAI,CAAC,CAAC;IACV4B,eAAe,GAAG7D,cAAc,CAACuD,cAAc,EAAE,CAAC,CAAC;IACnD9B,cAAc,GAAGoC,eAAe,CAAC,CAAC,CAAC;IACnCnC,YAAY,GAAGmC,eAAe,CAAC,CAAC,CAAC;EACnC,IAAIC,UAAU,GAAG9C,OAAO,CAAC8B,QAAQ,EAAE;IACjCiB,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGpB,KAAK,KAAKqB,SAAS,IAAIvB,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAGE,KAAK;EACtF,IAAIK,SAAS,GAAGV,YAAY,CAAC,OAAO,EAAES,kBAAkB,CAAC;EACzD,IAAIkB,EAAE,GAAGnD,UAAU,CAACkC,SAAS,EAAE,EAAE,CAACkB,MAAM,CAAClB,SAAS,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACzB,SAAS,CAAC,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAErC,eAAe,CAACqC,WAAW,EAAE,EAAE,CAAC+B,MAAM,CAAClB,SAAS,EAAE,MAAM,CAAC,EAAER,eAAe,KAAK,KAAK,CAAC,EAAE1C,eAAe,CAACqC,WAAW,EAAE,EAAE,CAAC+B,MAAM,CAAClB,SAAS,EAAE,SAAS,CAAC,CAACkB,MAAM,CAACH,WAAW,CAAC,EAAEA,WAAW,CAAC,EAAE5B,WAAW,GAAGS,SAAS,CAAC;EAC9S,IAAIuB,aAAa,GAAG,EAAE,CAACD,MAAM,CAAClB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIoB,eAAe,GAAG5B,eAAe,KAAK,KAAK,GAAG,YAAY,GAAG,aAAa;EAC9E;EACA,IAAIjB,WAAW,GAAG,CAAC;EACnB,IAAI8C,KAAK,GAAGR,UAAU,CAACH,GAAG,CAAC,UAAUY,KAAK,EAAE3D,CAAC,EAAE;IAC7C,IAAI2D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKN,SAAS,EAAE;MACzCzC,WAAW,GAAGZ,CAAC;IACjB;IACA,IAAI4D,GAAG,GAAGD,KAAK,IAAIA,KAAK,CAACC,GAAG,IAAI,EAAE,CAACL,MAAM,CAACC,aAAa,EAAE,GAAG,CAAC,CAACD,MAAM,CAACvD,CAAC,CAAC;IACvE,OAAO,aAAaK,KAAK,CAACwD,aAAa,CAACrD,IAAI,EAAE;MAC5CyB,SAAS,EAAEuB,aAAa;MACxBI,GAAG,EAAEA,GAAG;MACR9B,SAAS,EAAEA,SAAS;MACpBgC,KAAK,EAAE9D,CAAC;MACRyD,eAAe,EAAEA,eAAe;MAChCnB,KAAK,EAAEA,KAAK;MACZG,IAAI,EAAEA;IACR,CAAC,EAAEkB,KAAK,CAAC;EACX,CAAC,CAAC;EACF,IAAII,YAAY,GAAG1D,KAAK,CAACuC,OAAO,CAAC,YAAY;IAC3C,OAAO;MACL/B,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1BF,WAAW,EAAEA,WAAW;MACxBG,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACF,cAAc,EAAEC,YAAY,EAAEF,WAAW,EAAEG,cAAc,CAAC,CAAC;EAC/D;EACA,IAAImC,UAAU,CAACjD,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAI+D,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIvB,IAAI,EAAE;IACRuB,QAAQ,CAACC,QAAQ,GAAG,MAAM;IAC1B;IACA,IAAI,CAAClD,cAAc,EAAE;MACnBiD,QAAQ,CAACE,YAAY,GAAG,CAACpD,YAAY;IACvC;EACF;EACA,IAAIC,cAAc,EAAE;IAClBiD,QAAQ,CAACG,SAAS,GAAGtD,cAAc;IACnCmD,QAAQ,CAACI,MAAM,GAAGtD,YAAY;EAChC;EACA,OAAO,aAAaT,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE3E,QAAQ,CAAC;IACtD+C,SAAS,EAAEqB,EAAE;IACbf,KAAK,EAAErD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,QAAQ,CAAC,EAAEzB,KAAK;EAC/C,CAAC,EAAEG,UAAU,CAAC,EAAE,aAAarC,KAAK,CAACwD,aAAa,CAACnD,YAAY,CAAC2D,QAAQ,EAAE;IACtEC,KAAK,EAAEP;EACT,CAAC,EAAEL,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,IAAIa,eAAe,GAAGjD,KAAK;AAC3BiD,eAAe,CAAC9D,OAAO,GAAGA,OAAO;AACjC,eAAe8D,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}