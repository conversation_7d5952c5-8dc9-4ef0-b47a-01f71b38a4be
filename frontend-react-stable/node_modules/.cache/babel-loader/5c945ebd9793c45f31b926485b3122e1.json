{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar calcThumbStyle = function calcThumbStyle(targetElement) {\n  return targetElement ? {\n    left: targetElement.offsetLeft,\n    right: targetElement.parentElement.clientWidth - targetElement.clientWidth - targetElement.offsetLeft,\n    width: targetElement.clientWidth\n  } : null;\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd,\n    direction = props.direction;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n\n  // =========================== Effect ===========================\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return (ele === null || ele === void 0 ? void 0 : ele.offsetParent) && ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev);\n      var calcNextStyle = calcThumbStyle(next);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]);\n  var thumbStart = React.useMemo(function () {\n    return direction === 'rtl' ? toPX(-(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.right)) : toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left);\n  }, [direction, prevStyle]);\n  var thumbActive = React.useMemo(function () {\n    return direction === 'rtl' ? toPX(-(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.right)) : toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left);\n  }, [direction, nextStyle]);\n\n  // =========================== Motion ===========================\n  var onAppearStart = function onAppearStart() {\n    return {\n      transform: \"translateX(var(--thumb-start-left))\",\n      width: \"var(--thumb-start-width)\"\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    return {\n      transform: \"translateX(var(--thumb-active-left))\",\n      width: \"var(--thumb-active-width)\"\n    };\n  };\n  var onAppearEnd = function onAppearEnd() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  };\n\n  // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onAppearEnd: onAppearEnd\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': thumbStart,\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': thumbActive,\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width)\n    });\n\n    // It's little ugly which should be refactor when @umi/test update to latest jsdom\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", motionProps);\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "classNames", "CSSMotion", "useLayoutEffect", "composeRef", "React", "calcThumbStyle", "targetElement", "left", "offsetLeft", "right", "parentElement", "clientWidth", "width", "toPX", "value", "undefined", "concat", "MotionThumb", "props", "prefixCls", "containerRef", "getValueIndex", "motionName", "onMotionStart", "onMotionEnd", "direction", "thumbRef", "useRef", "_React$useState", "useState", "_React$useState2", "prevValue", "setPrevValue", "findValueElement", "val", "_containerRef$current", "index", "ele", "current", "querySelectorAll", "offsetParent", "_React$useState3", "_React$useState4", "prevStyle", "setPrevStyle", "_React$useState5", "_React$useState6", "nextStyle", "setNextStyle", "prev", "next", "calcPrevStyle", "calcNextStyle", "thumbStart", "useMemo", "thumbActive", "onAppearStart", "transform", "onAppearActive", "onAppearEnd", "createElement", "visible", "motionAppear", "_ref", "ref", "motionClassName", "className", "motionStyle", "style", "mergedStyle", "motionProps", "process", "env", "NODE_ENV", "JSON", "stringify"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-segmented/es/MotionThumb.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar calcThumbStyle = function calcThumbStyle(targetElement) {\n  return targetElement ? {\n    left: targetElement.offsetLeft,\n    right: targetElement.parentElement.clientWidth - targetElement.clientWidth - targetElement.offsetLeft,\n    width: targetElement.clientWidth\n  } : null;\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd,\n    direction = props.direction;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n\n  // =========================== Effect ===========================\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return (ele === null || ele === void 0 ? void 0 : ele.offsetParent) && ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev);\n      var calcNextStyle = calcThumbStyle(next);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]);\n  var thumbStart = React.useMemo(function () {\n    return direction === 'rtl' ? toPX(-(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.right)) : toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left);\n  }, [direction, prevStyle]);\n  var thumbActive = React.useMemo(function () {\n    return direction === 'rtl' ? toPX(-(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.right)) : toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left);\n  }, [direction, nextStyle]);\n\n  // =========================== Motion ===========================\n  var onAppearStart = function onAppearStart() {\n    return {\n      transform: \"translateX(var(--thumb-start-left))\",\n      width: \"var(--thumb-start-width)\"\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    return {\n      transform: \"translateX(var(--thumb-active-left))\",\n      width: \"var(--thumb-active-width)\"\n    };\n  };\n  var onAppearEnd = function onAppearEnd() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  };\n\n  // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onAppearEnd: onAppearEnd\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': thumbStart,\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': thumbActive,\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width)\n    });\n\n    // It's little ugly which should be refactor when @umi/test update to latest jsdom\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", motionProps);\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,aAAa,EAAE;EAC1D,OAAOA,aAAa,GAAG;IACrBC,IAAI,EAAED,aAAa,CAACE,UAAU;IAC9BC,KAAK,EAAEH,aAAa,CAACI,aAAa,CAACC,WAAW,GAAGL,aAAa,CAACK,WAAW,GAAGL,aAAa,CAACE,UAAU;IACrGI,KAAK,EAAEN,aAAa,CAACK;EACvB,CAAC,GAAG,IAAI;AACV,CAAC;AACD,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAKC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACF,KAAK,EAAE,IAAI,CAAC,GAAGC,SAAS;AACjE,CAAC;AACD,eAAe,SAASE,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCN,KAAK,GAAGI,KAAK,CAACJ,KAAK;IACnBO,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC7B,IAAIC,QAAQ,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,eAAe,GAAGxB,KAAK,CAACyB,QAAQ,CAACf,KAAK,CAAC;IACzCgB,gBAAgB,GAAG/B,cAAc,CAAC6B,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEpC;EACA,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,GAAG,EAAE;IACpD,IAAIC,qBAAqB;IACzB,IAAIC,KAAK,GAAGf,aAAa,CAACa,GAAG,CAAC;IAC9B,IAAIG,GAAG,GAAG,CAACF,qBAAqB,GAAGf,YAAY,CAACkB,OAAO,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,gBAAgB,CAAC,GAAG,CAACvB,MAAM,CAACG,SAAS,EAAE,OAAO,CAAC,CAAC,CAACiB,KAAK,CAAC;IAC9L,OAAO,CAACC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,YAAY,KAAKH,GAAG;EAC5E,CAAC;EACD,IAAII,gBAAgB,GAAGrC,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;IACzCa,gBAAgB,GAAG3C,cAAc,CAAC0C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAGzC,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;IACzCiB,gBAAgB,GAAG/C,cAAc,CAAC8C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC5C,eAAe,CAAC,YAAY;IAC1B,IAAI6B,SAAS,KAAKjB,KAAK,EAAE;MACvB,IAAImC,IAAI,GAAGhB,gBAAgB,CAACF,SAAS,CAAC;MACtC,IAAImB,IAAI,GAAGjB,gBAAgB,CAACnB,KAAK,CAAC;MAClC,IAAIqC,aAAa,GAAG9C,cAAc,CAAC4C,IAAI,CAAC;MACxC,IAAIG,aAAa,GAAG/C,cAAc,CAAC6C,IAAI,CAAC;MACxClB,YAAY,CAAClB,KAAK,CAAC;MACnB8B,YAAY,CAACO,aAAa,CAAC;MAC3BH,YAAY,CAACI,aAAa,CAAC;MAC3B,IAAIH,IAAI,IAAIC,IAAI,EAAE;QAChB3B,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLC,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAACV,KAAK,CAAC,CAAC;EACX,IAAIuC,UAAU,GAAGjD,KAAK,CAACkD,OAAO,CAAC,YAAY;IACzC,OAAO7B,SAAS,KAAK,KAAK,GAAGZ,IAAI,CAAC,EAAE8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAClC,KAAK,CAAC,CAAC,GAAGI,IAAI,CAAC8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpC,IAAI,CAAC;EAC1L,CAAC,EAAE,CAACkB,SAAS,EAAEkB,SAAS,CAAC,CAAC;EAC1B,IAAIY,WAAW,GAAGnD,KAAK,CAACkD,OAAO,CAAC,YAAY;IAC1C,OAAO7B,SAAS,KAAK,KAAK,GAAGZ,IAAI,CAAC,EAAEkC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtC,KAAK,CAAC,CAAC,GAAGI,IAAI,CAACkC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACxC,IAAI,CAAC;EAC1L,CAAC,EAAE,CAACkB,SAAS,EAAEsB,SAAS,CAAC,CAAC;;EAE1B;EACA,IAAIS,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,OAAO;MACLC,SAAS,EAAE,qCAAqC;MAChD7C,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,IAAI8C,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAO;MACLD,SAAS,EAAE,sCAAsC;MACjD7C,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,IAAI+C,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCf,YAAY,CAAC,IAAI,CAAC;IAClBI,YAAY,CAAC,IAAI,CAAC;IAClBxB,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA;EACA,IAAI,CAACmB,SAAS,IAAI,CAACI,SAAS,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,OAAO,aAAa3C,KAAK,CAACwD,aAAa,CAAC3D,SAAS,EAAE;IACjD4D,OAAO,EAAE,IAAI;IACbvC,UAAU,EAAEA,UAAU;IACtBwC,YAAY,EAAE,IAAI;IAClBN,aAAa,EAAEA,aAAa;IAC5BE,cAAc,EAAEA,cAAc;IAC9BC,WAAW,EAAEA;EACf,CAAC,EAAE,UAAUI,IAAI,EAAEC,GAAG,EAAE;IACtB,IAAIC,eAAe,GAAGF,IAAI,CAACG,SAAS;MAClCC,WAAW,GAAGJ,IAAI,CAACK,KAAK;IAC1B,IAAIC,WAAW,GAAGvE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MAClE,oBAAoB,EAAEd,UAAU;MAChC,qBAAqB,EAAExC,IAAI,CAAC8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/B,KAAK,CAAC;MAClG,qBAAqB,EAAE2C,WAAW;MAClC,sBAAsB,EAAE1C,IAAI,CAACkC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACnC,KAAK;IACpG,CAAC,CAAC;;IAEF;IACA,IAAI0D,WAAW,GAAG;MAChBN,GAAG,EAAE7D,UAAU,CAACuB,QAAQ,EAAEsC,GAAG,CAAC;MAC9BI,KAAK,EAAEC,WAAW;MAClBH,SAAS,EAAElE,UAAU,CAAC,EAAE,CAACgB,MAAM,CAACG,SAAS,EAAE,QAAQ,CAAC,EAAE8C,eAAe;IACvE,CAAC;IACD,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCH,WAAW,CAAC,iBAAiB,CAAC,GAAGI,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC;IAC9D;IACA,OAAO,aAAajE,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAEU,WAAW,CAAC;EAC7D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}