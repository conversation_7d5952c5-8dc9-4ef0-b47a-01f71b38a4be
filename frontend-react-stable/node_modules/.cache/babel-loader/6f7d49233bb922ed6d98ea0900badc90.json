{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/* eslint-disable jsx-a11y/heading-has-content */\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Title = function Title(_ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    width = _ref.width,\n    style = _ref.style;\n  return /*#__PURE__*/React.createElement(\"h3\", {\n    className: classNames(prefixCls, className),\n    style: _extends({\n      width: width\n    }, style)\n  });\n};\nexport default Title;", "map": {"version": 3, "names": ["_extends", "classNames", "React", "Title", "_ref", "prefixCls", "className", "width", "style", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Title.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n/* eslint-disable jsx-a11y/heading-has-content */\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Title = function Title(_ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    width = _ref.width,\n    style = _ref.style;\n  return /*#__PURE__*/React.createElement(\"h3\", {\n    className: classNames(prefixCls, className),\n    style: _extends({\n      width: width\n    }, style)\n  });\n};\nexport default Title;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,OAAO,aAAaN,KAAK,CAACO,aAAa,CAAC,IAAI,EAAE;IAC5CH,SAAS,EAAEL,UAAU,CAACI,SAAS,EAAEC,SAAS,CAAC;IAC3CE,KAAK,EAAER,QAAQ,CAAC;MACdO,KAAK,EAAEA;IACT,CAAC,EAAEC,KAAK;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}