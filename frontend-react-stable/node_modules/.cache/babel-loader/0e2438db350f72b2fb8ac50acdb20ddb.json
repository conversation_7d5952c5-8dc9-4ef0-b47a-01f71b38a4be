{"ast": null, "code": "import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen, customizedTrigger) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen,\n    customizedTrigger: customizedTrigger\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var _propsRef$current;\n      // If trigger is customized, <PERSON>gger will take control of popupVisible\n      if ((_propsRef$current = propsRef.current) !== null && _propsRef$current !== void 0 && _propsRef$current.customizedTrigger) {\n        return;\n      }\n      var target = event.target;\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["React", "useSelectTriggerControl", "elements", "open", "triggerOpen", "customizedTrigger", "propsRef", "useRef", "current", "useEffect", "onGlobalMouseDown", "event", "_propsRef$current", "target", "shadowRoot", "composed", "<PERSON><PERSON><PERSON>", "filter", "element", "every", "contains", "window", "addEventListener", "removeEventListener"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/hooks/useSelectTriggerControl.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen, customizedTrigger) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen,\n    customizedTrigger: customizedTrigger\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var _propsRef$current;\n      // If trigger is customized, <PERSON>gger will take control of popupVisible\n      if ((_propsRef$current = propsRef.current) !== null && _propsRef$current !== void 0 && _propsRef$current.customizedTrigger) {\n        return;\n      }\n      var target = event.target;\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,uBAAuBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;EAC9F,IAAIC,QAAQ,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACjCD,QAAQ,CAACE,OAAO,GAAG;IACjBL,IAAI,EAAEA,IAAI;IACVC,WAAW,EAAEA,WAAW;IACxBC,iBAAiB,EAAEA;EACrB,CAAC;EACDL,KAAK,CAACS,SAAS,CAAC,YAAY;IAC1B,SAASC,iBAAiBA,CAACC,KAAK,EAAE;MAChC,IAAIC,iBAAiB;MACrB;MACA,IAAI,CAACA,iBAAiB,GAAGN,QAAQ,CAACE,OAAO,MAAM,IAAI,IAAII,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACP,iBAAiB,EAAE;QAC1H;MACF;MACA,IAAIQ,MAAM,GAAGF,KAAK,CAACE,MAAM;MACzB,IAAIA,MAAM,CAACC,UAAU,IAAIH,KAAK,CAACI,QAAQ,EAAE;QACvCF,MAAM,GAAGF,KAAK,CAACK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIH,MAAM;MAC5C;MACA,IAAIP,QAAQ,CAACE,OAAO,CAACL,IAAI,IAAID,QAAQ,CAAC,CAAC,CAACe,MAAM,CAAC,UAAUC,OAAO,EAAE;QAChE,OAAOA,OAAO;MAChB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUD,OAAO,EAAE;QAC1B,OAAO,CAACA,OAAO,CAACE,QAAQ,CAACP,MAAM,CAAC,IAAIK,OAAO,KAAKL,MAAM;MACxD,CAAC,CAAC,EAAE;QACF;QACAP,QAAQ,CAACE,OAAO,CAACJ,WAAW,CAAC,KAAK,CAAC;MACrC;IACF;IACAiB,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEZ,iBAAiB,CAAC;IACvD,OAAO,YAAY;MACjB,OAAOW,MAAM,CAACE,mBAAmB,CAAC,WAAW,EAAEb,iBAAiB,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module"}