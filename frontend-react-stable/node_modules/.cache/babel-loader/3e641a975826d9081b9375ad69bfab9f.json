{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.calendar = calendar;\nexports.default = time;\nvar _index = require(\"../../../lib-vendor/d3-time/src/index.js\");\nvar _index2 = require(\"../../../lib-vendor/d3-time-format/src/index.js\");\nvar _continuous = _interopRequireWildcard(require(\"./continuous.js\"));\nvar _init = require(\"./init.js\");\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction date(t) {\n  return new Date(t);\n}\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\nfunction calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = (0, _continuous.default)(),\n    invert = scale.invert,\n    domain = scale.domain;\n  var formatMillisecond = format(\".%L\"),\n    formatSecond = format(\":%S\"),\n    formatMinute = format(\"%I:%M\"),\n    formatHour = format(\"%I %p\"),\n    formatDay = format(\"%a %d\"),\n    formatWeek = format(\"%b %d\"),\n    formatMonth = format(\"%B\"),\n    formatYear = format(\"%Y\");\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond : minute(date) < date ? formatSecond : hour(date) < date ? formatMinute : day(date) < date ? formatHour : month(date) < date ? week(date) < date ? formatDay : formatWeek : year(date) < date ? formatMonth : formatYear)(date);\n  }\n  scale.invert = function (y) {\n    return new Date(invert(y));\n  };\n  scale.domain = function (_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n  scale.ticks = function (interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n  scale.tickFormat = function (count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n  scale.nice = function (interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain((0, _nice.default)(d, interval)) : scale;\n  };\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n  return scale;\n}\nfunction time() {\n  return _init.initRange.apply(calendar(_index.timeTicks, _index.timeTickInterval, _index.timeYear, _index.timeMonth, _index.timeWeek, _index.timeDay, _index.timeHour, _index.timeMinute, _index.timeSecond, _index2.timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "calendar", "default", "time", "_index", "require", "_index2", "_continuous", "_interopRequireWildcard", "_init", "_nice", "_interopRequireDefault", "obj", "__esModule", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "date", "t", "Date", "number", "ticks", "tickInterval", "year", "month", "week", "day", "hour", "minute", "second", "format", "scale", "invert", "domain", "formatMillisecond", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "formatMonth", "formatYear", "tickFormat", "y", "_", "arguments", "length", "Array", "from", "map", "interval", "d", "count", "specifier", "nice", "range", "copy", "initRange", "apply", "timeTicks", "timeTickInterval", "timeYear", "timeMonth", "timeWeek", "timeDay", "timeHour", "timeMinute", "timeSecond", "timeFormat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/time.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.calendar = calendar;\nexports.default = time;\n\nvar _index = require(\"../../../lib-vendor/d3-time/src/index.js\");\n\nvar _index2 = require(\"../../../lib-vendor/d3-time-format/src/index.js\");\n\nvar _continuous = _interopRequireWildcard(require(\"./continuous.js\"));\n\nvar _init = require(\"./init.js\");\n\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nfunction calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = (0, _continuous.default)(),\n      invert = scale.invert,\n      domain = scale.domain;\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond : minute(date) < date ? formatSecond : hour(date) < date ? formatMinute : day(date) < date ? formatHour : month(date) < date ? week(date) < date ? formatDay : formatWeek : year(date) < date ? formatMonth : formatYear)(date);\n  }\n\n  scale.invert = function (y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function (_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function (interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function (count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function (interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain((0, _nice.default)(d, interval)) : scale;\n  };\n\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nfunction time() {\n  return _init.initRange.apply(calendar(_index.timeTicks, _index.timeTickInterval, _index.timeYear, _index.timeMonth, _index.timeWeek, _index.timeDay, _index.timeHour, _index.timeMinute, _index.timeSecond, _index2.timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3BF,OAAO,CAACG,OAAO,GAAGC,IAAI;AAEtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,0CAA0C,CAAC;AAEhE,IAAIC,OAAO,GAAGD,OAAO,CAAC,iDAAiD,CAAC;AAExE,IAAIE,WAAW,GAAGC,uBAAuB,CAACH,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAErE,IAAII,KAAK,GAAGJ,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIK,KAAK,GAAGC,sBAAsB,CAACN,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASM,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,SAASE,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASP,uBAAuBA,CAACI,GAAG,EAAEG,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAIH,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEV,OAAO,EAAEU;IAAI,CAAC;EAAE;EAAE,IAAIO,KAAK,GAAGL,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAII,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACR,GAAG,CAAC,EAAE;IAAE,OAAOO,KAAK,CAACE,GAAG,CAACT,GAAG,CAAC;EAAE;EAAE,IAAIU,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAG1B,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC2B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIb,GAAG,EAAE;IAAE,IAAIa,GAAG,KAAK,SAAS,IAAI5B,MAAM,CAAC6B,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChB,GAAG,EAAEa,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAG1B,MAAM,CAAC2B,wBAAwB,CAACZ,GAAG,EAAEa,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEjC,MAAM,CAACC,cAAc,CAACwB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGb,GAAG,CAACa,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACpB,OAAO,GAAGU,GAAG;EAAE,IAAIO,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAAClB,GAAG,EAAEU,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASS,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,IAAIC,IAAI,CAACD,CAAC,CAAC;AACpB;AAEA,SAASE,MAAMA,CAACF,CAAC,EAAE;EACjB,OAAOA,CAAC,YAAYC,IAAI,GAAG,CAACD,CAAC,GAAG,CAAC,IAAIC,IAAI,CAAC,CAACD,CAAC,CAAC;AAC/C;AAEA,SAAS/B,QAAQA,CAACkC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAC3F,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAEtC,WAAW,CAACL,OAAO,EAAE,CAAC;IAClC4C,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACzB,IAAIC,iBAAiB,GAAGJ,MAAM,CAAC,KAAK,CAAC;IACjCK,YAAY,GAAGL,MAAM,CAAC,KAAK,CAAC;IAC5BM,YAAY,GAAGN,MAAM,CAAC,OAAO,CAAC;IAC9BO,UAAU,GAAGP,MAAM,CAAC,OAAO,CAAC;IAC5BQ,SAAS,GAAGR,MAAM,CAAC,OAAO,CAAC;IAC3BS,UAAU,GAAGT,MAAM,CAAC,OAAO,CAAC;IAC5BU,WAAW,GAAGV,MAAM,CAAC,IAAI,CAAC;IAC1BW,UAAU,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE7B,SAASY,UAAUA,CAACzB,IAAI,EAAE;IACxB,OAAO,CAACY,MAAM,CAACZ,IAAI,CAAC,GAAGA,IAAI,GAAGiB,iBAAiB,GAAGN,MAAM,CAACX,IAAI,CAAC,GAAGA,IAAI,GAAGkB,YAAY,GAAGR,IAAI,CAACV,IAAI,CAAC,GAAGA,IAAI,GAAGmB,YAAY,GAAGV,GAAG,CAACT,IAAI,CAAC,GAAGA,IAAI,GAAGoB,UAAU,GAAGb,KAAK,CAACP,IAAI,CAAC,GAAGA,IAAI,GAAGQ,IAAI,CAACR,IAAI,CAAC,GAAGA,IAAI,GAAGqB,SAAS,GAAGC,UAAU,GAAGhB,IAAI,CAACN,IAAI,CAAC,GAAGA,IAAI,GAAGuB,WAAW,GAAGC,UAAU,EAAExB,IAAI,CAAC;EACjR;EAEAc,KAAK,CAACC,MAAM,GAAG,UAAUW,CAAC,EAAE;IAC1B,OAAO,IAAIxB,IAAI,CAACa,MAAM,CAACW,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDZ,KAAK,CAACE,MAAM,GAAG,UAAUW,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,GAAGb,MAAM,CAACc,KAAK,CAACC,IAAI,CAACJ,CAAC,EAAExB,MAAM,CAAC,CAAC,GAAGa,MAAM,CAAC,CAAC,CAACgB,GAAG,CAAChC,IAAI,CAAC;EAC9E,CAAC;EAEDc,KAAK,CAACV,KAAK,GAAG,UAAU6B,QAAQ,EAAE;IAChC,IAAIC,CAAC,GAAGlB,MAAM,CAAC,CAAC;IAChB,OAAOZ,KAAK,CAAC8B,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACL,MAAM,GAAG,CAAC,CAAC,EAAEI,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC;EACvE,CAAC;EAEDnB,KAAK,CAACW,UAAU,GAAG,UAAUU,KAAK,EAAEC,SAAS,EAAE;IAC7C,OAAOA,SAAS,IAAI,IAAI,GAAGX,UAAU,GAAGZ,MAAM,CAACuB,SAAS,CAAC;EAC3D,CAAC;EAEDtB,KAAK,CAACuB,IAAI,GAAG,UAAUJ,QAAQ,EAAE;IAC/B,IAAIC,CAAC,GAAGlB,MAAM,CAAC,CAAC;IAChB,IAAI,CAACiB,QAAQ,IAAI,OAAOA,QAAQ,CAACK,KAAK,KAAK,UAAU,EAAEL,QAAQ,GAAG5B,YAAY,CAAC6B,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACL,MAAM,GAAG,CAAC,CAAC,EAAEI,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC;IACvI,OAAOA,QAAQ,GAAGjB,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,CAACR,OAAO,EAAE+D,CAAC,EAAED,QAAQ,CAAC,CAAC,GAAGnB,KAAK;EACnE,CAAC;EAEDA,KAAK,CAACyB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAE/D,WAAW,CAAC+D,IAAI,EAAEzB,KAAK,EAAE5C,QAAQ,CAACkC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;EAC1H,CAAC;EAED,OAAOC,KAAK;AACd;AAEA,SAAS1C,IAAIA,CAAA,EAAG;EACd,OAAOM,KAAK,CAAC8D,SAAS,CAACC,KAAK,CAACvE,QAAQ,CAACG,MAAM,CAACqE,SAAS,EAAErE,MAAM,CAACsE,gBAAgB,EAAEtE,MAAM,CAACuE,QAAQ,EAAEvE,MAAM,CAACwE,SAAS,EAAExE,MAAM,CAACyE,QAAQ,EAAEzE,MAAM,CAAC0E,OAAO,EAAE1E,MAAM,CAAC2E,QAAQ,EAAE3E,MAAM,CAAC4E,UAAU,EAAE5E,MAAM,CAAC6E,UAAU,EAAE3E,OAAO,CAAC4E,UAAU,CAAC,CAACnC,MAAM,CAAC,CAAC,IAAId,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE0B,SAAS,CAAC;AAClS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}