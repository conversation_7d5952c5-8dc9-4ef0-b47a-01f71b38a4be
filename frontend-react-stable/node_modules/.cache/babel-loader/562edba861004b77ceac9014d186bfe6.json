{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nvar FormContext = /*#__PURE__*/React.createContext({\n  triggerFormChange: function triggerFormChange() {},\n  triggerFormFinish: function triggerFormFinish() {},\n  registerForm: function registerForm() {},\n  unregisterForm: function unregisterForm() {}\n});\nvar FormProvider = function FormProvider(_ref) {\n  var validateMessages = _ref.validateMessages,\n    onFormChange = _ref.onFormChange,\n    onFormFinish = _ref.onFormFinish,\n    children = _ref.children;\n  var formContext = React.useContext(FormContext);\n  var formsRef = React.useRef({});\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: _objectSpread(_objectSpread({}, formContext), {}, {\n      validateMessages: _objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages),\n      // =========================================================\n      // =                  Global Form Control                  =\n      // =========================================================\n      triggerFormChange: function triggerFormChange(name, changedFields) {\n        if (onFormChange) {\n          onFormChange(name, {\n            changedFields: changedFields,\n            forms: formsRef.current\n          });\n        }\n        formContext.triggerFormChange(name, changedFields);\n      },\n      triggerFormFinish: function triggerFormFinish(name, values) {\n        if (onFormFinish) {\n          onFormFinish(name, {\n            values: values,\n            forms: formsRef.current\n          });\n        }\n        formContext.triggerFormFinish(name, values);\n      },\n      registerForm: function registerForm(name, form) {\n        if (name) {\n          formsRef.current = _objectSpread(_objectSpread({}, formsRef.current), {}, _defineProperty({}, name, form));\n        }\n        formContext.registerForm(name, form);\n      },\n      unregisterForm: function unregisterForm(name) {\n        var newForms = _objectSpread({}, formsRef.current);\n        delete newForms[name];\n        formsRef.current = newForms;\n        formContext.unregisterForm(name);\n      }\n    })\n  }, children);\n};\nexport { FormProvider };\nexport default FormContext;", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "FormContext", "createContext", "triggerForm<PERSON>hange", "triggerFormFinish", "registerForm", "unregisterForm", "FormProvider", "_ref", "validateMessages", "onFormChange", "onFormFinish", "children", "formContext", "useContext", "formsRef", "useRef", "createElement", "Provider", "value", "name", "changed<PERSON>ields", "forms", "current", "values", "form", "newForms"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/FormContext.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nvar FormContext = /*#__PURE__*/React.createContext({\n  triggerFormChange: function triggerFormChange() {},\n  triggerFormFinish: function triggerFormFinish() {},\n  registerForm: function registerForm() {},\n  unregisterForm: function unregisterForm() {}\n});\nvar FormProvider = function FormProvider(_ref) {\n  var validateMessages = _ref.validateMessages,\n    onFormChange = _ref.onFormChange,\n    onFormFinish = _ref.onFormFinish,\n    children = _ref.children;\n  var formContext = React.useContext(FormContext);\n  var formsRef = React.useRef({});\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: _objectSpread(_objectSpread({}, formContext), {}, {\n      validateMessages: _objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages),\n      // =========================================================\n      // =                  Global Form Control                  =\n      // =========================================================\n      triggerFormChange: function triggerFormChange(name, changedFields) {\n        if (onFormChange) {\n          onFormChange(name, {\n            changedFields: changedFields,\n            forms: formsRef.current\n          });\n        }\n        formContext.triggerFormChange(name, changedFields);\n      },\n      triggerFormFinish: function triggerFormFinish(name, values) {\n        if (onFormFinish) {\n          onFormFinish(name, {\n            values: values,\n            forms: formsRef.current\n          });\n        }\n        formContext.triggerFormFinish(name, values);\n      },\n      registerForm: function registerForm(name, form) {\n        if (name) {\n          formsRef.current = _objectSpread(_objectSpread({}, formsRef.current), {}, _defineProperty({}, name, form));\n        }\n        formContext.registerForm(name, form);\n      },\n      unregisterForm: function unregisterForm(name) {\n        var newForms = _objectSpread({}, formsRef.current);\n        delete newForms[name];\n        formsRef.current = newForms;\n        formContext.unregisterForm(name);\n      }\n    })\n  }, children);\n};\nexport { FormProvider };\nexport default FormContext;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACjDC,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG,CAAC,CAAC;EAClDC,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG,CAAC,CAAC;EAClDC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG,CAAC,CAAC;EACxCC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC;AAC7C,CAAC,CAAC;AACF,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,IAAIC,gBAAgB,GAAGD,IAAI,CAACC,gBAAgB;IAC1CC,YAAY,GAAGF,IAAI,CAACE,YAAY;IAChCC,YAAY,GAAGH,IAAI,CAACG,YAAY;IAChCC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC1B,IAAIC,WAAW,GAAGb,KAAK,CAACc,UAAU,CAACb,WAAW,CAAC;EAC/C,IAAIc,QAAQ,GAAGf,KAAK,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,OAAO,aAAahB,KAAK,CAACiB,aAAa,CAAChB,WAAW,CAACiB,QAAQ,EAAE;IAC5DC,KAAK,EAAEpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEc,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MACvDJ,gBAAgB,EAAEV,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEc,WAAW,CAACJ,gBAAgB,CAAC,EAAEA,gBAAgB,CAAC;MAClG;MACA;MACA;MACAN,iBAAiB,EAAE,SAASA,iBAAiBA,CAACiB,IAAI,EAAEC,aAAa,EAAE;QACjE,IAAIX,YAAY,EAAE;UAChBA,YAAY,CAACU,IAAI,EAAE;YACjBC,aAAa,EAAEA,aAAa;YAC5BC,KAAK,EAAEP,QAAQ,CAACQ;UAClB,CAAC,CAAC;QACJ;QACAV,WAAW,CAACV,iBAAiB,CAACiB,IAAI,EAAEC,aAAa,CAAC;MACpD,CAAC;MACDjB,iBAAiB,EAAE,SAASA,iBAAiBA,CAACgB,IAAI,EAAEI,MAAM,EAAE;QAC1D,IAAIb,YAAY,EAAE;UAChBA,YAAY,CAACS,IAAI,EAAE;YACjBI,MAAM,EAAEA,MAAM;YACdF,KAAK,EAAEP,QAAQ,CAACQ;UAClB,CAAC,CAAC;QACJ;QACAV,WAAW,CAACT,iBAAiB,CAACgB,IAAI,EAAEI,MAAM,CAAC;MAC7C,CAAC;MACDnB,YAAY,EAAE,SAASA,YAAYA,CAACe,IAAI,EAAEK,IAAI,EAAE;QAC9C,IAAIL,IAAI,EAAE;UACRL,QAAQ,CAACQ,OAAO,GAAGxB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,QAAQ,CAACQ,OAAO,CAAC,EAAE,CAAC,CAAC,EAAEzB,eAAe,CAAC,CAAC,CAAC,EAAEsB,IAAI,EAAEK,IAAI,CAAC,CAAC;QAC5G;QACAZ,WAAW,CAACR,YAAY,CAACe,IAAI,EAAEK,IAAI,CAAC;MACtC,CAAC;MACDnB,cAAc,EAAE,SAASA,cAAcA,CAACc,IAAI,EAAE;QAC5C,IAAIM,QAAQ,GAAG3B,aAAa,CAAC,CAAC,CAAC,EAAEgB,QAAQ,CAACQ,OAAO,CAAC;QAClD,OAAOG,QAAQ,CAACN,IAAI,CAAC;QACrBL,QAAQ,CAACQ,OAAO,GAAGG,QAAQ;QAC3Bb,WAAW,CAACP,cAAc,CAACc,IAAI,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAER,QAAQ,CAAC;AACd,CAAC;AACD,SAASL,YAAY;AACrB,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}