{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n}", "map": {"version": 3, "names": ["warning", "parseWidthHeight", "value", "String", "Number", "warn<PERSON>heck", "props"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-drawer/es/util.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,KAAKA,KAAK,EAAE;IAChEF,OAAO,CAAC,KAAK,EAAE,gFAAgF,CAAC;IAChG,OAAOI,MAAM,CAACF,KAAK,CAAC;EACtB;EACA,OAAOA,KAAK;AACd;AACA,OAAO,SAASG,SAASA,CAACC,KAAK,EAAE;EAC/BN,OAAO,CAAC,EAAE,kBAAkB,IAAIM,KAAK,CAAC,EAAE,oEAAoE,CAAC;AAC/G", "ignoreList": []}, "metadata": {}, "sourceType": "module"}