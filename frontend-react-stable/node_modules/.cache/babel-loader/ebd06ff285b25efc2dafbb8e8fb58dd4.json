{"ast": null, "code": "import Dragger from './Dragger';\nimport InternalUpload, { LIST_IGNORE } from './Upload';\nvar Upload = InternalUpload;\nUpload.Dragger = Dragger;\nUpload.LIST_IGNORE = LIST_IGNORE;\nexport default Upload;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "InternalUpload", "LIST_IGNORE", "Upload"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/upload/index.js"], "sourcesContent": ["import Dragger from './Dragger';\nimport InternalUpload, { LIST_IGNORE } from './Upload';\nvar Upload = InternalUpload;\nUpload.Dragger = Dragger;\nUpload.LIST_IGNORE = LIST_IGNORE;\nexport default Upload;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,IAAIC,WAAW,QAAQ,UAAU;AACtD,IAAIC,MAAM,GAAGF,cAAc;AAC3BE,MAAM,CAACH,OAAO,GAAGA,OAAO;AACxBG,MAAM,CAACD,WAAW,GAAGA,WAAW;AAChC,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}