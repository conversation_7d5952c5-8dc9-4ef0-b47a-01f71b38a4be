{"ast": null, "code": "import { useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { ReactReduxContext } from '../components/Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStoreWithSelector = notInitialized;\nexport const initializeUseSelector = fn => {\n  useSyncExternalStoreWithSelector = fn;\n};\nconst refEquality = (a, b) => a === b;\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\nexport function createSelectorHook(context = ReactReduxContext) {\n  const useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : () => useContext(context);\n  return function useSelector(selector, equalityFn = refEquality) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`);\n      }\n      if (typeof selector !== 'function') {\n        throw new Error(`You must pass a function as a selector to useSelector`);\n      }\n      if (typeof equalityFn !== 'function') {\n        throw new Error(`You must pass a function as an equality function to useSelector`);\n      }\n    }\n    const {\n      store,\n      subscription,\n      getServerState\n    } = useReduxContext();\n    const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, selector, equalityFn);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport const useSelector = /*#__PURE__*/createSelectorHook();", "map": {"version": 3, "names": ["useContext", "useDebugValue", "useReduxContext", "useDefaultReduxContext", "ReactReduxContext", "notInitialized", "useSyncExternalStoreWithSelector", "initializeUseSelector", "fn", "refEquality", "a", "b", "createSelectorHook", "context", "useSelector", "selector", "equalityFn", "process", "env", "NODE_ENV", "Error", "store", "subscription", "getServerState", "selectedState", "addNestedSub", "getState"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/hooks/useSelector.js"], "sourcesContent": ["import { useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { ReactReduxContext } from '../components/Context';\nimport { notInitialized } from '../utils/useSyncExternalStore';\nlet useSyncExternalStoreWithSelector = notInitialized;\nexport const initializeUseSelector = fn => {\n  useSyncExternalStoreWithSelector = fn;\n};\n\nconst refEquality = (a, b) => a === b;\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\n\nexport function createSelectorHook(context = ReactReduxContext) {\n  const useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : () => useContext(context);\n  return function useSelector(selector, equalityFn = refEquality) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`);\n      }\n\n      if (typeof selector !== 'function') {\n        throw new Error(`You must pass a function as a selector to useSelector`);\n      }\n\n      if (typeof equalityFn !== 'function') {\n        throw new Error(`You must pass a function as an equality function to useSelector`);\n      }\n    }\n\n    const {\n      store,\n      subscription,\n      getServerState\n    } = useReduxContext();\n    const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, selector, equalityFn);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport const useSelector = /*#__PURE__*/createSelectorHook();"], "mappings": "AAAA,SAASA,UAAU,EAAEC,aAAa,QAAQ,OAAO;AACjD,SAASC,eAAe,IAAIC,sBAAsB,QAAQ,mBAAmB;AAC7E,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,IAAIC,gCAAgC,GAAGD,cAAc;AACrD,OAAO,MAAME,qBAAqB,GAAGC,EAAE,IAAI;EACzCF,gCAAgC,GAAGE,EAAE;AACvC,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,GAAGT,iBAAiB,EAAE;EAC9D,MAAMF,eAAe,GAAGW,OAAO,KAAKT,iBAAiB,GAAGD,sBAAsB,GAAG,MAAMH,UAAU,CAACa,OAAO,CAAC;EAC1G,OAAO,SAASC,WAAWA,CAACC,QAAQ,EAAEC,UAAU,GAAGP,WAAW,EAAE;IAC9D,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACJ,QAAQ,EAAE;QACb,MAAM,IAAIK,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MAEA,IAAI,OAAOL,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIK,KAAK,CAAC,uDAAuD,CAAC;MAC1E;MAEA,IAAI,OAAOJ,UAAU,KAAK,UAAU,EAAE;QACpC,MAAM,IAAII,KAAK,CAAC,iEAAiE,CAAC;MACpF;IACF;IAEA,MAAM;MACJC,KAAK;MACLC,YAAY;MACZC;IACF,CAAC,GAAGrB,eAAe,CAAC,CAAC;IACrB,MAAMsB,aAAa,GAAGlB,gCAAgC,CAACgB,YAAY,CAACG,YAAY,EAAEJ,KAAK,CAACK,QAAQ,EAAEH,cAAc,IAAIF,KAAK,CAACK,QAAQ,EAAEX,QAAQ,EAAEC,UAAU,CAAC;IACzJf,aAAa,CAACuB,aAAa,CAAC;IAC5B,OAAOA,aAAa;EACtB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMV,WAAW,GAAG,aAAaF,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}