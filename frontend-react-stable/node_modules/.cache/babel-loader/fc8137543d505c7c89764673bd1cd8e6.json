{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from './context/MenuContext';\nimport { useMeasure } from './context/PathContext';\nexport default function Divider(_ref) {\n  var className = _ref.className,\n    style = _ref.style;\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var measure = useMeasure();\n  if (measure) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: classNames(\"\".concat(prefixCls, \"-item-divider\"), className),\n    style: style\n  });\n}", "map": {"version": 3, "names": ["React", "classNames", "MenuContext", "useMeasure", "Divider", "_ref", "className", "style", "_React$useContext", "useContext", "prefixCls", "measure", "createElement", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/Divider.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from './context/MenuContext';\nimport { useMeasure } from './context/PathContext';\nexport default function Divider(_ref) {\n  var className = _ref.className,\n      style = _ref.style;\n\n  var _React$useContext = React.useContext(MenuContext),\n      prefixCls = _React$useContext.prefixCls;\n\n  var measure = useMeasure();\n\n  if (measure) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: classNames(\"\".concat(prefixCls, \"-item-divider\"), className),\n    style: style\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,eAAe,SAASC,OAAOA,CAACC,IAAI,EAAE;EACpC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;EAEtB,IAAIC,iBAAiB,GAAGR,KAAK,CAACS,UAAU,CAACP,WAAW,CAAC;IACjDQ,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,OAAO,GAAGR,UAAU,CAAC,CAAC;EAE1B,IAAIQ,OAAO,EAAE;IACX,OAAO,IAAI;EACb;EAEA,OAAO,aAAaX,KAAK,CAACY,aAAa,CAAC,IAAI,EAAE;IAC5CN,SAAS,EAAEL,UAAU,CAAC,EAAE,CAACY,MAAM,CAACH,SAAS,EAAE,eAAe,CAAC,EAAEJ,SAAS,CAAC;IACvEC,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}