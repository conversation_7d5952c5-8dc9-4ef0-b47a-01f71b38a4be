{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = median;\nvar _quantile = _interopRequireDefault(require(\"./quantile.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction median(values, valueof) {\n  return (0, _quantile.default)(values, 0.5, valueof);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "median", "_quantile", "_interopRequireDefault", "require", "obj", "__esModule", "values", "valueof"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/median.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = median;\n\nvar _quantile = _interopRequireDefault(require(\"./quantile.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction median(values, valueof) {\n  return (0, _quantile.default)(values, 0.5, valueof);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,MAAMA,CAACM,MAAM,EAAEC,OAAO,EAAE;EAC/B,OAAO,CAAC,CAAC,EAAEN,SAAS,CAACF,OAAO,EAAEO,MAAM,EAAE,GAAG,EAAEC,OAAO,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}