{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = disjoint;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction disjoint(values, other) {\n  const iterator = other[Symbol.iterator](),\n    set = new _index.InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "disjoint", "_index", "require", "values", "other", "iterator", "Symbol", "set", "InternSet", "v", "has", "done", "next", "is", "add"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/disjoint.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = disjoint;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction disjoint(values, other) {\n  const iterator = other[Symbol.iterator](),\n        set = new _index.InternSet();\n\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n\n    while (({\n      value,\n      done\n    } = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n\n  return true;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,QAAQA,CAACG,MAAM,EAAEC,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;IACnCE,GAAG,GAAG,IAAIN,MAAM,CAACO,SAAS,CAAC,CAAC;EAElC,KAAK,MAAMC,CAAC,IAAIN,MAAM,EAAE;IACtB,IAAII,GAAG,CAACG,GAAG,CAACD,CAAC,CAAC,EAAE,OAAO,KAAK;IAC5B,IAAIX,KAAK,EAAEa,IAAI;IAEf,OAAQ;MACNb,KAAK;MACLa;IACF,CAAC,GAAGN,QAAQ,CAACO,IAAI,CAAC,CAAC,EAAG;MACpB,IAAID,IAAI,EAAE;MACV,IAAIhB,MAAM,CAACkB,EAAE,CAACJ,CAAC,EAAEX,KAAK,CAAC,EAAE,OAAO,KAAK;MACrCS,GAAG,CAACO,GAAG,CAAChB,KAAK,CAAC;IAChB;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script"}