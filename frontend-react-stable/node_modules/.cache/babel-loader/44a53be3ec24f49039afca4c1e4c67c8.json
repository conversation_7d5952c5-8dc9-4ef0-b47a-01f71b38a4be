{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && function () {\n  function is(x, y) {\n    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n  }\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n  var React = require(\"react\"),\n    shim = require(\"use-sync-external-store/shim\"),\n    objectIs = \"function\" === typeof Object.is ? Object.is : is,\n    useSyncExternalStore = shim.useSyncExternalStore,\n    useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue;\n  exports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n    var instRef = useRef(null);\n    if (null === instRef.current) {\n      var inst = {\n        hasValue: !1,\n        value: null\n      };\n      instRef.current = inst;\n    } else inst = instRef.current;\n    instRef = useMemo(function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n          }\n          return memoizedSelection = nextSnapshot;\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return memoizedSelection = nextSelection;\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [function () {\n        return memoizedSelector(getSnapshot());\n      }, null === maybeGetServerSnapshot ? void 0 : function () {\n        return memoizedSelector(maybeGetServerSnapshot());\n      }];\n    }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n    var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n    useEffect(function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    }, [value]);\n    useDebugValue(value);\n    return value;\n  };\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "is", "x", "y", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "React", "require", "shim", "objectIs", "Object", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "useDebugValue", "exports", "useSyncExternalStoreWithSelector", "subscribe", "getSnapshot", "getServerSnapshot", "selector", "isEqual", "instRef", "current", "inst", "hasValue", "value", "memoizedSelector", "nextSnapshot", "hasMemo", "memoizedSnapshot", "currentSelection", "memoizedSelection", "nextSelection", "maybeGetServerSnapshot", "registerInternalModuleStop"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,YAAY,KAAKA,OAAO,CAACC,GAAG,CAACC,QAAQ,IAClC,YAAY;EACX,SAASC,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAChB,OAAQD,CAAC,KAAKC,CAAC,KAAK,CAAC,KAAKD,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IAAMD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAE;EAC1E;EACA,WAAW,KAAK,OAAOC,8BAA8B,IACnD,UAAU,KACR,OAAOA,8BAA8B,CAACC,2BAA2B,IACnED,8BAA8B,CAACC,2BAA2B,CAACC,KAAK,CAAC,CAAC,CAAC;EACrE,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;IAC1BC,IAAI,GAAGD,OAAO,CAAC,8BAA8B,CAAC;IAC9CE,QAAQ,GAAG,UAAU,KAAK,OAAOC,MAAM,CAACV,EAAE,GAAGU,MAAM,CAACV,EAAE,GAAGA,EAAE;IAC3DW,oBAAoB,GAAGH,IAAI,CAACG,oBAAoB;IAChDC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,aAAa,GAAGT,KAAK,CAACS,aAAa;EACrCC,OAAO,CAACC,gCAAgC,GAAG,UACzCC,SAAS,EACTC,WAAW,EACXC,iBAAiB,EACjBC,QAAQ,EACRC,OAAO,EACP;IACA,IAAIC,OAAO,GAAGX,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,IAAI,KAAKW,OAAO,CAACC,OAAO,EAAE;MAC5B,IAAIC,IAAI,GAAG;QAAEC,QAAQ,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MACxCJ,OAAO,CAACC,OAAO,GAAGC,IAAI;IACxB,CAAC,MAAMA,IAAI,GAAGF,OAAO,CAACC,OAAO;IAC7BD,OAAO,GAAGT,OAAO,CACf,YAAY;MACV,SAASc,gBAAgBA,CAACC,YAAY,EAAE;QACtC,IAAI,CAACC,OAAO,EAAE;UACZA,OAAO,GAAG,CAAC,CAAC;UACZC,gBAAgB,GAAGF,YAAY;UAC/BA,YAAY,GAAGR,QAAQ,CAACQ,YAAY,CAAC;UACrC,IAAI,KAAK,CAAC,KAAKP,OAAO,IAAIG,IAAI,CAACC,QAAQ,EAAE;YACvC,IAAIM,gBAAgB,GAAGP,IAAI,CAACE,KAAK;YACjC,IAAIL,OAAO,CAACU,gBAAgB,EAAEH,YAAY,CAAC,EACzC,OAAQI,iBAAiB,GAAGD,gBAAgB;UAChD;UACA,OAAQC,iBAAiB,GAAGJ,YAAY;QAC1C;QACAG,gBAAgB,GAAGC,iBAAiB;QACpC,IAAIxB,QAAQ,CAACsB,gBAAgB,EAAEF,YAAY,CAAC,EAC1C,OAAOG,gBAAgB;QACzB,IAAIE,aAAa,GAAGb,QAAQ,CAACQ,YAAY,CAAC;QAC1C,IAAI,KAAK,CAAC,KAAKP,OAAO,IAAIA,OAAO,CAACU,gBAAgB,EAAEE,aAAa,CAAC,EAChE,OAAQH,gBAAgB,GAAGF,YAAY,EAAGG,gBAAgB;QAC5DD,gBAAgB,GAAGF,YAAY;QAC/B,OAAQI,iBAAiB,GAAGC,aAAa;MAC3C;MACA,IAAIJ,OAAO,GAAG,CAAC,CAAC;QACdC,gBAAgB;QAChBE,iBAAiB;QACjBE,sBAAsB,GACpB,KAAK,CAAC,KAAKf,iBAAiB,GAAG,IAAI,GAAGA,iBAAiB;MAC3D,OAAO,CACL,YAAY;QACV,OAAOQ,gBAAgB,CAACT,WAAW,CAAC,CAAC,CAAC;MACxC,CAAC,EACD,IAAI,KAAKgB,sBAAsB,GAC3B,KAAK,CAAC,GACN,YAAY;QACV,OAAOP,gBAAgB,CAACO,sBAAsB,CAAC,CAAC,CAAC;MACnD,CAAC,CACN;IACH,CAAC,EACD,CAAChB,WAAW,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,OAAO,CACpD,CAAC;IACD,IAAIK,KAAK,GAAGhB,oBAAoB,CAACO,SAAS,EAAEK,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;IACnEV,SAAS,CACP,YAAY;MACVY,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MAClBD,IAAI,CAACE,KAAK,GAAGA,KAAK;IACpB,CAAC,EACD,CAACA,KAAK,CACR,CAAC;IACDZ,aAAa,CAACY,KAAK,CAAC;IACpB,OAAOA,KAAK;EACd,CAAC;EACD,WAAW,KAAK,OAAOxB,8BAA8B,IACnD,UAAU,KACR,OAAOA,8BAA8B,CAACiC,0BAA0B,IAClEjC,8BAA8B,CAACiC,0BAA0B,CAAC/B,KAAK,CAAC,CAAC,CAAC;AACtE,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}