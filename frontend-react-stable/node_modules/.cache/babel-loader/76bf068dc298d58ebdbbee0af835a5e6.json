{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = least;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction least(values, compare = _ascending.default) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? (0, _ascending.default)(value, minValue) < 0 : (0, _ascending.default)(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "least", "_ascending", "_interopRequireDefault", "require", "obj", "__esModule", "values", "compare", "min", "defined", "length", "minValue", "element"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/least.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = least;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction least(values, compare = _ascending.default) {\n  let min;\n  let defined = false;\n\n  if (compare.length === 1) {\n    let minValue;\n\n    for (const element of values) {\n      const value = compare(element);\n\n      if (defined ? (0, _ascending.default)(value, minValue) < 0 : (0, _ascending.default)(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n\n  return min;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,KAAKA,CAACM,MAAM,EAAEC,OAAO,GAAGN,UAAU,CAACF,OAAO,EAAE;EACnD,IAAIS,GAAG;EACP,IAAIC,OAAO,GAAG,KAAK;EAEnB,IAAIF,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;IACxB,IAAIC,QAAQ;IAEZ,KAAK,MAAMC,OAAO,IAAIN,MAAM,EAAE;MAC5B,MAAMR,KAAK,GAAGS,OAAO,CAACK,OAAO,CAAC;MAE9B,IAAIH,OAAO,GAAG,CAAC,CAAC,EAAER,UAAU,CAACF,OAAO,EAAED,KAAK,EAAEa,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEV,UAAU,CAACF,OAAO,EAAED,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACxGU,GAAG,GAAGI,OAAO;QACbD,QAAQ,GAAGb,KAAK;QAChBW,OAAO,GAAG,IAAI;MAChB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMX,KAAK,IAAIQ,MAAM,EAAE;MAC1B,IAAIG,OAAO,GAAGF,OAAO,CAACT,KAAK,EAAEU,GAAG,CAAC,GAAG,CAAC,GAAGD,OAAO,CAACT,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACnEU,GAAG,GAAGV,KAAK;QACXW,OAAO,GAAG,IAAI;MAChB;IACF;EACF;EAEA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}