{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _basis = require(\"./basis.js\");\nfunction _default(values) {\n  var n = values.length;\n  return function (t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n      v0 = values[(i + n - 1) % n],\n      v1 = values[i % n],\n      v2 = values[(i + 1) % n],\n      v3 = values[(i + 2) % n];\n    return (0, _basis.basis)((t - i / n) * n, v0, v1, v2, v3);\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_basis", "require", "values", "n", "length", "t", "i", "Math", "floor", "v0", "v1", "v2", "v3", "basis"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/basisClosed.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _basis = require(\"./basis.js\");\n\nfunction _default(values) {\n  var n = values.length;\n  return function (t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return (0, _basis.basis)((t - i / n) * n, v0, v1, v2, v3);\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AAElC,SAASF,QAAQA,CAACG,MAAM,EAAE;EACxB,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;EACrB,OAAO,UAAUC,CAAC,EAAE;IAClB,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,CAACH,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAEA,CAAC,GAAGA,CAAC,IAAIF,CAAC,CAAC;MAC5CM,EAAE,GAAGP,MAAM,CAAC,CAACI,CAAC,GAAGH,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC;MAC5BO,EAAE,GAAGR,MAAM,CAACI,CAAC,GAAGH,CAAC,CAAC;MAClBQ,EAAE,GAAGT,MAAM,CAAC,CAACI,CAAC,GAAG,CAAC,IAAIH,CAAC,CAAC;MACxBS,EAAE,GAAGV,MAAM,CAAC,CAACI,CAAC,GAAG,CAAC,IAAIH,CAAC,CAAC;IAC5B,OAAO,CAAC,CAAC,EAAEH,MAAM,CAACa,KAAK,EAAE,CAACR,CAAC,GAAGC,CAAC,GAAGH,CAAC,IAAIA,CAAC,EAAEM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC3D,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}