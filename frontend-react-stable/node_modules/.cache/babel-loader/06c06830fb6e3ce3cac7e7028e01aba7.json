{"ast": null, "code": "import { SHOW_CHILD } from './commonUtil';\nexport function formatStrategyValues(pathKeys, getKeyPathEntities, showCheckedStrategy) {\n  var valueSet = new Set(pathKeys);\n  var keyPathEntities = getKeyPathEntities();\n  return pathKeys.filter(function (key) {\n    var entity = keyPathEntities[key];\n    var parent = entity ? entity.parent : null;\n    var children = entity ? entity.children : null;\n    return showCheckedStrategy === SHOW_CHILD ? !(children && children.some(function (child) {\n      return child.key && valueSet.has(child.key);\n    })) : !(parent && !parent.node.disabled && valueSet.has(parent.key));\n  });\n}\nexport function toPathOptions(valueCells, options, fieldNames) {\n  var stringMode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var currentList = options;\n  var valueOptions = [];\n  var _loop = function _loop() {\n    var _currentList, _currentList2, _foundOption$fieldNam;\n    var valueCell = valueCells[i];\n    var foundIndex = (_currentList = currentList) === null || _currentList === void 0 ? void 0 : _currentList.findIndex(function (option) {\n      var val = option[fieldNames.value];\n      return stringMode ? String(val) === String(valueCell) : val === valueCell;\n    });\n    var foundOption = foundIndex !== -1 ? (_currentList2 = currentList) === null || _currentList2 === void 0 ? void 0 : _currentList2[foundIndex] : null;\n    valueOptions.push({\n      value: (_foundOption$fieldNam = foundOption === null || foundOption === void 0 ? void 0 : foundOption[fieldNames.value]) !== null && _foundOption$fieldNam !== void 0 ? _foundOption$fieldNam : valueCell,\n      index: foundIndex,\n      option: foundOption\n    });\n    currentList = foundOption === null || foundOption === void 0 ? void 0 : foundOption[fieldNames.children];\n  };\n  for (var i = 0; i < valueCells.length; i += 1) {\n    _loop();\n  }\n  return valueOptions;\n}", "map": {"version": 3, "names": ["SHOW_CHILD", "formatStrategyValues", "pathKeys", "getKeyPathEntities", "showCheckedStrategy", "valueSet", "Set", "keyPathEntities", "filter", "key", "entity", "parent", "children", "some", "child", "has", "node", "disabled", "toPathOptions", "valueCells", "options", "fieldNames", "stringMode", "arguments", "length", "undefined", "currentList", "valueOptions", "_loop", "_currentList", "_currentList2", "_foundOption$fieldNam", "valueCell", "i", "foundIndex", "findIndex", "option", "val", "value", "String", "foundOption", "push", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-cascader/es/utils/treeUtil.js"], "sourcesContent": ["import { SHOW_CHILD } from './commonUtil';\nexport function formatStrategyValues(pathKeys, getKeyPathEntities, showCheckedStrategy) {\n  var valueSet = new Set(pathKeys);\n  var keyPathEntities = getKeyPathEntities();\n  return pathKeys.filter(function (key) {\n    var entity = keyPathEntities[key];\n    var parent = entity ? entity.parent : null;\n    var children = entity ? entity.children : null;\n    return showCheckedStrategy === SHOW_CHILD ? !(children && children.some(function (child) {\n      return child.key && valueSet.has(child.key);\n    })) : !(parent && !parent.node.disabled && valueSet.has(parent.key));\n  });\n}\nexport function toPathOptions(valueCells, options, fieldNames) {\n  var stringMode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var currentList = options;\n  var valueOptions = [];\n  var _loop = function _loop() {\n    var _currentList, _currentList2, _foundOption$fieldNam;\n    var valueCell = valueCells[i];\n    var foundIndex = (_currentList = currentList) === null || _currentList === void 0 ? void 0 : _currentList.findIndex(function (option) {\n      var val = option[fieldNames.value];\n      return stringMode ? String(val) === String(valueCell) : val === valueCell;\n    });\n    var foundOption = foundIndex !== -1 ? (_currentList2 = currentList) === null || _currentList2 === void 0 ? void 0 : _currentList2[foundIndex] : null;\n    valueOptions.push({\n      value: (_foundOption$fieldNam = foundOption === null || foundOption === void 0 ? void 0 : foundOption[fieldNames.value]) !== null && _foundOption$fieldNam !== void 0 ? _foundOption$fieldNam : valueCell,\n      index: foundIndex,\n      option: foundOption\n    });\n    currentList = foundOption === null || foundOption === void 0 ? void 0 : foundOption[fieldNames.children];\n  };\n  for (var i = 0; i < valueCells.length; i += 1) {\n    _loop();\n  }\n  return valueOptions;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAE;EACtF,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAACJ,QAAQ,CAAC;EAChC,IAAIK,eAAe,GAAGJ,kBAAkB,CAAC,CAAC;EAC1C,OAAOD,QAAQ,CAACM,MAAM,CAAC,UAAUC,GAAG,EAAE;IACpC,IAAIC,MAAM,GAAGH,eAAe,CAACE,GAAG,CAAC;IACjC,IAAIE,MAAM,GAAGD,MAAM,GAAGA,MAAM,CAACC,MAAM,GAAG,IAAI;IAC1C,IAAIC,QAAQ,GAAGF,MAAM,GAAGA,MAAM,CAACE,QAAQ,GAAG,IAAI;IAC9C,OAAOR,mBAAmB,KAAKJ,UAAU,GAAG,EAAEY,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,UAAUC,KAAK,EAAE;MACvF,OAAOA,KAAK,CAACL,GAAG,IAAIJ,QAAQ,CAACU,GAAG,CAACD,KAAK,CAACL,GAAG,CAAC;IAC7C,CAAC,CAAC,CAAC,GAAG,EAAEE,MAAM,IAAI,CAACA,MAAM,CAACK,IAAI,CAACC,QAAQ,IAAIZ,QAAQ,CAACU,GAAG,CAACJ,MAAM,CAACF,GAAG,CAAC,CAAC;EACtE,CAAC,CAAC;AACJ;AACA,OAAO,SAASS,aAAaA,CAACC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAC7D,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1F,IAAIG,WAAW,GAAGN,OAAO;EACzB,IAAIO,YAAY,GAAG,EAAE;EACrB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB;IACtD,IAAIC,SAAS,GAAGb,UAAU,CAACc,CAAC,CAAC;IAC7B,IAAIC,UAAU,GAAG,CAACL,YAAY,GAAGH,WAAW,MAAM,IAAI,IAAIG,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACM,SAAS,CAAC,UAAUC,MAAM,EAAE;MACpI,IAAIC,GAAG,GAAGD,MAAM,CAACf,UAAU,CAACiB,KAAK,CAAC;MAClC,OAAOhB,UAAU,GAAGiB,MAAM,CAACF,GAAG,CAAC,KAAKE,MAAM,CAACP,SAAS,CAAC,GAAGK,GAAG,KAAKL,SAAS;IAC3E,CAAC,CAAC;IACF,IAAIQ,WAAW,GAAGN,UAAU,KAAK,CAAC,CAAC,GAAG,CAACJ,aAAa,GAAGJ,WAAW,MAAM,IAAI,IAAII,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,UAAU,CAAC,GAAG,IAAI;IACpJP,YAAY,CAACc,IAAI,CAAC;MAChBH,KAAK,EAAE,CAACP,qBAAqB,GAAGS,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACnB,UAAU,CAACiB,KAAK,CAAC,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGC,SAAS;MACzMU,KAAK,EAAER,UAAU;MACjBE,MAAM,EAAEI;IACV,CAAC,CAAC;IACFd,WAAW,GAAGc,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACnB,UAAU,CAACT,QAAQ,CAAC;EAC1G,CAAC;EACD,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,CAACK,MAAM,EAAES,CAAC,IAAI,CAAC,EAAE;IAC7CL,KAAK,CAAC,CAAC;EACT;EACA,OAAOD,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}