{"ast": null, "code": "var minus = \"-\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar dot = \".\".charCodeAt(0);\nvar exp = \"e\".charCodeAt(0);\nvar EXP = \"E\".charCodeAt(0);\nmodule.exports = function (value) {\n  var pos = 0;\n  var length = value.length;\n  var dotted = false;\n  var sciPos = -1;\n  var containsNumber = false;\n  var code;\n  while (pos < length) {\n    code = value.charCodeAt(pos);\n    if (code >= 48 && code <= 57) {\n      containsNumber = true;\n    } else if (code === exp || code === EXP) {\n      if (sciPos > -1) {\n        break;\n      }\n      sciPos = pos;\n    } else if (code === dot) {\n      if (dotted) {\n        break;\n      }\n      dotted = true;\n    } else if (code === plus || code === minus) {\n      if (pos !== 0) {\n        break;\n      }\n    } else {\n      break;\n    }\n    pos += 1;\n  }\n  if (sciPos + 1 === pos) pos--;\n  return containsNumber ? {\n    number: value.slice(0, pos),\n    unit: value.slice(pos)\n  } : false;\n};", "map": {"version": 3, "names": ["minus", "charCodeAt", "plus", "dot", "exp", "EXP", "module", "exports", "value", "pos", "length", "dotted", "sciPos", "containsNumber", "code", "number", "slice", "unit"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/node_modules/postcss-value-parser/lib/unit.js"], "sourcesContent": ["var minus = \"-\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar dot = \".\".charCodeAt(0);\nvar exp = \"e\".charCodeAt(0);\nvar EXP = \"E\".charCodeAt(0);\n\nmodule.exports = function(value) {\n  var pos = 0;\n  var length = value.length;\n  var dotted = false;\n  var sciPos = -1;\n  var containsNumber = false;\n  var code;\n\n  while (pos < length) {\n    code = value.charCodeAt(pos);\n\n    if (code >= 48 && code <= 57) {\n      containsNumber = true;\n    } else if (code === exp || code === EXP) {\n      if (sciPos > -1) {\n        break;\n      }\n      sciPos = pos;\n    } else if (code === dot) {\n      if (dotted) {\n        break;\n      }\n      dotted = true;\n    } else if (code === plus || code === minus) {\n      if (pos !== 0) {\n        break;\n      }\n    } else {\n      break;\n    }\n\n    pos += 1;\n  }\n\n  if (sciPos + 1 === pos) pos--;\n\n  return containsNumber\n    ? {\n        number: value.slice(0, pos),\n        unit: value.slice(pos)\n      }\n    : false;\n};\n"], "mappings": "AAAA,IAAIA,KAAK,GAAG,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;AAC7B,IAAIC,IAAI,GAAG,GAAG,CAACD,UAAU,CAAC,CAAC,CAAC;AAC5B,IAAIE,GAAG,GAAG,GAAG,CAACF,UAAU,CAAC,CAAC,CAAC;AAC3B,IAAIG,GAAG,GAAG,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC;AAC3B,IAAII,GAAG,GAAG,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAC;AAE3BK,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAE;EAC/B,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACzB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,IAAI;EAER,OAAOL,GAAG,GAAGC,MAAM,EAAE;IACnBI,IAAI,GAAGN,KAAK,CAACP,UAAU,CAACQ,GAAG,CAAC;IAE5B,IAAIK,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;MAC5BD,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM,IAAIC,IAAI,KAAKV,GAAG,IAAIU,IAAI,KAAKT,GAAG,EAAE;MACvC,IAAIO,MAAM,GAAG,CAAC,CAAC,EAAE;QACf;MACF;MACAA,MAAM,GAAGH,GAAG;IACd,CAAC,MAAM,IAAIK,IAAI,KAAKX,GAAG,EAAE;MACvB,IAAIQ,MAAM,EAAE;QACV;MACF;MACAA,MAAM,GAAG,IAAI;IACf,CAAC,MAAM,IAAIG,IAAI,KAAKZ,IAAI,IAAIY,IAAI,KAAKd,KAAK,EAAE;MAC1C,IAAIS,GAAG,KAAK,CAAC,EAAE;QACb;MACF;IACF,CAAC,MAAM;MACL;IACF;IAEAA,GAAG,IAAI,CAAC;EACV;EAEA,IAAIG,MAAM,GAAG,CAAC,KAAKH,GAAG,EAAEA,GAAG,EAAE;EAE7B,OAAOI,cAAc,GACjB;IACEE,MAAM,EAAEP,KAAK,CAACQ,KAAK,CAAC,CAAC,EAAEP,GAAG,CAAC;IAC3BQ,IAAI,EAAET,KAAK,CAACQ,KAAK,CAACP,GAAG;EACvB,CAAC,GACD,KAAK;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}