{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, InputNumber, Slider, Checkbox, Progress, Alert, Table, Row, Col, Statistic } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay = ({\n  resultKey,\n  result\n}) => {\n  var _result$train_losses, _result$val_losses;\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"middle\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u534F\\u8BAE:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 17\n          }, this), \" \", selectedProt]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 17\n          }, this), \" \", selectedDatatype]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BAD\\u7EC3\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D4B\\u8BD5\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"R\\xB2 \\u5206\\u6570\",\n            value: result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A',\n            precision: 4,\n            valueStyle: {\n              color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), result.train_losses && result.val_losses && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u8BAD\\u7EC3\\u635F\\u5931\\u66F2\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.train_losses.map((trainLoss, index) => ({\n                epoch: index + 1,\n                训练损失: trainLoss,\n                验证损失: result.val_losses[index] || null\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"epoch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u8BAD\\u7EC3\\u635F\\u5931\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9A8C\\u8BC1\\u635F\\u5931\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \", result.train_losses.length, \" epochs | \\u6700\\u7EC8\\u8BAD\\u7EC3\\u635F\\u5931: \", (_result$train_losses = result.train_losses[result.train_losses.length - 1]) === null || _result$train_losses === void 0 ? void 0 : _result$train_losses.toFixed(6), \" | \\u6700\\u7EC8\\u9A8C\\u8BC1\\u635F\\u5931: \", (_result$val_losses = result.val_losses[result.val_losses.length - 1]) === null || _result$val_losses === void 0 ? void 0 : _result$val_losses.toFixed(6)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B9E\\u9645\\u503C vs \\u9884\\u6D4B\\u503C\\u5BF9\\u6BD4\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.y_test_actual.slice(0, 100).map((actual, index) => ({\n                index: index + 1,\n                实际值: actual,\n                预测值: result.y_pred[index]\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"index\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u5B9E\\u9645\\u503C\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9884\\u6D4B\\u503C\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u663E\\u793A\\u524D100\\u4E2A\\u6D4B\\u8BD5\\u6837\\u672C\\u7684\\u9884\\u6D4B\\u5BF9\\u6BD4 | R\\xB2 \\u5206\\u6570: \", result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A', \" | \\u5EFA\\u8BAE\\u9608\\u503C: \", result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u9884\\u6D4B\\u7ED3\\u679C\\u6837\\u672C (\\u524D10\\u6761)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          dataSource: result.y_test_actual.slice(0, 10).map((actual, index) => ({\n            key: index,\n            index: index + 1,\n            actual: actual,\n            predicted: result.y_pred[index],\n            error: Math.abs(actual - result.y_pred[index])\n          })),\n          columns: [{\n            title: '序号',\n            dataIndex: 'index',\n            key: 'index'\n          }, {\n            title: '实际值',\n            dataIndex: 'actual',\n            key: 'actual',\n            render: value => value === null || value === void 0 ? void 0 : value.toFixed(4)\n          }, {\n            title: '预测值',\n            dataIndex: 'predicted',\n            key: 'predicted',\n            render: value => value === null || value === void 0 ? void 0 : value.toFixed(4)\n          }, {\n            title: '误差',\n            dataIndex: 'error',\n            key: 'error',\n            render: value => value === null || value === void 0 ? void 0 : value.toFixed(4)\n          }],\n          pagination: false,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this), result.model_save_path && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6A21\\u578B\\u6587\\u4EF6\\u4FE1\\u606F\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 20\n            }, this), \" \", result.model_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this), result.scaler_y_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u51C6\\u5316\\u5668\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 22\n            }, this), \" \", result.scaler_y_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 19\n          }, this), result.params_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u53C2\\u6570\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 22\n            }, this), \" \", result.params_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 19\n          }, this), result.test_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6D4B\\u8BD5\\u6570\\u636E\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 22\n            }, this), \" \", result.test_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 19\n          }, this), result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 22\n            }, this), \" \", result.static_anomaly_threshold.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 19\n          }, this), result.finished_time && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 22\n            }, this), \" \", result.finished_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 19\n          }, this), result.duration_seconds && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u8017\\u65F6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 22\n            }, this), \" \", result.duration_seconds.toFixed(2), \" \\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_c = TrainingResultDisplay;\nconst ModelTrainingPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('/home/<USER>');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(50);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState(null);\n  const [selectedResultKey, setSelectedResultKey] = useState('');\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      fetchCsvFiles();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = prots => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot]) {\n        newDatatypes[prot] = [datatypeOptions[prot][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n    const hasValidDatatypes = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n    try {\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n    } catch (error) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        var _error$response$data2, _error$response$data3;\n        if ((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.detail) {\n          errorMessage = error.response.data.detail;\n        } else if ((_error$response$data3 = error.response.data) !== null && _error$response$data3 !== void 0 && _error$response$data3.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              value: csvDir,\n              onChange: e => setCsvDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n              style: {\n                marginTop: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedFile,\n                onChange: setSelectedFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            value: selectedProts,\n            onChange: handleProtocolChange,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n              value: prot,\n              children: prot\n            }, prot, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), selectedProts.map(prot => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: selectedDatatypes[prot] || [],\n            onChange: datatypes => handleDatatypeChange(prot, datatypes),\n            style: {\n              marginTop: 8\n            },\n            children: (datatypeOptions[prot] || []).map(datatype => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: datatype,\n              children: datatype\n            }, datatype, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this)]\n        }, prot, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u57FA\\u7840\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5B66\\u4E60\\u7387\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: learningRate,\n                  onChange: value => setLearningRate(value || 0.001),\n                  min: 0.0001,\n                  max: 1,\n                  step: 0.0001,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: batchSize,\n                  onChange: value => setBatchSize(value || 32),\n                  min: 1,\n                  max: 512,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: epochs,\n                  onChange: value => setEpochs(value || 50),\n                  min: 1,\n                  max: 1000,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u53C2\\u6570 (GRU/LSTM)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: sequenceLength,\n                  onChange: value => setSequenceLength(value || 10),\n                  min: 1,\n                  max: 100,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: hiddenSize,\n                  onChange: value => setHiddenSize(value || 50),\n                  min: 10,\n                  max: 512,\n                  step: 10,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5C42\\u6570\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                  value: numLayers,\n                  onChange: value => setNumLayers(value || 2),\n                  min: 1,\n                  max: 10,\n                  style: {\n                    marginLeft: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"Dropout \\u6982\\u7387\\uFF1A\", dropout]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                  value: dropout,\n                  onChange: setDropout,\n                  min: 0,\n                  max: 0.9,\n                  step: 0.05,\n                  style: {\n                    marginTop: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: outputFolder,\n            onChange: e => setOutputFolder(e.target.value),\n            placeholder: \"\\u4F8B\\u5982: /data/output\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartTraining,\n        loading: training,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: training ? '正在训练...' : '开始训练预测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 9\n      }, this), training && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6240\\u6709\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 19\n            }, this), trainingResults.result_path && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u7ED3\\u679C\\u5DF2\\u66F4\\u65B0\\u81F3:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 24\n              }, this), \" \", trainingResults.result_path]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 21\n            }, this), Object.entries(trainingResults.results).map(([key, result]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u534F\\u8BAE\\u4E0E\\u6570\\u636E\\u7C7B\\u578B:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 26\n                }, this), \" \", key]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6A21\\u578B\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.model_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6807\\u51C6\\u5316\\u5668\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.scaler_y_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u67E5\\u770B\\u6A21\\u578B\\u8BAD\\u7EC3\\u53CA\\u7279\\u5F81\\u9884\\u6D4B\\u7ED3\\u679C\",\n        className: \"function-card\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedResultKey,\n              onChange: setSelectedResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(trainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), selectedResultKey && trainingResults.results[selectedResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedResultKey,\n            result: trainingResults.results[selectedResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 485,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelTrainingPage, \"6fFFR4kmqECw7edlkC7CK8+CNMw=\");\n_c2 = ModelTrainingPage;\nexport default ModelTrainingPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TrainingResultDisplay\");\n$RefreshReg$(_c2, \"ModelTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "Table", "Row", "Col", "Statistic", "InboxOutlined", "PlayCircleOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelTrainingAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "TrainingResultDisplay", "<PERSON><PERSON><PERSON>", "result", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "children", "direction", "size", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "title", "value", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "color", "train_losses", "val_losses", "strong", "height", "marginTop", "data", "map", "trainLoss", "index", "epoch", "训练损失", "验证损失", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "length", "y_test_actual", "y_pred", "slice", "actual", "实际值", "预测值", "static_anomaly_threshold", "dataSource", "key", "predicted", "error", "Math", "abs", "columns", "dataIndex", "render", "pagination", "model_save_path", "description", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "showIcon", "_c", "ModelTrainingPage", "_s", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "files", "_error$response", "_error$response$data", "detail", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "Object", "keys", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "handleStartTraining", "hasValidDatatypes", "some", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "toString", "progressInterval", "setInterval", "clearInterval", "trainModel", "localTrainingData", "csv_dir", "selected_file", "selected_prots", "selected_datatypes", "learning_rate", "batch_size", "sequence_length", "hidden_size", "num_layers", "output_folder", "trainModelLocal", "console", "log", "results", "success", "result_path", "errorMessage", "_error$response$data2", "_error$response$data3", "statusText", "status", "isFormValid", "level", "className", "Group", "e", "target", "placeholder", "spinning", "loading", "file", "mode", "datatype", "min", "max", "step", "marginLeft", "icon", "onClick", "disabled", "percent", "entries", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Table,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={8}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={8}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={8}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.slice(0, 100).map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示前100个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>预测结果样本 (前10条)</Text>\n            <Table\n              size=\"small\"\n              dataSource={result.y_test_actual.slice(0, 10).map((actual: number, index: number) => ({\n                key: index,\n                index: index + 1,\n                actual: actual,\n                predicted: result.y_pred[index],\n                error: Math.abs(actual - result.y_pred[index]),\n              }))}\n              columns={[\n                {\n                  title: '序号',\n                  dataIndex: 'index',\n                  key: 'index',\n                },\n                {\n                  title: '实际值',\n                  dataIndex: 'actual',\n                  key: 'actual',\n                  render: (value: number) => value?.toFixed(4),\n                },\n                {\n                  title: '预测值',\n                  dataIndex: 'predicted',\n                  key: 'predicted',\n                  render: (value: number) => value?.toFixed(4),\n                },\n                {\n                  title: '误差',\n                  dataIndex: 'error',\n                  key: 'error',\n                  render: (value: number) => value?.toFixed(4),\n                },\n              ]}\n              pagination={false}\n              style={{ marginTop: 8 }}\n            />\n          </div>\n        )}\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('/home/<USER>');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(50);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir) {\n      fetchCsvFiles();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={2}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input\n                  value={csvDir}\n                  onChange={(e) => setCsvDir(e.target.value)}\n                  placeholder=\"例如: /home/<USER>\"\n                  style={{ marginTop: 8 }}\n                />\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card title=\"训练参数配置\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>基础参数</Text>\n            <div style={{ marginTop: 16 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>学习率：</Text>\n                  <InputNumber\n                    value={learningRate}\n                    onChange={(value) => setLearningRate(value || 0.001)}\n                    min={0.0001}\n                    max={1}\n                    step={0.0001}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>批量大小：</Text>\n                  <InputNumber\n                    value={batchSize}\n                    onChange={(value) => setBatchSize(value || 32)}\n                    min={1}\n                    max={512}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>训练轮数：</Text>\n                  <InputNumber\n                    value={epochs}\n                    onChange={(value) => setEpochs(value || 50)}\n                    min={1}\n                    max={1000}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n              </Space>\n            </div>\n          </div>\n\n          <div>\n            <Text strong>模型参数 (GRU/LSTM)</Text>\n            <div style={{ marginTop: 16 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>序列长度：</Text>\n                  <InputNumber\n                    value={sequenceLength}\n                    onChange={(value) => setSequenceLength(value || 10)}\n                    min={1}\n                    max={100}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>隐藏层大小：</Text>\n                  <InputNumber\n                    value={hiddenSize}\n                    onChange={(value) => setHiddenSize(value || 50)}\n                    min={10}\n                    max={512}\n                    step={10}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div>\n                  <Text>层数：</Text>\n                  <InputNumber\n                    value={numLayers}\n                    onChange={(value) => setNumLayers(value || 2)}\n                    min={1}\n                    max={10}\n                    style={{ marginLeft: 8 }}\n                  />\n                </div>\n                <div style={{ width: '100%' }}>\n                  <Text>Dropout 概率：{dropout}</Text>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    style={{ marginTop: 8 }}\n                  />\n                </div>\n              </Space>\n            </div>\n          </div>\n\n          <div>\n            <Text strong>模型保存路径：</Text>\n            <Input\n              value={outputFolder}\n              onChange={(e) => setOutputFolder(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果 */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,gBAAgB,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B;AAAQ,CAAC,GAAGjC,MAAM;AAC1B,MAAM;EAAEkC;AAAO,CAAC,GAAGhC,MAAM;;AAEzB;AACA,MAAMiC,qBAAmE,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAA,IAAAC,oBAAA,EAAAC,kBAAA;EACrG,MAAM,CAACC,YAAY,EAAEC,gBAAgB,CAAC,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EAEhE,oBACEZ,OAAA;IAAAa,QAAA,eACEb,OAAA,CAACzB,KAAK;MAACuC,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACjEb,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAAAW,QAAA,gBAACb,OAAA;YAAAa,QAAA,EAAQ;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACX,YAAY;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDrB,OAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrB,OAAA,CAACE,IAAI;UAAAW,QAAA,gBAACb,OAAA;YAAAa,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,gBAAgB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENrB,OAAA,CAACf,GAAG;QAACqC,MAAM,EAAE,EAAG;QAAAT,QAAA,gBACdb,OAAA,CAACd,GAAG;UAACqC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACmB,WAAW,GAAG,GAAGnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAMnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACd,GAAG;UAACqC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACoB,UAAU,GAAG,GAAGpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,MAAMpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACd,GAAG;UAACqC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,oBAAO;YACbC,KAAK,EAAElB,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAO;YACjGE,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEC,KAAK,EAAE1B,MAAM,CAACqB,QAAQ,GAAG,GAAG,IAAIrB,MAAM,CAACuB,EAAE,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELd,MAAM,CAAC2B,YAAY,IAAI3B,MAAM,CAAC4B,UAAU,iBACvCnC,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAACkC,MAAM;UAAAvB,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BrB,OAAA;UAAKgB,KAAK,EAAE;YAAEqB,MAAM,EAAE,GAAG;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAzB,QAAA,eACxCb,OAAA,CAACH,mBAAmB;YAACoB,KAAK,EAAC,MAAM;YAACoB,MAAM,EAAC,MAAM;YAAAxB,QAAA,eAC7Cb,OAAA,CAACV,SAAS;cACRiD,IAAI,EAAEhC,MAAM,CAAC2B,YAAY,CAACM,GAAG,CAAC,CAACC,SAAiB,EAAEC,KAAa,MAAM;gBACnEC,KAAK,EAAED,KAAK,GAAG,CAAC;gBAChBE,IAAI,EAAEH,SAAS;gBACfI,IAAI,EAAEtC,MAAM,CAAC4B,UAAU,CAACO,KAAK,CAAC,IAAI;cACpC,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAArC,QAAA,gBAEnDb,OAAA,CAACN,aAAa;gBAACyD,eAAe,EAAC;cAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCrB,OAAA,CAACR,KAAK;gBAAC4D,OAAO,EAAC;cAAO;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBrB,OAAA,CAACP,KAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTrB,OAAA,CAACL,OAAO;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrB,OAAA,CAACJ,MAAM;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVrB,OAAA,CAACT,IAAI;gBACH8D,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFrB,OAAA,CAACT,IAAI;gBACH8D,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrB,OAAA;UAAKgB,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAE,CAAE;UAAAzB,QAAA,eAC3Bb,OAAA,CAACE,IAAI;YAACmD,IAAI,EAAC,WAAW;YAAAxC,QAAA,GAAC,4BACf,EAACN,MAAM,CAAC2B,YAAY,CAACuB,MAAM,EAAC,kDAC1B,GAAAjD,oBAAA,GAACD,MAAM,CAAC2B,YAAY,CAAC3B,MAAM,CAAC2B,YAAY,CAACuB,MAAM,GAAG,CAAC,CAAC,cAAAjD,oBAAA,uBAAnDA,oBAAA,CAAqDqB,OAAO,CAAC,CAAC,CAAC,EAAC,2CACjE,GAAApB,kBAAA,GAACF,MAAM,CAAC4B,UAAU,CAAC5B,MAAM,CAAC4B,UAAU,CAACsB,MAAM,GAAG,CAAC,CAAC,cAAAhD,kBAAA,uBAA/CA,kBAAA,CAAiDoB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAd,MAAM,CAACmD,aAAa,IAAInD,MAAM,CAACoD,MAAM,IAAIpD,MAAM,CAACmD,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvEzD,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAACkC,MAAM;UAAAvB,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCrB,OAAA;UAAKgB,KAAK,EAAE;YAAEqB,MAAM,EAAE,GAAG;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAzB,QAAA,eACxCb,OAAA,CAACH,mBAAmB;YAACoB,KAAK,EAAC,MAAM;YAACoB,MAAM,EAAC,MAAM;YAAAxB,QAAA,eAC7Cb,OAAA,CAACV,SAAS;cACRiD,IAAI,EAAEhC,MAAM,CAACmD,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACpB,GAAG,CAAC,CAACqB,MAAc,EAAEnB,KAAa,MAAM;gBAC/EA,KAAK,EAAEA,KAAK,GAAG,CAAC;gBAChBoB,GAAG,EAAED,MAAM;gBACXE,GAAG,EAAExD,MAAM,CAACoD,MAAM,CAACjB,KAAK;cAC1B,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAArC,QAAA,gBAEnDb,OAAA,CAACN,aAAa;gBAACyD,eAAe,EAAC;cAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCrB,OAAA,CAACR,KAAK;gBAAC4D,OAAO,EAAC;cAAO;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBrB,OAAA,CAACP,KAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTrB,OAAA,CAACL,OAAO;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrB,OAAA,CAACJ,MAAM;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVrB,OAAA,CAACT,IAAI;gBACH8D,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFrB,OAAA,CAACT,IAAI;gBACH8D,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrB,OAAA;UAAKgB,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAE,CAAE;UAAAzB,QAAA,eAC3Bb,OAAA,CAACE,IAAI;YAACmD,IAAI,EAAC,WAAW;YAAAxC,QAAA,GAAC,0GAEd,EAACN,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM,EAAC,+BAC5F,EAACtB,MAAM,CAACyD,wBAAwB,GAAGzD,MAAM,CAACyD,wBAAwB,CAACnC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAd,MAAM,CAACmD,aAAa,IAAInD,MAAM,CAACoD,MAAM,IAAIpD,MAAM,CAACmD,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvEzD,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAACkC,MAAM;UAAAvB,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCrB,OAAA,CAAChB,KAAK;UACJ+B,IAAI,EAAC,OAAO;UACZkD,UAAU,EAAE1D,MAAM,CAACmD,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACpB,GAAG,CAAC,CAACqB,MAAc,EAAEnB,KAAa,MAAM;YACpFwB,GAAG,EAAExB,KAAK;YACVA,KAAK,EAAEA,KAAK,GAAG,CAAC;YAChBmB,MAAM,EAAEA,MAAM;YACdM,SAAS,EAAE5D,MAAM,CAACoD,MAAM,CAACjB,KAAK,CAAC;YAC/B0B,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACT,MAAM,GAAGtD,MAAM,CAACoD,MAAM,CAACjB,KAAK,CAAC;UAC/C,CAAC,CAAC,CAAE;UACJ6B,OAAO,EAAE,CACP;YACE/C,KAAK,EAAE,IAAI;YACXgD,SAAS,EAAE,OAAO;YAClBN,GAAG,EAAE;UACP,CAAC,EACD;YACE1C,KAAK,EAAE,KAAK;YACZgD,SAAS,EAAE,QAAQ;YACnBN,GAAG,EAAE,QAAQ;YACbO,MAAM,EAAGhD,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,OAAO,CAAC,CAAC;UAC7C,CAAC,EACD;YACEL,KAAK,EAAE,KAAK;YACZgD,SAAS,EAAE,WAAW;YACtBN,GAAG,EAAE,WAAW;YAChBO,MAAM,EAAGhD,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,OAAO,CAAC,CAAC;UAC7C,CAAC,EACD;YACEL,KAAK,EAAE,IAAI;YACXgD,SAAS,EAAE,OAAO;YAClBN,GAAG,EAAE,OAAO;YACZO,MAAM,EAAGhD,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,OAAO,CAAC,CAAC;UAC7C,CAAC,CACD;UACF6C,UAAU,EAAE,KAAM;UAClB1D,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAE;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEAd,MAAM,CAACoE,eAAe,iBACrB3E,OAAA,CAACjB,KAAK;QACJN,OAAO,EAAC,sCAAQ;QAChBmG,WAAW,eACT5E,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACoE,eAAe;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvDd,MAAM,CAACsE,kBAAkB,iBACxB7E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACsE,kBAAkB;UAAA;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC7D,EACAd,MAAM,CAACuE,gBAAgB,iBACtB9E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACuE,gBAAgB;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAACwE,cAAc,iBACpB/E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACwE,cAAc;UAAA;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAACyD,wBAAwB,iBAC9BhE,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACyD,wBAAwB,CAACnC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5E,EACAd,MAAM,CAACyE,aAAa,iBACnBhF,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACyE,aAAa;UAAA;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACtD,EACAd,MAAM,CAAC0E,gBAAgB,iBACtBjF,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC0E,gBAAgB,CAACpD,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDgC,IAAI,EAAC,MAAM;QACX6B,QAAQ;MAAA;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC8D,EAAA,GAzMI9E,qBAAmE;AA2MzE,MAAM+E,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACpB,UAAU,EAAEqB,aAAa,CAAC,GAAGxH,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAACyH,YAAY,EAAEC,eAAe,CAAC,GAAG1H,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC2H,MAAM,EAAEC,SAAS,CAAC,GAAG5H,QAAQ,CAAC,cAAc,CAAC;EACpD,MAAM,CAAC6H,cAAc,EAAEC,iBAAiB,CAAC,GAAG9H,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAACqI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtI,QAAQ,CAA4B;IACpFuI,GAAG,EAAE,CAAC,aAAa;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0I,SAAS,EAAEC,YAAY,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4I,MAAM,EAAEC,SAAS,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8I,cAAc,EAAEC,iBAAiB,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgJ,UAAU,EAAEC,aAAa,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkJ,SAAS,EAAEC,YAAY,CAAC,GAAGnJ,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoJ,OAAO,EAAEC,UAAU,CAAC,GAAGrJ,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACsJ,YAAY,EAAEC,eAAe,CAAC,GAAGvJ,QAAQ,CAAC,cAAc,CAAC;;EAEhE;EACA,MAAM,CAACwJ,QAAQ,EAAEC,WAAW,CAAC,GAAGzJ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0J,QAAQ,EAAEC,WAAW,CAAC,GAAG3J,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC4J,eAAe,EAAEC,kBAAkB,CAAC,GAAG7J,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAAC8J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/J,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAMgK,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtB1B,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjE2B,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACzC,MAAM,EAAE;IAEbO,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAMrI,gBAAgB,CAACsI,YAAY,CAAC3C,MAAM,CAAC;MAC5DG,iBAAiB,CAACuC,QAAQ,CAAC5F,IAAI,CAAC8F,KAAK,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOjE,KAAU,EAAE;MAAA,IAAAkE,eAAA,EAAAC,oBAAA;MACnB9J,OAAO,CAAC2F,KAAK,CAAC,EAAAkE,eAAA,GAAAlE,KAAK,CAAC+D,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB/F,IAAI,cAAAgG,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzD5C,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDjI,SAAS,CAAC,MAAM;IACd,IAAIkG,UAAU,KAAK,OAAO,IAAIwB,MAAM,EAAE;MACpCyC,aAAa,CAAC,CAAC;IACjB;IACA;EACF,CAAC,EAAE,CAACjE,UAAU,EAAEwB,MAAM,CAAC,CAAC;;EAExB;EACA,MAAMgD,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACvF,MAAM,GAAG,CAAC,EAAE;QAC5B+B,eAAe,CAACuD,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLxD,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMyD,oBAAoB,GAAIC,KAAe,IAAK;IAChDhD,gBAAgB,CAACgD,KAAK,CAAC;IACvB;IACA,MAAMC,YAAY,GAAG;MAAE,GAAGhD;IAAkB,CAAC;IAC7C+C,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpB,IAAI,CAACF,YAAY,CAACE,IAAI,CAAC,IAAItB,eAAe,CAACsB,IAAI,CAAiC,EAAE;QAChFF,YAAY,CAACE,IAAI,CAAC,GAAG,CAACtB,eAAe,CAACsB,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC;MACjF;IACF,CAAC,CAAC;IACF;IACAC,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACH,IAAI,CAAC,EAAE;QACzB,OAAOF,YAAY,CAACE,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;IACFjD,oBAAoB,CAAC+C,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtEvD,oBAAoB,CAACwD,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IAAI5F,UAAU,KAAK,QAAQ,IAAI,CAACsB,YAAY,EAAE;MAC5C9G,OAAO,CAAC2F,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIH,UAAU,KAAK,OAAO,KAAK,CAACwB,MAAM,IAAI,CAACI,YAAY,CAAC,EAAE;MACxDpH,OAAO,CAAC2F,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAI6B,aAAa,CAACxC,MAAM,KAAK,CAAC,EAAE;MAC9BhF,OAAO,CAAC2F,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,MAAM0F,iBAAiB,GAAG7D,aAAa,CAAC8D,IAAI,CAACV,IAAI,IAC/ClD,iBAAiB,CAACkD,IAAI,CAAC,IAAIlD,iBAAiB,CAACkD,IAAI,CAAC,CAAC5F,MAAM,GAAG,CAC9D,CAAC;IAED,IAAI,CAACqG,iBAAiB,EAAE;MACtBrL,OAAO,CAAC2F,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF;IAEAmD,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;IAExB,IAAI;MACF,IAAIM,QAAQ;MAEZ,IAAIlE,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAM+F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE3E,YAAY,CAAC4E,aAAa,CAAC;QACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACpE,aAAa,CAAC,CAAC;QAChE+D,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAAClE,iBAAiB,CAAC,CAAC;QACxE6D,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE5D,YAAY,CAACgE,QAAQ,CAAC,CAAC,CAAC;QACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1D,SAAS,CAAC8D,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAExD,MAAM,CAAC4D,QAAQ,CAAC,CAAC,CAAC;QAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEtD,cAAc,CAAC0D,QAAQ,CAAC,CAAC,CAAC;QAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEpD,UAAU,CAACwD,QAAQ,CAAC,CAAC,CAAC;QACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAElD,SAAS,CAACsD,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhD,OAAO,CAACoD,QAAQ,CAAC,CAAC,CAAC;QAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE9C,YAAY,CAAC;;QAE9C;QACA,MAAMmD,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC/C,WAAW,CAAEmC,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACda,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOX,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERzB,QAAQ,GAAG,MAAMrI,gBAAgB,CAAC4K,UAAU,CAACV,QAAQ,CAAC;QACtDS,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMI,iBAAiB,GAAG;UACxBC,OAAO,EAAEnF,MAAM;UACfoF,aAAa,EAAEhF,YAAY;UAC3BiF,cAAc,EAAE7E,aAAa;UAC7B8E,kBAAkB,EAAE5E,iBAAiB;UACrC6E,aAAa,EAAE1E,YAAY;UAC3B2E,UAAU,EAAEzE,SAAS;UACrBE,MAAM,EAAEA,MAAM;UACdwE,eAAe,EAAEtE,cAAc;UAC/BuE,WAAW,EAAErE,UAAU;UACvBsE,UAAU,EAAEpE,SAAS;UACrBE,OAAO,EAAEA,OAAO;UAChBmE,aAAa,EAAEjE;QACjB,CAAC;;QAED;QACA,MAAMmD,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC/C,WAAW,CAAEmC,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACda,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOX,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERzB,QAAQ,GAAG,MAAMrI,gBAAgB,CAACwL,eAAe,CAACX,iBAAiB,CAAC;QACpEF,aAAa,CAACF,gBAAgB,CAAC;MACjC;MAEA9C,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACA8D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAErD,QAAQ,CAAC5F,IAAI,CAAC;MAErCoF,kBAAkB,CAACQ,QAAQ,CAAC5F,IAAI,CAAC;;MAEjC;MACA,IAAI4F,QAAQ,CAAC5F,IAAI,CAACkJ,OAAO,IAAInC,MAAM,CAACC,IAAI,CAACpB,QAAQ,CAAC5F,IAAI,CAACkJ,OAAO,CAAC,CAAChI,MAAM,GAAG,CAAC,EAAE;QAC1EoE,oBAAoB,CAACyB,MAAM,CAACC,IAAI,CAACpB,QAAQ,CAAC5F,IAAI,CAACkJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;MAEAhN,OAAO,CAACiN,OAAO,CAAC,SAAS,CAAC;;MAE1B;MACA,IAAIvD,QAAQ,CAAC5F,IAAI,CAACoJ,WAAW,EAAE;QAC7BlN,OAAO,CAACsK,IAAI,CAAC,WAAWZ,QAAQ,CAAC5F,IAAI,CAACoJ,WAAW,EAAE,CAAC;MACtD;IAEF,CAAC,CAAC,OAAOvH,KAAU,EAAE;MACnBmH,OAAO,CAACnH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BmH,OAAO,CAACnH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC+D,QAAQ,CAAC;;MAEtC;MACA,IAAIyD,YAAY,GAAG,QAAQ;MAC3B,IAAIxH,KAAK,CAAC+D,QAAQ,EAAE;QAAA,IAAA0D,qBAAA,EAAAC,qBAAA;QAClB,KAAAD,qBAAA,GAAIzH,KAAK,CAAC+D,QAAQ,CAAC5F,IAAI,cAAAsJ,qBAAA,eAAnBA,qBAAA,CAAqBrD,MAAM,EAAE;UAC/BoD,YAAY,GAAGxH,KAAK,CAAC+D,QAAQ,CAAC5F,IAAI,CAACiG,MAAM;QAC3C,CAAC,MAAM,KAAAsD,qBAAA,GAAI1H,KAAK,CAAC+D,QAAQ,CAAC5F,IAAI,cAAAuJ,qBAAA,eAAnBA,qBAAA,CAAqBrN,OAAO,EAAE;UACvCmN,YAAY,GAAGxH,KAAK,CAAC+D,QAAQ,CAAC5F,IAAI,CAAC9D,OAAO;QAC5C,CAAC,MAAM,IAAI2F,KAAK,CAAC+D,QAAQ,CAAC4D,UAAU,EAAE;UACpCH,YAAY,GAAG,SAASxH,KAAK,CAAC+D,QAAQ,CAAC6D,MAAM,IAAI5H,KAAK,CAAC+D,QAAQ,CAAC4D,UAAU,EAAE;QAC9E;MACF,CAAC,MAAM,IAAI3H,KAAK,CAAC3F,OAAO,EAAE;QACxBmN,YAAY,GAAGxH,KAAK,CAAC3F,OAAO;MAC9B;MAEAA,OAAO,CAAC2F,KAAK,CAACwH,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRrE,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM0E,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIhI,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOsB,YAAY,IAAIU,aAAa,CAACxC,MAAM,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAOgC,MAAM,IAAII,YAAY,IAAII,aAAa,CAACxC,MAAM,GAAG,CAAC;IAC3D;EACF,CAAC;EAED,oBACEzD,OAAA;IAAAa,QAAA,gBACEb,OAAA,CAACC,KAAK;MAACiM,KAAK,EAAE,CAAE;MAAArL,QAAA,EAAC;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClCrB,OAAA,CAACE,IAAI;MAACmD,IAAI,EAAC,WAAW;MAAAxC,QAAA,EAAC;IAEvB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPrB,OAAA,CAACxB,OAAO;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXrB,OAAA,CAAChC,IAAI;MAACwD,KAAK,EAAC,oBAAK;MAAC2K,SAAS,EAAC,eAAe;MAAAtL,QAAA,eACzCb,OAAA,CAACzB,KAAK;QAACuC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BrB,OAAA,CAAC/B,KAAK,CAACmO,KAAK;YACV3K,KAAK,EAAEwC,UAAW;YAClB6E,QAAQ,EAAGuD,CAAC,IAAK/G,aAAa,CAAC+G,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;YAC/CT,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAExBb,OAAA,CAAC/B,KAAK;cAACwD,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCrB,OAAA,CAAC/B,KAAK;cAACwD,KAAK,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL4C,UAAU,KAAK,QAAQ,iBACtBjE,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBrB,OAAA,CAACG,OAAO;YAAA,GAAKsI,WAAW;YAAEzH,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAChDb,OAAA;cAAGmM,SAAS,EAAC,sBAAsB;cAAAtL,QAAA,eACjCb,OAAA,CAACZ,aAAa;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJrB,OAAA;cAAGmM,SAAS,EAAC,iBAAiB;cAAAtL,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDrB,OAAA;cAAGmM,SAAS,EAAC,iBAAiB;cAAAtL,QAAA,EAAC;YAE/B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGA4C,UAAU,KAAK,OAAO,iBACrBjE,OAAA,CAACzB,KAAK;UAACuC,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBACnDb,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAACkC,MAAM;cAAAvB,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BrB,OAAA,CAAC7B,KAAK;cACJsD,KAAK,EAAEgE,MAAO;cACdqD,QAAQ,EAAGuD,CAAC,IAAK3G,SAAS,CAAC2G,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;cAC3C8K,WAAW,EAAC,4BAAkB;cAC9BvL,KAAK,EAAE;gBAAEsB,SAAS,EAAE;cAAE;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrB,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAACkC,MAAM;cAAAvB,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBrB,OAAA,CAACtB,IAAI;cAAC8N,QAAQ,EAAEzG,YAAa;cAAAlF,QAAA,eAC3Bb,OAAA,CAAC5B,MAAM;gBACLqD,KAAK,EAAEoE,YAAa;gBACpBiD,QAAQ,EAAEhD,eAAgB;gBAC1ByG,WAAW,EAAC,mCAAU;gBACtBvL,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEqB,SAAS,EAAE;gBAAE,CAAE;gBACvCmK,OAAO,EAAE1G,YAAa;gBAAAlF,QAAA,EAErB8E,cAAc,CAACnD,GAAG,CAAEkK,IAAI,iBACvB1M,OAAA,CAACI,MAAM;kBAAYqB,KAAK,EAAEiL,IAAK;kBAAA7L,QAAA,EAC5B6L;gBAAI,GADMA,IAAI;kBAAAxL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAChC,IAAI;MAACwD,KAAK,EAAC,wDAAW;MAAC2K,SAAS,EAAC,eAAe;MAAAtL,QAAA,eAC/Cb,OAAA,CAACzB,KAAK;QAACuC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBrB,OAAA,CAAC5B,MAAM;YACLuO,IAAI,EAAC,UAAU;YACflL,KAAK,EAAEwE,aAAc;YACrB6C,QAAQ,EAAEG,oBAAqB;YAC/BsD,WAAW,EAAC,gCAAO;YACnBvL,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEqB,SAAS,EAAE;YAAE,CAAE;YAAAzB,QAAA,EAEtCiH,eAAe,CAACtF,GAAG,CAAE6G,IAAI,iBACxBrJ,OAAA,CAACI,MAAM;cAAYqB,KAAK,EAAE4H,IAAK;cAAAxI,QAAA,EAC5BwI;YAAI,GADMA,IAAI;cAAAnI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL4E,aAAa,CAACzD,GAAG,CAAE6G,IAAI,iBACtBrJ,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,GAAEwI,IAAI,EAAC,iCAAM;UAAA;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCrB,OAAA,CAACnB,QAAQ,CAACuN,KAAK;YACb3K,KAAK,EAAE0E,iBAAiB,CAACkD,IAAI,CAAC,IAAI,EAAG;YACrCP,QAAQ,EAAGa,SAAS,IAAKF,oBAAoB,CAACJ,IAAI,EAAEM,SAAqB,CAAE;YAC3E3I,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAE,CAAE;YAAAzB,QAAA,EAEvB,CAACkH,eAAe,CAACsB,IAAI,CAAiC,IAAI,EAAE,EAAE7G,GAAG,CAAEoK,QAAQ,iBAC1E5M,OAAA,CAACnB,QAAQ;cAAgB4C,KAAK,EAAEmL,QAAS;cAAA/L,QAAA,EACtC+L;YAAQ,GADIA,QAAQ;cAAA1L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA,GAZTgI,IAAI;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAChC,IAAI;MAACwD,KAAK,EAAC,sCAAQ;MAAC2K,SAAS,EAAC,eAAe;MAAAtL,QAAA,eAC5Cb,OAAA,CAACzB,KAAK;QAACuC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxBrB,OAAA;YAAKgB,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAG,CAAE;YAAAzB,QAAA,eAC5Bb,OAAA,CAACzB,KAAK;cAACuC,SAAS,EAAC,UAAU;cAACE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACnDb,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAE6E,YAAa;kBACpBwC,QAAQ,EAAGrH,KAAK,IAAK8E,eAAe,CAAC9E,KAAK,IAAI,KAAK,CAAE;kBACrDoL,GAAG,EAAE,MAAO;kBACZC,GAAG,EAAE,CAAE;kBACPC,IAAI,EAAE,MAAO;kBACb/L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrB,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAE+E,SAAU;kBACjBsC,QAAQ,EAAGrH,KAAK,IAAKgF,YAAY,CAAChF,KAAK,IAAI,EAAE,CAAE;kBAC/CoL,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACT9L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrB,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAEiF,MAAO;kBACdoC,QAAQ,EAAGrH,KAAK,IAAKkF,SAAS,CAAClF,KAAK,IAAI,EAAE,CAAE;kBAC5CoL,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,IAAK;kBACV9L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrB,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCrB,OAAA;YAAKgB,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAG,CAAE;YAAAzB,QAAA,eAC5Bb,OAAA,CAACzB,KAAK;cAACuC,SAAS,EAAC,UAAU;cAACE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACnDb,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAEmF,cAAe;kBACtBkC,QAAQ,EAAGrH,KAAK,IAAKoF,iBAAiB,CAACpF,KAAK,IAAI,EAAE,CAAE;kBACpDoL,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACT9L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrB,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAEqF,UAAW;kBAClBgC,QAAQ,EAAGrH,KAAK,IAAKsF,aAAa,CAACtF,KAAK,IAAI,EAAE,CAAE;kBAChDoL,GAAG,EAAE,EAAG;kBACRC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,EAAG;kBACT/L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrB,OAAA;gBAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;kBAAAW,QAAA,EAAC;gBAAG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChBrB,OAAA,CAACrB,WAAW;kBACV8C,KAAK,EAAEuF,SAAU;kBACjB8B,QAAQ,EAAGrH,KAAK,IAAKwF,YAAY,CAACxF,KAAK,IAAI,CAAC,CAAE;kBAC9CoL,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,EAAG;kBACR9L,KAAK,EAAE;oBAAEgM,UAAU,EAAE;kBAAE;gBAAE;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrB,OAAA;gBAAKgB,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,gBAC5Bb,OAAA,CAACE,IAAI;kBAAAW,QAAA,GAAC,4BAAW,EAACqG,OAAO;gBAAA;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCrB,OAAA,CAACpB,MAAM;kBACL6C,KAAK,EAAEyF,OAAQ;kBACf4B,QAAQ,EAAE3B,UAAW;kBACrB0F,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,IAAK;kBACX/L,KAAK,EAAE;oBAAEsB,SAAS,EAAE;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrB,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAACkC,MAAM;YAAAvB,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BrB,OAAA,CAAC7B,KAAK;YACJsD,KAAK,EAAE2F,YAAa;YACpB0B,QAAQ,EAAGuD,CAAC,IAAKhF,eAAe,CAACgF,CAAC,CAACC,MAAM,CAAC7K,KAAK,CAAE;YACjD8K,WAAW,EAAC,4BAAkB;YAC9BvL,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAE;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAChC,IAAI;MAACmO,SAAS,EAAC,eAAe;MAAAtL,QAAA,gBAC7Bb,OAAA,CAAC3B,MAAM;QACLgF,IAAI,EAAC,SAAS;QACdtC,IAAI,EAAC,OAAO;QACZkM,IAAI,eAAEjN,OAAA,CAACX,kBAAkB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B6L,OAAO,EAAErD,mBAAoB;QAC7B4C,OAAO,EAAEnF,QAAS;QAClB6F,QAAQ,EAAE,CAAClB,WAAW,CAAC,CAAE;QACzBE,SAAS,EAAC,eAAe;QAAAtL,QAAA,EAExByG,QAAQ,GAAG,SAAS,GAAG;MAAQ;QAAApG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAGRiG,QAAQ,iBACPtH,OAAA;QAAKmM,SAAS,EAAC,kBAAkB;QAAAtL,QAAA,gBAC/Bb,OAAA,CAACE,IAAI;UAAAW,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBrB,OAAA,CAAClB,QAAQ;UAACsO,OAAO,EAAE5F,QAAS;UAACwE,MAAM,EAAC;QAAQ;UAAA9K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAGAqG,eAAe,IAAIA,eAAe,CAAC+D,OAAO,iBACzCzL,OAAA;QAAKgB,KAAK,EAAE;UAAEsB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAC5Bb,OAAA,CAACjB,KAAK;UACJN,OAAO,EAAC,0BAAM;UACdmG,WAAW,eACT5E,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAAa,QAAA,EAAG;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACfqG,eAAe,CAACiE,WAAW,iBAC1B3L,OAAA;cAAAa,QAAA,gBAAGb,OAAA;gBAAAa,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACqG,eAAe,CAACiE,WAAW;YAAA;cAAAzK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7D,EACAiI,MAAM,CAAC+D,OAAO,CAAC3F,eAAe,CAAC+D,OAAO,CAAC,CAACjJ,GAAG,CAAC,CAAC,CAAC0B,GAAG,EAAE3D,MAAM,CAAgB,kBACxEP,OAAA;cAAegB,KAAK,EAAE;gBAAEsB,SAAS,EAAE;cAAE,CAAE;cAAAzB,QAAA,gBACrCb,OAAA;gBAAAa,QAAA,gBAAGb,OAAA;kBAAAa,QAAA,EAAQ;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC6C,GAAG;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCrB,OAAA;gBAAAa,QAAA,GAAG,wCAAQ,EAACN,MAAM,CAACoE,eAAe;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCrB,OAAA;gBAAAa,QAAA,GAAG,oDAAU,EAACN,MAAM,CAACsE,kBAAkB;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHpC6C,GAAG;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;UACDgC,IAAI,EAAC,SAAS;UACd6B,QAAQ;UACRlE,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAG;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGFqG,eAAe,IAAIA,eAAe,CAAC+D,OAAO,IAAInC,MAAM,CAACC,IAAI,CAAC7B,eAAe,CAAC+D,OAAO,CAAC,CAAChI,MAAM,GAAG,CAAC,iBAC5FzD,OAAA,CAAChC,IAAI;QAACwD,KAAK,EAAC,gFAAe;QAAC2K,SAAS,EAAC,eAAe;QAAAtL,QAAA,eACnDb,OAAA,CAACzB,KAAK;UAACuC,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAChEb,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAACkC,MAAM;cAAAvB,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCrB,OAAA,CAAC5B,MAAM;cACLqD,KAAK,EAAEmG,iBAAkB;cACzBkB,QAAQ,EAAEjB,oBAAqB;cAC/B7G,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEqB,SAAS,EAAE;cAAE,CAAE;cACvCiK,WAAW,EAAC,8DAAY;cAAA1L,QAAA,EAEvByI,MAAM,CAACC,IAAI,CAAC7B,eAAe,CAAC+D,OAAO,CAAC,CAACjJ,GAAG,CAAE0B,GAAG,iBAC5ClE,OAAA,CAACI,MAAM;gBAAWqB,KAAK,EAAEyC,GAAI;gBAAArD,QAAA,EAC1BqD;cAAG,GADOA,GAAG;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELuG,iBAAiB,IAAIF,eAAe,CAAC+D,OAAO,CAAC7D,iBAAiB,CAAC,iBAC9D5H,OAAA,CAACK,qBAAqB;YACpBC,SAAS,EAAEsH,iBAAkB;YAC7BrH,MAAM,EAAEmH,eAAe,CAAC+D,OAAO,CAAC7D,iBAAiB;UAAE;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACgE,EAAA,CApiBID,iBAA2B;AAAAkI,GAAA,GAA3BlI,iBAA2B;AAsiBjC,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAmI,GAAA;AAAAC,YAAA,CAAApI,EAAA;AAAAoI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}