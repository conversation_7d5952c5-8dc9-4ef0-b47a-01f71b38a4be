{"ast": null, "code": "import Cascader from './Cascader';\nexport default Cascader;", "map": {"version": 3, "names": ["<PERSON>r"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-cascader/es/index.js"], "sourcesContent": ["import Cascader from './Cascader';\nexport default Cascader;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}