{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdFreedmanDiaconis;\nvar _count = _interopRequireDefault(require(\"../count.js\"));\nvar _quantile = _interopRequireDefault(require(\"../quantile.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction thresholdFreedmanDiaconis(values, min, max) {\n  return Math.ceil((max - min) / (2 * ((0, _quantile.default)(values, 0.75) - (0, _quantile.default)(values, 0.25)) * Math.pow((0, _count.default)(values), -1 / 3)));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "thresholdFreedman<PERSON><PERSON><PERSON><PERSON>", "_count", "_interopRequireDefault", "require", "_quantile", "obj", "__esModule", "values", "min", "max", "Math", "ceil", "pow"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/threshold/freedmanDiaconis.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdFreedmanDiaconis;\n\nvar _count = _interopRequireDefault(require(\"../count.js\"));\n\nvar _quantile = _interopRequireDefault(require(\"../quantile.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction thresholdFreedmanDiaconis(values, min, max) {\n  return Math.ceil((max - min) / (2 * ((0, _quantile.default)(values, 0.75) - (0, _quantile.default)(values, 0.25)) * Math.pow((0, _count.default)(values), -1 / 3)));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,yBAAyB;AAE3C,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE3D,IAAIC,SAAS,GAAGF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAEjE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,yBAAyBA,CAACO,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACnD,OAAOC,IAAI,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGD,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAEJ,SAAS,CAACL,OAAO,EAAEQ,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEH,SAAS,CAACL,OAAO,EAAEQ,MAAM,EAAE,IAAI,CAAC,CAAC,GAAGG,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAAEX,MAAM,CAACF,OAAO,EAAEQ,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}