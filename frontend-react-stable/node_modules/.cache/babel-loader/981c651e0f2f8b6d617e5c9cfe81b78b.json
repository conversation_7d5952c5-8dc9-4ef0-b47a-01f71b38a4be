{"ast": null, "code": "var camel2hyphen = require('string-convert/camel2hyphen');\nvar isDimension = function (feature) {\n  var re = /[height|width]$/;\n  return re.test(feature);\n};\nvar obj2mq = function (obj) {\n  var mq = '';\n  var features = Object.keys(obj);\n  features.forEach(function (feature, index) {\n    var value = obj[feature];\n    feature = camel2hyphen(feature);\n    // Add px to dimension features\n    if (isDimension(feature) && typeof value === 'number') {\n      value = value + 'px';\n    }\n    if (value === true) {\n      mq += feature;\n    } else if (value === false) {\n      mq += 'not ' + feature;\n    } else {\n      mq += '(' + feature + ': ' + value + ')';\n    }\n    if (index < features.length - 1) {\n      mq += ' and ';\n    }\n  });\n  return mq;\n};\nvar json2mq = function (query) {\n  var mq = '';\n  if (typeof query === 'string') {\n    return query;\n  }\n  // Handling array of media queries\n  if (query instanceof Array) {\n    query.forEach(function (q, index) {\n      mq += obj2mq(q);\n      if (index < query.length - 1) {\n        mq += ', ';\n      }\n    });\n    return mq;\n  }\n  // Handling single media query\n  return obj2mq(query);\n};\nmodule.exports = json2mq;", "map": {"version": 3, "names": ["camel2hyphen", "require", "isDimension", "feature", "re", "test", "obj2mq", "obj", "mq", "features", "Object", "keys", "for<PERSON>ach", "index", "value", "length", "json2mq", "query", "Array", "q", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/json2mq/index.js"], "sourcesContent": ["var camel2hyphen = require('string-convert/camel2hyphen');\n\nvar isDimension = function (feature) {\n  var re = /[height|width]$/;\n  return re.test(feature);\n};\n\nvar obj2mq = function (obj) {\n  var mq = '';\n  var features = Object.keys(obj);\n  features.forEach(function (feature, index) {\n    var value = obj[feature];\n    feature = camel2hyphen(feature);\n    // Add px to dimension features\n    if (isDimension(feature) && typeof value === 'number') {\n      value = value + 'px';\n    }\n    if (value === true) {\n      mq += feature;\n    } else if (value === false) {\n      mq += 'not ' + feature;\n    } else {\n      mq += '(' + feature + ': ' + value + ')';\n    }\n    if (index < features.length-1) {\n      mq += ' and '\n    }\n  });\n  return mq;\n};\n\nvar json2mq = function (query) {\n  var mq = '';\n  if (typeof query === 'string') {\n    return query;\n  }\n  // Handling array of media queries\n  if (query instanceof Array) {\n    query.forEach(function (q, index) {\n      mq += obj2mq(q);\n      if (index < query.length-1) {\n        mq += ', '\n      }\n    });\n    return mq;\n  }\n  // Handling single media query\n  return obj2mq(query);\n};\n\nmodule.exports = json2mq;"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AAEzD,IAAIC,WAAW,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACnC,IAAIC,EAAE,GAAG,iBAAiB;EAC1B,OAAOA,EAAE,CAACC,IAAI,CAACF,OAAO,CAAC;AACzB,CAAC;AAED,IAAIG,MAAM,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAC1B,IAAIC,EAAE,GAAG,EAAE;EACX,IAAIC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC;EAC/BE,QAAQ,CAACG,OAAO,CAAC,UAAUT,OAAO,EAAEU,KAAK,EAAE;IACzC,IAAIC,KAAK,GAAGP,GAAG,CAACJ,OAAO,CAAC;IACxBA,OAAO,GAAGH,YAAY,CAACG,OAAO,CAAC;IAC/B;IACA,IAAID,WAAW,CAACC,OAAO,CAAC,IAAI,OAAOW,KAAK,KAAK,QAAQ,EAAE;MACrDA,KAAK,GAAGA,KAAK,GAAG,IAAI;IACtB;IACA,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBN,EAAE,IAAIL,OAAO;IACf,CAAC,MAAM,IAAIW,KAAK,KAAK,KAAK,EAAE;MAC1BN,EAAE,IAAI,MAAM,GAAGL,OAAO;IACxB,CAAC,MAAM;MACLK,EAAE,IAAI,GAAG,GAAGL,OAAO,GAAG,IAAI,GAAGW,KAAK,GAAG,GAAG;IAC1C;IACA,IAAID,KAAK,GAAGJ,QAAQ,CAACM,MAAM,GAAC,CAAC,EAAE;MAC7BP,EAAE,IAAI,OAAO;IACf;EACF,CAAC,CAAC;EACF,OAAOA,EAAE;AACX,CAAC;AAED,IAAIQ,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC7B,IAAIT,EAAE,GAAG,EAAE;EACX,IAAI,OAAOS,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA;EACA,IAAIA,KAAK,YAAYC,KAAK,EAAE;IAC1BD,KAAK,CAACL,OAAO,CAAC,UAAUO,CAAC,EAAEN,KAAK,EAAE;MAChCL,EAAE,IAAIF,MAAM,CAACa,CAAC,CAAC;MACf,IAAIN,KAAK,GAAGI,KAAK,CAACF,MAAM,GAAC,CAAC,EAAE;QAC1BP,EAAE,IAAI,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAOA,EAAE;EACX;EACA;EACA,OAAOF,MAAM,CAACW,KAAK,CAAC;AACtB,CAAC;AAEDG,MAAM,CAACC,OAAO,GAAGL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}