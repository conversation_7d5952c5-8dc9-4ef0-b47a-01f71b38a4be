{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport { getMotion } from '../utils/motionUtil';\nimport MenuContextProvider, { MenuContext } from '../context/MenuContext';\nimport SubMenuList from './SubMenuList';\nexport default function InlineSubMenuList(_ref) {\n  var id = _ref.id,\n    open = _ref.open,\n    keyPath = _ref.keyPath,\n    children = _ref.children;\n  var fixedMode = 'inline';\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions,\n    mode = _React$useContext.mode; // Always use latest mode check\n\n  var sameModeRef = React.useRef(false);\n  sameModeRef.current = mode === fixedMode; // We record `destroy` mark here since when mode change from `inline` to others.\n  // The inline list should remove when motion end.\n\n  var _React$useState = React.useState(!sameModeRef.current),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    destroy = _React$useState2[0],\n    setDestroy = _React$useState2[1];\n  var mergedOpen = sameModeRef.current ? open : false; // ================================= Effect =================================\n  // Reset destroy state when mode change back\n\n  React.useEffect(function () {\n    if (sameModeRef.current) {\n      setDestroy(false);\n    }\n  }, [mode]); // ================================= Render =================================\n\n  var mergedMotion = _objectSpread({}, getMotion(fixedMode, motion, defaultMotions)); // No need appear since nest inlineCollapse changed\n\n  if (keyPath.length > 1) {\n    mergedMotion.motionAppear = false;\n  } // Hide inline list when mode changed and motion end\n\n  var originOnVisibleChanged = mergedMotion.onVisibleChanged;\n  mergedMotion.onVisibleChanged = function (newVisible) {\n    if (!sameModeRef.current && !newVisible) {\n      setDestroy(true);\n    }\n    return originOnVisibleChanged === null || originOnVisibleChanged === void 0 ? void 0 : originOnVisibleChanged(newVisible);\n  };\n  if (destroy) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    mode: fixedMode,\n    locked: !sameModeRef.current\n  }, /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: mergedOpen\n  }, mergedMotion, {\n    forceRender: forceSubMenuRender,\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n  }), function (_ref2) {\n    var motionClassName = _ref2.className,\n      motionStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(SubMenuList, {\n      id: id,\n      className: motionClassName,\n      style: motionStyle\n    }, children);\n  }));\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "React", "CSSMotion", "getMotion", "MenuContextProvider", "MenuContext", "SubMenuList", "InlineSubMenuList", "_ref", "id", "open", "keyP<PERSON>", "children", "fixedMode", "_React$useContext", "useContext", "prefixCls", "forceSubMenuRender", "motion", "defaultMotions", "mode", "sameModeRef", "useRef", "current", "_React$useState", "useState", "_React$useState2", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "mergedOpen", "useEffect", "mergedMotion", "length", "motionAppear", "originOnVisibleChanged", "onVisibleChanged", "newVisible", "createElement", "locked", "visible", "forceRender", "removeOnLeave", "leavedClassName", "concat", "_ref2", "motionClassName", "className", "motionStyle", "style"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/SubMenu/InlineSubMenuList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport { getMotion } from '../utils/motionUtil';\nimport MenuContextProvider, { MenuContext } from '../context/MenuContext';\nimport SubMenuList from './SubMenuList';\nexport default function InlineSubMenuList(_ref) {\n  var id = _ref.id,\n      open = _ref.open,\n      keyPath = _ref.keyPath,\n      children = _ref.children;\n  var fixedMode = 'inline';\n\n  var _React$useContext = React.useContext(MenuContext),\n      prefixCls = _React$useContext.prefixCls,\n      forceSubMenuRender = _React$useContext.forceSubMenuRender,\n      motion = _React$useContext.motion,\n      defaultMotions = _React$useContext.defaultMotions,\n      mode = _React$useContext.mode; // Always use latest mode check\n\n\n  var sameModeRef = React.useRef(false);\n  sameModeRef.current = mode === fixedMode; // We record `destroy` mark here since when mode change from `inline` to others.\n  // The inline list should remove when motion end.\n\n  var _React$useState = React.useState(!sameModeRef.current),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      destroy = _React$useState2[0],\n      setDestroy = _React$useState2[1];\n\n  var mergedOpen = sameModeRef.current ? open : false; // ================================= Effect =================================\n  // Reset destroy state when mode change back\n\n  React.useEffect(function () {\n    if (sameModeRef.current) {\n      setDestroy(false);\n    }\n  }, [mode]); // ================================= Render =================================\n\n  var mergedMotion = _objectSpread({}, getMotion(fixedMode, motion, defaultMotions)); // No need appear since nest inlineCollapse changed\n\n\n  if (keyPath.length > 1) {\n    mergedMotion.motionAppear = false;\n  } // Hide inline list when mode changed and motion end\n\n\n  var originOnVisibleChanged = mergedMotion.onVisibleChanged;\n\n  mergedMotion.onVisibleChanged = function (newVisible) {\n    if (!sameModeRef.current && !newVisible) {\n      setDestroy(true);\n    }\n\n    return originOnVisibleChanged === null || originOnVisibleChanged === void 0 ? void 0 : originOnVisibleChanged(newVisible);\n  };\n\n  if (destroy) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    mode: fixedMode,\n    locked: !sameModeRef.current\n  }, /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: mergedOpen\n  }, mergedMotion, {\n    forceRender: forceSubMenuRender,\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n  }), function (_ref2) {\n    var motionClassName = _ref2.className,\n        motionStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(SubMenuList, {\n      id: id,\n      className: motionClassName,\n      style: motionStyle\n    }, children);\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAOC,mBAAmB,IAAIC,WAAW,QAAQ,wBAAwB;AACzE,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IACZC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC5B,IAAIC,SAAS,GAAG,QAAQ;EAExB,IAAIC,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACV,WAAW,CAAC;IACjDW,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,kBAAkB,GAAGH,iBAAiB,CAACG,kBAAkB;IACzDC,MAAM,GAAGJ,iBAAiB,CAACI,MAAM;IACjCC,cAAc,GAAGL,iBAAiB,CAACK,cAAc;IACjDC,IAAI,GAAGN,iBAAiB,CAACM,IAAI,CAAC,CAAC;;EAGnC,IAAIC,WAAW,GAAGpB,KAAK,CAACqB,MAAM,CAAC,KAAK,CAAC;EACrCD,WAAW,CAACE,OAAO,GAAGH,IAAI,KAAKP,SAAS,CAAC,CAAC;EAC1C;;EAEA,IAAIW,eAAe,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,CAACJ,WAAW,CAACE,OAAO,CAAC;IACtDG,gBAAgB,GAAG1B,cAAc,CAACwB,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIG,UAAU,GAAGR,WAAW,CAACE,OAAO,GAAGb,IAAI,GAAG,KAAK,CAAC,CAAC;EACrD;;EAEAT,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1B,IAAIT,WAAW,CAACE,OAAO,EAAE;MACvBK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAIW,YAAY,GAAGhC,aAAa,CAAC,CAAC,CAAC,EAAEI,SAAS,CAACU,SAAS,EAAEK,MAAM,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAGpF,IAAIR,OAAO,CAACqB,MAAM,GAAG,CAAC,EAAE;IACtBD,YAAY,CAACE,YAAY,GAAG,KAAK;EACnC,CAAC,CAAC;;EAGF,IAAIC,sBAAsB,GAAGH,YAAY,CAACI,gBAAgB;EAE1DJ,YAAY,CAACI,gBAAgB,GAAG,UAAUC,UAAU,EAAE;IACpD,IAAI,CAACf,WAAW,CAACE,OAAO,IAAI,CAACa,UAAU,EAAE;MACvCR,UAAU,CAAC,IAAI,CAAC;IAClB;IAEA,OAAOM,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACE,UAAU,CAAC;EAC3H,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,OAAO,IAAI;EACb;EAEA,OAAO,aAAa1B,KAAK,CAACoC,aAAa,CAACjC,mBAAmB,EAAE;IAC3DgB,IAAI,EAAEP,SAAS;IACfyB,MAAM,EAAE,CAACjB,WAAW,CAACE;EACvB,CAAC,EAAE,aAAatB,KAAK,CAACoC,aAAa,CAACnC,SAAS,EAAEJ,QAAQ,CAAC;IACtDyC,OAAO,EAAEV;EACX,CAAC,EAAEE,YAAY,EAAE;IACfS,WAAW,EAAEvB,kBAAkB;IAC/BwB,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,EAAE,CAACC,MAAM,CAAC3B,SAAS,EAAE,SAAS;EACjD,CAAC,CAAC,EAAE,UAAU4B,KAAK,EAAE;IACnB,IAAIC,eAAe,GAAGD,KAAK,CAACE,SAAS;MACjCC,WAAW,GAAGH,KAAK,CAACI,KAAK;IAC7B,OAAO,aAAa/C,KAAK,CAACoC,aAAa,CAAC/B,WAAW,EAAE;MACnDG,EAAE,EAAEA,EAAE;MACNqC,SAAS,EAAED,eAAe;MAC1BG,KAAK,EAAED;IACT,CAAC,EAAEnC,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module"}