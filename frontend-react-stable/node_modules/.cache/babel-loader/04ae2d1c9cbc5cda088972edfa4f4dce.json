{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.x = x;\nexports.y = y;\nfunction x(p) {\n  return p[0];\n}\nfunction y(p) {\n  return p[1];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "x", "y", "p"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/point.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.x = x;\nexports.y = y;\n\nfunction x(p) {\n  return p[0];\n}\n\nfunction y(p) {\n  return p[1];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,CAAC,GAAGA,CAAC;AACbF,OAAO,CAACG,CAAC,GAAGA,CAAC;AAEb,SAASD,CAACA,CAACE,CAAC,EAAE;EACZ,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,SAASD,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script"}