{"ast": null, "code": "// This icon file is generated automatically.\nvar CloseSquareTwoTone = {\n  \"icon\": function render(primaryColor, secondaryColor) {\n    return {\n      \"tag\": \"svg\",\n      \"attrs\": {\n        \"viewBox\": \"64 64 896 896\",\n        \"focusable\": \"false\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z\",\n          \"fill\": primaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M184 840h656V184H184v656zm163.9-473.9A7.95 7.95 0 01354 353h58.9c4.7 0 9.2 2.1 12.3 5.7L512 462.2l86.8-103.5c3-3.6 7.5-5.7 12.3-5.7H670c6.8 0 10.5 7.9 6.1 13.1L553.8 512l122.3 145.9c4.4 5.2.7 13.1-6.1 13.1h-58.9c-4.7 0-9.2-2.1-12.3-5.7L512 561.8l-86.8 103.5c-3 3.6-7.5 5.7-12.3 5.7H354c-6.8 0-10.5-7.9-6.1-13.1L470.2 512 347.9 366.1z\",\n          \"fill\": secondaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M354 671h58.9c4.8 0 9.3-2.1 12.3-5.7L512 561.8l86.8 103.5c3.1 3.6 7.6 5.7 12.3 5.7H670c6.8 0 10.5-7.9 6.1-13.1L553.8 512l122.3-145.9c4.4-5.2.7-13.1-6.1-13.1h-58.9c-4.8 0-9.3 2.1-12.3 5.7L512 462.2l-86.8-103.5c-3.1-3.6-7.6-5.7-12.3-5.7H354c-6.8 0-10.5 7.9-6.1 13.1L470.2 512 347.9 657.9A7.95 7.95 0 00354 671z\",\n          \"fill\": primaryColor\n        }\n      }]\n    };\n  },\n  \"name\": \"close-square\",\n  \"theme\": \"twotone\"\n};\nexport default CloseSquareTwoTone;", "map": {"version": 3, "names": ["CloseSquareTwoTone", "render", "primaryColor", "secondaryColor"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/CloseSquareTwoTone.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CloseSquareTwoTone = { \"icon\": function render(primaryColor, secondaryColor) { return { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z\", \"fill\": primaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M184 840h656V184H184v656zm163.9-473.9A7.95 7.95 0 01354 353h58.9c4.7 0 9.2 2.1 12.3 5.7L512 462.2l86.8-103.5c3-3.6 7.5-5.7 12.3-5.7H670c6.8 0 10.5 7.9 6.1 13.1L553.8 512l122.3 145.9c4.4 5.2.7 13.1-6.1 13.1h-58.9c-4.7 0-9.2-2.1-12.3-5.7L512 561.8l-86.8 103.5c-3 3.6-7.5 5.7-12.3 5.7H354c-6.8 0-10.5-7.9-6.1-13.1L470.2 512 347.9 366.1z\", \"fill\": secondaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M354 671h58.9c4.8 0 9.3-2.1 12.3-5.7L512 561.8l86.8 103.5c3.1 3.6 7.6 5.7 12.3 5.7H670c6.8 0 10.5-7.9 6.1-13.1L553.8 512l122.3-145.9c4.4-5.2.7-13.1-6.1-13.1h-58.9c-4.8 0-9.3 2.1-12.3 5.7L512 462.2l-86.8-103.5c-3.1-3.6-7.6-5.7-12.3-5.7H354c-6.8 0-10.5 7.9-6.1 13.1L470.2 512 347.9 657.9A7.95 7.95 0 00354 671z\", \"fill\": primaryColor } }] }; }, \"name\": \"close-square\", \"theme\": \"twotone\" };\nexport default CloseSquareTwoTone;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE,SAASC,MAAMA,CAACC,YAAY,EAAEC,cAAc,EAAE;IAAE,OAAO;MAAE,KAAK,EAAE,KAAK;MAAE,OAAO,EAAE;QAAE,SAAS,EAAE,eAAe;QAAE,WAAW,EAAE;MAAQ,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,yIAAyI;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,+UAA+U;UAAE,MAAM,EAAEC;QAAe;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,sTAAsT;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,cAAc;EAAE,OAAO,EAAE;AAAU,CAAC;AAC/qC,eAAeF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}