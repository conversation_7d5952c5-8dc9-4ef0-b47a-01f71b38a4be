{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = diverging;\nexports.divergingLog = divergingLog;\nexports.divergingPow = divergingPow;\nexports.divergingSqrt = divergingSqrt;\nexports.divergingSymlog = divergingSymlog;\nvar _index = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nvar _linear = require(\"./linear.js\");\nvar _log = require(\"./log.js\");\nvar _sequential = require(\"./sequential.js\");\nvar _symlog = require(\"./symlog.js\");\nvar _pow = require(\"./pow.js\");\nfunction transformer() {\n  var x0 = 0,\n    x1 = 0.5,\n    x2 = 1,\n    s = 1,\n    t0,\n    t1,\n    t2,\n    k10,\n    k21,\n    interpolator = _continuous.identity,\n    transform,\n    clamp = false,\n    unknown;\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = (0, _index.piecewise)(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n  scale.range = range(_index.interpolate);\n  scale.rangeRound = range(_index.interpolateRound);\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\nfunction diverging() {\n  var scale = (0, _linear.linearish)(transformer()(_continuous.identity));\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, diverging());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction divergingLog() {\n  var scale = (0, _log.loggish)(transformer()).domain([0.1, 1, 10]);\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingLog()).base(scale.base());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction divergingSymlog() {\n  var scale = (0, _symlog.symlogish)(transformer());\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingSymlog()).constant(scale.constant());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction divergingPow() {\n  var scale = (0, _pow.powish)(transformer());\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingPow()).exponent(scale.exponent());\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}\nfunction divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "diverging", "divergingLog", "divergingPow", "divergingSqrt", "divergingSymlog", "_index", "require", "_continuous", "_init", "_linear", "_log", "_sequential", "_symlog", "_pow", "transformer", "x0", "x1", "x2", "s", "t0", "t1", "t2", "k10", "k21", "interpolator", "identity", "transform", "clamp", "unknown", "scale", "x", "isNaN", "Math", "max", "min", "domain", "_", "arguments", "length", "range", "interpolate", "r0", "r1", "r2", "piecewise", "rangeRound", "interpolateRound", "t", "linearish", "copy", "initInterpolator", "apply", "loggish", "base", "symlogish", "constant", "powish", "exponent"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/diverging.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = diverging;\nexports.divergingLog = divergingLog;\nexports.divergingPow = divergingPow;\nexports.divergingSqrt = divergingSqrt;\nexports.divergingSymlog = divergingSymlog;\n\nvar _index = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nvar _linear = require(\"./linear.js\");\n\nvar _log = require(\"./log.js\");\n\nvar _sequential = require(\"./sequential.js\");\n\nvar _symlog = require(\"./symlog.js\");\n\nvar _pow = require(\"./pow.js\");\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = _continuous.identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = (0, _index.piecewise)(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(_index.interpolate);\n  scale.rangeRound = range(_index.interpolateRound);\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nfunction diverging() {\n  var scale = (0, _linear.linearish)(transformer()(_continuous.identity));\n\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, diverging());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingLog() {\n  var scale = (0, _log.loggish)(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingLog()).base(scale.base());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSymlog() {\n  var scale = (0, _symlog.symlogish)(transformer());\n\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingPow() {\n  var scale = (0, _pow.powish)(transformer());\n\n  scale.copy = function () {\n    return (0, _sequential.copy)(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAC3BH,OAAO,CAACI,YAAY,GAAGA,YAAY;AACnCJ,OAAO,CAACK,YAAY,GAAGA,YAAY;AACnCL,OAAO,CAACM,aAAa,GAAGA,aAAa;AACrCN,OAAO,CAACO,eAAe,GAAGA,eAAe;AAEzC,IAAIC,MAAM,GAAGC,OAAO,CAAC,iDAAiD,CAAC;AAEvE,IAAIC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIG,OAAO,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAII,IAAI,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAE9B,IAAIK,WAAW,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIM,OAAO,GAAGN,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIO,IAAI,GAAGP,OAAO,CAAC,UAAU,CAAC;AAE9B,SAASQ,WAAWA,CAAA,EAAG;EACrB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,GAAG;IACRC,EAAE,GAAG,CAAC;IACNC,CAAC,GAAG,CAAC;IACLC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,GAAG;IACHC,GAAG;IACHC,YAAY,GAAGjB,WAAW,CAACkB,QAAQ;IACnCC,SAAS;IACTC,KAAK,GAAG,KAAK;IACbC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,IAAIE,CAAC,GAAG,GAAG,GAAG,CAAC,CAACA,CAAC,GAAG,CAACJ,SAAS,CAACI,CAAC,CAAC,IAAIV,EAAE,KAAKF,CAAC,GAAGY,CAAC,GAAGZ,CAAC,GAAGE,EAAE,GAAGE,GAAG,GAAGC,GAAG,CAAC,EAAEC,YAAY,CAACG,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EAC/J;EAEAD,KAAK,CAACM,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAI,CAACvB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGmB,CAAC,EAAEjB,EAAE,GAAGO,SAAS,CAACX,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,EAAE,GAAGM,SAAS,CAACV,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,EAAE,GAAGK,SAAS,CAACT,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEI,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEF,CAAC,GAAGE,EAAE,GAAGD,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEU,KAAK,IAAI,CAACd,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACtP,CAAC;EAEDY,KAAK,CAACF,KAAK,GAAG,UAAUS,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIX,KAAK,GAAG,CAAC,CAACS,CAAC,EAAEP,KAAK,IAAIF,KAAK;EACxD,CAAC;EAEDE,KAAK,CAACL,YAAY,GAAG,UAAUY,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAId,YAAY,GAAGY,CAAC,EAAEP,KAAK,IAAIL,YAAY;EACpE,CAAC;EAED,SAASe,KAAKA,CAACC,WAAW,EAAE;IAC1B,OAAO,UAAUJ,CAAC,EAAE;MAClB,IAAIK,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,OAAON,SAAS,CAACC,MAAM,IAAI,CAACG,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGP,CAAC,EAAEZ,YAAY,GAAG,CAAC,CAAC,EAAEnB,MAAM,CAACuC,SAAS,EAAEJ,WAAW,EAAE,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC,EAAEd,KAAK,IAAI,CAACL,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9K,CAAC;EACH;EAEAK,KAAK,CAACU,KAAK,GAAGA,KAAK,CAAClC,MAAM,CAACmC,WAAW,CAAC;EACvCX,KAAK,CAACgB,UAAU,GAAGN,KAAK,CAAClC,MAAM,CAACyC,gBAAgB,CAAC;EAEjDjB,KAAK,CAACD,OAAO,GAAG,UAAUQ,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIV,OAAO,GAAGQ,CAAC,EAAEP,KAAK,IAAID,OAAO;EAC1D,CAAC;EAED,OAAO,UAAUmB,CAAC,EAAE;IAClBrB,SAAS,GAAGqB,CAAC,EAAE5B,EAAE,GAAG4B,CAAC,CAAChC,EAAE,CAAC,EAAEK,EAAE,GAAG2B,CAAC,CAAC/B,EAAE,CAAC,EAAEK,EAAE,GAAG0B,CAAC,CAAC9B,EAAE,CAAC,EAAEK,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEI,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEF,CAAC,GAAGE,EAAE,GAAGD,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IACrJ,OAAOU,KAAK;EACd,CAAC;AACH;AAEA,SAAS7B,SAASA,CAAA,EAAG;EACnB,IAAI6B,KAAK,GAAG,CAAC,CAAC,EAAEpB,OAAO,CAACuC,SAAS,EAAElC,WAAW,CAAC,CAAC,CAACP,WAAW,CAACkB,QAAQ,CAAC,CAAC;EAEvEI,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEtC,WAAW,CAACsC,IAAI,EAAEpB,KAAK,EAAE7B,SAAS,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,OAAOQ,KAAK,CAAC0C,gBAAgB,CAACC,KAAK,CAACtB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAASpC,YAAYA,CAAA,EAAG;EACtB,IAAI4B,KAAK,GAAG,CAAC,CAAC,EAAEnB,IAAI,CAAC0C,OAAO,EAAEtC,WAAW,CAAC,CAAC,CAAC,CAACqB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EAEjEN,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEtC,WAAW,CAACsC,IAAI,EAAEpB,KAAK,EAAE5B,YAAY,CAAC,CAAC,CAAC,CAACoD,IAAI,CAACxB,KAAK,CAACwB,IAAI,CAAC,CAAC,CAAC;EACxE,CAAC;EAED,OAAO7C,KAAK,CAAC0C,gBAAgB,CAACC,KAAK,CAACtB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAASjC,eAAeA,CAAA,EAAG;EACzB,IAAIyB,KAAK,GAAG,CAAC,CAAC,EAAEjB,OAAO,CAAC0C,SAAS,EAAExC,WAAW,CAAC,CAAC,CAAC;EAEjDe,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEtC,WAAW,CAACsC,IAAI,EAAEpB,KAAK,EAAEzB,eAAe,CAAC,CAAC,CAAC,CAACmD,QAAQ,CAAC1B,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;EACnF,CAAC;EAED,OAAO/C,KAAK,CAAC0C,gBAAgB,CAACC,KAAK,CAACtB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAASnC,YAAYA,CAAA,EAAG;EACtB,IAAI2B,KAAK,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAAC2C,MAAM,EAAE1C,WAAW,CAAC,CAAC,CAAC;EAE3Ce,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEtC,WAAW,CAACsC,IAAI,EAAEpB,KAAK,EAAE3B,YAAY,CAAC,CAAC,CAAC,CAACuD,QAAQ,CAAC5B,KAAK,CAAC4B,QAAQ,CAAC,CAAC,CAAC;EAChF,CAAC;EAED,OAAOjD,KAAK,CAAC0C,gBAAgB,CAACC,KAAK,CAACtB,KAAK,EAAEQ,SAAS,CAAC;AACvD;AAEA,SAASlC,aAAaA,CAAA,EAAG;EACvB,OAAOD,YAAY,CAACiD,KAAK,CAAC,IAAI,EAAEd,SAAS,CAAC,CAACoB,QAAQ,CAAC,GAAG,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "script"}