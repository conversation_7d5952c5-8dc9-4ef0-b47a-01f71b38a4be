{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nexport default function Popup(props) {\n  var showArrow = props.showArrow,\n    arrowContent = props.arrowContent,\n    children = props.children,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    overlayInnerStyle = props.overlayInnerStyle,\n    className = props.className,\n    style = props.style;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: style\n  }, showArrow !== false && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\"),\n    key: \"arrow\"\n  }, arrowContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\"),\n    id: id,\n    role: \"tooltip\",\n    style: overlayInnerStyle\n  }, typeof children === 'function' ? children() : children));\n}", "map": {"version": 3, "names": ["React", "classNames", "Popup", "props", "showArrow", "arrow<PERSON>ontent", "children", "prefixCls", "id", "overlayInnerStyle", "className", "style", "createElement", "concat", "key", "role"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tooltip/es/Popup.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nexport default function Popup(props) {\n  var showArrow = props.showArrow,\n      arrowContent = props.arrowContent,\n      children = props.children,\n      prefixCls = props.prefixCls,\n      id = props.id,\n      overlayInnerStyle = props.overlayInnerStyle,\n      className = props.className,\n      style = props.style;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: style\n  }, showArrow !== false && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\"),\n    key: \"arrow\"\n  }, arrowContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\"),\n    id: id,\n    role: \"tooltip\",\n    style: overlayInnerStyle\n  }, typeof children === 'function' ? children() : children));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,EAAE,GAAGL,KAAK,CAACK,EAAE;IACbC,iBAAiB,GAAGN,KAAK,CAACM,iBAAiB;IAC3CC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;EACvB,OAAO,aAAaX,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC7CF,SAAS,EAAET,UAAU,CAAC,EAAE,CAACY,MAAM,CAACN,SAAS,EAAE,UAAU,CAAC,EAAEG,SAAS,CAAC;IAClEC,KAAK,EAAEA;EACT,CAAC,EAAEP,SAAS,KAAK,KAAK,IAAI,aAAaJ,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAChEF,SAAS,EAAE,EAAE,CAACG,MAAM,CAACN,SAAS,EAAE,QAAQ,CAAC;IACzCO,GAAG,EAAE;EACP,CAAC,EAAET,YAAY,CAAC,EAAE,aAAaL,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IACxDF,SAAS,EAAE,EAAE,CAACG,MAAM,CAACN,SAAS,EAAE,QAAQ,CAAC;IACzCC,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAE,SAAS;IACfJ,KAAK,EAAEF;EACT,CAAC,EAAE,OAAOH,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module"}