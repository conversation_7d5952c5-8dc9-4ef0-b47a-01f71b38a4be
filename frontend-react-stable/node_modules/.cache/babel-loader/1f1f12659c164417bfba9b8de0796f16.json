{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { tupleNum } from '../_util/type';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar TITLE_ELE_LIST = tupleNum(1, 2, 3, 4, 5);\nvar Title = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$level = props.level,\n    level = _props$level === void 0 ? 1 : _props$level,\n    restProps = __rest(props, [\"level\"]);\n  var component;\n  if (TITLE_ELE_LIST.includes(level)) {\n    component = \"h\".concat(level);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n    component = 'h1';\n  }\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "tupleNum", "warning", "Base", "TITLE_ELE_LIST", "Title", "forwardRef", "props", "ref", "_props$level", "level", "restProps", "component", "includes", "concat", "process", "env", "NODE_ENV", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Title.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { tupleNum } from '../_util/type';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar TITLE_ELE_LIST = tupleNum(1, 2, 3, 4, 5);\nvar Title = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$level = props.level,\n    level = _props$level === void 0 ? 1 : _props$level,\n    restProps = __rest(props, [\"level\"]);\n  var component;\n  if (TITLE_ELE_LIST.includes(level)) {\n    component = \"h\".concat(level);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n    component = 'h1';\n  }\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,eAAe;AACxC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,cAAc,GAAGH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5C,IAAII,KAAK,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,YAAY,GAAGF,KAAK,CAACG,KAAK;IAC5BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IAClDE,SAAS,GAAGzB,MAAM,CAACqB,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;EACtC,IAAIK,SAAS;EACb,IAAIR,cAAc,CAACS,QAAQ,CAACH,KAAK,CAAC,EAAE;IAClCE,SAAS,GAAG,GAAG,CAACE,MAAM,CAACJ,KAAK,CAAC;EAC/B,CAAC,MAAM;IACLK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,OAAO,CAAC,KAAK,EAAE,kBAAkB,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;IAC3KU,SAAS,GAAG,IAAI;EAClB;EACA,OAAO,aAAaZ,KAAK,CAACkB,aAAa,CAACf,IAAI,EAAElB,QAAQ,CAAC;IACrDuB,GAAG,EAAEA;EACP,CAAC,EAAEG,SAAS,EAAE;IACZC,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAeP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}