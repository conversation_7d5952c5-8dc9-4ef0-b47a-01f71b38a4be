{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport RcImage from 'rc-image';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport defaultLocale from '../locale/en_US';\nimport { getTransitionName } from '../_util/motion';\nimport PreviewGroup, { icons } from './PreviewGroup';\nvar Image = function Image(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    preview = _a.preview,\n    otherProps = __rest(_a, [\"prefixCls\", \"preview\"]);\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    _useContext$locale = _useContext.locale,\n    contextLocale = _useContext$locale === void 0 ? defaultLocale : _useContext$locale,\n    getContextPopupContainer = _useContext.getPopupContainer;\n  var prefixCls = getPrefixCls('image', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var imageLocale = contextLocale.Image || defaultLocale.Image;\n  var mergedPreview = React.useMemo(function () {\n    if (preview === false) {\n      return preview;\n    }\n    var _preview = _typeof(preview) === 'object' ? preview : {};\n    var getContainer = _preview.getContainer,\n      restPreviewProps = __rest(_preview, [\"getContainer\"]);\n    return _extends(_extends({\n      mask: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-mask-info\")\n      }, /*#__PURE__*/React.createElement(EyeOutlined, null), imageLocale === null || imageLocale === void 0 ? void 0 : imageLocale.preview),\n      icons: icons\n    }, restPreviewProps), {\n      getContainer: getContainer || getContextPopupContainer,\n      transitionName: getTransitionName(rootPrefixCls, 'zoom', _preview.transitionName),\n      maskTransitionName: getTransitionName(rootPrefixCls, 'fade', _preview.maskTransitionName)\n    });\n  }, [preview, imageLocale]);\n  return /*#__PURE__*/React.createElement(RcImage, _extends({\n    prefixCls: prefixCls,\n    preview: mergedPreview\n  }, otherProps));\n};\nImage.PreviewGroup = PreviewGroup;\nexport default Image;", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "EyeOutlined", "RcImage", "React", "useContext", "ConfigContext", "defaultLocale", "getTransitionName", "PreviewGroup", "icons", "Image", "_a", "customizePrefixCls", "prefixCls", "preview", "otherProps", "_useContext", "getPrefixCls", "_useContext$locale", "locale", "contextLocale", "getContextPopupContainer", "getPopupContainer", "rootPrefixCls", "imageLocale", "mergedPreview", "useMemo", "_preview", "getContainer", "restPreviewProps", "mask", "createElement", "className", "concat", "transitionName", "maskTransitionName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/image/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport RcImage from 'rc-image';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport defaultLocale from '../locale/en_US';\nimport { getTransitionName } from '../_util/motion';\nimport PreviewGroup, { icons } from './PreviewGroup';\nvar Image = function Image(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    preview = _a.preview,\n    otherProps = __rest(_a, [\"prefixCls\", \"preview\"]);\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    _useContext$locale = _useContext.locale,\n    contextLocale = _useContext$locale === void 0 ? defaultLocale : _useContext$locale,\n    getContextPopupContainer = _useContext.getPopupContainer;\n  var prefixCls = getPrefixCls('image', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var imageLocale = contextLocale.Image || defaultLocale.Image;\n  var mergedPreview = React.useMemo(function () {\n    if (preview === false) {\n      return preview;\n    }\n    var _preview = _typeof(preview) === 'object' ? preview : {};\n    var getContainer = _preview.getContainer,\n      restPreviewProps = __rest(_preview, [\"getContainer\"]);\n    return _extends(_extends({\n      mask: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-mask-info\")\n      }, /*#__PURE__*/React.createElement(EyeOutlined, null), imageLocale === null || imageLocale === void 0 ? void 0 : imageLocale.preview),\n      icons: icons\n    }, restPreviewProps), {\n      getContainer: getContainer || getContextPopupContainer,\n      transitionName: getTransitionName(rootPrefixCls, 'zoom', _preview.transitionName),\n      maskTransitionName: getTransitionName(rootPrefixCls, 'fade', _preview.maskTransitionName)\n    });\n  }, [preview, imageLocale]);\n  return /*#__PURE__*/React.createElement(RcImage, _extends({\n    prefixCls: prefixCls,\n    preview: mergedPreview\n  }, otherProps));\n};\nImage.PreviewGroup = PreviewGroup;\nexport default Image;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,WAAW,MAAM,wCAAwC;AAChE,OAAOC,OAAO,MAAM,UAAU;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,OAAOC,YAAY,IAAIC,KAAK,QAAQ,gBAAgB;AACpD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;EAC7B,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCC,OAAO,GAAGH,EAAE,CAACG,OAAO;IACpBC,UAAU,GAAG5B,MAAM,CAACwB,EAAE,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;EACnD,IAAIK,WAAW,GAAGZ,UAAU,CAACC,aAAa,CAAC;IACzCY,YAAY,GAAGD,WAAW,CAACC,YAAY;IACvCC,kBAAkB,GAAGF,WAAW,CAACG,MAAM;IACvCC,aAAa,GAAGF,kBAAkB,KAAK,KAAK,CAAC,GAAGZ,aAAa,GAAGY,kBAAkB;IAClFG,wBAAwB,GAAGL,WAAW,CAACM,iBAAiB;EAC1D,IAAIT,SAAS,GAAGI,YAAY,CAAC,OAAO,EAAEL,kBAAkB,CAAC;EACzD,IAAIW,aAAa,GAAGN,YAAY,CAAC,CAAC;EAClC,IAAIO,WAAW,GAAGJ,aAAa,CAACV,KAAK,IAAIJ,aAAa,CAACI,KAAK;EAC5D,IAAIe,aAAa,GAAGtB,KAAK,CAACuB,OAAO,CAAC,YAAY;IAC5C,IAAIZ,OAAO,KAAK,KAAK,EAAE;MACrB,OAAOA,OAAO;IAChB;IACA,IAAIa,QAAQ,GAAGzC,OAAO,CAAC4B,OAAO,CAAC,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC,CAAC;IAC3D,IAAIc,YAAY,GAAGD,QAAQ,CAACC,YAAY;MACtCC,gBAAgB,GAAG1C,MAAM,CAACwC,QAAQ,EAAE,CAAC,cAAc,CAAC,CAAC;IACvD,OAAO1C,QAAQ,CAACA,QAAQ,CAAC;MACvB6C,IAAI,EAAE,aAAa3B,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;QAC5CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,YAAY;MAC9C,CAAC,EAAE,aAAaV,KAAK,CAAC4B,aAAa,CAAC9B,WAAW,EAAE,IAAI,CAAC,EAAEuB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACV,OAAO,CAAC;MACtIL,KAAK,EAAEA;IACT,CAAC,EAAEoB,gBAAgB,CAAC,EAAE;MACpBD,YAAY,EAAEA,YAAY,IAAIP,wBAAwB;MACtDa,cAAc,EAAE3B,iBAAiB,CAACgB,aAAa,EAAE,MAAM,EAAEI,QAAQ,CAACO,cAAc,CAAC;MACjFC,kBAAkB,EAAE5B,iBAAiB,CAACgB,aAAa,EAAE,MAAM,EAAEI,QAAQ,CAACQ,kBAAkB;IAC1F,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrB,OAAO,EAAEU,WAAW,CAAC,CAAC;EAC1B,OAAO,aAAarB,KAAK,CAAC4B,aAAa,CAAC7B,OAAO,EAAEjB,QAAQ,CAAC;IACxD4B,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEW;EACX,CAAC,EAAEV,UAAU,CAAC,CAAC;AACjB,CAAC;AACDL,KAAK,CAACF,YAAY,GAAGA,YAAY;AACjC,eAAeE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}