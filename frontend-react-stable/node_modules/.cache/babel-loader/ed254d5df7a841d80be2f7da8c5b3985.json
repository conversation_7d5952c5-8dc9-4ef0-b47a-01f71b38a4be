{"ast": null, "code": "var isSymbol = require('./isSymbol');\n\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n    length = array.length;\n  while (++index < length) {\n    var value = array[index],\n      current = iteratee(value);\n    if (current != null && (computed === undefined ? current === current && !isSymbol(current) : comparator(current, computed))) {\n      var computed = current,\n        result = value;\n    }\n  }\n  return result;\n}\nmodule.exports = baseExtremum;", "map": {"version": 3, "names": ["isSymbol", "require", "baseExtremum", "array", "iteratee", "comparator", "index", "length", "value", "current", "computed", "undefined", "result", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseExtremum.js"], "sourcesContent": ["var isSymbol = require('./isSymbol');\n\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var value = array[index],\n        current = iteratee(value);\n\n    if (current != null && (computed === undefined\n          ? (current === current && !isSymbol(current))\n          : comparator(current, computed)\n        )) {\n      var computed = current,\n          result = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseExtremum;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EACjD,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,KAAK,GAAGL,KAAK,CAACG,KAAK,CAAC;MACpBG,OAAO,GAAGL,QAAQ,CAACI,KAAK,CAAC;IAE7B,IAAIC,OAAO,IAAI,IAAI,KAAKC,QAAQ,KAAKC,SAAS,GACrCF,OAAO,KAAKA,OAAO,IAAI,CAACT,QAAQ,CAACS,OAAO,CAAC,GAC1CJ,UAAU,CAACI,OAAO,EAAEC,QAAQ,CAAC,CAChC,EAAE;MACL,IAAIA,QAAQ,GAAGD,OAAO;QAClBG,MAAM,GAAGJ,KAAK;IACpB;EACF;EACA,OAAOI,MAAM;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}