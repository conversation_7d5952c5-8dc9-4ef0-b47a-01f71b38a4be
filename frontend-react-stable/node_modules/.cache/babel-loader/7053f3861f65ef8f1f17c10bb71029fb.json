{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from '../Header';\nimport { YEAR_DECADE_COUNT } from '.';\nimport PanelContext from '../../PanelContext';\nfunction YearHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    onPrevDecade = props.onPrevDecade,\n    onNextDecade = props.onNextDecade,\n    onDecadeClick = props.onDecadeClick;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecade,\n    onSuperNext: onNextDecade\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onDecadeClick,\n    className: \"\".concat(prefixCls, \"-decade-btn\")\n  }, startYear, \"-\", endYear));\n}\nexport default YearHeader;", "map": {"version": 3, "names": ["_extends", "React", "Header", "YEAR_DECADE_COUNT", "PanelContext", "<PERSON><PERSON><PERSON><PERSON>", "props", "prefixCls", "generateConfig", "viewDate", "onPrevDecade", "onNextDecade", "onDecadeClick", "_React$useContext", "useContext", "<PERSON><PERSON>ead<PERSON>", "headerPrefixCls", "concat", "yearNumber", "getYear", "startYear", "Math", "floor", "endYear", "createElement", "onSuperPrev", "onSuperNext", "type", "onClick", "className"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/YearPanel/YearHeader.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from '../Header';\nimport { YEAR_DECADE_COUNT } from '.';\nimport PanelContext from '../../PanelContext';\nfunction YearHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    onPrevDecade = props.onPrevDecade,\n    onNextDecade = props.onNextDecade,\n    onDecadeClick = props.onDecadeClick;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecade,\n    onSuperNext: onNextDecade\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onDecadeClick,\n    className: \"\".concat(prefixCls, \"-decade-btn\")\n  }, startYear, \"-\", endYear));\n}\nexport default YearHeader;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,iBAAiB,QAAQ,GAAG;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,aAAa,GAAGN,KAAK,CAACM,aAAa;EACrC,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACV,YAAY,CAAC;IACpDW,UAAU,GAAGF,iBAAiB,CAACE,UAAU;EAC3C,IAAIA,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;EACrD,IAAIW,UAAU,GAAGV,cAAc,CAACW,OAAO,CAACV,QAAQ,CAAC;EACjD,IAAIW,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAGf,iBAAiB,CAAC,GAAGA,iBAAiB;EAC9E,IAAIoB,OAAO,GAAGH,SAAS,GAAGjB,iBAAiB,GAAG,CAAC;EAC/C,OAAO,aAAaF,KAAK,CAACuB,aAAa,CAACtB,MAAM,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IAClEC,SAAS,EAAES,eAAe;IAC1BS,WAAW,EAAEf,YAAY;IACzBgB,WAAW,EAAEf;EACf,CAAC,CAAC,EAAE,aAAaV,KAAK,CAACuB,aAAa,CAAC,QAAQ,EAAE;IAC7CG,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAEhB,aAAa;IACtBiB,SAAS,EAAE,EAAE,CAACZ,MAAM,CAACV,SAAS,EAAE,aAAa;EAC/C,CAAC,EAAEa,SAAS,EAAE,GAAG,EAAEG,OAAO,CAAC,CAAC;AAC9B;AACA,eAAelB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}