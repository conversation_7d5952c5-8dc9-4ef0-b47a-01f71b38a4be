{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nexport default (function (treeData, searchValue, _ref) {\n  var treeNodeFilterProp = _ref.treeNodeFilterProp,\n    filterTreeNode = _ref.filterTreeNode,\n    fieldNames = _ref.fieldNames;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc;\n    if (typeof filterTreeNode === 'function') {\n      filterOptionFunc = filterTreeNode;\n    } else {\n      var upperStr = searchValue.toUpperCase();\n      filterOptionFunc = function filterOptionFunc(_, dataNode) {\n        var value = dataNode[treeNodeFilterProp];\n        return String(value).toUpperCase().includes(upperStr);\n      };\n    }\n    function dig(list) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return list.map(function (dataNode) {\n        var children = dataNode[fieldChildren];\n        var match = keepAll || filterOptionFunc(searchValue, fillLegacyProps(dataNode));\n        var childList = dig(children || [], match);\n        if (match || childList.length) {\n          return _objectSpread(_objectSpread({}, dataNode), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, childList));\n        }\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n    return dig(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n});", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "fillLegacyProps", "treeData", "searchValue", "_ref", "treeNodeFilterProp", "filterTreeNode", "fieldNames", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "useMemo", "filterOptionFunc", "upperStr", "toUpperCase", "_", "dataNode", "value", "String", "includes", "dig", "list", "keepAll", "arguments", "length", "undefined", "map", "match", "childList", "<PERSON><PERSON><PERSON><PERSON>", "filter", "node"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nexport default (function (treeData, searchValue, _ref) {\n  var treeNodeFilterProp = _ref.treeNodeFilterProp,\n      filterTreeNode = _ref.filterTreeNode,\n      fieldNames = _ref.fieldNames;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n\n    var filterOptionFunc;\n\n    if (typeof filterTreeNode === 'function') {\n      filterOptionFunc = filterTreeNode;\n    } else {\n      var upperStr = searchValue.toUpperCase();\n\n      filterOptionFunc = function filterOptionFunc(_, dataNode) {\n        var value = dataNode[treeNodeFilterProp];\n        return String(value).toUpperCase().includes(upperStr);\n      };\n    }\n\n    function dig(list) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return list.map(function (dataNode) {\n        var children = dataNode[fieldChildren];\n        var match = keepAll || filterOptionFunc(searchValue, fillLegacyProps(dataNode));\n        var childList = dig(children || [], match);\n\n        if (match || childList.length) {\n          return _objectSpread(_objectSpread({}, dataNode), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, childList));\n        }\n\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n\n    return dig(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,qBAAqB;AACrD,gBAAgB,UAAUC,QAAQ,EAAEC,WAAW,EAAEC,IAAI,EAAE;EACrD,IAAIC,kBAAkB,GAAGD,IAAI,CAACC,kBAAkB;IAC5CC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAChC,IAAIC,aAAa,GAAGD,UAAU,CAACE,QAAQ;EACvC,OAAOT,KAAK,CAACU,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACP,WAAW,IAAIG,cAAc,KAAK,KAAK,EAAE;MAC5C,OAAOJ,QAAQ;IACjB;IAEA,IAAIS,gBAAgB;IAEpB,IAAI,OAAOL,cAAc,KAAK,UAAU,EAAE;MACxCK,gBAAgB,GAAGL,cAAc;IACnC,CAAC,MAAM;MACL,IAAIM,QAAQ,GAAGT,WAAW,CAACU,WAAW,CAAC,CAAC;MAExCF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACG,CAAC,EAAEC,QAAQ,EAAE;QACxD,IAAIC,KAAK,GAAGD,QAAQ,CAACV,kBAAkB,CAAC;QACxC,OAAOY,MAAM,CAACD,KAAK,CAAC,CAACH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,QAAQ,CAAC;MACvD,CAAC;IACH;IAEA,SAASO,GAAGA,CAACC,IAAI,EAAE;MACjB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACvF,OAAOF,IAAI,CAACK,GAAG,CAAC,UAAUV,QAAQ,EAAE;QAClC,IAAIN,QAAQ,GAAGM,QAAQ,CAACP,aAAa,CAAC;QACtC,IAAIkB,KAAK,GAAGL,OAAO,IAAIV,gBAAgB,CAACR,WAAW,EAAEF,eAAe,CAACc,QAAQ,CAAC,CAAC;QAC/E,IAAIY,SAAS,GAAGR,GAAG,CAACV,QAAQ,IAAI,EAAE,EAAEiB,KAAK,CAAC;QAE1C,IAAIA,KAAK,IAAIC,SAAS,CAACJ,MAAM,EAAE;UAC7B,OAAOxB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAEjB,eAAe,CAAC;YACpE8B,MAAM,EAAEJ;UACV,CAAC,EAAEhB,aAAa,EAAEmB,SAAS,CAAC,CAAC;QAC/B;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,CAAC;IACJ;IAEA,OAAOX,GAAG,CAACjB,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACA,QAAQ,EAAEC,WAAW,EAAEK,aAAa,EAAEH,kBAAkB,EAAEC,cAAc,CAAC,CAAC;AAChF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}