{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = reverse;\nfunction reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "reverse", "values", "Symbol", "iterator", "TypeError", "Array", "from"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/reverse.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = reverse;\n\nfunction reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,OAAO;AAEzB,SAASA,OAAOA,CAACC,MAAM,EAAE;EACvB,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,OAAOC,KAAK,CAACC,IAAI,CAACL,MAAM,CAAC,CAACD,OAAO,CAAC,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}