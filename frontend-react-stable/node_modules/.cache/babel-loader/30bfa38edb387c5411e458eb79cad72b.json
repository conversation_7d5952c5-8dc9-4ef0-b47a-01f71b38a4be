{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = linear;\nexports.linearish = linearish;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _continuous = _interopRequireWildcard(require(\"./continuous.js\"));\nvar _init = require(\"./init.js\");\nvar _tickFormat = _interopRequireDefault(require(\"./tickFormat.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction linearish(scale) {\n  var domain = scale.domain;\n  scale.ticks = function (count) {\n    var d = domain();\n    return (0, _index.ticks)(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n  scale.tickFormat = function (count, specifier) {\n    var d = domain();\n    return (0, _tickFormat.default)(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n  scale.nice = function (count) {\n    if (count == null) count = 10;\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    while (maxIter-- > 0) {\n      step = (0, _index.tickIncrement)(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start;\n        d[i1] = stop;\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n    return scale;\n  };\n  return scale;\n}\nfunction linear() {\n  var scale = (0, _continuous.default)();\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, linear());\n  };\n  _init.initRange.apply(scale, arguments);\n  return linearish(scale);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "linear", "linearish", "_index", "require", "_continuous", "_interopRequireWildcard", "_init", "_tickFormat", "_interopRequireDefault", "obj", "__esModule", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "scale", "domain", "ticks", "count", "d", "length", "tickFormat", "specifier", "nice", "i0", "i1", "start", "stop", "prestep", "step", "maxIter", "tickIncrement", "Math", "floor", "ceil", "copy", "initRange", "apply", "arguments"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/linear.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = linear;\nexports.linearish = linearish;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _continuous = _interopRequireWildcard(require(\"./continuous.js\"));\n\nvar _init = require(\"./init.js\");\n\nvar _tickFormat = _interopRequireDefault(require(\"./tickFormat.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function (count) {\n    var d = domain();\n    return (0, _index.ticks)(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function (count, specifier) {\n    var d = domain();\n    return (0, _tickFormat.default)(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function (count) {\n    if (count == null) count = 10;\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n\n    while (maxIter-- > 0) {\n      step = (0, _index.tickIncrement)(start, stop, count);\n\n      if (step === prestep) {\n        d[i0] = start;\n        d[i1] = stop;\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nfunction linear() {\n  var scale = (0, _continuous.default)();\n\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, linear());\n  };\n\n  _init.initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AACxBH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAE7B,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,WAAW,GAAGC,uBAAuB,CAACF,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAErE,IAAIG,KAAK,GAAGH,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAII,WAAW,GAAGC,sBAAsB,CAACL,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEpE,SAASK,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,SAASE,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASP,uBAAuBA,CAACI,GAAG,EAAEG,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAIH,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEV,OAAO,EAAEU;IAAI,CAAC;EAAE;EAAE,IAAIO,KAAK,GAAGL,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAII,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACR,GAAG,CAAC,EAAE;IAAE,OAAOO,KAAK,CAACE,GAAG,CAACT,GAAG,CAAC;EAAE;EAAE,IAAIU,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGzB,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC0B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIb,GAAG,EAAE;IAAE,IAAIa,GAAG,KAAK,SAAS,IAAI3B,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChB,GAAG,EAAEa,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACZ,GAAG,EAAEa,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEhC,MAAM,CAACC,cAAc,CAACuB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGb,GAAG,CAACa,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACpB,OAAO,GAAGU,GAAG;EAAE,IAAIO,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAAClB,GAAG,EAAEU,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASlB,SAASA,CAAC2B,KAAK,EAAE;EACxB,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EAEzBD,KAAK,CAACE,KAAK,GAAG,UAAUC,KAAK,EAAE;IAC7B,IAAIC,CAAC,GAAGH,MAAM,CAAC,CAAC;IAChB,OAAO,CAAC,CAAC,EAAE3B,MAAM,CAAC4B,KAAK,EAAEE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEF,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC;EAC7E,CAAC;EAEDH,KAAK,CAACM,UAAU,GAAG,UAAUH,KAAK,EAAEI,SAAS,EAAE;IAC7C,IAAIH,CAAC,GAAGH,MAAM,CAAC,CAAC;IAChB,OAAO,CAAC,CAAC,EAAEtB,WAAW,CAACR,OAAO,EAAEiC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEF,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAEI,SAAS,CAAC;EAC/F,CAAC;EAEDP,KAAK,CAACQ,IAAI,GAAG,UAAUL,KAAK,EAAE;IAC5B,IAAIA,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;IAC7B,IAAIC,CAAC,GAAGH,MAAM,CAAC,CAAC;IAChB,IAAIQ,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAGN,CAAC,CAACC,MAAM,GAAG,CAAC;IACrB,IAAIM,KAAK,GAAGP,CAAC,CAACK,EAAE,CAAC;IACjB,IAAIG,IAAI,GAAGR,CAAC,CAACM,EAAE,CAAC;IAChB,IAAIG,OAAO;IACX,IAAIC,IAAI;IACR,IAAIC,OAAO,GAAG,EAAE;IAEhB,IAAIH,IAAI,GAAGD,KAAK,EAAE;MAChBG,IAAI,GAAGH,KAAK,EAAEA,KAAK,GAAGC,IAAI,EAAEA,IAAI,GAAGE,IAAI;MACvCA,IAAI,GAAGL,EAAE,EAAEA,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGI,IAAI;IAC/B;IAEA,OAAOC,OAAO,EAAE,GAAG,CAAC,EAAE;MACpBD,IAAI,GAAG,CAAC,CAAC,EAAExC,MAAM,CAAC0C,aAAa,EAAEL,KAAK,EAAEC,IAAI,EAAET,KAAK,CAAC;MAEpD,IAAIW,IAAI,KAAKD,OAAO,EAAE;QACpBT,CAAC,CAACK,EAAE,CAAC,GAAGE,KAAK;QACbP,CAAC,CAACM,EAAE,CAAC,GAAGE,IAAI;QACZ,OAAOX,MAAM,CAACG,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIU,IAAI,GAAG,CAAC,EAAE;QACnBH,KAAK,GAAGM,IAAI,CAACC,KAAK,CAACP,KAAK,GAAGG,IAAI,CAAC,GAAGA,IAAI;QACvCF,IAAI,GAAGK,IAAI,CAACE,IAAI,CAACP,IAAI,GAAGE,IAAI,CAAC,GAAGA,IAAI;MACtC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;QACnBH,KAAK,GAAGM,IAAI,CAACE,IAAI,CAACR,KAAK,GAAGG,IAAI,CAAC,GAAGA,IAAI;QACtCF,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACN,IAAI,GAAGE,IAAI,CAAC,GAAGA,IAAI;MACvC,CAAC,MAAM;QACL;MACF;MAEAD,OAAO,GAAGC,IAAI;IAChB;IAEA,OAAOd,KAAK;EACd,CAAC;EAED,OAAOA,KAAK;AACd;AAEA,SAAS5B,MAAMA,CAAA,EAAG;EAChB,IAAI4B,KAAK,GAAG,CAAC,CAAC,EAAExB,WAAW,CAACL,OAAO,EAAE,CAAC;EAEtC6B,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAE5C,WAAW,CAAC4C,IAAI,EAAEpB,KAAK,EAAE5B,MAAM,CAAC,CAAC,CAAC;EAC/C,CAAC;EAEDM,KAAK,CAAC2C,SAAS,CAACC,KAAK,CAACtB,KAAK,EAAEuB,SAAS,CAAC;EAEvC,OAAOlD,SAAS,CAAC2B,KAAK,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}