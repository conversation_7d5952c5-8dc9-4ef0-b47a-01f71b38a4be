{"ast": null, "code": "import SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nimport Input from '../input';\nexport default function Search(props) {\n  var _props$placeholder = props.placeholder,\n    placeholder = _props$placeholder === void 0 ? '' : _props$placeholder,\n    value = props.value,\n    prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    handleClear = props.handleClear;\n  var handleChange = React.useCallback(function (e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n}", "map": {"version": 3, "names": ["SearchOutlined", "React", "Input", "Search", "props", "_props$placeholder", "placeholder", "value", "prefixCls", "disabled", "onChange", "handleClear", "handleChange", "useCallback", "e", "target", "createElement", "className", "allowClear", "prefix"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/transfer/search.js"], "sourcesContent": ["import SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nimport Input from '../input';\nexport default function Search(props) {\n  var _props$placeholder = props.placeholder,\n    placeholder = _props$placeholder === void 0 ? '' : _props$placeholder,\n    value = props.value,\n    prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    handleClear = props.handleClear;\n  var handleChange = React.useCallback(function (e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2CAA2C;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,UAAU;AAC5B,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,WAAW;IACxCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;IACrEE,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,WAAW,GAAGP,KAAK,CAACO,WAAW;EACjC,IAAIC,YAAY,GAAGX,KAAK,CAACY,WAAW,CAAC,UAAUC,CAAC,EAAE;IAChDJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,CAAC,CAAC;IAC/D,IAAIA,CAAC,CAACC,MAAM,CAACR,KAAK,KAAK,EAAE,EAAE;MACzBI,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;EACd,OAAO,aAAaT,KAAK,CAACe,aAAa,CAACd,KAAK,EAAE;IAC7CI,WAAW,EAAEA,WAAW;IACxBW,SAAS,EAAET,SAAS;IACpBD,KAAK,EAAEA,KAAK;IACZG,QAAQ,EAAEE,YAAY;IACtBH,QAAQ,EAAEA,QAAQ;IAClBS,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,aAAalB,KAAK,CAACe,aAAa,CAAChB,cAAc,EAAE,IAAI;EAC/D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}