{"ast": null, "code": "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) : typeof define === 'function' && define.amd ? define(['exports'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global[\"fast-equals\"] = {}));\n})(this, function (exports) {\n  'use strict';\n\n  var getOwnPropertyNames = Object.getOwnPropertyNames,\n    getOwnPropertySymbols = Object.getOwnPropertySymbols;\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  /**\n   * Combine two comparators into a single comparators.\n   */\n  function combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n      return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n  }\n  /**\n   * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n   * for circular references to be safely included in the comparison without creating\n   * stack overflows.\n   */\n  function createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n      if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n        return areItemsEqual(a, b, state);\n      }\n      var cache = state.cache;\n      var cachedA = cache.get(a);\n      var cachedB = cache.get(b);\n      if (cachedA && cachedB) {\n        return cachedA === b && cachedB === a;\n      }\n      cache.set(a, b);\n      cache.set(b, a);\n      var result = areItemsEqual(a, b, state);\n      cache.delete(a);\n      cache.delete(b);\n      return result;\n    };\n  }\n  /**\n   * Get the properties to strictly examine, which include both own properties that are\n   * not enumerable and symbol properties.\n   */\n  function getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n  }\n  /**\n   * Whether the object contains the property passed as an own property.\n   */\n  var hasOwn = Object.hasOwn || function (object, property) {\n    return hasOwnProperty.call(object, property);\n  };\n  /**\n   * Whether the values passed are strictly equal or both NaN.\n   */\n  function sameValueZeroEqual(a, b) {\n    return a === b || !a && !b && a !== a && b !== b;\n  }\n  var PREACT_VNODE = '__v';\n  var PREACT_OWNER = '__o';\n  var REACT_OWNER = '_owner';\n  var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor,\n    keys = Object.keys;\n  /**\n   * Whether the arrays are equal in value.\n   */\n  function areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n      return false;\n    }\n    while (index-- > 0) {\n      if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Whether the dates passed are equal in value.\n   */\n  function areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n  }\n  /**\n   * Whether the errors passed are equal in value.\n   */\n  function areErrorsEqual(a, b) {\n    return a.name === b.name && a.message === b.message && a.cause === b.cause && a.stack === b.stack;\n  }\n  /**\n   * Whether the functions passed are equal in value.\n   */\n  function areFunctionsEqual(a, b) {\n    return a === b;\n  }\n  /**\n   * Whether the `Map`s are equal in value.\n   */\n  function areMapsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n      return false;\n    }\n    if (!size) {\n      return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.entries();\n    var aResult;\n    var bResult;\n    var index = 0;\n    while (aResult = aIterable.next()) {\n      if (aResult.done) {\n        break;\n      }\n      var bIterable = b.entries();\n      var hasMatch = false;\n      var matchIndex = 0;\n      while (bResult = bIterable.next()) {\n        if (bResult.done) {\n          break;\n        }\n        if (matchedIndices[matchIndex]) {\n          matchIndex++;\n          continue;\n        }\n        var aEntry = aResult.value;\n        var bEntry = bResult.value;\n        if (state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) && state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)) {\n          hasMatch = matchedIndices[matchIndex] = true;\n          break;\n        }\n        matchIndex++;\n      }\n      if (!hasMatch) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  /**\n   * Whether the numbers are equal in value.\n   */\n  var areNumbersEqual = sameValueZeroEqual;\n  /**\n   * Whether the objects are equal in value.\n   */\n  function areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n      return false;\n    }\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n      if (!isPropertyEqual(a, b, state, properties[index])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Whether the objects are equal in value with strict property checking.\n   */\n  function areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n      return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n      property = properties[index];\n      if (!isPropertyEqual(a, b, state, property)) {\n        return false;\n      }\n      descriptorA = getOwnPropertyDescriptor(a, property);\n      descriptorB = getOwnPropertyDescriptor(b, property);\n      if ((descriptorA || descriptorB) && (!descriptorA || !descriptorB || descriptorA.configurable !== descriptorB.configurable || descriptorA.enumerable !== descriptorB.enumerable || descriptorA.writable !== descriptorB.writable)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Whether the primitive wrappers passed are equal in value.\n   */\n  function arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n  }\n  /**\n   * Whether the regexps passed are equal in value.\n   */\n  function areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n  }\n  /**\n   * Whether the `Set`s are equal in value.\n   */\n  function areSetsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n      return false;\n    }\n    if (!size) {\n      return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while (aResult = aIterable.next()) {\n      if (aResult.done) {\n        break;\n      }\n      var bIterable = b.values();\n      var hasMatch = false;\n      var matchIndex = 0;\n      while (bResult = bIterable.next()) {\n        if (bResult.done) {\n          break;\n        }\n        if (!matchedIndices[matchIndex] && state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state)) {\n          hasMatch = matchedIndices[matchIndex] = true;\n          break;\n        }\n        matchIndex++;\n      }\n      if (!hasMatch) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Whether the TypedArray instances are equal in value.\n   */\n  function areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n      return false;\n    }\n    while (index-- > 0) {\n      if (a[index] !== b[index]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Whether the URL instances are equal in value.\n   */\n  function areUrlsEqual(a, b) {\n    return a.hostname === b.hostname && a.pathname === b.pathname && a.protocol === b.protocol && a.port === b.port && a.hash === b.hash && a.username === b.username && a.password === b.password;\n  }\n  function isPropertyEqual(a, b, state, property) {\n    if ((property === REACT_OWNER || property === PREACT_OWNER || property === PREACT_VNODE) && (a.$$typeof || b.$$typeof)) {\n      return true;\n    }\n    return hasOwn(b, property) && state.equals(a[property], b[property], property, property, a, b, state);\n  }\n  var ARGUMENTS_TAG = '[object Arguments]';\n  var BOOLEAN_TAG = '[object Boolean]';\n  var DATE_TAG = '[object Date]';\n  var ERROR_TAG = '[object Error]';\n  var MAP_TAG = '[object Map]';\n  var NUMBER_TAG = '[object Number]';\n  var OBJECT_TAG = '[object Object]';\n  var REG_EXP_TAG = '[object RegExp]';\n  var SET_TAG = '[object Set]';\n  var STRING_TAG = '[object String]';\n  var URL_TAG = '[object URL]';\n  var isArray = Array.isArray;\n  var isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView ? ArrayBuffer.isView : null;\n  var assign = Object.assign;\n  var getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n  /**\n   * Create a comparator method based on the type-specific equality comparators passed.\n   */\n  function createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual,\n      areDatesEqual = _a.areDatesEqual,\n      areErrorsEqual = _a.areErrorsEqual,\n      areFunctionsEqual = _a.areFunctionsEqual,\n      areMapsEqual = _a.areMapsEqual,\n      areNumbersEqual = _a.areNumbersEqual,\n      areObjectsEqual = _a.areObjectsEqual,\n      arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual,\n      areRegExpsEqual = _a.areRegExpsEqual,\n      areSetsEqual = _a.areSetsEqual,\n      areTypedArraysEqual = _a.areTypedArraysEqual,\n      areUrlsEqual = _a.areUrlsEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */\n    return function comparator(a, b, state) {\n      // If the items are strictly equal, no need to do a value comparison.\n      if (a === b) {\n        return true;\n      }\n      // If either of the items are nullish and fail the strictly equal check\n      // above, then they must be unequal.\n      if (a == null || b == null) {\n        return false;\n      }\n      var type = typeof a;\n      if (type !== typeof b) {\n        return false;\n      }\n      if (type !== 'object') {\n        if (type === 'number') {\n          return areNumbersEqual(a, b, state);\n        }\n        if (type === 'function') {\n          return areFunctionsEqual(a, b, state);\n        }\n        // If a primitive value that is not strictly equal, it must be unequal.\n        return false;\n      }\n      var constructor = a.constructor;\n      // Checks are listed in order of commonality of use-case:\n      //   1. Common complex object types (plain object, array)\n      //   2. Common data values (date, regexp)\n      //   3. Less-common complex object types (map, set)\n      //   4. Less-common data values (promise, primitive wrappers)\n      // Inherently this is both subjective and assumptive, however\n      // when reviewing comparable libraries in the wild this order\n      // appears to be generally consistent.\n      // Constructors should match, otherwise there is potential for false positives\n      // between class and subclass or custom object and POJO.\n      if (constructor !== b.constructor) {\n        return false;\n      }\n      // `isPlainObject` only checks against the object's own realm. Cross-realm\n      // comparisons are rare, and will be handled in the ultimate fallback, so\n      // we can avoid capturing the string tag.\n      if (constructor === Object) {\n        return areObjectsEqual(a, b, state);\n      }\n      // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n      // the string tag or doing an `instanceof` check.\n      if (isArray(a)) {\n        return areArraysEqual(a, b, state);\n      }\n      // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n      // capturing the string tag or comparing against all possible constructors.\n      if (isTypedArray != null && isTypedArray(a)) {\n        return areTypedArraysEqual(a, b, state);\n      }\n      // Try to fast-path equality checks for other complex object types in the\n      // same realm to avoid capturing the string tag. Strict equality is used\n      // instead of `instanceof` because it is more performant for the common\n      // use-case. If someone is subclassing a native class, it will be handled\n      // with the string tag comparison.\n      if (constructor === Date) {\n        return areDatesEqual(a, b, state);\n      }\n      if (constructor === RegExp) {\n        return areRegExpsEqual(a, b, state);\n      }\n      if (constructor === Map) {\n        return areMapsEqual(a, b, state);\n      }\n      if (constructor === Set) {\n        return areSetsEqual(a, b, state);\n      }\n      // Since this is a custom object, capture the string tag to determing its type.\n      // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n      var tag = getTag(a);\n      if (tag === DATE_TAG) {\n        return areDatesEqual(a, b, state);\n      }\n      // For RegExp, the properties are not enumerable, and therefore will give false positives if\n      // tested like a standard object.\n      if (tag === REG_EXP_TAG) {\n        return areRegExpsEqual(a, b, state);\n      }\n      if (tag === MAP_TAG) {\n        return areMapsEqual(a, b, state);\n      }\n      if (tag === SET_TAG) {\n        return areSetsEqual(a, b, state);\n      }\n      if (tag === OBJECT_TAG) {\n        // The exception for value comparison is custom `Promise`-like class instances. These should\n        // be treated the same as standard `Promise` objects, which means strict equality, and if\n        // it reaches this point then that strict equality comparison has already failed.\n        return typeof a.then !== 'function' && typeof b.then !== 'function' && areObjectsEqual(a, b, state);\n      }\n      // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n      // enumerable, and therefore will give false positives if tested like a standard object.\n      if (tag === URL_TAG) {\n        return areUrlsEqual(a, b, state);\n      }\n      // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n      // enumerable, and therefore will give false positives if tested like a standard object.\n      if (tag === ERROR_TAG) {\n        return areErrorsEqual(a, b, state);\n      }\n      // If an arguments tag, it should be treated as a standard object.\n      if (tag === ARGUMENTS_TAG) {\n        return areObjectsEqual(a, b, state);\n      }\n      // As the penultimate fallback, check if the values passed are primitive wrappers. This\n      // is very rare in modern JS, which is why it is deprioritized compared to all other object\n      // types.\n      if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n        return arePrimitiveWrappersEqual(a, b, state);\n      }\n      // If not matching any tags that require a specific type of comparison, then we hard-code false because\n      // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n      //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n      //     comparison that can be made.\n      //   - For types that can be introspected, but rarely have requirements to be compared\n      //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n      //     use-cases (may be included in a future release, if requested enough).\n      //   - For types that can be introspected but do not have an objective definition of what\n      //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n      // In all cases, these decisions should be reevaluated based on changes to the language and\n      // common development practices.\n      return false;\n    };\n  }\n  /**\n   * Create the configuration object used for building comparators.\n   */\n  function createEqualityComparatorConfig(_a) {\n    var circular = _a.circular,\n      createCustomConfig = _a.createCustomConfig,\n      strict = _a.strict;\n    var config = {\n      areArraysEqual: strict ? areObjectsEqualStrict : areArraysEqual,\n      areDatesEqual: areDatesEqual,\n      areErrorsEqual: areErrorsEqual,\n      areFunctionsEqual: areFunctionsEqual,\n      areMapsEqual: strict ? combineComparators(areMapsEqual, areObjectsEqualStrict) : areMapsEqual,\n      areNumbersEqual: areNumbersEqual,\n      areObjectsEqual: strict ? areObjectsEqualStrict : areObjectsEqual,\n      arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n      areRegExpsEqual: areRegExpsEqual,\n      areSetsEqual: strict ? combineComparators(areSetsEqual, areObjectsEqualStrict) : areSetsEqual,\n      areTypedArraysEqual: strict ? areObjectsEqualStrict : areTypedArraysEqual,\n      areUrlsEqual: areUrlsEqual\n    };\n    if (createCustomConfig) {\n      config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n      var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n      var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n      var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n      var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n      config = assign({}, config, {\n        areArraysEqual: areArraysEqual$1,\n        areMapsEqual: areMapsEqual$1,\n        areObjectsEqual: areObjectsEqual$1,\n        areSetsEqual: areSetsEqual$1\n      });\n    }\n    return config;\n  }\n  /**\n   * Default equality comparator pass-through, used as the standard `isEqual` creator for\n   * use inside the built comparator.\n   */\n  function createInternalEqualityComparator(compare) {\n    return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n      return compare(a, b, state);\n    };\n  }\n  /**\n   * Create the `isEqual` function used by the consuming application.\n   */\n  function createIsEqual(_a) {\n    var circular = _a.circular,\n      comparator = _a.comparator,\n      createState = _a.createState,\n      equals = _a.equals,\n      strict = _a.strict;\n    if (createState) {\n      return function isEqual(a, b) {\n        var _a = createState(),\n          _b = _a.cache,\n          cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b,\n          meta = _a.meta;\n        return comparator(a, b, {\n          cache: cache,\n          equals: equals,\n          meta: meta,\n          strict: strict\n        });\n      };\n    }\n    if (circular) {\n      return function isEqual(a, b) {\n        return comparator(a, b, {\n          cache: new WeakMap(),\n          equals: equals,\n          meta: undefined,\n          strict: strict\n        });\n      };\n    }\n    var state = {\n      cache: undefined,\n      equals: equals,\n      meta: undefined,\n      strict: strict\n    };\n    return function isEqual(a, b) {\n      return comparator(a, b, state);\n    };\n  }\n\n  /**\n   * Whether the items passed are deeply-equal in value.\n   */\n  var deepEqual = createCustomEqual();\n  /**\n   * Whether the items passed are deeply-equal in value based on strict comparison.\n   */\n  var strictDeepEqual = createCustomEqual({\n    strict: true\n  });\n  /**\n   * Whether the items passed are deeply-equal in value, including circular references.\n   */\n  var circularDeepEqual = createCustomEqual({\n    circular: true\n  });\n  /**\n   * Whether the items passed are deeply-equal in value, including circular references,\n   * based on strict comparison.\n   */\n  var strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true\n  });\n  /**\n   * Whether the items passed are shallowly-equal in value.\n   */\n  var shallowEqual = createCustomEqual({\n    createInternalComparator: function () {\n      return sameValueZeroEqual;\n    }\n  });\n  /**\n   * Whether the items passed are shallowly-equal in value based on strict comparison\n   */\n  var strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function () {\n      return sameValueZeroEqual;\n    }\n  });\n  /**\n   * Whether the items passed are shallowly-equal in value, including circular references.\n   */\n  var circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () {\n      return sameValueZeroEqual;\n    }\n  });\n  /**\n   * Whether the items passed are shallowly-equal in value, including circular references,\n   * based on strict comparison.\n   */\n  var strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () {\n      return sameValueZeroEqual;\n    },\n    strict: true\n  });\n  /**\n   * Create a custom equality comparison method.\n   *\n   * This can be done to create very targeted comparisons in extreme hot-path scenarios\n   * where the standard methods are not performant enough, but can also be used to provide\n   * support for legacy environments that do not support expected features like\n   * `RegExp.prototype.flags` out of the box.\n   */\n  function createCustomEqual(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var _a = options.circular,\n      circular = _a === void 0 ? false : _a,\n      createCustomInternalComparator = options.createInternalComparator,\n      createState = options.createState,\n      _b = options.strict,\n      strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator ? createCustomInternalComparator(comparator) : createInternalEqualityComparator(comparator);\n    return createIsEqual({\n      circular: circular,\n      comparator: comparator,\n      createState: createState,\n      equals: equals,\n      strict: strict\n    });\n  }\n  exports.circularDeepEqual = circularDeepEqual;\n  exports.circularShallowEqual = circularShallowEqual;\n  exports.createCustomEqual = createCustomEqual;\n  exports.deepEqual = deepEqual;\n  exports.sameValueZeroEqual = sameValueZeroEqual;\n  exports.shallowEqual = shallowEqual;\n  exports.strictCircularDeepEqual = strictCircularDeepEqual;\n  exports.strictCircularShallowEqual = strictCircularShallowEqual;\n  exports.strictDeepEqual = strictDeepEqual;\n  exports.strictShallowEqual = strictShallowEqual;\n});", "map": {"version": 3, "names": ["getOwnPropertyNames", "Object", "getOwnPropertySymbols", "hasOwnProperty", "prototype", "combineComparators", "comparatorA", "comparatorB", "isEqual", "a", "b", "state", "createIsCircular", "areItemsEqual", "isCircular", "cache", "cachedA", "get", "cachedB", "set", "result", "delete", "getStrictProperties", "object", "concat", "hasOwn", "property", "call", "sameValueZeroEqual", "PREACT_VNODE", "PREACT_OWNER", "REACT_OWNER", "getOwnPropertyDescriptor", "keys", "areArraysEqual", "index", "length", "equals", "areDatesEqual", "getTime", "areErrorsEqual", "name", "message", "cause", "stack", "areFunctionsEqual", "areMapsEqual", "size", "matchedIndices", "Array", "aIterable", "entries", "aResult", "bResult", "next", "done", "bIterable", "hasMatch", "matchIndex", "aEntry", "value", "b<PERSON><PERSON><PERSON>", "areNumbersEqual", "areObjectsEqual", "properties", "isPropertyEqual", "areObjectsEqualStrict", "descriptorA", "descriptorB", "configurable", "enumerable", "writable", "arePrimitiveWrappersEqual", "valueOf", "areRegExpsEqual", "source", "flags", "areSetsEqual", "values", "areTypedArraysEqual", "areUrlsEqual", "hostname", "pathname", "protocol", "port", "hash", "username", "password", "$$typeof", "ARGUMENTS_TAG", "BOOLEAN_TAG", "DATE_TAG", "ERROR_TAG", "MAP_TAG", "NUMBER_TAG", "OBJECT_TAG", "REG_EXP_TAG", "SET_TAG", "STRING_TAG", "URL_TAG", "isArray", "isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "assign", "getTag", "toString", "bind", "createEqualityComparator", "_a", "comparator", "type", "constructor", "Date", "RegExp", "Map", "Set", "tag", "then", "createEqualityComparatorConfig", "circular", "createCustomConfig", "strict", "config", "areArraysEqual$1", "areMapsEqual$1", "areObjectsEqual$1", "areSetsEqual$1", "createInternalEqualityComparator", "compare", "_indexOrKeyA", "_indexOrKeyB", "_parentA", "_parentB", "createIsEqual", "createState", "_b", "WeakMap", "undefined", "meta", "deepEqual", "createCustomEqual", "strictDeepEqual", "circularDeepEqual", "strictCircularDeepEqual", "shallowEqual", "createInternalComparator", "strictShallowEqual", "circularShallowEqual", "strictCircularShallowEqual", "options", "createCustomInternalComparator"], "sources": ["../../src/utils.ts", "../../src/equals.ts", "../../src/comparator.ts", "../../src/index.ts"], "sourcesContent": ["import type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  Dictionary,\n  State,\n  TypeEqualityComparator,\n} from './internalTypes';\n\nconst { getOwnPropertyNames, getOwnPropertySymbols } = Object;\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Combine two comparators into a single comparators.\n */\nexport function combineComparators<Meta>(\n  comparatorA: AnyEqualityComparator<Meta>,\n  comparatorB: AnyEqualityComparator<Meta>,\n) {\n  return function isEqual<A, B>(a: A, b: B, state: State<Meta>) {\n    return comparatorA(a, b, state) && comparatorB(a, b, state);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    state: CircularState<Cache<any, any>>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, state);\n    }\n\n    const { cache } = state;\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, state);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nexport function getStrictProperties(\n  object: Dictionary,\n): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object),\n  );\n}\n\n/**\n * Whether the object contains the property passed as an own property.\n */\nexport const hasOwn =\n  Object.hasOwn ||\n  ((object: Dictionary, property: number | string | symbol) =>\n    hasOwnProperty.call(object, property));\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (!a && !b && a !== a && b !== b);\n}\n", "import { getStrictProperties, hasOwn, sameValueZeroEqual } from './utils';\nimport type {\n  Dictionary,\n  PrimitiveWrapper,\n  State,\n  TypedArray,\n} from './internalTypes';\n\nconst PREACT_VNODE = '__v';\nconst PREACT_OWNER = '__o';\nconst REACT_OWNER = '_owner';\n\nconst { getOwnPropertyDescriptor, keys } = Object;\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(a: any[], b: any[], state: State<any>) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the dates passed are equal in value.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n\n/**\n * Whether the errors passed are equal in value.\n */\nexport function areErrorsEqual(a: Error, b: Error): boolean {\n  return (\n    a.name === b.name &&\n    a.message === b.message &&\n    a.cause === b.cause &&\n    a.stack === b.stack\n  );\n}\n\n/**\n * Whether the functions passed are equal in value.\n */\nexport function areFunctionsEqual(\n  a: (...args: any[]) => any,\n  b: (...args: any[]) => any,\n): boolean {\n  return a === b;\n}\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.entries();\n\n  let aResult: IteratorResult<[any, any]>;\n  let bResult: IteratorResult<[any, any]>;\n  let index = 0;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.entries();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (matchedIndices[matchIndex]) {\n        matchIndex++;\n        continue;\n      }\n\n      const aEntry = aResult.value;\n      const bEntry = bResult.value;\n\n      if (\n        state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n        state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n\n    index++;\n  }\n\n  return true;\n}\n\n/**\n * Whether the numbers are equal in value.\n */\nexport const areNumbersEqual = sameValueZeroEqual;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = keys(a);\n\n  let index = properties.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isPropertyEqual(a, b, state, properties[index]!)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nexport function areObjectsEqualStrict(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = getStrictProperties(a);\n\n  let index = properties.length;\n\n  if (getStrictProperties(b).length !== index) {\n    return false;\n  }\n\n  let property: string | symbol;\n  let descriptorA: ReturnType<typeof getOwnPropertyDescriptor>;\n  let descriptorB: ReturnType<typeof getOwnPropertyDescriptor>;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    property = properties[index]!;\n\n    if (!isPropertyEqual(a, b, state, property)) {\n      return false;\n    }\n\n    descriptorA = getOwnPropertyDescriptor(a, property);\n    descriptorB = getOwnPropertyDescriptor(b, property);\n\n    if (\n      (descriptorA || descriptorB) &&\n      (!descriptorA ||\n        !descriptorB ||\n        descriptorA.configurable !== descriptorB.configurable ||\n        descriptorA.enumerable !== descriptorB.enumerable ||\n        descriptorA.writable !== descriptorB.writable)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nexport function arePrimitiveWrappersEqual(\n  a: PrimitiveWrapper,\n  b: PrimitiveWrapper,\n): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n\n/**\n * Whether the regexps passed are equal in value.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.values();\n\n  let aResult: IteratorResult<any>;\n  let bResult: IteratorResult<any>;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.values();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (\n        !matchedIndices[matchIndex] &&\n        state.equals(\n          aResult.value,\n          bResult.value,\n          aResult.value,\n          bResult.value,\n          a,\n          b,\n          state,\n        )\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the TypedArray instances are equal in value.\n */\nexport function areTypedArraysEqual(a: TypedArray, b: TypedArray) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (a[index] !== b[index]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the URL instances are equal in value.\n */\nexport function areUrlsEqual(a: URL, b: URL): boolean {\n  return (\n    a.hostname === b.hostname &&\n    a.pathname === b.pathname &&\n    a.protocol === b.protocol &&\n    a.port === b.port &&\n    a.hash === b.hash &&\n    a.username === b.username &&\n    a.password === b.password\n  );\n}\n\nfunction isPropertyEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n  property: string | symbol,\n) {\n  if (\n    (property === REACT_OWNER ||\n      property === PREACT_OWNER ||\n      property === PREACT_VNODE) &&\n    (a.$$typeof || b.$$typeof)\n  ) {\n    return true;\n  }\n\n  return (\n    hasOwn(b, property) &&\n    state.equals(a[property], b[property], property, property, a, b, state)\n  );\n}\n", "import {\n  areArraysEqual as areArraysEqualDefault,\n  areDatesEqual as areDatesEqualDefault,\n  areErrorsEqual as areErrorsEqualDefault,\n  areFunctionsEqual as areFunctionsEqualDefault,\n  areMapsEqual as areMapsEqualDefault,\n  areNumbersEqual as areNumbersEqualDefault,\n  areObjectsEqual as areObjectsEqualDefault,\n  areObjectsEqualStrict as areObjectsEqualStrictDefault,\n  arePrimitiveWrappersEqual as arePrimitiveWrappersEqualDefault,\n  areRegExpsEqual as areRegExpsEqualDefault,\n  areSetsEqual as areSetsEqualDefault,\n  areTypedArraysEqual as areTypedArraysEqualDefault,\n  areUrlsEqual as areUrlsEqualDefault,\n} from './equals';\nimport { combineComparators, createIsCircular } from './utils';\nimport type {\n  ComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  EqualityComparator,\n  InternalEqualityComparator,\n  State,\n} from './internalTypes';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst ERROR_TAG = '[object Error]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\nconst URL_TAG = '[object URL]';\n\nconst { isArray } = Array;\nconst isTypedArray =\n  typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nconst { assign } = Object;\nconst getTag = Object.prototype.toString.call.bind(\n  Object.prototype.toString,\n) as (a: object) => string;\n\ninterface CreateIsEqualOptions<Meta> {\n  circular: boolean;\n  comparator: EqualityComparator<Meta>;\n  createState: CreateState<Meta> | undefined;\n  equals: InternalEqualityComparator<Meta>;\n  strict: boolean;\n}\n\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nexport function createEqualityComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areErrorsEqual,\n  areFunctionsEqual,\n  areMapsEqual,\n  areNumbersEqual,\n  areObjectsEqual,\n  arePrimitiveWrappersEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  areTypedArraysEqual,\n  areUrlsEqual,\n}: ComparatorConfig<Meta>): EqualityComparator<Meta> {\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  return function comparator(a: any, b: any, state: State<Meta>): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If either of the items are nullish and fail the strictly equal check\n    // above, then they must be unequal.\n    if (a == null || b == null) {\n      return false;\n    }\n\n    const type = typeof a;\n\n    if (type !== typeof b) {\n      return false;\n    }\n\n    if (type !== 'object') {\n      if (type === 'number') {\n        return areNumbersEqual(a, b, state);\n      }\n\n      if (type === 'function') {\n        return areFunctionsEqual(a, b, state);\n      }\n\n      // If a primitive value that is not strictly equal, it must be unequal.\n      return false;\n    }\n\n    const constructor = a.constructor;\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // Constructors should match, otherwise there is potential for false positives\n    // between class and subclass or custom object and POJO.\n    if (constructor !== b.constructor) {\n      return false;\n    }\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid capturing the string tag.\n    if (constructor === Object) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n    // the string tag or doing an `instanceof` check.\n    if (isArray(a)) {\n      return areArraysEqual(a, b, state);\n    }\n\n    // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n    // capturing the string tag or comparing against all possible constructors.\n    if (isTypedArray != null && isTypedArray(a)) {\n      return areTypedArraysEqual(a, b, state);\n    }\n\n    // Try to fast-path equality checks for other complex object types in the\n    // same realm to avoid capturing the string tag. Strict equality is used\n    // instead of `instanceof` because it is more performant for the common\n    // use-case. If someone is subclassing a native class, it will be handled\n    // with the string tag comparison.\n\n    if (constructor === Date) {\n      return areDatesEqual(a, b, state);\n    }\n\n    if (constructor === RegExp) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (constructor === Map) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (constructor === Set) {\n      return areSetsEqual(a, b, state);\n    }\n\n    // Since this is a custom object, capture the string tag to determing its type.\n    // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n    const tag = getTag(a);\n\n    if (tag === DATE_TAG) {\n      return areDatesEqual(a, b, state);\n    }\n\n    // For RegExp, the properties are not enumerable, and therefore will give false positives if\n    // tested like a standard object.\n    if (tag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (tag === MAP_TAG) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (tag === SET_TAG) {\n      return areSetsEqual(a, b, state);\n    }\n\n    if (tag === OBJECT_TAG) {\n      // The exception for value comparison is custom `Promise`-like class instances. These should\n      // be treated the same as standard `Promise` objects, which means strict equality, and if\n      // it reaches this point then that strict equality comparison has already failed.\n      return (\n        typeof a.then !== 'function' &&\n        typeof b.then !== 'function' &&\n        areObjectsEqual(a, b, state)\n      );\n    }\n\n    // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === URL_TAG) {\n      return areUrlsEqual(a, b, state);\n    }\n\n    // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === ERROR_TAG) {\n      return areErrorsEqual(a, b, state);\n    }\n\n    // If an arguments tag, it should be treated as a standard object.\n    if (tag === ARGUMENTS_TAG) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n      return arePrimitiveWrappersEqual(a, b, state);\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  };\n}\n\n/**\n * Create the configuration object used for building comparators.\n */\nexport function createEqualityComparatorConfig<Meta>({\n  circular,\n  createCustomConfig,\n  strict,\n}: CustomEqualCreatorOptions<Meta>): ComparatorConfig<Meta> {\n  let config = {\n    areArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areArraysEqualDefault,\n    areDatesEqual: areDatesEqualDefault,\n    areErrorsEqual: areErrorsEqualDefault,\n    areFunctionsEqual: areFunctionsEqualDefault,\n    areMapsEqual: strict\n      ? combineComparators(areMapsEqualDefault, areObjectsEqualStrictDefault)\n      : areMapsEqualDefault,\n    areNumbersEqual: areNumbersEqualDefault,\n    areObjectsEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areObjectsEqualDefault,\n    arePrimitiveWrappersEqual: arePrimitiveWrappersEqualDefault,\n    areRegExpsEqual: areRegExpsEqualDefault,\n    areSetsEqual: strict\n      ? combineComparators(areSetsEqualDefault, areObjectsEqualStrictDefault)\n      : areSetsEqualDefault,\n    areTypedArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areTypedArraysEqualDefault,\n    areUrlsEqual: areUrlsEqualDefault,\n  };\n\n  if (createCustomConfig) {\n    config = assign({}, config, createCustomConfig(config));\n  }\n\n  if (circular) {\n    const areArraysEqual = createIsCircular(config.areArraysEqual);\n    const areMapsEqual = createIsCircular(config.areMapsEqual);\n    const areObjectsEqual = createIsCircular(config.areObjectsEqual);\n    const areSetsEqual = createIsCircular(config.areSetsEqual);\n\n    config = assign({}, config, {\n      areArraysEqual,\n      areMapsEqual,\n      areObjectsEqual,\n      areSetsEqual,\n    });\n  }\n\n  return config;\n}\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createInternalEqualityComparator<Meta>(\n  compare: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function (\n    a: any,\n    b: any,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    state: State<Meta>,\n  ) {\n    return compare(a, b, state);\n  };\n}\n\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nexport function createIsEqual<Meta>({\n  circular,\n  comparator,\n  createState,\n  equals,\n  strict,\n}: CreateIsEqualOptions<Meta>) {\n  if (createState) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      const { cache = circular ? new WeakMap() : undefined, meta } =\n        createState!();\n\n      return comparator(a, b, {\n        cache,\n        equals,\n        meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  if (circular) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      return comparator(a, b, {\n        cache: new WeakMap(),\n        equals,\n        meta: undefined as Meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  const state = {\n    cache: undefined,\n    equals,\n    meta: undefined,\n    strict,\n  } as State<Meta>;\n\n  return function isEqual<A, B>(a: A, b: B): boolean {\n    return comparator(a, b, state);\n  };\n}\n", "import {\n  createEqualityComparatorConfig,\n  createEqualityComparator,\n  createInternalEqualityComparator,\n  createIsEqual,\n} from './comparator';\nimport type { CustomEqualCreatorOptions } from './internalTypes';\nimport { sameValueZeroEqual } from './utils';\n\nexport { sameValueZeroEqual };\nexport type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  ComparatorConfig,\n  CreateCustomComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  DefaultState,\n  Dictionary,\n  EqualityComparator,\n  EqualityComparatorCreator,\n  InternalEqualityComparator,\n  PrimitiveWrapper,\n  State,\n  TypeEqualityComparator,\n  TypedArray,\n} from './internalTypes';\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport const deepEqual = createCustomEqual();\n\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nexport const strictDeepEqual = createCustomEqual({ strict: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport const circularDeepEqual = createCustomEqual({ circular: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularDeepEqual = createCustomEqual({\n  circular: true,\n  strict: true,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport const shallowEqual = createCustomEqual({\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nexport const strictShallowEqual = createCustomEqual({\n  strict: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport const circularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n  strict: true,\n});\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  options: CustomEqualCreatorOptions<Meta> = {},\n) {\n  const {\n    circular = false,\n    createInternalComparator: createCustomInternalComparator,\n    createState,\n    strict = false,\n  } = options;\n\n  const config = createEqualityComparatorConfig<Meta>(options);\n  const comparator = createEqualityComparator(config);\n  const equals = createCustomInternalComparator\n    ? createCustomInternalComparator(comparator)\n    : createInternalEqualityComparator(comparator);\n\n  return createIsEqual({ circular, comparator, createState, equals, strict });\n}\n"], "mappings": ";;;;;EASQ,IAAAA,mBAAmB,GAA4BC,MAAM,CAAAD,mBAAlC;IAAEE,qBAAqB,GAAKD,MAAM,CAAAC,qBAAX;EAC1C,IAAAC,cAAc,GAAKF,MAAM,CAACG,SAAS,CAAAD,cAArB;EAEtB;;;EAGgB,SAAAE,kBAAkBA,CAChCC,WAAwC,EACxCC,WAAwC;IAExC,OAAO,SAASC,OAAOA,CAAOC,CAAI,EAAEC,CAAI,EAAEC,KAAkB;MAC1D,OAAOL,WAAW,CAACG,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,IAAIJ,WAAW,CAACE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAC7D,CAAC;EACH;EAEA;;;;;EAKM,SAAUC,gBAAgBA,CAE9BC,aAA4B;IAC5B,OAAO,SAASC,UAAUA,CACxBL,CAAM,EACNC,CAAM,EACNC,KAAqC;MAErC,IAAI,CAACF,CAAC,IAAI,CAACC,CAAC,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;QAC9D,OAAOG,aAAa,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAClC;MAEO,IAAAI,KAAK,GAAKJ,KAAK,CAAAI,KAAV;MAEb,IAAMC,OAAO,GAAGD,KAAK,CAACE,GAAG,CAACR,CAAC,CAAC;MAC5B,IAAMS,OAAO,GAAGH,KAAK,CAACE,GAAG,CAACP,CAAC,CAAC;MAE5B,IAAIM,OAAO,IAAIE,OAAO,EAAE;QACtB,OAAOF,OAAO,KAAKN,CAAC,IAAIQ,OAAO,KAAKT,CAAC;MACtC;MAEDM,KAAK,CAACI,GAAG,CAACV,CAAC,EAAEC,CAAC,CAAC;MACfK,KAAK,CAACI,GAAG,CAACT,CAAC,EAAED,CAAC,CAAC;MAEf,IAAMW,MAAM,GAAGP,aAAa,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAEzCI,KAAK,CAACM,MAAM,CAACZ,CAAC,CAAC;MACfM,KAAK,CAACM,MAAM,CAACX,CAAC,CAAC;MAEf,OAAOU,MAAM;IACf,CAAkB;EACpB;EAEA;;;;EAIM,SAAUE,mBAAmBA,CACjCC,MAAkB;IAElB,OAAQvB,mBAAmB,CAACuB,MAAM,CAA4B,CAACC,MAAM,CACnEtB,qBAAqB,CAACqB,MAAM,CAAC,CAC9B;EACH;EAEA;;;EAGO,IAAME,MAAM,GACjBxB,MAAM,CAACwB,MAAM,IACZ,UAACF,MAAkB,EAAEG,QAAkC;IACtD,OAAAvB,cAAc,CAACwB,IAAI,CAACJ,MAAM,EAAEG,QAAQ,CAAC;EAArC,CAAsC;EAE1C;;;EAGgB,SAAAE,kBAAkBA,CAACnB,CAAM,EAAEC,CAAM;IAC/C,OAAOD,CAAC,KAAKC,CAAC,IAAK,CAACD,CAAC,IAAI,CAACC,CAAC,IAAID,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAE;EACpD;EC/EA,IAAMmB,YAAY,GAAG,KAAK;EAC1B,IAAMC,YAAY,GAAG,KAAK;EAC1B,IAAMC,WAAW,GAAG,QAAQ;EAEpB,IAAAC,wBAAwB,GAAW/B,MAAM,CAAA+B,wBAAjB;IAAEC,IAAI,GAAKhC,MAAM,CAAAgC,IAAX;EAEtC;;;WAGgBC,cAAcA,CAACzB,CAAQ,EAAEC,CAAQ,EAAEC,KAAiB;IAClE,IAAIwB,KAAK,GAAG1B,CAAC,CAAC2B,MAAM;IAEpB,IAAI1B,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;MACtB,OAAO,KAAK;IACb;IAED,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;MAClB,IAAI,CAACxB,KAAK,CAAC0B,MAAM,CAAC5B,CAAC,CAAC0B,KAAK,CAAC,EAAEzB,CAAC,CAACyB,KAAK,CAAC,EAAEA,KAAK,EAAEA,KAAK,EAAE1B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,EAAE;QAChE,OAAO,KAAK;MACb;IACF;IAED,OAAO,IAAI;EACb;EAEA;;;EAGgB,SAAA2B,aAAaA,CAAC7B,CAAO,EAAEC,CAAO;IAC5C,OAAOkB,kBAAkB,CAACnB,CAAC,CAAC8B,OAAO,EAAE,EAAE7B,CAAC,CAAC6B,OAAO,EAAE,CAAC;EACrD;EAEA;;;EAGgB,SAAAC,cAAcA,CAAC/B,CAAQ,EAAEC,CAAQ;IAC/C,OACED,CAAC,CAACgC,IAAI,KAAK/B,CAAC,CAAC+B,IAAI,IACjBhC,CAAC,CAACiC,OAAO,KAAKhC,CAAC,CAACgC,OAAO,IACvBjC,CAAC,CAACkC,KAAK,KAAKjC,CAAC,CAACiC,KAAK,IACnBlC,CAAC,CAACmC,KAAK,KAAKlC,CAAC,CAACkC,KAAK;EAEvB;EAEA;;;EAGgB,SAAAC,iBAAiBA,CAC/BpC,CAA0B,EAC1BC,CAA0B;IAE1B,OAAOD,CAAC,KAAKC,CAAC;EAChB;EAEA;;;WAGgBoC,YAAYA,CAC1BrC,CAAgB,EAChBC,CAAgB,EAChBC,KAAiB;IAEjB,IAAMoC,IAAI,GAAGtC,CAAC,CAACsC,IAAI;IAEnB,IAAIA,IAAI,KAAKrC,CAAC,CAACqC,IAAI,EAAE;MACnB,OAAO,KAAK;IACb;IAED,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,IAAI;IACZ;IAED,IAAMC,cAAc,GAA4B,IAAIC,KAAK,CAACF,IAAI,CAAC;IAC/D,IAAMG,SAAS,GAAGzC,CAAC,CAAC0C,OAAO,EAAE;IAE7B,IAAIC,OAAmC;IACvC,IAAIC,OAAmC;IACvC,IAAIlB,KAAK,GAAG,CAAC;IAEb,OAAQiB,OAAO,GAAGF,SAAS,CAACI,IAAI,EAAE,EAAG;MACnC,IAAIF,OAAO,CAACG,IAAI,EAAE;QAChB;MACD;MAED,IAAMC,SAAS,GAAG9C,CAAC,CAACyC,OAAO,EAAE;MAE7B,IAAIM,QAAQ,GAAG,KAAK;MACpB,IAAIC,UAAU,GAAG,CAAC;MAElB,OAAQL,OAAO,GAAGG,SAAS,CAACF,IAAI,EAAE,EAAG;QACnC,IAAID,OAAO,CAACE,IAAI,EAAE;UAChB;QACD;QAED,IAAIP,cAAc,CAACU,UAAU,CAAC,EAAE;UAC9BA,UAAU,EAAE;UACZ;QACD;QAED,IAAMC,MAAM,GAAGP,OAAO,CAACQ,KAAK;QAC5B,IAAMC,MAAM,GAAGR,OAAO,CAACO,KAAK;QAE5B,IACEjD,KAAK,CAAC0B,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAE1B,KAAK,EAAEuB,UAAU,EAAEjD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,IAClEA,KAAK,CAAC0B,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAEpD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,EACrE;UACA8C,QAAQ,GAAGT,cAAc,CAACU,UAAU,CAAC,GAAG,IAAI;UAC5C;QACD;QAEDA,UAAU,EAAE;MACb;MAED,IAAI,CAACD,QAAQ,EAAE;QACb,OAAO,KAAK;MACb;MAEDtB,KAAK,EAAE;IACR;IAED,OAAO,IAAI;EACb;EAEA;;;EAGO,IAAM2B,eAAe,GAAGlC,kBAAkB;EAEjD;;;WAGgBmC,eAAeA,CAC7BtD,CAAa,EACbC,CAAa,EACbC,KAAiB;IAEjB,IAAMqD,UAAU,GAAG/B,IAAI,CAACxB,CAAC,CAAC;IAE1B,IAAI0B,KAAK,GAAG6B,UAAU,CAAC5B,MAAM;IAE7B,IAAIH,IAAI,CAACvB,CAAC,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;MAC5B,OAAO,KAAK;IACb;;;;;IAMD,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;MAClB,IAAI,CAAC8B,eAAe,CAACxD,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEqD,UAAU,CAAC7B,KAAK,CAAE,CAAC,EAAE;QACrD,OAAO,KAAK;MACb;IACF;IAED,OAAO,IAAI;EACb;EAEA;;;WAGgB+B,qBAAqBA,CACnCzD,CAAa,EACbC,CAAa,EACbC,KAAiB;IAEjB,IAAMqD,UAAU,GAAG1C,mBAAmB,CAACb,CAAC,CAAC;IAEzC,IAAI0B,KAAK,GAAG6B,UAAU,CAAC5B,MAAM;IAE7B,IAAId,mBAAmB,CAACZ,CAAC,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;MAC3C,OAAO,KAAK;IACb;IAED,IAAIT,QAAyB;IAC7B,IAAIyC,WAAwD;IAC5D,IAAIC,WAAwD;;;;;IAM5D,OAAOjC,KAAK,EAAE,GAAG,CAAC,EAAE;MAClBT,QAAQ,GAAGsC,UAAU,CAAC7B,KAAK,CAAE;MAE7B,IAAI,CAAC8B,eAAe,CAACxD,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEe,QAAQ,CAAC,EAAE;QAC3C,OAAO,KAAK;MACb;MAEDyC,WAAW,GAAGnC,wBAAwB,CAACvB,CAAC,EAAEiB,QAAQ,CAAC;MACnD0C,WAAW,GAAGpC,wBAAwB,CAACtB,CAAC,EAAEgB,QAAQ,CAAC;MAEnD,IACE,CAACyC,WAAW,IAAIC,WAAW,MAC1B,CAACD,WAAW,IACX,CAACC,WAAW,IACZD,WAAW,CAACE,YAAY,KAAKD,WAAW,CAACC,YAAY,IACrDF,WAAW,CAACG,UAAU,KAAKF,WAAW,CAACE,UAAU,IACjDH,WAAW,CAACI,QAAQ,KAAKH,WAAW,CAACG,QAAQ,CAAC,EAChD;QACA,OAAO,KAAK;MACb;IACF;IAED,OAAO,IAAI;EACb;EAEA;;;EAGgB,SAAAC,yBAAyBA,CACvC/D,CAAmB,EACnBC,CAAmB;IAEnB,OAAOkB,kBAAkB,CAACnB,CAAC,CAACgE,OAAO,EAAE,EAAE/D,CAAC,CAAC+D,OAAO,EAAE,CAAC;EACrD;EAEA;;;EAGgB,SAAAC,eAAeA,CAACjE,CAAS,EAAEC,CAAS;IAClD,OAAOD,CAAC,CAACkE,MAAM,KAAKjE,CAAC,CAACiE,MAAM,IAAIlE,CAAC,CAACmE,KAAK,KAAKlE,CAAC,CAACkE,KAAK;EACrD;EAEA;;;WAGgBC,YAAYA,CAC1BpE,CAAW,EACXC,CAAW,EACXC,KAAiB;IAEjB,IAAMoC,IAAI,GAAGtC,CAAC,CAACsC,IAAI;IAEnB,IAAIA,IAAI,KAAKrC,CAAC,CAACqC,IAAI,EAAE;MACnB,OAAO,KAAK;IACb;IAED,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,IAAI;IACZ;IAED,IAAMC,cAAc,GAA4B,IAAIC,KAAK,CAACF,IAAI,CAAC;IAC/D,IAAMG,SAAS,GAAGzC,CAAC,CAACqE,MAAM,EAAE;IAE5B,IAAI1B,OAA4B;IAChC,IAAIC,OAA4B;IAEhC,OAAQD,OAAO,GAAGF,SAAS,CAACI,IAAI,EAAE,EAAG;MACnC,IAAIF,OAAO,CAACG,IAAI,EAAE;QAChB;MACD;MAED,IAAMC,SAAS,GAAG9C,CAAC,CAACoE,MAAM,EAAE;MAE5B,IAAIrB,QAAQ,GAAG,KAAK;MACpB,IAAIC,UAAU,GAAG,CAAC;MAElB,OAAQL,OAAO,GAAGG,SAAS,CAACF,IAAI,EAAE,EAAG;QACnC,IAAID,OAAO,CAACE,IAAI,EAAE;UAChB;QACD;QAED,IACE,CAACP,cAAc,CAACU,UAAU,CAAC,IAC3B/C,KAAK,CAAC0B,MAAM,CACVe,OAAO,CAACQ,KAAK,EACbP,OAAO,CAACO,KAAK,EACbR,OAAO,CAACQ,KAAK,EACbP,OAAO,CAACO,KAAK,EACbnD,CAAC,EACDC,CAAC,EACDC,KAAK,CACN,EACD;UACA8C,QAAQ,GAAGT,cAAc,CAACU,UAAU,CAAC,GAAG,IAAI;UAC5C;QACD;QAEDA,UAAU,EAAE;MACb;MAED,IAAI,CAACD,QAAQ,EAAE;QACb,OAAO,KAAK;MACb;IACF;IAED,OAAO,IAAI;EACb;EAEA;;;EAGgB,SAAAsB,mBAAmBA,CAACtE,CAAa,EAAEC,CAAa;IAC9D,IAAIyB,KAAK,GAAG1B,CAAC,CAAC2B,MAAM;IAEpB,IAAI1B,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;MACtB,OAAO,KAAK;IACb;IAED,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;MAClB,IAAI1B,CAAC,CAAC0B,KAAK,CAAC,KAAKzB,CAAC,CAACyB,KAAK,CAAC,EAAE;QACzB,OAAO,KAAK;MACb;IACF;IAED,OAAO,IAAI;EACb;EAEA;;;EAGgB,SAAA6C,YAAYA,CAACvE,CAAM,EAAEC,CAAM;IACzC,OACED,CAAC,CAACwE,QAAQ,KAAKvE,CAAC,CAACuE,QAAQ,IACzBxE,CAAC,CAACyE,QAAQ,KAAKxE,CAAC,CAACwE,QAAQ,IACzBzE,CAAC,CAAC0E,QAAQ,KAAKzE,CAAC,CAACyE,QAAQ,IACzB1E,CAAC,CAAC2E,IAAI,KAAK1E,CAAC,CAAC0E,IAAI,IACjB3E,CAAC,CAAC4E,IAAI,KAAK3E,CAAC,CAAC2E,IAAI,IACjB5E,CAAC,CAAC6E,QAAQ,KAAK5E,CAAC,CAAC4E,QAAQ,IACzB7E,CAAC,CAAC8E,QAAQ,KAAK7E,CAAC,CAAC6E,QAAQ;EAE7B;EAEA,SAAStB,eAAeA,CACtBxD,CAAa,EACbC,CAAa,EACbC,KAAiB,EACjBe,QAAyB;IAEzB,IACE,CAACA,QAAQ,KAAKK,WAAW,IACvBL,QAAQ,KAAKI,YAAY,IACzBJ,QAAQ,KAAKG,YAAY,MAC1BpB,CAAC,CAAC+E,QAAQ,IAAI9E,CAAC,CAAC8E,QAAQ,CAAC,EAC1B;MACA,OAAO,IAAI;IACZ;IAED,OACE/D,MAAM,CAACf,CAAC,EAAEgB,QAAQ,CAAC,IACnBf,KAAK,CAAC0B,MAAM,CAAC5B,CAAC,CAACiB,QAAQ,CAAC,EAAEhB,CAAC,CAACgB,QAAQ,CAAC,EAAEA,QAAQ,EAAEA,QAAQ,EAAEjB,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;EAE3E;ECrUA,IAAM8E,aAAa,GAAG,oBAAoB;EAC1C,IAAMC,WAAW,GAAG,kBAAkB;EACtC,IAAMC,QAAQ,GAAG,eAAe;EAChC,IAAMC,SAAS,GAAG,gBAAgB;EAClC,IAAMC,OAAO,GAAG,cAAc;EAC9B,IAAMC,UAAU,GAAG,iBAAiB;EACpC,IAAMC,UAAU,GAAG,iBAAiB;EACpC,IAAMC,WAAW,GAAG,iBAAiB;EACrC,IAAMC,OAAO,GAAG,cAAc;EAC9B,IAAMC,UAAU,GAAG,iBAAiB;EACpC,IAAMC,OAAO,GAAG,cAAc;EAEtB,IAAAC,OAAO,GAAKnD,KAAK,CAAAmD,OAAV;EACf,IAAMC,YAAY,GAChB,OAAOC,WAAW,KAAK,UAAU,IAAIA,WAAW,CAACC,MAAM,GACnDD,WAAW,CAACC,MAAM,GAClB,IAAI;EACF,IAAAC,MAAM,GAAKvG,MAAM,CAAAuG,MAAX;EACd,IAAMC,MAAM,GAAGxG,MAAM,CAACG,SAAS,CAACsG,QAAQ,CAAC/E,IAAI,CAACgF,IAAI,CAChD1G,MAAM,CAACG,SAAS,CAACsG,QAAQ,CACD;EAU1B;;;EAGM,SAAUE,wBAAwBA,CAAOC,EAatB;IAZvB,IAAA3E,cAAc,GAAA2E,EAAA,CAAA3E,cAAA;MACdI,aAAa,GAAAuE,EAAA,CAAAvE,aAAA;MACbE,cAAc,GAAAqE,EAAA,CAAArE,cAAA;MACdK,iBAAiB,GAAAgE,EAAA,CAAAhE,iBAAA;MACjBC,YAAY,GAAA+D,EAAA,CAAA/D,YAAA;MACZgB,eAAe,GAAA+C,EAAA,CAAA/C,eAAA;MACfC,eAAe,GAAA8C,EAAA,CAAA9C,eAAA;MACfS,yBAAyB,GAAAqC,EAAA,CAAArC,yBAAA;MACzBE,eAAe,GAAAmC,EAAA,CAAAnC,eAAA;MACfG,YAAY,GAAAgC,EAAA,CAAAhC,YAAA;MACZE,mBAAmB,GAAA8B,EAAA,CAAA9B,mBAAA;MACnBC,YAAY,GAAA6B,EAAA,CAAA7B,YAAA;IAEZ;;;IAGA,OAAO,SAAS8B,UAAUA,CAACrG,CAAM,EAAEC,CAAM,EAAEC,KAAkB;;MAE3D,IAAIF,CAAC,KAAKC,CAAC,EAAE;QACX,OAAO,IAAI;MACZ;;;MAID,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE;QAC1B,OAAO,KAAK;MACb;MAED,IAAMqG,IAAI,GAAG,OAAOtG,CAAC;MAErB,IAAIsG,IAAI,KAAK,OAAOrG,CAAC,EAAE;QACrB,OAAO,KAAK;MACb;MAED,IAAIqG,IAAI,KAAK,QAAQ,EAAE;QACrB,IAAIA,IAAI,KAAK,QAAQ,EAAE;UACrB,OAAOjD,eAAe,CAACrD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;QACpC;QAED,IAAIoG,IAAI,KAAK,UAAU,EAAE;UACvB,OAAOlE,iBAAiB,CAACpC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;QACtC;;QAGD,OAAO,KAAK;MACb;MAED,IAAMqG,WAAW,GAAGvG,CAAC,CAACuG,WAAW;;;;;;;;;;;MAajC,IAAIA,WAAW,KAAKtG,CAAC,CAACsG,WAAW,EAAE;QACjC,OAAO,KAAK;MACb;;;;MAKD,IAAIA,WAAW,KAAK/G,MAAM,EAAE;QAC1B,OAAO8D,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpC;;;MAID,IAAIyF,OAAO,CAAC3F,CAAC,CAAC,EAAE;QACd,OAAOyB,cAAc,CAACzB,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACnC;;;MAID,IAAI0F,YAAY,IAAI,IAAI,IAAIA,YAAY,CAAC5F,CAAC,CAAC,EAAE;QAC3C,OAAOsE,mBAAmB,CAACtE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACxC;;;;;;MAQD,IAAIqG,WAAW,KAAKC,IAAI,EAAE;QACxB,OAAO3E,aAAa,CAAC7B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAClC;MAED,IAAIqG,WAAW,KAAKE,MAAM,EAAE;QAC1B,OAAOxC,eAAe,CAACjE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpC;MAED,IAAIqG,WAAW,KAAKG,GAAG,EAAE;QACvB,OAAOrE,YAAY,CAACrC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACjC;MAED,IAAIqG,WAAW,KAAKI,GAAG,EAAE;QACvB,OAAOvC,YAAY,CAACpE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACjC;;;MAID,IAAM0G,GAAG,GAAGZ,MAAM,CAAChG,CAAC,CAAC;MAErB,IAAI4G,GAAG,KAAK1B,QAAQ,EAAE;QACpB,OAAOrD,aAAa,CAAC7B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAClC;;;MAID,IAAI0G,GAAG,KAAKrB,WAAW,EAAE;QACvB,OAAOtB,eAAe,CAACjE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpC;MAED,IAAI0G,GAAG,KAAKxB,OAAO,EAAE;QACnB,OAAO/C,YAAY,CAACrC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACjC;MAED,IAAI0G,GAAG,KAAKpB,OAAO,EAAE;QACnB,OAAOpB,YAAY,CAACpE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACjC;MAED,IAAI0G,GAAG,KAAKtB,UAAU,EAAE;;;;QAItB,OACE,OAAOtF,CAAC,CAAC6G,IAAI,KAAK,UAAU,IAC5B,OAAO5G,CAAC,CAAC4G,IAAI,KAAK,UAAU,IAC5BvD,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAE/B;;;MAID,IAAI0G,GAAG,KAAKlB,OAAO,EAAE;QACnB,OAAOnB,YAAY,CAACvE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACjC;;;MAID,IAAI0G,GAAG,KAAKzB,SAAS,EAAE;QACrB,OAAOpD,cAAc,CAAC/B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACnC;;MAGD,IAAI0G,GAAG,KAAK5B,aAAa,EAAE;QACzB,OAAO1B,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpC;;;;MAKD,IAAI0G,GAAG,KAAK3B,WAAW,IAAI2B,GAAG,KAAKvB,UAAU,IAAIuB,GAAG,KAAKnB,UAAU,EAAE;QACnE,OAAO1B,yBAAyB,CAAC/D,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MAC9C;;;;;;;;;;;;MAaD,OAAO,KAAK;IACd,CAAC;EACH;EAEA;;;EAGM,SAAU4G,8BAA8BA,CAAOV,EAInB;IAHhC,IAAAW,QAAQ,GAAAX,EAAA,CAAAW,QAAA;MACRC,kBAAkB,GAAAZ,EAAA,CAAAY,kBAAA;MAClBC,MAAM,GAAAb,EAAA,CAAAa,MAAA;IAEN,IAAIC,MAAM,GAAG;MACXzF,cAAc,EAAEwF,MAAM,GAClBxD,qBAA4B,GAC5BhC,cAAqB;MACzBI,aAAa,EAAEA,aAAoB;MACnCE,cAAc,EAAEA,cAAqB;MACrCK,iBAAiB,EAAEA,iBAAwB;MAC3CC,YAAY,EAAE4E,MAAM,GAChBrH,kBAAkB,CAACyC,YAAmB,EAAEoB,qBAA4B,CAAC,GACrEpB,YAAmB;MACvBgB,eAAe,EAAEA,eAAsB;MACvCC,eAAe,EAAE2D,MAAM,GACnBxD,qBAA4B,GAC5BH,eAAsB;MAC1BS,yBAAyB,EAAEA,yBAAgC;MAC3DE,eAAe,EAAEA,eAAsB;MACvCG,YAAY,EAAE6C,MAAM,GAChBrH,kBAAkB,CAACwE,YAAmB,EAAEX,qBAA4B,CAAC,GACrEW,YAAmB;MACvBE,mBAAmB,EAAE2C,MAAM,GACvBxD,qBAA4B,GAC5Ba,mBAA0B;MAC9BC,YAAY,EAAEA;KACf;IAED,IAAIyC,kBAAkB,EAAE;MACtBE,MAAM,GAAGnB,MAAM,CAAC,EAAE,EAAEmB,MAAM,EAAEF,kBAAkB,CAACE,MAAM,CAAC,CAAC;IACxD;IAED,IAAIH,QAAQ,EAAE;MACZ,IAAMI,gBAAc,GAAGhH,gBAAgB,CAAC+G,MAAM,CAACzF,cAAc,CAAC;MAC9D,IAAM2F,cAAY,GAAGjH,gBAAgB,CAAC+G,MAAM,CAAC7E,YAAY,CAAC;MAC1D,IAAMgF,iBAAe,GAAGlH,gBAAgB,CAAC+G,MAAM,CAAC5D,eAAe,CAAC;MAChE,IAAMgE,cAAY,GAAGnH,gBAAgB,CAAC+G,MAAM,CAAC9C,YAAY,CAAC;MAE1D8C,MAAM,GAAGnB,MAAM,CAAC,EAAE,EAAEmB,MAAM,EAAE;QAC1BzF,cAAc,EAAA0F,gBAAA;QACd9E,YAAY,EAAA+E,cAAA;QACZ9D,eAAe,EAAA+D,iBAAA;QACfjD,YAAY,EAAAkD;MACb,EAAC;IACH;IAED,OAAOJ,MAAM;EACf;EAEA;;;;EAIM,SAAUK,gCAAgCA,CAC9CC,OAAiC;IAEjC,OAAO,UACLxH,CAAM,EACNC,CAAM,EACNwH,YAAiB,EACjBC,YAAiB,EACjBC,QAAa,EACbC,QAAa,EACb1H,KAAkB;MAElB,OAAOsH,OAAO,CAACxH,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAC7B,CAAC;EACH;EAEA;;;EAGM,SAAU2H,aAAaA,CAAOzB,EAMP;IAL3B,IAAAW,QAAQ,GAAAX,EAAA,CAAAW,QAAA;MACRV,UAAU,GAAAD,EAAA,CAAAC,UAAA;MACVyB,WAAW,GAAA1B,EAAA,CAAA0B,WAAA;MACXlG,MAAM,GAAAwE,EAAA,CAAAxE,MAAA;MACNqF,MAAM,GAAAb,EAAA,CAAAa,MAAA;IAEN,IAAIa,WAAW,EAAE;MACf,OAAO,SAAS/H,OAAOA,CAAOC,CAAI,EAAEC,CAAI;QAChC,IAAAmG,EAAA,GACJ0B,WAAY,EAAE;UADRC,EAAA,GAAA3B,EAAA,CAAA9F,KAA4C;UAA5CA,KAAK,GAAGyH,EAAA,cAAAhB,QAAQ,GAAG,IAAIiB,OAAO,EAAE,GAAGC,SAAS,GAAAF,EAAA;UAAEG,IAAI,GAAA9B,EAAA,CAAA8B,IAC1C;QAEhB,OAAO7B,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAE;UACtBK,KAAK,EAAAA,KAAA;UACLsB,MAAM,EAAAA,MAAA;UACNsG,IAAI,EAAAA,IAAA;UACJjB,MAAM,EAAAA;QACQ,EAAC;MACnB,CAAC;IACF;IAED,IAAIF,QAAQ,EAAE;MACZ,OAAO,SAAShH,OAAOA,CAAOC,CAAI,EAAEC,CAAI;QACtC,OAAOoG,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAE;UACtBK,KAAK,EAAE,IAAI0H,OAAO,EAAE;UACpBpG,MAAM,EAAAA,MAAA;UACNsG,IAAI,EAAED,SAAiB;UACvBhB,MAAM,EAAAA;QACQ,EAAC;MACnB,CAAC;IACF;IAED,IAAM/G,KAAK,GAAG;MACZI,KAAK,EAAE2H,SAAS;MAChBrG,MAAM,EAAAA,MAAA;MACNsG,IAAI,EAAED,SAAS;MACfhB,MAAM,EAAAA;KACQ;IAEhB,OAAO,SAASlH,OAAOA,CAAOC,CAAI,EAAEC,CAAI;MACtC,OAAOoG,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAChC,CAAC;EACH;;ECtUA;;;EAGa,IAAAiI,SAAS,GAAGC,iBAAiB;EAE1C;;;EAGO,IAAMC,eAAe,GAAGD,iBAAiB,CAAC;IAAEnB,MAAM,EAAE;EAAI,CAAE;EAEjE;;;EAGO,IAAMqB,iBAAiB,GAAGF,iBAAiB,CAAC;IAAErB,QAAQ,EAAE;EAAI,CAAE;EAErE;;;;EAIO,IAAMwB,uBAAuB,GAAGH,iBAAiB,CAAC;IACvDrB,QAAQ,EAAE,IAAI;IACdE,MAAM,EAAE;EACT;EAED;;;EAGO,IAAMuB,YAAY,GAAGJ,iBAAiB,CAAC;IAC5CK,wBAAwB,EAAE,SAAAA,CAAA;MAAM,OAAAtH,kBAAkB;IAAA;EACnD;EAED;;;EAGO,IAAMuH,kBAAkB,GAAGN,iBAAiB,CAAC;IAClDnB,MAAM,EAAE,IAAI;IACZwB,wBAAwB,EAAE,SAAAA,CAAA;MAAM,OAAAtH,kBAAkB;IAAA;EACnD;EAED;;;EAGO,IAAMwH,oBAAoB,GAAGP,iBAAiB,CAAC;IACpDrB,QAAQ,EAAE,IAAI;IACd0B,wBAAwB,EAAE,SAAAA,CAAA;MAAM,OAAAtH,kBAAkB;IAAA;EACnD;EAED;;;;EAIO,IAAMyH,0BAA0B,GAAGR,iBAAiB,CAAC;IAC1DrB,QAAQ,EAAE,IAAI;IACd0B,wBAAwB,EAAE,SAAAA,CAAA;MAAM,OAAAtH,kBAAkB;IAAA;IAClD8F,MAAM,EAAE;EACT;EAED;;;;;;;;EAQM,SAAUmB,iBAAiBA,CAC/BS,OAA6C;IAA7C,IAAAA,OAAA;MAAAA,OAA6C;IAAA;IAG3C,IAAAzC,EAAA,GAIEyC,OAAO,CAAA9B,QAJO;MAAhBA,QAAQ,GAAGX,EAAA,mBAAK,GAAAA,EAAA;MACU0C,8BAA8B,GAGtDD,OAAO,CAAAJ,wBAH+C;MACxDX,WAAW,GAETe,OAAO,CAFEf,WAAA;MACXC,EACE,GAAAc,OAAO,CADK5B,MAAA;MAAdA,MAAM,GAAAc,EAAA,cAAG,KAAK,GAAAA,EAAA;IAGhB,IAAMb,MAAM,GAAGJ,8BAA8B,CAAO+B,OAAO,CAAC;IAC5D,IAAMxC,UAAU,GAAGF,wBAAwB,CAACe,MAAM,CAAC;IACnD,IAAMtF,MAAM,GAAGkH,8BAA8B,GACzCA,8BAA8B,CAACzC,UAAU,CAAC,GAC1CkB,gCAAgC,CAAClB,UAAU,CAAC;IAEhD,OAAOwB,aAAa,CAAC;MAAEd,QAAQ,EAAAA,QAAA;MAAEV,UAAU,EAAAA,UAAA;MAAEyB,WAAW,EAAAA,WAAA;MAAElG,MAAM,EAAAA,MAAA;MAAEqF,MAAM,EAAAA;IAAA,CAAE,CAAC;EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "script"}