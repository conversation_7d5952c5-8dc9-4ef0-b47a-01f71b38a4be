{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quickselect;\nvar _sort = require(\"./sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = array.length - 1, compare) {\n  compare = compare === undefined ? _sort.ascendingDefined : (0, _sort.compareDefined)(compare);\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "quickselect", "_sort", "require", "array", "k", "left", "right", "length", "compare", "undefined", "ascendingDefined", "compareDefined", "n", "m", "z", "Math", "log", "s", "exp", "sd", "sqrt", "newLeft", "max", "floor", "newRight", "min", "t", "i", "j", "swap"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/quickselect.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quickselect;\n\nvar _sort = require(\"./sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = array.length - 1, compare) {\n  compare = compare === undefined ? _sort.ascendingDefined : (0, _sort.compareDefined)(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n\n      while (compare(array[i], t) < 0) ++i;\n\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,WAAW;AAE7B,IAAIC,KAAK,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAEhC;AACA;AACA,SAASF,WAAWA,CAACG,KAAK,EAAEC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAGH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAEC,OAAO,EAAE;EAC1EA,OAAO,GAAGA,OAAO,KAAKC,SAAS,GAAGR,KAAK,CAACS,gBAAgB,GAAG,CAAC,CAAC,EAAET,KAAK,CAACU,cAAc,EAAEH,OAAO,CAAC;EAE7F,OAAOF,KAAK,GAAGD,IAAI,EAAE;IACnB,IAAIC,KAAK,GAAGD,IAAI,GAAG,GAAG,EAAE;MACtB,MAAMO,CAAC,GAAGN,KAAK,GAAGD,IAAI,GAAG,CAAC;MAC1B,MAAMQ,CAAC,GAAGT,CAAC,GAAGC,IAAI,GAAG,CAAC;MACtB,MAAMS,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,CAAC;MACrB,MAAMK,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAGJ,CAAC,GAAG,CAAC,CAAC;MACnC,MAAMK,EAAE,GAAG,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACN,CAAC,GAAGG,CAAC,IAAIL,CAAC,GAAGK,CAAC,CAAC,GAAGL,CAAC,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,MAAMS,OAAO,GAAGN,IAAI,CAACO,GAAG,CAACjB,IAAI,EAAEU,IAAI,CAACQ,KAAK,CAACnB,CAAC,GAAGS,CAAC,GAAGI,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MAC9D,MAAMK,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACnB,KAAK,EAAES,IAAI,CAACQ,KAAK,CAACnB,CAAC,GAAG,CAACQ,CAAC,GAAGC,CAAC,IAAII,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MACtEnB,WAAW,CAACG,KAAK,EAAEC,CAAC,EAAEiB,OAAO,EAAEG,QAAQ,EAAEhB,OAAO,CAAC;IACnD;IAEA,MAAMkB,CAAC,GAAGvB,KAAK,CAACC,CAAC,CAAC;IAClB,IAAIuB,CAAC,GAAGtB,IAAI;IACZ,IAAIuB,CAAC,GAAGtB,KAAK;IACbuB,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAED,CAAC,CAAC;IACpB,IAAII,OAAO,CAACL,KAAK,CAACG,KAAK,CAAC,EAAEoB,CAAC,CAAC,GAAG,CAAC,EAAEG,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAEC,KAAK,CAAC;IAE1D,OAAOqB,CAAC,GAAGC,CAAC,EAAE;MACZC,IAAI,CAAC1B,KAAK,EAAEwB,CAAC,EAAEC,CAAC,CAAC,EAAE,EAAED,CAAC,EAAE,EAAEC,CAAC;MAE3B,OAAOpB,OAAO,CAACL,KAAK,CAACwB,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;MAEpC,OAAOnB,OAAO,CAACL,KAAK,CAACyB,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEE,CAAC;IACtC;IAEA,IAAIpB,OAAO,CAACL,KAAK,CAACE,IAAI,CAAC,EAAEqB,CAAC,CAAC,KAAK,CAAC,EAAEG,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAEuB,CAAC,CAAC,CAAC,KAAK,EAAEA,CAAC,EAAEC,IAAI,CAAC1B,KAAK,EAAEyB,CAAC,EAAEtB,KAAK,CAAC;IACvF,IAAIsB,CAAC,IAAIxB,CAAC,EAAEC,IAAI,GAAGuB,CAAC,GAAG,CAAC;IACxB,IAAIxB,CAAC,IAAIwB,CAAC,EAAEtB,KAAK,GAAGsB,CAAC,GAAG,CAAC;EAC3B;EAEA,OAAOzB,KAAK;AACd;AAEA,SAAS0B,IAAIA,CAAC1B,KAAK,EAAEwB,CAAC,EAAEC,CAAC,EAAE;EACzB,MAAMF,CAAC,GAAGvB,KAAK,CAACwB,CAAC,CAAC;EAClBxB,KAAK,CAACwB,CAAC,CAAC,GAAGxB,KAAK,CAACyB,CAAC,CAAC;EACnBzB,KAAK,CAACyB,CAAC,CAAC,GAAGF,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}