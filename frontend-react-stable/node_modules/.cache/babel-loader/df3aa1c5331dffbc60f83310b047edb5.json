{"ast": null, "code": "import padEnd from 'lodash/padEnd';\nimport * as React from 'react';\nvar StatisticNumber = function StatisticNumber(props) {\n  var value = props.value,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    _props$groupSeparator = props.groupSeparator,\n    groupSeparator = _props$groupSeparator === void 0 ? '' : _props$groupSeparator,\n    prefixCls = props.prefixCls;\n  var valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    var val = String(value);\n    var cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      var negative = cells[1];\n      var int = cells[2] || '0';\n      var decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = padEnd(decimal, precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = \"\".concat(decimalSeparator).concat(decimal);\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: \"\".concat(prefixCls, \"-content-value-int\")\n      }, negative, int), decimal && /*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: \"\".concat(prefixCls, \"-content-value-decimal\")\n      }, decimal)];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-value\")\n  }, valueNode);\n};\nexport default StatisticNumber;", "map": {"version": 3, "names": ["padEnd", "React", "StatisticNumber", "props", "value", "formatter", "precision", "decimalSeparator", "_props$groupSeparator", "groupSeparator", "prefixCls", "valueNode", "val", "String", "cells", "match", "negative", "int", "decimal", "replace", "slice", "concat", "createElement", "key", "className"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/statistic/Number.js"], "sourcesContent": ["import padEnd from 'lodash/padEnd';\nimport * as React from 'react';\nvar StatisticNumber = function StatisticNumber(props) {\n  var value = props.value,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    _props$groupSeparator = props.groupSeparator,\n    groupSeparator = _props$groupSeparator === void 0 ? '' : _props$groupSeparator,\n    prefixCls = props.prefixCls;\n  var valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    var val = String(value);\n    var cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      var negative = cells[1];\n      var int = cells[2] || '0';\n      var decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = padEnd(decimal, precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = \"\".concat(decimalSeparator).concat(decimal);\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: \"\".concat(prefixCls, \"-content-value-int\")\n      }, negative, int), decimal && /*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: \"\".concat(prefixCls, \"-content-value-decimal\")\n      }, decimal)];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-value\")\n  }, valueNode);\n};\nexport default StatisticNumber;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,eAAe;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;EACpD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,gBAAgB,GAAGJ,KAAK,CAACI,gBAAgB;IACzCC,qBAAqB,GAAGL,KAAK,CAACM,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9EE,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC7B,IAAIC,SAAS;EACb,IAAI,OAAON,SAAS,KAAK,UAAU,EAAE;IACnC;IACAM,SAAS,GAAGN,SAAS,CAACD,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL;IACA,IAAIQ,GAAG,GAAGC,MAAM,CAACT,KAAK,CAAC;IACvB,IAAIU,KAAK,GAAGF,GAAG,CAACG,KAAK,CAAC,uBAAuB,CAAC;IAC9C;IACA,IAAI,CAACD,KAAK,IAAIF,GAAG,KAAK,GAAG,EAAE;MACzBD,SAAS,GAAGC,GAAG;IACjB,CAAC,MAAM;MACL,IAAII,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;MACvB,IAAIG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;MACzB,IAAII,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAC5BG,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,uBAAuB,EAAEV,cAAc,CAAC;MAC1D,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;QACjCY,OAAO,GAAGlB,MAAM,CAACkB,OAAO,EAAEZ,SAAS,EAAE,GAAG,CAAC,CAACc,KAAK,CAAC,CAAC,EAAEd,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC,CAAC;MACnF;MACA,IAAIY,OAAO,EAAE;QACXA,OAAO,GAAG,EAAE,CAACG,MAAM,CAACd,gBAAgB,CAAC,CAACc,MAAM,CAACH,OAAO,CAAC;MACvD;MACAP,SAAS,GAAG,CAAC,aAAaV,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QACpDC,GAAG,EAAE,KAAK;QACVC,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,oBAAoB;MACtD,CAAC,EAAEM,QAAQ,EAAEC,GAAG,CAAC,EAAEC,OAAO,IAAI,aAAajB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QACrEC,GAAG,EAAE,SAAS;QACdC,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,wBAAwB;MAC1D,CAAC,EAAEQ,OAAO,CAAC,CAAC;IACd;EACF;EACA,OAAO,aAAajB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;IAC9CE,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAEC,SAAS,CAAC;AACf,CAAC;AACD,eAAeT,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}