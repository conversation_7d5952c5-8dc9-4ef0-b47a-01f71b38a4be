{"ast": null, "code": "import _isEqual from \"lodash/isEqual\";\nimport _get from \"lodash/get\";\nimport _isPlainObject from \"lodash/isPlainObject\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Render sectors of a pie\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Layer } from '../container/Layer';\nimport { Sector } from '../shape/Sector';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian, getMaxRadius } from '../util/PolarUtils';\nimport { isNumber, getPercentValue, mathSign, interpolateNumber, uniqueId } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { warn } from '../util/LogUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Pie = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Pie, _PureComponent);\n  var _super = _createSuper(Pie);\n  function Pie(props) {\n    var _this;\n    _classCallCheck(this, Pie);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"pieRef\", null);\n    _defineProperty(_assertThisInitialized(_this), \"sectorRefs\", []);\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-pie-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    _this.state = {\n      isAnimationFinished: !props.isAnimationActive,\n      prevIsAnimationActive: props.isAnimationActive,\n      prevAnimationId: props.animationId,\n      sectorToFocus: 0\n    };\n    return _this;\n  }\n  _createClass(Pie, [{\n    key: \"isActiveIndex\",\n    value: function isActiveIndex(i) {\n      var activeIndex = this.props.activeIndex;\n      if (Array.isArray(activeIndex)) {\n        return activeIndex.indexOf(i) !== -1;\n      }\n      return i === activeIndex;\n    }\n  }, {\n    key: \"hasActiveIndex\",\n    value: function hasActiveIndex() {\n      var activeIndex = this.props.activeIndex;\n      return Array.isArray(activeIndex) ? activeIndex.length !== 0 : activeIndex || activeIndex === 0;\n    }\n  }, {\n    key: \"renderLabels\",\n    value: function renderLabels(sectors) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        label = _this$props.label,\n        labelLine = _this$props.labelLine,\n        dataKey = _this$props.dataKey,\n        valueKey = _this$props.valueKey;\n      var pieProps = filterProps(this.props);\n      var customLabelProps = filterProps(label);\n      var customLabelLineProps = filterProps(labelLine);\n      var offsetRadius = label && label.offsetRadius || 20;\n      var labels = sectors.map(function (entry, i) {\n        var midAngle = (entry.startAngle + entry.endAngle) / 2;\n        var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n        var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          stroke: 'none'\n        }, customLabelProps), {}, {\n          index: i,\n          textAnchor: Pie.getTextAnchor(endPoint.x, entry.cx)\n        }, endPoint);\n        var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          fill: 'none',\n          stroke: entry.fill\n        }, customLabelLineProps), {}, {\n          index: i,\n          points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint],\n          key: 'line'\n        });\n        var realDataKey = dataKey;\n        // TODO: compatible to lower versions\n        if (_isNil(dataKey) && _isNil(valueKey)) {\n          realDataKey = 'value';\n        } else if (_isNil(dataKey)) {\n          realDataKey = valueKey;\n        }\n        return (/*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, {\n            key: \"label-\".concat(i)\n          }, labelLine && Pie.renderLabelLineItem(labelLine, lineProps), Pie.renderLabelItem(label, labelProps, getValueByDataKey(entry, realDataKey)))\n        );\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-pie-labels\"\n      }, labels);\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        activeShape = _this$props2.activeShape,\n        blendStroke = _this$props2.blendStroke,\n        inactiveShapeProp = _this$props2.inactiveShape;\n      return sectors.map(function (entry, i) {\n        var inactiveShape = inactiveShapeProp && _this2.hasActiveIndex() ? inactiveShapeProp : null;\n        var sectorOptions = _this2.isActiveIndex(i) ? activeShape : inactiveShape;\n        var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n          stroke: blendStroke ? entry.fill : entry.stroke\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          ref: function ref(_ref) {\n            if (_ref && !_this2.sectorRefs.includes(_ref)) {\n              _this2.sectorRefs.push(_ref);\n            }\n          },\n          tabIndex: -1,\n          className: \"recharts-pie-sector\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"sector-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }), Pie.renderSectorItem(sectorOptions, sectorProps));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        sectors = _this$props3.sectors,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var _this$state = this.state,\n        prevSectors = _this$state.prevSectors,\n        prevIsAnimationActive = _this$state.prevIsAnimationActive;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId, \"-\").concat(prevIsAnimationActive),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var t = _ref2.t;\n        var stepData = [];\n        var first = sectors && sectors[0];\n        var curAngle = first.startAngle;\n        sectors.forEach(function (entry, index) {\n          var prev = prevSectors && prevSectors[index];\n          var paddingAngle = index > 0 ? _get(entry, 'paddingAngle', 0) : 0;\n          if (prev) {\n            var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n            var latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + angleIp(t) + paddingAngle\n            });\n            stepData.push(latest);\n            curAngle = latest.endAngle;\n          } else {\n            var endAngle = entry.endAngle,\n              startAngle = entry.startAngle;\n            var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n            var deltaAngle = interpolatorAngle(t);\n            var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + deltaAngle + paddingAngle\n            });\n            stepData.push(_latest);\n            curAngle = _latest.endAngle;\n          }\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"attachKeyboardHandlers\",\n    value: function attachKeyboardHandlers(pieRef) {\n      var _this4 = this;\n      // eslint-disable-next-line no-param-reassign\n      pieRef.onkeydown = function (e) {\n        if (!e.altKey) {\n          switch (e.key) {\n            case 'ArrowLeft':\n              {\n                var next = ++_this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[next].focus();\n                _this4.setState({\n                  sectorToFocus: next\n                });\n                break;\n              }\n            case 'ArrowRight':\n              {\n                var _next = --_this4.state.sectorToFocus < 0 ? _this4.sectorRefs.length - 1 : _this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[_next].focus();\n                _this4.setState({\n                  sectorToFocus: _next\n                });\n                break;\n              }\n            case 'Escape':\n              {\n                _this4.sectorRefs[_this4.state.sectorToFocus].blur();\n                _this4.setState({\n                  sectorToFocus: 0\n                });\n                break;\n              }\n            default:\n              {\n                // There is nothing to do here\n              }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        sectors = _this$props4.sectors,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevSectors = this.state.prevSectors;\n      if (isAnimationActive && sectors && sectors.length && (!prevSectors || !_isEqual(prevSectors, sectors))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(sectors);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.pieRef) {\n        this.attachKeyboardHandlers(this.pieRef);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        sectors = _this$props5.sectors,\n        className = _this$props5.className,\n        label = _this$props5.label,\n        cx = _this$props5.cx,\n        cy = _this$props5.cy,\n        innerRadius = _this$props5.innerRadius,\n        outerRadius = _this$props5.outerRadius,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (hide || !sectors || !sectors.length || !isNumber(cx) || !isNumber(cy) || !isNumber(innerRadius) || !isNumber(outerRadius)) {\n        return null;\n      }\n      var layerClass = classNames('recharts-pie', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: 0,\n        className: layerClass,\n        ref: function ref(_ref3) {\n          _this5.pieRef = _ref3;\n        }\n      }, this.renderSectors(), label && this.renderLabels(sectors), Label.renderCallByParent(this.props, null, false), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, sectors, false));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (prevState.prevIsAnimationActive !== nextProps.isAnimationActive) {\n        return {\n          prevIsAnimationActive: nextProps.isAnimationActive,\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: [],\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.isAnimationActive && nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: prevState.curSectors,\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.sectors !== prevState.curSectors) {\n        return {\n          curSectors: nextProps.sectors,\n          isAnimationFinished: true\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getTextAnchor\",\n    value: function getTextAnchor(x, cx) {\n      if (x > cx) {\n        return 'start';\n      }\n      if (x < cx) {\n        return 'end';\n      }\n      return 'middle';\n    }\n  }, {\n    key: \"renderLabelLineItem\",\n    value: function renderLabelLineItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n        type: \"linear\",\n        className: \"recharts-pie-label-line\"\n      }));\n    }\n  }, {\n    key: \"renderLabelItem\",\n    value: function renderLabelItem(option, props, value) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      var label = value;\n      if (_isFunction(option)) {\n        label = option(props);\n        if (/*#__PURE__*/React.isValidElement(label)) {\n          return label;\n        }\n      }\n      return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        alignmentBaseline: \"middle\",\n        className: \"recharts-pie-label-text\"\n      }), label);\n    }\n  }, {\n    key: \"renderSectorItem\",\n    value: function renderSectorItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      if (_isPlainObject(option)) {\n        return /*#__PURE__*/React.createElement(Sector, _extends({\n          tabIndex: -1\n        }, props, option));\n      }\n      return /*#__PURE__*/React.createElement(Sector, _extends({\n        tabIndex: -1\n      }, props));\n    }\n  }]);\n  return Pie;\n}(PureComponent);\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  cx: '50%',\n  cy: '50%',\n  startAngle: 0,\n  endAngle: 360,\n  innerRadius: 0,\n  outerRadius: '80%',\n  paddingAngle: 0,\n  labelLine: true,\n  hide: false,\n  minAngle: 0,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  blendStroke: false\n});\n_defineProperty(Pie, \"parseDeltaAngle\", function (startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n});\n_defineProperty(Pie, \"getRealPieData\", function (item) {\n  var _item$props = item.props,\n    data = _item$props.data,\n    children = _item$props.children;\n  var presentationProps = filterProps(item.props);\n  var cells = findAllByType(children, Cell);\n  if (data && data.length) {\n    return data.map(function (entry, index) {\n      return _objectSpread(_objectSpread(_objectSpread({\n        payload: entry\n      }, presentationProps), entry), cells && cells[index] && cells[index].props);\n    });\n  }\n  if (cells && cells.length) {\n    return cells.map(function (cell) {\n      return _objectSpread(_objectSpread({}, presentationProps), cell.props);\n    });\n  }\n  return [];\n});\n_defineProperty(Pie, \"parseCoordinateOfPie\", function (item, offset) {\n  var top = offset.top,\n    left = offset.left,\n    width = offset.width,\n    height = offset.height;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(item.props.cx, width, width / 2);\n  var cy = top + getPercentValue(item.props.cy, height, height / 2);\n  var innerRadius = getPercentValue(item.props.innerRadius, maxPieRadius, 0);\n  var outerRadius = getPercentValue(item.props.outerRadius, maxPieRadius, maxPieRadius * 0.8);\n  var maxRadius = item.props.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx: cx,\n    cy: cy,\n    innerRadius: innerRadius,\n    outerRadius: outerRadius,\n    maxRadius: maxRadius\n  };\n});\n_defineProperty(Pie, \"getComposedData\", function (_ref4) {\n  var item = _ref4.item,\n    offset = _ref4.offset;\n  var pieData = Pie.getRealPieData(item);\n  if (!pieData || !pieData.length) {\n    return null;\n  }\n  var _item$props2 = item.props,\n    cornerRadius = _item$props2.cornerRadius,\n    startAngle = _item$props2.startAngle,\n    endAngle = _item$props2.endAngle,\n    paddingAngle = _item$props2.paddingAngle,\n    dataKey = _item$props2.dataKey,\n    nameKey = _item$props2.nameKey,\n    valueKey = _item$props2.valueKey,\n    tooltipType = _item$props2.tooltipType;\n  var minAngle = Math.abs(item.props.minAngle);\n  var coordinate = Pie.parseCoordinateOfPie(item, offset);\n  var deltaAngle = Pie.parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var realDataKey = dataKey;\n  if (_isNil(dataKey) && _isNil(valueKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = 'value';\n  } else if (_isNil(dataKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = valueKey;\n  }\n  var notZeroItemCount = pieData.filter(function (entry) {\n    return getValueByDataKey(entry, realDataKey, 0) !== 0;\n  }).length;\n  var totalPadingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPadingAngle;\n  var sum = pieData.reduce(function (result, entry) {\n    var val = getValueByDataKey(entry, realDataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = pieData.map(function (entry, i) {\n      var val = getValueByDataKey(entry, realDataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        name: name,\n        value: val,\n        payload: entry,\n        dataKey: realDataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread({\n        percent: percent,\n        cornerRadius: cornerRadius,\n        name: name,\n        tooltipPayload: tooltipPayload,\n        midAngle: midAngle,\n        middleRadius: middleRadius,\n        tooltipPosition: tooltipPosition\n      }, entry), coordinate), {}, {\n        value: getValueByDataKey(entry, realDataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entry,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return _objectSpread(_objectSpread({}, coordinate), {}, {\n    sectors: sectors,\n    data: pieData\n  });\n});", "map": {"version": 3, "names": ["_isEqual", "_get", "_isPlainObject", "_isFunction", "_isNil", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "Animate", "classNames", "Layer", "Sector", "Curve", "Text", "Label", "LabelList", "Cell", "findAllByType", "filterProps", "Global", "polarToCartesian", "getMaxRadius", "isNumber", "getPercentValue", "mathSign", "interpolateNumber", "uniqueId", "getValueByDataKey", "warn", "adaptEventsOfChild", "Pie", "_PureComponent", "_super", "_this", "onAnimationEnd", "setState", "isAnimationFinished", "onAnimationStart", "state", "isAnimationActive", "prevIsAnimationActive", "prevAnimationId", "animationId", "sectorToFocus", "isActiveIndex", "activeIndex", "Array", "isArray", "indexOf", "hasActiveIndex", "renderLabels", "sectors", "_this$props", "label", "labelLine", "dataKey", "valueKey", "pieProps", "customLabelProps", "customLabelLineProps", "offsetRadius", "labels", "map", "entry", "midAngle", "startAngle", "endAngle", "endPoint", "cx", "cy", "outerRadius", "labelProps", "stroke", "index", "textAnchor", "getTextAnchor", "x", "lineProps", "fill", "points", "realDataKey", "createElement", "concat", "renderLabelLineItem", "renderLabelItem", "className", "renderSectorsStatically", "_this2", "_this$props2", "activeShape", "blendStroke", "inactiveShapeProp", "inactiveShape", "sectorOptions", "sectorProps", "ref", "_ref", "sectorRefs", "includes", "tabIndex", "renderSectorItem", "renderSectorsWithAnimation", "_this3", "_this$props3", "animationBegin", "animationDuration", "animationEasing", "_this$state", "prevSectors", "begin", "duration", "isActive", "easing", "from", "t", "to", "handleAnimationStart", "handleAnimationEnd", "_ref2", "stepData", "first", "curAngle", "prev", "paddingAngle", "angleIp", "latest", "interpolatorAngle", "deltaAngle", "_latest", "attachKeyboardHandlers", "pieRef", "_this4", "onkeydown", "altKey", "next", "focus", "_next", "blur", "renderSectors", "_this$props4", "componentDidMount", "render", "_this5", "_this$props5", "hide", "innerRadius", "layerClass", "_ref3", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "curSectors", "option", "isValidElement", "cloneElement", "type", "alignmentBaseline", "legendType", "minAngle", "isSsr", "<PERSON><PERSON><PERSON>", "sign", "Math", "min", "abs", "item", "_item$props", "data", "children", "presentationProps", "cells", "payload", "cell", "offset", "top", "left", "width", "height", "maxPieRadius", "maxRadius", "sqrt", "_ref4", "pieData", "getRealPieData", "_item$props2", "cornerRadius", "tooltipType", "coordinate", "parseCoordinateOfPie", "parseDeltaAngle", "absDeltaAngle", "notZeroItemCount", "totalPadingAngle", "realTotalAngle", "sum", "reduce", "val", "name", "percent", "tempStartAngle", "tempEndAngle", "middleRadius", "tooltipPayload", "tooltipPosition"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/polar/Pie.js"], "sourcesContent": ["import _isEqual from \"lodash/isEqual\";\nimport _get from \"lodash/get\";\nimport _isPlainObject from \"lodash/isPlainObject\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Render sectors of a pie\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Layer } from '../container/Layer';\nimport { Sector } from '../shape/Sector';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian, getMaxRadius } from '../util/PolarUtils';\nimport { isNumber, getPercentValue, mathSign, interpolateNumber, uniqueId } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { warn } from '../util/LogUtils';\nimport { adaptEventsOfChild } from '../util/types';\nexport var Pie = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Pie, _PureComponent);\n  var _super = _createSuper(Pie);\n  function Pie(props) {\n    var _this;\n    _classCallCheck(this, Pie);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"pieRef\", null);\n    _defineProperty(_assertThisInitialized(_this), \"sectorRefs\", []);\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-pie-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    _this.state = {\n      isAnimationFinished: !props.isAnimationActive,\n      prevIsAnimationActive: props.isAnimationActive,\n      prevAnimationId: props.animationId,\n      sectorToFocus: 0\n    };\n    return _this;\n  }\n  _createClass(Pie, [{\n    key: \"isActiveIndex\",\n    value: function isActiveIndex(i) {\n      var activeIndex = this.props.activeIndex;\n      if (Array.isArray(activeIndex)) {\n        return activeIndex.indexOf(i) !== -1;\n      }\n      return i === activeIndex;\n    }\n  }, {\n    key: \"hasActiveIndex\",\n    value: function hasActiveIndex() {\n      var activeIndex = this.props.activeIndex;\n      return Array.isArray(activeIndex) ? activeIndex.length !== 0 : activeIndex || activeIndex === 0;\n    }\n  }, {\n    key: \"renderLabels\",\n    value: function renderLabels(sectors) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        label = _this$props.label,\n        labelLine = _this$props.labelLine,\n        dataKey = _this$props.dataKey,\n        valueKey = _this$props.valueKey;\n      var pieProps = filterProps(this.props);\n      var customLabelProps = filterProps(label);\n      var customLabelLineProps = filterProps(labelLine);\n      var offsetRadius = label && label.offsetRadius || 20;\n      var labels = sectors.map(function (entry, i) {\n        var midAngle = (entry.startAngle + entry.endAngle) / 2;\n        var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n        var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          stroke: 'none'\n        }, customLabelProps), {}, {\n          index: i,\n          textAnchor: Pie.getTextAnchor(endPoint.x, entry.cx)\n        }, endPoint);\n        var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          fill: 'none',\n          stroke: entry.fill\n        }, customLabelLineProps), {}, {\n          index: i,\n          points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint],\n          key: 'line'\n        });\n        var realDataKey = dataKey;\n        // TODO: compatible to lower versions\n        if (_isNil(dataKey) && _isNil(valueKey)) {\n          realDataKey = 'value';\n        } else if (_isNil(dataKey)) {\n          realDataKey = valueKey;\n        }\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, {\n            key: \"label-\".concat(i)\n          }, labelLine && Pie.renderLabelLineItem(labelLine, lineProps), Pie.renderLabelItem(label, labelProps, getValueByDataKey(entry, realDataKey)))\n        );\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-pie-labels\"\n      }, labels);\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        activeShape = _this$props2.activeShape,\n        blendStroke = _this$props2.blendStroke,\n        inactiveShapeProp = _this$props2.inactiveShape;\n      return sectors.map(function (entry, i) {\n        var inactiveShape = inactiveShapeProp && _this2.hasActiveIndex() ? inactiveShapeProp : null;\n        var sectorOptions = _this2.isActiveIndex(i) ? activeShape : inactiveShape;\n        var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n          stroke: blendStroke ? entry.fill : entry.stroke\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          ref: function ref(_ref) {\n            if (_ref && !_this2.sectorRefs.includes(_ref)) {\n              _this2.sectorRefs.push(_ref);\n            }\n          },\n          tabIndex: -1,\n          className: \"recharts-pie-sector\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          key: \"sector-\".concat(i) // eslint-disable-line react/no-array-index-key\n        }), Pie.renderSectorItem(sectorOptions, sectorProps));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        sectors = _this$props3.sectors,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var _this$state = this.state,\n        prevSectors = _this$state.prevSectors,\n        prevIsAnimationActive = _this$state.prevIsAnimationActive;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId, \"-\").concat(prevIsAnimationActive),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var t = _ref2.t;\n        var stepData = [];\n        var first = sectors && sectors[0];\n        var curAngle = first.startAngle;\n        sectors.forEach(function (entry, index) {\n          var prev = prevSectors && prevSectors[index];\n          var paddingAngle = index > 0 ? _get(entry, 'paddingAngle', 0) : 0;\n          if (prev) {\n            var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n            var latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + angleIp(t) + paddingAngle\n            });\n            stepData.push(latest);\n            curAngle = latest.endAngle;\n          } else {\n            var endAngle = entry.endAngle,\n              startAngle = entry.startAngle;\n            var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n            var deltaAngle = interpolatorAngle(t);\n            var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + deltaAngle + paddingAngle\n            });\n            stepData.push(_latest);\n            curAngle = _latest.endAngle;\n          }\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"attachKeyboardHandlers\",\n    value: function attachKeyboardHandlers(pieRef) {\n      var _this4 = this;\n      // eslint-disable-next-line no-param-reassign\n      pieRef.onkeydown = function (e) {\n        if (!e.altKey) {\n          switch (e.key) {\n            case 'ArrowLeft':\n              {\n                var next = ++_this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[next].focus();\n                _this4.setState({\n                  sectorToFocus: next\n                });\n                break;\n              }\n            case 'ArrowRight':\n              {\n                var _next = --_this4.state.sectorToFocus < 0 ? _this4.sectorRefs.length - 1 : _this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[_next].focus();\n                _this4.setState({\n                  sectorToFocus: _next\n                });\n                break;\n              }\n            case 'Escape':\n              {\n                _this4.sectorRefs[_this4.state.sectorToFocus].blur();\n                _this4.setState({\n                  sectorToFocus: 0\n                });\n                break;\n              }\n            default:\n              {\n                // There is nothing to do here\n              }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        sectors = _this$props4.sectors,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevSectors = this.state.prevSectors;\n      if (isAnimationActive && sectors && sectors.length && (!prevSectors || !_isEqual(prevSectors, sectors))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(sectors);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.pieRef) {\n        this.attachKeyboardHandlers(this.pieRef);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        sectors = _this$props5.sectors,\n        className = _this$props5.className,\n        label = _this$props5.label,\n        cx = _this$props5.cx,\n        cy = _this$props5.cy,\n        innerRadius = _this$props5.innerRadius,\n        outerRadius = _this$props5.outerRadius,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (hide || !sectors || !sectors.length || !isNumber(cx) || !isNumber(cy) || !isNumber(innerRadius) || !isNumber(outerRadius)) {\n        return null;\n      }\n      var layerClass = classNames('recharts-pie', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: 0,\n        className: layerClass,\n        ref: function ref(_ref3) {\n          _this5.pieRef = _ref3;\n        }\n      }, this.renderSectors(), label && this.renderLabels(sectors), Label.renderCallByParent(this.props, null, false), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, sectors, false));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (prevState.prevIsAnimationActive !== nextProps.isAnimationActive) {\n        return {\n          prevIsAnimationActive: nextProps.isAnimationActive,\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: [],\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.isAnimationActive && nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: prevState.curSectors,\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.sectors !== prevState.curSectors) {\n        return {\n          curSectors: nextProps.sectors,\n          isAnimationFinished: true\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getTextAnchor\",\n    value: function getTextAnchor(x, cx) {\n      if (x > cx) {\n        return 'start';\n      }\n      if (x < cx) {\n        return 'end';\n      }\n      return 'middle';\n    }\n  }, {\n    key: \"renderLabelLineItem\",\n    value: function renderLabelLineItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n        type: \"linear\",\n        className: \"recharts-pie-label-line\"\n      }));\n    }\n  }, {\n    key: \"renderLabelItem\",\n    value: function renderLabelItem(option, props, value) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      var label = value;\n      if (_isFunction(option)) {\n        label = option(props);\n        if ( /*#__PURE__*/React.isValidElement(label)) {\n          return label;\n        }\n      }\n      return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        alignmentBaseline: \"middle\",\n        className: \"recharts-pie-label-text\"\n      }), label);\n    }\n  }, {\n    key: \"renderSectorItem\",\n    value: function renderSectorItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      if (_isPlainObject(option)) {\n        return /*#__PURE__*/React.createElement(Sector, _extends({\n          tabIndex: -1\n        }, props, option));\n      }\n      return /*#__PURE__*/React.createElement(Sector, _extends({\n        tabIndex: -1\n      }, props));\n    }\n  }]);\n  return Pie;\n}(PureComponent);\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  cx: '50%',\n  cy: '50%',\n  startAngle: 0,\n  endAngle: 360,\n  innerRadius: 0,\n  outerRadius: '80%',\n  paddingAngle: 0,\n  labelLine: true,\n  hide: false,\n  minAngle: 0,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  blendStroke: false\n});\n_defineProperty(Pie, \"parseDeltaAngle\", function (startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n});\n_defineProperty(Pie, \"getRealPieData\", function (item) {\n  var _item$props = item.props,\n    data = _item$props.data,\n    children = _item$props.children;\n  var presentationProps = filterProps(item.props);\n  var cells = findAllByType(children, Cell);\n  if (data && data.length) {\n    return data.map(function (entry, index) {\n      return _objectSpread(_objectSpread(_objectSpread({\n        payload: entry\n      }, presentationProps), entry), cells && cells[index] && cells[index].props);\n    });\n  }\n  if (cells && cells.length) {\n    return cells.map(function (cell) {\n      return _objectSpread(_objectSpread({}, presentationProps), cell.props);\n    });\n  }\n  return [];\n});\n_defineProperty(Pie, \"parseCoordinateOfPie\", function (item, offset) {\n  var top = offset.top,\n    left = offset.left,\n    width = offset.width,\n    height = offset.height;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(item.props.cx, width, width / 2);\n  var cy = top + getPercentValue(item.props.cy, height, height / 2);\n  var innerRadius = getPercentValue(item.props.innerRadius, maxPieRadius, 0);\n  var outerRadius = getPercentValue(item.props.outerRadius, maxPieRadius, maxPieRadius * 0.8);\n  var maxRadius = item.props.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx: cx,\n    cy: cy,\n    innerRadius: innerRadius,\n    outerRadius: outerRadius,\n    maxRadius: maxRadius\n  };\n});\n_defineProperty(Pie, \"getComposedData\", function (_ref4) {\n  var item = _ref4.item,\n    offset = _ref4.offset;\n  var pieData = Pie.getRealPieData(item);\n  if (!pieData || !pieData.length) {\n    return null;\n  }\n  var _item$props2 = item.props,\n    cornerRadius = _item$props2.cornerRadius,\n    startAngle = _item$props2.startAngle,\n    endAngle = _item$props2.endAngle,\n    paddingAngle = _item$props2.paddingAngle,\n    dataKey = _item$props2.dataKey,\n    nameKey = _item$props2.nameKey,\n    valueKey = _item$props2.valueKey,\n    tooltipType = _item$props2.tooltipType;\n  var minAngle = Math.abs(item.props.minAngle);\n  var coordinate = Pie.parseCoordinateOfPie(item, offset);\n  var deltaAngle = Pie.parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var realDataKey = dataKey;\n  if (_isNil(dataKey) && _isNil(valueKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = 'value';\n  } else if (_isNil(dataKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = valueKey;\n  }\n  var notZeroItemCount = pieData.filter(function (entry) {\n    return getValueByDataKey(entry, realDataKey, 0) !== 0;\n  }).length;\n  var totalPadingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPadingAngle;\n  var sum = pieData.reduce(function (result, entry) {\n    var val = getValueByDataKey(entry, realDataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = pieData.map(function (entry, i) {\n      var val = getValueByDataKey(entry, realDataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        name: name,\n        value: val,\n        payload: entry,\n        dataKey: realDataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread({\n        percent: percent,\n        cornerRadius: cornerRadius,\n        name: name,\n        tooltipPayload: tooltipPayload,\n        midAngle: midAngle,\n        middleRadius: middleRadius,\n        tooltipPosition: tooltipPosition\n      }, entry), coordinate), {}, {\n        value: getValueByDataKey(entry, realDataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entry,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return _objectSpread(_objectSpread({}, coordinate), {}, {\n    sectors: sectors,\n    data: pieData\n  });\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASQ,aAAaA,CAACpB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGQ,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAEiB,eAAe,CAACtB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACxB,MAAM,EAAEH,MAAM,CAAC0B,yBAAyB,CAACnB,MAAM,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,MAAM,CAAC,CAAC,CAACiB,OAAO,CAAC,UAAUhB,GAAG,EAAE;MAAER,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACoB,wBAAwB,CAACb,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC9B,MAAM,EAAE+B,KAAK,EAAE;EAAE,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,KAAK,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI+B,UAAU,GAAGD,KAAK,CAAC9B,CAAC,CAAC;IAAE+B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAErC,MAAM,CAAC4B,cAAc,CAACzB,MAAM,EAAEmC,cAAc,CAACH,UAAU,CAAC3B,GAAG,CAAC,EAAE2B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACjC,SAAS,EAAE0C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEzC,MAAM,CAAC4B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAAC7C,SAAS,GAAGE,MAAM,CAAC6C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC9C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEiD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEpC,MAAM,CAAC4B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAG/C,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAAChD,IAAI,CAAC,CAAC,GAAG,SAAS6C,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC7D,WAAW;MAAE8D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEpD,SAAS,EAAEuD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC9C,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAO0D,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEtD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIsB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACvE,SAAS,CAACwE,OAAO,CAAC5D,IAAI,CAACmD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG1D,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACwE,cAAc,CAACtE,IAAI,CAAC,CAAC,GAAG,SAASwD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAInD,MAAM,CAACwE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC/B,GAAG,EAAEc,GAAG,EAAEsC,KAAK,EAAE;EAAEtC,GAAG,GAAG8B,cAAc,CAAC9B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC4B,cAAc,CAAClC,GAAG,EAAEc,GAAG,EAAE;MAAEsC,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE3C,GAAG,CAACc,GAAG,CAAC,GAAGsC,KAAK;EAAE;EAAE,OAAOpD,GAAG;AAAE;AAC3O,SAAS4C,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAIjE,GAAG,GAAGkE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGmE,MAAM,CAACnE,GAAG,CAAC;AAAE;AAC5H,SAASkE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpF,OAAO,CAACmF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjF,MAAM,CAACoF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACpE,IAAI,CAACkE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpF,OAAO,CAACwF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,QAAQ,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,mBAAmB;AACpG,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAO,IAAIC,GAAG,GAAG,aAAa,UAAUC,cAAc,EAAE;EACtDlE,SAAS,CAACiE,GAAG,EAAEC,cAAc,CAAC;EAC9B,IAAIC,MAAM,GAAGzD,YAAY,CAACuD,GAAG,CAAC;EAC9B,SAASA,GAAGA,CAACzE,KAAK,EAAE;IAClB,IAAI4E,KAAK;IACTjF,eAAe,CAAC,IAAI,EAAE8E,GAAG,CAAC;IAC1BG,KAAK,GAAGD,MAAM,CAACnG,IAAI,CAAC,IAAI,EAAEwB,KAAK,CAAC;IAChCT,eAAe,CAACwC,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;IAC9DrF,eAAe,CAACwC,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,YAAY,EAAE,EAAE,CAAC;IAChErF,eAAe,CAACwC,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,IAAI,EAAEP,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC/E9E,eAAe,CAACwC,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIC,cAAc,GAAGD,KAAK,CAAC5E,KAAK,CAAC6E,cAAc;MAC/CD,KAAK,CAACE,QAAQ,CAAC;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI1H,WAAW,CAACwH,cAAc,CAAC,EAAE;QAC/BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFtF,eAAe,CAACwC,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjF,IAAII,gBAAgB,GAAGJ,KAAK,CAAC5E,KAAK,CAACgF,gBAAgB;MACnDJ,KAAK,CAACE,QAAQ,CAAC;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI1H,WAAW,CAAC2H,gBAAgB,CAAC,EAAE;QACjCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACFJ,KAAK,CAACK,KAAK,GAAG;MACZF,mBAAmB,EAAE,CAAC/E,KAAK,CAACkF,iBAAiB;MAC7CC,qBAAqB,EAAEnF,KAAK,CAACkF,iBAAiB;MAC9CE,eAAe,EAAEpF,KAAK,CAACqF,WAAW;MAClCC,aAAa,EAAE;IACjB,CAAC;IACD,OAAOV,KAAK;EACd;EACAvE,YAAY,CAACoE,GAAG,EAAE,CAAC;IACjBnG,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,SAAS2E,aAAaA,CAACrH,CAAC,EAAE;MAC/B,IAAIsH,WAAW,GAAG,IAAI,CAACxF,KAAK,CAACwF,WAAW;MACxC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;QAC9B,OAAOA,WAAW,CAACG,OAAO,CAACzH,CAAC,CAAC,KAAK,CAAC,CAAC;MACtC;MACA,OAAOA,CAAC,KAAKsH,WAAW;IAC1B;EACF,CAAC,EAAE;IACDlH,GAAG,EAAE,gBAAgB;IACrBsC,KAAK,EAAE,SAASgF,cAAcA,CAAA,EAAG;MAC/B,IAAIJ,WAAW,GAAG,IAAI,CAACxF,KAAK,CAACwF,WAAW;MACxC,OAAOC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,CAACpH,MAAM,KAAK,CAAC,GAAGoH,WAAW,IAAIA,WAAW,KAAK,CAAC;IACjG;EACF,CAAC,EAAE;IACDlH,GAAG,EAAE,cAAc;IACnBsC,KAAK,EAAE,SAASiF,YAAYA,CAACC,OAAO,EAAE;MACpC,IAAIZ,iBAAiB,GAAG,IAAI,CAAClF,KAAK,CAACkF,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACD,KAAK,CAACF,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAIgB,WAAW,GAAG,IAAI,CAAC/F,KAAK;QAC1BgG,KAAK,GAAGD,WAAW,CAACC,KAAK;QACzBC,SAAS,GAAGF,WAAW,CAACE,SAAS;QACjCC,OAAO,GAAGH,WAAW,CAACG,OAAO;QAC7BC,QAAQ,GAAGJ,WAAW,CAACI,QAAQ;MACjC,IAAIC,QAAQ,GAAGvC,WAAW,CAAC,IAAI,CAAC7D,KAAK,CAAC;MACtC,IAAIqG,gBAAgB,GAAGxC,WAAW,CAACmC,KAAK,CAAC;MACzC,IAAIM,oBAAoB,GAAGzC,WAAW,CAACoC,SAAS,CAAC;MACjD,IAAIM,YAAY,GAAGP,KAAK,IAAIA,KAAK,CAACO,YAAY,IAAI,EAAE;MACpD,IAAIC,MAAM,GAAGV,OAAO,CAACW,GAAG,CAAC,UAAUC,KAAK,EAAExI,CAAC,EAAE;QAC3C,IAAIyI,QAAQ,GAAG,CAACD,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACG,QAAQ,IAAI,CAAC;QACtD,IAAIC,QAAQ,GAAG/C,gBAAgB,CAAC2C,KAAK,CAACK,EAAE,EAAEL,KAAK,CAACM,EAAE,EAAEN,KAAK,CAACO,WAAW,GAAGV,YAAY,EAAEI,QAAQ,CAAC;QAC/F,IAAIO,UAAU,GAAG7H,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,QAAQ,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClGS,MAAM,EAAE;QACV,CAAC,EAAEd,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UACxBe,KAAK,EAAElJ,CAAC;UACRmJ,UAAU,EAAE5C,GAAG,CAAC6C,aAAa,CAACR,QAAQ,CAACS,CAAC,EAAEb,KAAK,CAACK,EAAE;QACpD,CAAC,EAAED,QAAQ,CAAC;QACZ,IAAIU,SAAS,GAAGnI,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,QAAQ,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjGe,IAAI,EAAE,MAAM;UACZN,MAAM,EAAET,KAAK,CAACe;QAChB,CAAC,EAAEnB,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5Bc,KAAK,EAAElJ,CAAC;UACRwJ,MAAM,EAAE,CAAC3D,gBAAgB,CAAC2C,KAAK,CAACK,EAAE,EAAEL,KAAK,CAACM,EAAE,EAAEN,KAAK,CAACO,WAAW,EAAEN,QAAQ,CAAC,EAAEG,QAAQ,CAAC;UACrFxI,GAAG,EAAE;QACP,CAAC,CAAC;QACF,IAAIqJ,WAAW,GAAGzB,OAAO;QACzB;QACA,IAAI5I,MAAM,CAAC4I,OAAO,CAAC,IAAI5I,MAAM,CAAC6I,QAAQ,CAAC,EAAE;UACvCwB,WAAW,GAAG,OAAO;QACvB,CAAC,MAAM,IAAIrK,MAAM,CAAC4I,OAAO,CAAC,EAAE;UAC1ByB,WAAW,GAAGxB,QAAQ;QACxB;QACA,QACE;UACA;UACAlD,KAAK,CAAC2E,aAAa,CAACvE,KAAK,EAAE;YACzB/E,GAAG,EAAE,QAAQ,CAACuJ,MAAM,CAAC3J,CAAC;UACxB,CAAC,EAAE+H,SAAS,IAAIxB,GAAG,CAACqD,mBAAmB,CAAC7B,SAAS,EAAEuB,SAAS,CAAC,EAAE/C,GAAG,CAACsD,eAAe,CAAC/B,KAAK,EAAEkB,UAAU,EAAE5C,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,CAAC,CAAC;QAAC;MAEjJ,CAAC,CAAC;MACF,OAAO,aAAa1E,KAAK,CAAC2E,aAAa,CAACvE,KAAK,EAAE;QAC7C2E,SAAS,EAAE;MACb,CAAC,EAAExB,MAAM,CAAC;IACZ;EACF,CAAC,EAAE;IACDlI,GAAG,EAAE,yBAAyB;IAC9BsC,KAAK,EAAE,SAASqH,uBAAuBA,CAACnC,OAAO,EAAE;MAC/C,IAAIoC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACnI,KAAK;QAC3BoI,WAAW,GAAGD,YAAY,CAACC,WAAW;QACtCC,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtCC,iBAAiB,GAAGH,YAAY,CAACI,aAAa;MAChD,OAAOzC,OAAO,CAACW,GAAG,CAAC,UAAUC,KAAK,EAAExI,CAAC,EAAE;QACrC,IAAIqK,aAAa,GAAGD,iBAAiB,IAAIJ,MAAM,CAACtC,cAAc,CAAC,CAAC,GAAG0C,iBAAiB,GAAG,IAAI;QAC3F,IAAIE,aAAa,GAAGN,MAAM,CAAC3C,aAAa,CAACrH,CAAC,CAAC,GAAGkK,WAAW,GAAGG,aAAa;QACzE,IAAIE,WAAW,GAAGpJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DS,MAAM,EAAEkB,WAAW,GAAG3B,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACS;QAC3C,CAAC,CAAC;QACF,OAAO,aAAalE,KAAK,CAAC2E,aAAa,CAACvE,KAAK,EAAExF,QAAQ,CAAC;UACtD6K,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;YACtB,IAAIA,IAAI,IAAI,CAACT,MAAM,CAACU,UAAU,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;cAC7CT,MAAM,CAACU,UAAU,CAACxJ,IAAI,CAACuJ,IAAI,CAAC;YAC9B;UACF,CAAC;UACDG,QAAQ,EAAE,CAAC,CAAC;UACZd,SAAS,EAAE;QACb,CAAC,EAAExD,kBAAkB,CAAC0D,MAAM,CAAClI,KAAK,EAAE0G,KAAK,EAAExI,CAAC,CAAC,EAAE;UAC7CI,GAAG,EAAE,SAAS,CAACuJ,MAAM,CAAC3J,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,EAAEuG,GAAG,CAACsE,gBAAgB,CAACP,aAAa,EAAEC,WAAW,CAAC,CAAC;MACvD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnK,GAAG,EAAE,4BAA4B;IACjCsC,KAAK,EAAE,SAASoI,0BAA0BA,CAAA,EAAG;MAC3C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAClJ,KAAK;QAC3B8F,OAAO,GAAGoD,YAAY,CAACpD,OAAO;QAC9BZ,iBAAiB,GAAGgE,YAAY,CAAChE,iBAAiB;QAClDiE,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,eAAe,GAAGH,YAAY,CAACG,eAAe;QAC9ChE,WAAW,GAAG6D,YAAY,CAAC7D,WAAW;MACxC,IAAIiE,WAAW,GAAG,IAAI,CAACrE,KAAK;QAC1BsE,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCpE,qBAAqB,GAAGmE,WAAW,CAACnE,qBAAqB;MAC3D,OAAO,aAAalC,KAAK,CAAC2E,aAAa,CAACzE,OAAO,EAAE;QAC/CqG,KAAK,EAAEL,cAAc;QACrBM,QAAQ,EAAEL,iBAAiB;QAC3BM,QAAQ,EAAExE,iBAAiB;QAC3ByE,MAAM,EAAEN,eAAe;QACvBO,IAAI,EAAE;UACJC,CAAC,EAAE;QACL,CAAC;QACDC,EAAE,EAAE;UACFD,CAAC,EAAE;QACL,CAAC;QACDvL,GAAG,EAAE,MAAM,CAACuJ,MAAM,CAACxC,WAAW,EAAE,GAAG,CAAC,CAACwC,MAAM,CAAC1C,qBAAqB,CAAC;QAClEH,gBAAgB,EAAE,IAAI,CAAC+E,oBAAoB;QAC3ClF,cAAc,EAAE,IAAI,CAACmF;MACvB,CAAC,EAAE,UAAUC,KAAK,EAAE;QAClB,IAAIJ,CAAC,GAAGI,KAAK,CAACJ,CAAC;QACf,IAAIK,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAGrE,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;QACjC,IAAIsE,QAAQ,GAAGD,KAAK,CAACvD,UAAU;QAC/Bd,OAAO,CAACxG,OAAO,CAAC,UAAUoH,KAAK,EAAEU,KAAK,EAAE;UACtC,IAAIiD,IAAI,GAAGd,WAAW,IAAIA,WAAW,CAACnC,KAAK,CAAC;UAC5C,IAAIkD,YAAY,GAAGlD,KAAK,GAAG,CAAC,GAAGjK,IAAI,CAACuJ,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC;UACjE,IAAI2D,IAAI,EAAE;YACR,IAAIE,OAAO,GAAGnG,iBAAiB,CAACiG,IAAI,CAACxD,QAAQ,GAAGwD,IAAI,CAACzD,UAAU,EAAEF,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACE,UAAU,CAAC;YACnG,IAAI4D,MAAM,GAAGnL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACvDE,UAAU,EAAEwD,QAAQ,GAAGE,YAAY;cACnCzD,QAAQ,EAAEuD,QAAQ,GAAGG,OAAO,CAACV,CAAC,CAAC,GAAGS;YACpC,CAAC,CAAC;YACFJ,QAAQ,CAAC9K,IAAI,CAACoL,MAAM,CAAC;YACrBJ,QAAQ,GAAGI,MAAM,CAAC3D,QAAQ;UAC5B,CAAC,MAAM;YACL,IAAIA,QAAQ,GAAGH,KAAK,CAACG,QAAQ;cAC3BD,UAAU,GAAGF,KAAK,CAACE,UAAU;YAC/B,IAAI6D,iBAAiB,GAAGrG,iBAAiB,CAAC,CAAC,EAAEyC,QAAQ,GAAGD,UAAU,CAAC;YACnE,IAAI8D,UAAU,GAAGD,iBAAiB,CAACZ,CAAC,CAAC;YACrC,IAAIc,OAAO,GAAGtL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACxDE,UAAU,EAAEwD,QAAQ,GAAGE,YAAY;cACnCzD,QAAQ,EAAEuD,QAAQ,GAAGM,UAAU,GAAGJ;YACpC,CAAC,CAAC;YACFJ,QAAQ,CAAC9K,IAAI,CAACuL,OAAO,CAAC;YACtBP,QAAQ,GAAGO,OAAO,CAAC9D,QAAQ;UAC7B;QACF,CAAC,CAAC;QACF,OAAO,aAAa5D,KAAK,CAAC2E,aAAa,CAACvE,KAAK,EAAE,IAAI,EAAE4F,MAAM,CAAChB,uBAAuB,CAACiC,QAAQ,CAAC,CAAC;MAChG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD5L,GAAG,EAAE,wBAAwB;IAC7BsC,KAAK,EAAE,SAASgK,sBAAsBA,CAACC,MAAM,EAAE;MAC7C,IAAIC,MAAM,GAAG,IAAI;MACjB;MACAD,MAAM,CAACE,SAAS,GAAG,UAAU1I,CAAC,EAAE;QAC9B,IAAI,CAACA,CAAC,CAAC2I,MAAM,EAAE;UACb,QAAQ3I,CAAC,CAAC/D,GAAG;YACX,KAAK,WAAW;cACd;gBACE,IAAI2M,IAAI,GAAG,EAAEH,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAGwF,MAAM,CAAClC,UAAU,CAACxK,MAAM;gBAClE0M,MAAM,CAAClC,UAAU,CAACqC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;gBAC/BJ,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE2F;gBACjB,CAAC,CAAC;gBACF;cACF;YACF,KAAK,YAAY;cACf;gBACE,IAAIE,KAAK,GAAG,EAAEL,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAG,CAAC,GAAGwF,MAAM,CAAClC,UAAU,CAACxK,MAAM,GAAG,CAAC,GAAG0M,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAGwF,MAAM,CAAClC,UAAU,CAACxK,MAAM;gBACnI0M,MAAM,CAAClC,UAAU,CAACuC,KAAK,CAAC,CAACD,KAAK,CAAC,CAAC;gBAChCJ,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE6F;gBACjB,CAAC,CAAC;gBACF;cACF;YACF,KAAK,QAAQ;cACX;gBACEL,MAAM,CAAClC,UAAU,CAACkC,MAAM,CAAC7F,KAAK,CAACK,aAAa,CAAC,CAAC8F,IAAI,CAAC,CAAC;gBACpDN,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE;gBACjB,CAAC,CAAC;gBACF;cACF;YACF;cACE;gBACE;cAAA;UAEN;QACF;MACF,CAAC;IACH;EACF,CAAC,EAAE;IACDhH,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,SAASyK,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAACtL,KAAK;QAC3B8F,OAAO,GAAGwF,YAAY,CAACxF,OAAO;QAC9BZ,iBAAiB,GAAGoG,YAAY,CAACpG,iBAAiB;MACpD,IAAIqE,WAAW,GAAG,IAAI,CAACtE,KAAK,CAACsE,WAAW;MACxC,IAAIrE,iBAAiB,IAAIY,OAAO,IAAIA,OAAO,CAAC1H,MAAM,KAAK,CAACmL,WAAW,IAAI,CAACrM,QAAQ,CAACqM,WAAW,EAAEzD,OAAO,CAAC,CAAC,EAAE;QACvG,OAAO,IAAI,CAACkD,0BAA0B,CAAC,CAAC;MAC1C;MACA,OAAO,IAAI,CAACf,uBAAuB,CAACnC,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE;IACDxH,GAAG,EAAE,mBAAmB;IACxBsC,KAAK,EAAE,SAAS2K,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACV,MAAM,EAAE;QACf,IAAI,CAACD,sBAAsB,CAAC,IAAI,CAACC,MAAM,CAAC;MAC1C;IACF;EACF,CAAC,EAAE;IACDvM,GAAG,EAAE,QAAQ;IACbsC,KAAK,EAAE,SAAS4K,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC1L,KAAK;QAC3B2L,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxB7F,OAAO,GAAG4F,YAAY,CAAC5F,OAAO;QAC9BkC,SAAS,GAAG0D,YAAY,CAAC1D,SAAS;QAClChC,KAAK,GAAG0F,YAAY,CAAC1F,KAAK;QAC1Be,EAAE,GAAG2E,YAAY,CAAC3E,EAAE;QACpBC,EAAE,GAAG0E,YAAY,CAAC1E,EAAE;QACpB4E,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtC3E,WAAW,GAAGyE,YAAY,CAACzE,WAAW;QACtC/B,iBAAiB,GAAGwG,YAAY,CAACxG,iBAAiB;MACpD,IAAIH,mBAAmB,GAAG,IAAI,CAACE,KAAK,CAACF,mBAAmB;MACxD,IAAI4G,IAAI,IAAI,CAAC7F,OAAO,IAAI,CAACA,OAAO,CAAC1H,MAAM,IAAI,CAAC6F,QAAQ,CAAC8C,EAAE,CAAC,IAAI,CAAC9C,QAAQ,CAAC+C,EAAE,CAAC,IAAI,CAAC/C,QAAQ,CAAC2H,WAAW,CAAC,IAAI,CAAC3H,QAAQ,CAACgD,WAAW,CAAC,EAAE;QAC7H,OAAO,IAAI;MACb;MACA,IAAI4E,UAAU,GAAGzI,UAAU,CAAC,cAAc,EAAE4E,SAAS,CAAC;MACtD,OAAO,aAAa/E,KAAK,CAAC2E,aAAa,CAACvE,KAAK,EAAE;QAC7CyF,QAAQ,EAAE,CAAC;QACXd,SAAS,EAAE6D,UAAU;QACrBnD,GAAG,EAAE,SAASA,GAAGA,CAACoD,KAAK,EAAE;UACvBL,MAAM,CAACZ,MAAM,GAAGiB,KAAK;QACvB;MACF,CAAC,EAAE,IAAI,CAACT,aAAa,CAAC,CAAC,EAAErF,KAAK,IAAI,IAAI,CAACH,YAAY,CAACC,OAAO,CAAC,EAAErC,KAAK,CAACsI,kBAAkB,CAAC,IAAI,CAAC/L,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAACkF,iBAAiB,IAAIH,mBAAmB,KAAKrB,SAAS,CAACqI,kBAAkB,CAAC,IAAI,CAAC/L,KAAK,EAAE8F,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3N;EACF,CAAC,CAAC,EAAE,CAAC;IACHxH,GAAG,EAAE,0BAA0B;IAC/BsC,KAAK,EAAE,SAASoL,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAIA,SAAS,CAAC/G,qBAAqB,KAAK8G,SAAS,CAAC/G,iBAAiB,EAAE;QACnE,OAAO;UACLC,qBAAqB,EAAE8G,SAAS,CAAC/G,iBAAiB;UAClDE,eAAe,EAAE6G,SAAS,CAAC5G,WAAW;UACtC8G,UAAU,EAAEF,SAAS,CAACnG,OAAO;UAC7ByD,WAAW,EAAE,EAAE;UACfxE,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,IAAIkH,SAAS,CAAC/G,iBAAiB,IAAI+G,SAAS,CAAC5G,WAAW,KAAK6G,SAAS,CAAC9G,eAAe,EAAE;QACtF,OAAO;UACLA,eAAe,EAAE6G,SAAS,CAAC5G,WAAW;UACtC8G,UAAU,EAAEF,SAAS,CAACnG,OAAO;UAC7ByD,WAAW,EAAE2C,SAAS,CAACC,UAAU;UACjCpH,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,IAAIkH,SAAS,CAACnG,OAAO,KAAKoG,SAAS,CAACC,UAAU,EAAE;QAC9C,OAAO;UACLA,UAAU,EAAEF,SAAS,CAACnG,OAAO;UAC7Bf,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,SAAS0G,aAAaA,CAACC,CAAC,EAAER,EAAE,EAAE;MACnC,IAAIQ,CAAC,GAAGR,EAAE,EAAE;QACV,OAAO,OAAO;MAChB;MACA,IAAIQ,CAAC,GAAGR,EAAE,EAAE;QACV,OAAO,KAAK;MACd;MACA,OAAO,QAAQ;IACjB;EACF,CAAC,EAAE;IACDzI,GAAG,EAAE,qBAAqB;IAC1BsC,KAAK,EAAE,SAASkH,mBAAmBA,CAACsE,MAAM,EAAEpM,KAAK,EAAE;MACjD,IAAK,aAAaiD,KAAK,CAACoJ,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAanJ,KAAK,CAACqJ,YAAY,CAACF,MAAM,EAAEpM,KAAK,CAAC;MACvD;MACA,IAAI3C,WAAW,CAAC+O,MAAM,CAAC,EAAE;QACvB,OAAOA,MAAM,CAACpM,KAAK,CAAC;MACtB;MACA,OAAO,aAAaiD,KAAK,CAAC2E,aAAa,CAACrE,KAAK,EAAE1F,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;QACjEuM,IAAI,EAAE,QAAQ;QACdvE,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD1J,GAAG,EAAE,iBAAiB;IACtBsC,KAAK,EAAE,SAASmH,eAAeA,CAACqE,MAAM,EAAEpM,KAAK,EAAEY,KAAK,EAAE;MACpD,IAAK,aAAaqC,KAAK,CAACoJ,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAanJ,KAAK,CAACqJ,YAAY,CAACF,MAAM,EAAEpM,KAAK,CAAC;MACvD;MACA,IAAIgG,KAAK,GAAGpF,KAAK;MACjB,IAAIvD,WAAW,CAAC+O,MAAM,CAAC,EAAE;QACvBpG,KAAK,GAAGoG,MAAM,CAACpM,KAAK,CAAC;QACrB,IAAK,aAAaiD,KAAK,CAACoJ,cAAc,CAACrG,KAAK,CAAC,EAAE;UAC7C,OAAOA,KAAK;QACd;MACF;MACA,OAAO,aAAa/C,KAAK,CAAC2E,aAAa,CAACpE,IAAI,EAAE3F,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;QAChEwM,iBAAiB,EAAE,QAAQ;QAC3BxE,SAAS,EAAE;MACb,CAAC,CAAC,EAAEhC,KAAK,CAAC;IACZ;EACF,CAAC,EAAE;IACD1H,GAAG,EAAE,kBAAkB;IACvBsC,KAAK,EAAE,SAASmI,gBAAgBA,CAACqD,MAAM,EAAEpM,KAAK,EAAE;MAC9C,IAAK,aAAaiD,KAAK,CAACoJ,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAanJ,KAAK,CAACqJ,YAAY,CAACF,MAAM,EAAEpM,KAAK,CAAC;MACvD;MACA,IAAI3C,WAAW,CAAC+O,MAAM,CAAC,EAAE;QACvB,OAAOA,MAAM,CAACpM,KAAK,CAAC;MACtB;MACA,IAAI5C,cAAc,CAACgP,MAAM,CAAC,EAAE;QAC1B,OAAO,aAAanJ,KAAK,CAAC2E,aAAa,CAACtE,MAAM,EAAEzF,QAAQ,CAAC;UACvDiL,QAAQ,EAAE,CAAC;QACb,CAAC,EAAE9I,KAAK,EAAEoM,MAAM,CAAC,CAAC;MACpB;MACA,OAAO,aAAanJ,KAAK,CAAC2E,aAAa,CAACtE,MAAM,EAAEzF,QAAQ,CAAC;QACvDiL,QAAQ,EAAE,CAAC;MACb,CAAC,EAAE9I,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,CAAC,CAAC;EACH,OAAOyE,GAAG;AACZ,CAAC,CAACvB,aAAa,CAAC;AAChB3D,eAAe,CAACkF,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1ClF,eAAe,CAACkF,GAAG,EAAE,cAAc,EAAE;EACnC0C,MAAM,EAAE,MAAM;EACdM,IAAI,EAAE,SAAS;EACfgF,UAAU,EAAE,MAAM;EAClB1F,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,KAAK;EACTJ,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE,GAAG;EACb+E,WAAW,EAAE,CAAC;EACd3E,WAAW,EAAE,KAAK;EAClBqD,YAAY,EAAE,CAAC;EACfrE,SAAS,EAAE,IAAI;EACf0F,IAAI,EAAE,KAAK;EACXe,QAAQ,EAAE,CAAC;EACXxH,iBAAiB,EAAE,CAACpB,MAAM,CAAC6I,KAAK;EAChCxD,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBuD,OAAO,EAAE,MAAM;EACfvE,WAAW,EAAE;AACf,CAAC,CAAC;AACF9I,eAAe,CAACkF,GAAG,EAAE,iBAAiB,EAAE,UAAUmC,UAAU,EAAEC,QAAQ,EAAE;EACtE,IAAIgG,IAAI,GAAG1I,QAAQ,CAAC0C,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAI8D,UAAU,GAAGoC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACnG,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAOiG,IAAI,GAAGnC,UAAU;AAC1B,CAAC,CAAC;AACFnL,eAAe,CAACkF,GAAG,EAAE,gBAAgB,EAAE,UAAUwI,IAAI,EAAE;EACrD,IAAIC,WAAW,GAAGD,IAAI,CAACjN,KAAK;IAC1BmN,IAAI,GAAGD,WAAW,CAACC,IAAI;IACvBC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;EACjC,IAAIC,iBAAiB,GAAGxJ,WAAW,CAACoJ,IAAI,CAACjN,KAAK,CAAC;EAC/C,IAAIsN,KAAK,GAAG1J,aAAa,CAACwJ,QAAQ,EAAEzJ,IAAI,CAAC;EACzC,IAAIwJ,IAAI,IAAIA,IAAI,CAAC/O,MAAM,EAAE;IACvB,OAAO+O,IAAI,CAAC1G,GAAG,CAAC,UAAUC,KAAK,EAAEU,KAAK,EAAE;MACtC,OAAO/H,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC/CkO,OAAO,EAAE7G;MACX,CAAC,EAAE2G,iBAAiB,CAAC,EAAE3G,KAAK,CAAC,EAAE4G,KAAK,IAAIA,KAAK,CAAClG,KAAK,CAAC,IAAIkG,KAAK,CAAClG,KAAK,CAAC,CAACpH,KAAK,CAAC;IAC7E,CAAC,CAAC;EACJ;EACA,IAAIsN,KAAK,IAAIA,KAAK,CAAClP,MAAM,EAAE;IACzB,OAAOkP,KAAK,CAAC7G,GAAG,CAAC,UAAU+G,IAAI,EAAE;MAC/B,OAAOnO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgO,iBAAiB,CAAC,EAAEG,IAAI,CAACxN,KAAK,CAAC;IACxE,CAAC,CAAC;EACJ;EACA,OAAO,EAAE;AACX,CAAC,CAAC;AACFT,eAAe,CAACkF,GAAG,EAAE,sBAAsB,EAAE,UAAUwI,IAAI,EAAEQ,MAAM,EAAE;EACnE,IAAIC,GAAG,GAAGD,MAAM,CAACC,GAAG;IAClBC,IAAI,GAAGF,MAAM,CAACE,IAAI;IAClBC,KAAK,GAAGH,MAAM,CAACG,KAAK;IACpBC,MAAM,GAAGJ,MAAM,CAACI,MAAM;EACxB,IAAIC,YAAY,GAAG9J,YAAY,CAAC4J,KAAK,EAAEC,MAAM,CAAC;EAC9C,IAAI9G,EAAE,GAAG4G,IAAI,GAAGzJ,eAAe,CAAC+I,IAAI,CAACjN,KAAK,CAAC+G,EAAE,EAAE6G,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;EAChE,IAAI5G,EAAE,GAAG0G,GAAG,GAAGxJ,eAAe,CAAC+I,IAAI,CAACjN,KAAK,CAACgH,EAAE,EAAE6G,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;EACjE,IAAIjC,WAAW,GAAG1H,eAAe,CAAC+I,IAAI,CAACjN,KAAK,CAAC4L,WAAW,EAAEkC,YAAY,EAAE,CAAC,CAAC;EAC1E,IAAI7G,WAAW,GAAG/C,eAAe,CAAC+I,IAAI,CAACjN,KAAK,CAACiH,WAAW,EAAE6G,YAAY,EAAEA,YAAY,GAAG,GAAG,CAAC;EAC3F,IAAIC,SAAS,GAAGd,IAAI,CAACjN,KAAK,CAAC+N,SAAS,IAAIjB,IAAI,CAACkB,IAAI,CAACJ,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC;EACtF,OAAO;IACL9G,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACN4E,WAAW,EAAEA,WAAW;IACxB3E,WAAW,EAAEA,WAAW;IACxB8G,SAAS,EAAEA;EACb,CAAC;AACH,CAAC,CAAC;AACFxO,eAAe,CAACkF,GAAG,EAAE,iBAAiB,EAAE,UAAUwJ,KAAK,EAAE;EACvD,IAAIhB,IAAI,GAAGgB,KAAK,CAAChB,IAAI;IACnBQ,MAAM,GAAGQ,KAAK,CAACR,MAAM;EACvB,IAAIS,OAAO,GAAGzJ,GAAG,CAAC0J,cAAc,CAAClB,IAAI,CAAC;EACtC,IAAI,CAACiB,OAAO,IAAI,CAACA,OAAO,CAAC9P,MAAM,EAAE;IAC/B,OAAO,IAAI;EACb;EACA,IAAIgQ,YAAY,GAAGnB,IAAI,CAACjN,KAAK;IAC3BqO,YAAY,GAAGD,YAAY,CAACC,YAAY;IACxCzH,UAAU,GAAGwH,YAAY,CAACxH,UAAU;IACpCC,QAAQ,GAAGuH,YAAY,CAACvH,QAAQ;IAChCyD,YAAY,GAAG8D,YAAY,CAAC9D,YAAY;IACxCpE,OAAO,GAAGkI,YAAY,CAAClI,OAAO;IAC9B0G,OAAO,GAAGwB,YAAY,CAACxB,OAAO;IAC9BzG,QAAQ,GAAGiI,YAAY,CAACjI,QAAQ;IAChCmI,WAAW,GAAGF,YAAY,CAACE,WAAW;EACxC,IAAI5B,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAACC,IAAI,CAACjN,KAAK,CAAC0M,QAAQ,CAAC;EAC5C,IAAI6B,UAAU,GAAG9J,GAAG,CAAC+J,oBAAoB,CAACvB,IAAI,EAAEQ,MAAM,CAAC;EACvD,IAAI/C,UAAU,GAAGjG,GAAG,CAACgK,eAAe,CAAC7H,UAAU,EAAEC,QAAQ,CAAC;EAC1D,IAAI6H,aAAa,GAAG5B,IAAI,CAACE,GAAG,CAACtC,UAAU,CAAC;EACxC,IAAI/C,WAAW,GAAGzB,OAAO;EACzB,IAAI5I,MAAM,CAAC4I,OAAO,CAAC,IAAI5I,MAAM,CAAC6I,QAAQ,CAAC,EAAE;IACvC5B,IAAI,CAAC,KAAK,EAAE,wGAAwG,CAAC;IACrHoD,WAAW,GAAG,OAAO;EACvB,CAAC,MAAM,IAAIrK,MAAM,CAAC4I,OAAO,CAAC,EAAE;IAC1B3B,IAAI,CAAC,KAAK,EAAE,wGAAwG,CAAC;IACrHoD,WAAW,GAAGxB,QAAQ;EACxB;EACA,IAAIwI,gBAAgB,GAAGT,OAAO,CAAClP,MAAM,CAAC,UAAU0H,KAAK,EAAE;IACrD,OAAOpC,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC,CAACvJ,MAAM;EACT,IAAIwQ,gBAAgB,GAAG,CAACF,aAAa,IAAI,GAAG,GAAGC,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,IAAIrE,YAAY;EACtG,IAAIuE,cAAc,GAAGH,aAAa,GAAGC,gBAAgB,GAAGjC,QAAQ,GAAGkC,gBAAgB;EACnF,IAAIE,GAAG,GAAGZ,OAAO,CAACa,MAAM,CAAC,UAAUtN,MAAM,EAAEiF,KAAK,EAAE;IAChD,IAAIsI,GAAG,GAAG1K,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC;IAClD,OAAOlG,MAAM,IAAIwC,QAAQ,CAAC+K,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;EAC3C,CAAC,EAAE,CAAC,CAAC;EACL,IAAIlJ,OAAO;EACX,IAAIgJ,GAAG,GAAG,CAAC,EAAE;IACX,IAAIzE,IAAI;IACRvE,OAAO,GAAGoI,OAAO,CAACzH,GAAG,CAAC,UAAUC,KAAK,EAAExI,CAAC,EAAE;MACxC,IAAI8Q,GAAG,GAAG1K,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC;MAClD,IAAIsH,IAAI,GAAG3K,iBAAiB,CAACoC,KAAK,EAAEkG,OAAO,EAAE1O,CAAC,CAAC;MAC/C,IAAIgR,OAAO,GAAG,CAACjL,QAAQ,CAAC+K,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,IAAIF,GAAG;MAC7C,IAAIK,cAAc;MAClB,IAAIjR,CAAC,EAAE;QACLiR,cAAc,GAAG9E,IAAI,CAACxD,QAAQ,GAAG1C,QAAQ,CAACuG,UAAU,CAAC,GAAGJ,YAAY,IAAI0E,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5F,CAAC,MAAM;QACLG,cAAc,GAAGvI,UAAU;MAC7B;MACA,IAAIwI,YAAY,GAAGD,cAAc,GAAGhL,QAAQ,CAACuG,UAAU,CAAC,IAAI,CAACsE,GAAG,KAAK,CAAC,GAAGtC,QAAQ,GAAG,CAAC,IAAIwC,OAAO,GAAGL,cAAc,CAAC;MAClH,IAAIlI,QAAQ,GAAG,CAACwI,cAAc,GAAGC,YAAY,IAAI,CAAC;MAClD,IAAIC,YAAY,GAAG,CAACd,UAAU,CAAC3C,WAAW,GAAG2C,UAAU,CAACtH,WAAW,IAAI,CAAC;MACxE,IAAIqI,cAAc,GAAG,CAAC;QACpBL,IAAI,EAAEA,IAAI;QACVrO,KAAK,EAAEoO,GAAG;QACVzB,OAAO,EAAE7G,KAAK;QACdR,OAAO,EAAEyB,WAAW;QACpB4E,IAAI,EAAE+B;MACR,CAAC,CAAC;MACF,IAAIiB,eAAe,GAAGxL,gBAAgB,CAACwK,UAAU,CAACxH,EAAE,EAAEwH,UAAU,CAACvH,EAAE,EAAEqI,YAAY,EAAE1I,QAAQ,CAAC;MAC5F0D,IAAI,GAAGhL,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC/C6P,OAAO,EAAEA,OAAO;QAChBb,YAAY,EAAEA,YAAY;QAC1BY,IAAI,EAAEA,IAAI;QACVK,cAAc,EAAEA,cAAc;QAC9B3I,QAAQ,EAAEA,QAAQ;QAClB0I,YAAY,EAAEA,YAAY;QAC1BE,eAAe,EAAEA;MACnB,CAAC,EAAE7I,KAAK,CAAC,EAAE6H,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1B3N,KAAK,EAAE0D,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,CAAC;QAC5Cf,UAAU,EAAEuI,cAAc;QAC1BtI,QAAQ,EAAEuI,YAAY;QACtB7B,OAAO,EAAE7G,KAAK;QACd4D,YAAY,EAAEnG,QAAQ,CAACuG,UAAU,CAAC,GAAGJ;MACvC,CAAC,CAAC;MACF,OAAOD,IAAI;IACb,CAAC,CAAC;EACJ;EACA,OAAOhL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkP,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDzI,OAAO,EAAEA,OAAO;IAChBqH,IAAI,EAAEe;EACR,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}