{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Element = function Element(props) {\n  var _classNames, _classNames2;\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    size = props.size,\n    shape = props.shape;\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var shapeCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-circle\"), shape === 'circle'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-square\"), shape === 'square'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-round\"), shape === 'round'), _classNames2));\n  var sizeStyle = React.useMemo(function () {\n    return typeof size === 'number' ? {\n      width: size,\n      height: size,\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n  }, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: _extends(_extends({}, sizeStyle), style)\n  });\n};\nexport default Element;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "React", "Element", "props", "_classNames", "_classNames2", "prefixCls", "className", "style", "size", "shape", "sizeCls", "concat", "shapeCls", "sizeStyle", "useMemo", "width", "height", "lineHeight", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Element.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Element = function Element(props) {\n  var _classNames, _classNames2;\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    size = props.size,\n    shape = props.shape;\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var shapeCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-circle\"), shape === 'circle'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-square\"), shape === 'square'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-round\"), shape === 'round'), _classNames2));\n  var sizeStyle = React.useMemo(function () {\n    return typeof size === 'number' ? {\n      width: size,\n      height: size,\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n  }, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: _extends(_extends({}, sizeStyle), style)\n  });\n};\nexport default Element;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,WAAW,EAAEC,YAAY;EAC7B,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC7BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,KAAK,GAAGP,KAAK,CAACO,KAAK;EACrB,IAAIC,OAAO,GAAGX,UAAU,EAAEI,WAAW,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACN,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAEV,eAAe,CAACK,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACN,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAEL,WAAW,CAAC,CAAC;EACnN,IAAIS,QAAQ,GAAGb,UAAU,EAAEK,YAAY,GAAG,CAAC,CAAC,EAAEN,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,SAAS,CAAC,EAAEI,KAAK,KAAK,QAAQ,CAAC,EAAEX,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,SAAS,CAAC,EAAEI,KAAK,KAAK,QAAQ,CAAC,EAAEX,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,QAAQ,CAAC,EAAEI,KAAK,KAAK,OAAO,CAAC,EAAEL,YAAY,CAAC,CAAC;EACtT,IAAIS,SAAS,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY;IACxC,OAAO,OAAON,IAAI,KAAK,QAAQ,GAAG;MAChCO,KAAK,EAAEP,IAAI;MACXQ,MAAM,EAAER,IAAI;MACZS,UAAU,EAAE,EAAE,CAACN,MAAM,CAACH,IAAI,EAAE,IAAI;IAClC,CAAC,GAAG,CAAC,CAAC;EACR,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,OAAO,aAAaR,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC9CZ,SAAS,EAAEP,UAAU,CAACM,SAAS,EAAEK,OAAO,EAAEE,QAAQ,EAAEN,SAAS,CAAC;IAC9DC,KAAK,EAAEV,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgB,SAAS,CAAC,EAAEN,KAAK;EAChD,CAAC,CAAC;AACJ,CAAC;AACD,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}