{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && /*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        }),\n        role: \"presentation\"\n      });\n      var downNode = sortDirections.includes(DESCEND) && /*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        }),\n        role: \"presentation\"\n      });\n      var _ref2 = tableLocale || {},\n        cancelSort = _ref2.cancelSort,\n        triggerAsc = _ref2.triggerAsc,\n        triggerDesc = _ref2.triggerDesc;\n      var sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps), renderSortTitle) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var _a;\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sorterOrder) {\n            cell['aria-sort'] = sorterOrder === 'ascend' ? 'ascending' : 'descending';\n          } else {\n            cell['aria-label'] = \"\".concat(renderColumnTitle(column.title, {}), \" sortable\");\n          }\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = ((_a = renderColumnTitle(column.title, {})) !== null && _a !== void 0 ? _a : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n    sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n      sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  });\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n        sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n    mergedColumns = _ref5.mergedColumns,\n    onSorterChange = _ref5.onSorterChange,\n    sortDirections = _ref5.sortDirections,\n    tableLocale = _ref5.tableLocale,\n    showSorterTooltip = _ref5.showSorterTooltip;\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    sortStates = _React$useState2[0],\n    setSortStates = _React$useState2[1];\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    var validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n        sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    var newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "_extends", "_toConsumableArray", "_typeof", "CaretDownOutlined", "CaretUpOutlined", "classNames", "KeyCode", "React", "<PERSON><PERSON><PERSON>", "getColumnKey", "getColumnPos", "renderColumnTitle", "ASCEND", "DESCEND", "getMultiplePriority", "column", "sorter", "multiple", "getSortFunction", "compare", "nextSortDirection", "sortDirections", "current", "indexOf", "collectSortStates", "columns", "init", "pos", "sortStates", "pushState", "columnPos", "push", "key", "multiplePriority", "sortOrder", "for<PERSON>ach", "index", "children", "concat", "defaultSortOrder", "injectSorter", "prefixCls", "sorterStates", "triggerSorter", "defaultSortDirections", "tableLocale", "tableShowSorterTooltip", "map", "newColumn", "showSorterTooltip", "undefined", "column<PERSON>ey", "sorterState", "find", "_ref", "sorterOrder", "nextSortOrder", "upNode", "includes", "createElement", "className", "active", "role", "downNode", "_ref2", "cancelSort", "triggerAsc", "triggerDesc", "sortTip", "tooltipProps", "title", "renderProps", "renderSortTitle", "onHeaderCell", "col", "_a", "cell", "originOnClick", "onClick", "originOKeyDown", "onKeyDown", "event", "keyCode", "ENTER", "tabIndex", "ellipsis", "toString", "stateToInfo", "order", "field", "dataIndex", "generateSorterInfo", "list", "filter", "_ref3", "length", "getSortData", "data", "childrenColumnName", "innerSorterStates", "slice", "sort", "a", "b", "cloneData", "running<PERSON><PERSON><PERSON>", "_ref4", "record1", "record2", "i", "compareFn", "compareResult", "record", "subRecords", "use<PERSON>ilter<PERSON><PERSON>er", "_ref5", "mergedColumns", "onSorterChange", "_React$useState", "useState", "_React$useState2", "setSortStates", "mergedSorterStates", "useMemo", "validate", "collectedStates", "validateStates", "patchStates", "state", "multipleMode", "columnTitleSorterProps", "sortColumns", "_ref6", "sortColumn", "sortState", "newSorterStates", "_ref7", "transformColumns", "innerColumns", "getSorters"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/hooks/useSorter.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && /*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        }),\n        role: \"presentation\"\n      });\n      var downNode = sortDirections.includes(DESCEND) && /*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        }),\n        role: \"presentation\"\n      });\n      var _ref2 = tableLocale || {},\n        cancelSort = _ref2.cancelSort,\n        triggerAsc = _ref2.triggerAsc,\n        triggerDesc = _ref2.triggerDesc;\n      var sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps), renderSortTitle) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var _a;\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sorterOrder) {\n            cell['aria-sort'] = sorterOrder === 'ascend' ? 'ascending' : 'descending';\n          } else {\n            cell['aria-label'] = \"\".concat(renderColumnTitle(column.title, {}), \" sortable\");\n          }\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = ((_a = renderColumnTitle(column.title, {})) !== null && _a !== void 0 ? _a : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n    sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n      sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  });\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n        sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n    mergedColumns = _ref5.mergedColumns,\n    onSorterChange = _ref5.onSorterChange,\n    sortDirections = _ref5.sortDirections,\n    tableLocale = _ref5.tableLocale,\n    showSorterTooltip = _ref5.showSorterTooltip;\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    sortStates = _React$useState2[0],\n    setSortStates = _React$useState2[1];\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    var validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n        sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    var newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,SAAS;AACvE,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AACvB,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAIb,OAAO,CAACa,MAAM,CAACC,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACC,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACrF,OAAOF,MAAM,CAACC,MAAM,CAACC,QAAQ;EAC/B;EACA,OAAO,KAAK;AACd;AACA,SAASC,eAAeA,CAACF,MAAM,EAAE;EAC/B,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM;EACf;EACA,IAAIA,MAAM,IAAId,OAAO,CAACc,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACG,OAAO,EAAE;IAC5D,OAAOH,MAAM,CAACG,OAAO;EACvB;EACA,OAAO,KAAK;AACd;AACA,SAASC,iBAAiBA,CAACC,cAAc,EAAEC,OAAO,EAAE;EAClD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOD,cAAc,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOA,cAAc,CAACA,cAAc,CAACE,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5D;AACA,SAASE,iBAAiBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC7C,IAAIC,UAAU,GAAG,EAAE;EACnB,SAASC,SAASA,CAACd,MAAM,EAAEe,SAAS,EAAE;IACpCF,UAAU,CAACG,IAAI,CAAC;MACdhB,MAAM,EAAEA,MAAM;MACdiB,GAAG,EAAEvB,YAAY,CAACM,MAAM,EAAEe,SAAS,CAAC;MACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;MAC7CmB,SAAS,EAAEnB,MAAM,CAACmB;IACpB,CAAC,CAAC;EACJ;EACA,CAACT,OAAO,IAAI,EAAE,EAAEU,OAAO,CAAC,UAAUpB,MAAM,EAAEqB,KAAK,EAAE;IAC/C,IAAIN,SAAS,GAAGpB,YAAY,CAAC0B,KAAK,EAAET,GAAG,CAAC;IACxC,IAAIZ,MAAM,CAACsB,QAAQ,EAAE;MACnB,IAAI,WAAW,IAAItB,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B;MACAF,UAAU,GAAG,EAAE,CAACU,MAAM,CAACrC,kBAAkB,CAAC2B,UAAU,CAAC,EAAE3B,kBAAkB,CAACuB,iBAAiB,CAACT,MAAM,CAACsB,QAAQ,EAAEX,IAAI,EAAEI,SAAS,CAAC,CAAC,CAAC;IACjI,CAAC,MAAM,IAAIf,MAAM,CAACC,MAAM,EAAE;MACxB,IAAI,WAAW,IAAID,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B,CAAC,MAAM,IAAIJ,IAAI,IAAIX,MAAM,CAACwB,gBAAgB,EAAE;QAC1C;QACAX,UAAU,CAACG,IAAI,CAAC;UACdhB,MAAM,EAAEA,MAAM;UACdiB,GAAG,EAAEvB,YAAY,CAACM,MAAM,EAAEe,SAAS,CAAC;UACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;UAC7CmB,SAAS,EAAEnB,MAAM,CAACwB;QACpB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EACF,OAAOX,UAAU;AACnB;AACA,SAASY,YAAYA,CAACC,SAAS,EAAEhB,OAAO,EAAEiB,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEnB,GAAG,EAAE;EACtI,OAAO,CAACF,OAAO,IAAI,EAAE,EAAEsB,GAAG,CAAC,UAAUhC,MAAM,EAAEqB,KAAK,EAAE;IAClD,IAAIN,SAAS,GAAGpB,YAAY,CAAC0B,KAAK,EAAET,GAAG,CAAC;IACxC,IAAIqB,SAAS,GAAGjC,MAAM;IACtB,IAAIiC,SAAS,CAAChC,MAAM,EAAE;MACpB,IAAIK,cAAc,GAAG2B,SAAS,CAAC3B,cAAc,IAAIuB,qBAAqB;MACtE,IAAIK,iBAAiB,GAAGD,SAAS,CAACC,iBAAiB,KAAKC,SAAS,GAAGJ,sBAAsB,GAAGE,SAAS,CAACC,iBAAiB;MACxH,IAAIE,SAAS,GAAG1C,YAAY,CAACuC,SAAS,EAAElB,SAAS,CAAC;MAClD,IAAIsB,WAAW,GAAGV,YAAY,CAACW,IAAI,CAAC,UAAUC,IAAI,EAAE;QAClD,IAAItB,GAAG,GAAGsB,IAAI,CAACtB,GAAG;QAClB,OAAOA,GAAG,KAAKmB,SAAS;MAC1B,CAAC,CAAC;MACF,IAAII,WAAW,GAAGH,WAAW,GAAGA,WAAW,CAAClB,SAAS,GAAG,IAAI;MAC5D,IAAIsB,aAAa,GAAGpC,iBAAiB,CAACC,cAAc,EAAEkC,WAAW,CAAC;MAClE,IAAIE,MAAM,GAAGpC,cAAc,CAACqC,QAAQ,CAAC9C,MAAM,CAAC,IAAI,aAAaL,KAAK,CAACoD,aAAa,CAACvD,eAAe,EAAE;QAChGwD,SAAS,EAAEvD,UAAU,CAAC,EAAE,CAACiC,MAAM,CAACG,SAAS,EAAE,mBAAmB,CAAC,EAAE;UAC/DoB,MAAM,EAAEN,WAAW,KAAK3C;QAC1B,CAAC,CAAC;QACFkD,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAIC,QAAQ,GAAG1C,cAAc,CAACqC,QAAQ,CAAC7C,OAAO,CAAC,IAAI,aAAaN,KAAK,CAACoD,aAAa,CAACxD,iBAAiB,EAAE;QACrGyD,SAAS,EAAEvD,UAAU,CAAC,EAAE,CAACiC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE;UACjEoB,MAAM,EAAEN,WAAW,KAAK1C;QAC1B,CAAC,CAAC;QACFiD,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAIE,KAAK,GAAGnB,WAAW,IAAI,CAAC,CAAC;QAC3BoB,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;QAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;MACjC,IAAIC,OAAO,GAAGH,UAAU;MACxB,IAAIT,aAAa,KAAK3C,OAAO,EAAE;QAC7BuD,OAAO,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAIX,aAAa,KAAK5C,MAAM,EAAE;QACnCwD,OAAO,GAAGF,UAAU;MACtB;MACA,IAAIG,YAAY,GAAGnE,OAAO,CAAC+C,iBAAiB,CAAC,KAAK,QAAQ,GAAGA,iBAAiB,GAAG;QAC/EqB,KAAK,EAAEF;MACT,CAAC;MACDpB,SAAS,GAAGhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC,EAAE;QAC5CY,SAAS,EAAEvD,UAAU,CAAC2C,SAAS,CAACY,SAAS,EAAE7D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuC,MAAM,CAACG,SAAS,EAAE,cAAc,CAAC,EAAEc,WAAW,CAAC,CAAC;QAClHe,KAAK,EAAE,SAASA,KAAKA,CAACC,WAAW,EAAE;UACjC,IAAIC,eAAe,GAAG,aAAajE,KAAK,CAACoD,aAAa,CAAC,KAAK,EAAE;YAC5DC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,iBAAiB;UACnD,CAAC,EAAE,aAAalC,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,eAAe;UACjD,CAAC,EAAE9B,iBAAiB,CAACI,MAAM,CAACuD,KAAK,EAAEC,WAAW,CAAC,CAAC,EAAE,aAAahE,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE;YACzFC,SAAS,EAAEvD,UAAU,CAAC,EAAE,CAACiC,MAAM,CAACG,SAAS,EAAE,gBAAgB,CAAC,EAAE1C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE,CAAC,EAAEgB,MAAM,IAAIM,QAAQ,CAAC,CAAC;UACxJ,CAAC,EAAE,aAAaxD,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,sBAAsB;UACxD,CAAC,EAAEgB,MAAM,EAAEM,QAAQ,CAAC,CAAC,CAAC;UACtB,OAAOd,iBAAiB,GAAG,aAAa1C,KAAK,CAACoD,aAAa,CAACnD,OAAO,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEqE,YAAY,CAAC,EAAEG,eAAe,CAAC,GAAGA,eAAe;QACrI,CAAC;QACDC,YAAY,EAAE,SAASA,YAAYA,CAACC,GAAG,EAAE;UACvC,IAAIC,EAAE;UACN,IAAIC,IAAI,GAAG7D,MAAM,CAAC0D,YAAY,IAAI1D,MAAM,CAAC0D,YAAY,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;UAChE,IAAIG,aAAa,GAAGD,IAAI,CAACE,OAAO;UAChC,IAAIC,cAAc,GAAGH,IAAI,CAACI,SAAS;UACnCJ,IAAI,CAACE,OAAO,GAAG,UAAUG,KAAK,EAAE;YAC9BtC,aAAa,CAAC;cACZ5B,MAAM,EAAEA,MAAM;cACdiB,GAAG,EAAEmB,SAAS;cACdjB,SAAS,EAAEsB,aAAa;cACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;YAC9C,CAAC,CAAC;YACF8D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,KAAK,CAAC;UACpF,CAAC;UACDL,IAAI,CAACI,SAAS,GAAG,UAAUC,KAAK,EAAE;YAChC,IAAIA,KAAK,CAACC,OAAO,KAAK5E,OAAO,CAAC6E,KAAK,EAAE;cACnCxC,aAAa,CAAC;gBACZ5B,MAAM,EAAEA,MAAM;gBACdiB,GAAG,EAAEmB,SAAS;gBACdjB,SAAS,EAAEsB,aAAa;gBACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;cAC9C,CAAC,CAAC;cACFgE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,KAAK,CAAC;YACvF;UACF,CAAC;UACD;UACA,IAAI1B,WAAW,EAAE;YACfqB,IAAI,CAAC,WAAW,CAAC,GAAGrB,WAAW,KAAK,QAAQ,GAAG,WAAW,GAAG,YAAY;UAC3E,CAAC,MAAM;YACLqB,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAACtC,MAAM,CAAC3B,iBAAiB,CAACI,MAAM,CAACuD,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC;UAClF;UACAM,IAAI,CAAChB,SAAS,GAAGvD,UAAU,CAACuE,IAAI,CAAChB,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,CAAC;UACxFmC,IAAI,CAACQ,QAAQ,GAAG,CAAC;UACjB,IAAIrE,MAAM,CAACsE,QAAQ,EAAE;YACnBT,IAAI,CAACN,KAAK,GAAG,CAAC,CAACK,EAAE,GAAGhE,iBAAiB,CAACI,MAAM,CAACuD,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEW,QAAQ,CAAC,CAAC;UAC1G;UACA,OAAOV,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IACA,IAAI,UAAU,IAAI5B,SAAS,EAAE;MAC3BA,SAAS,GAAGhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC,EAAE;QAC5CX,QAAQ,EAAEG,YAAY,CAACC,SAAS,EAAEO,SAAS,CAACX,QAAQ,EAAEK,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEhB,SAAS;MAC1J,CAAC,CAAC;IACJ;IACA,OAAOkB,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,SAASuC,WAAWA,CAAC7C,YAAY,EAAE;EACjC,IAAI3B,MAAM,GAAG2B,YAAY,CAAC3B,MAAM;IAC9BmB,SAAS,GAAGQ,YAAY,CAACR,SAAS;EACpC,OAAO;IACLnB,MAAM,EAAEA,MAAM;IACdyE,KAAK,EAAEtD,SAAS;IAChBuD,KAAK,EAAE1E,MAAM,CAAC2E,SAAS;IACvBvC,SAAS,EAAEpC,MAAM,CAACiB;EACpB,CAAC;AACH;AACA,SAAS2D,kBAAkBA,CAACjD,YAAY,EAAE;EACxC,IAAIkD,IAAI,GAAGlD,YAAY,CAACmD,MAAM,CAAC,UAAUC,KAAK,EAAE;IAC9C,IAAI5D,SAAS,GAAG4D,KAAK,CAAC5D,SAAS;IAC/B,OAAOA,SAAS;EAClB,CAAC,CAAC,CAACa,GAAG,CAACwC,WAAW,CAAC;EACnB;EACA;EACA,IAAIK,IAAI,CAACG,MAAM,KAAK,CAAC,IAAIrD,YAAY,CAACqD,MAAM,EAAE;IAC5C,OAAO/F,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuF,WAAW,CAAC7C,YAAY,CAACA,YAAY,CAACqD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAChFhF,MAAM,EAAEmC;IACV,CAAC,CAAC;EACJ;EACA,IAAI0C,IAAI,CAACG,MAAM,IAAI,CAAC,EAAE;IACpB,OAAOH,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACtB;EACA,OAAOA,IAAI;AACb;AACA,OAAO,SAASI,WAAWA,CAACC,IAAI,EAAErE,UAAU,EAAEsE,kBAAkB,EAAE;EAChE,IAAIC,iBAAiB,GAAGvE,UAAU,CAACwE,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC9D,OAAOA,CAAC,CAACtE,gBAAgB,GAAGqE,CAAC,CAACrE,gBAAgB;EAChD,CAAC,CAAC;EACF,IAAIuE,SAAS,GAAGP,IAAI,CAACG,KAAK,CAAC,CAAC;EAC5B,IAAIK,cAAc,GAAGN,iBAAiB,CAACN,MAAM,CAAC,UAAUa,KAAK,EAAE;IAC7D,IAAI1F,MAAM,GAAG0F,KAAK,CAAC3F,MAAM,CAACC,MAAM;MAC9BkB,SAAS,GAAGwE,KAAK,CAACxE,SAAS;IAC7B,OAAOhB,eAAe,CAACF,MAAM,CAAC,IAAIkB,SAAS;EAC7C,CAAC,CAAC;EACF;EACA,IAAI,CAACuE,cAAc,CAACV,MAAM,EAAE;IAC1B,OAAOS,SAAS;EAClB;EACA,OAAOA,SAAS,CAACH,IAAI,CAAC,UAAUM,OAAO,EAAEC,OAAO,EAAE;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACV,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIzD,WAAW,GAAGqD,cAAc,CAACI,CAAC,CAAC;MACnC,IAAI7F,MAAM,GAAGoC,WAAW,CAACrC,MAAM,CAACC,MAAM;QACpCkB,SAAS,GAAGkB,WAAW,CAAClB,SAAS;MACnC,IAAI4E,SAAS,GAAG5F,eAAe,CAACF,MAAM,CAAC;MACvC,IAAI8F,SAAS,IAAI5E,SAAS,EAAE;QAC1B,IAAI6E,aAAa,GAAGD,SAAS,CAACH,OAAO,EAAEC,OAAO,EAAE1E,SAAS,CAAC;QAC1D,IAAI6E,aAAa,KAAK,CAAC,EAAE;UACvB,OAAO7E,SAAS,KAAKtB,MAAM,GAAGmG,aAAa,GAAG,CAACA,aAAa;QAC9D;MACF;IACF;IACA,OAAO,CAAC;EACV,CAAC,CAAC,CAAChE,GAAG,CAAC,UAAUiE,MAAM,EAAE;IACvB,IAAIC,UAAU,GAAGD,MAAM,CAACd,kBAAkB,CAAC;IAC3C,IAAIe,UAAU,EAAE;MACd,OAAOjH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgH,MAAM,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAEmG,kBAAkB,EAAEF,WAAW,CAACiB,UAAU,EAAErF,UAAU,EAAEsE,kBAAkB,CAAC,CAAC,CAAC;IACzI;IACA,OAAOc,MAAM;EACf,CAAC,CAAC;AACJ;AACA,eAAe,SAASE,eAAeA,CAACC,KAAK,EAAE;EAC7C,IAAI1E,SAAS,GAAG0E,KAAK,CAAC1E,SAAS;IAC7B2E,aAAa,GAAGD,KAAK,CAACC,aAAa;IACnCC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrChG,cAAc,GAAG8F,KAAK,CAAC9F,cAAc;IACrCwB,WAAW,GAAGsE,KAAK,CAACtE,WAAW;IAC/BI,iBAAiB,GAAGkE,KAAK,CAAClE,iBAAiB;EAC7C,IAAIqE,eAAe,GAAG/G,KAAK,CAACgH,QAAQ,CAAC/F,iBAAiB,CAAC4F,aAAa,EAAE,IAAI,CAAC,CAAC;IAC1EI,gBAAgB,GAAG1H,cAAc,CAACwH,eAAe,EAAE,CAAC,CAAC;IACrD1F,UAAU,GAAG4F,gBAAgB,CAAC,CAAC,CAAC;IAChCC,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIE,kBAAkB,GAAGnH,KAAK,CAACoH,OAAO,CAAC,YAAY;IACjD,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,eAAe,GAAGrG,iBAAiB,CAAC4F,aAAa,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAACS,eAAe,CAAC9B,MAAM,EAAE;MAC3B,OAAOnE,UAAU;IACnB;IACA,IAAIkG,cAAc,GAAG,EAAE;IACvB,SAASC,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIJ,QAAQ,EAAE;QACZE,cAAc,CAAC/F,IAAI,CAACiG,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLF,cAAc,CAAC/F,IAAI,CAAC/B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgI,KAAK,CAAC,EAAE;UAChD9F,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL;IACF;IACA,IAAI+F,YAAY,GAAG,IAAI;IACvBJ,eAAe,CAAC1F,OAAO,CAAC,UAAU6F,KAAK,EAAE;MACvC,IAAIC,YAAY,KAAK,IAAI,EAAE;QACzBF,WAAW,CAACC,KAAK,CAAC;QAClB,IAAIA,KAAK,CAAC9F,SAAS,EAAE;UACnB,IAAI8F,KAAK,CAAC/F,gBAAgB,KAAK,KAAK,EAAE;YACpC2F,QAAQ,GAAG,KAAK;UAClB,CAAC,MAAM;YACLK,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC,MAAM,IAAIA,YAAY,IAAID,KAAK,CAAC/F,gBAAgB,KAAK,KAAK,EAAE;QAC3D8F,WAAW,CAACC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLJ,QAAQ,GAAG,KAAK;QAChBG,WAAW,CAACC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACV,aAAa,EAAExF,UAAU,CAAC,CAAC;EAC/B;EACA,IAAIsG,sBAAsB,GAAG3H,KAAK,CAACoH,OAAO,CAAC,YAAY;IACrD,IAAIQ,WAAW,GAAGT,kBAAkB,CAAC3E,GAAG,CAAC,UAAUqF,KAAK,EAAE;MACxD,IAAIrH,MAAM,GAAGqH,KAAK,CAACrH,MAAM;QACvBmB,SAAS,GAAGkG,KAAK,CAAClG,SAAS;MAC7B,OAAO;QACLnB,MAAM,EAAEA,MAAM;QACdyE,KAAK,EAAEtD;MACT,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLiG,WAAW,EAAEA,WAAW;MACxB;MACAE,UAAU,EAAEF,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAACpH,MAAM;MACnDmB,SAAS,EAAEiG,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAAC3C;IAC9C,CAAC;EACH,CAAC,EAAE,CAACkC,kBAAkB,CAAC,CAAC;EACxB,SAAS/E,aAAaA,CAAC2F,SAAS,EAAE;IAChC,IAAIC,eAAe;IACnB,IAAID,SAAS,CAACrG,gBAAgB,KAAK,KAAK,IAAI,CAACyF,kBAAkB,CAAC3B,MAAM,IAAI2B,kBAAkB,CAAC,CAAC,CAAC,CAACzF,gBAAgB,KAAK,KAAK,EAAE;MAC1HsG,eAAe,GAAG,CAACD,SAAS,CAAC;IAC/B,CAAC,MAAM;MACLC,eAAe,GAAG,EAAE,CAACjG,MAAM,CAACrC,kBAAkB,CAACyH,kBAAkB,CAAC7B,MAAM,CAAC,UAAU2C,KAAK,EAAE;QACxF,IAAIxG,GAAG,GAAGwG,KAAK,CAACxG,GAAG;QACnB,OAAOA,GAAG,KAAKsG,SAAS,CAACtG,GAAG;MAC9B,CAAC,CAAC,CAAC,EAAE,CAACsG,SAAS,CAAC,CAAC;IACnB;IACAb,aAAa,CAACc,eAAe,CAAC;IAC9BlB,cAAc,CAAC1B,kBAAkB,CAAC4C,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,OAAOlG,YAAY,CAACC,SAAS,EAAEiG,YAAY,EAAEhB,kBAAkB,EAAE/E,aAAa,EAAEtB,cAAc,EAAEwB,WAAW,EAAEI,iBAAiB,CAAC;EACjI,CAAC;EACD,IAAI0F,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAOhD,kBAAkB,CAAC+B,kBAAkB,CAAC;EAC/C,CAAC;EACD,OAAO,CAACe,gBAAgB,EAAEf,kBAAkB,EAAEQ,sBAAsB,EAAES,UAAU,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}