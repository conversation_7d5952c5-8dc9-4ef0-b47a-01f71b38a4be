{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = leastIndex;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _minIndex = _interopRequireDefault(require(\"./minIndex.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction leastIndex(values, compare = _ascending.default) {\n  if (compare.length === 1) return (0, _minIndex.default)(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "leastIndex", "_ascending", "_interopRequireDefault", "require", "_minIndex", "obj", "__esModule", "values", "compare", "length", "minValue", "min", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/leastIndex.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = leastIndex;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _minIndex = _interopRequireDefault(require(\"./minIndex.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction leastIndex(values, compare = _ascending.default) {\n  if (compare.length === 1) return (0, _minIndex.default)(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n\n  for (const value of values) {\n    ++index;\n\n    if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n\n  return min;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,SAAS,GAAGF,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,UAAUA,CAACO,MAAM,EAAEC,OAAO,GAAGP,UAAU,CAACF,OAAO,EAAE;EACxD,IAAIS,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAEL,SAAS,CAACL,OAAO,EAAEQ,MAAM,EAAEC,OAAO,CAAC;EACxE,IAAIE,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,MAAMd,KAAK,IAAIS,MAAM,EAAE;IAC1B,EAAEK,KAAK;IAEP,IAAID,GAAG,GAAG,CAAC,GAAGH,OAAO,CAACV,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAAGU,OAAO,CAACV,KAAK,EAAEY,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxEA,QAAQ,GAAGZ,KAAK;MAChBa,GAAG,GAAGC,KAAK;IACb;EACF;EAEA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}