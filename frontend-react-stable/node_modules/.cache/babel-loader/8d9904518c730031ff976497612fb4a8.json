{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.seconds = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nvar _duration = require(\"./duration.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar second = (0, _interval.default)(function (date) {\n  date.setTime(date - date.getMilliseconds());\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationSecond);\n}, function (start, end) {\n  return (end - start) / _duration.durationSecond;\n}, function (date) {\n  return date.getUTCSeconds();\n});\nvar _default = second;\nexports.default = _default;\nvar seconds = second.range;\nexports.seconds = seconds;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "seconds", "default", "_interval", "_interopRequireDefault", "require", "_duration", "obj", "__esModule", "second", "date", "setTime", "getMilliseconds", "step", "durationSecond", "start", "end", "getUTCSeconds", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/second.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.seconds = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nvar _duration = require(\"./duration.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar second = (0, _interval.default)(function (date) {\n  date.setTime(date - date.getMilliseconds());\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationSecond);\n}, function (start, end) {\n  return (end - start) / _duration.durationSecond;\n}, function (date) {\n  return date.getUTCSeconds();\n});\nvar _default = second;\nexports.default = _default;\nvar seconds = second.range;\nexports.seconds = seconds;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE1C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,MAAM,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACD,OAAO,EAAE,UAAUQ,IAAI,EAAE;EAClDA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;AAC7C,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;EACvBH,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGG,IAAI,GAAGP,SAAS,CAACQ,cAAc,CAAC;AACvD,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIT,SAAS,CAACQ,cAAc;AACjD,CAAC,EAAE,UAAUJ,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACO,aAAa,CAAC,CAAC;AAC7B,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGT,MAAM;AACrBV,OAAO,CAACG,OAAO,GAAGgB,QAAQ;AAC1B,IAAIjB,OAAO,GAAGQ,MAAM,CAACU,KAAK;AAC1BpB,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}