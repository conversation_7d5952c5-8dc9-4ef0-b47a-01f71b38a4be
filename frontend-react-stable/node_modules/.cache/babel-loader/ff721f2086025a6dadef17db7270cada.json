{"ast": null, "code": "/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    var REACT_FRAGMENT_TYPE = 0xeacb;\n    var REACT_STRICT_MODE_TYPE = 0xeacc;\n    var REACT_PROFILER_TYPE = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    var REACT_SUSPENSE_TYPE = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_BLOCK_TYPE = 0xead9;\n    var REACT_SERVER_BLOCK_TYPE = 0xeada;\n    var REACT_FUNDAMENTAL_TYPE = 0xead5;\n    var REACT_RESPONDER_TYPE = 0xead6;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_OPAQUE_ID_TYPE = 0xeae0;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n      REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n      REACT_PROFILER_TYPE = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_BLOCK_TYPE = symbolFor('react.block');\n      REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n      REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n      REACT_RESPONDER_TYPE = symbolFor('react.responder');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n    }\n    var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n    // It's OK to reference families, but use WeakMap/Set for types.\n\n    var allFamiliesByID = new Map();\n    var allFamiliesByType = new PossiblyWeakMap();\n    var allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n    // that have actually been edited here. This keeps checks fast.\n    // $FlowIssue\n\n    var updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n    // It is an array of [Family, NextType] tuples.\n\n    var pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\n    var helpersByRendererID = new Map();\n    var helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\n    var mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\n    var failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n    // It needs to be weak because we do this even for roots that failed to mount.\n    // If there is no WeakMap, we won't attempt to do retrying.\n    // $FlowIssue\n\n    var rootElements =\n    // $FlowIssue\n    typeof WeakMap === 'function' ? new WeakMap() : null;\n    var isPerformingRefresh = false;\n    function computeFullKey(signature) {\n      if (signature.fullKey !== null) {\n        return signature.fullKey;\n      }\n      var fullKey = signature.ownKey;\n      var hooks;\n      try {\n        hooks = signature.getCustomHooks();\n      } catch (err) {\n        // This can happen in an edge case, e.g. if expression like Foo.useSomething\n        // depends on Foo which is lazily initialized during rendering.\n        // In that case just assume we'll have to remount.\n        signature.forceReset = true;\n        signature.fullKey = fullKey;\n        return fullKey;\n      }\n      for (var i = 0; i < hooks.length; i++) {\n        var hook = hooks[i];\n        if (typeof hook !== 'function') {\n          // Something's wrong. Assume we need to remount.\n          signature.forceReset = true;\n          signature.fullKey = fullKey;\n          return fullKey;\n        }\n        var nestedHookSignature = allSignaturesByType.get(hook);\n        if (nestedHookSignature === undefined) {\n          // No signature means Hook wasn't in the source code, e.g. in a library.\n          // We'll skip it because we can assume it won't change during this session.\n          continue;\n        }\n        var nestedHookKey = computeFullKey(nestedHookSignature);\n        if (nestedHookSignature.forceReset) {\n          signature.forceReset = true;\n        }\n        fullKey += '\\n---\\n' + nestedHookKey;\n      }\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n    function haveEqualSignatures(prevType, nextType) {\n      var prevSignature = allSignaturesByType.get(prevType);\n      var nextSignature = allSignaturesByType.get(nextType);\n      if (prevSignature === undefined && nextSignature === undefined) {\n        return true;\n      }\n      if (prevSignature === undefined || nextSignature === undefined) {\n        return false;\n      }\n      if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n        return false;\n      }\n      if (nextSignature.forceReset) {\n        return false;\n      }\n      return true;\n    }\n    function isReactClass(type) {\n      return type.prototype && type.prototype.isReactComponent;\n    }\n    function canPreserveStateBetween(prevType, nextType) {\n      if (isReactClass(prevType) || isReactClass(nextType)) {\n        return false;\n      }\n      if (haveEqualSignatures(prevType, nextType)) {\n        return true;\n      }\n      return false;\n    }\n    function resolveFamily(type) {\n      // Only check updated types to keep lookups fast.\n      return updatedFamiliesByType.get(type);\n    } // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n    function cloneMap(map) {\n      var clone = new Map();\n      map.forEach(function (value, key) {\n        clone.set(key, value);\n      });\n      return clone;\n    }\n    function cloneSet(set) {\n      var clone = new Set();\n      set.forEach(function (value) {\n        clone.add(value);\n      });\n      return clone;\n    }\n    function performReactRefresh() {\n      if (pendingUpdates.length === 0) {\n        return null;\n      }\n      if (isPerformingRefresh) {\n        return null;\n      }\n      isPerformingRefresh = true;\n      try {\n        var staleFamilies = new Set();\n        var updatedFamilies = new Set();\n        var updates = pendingUpdates;\n        pendingUpdates = [];\n        updates.forEach(function (_ref) {\n          var family = _ref[0],\n            nextType = _ref[1];\n          // Now that we got a real edit, we can create associations\n          // that will be read by the React reconciler.\n          var prevType = family.current;\n          updatedFamiliesByType.set(prevType, family);\n          updatedFamiliesByType.set(nextType, family);\n          family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n          if (canPreserveStateBetween(prevType, nextType)) {\n            updatedFamilies.add(family);\n          } else {\n            staleFamilies.add(family);\n          }\n        }); // TODO: rename these fields to something more meaningful.\n\n        var update = {\n          updatedFamilies: updatedFamilies,\n          // Families that will re-render preserving state\n          staleFamilies: staleFamilies // Families that will be remounted\n        };\n        helpersByRendererID.forEach(function (helpers) {\n          // Even if there are no roots, set the handler on first update.\n          // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n          helpers.setRefreshHandler(resolveFamily);\n        });\n        var didError = false;\n        var firstError = null; // We snapshot maps and sets that are mutated during commits.\n        // If we don't do this, there is a risk they will be mutated while\n        // we iterate over them. For example, trying to recover a failed root\n        // may cause another root to be added to the failed list -- an infinite loop.\n\n        var failedRootsSnapshot = cloneSet(failedRoots);\n        var mountedRootsSnapshot = cloneSet(mountedRoots);\n        var helpersByRootSnapshot = cloneMap(helpersByRoot);\n        failedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!failedRoots.has(root)) {// No longer failed.\n          }\n          if (rootElements === null) {\n            return;\n          }\n          if (!rootElements.has(root)) {\n            return;\n          }\n          var element = rootElements.get(root);\n          try {\n            helpers.scheduleRoot(root, element);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        mountedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!mountedRoots.has(root)) {// No longer mounted.\n          }\n          try {\n            helpers.scheduleRefresh(root, update);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        if (didError) {\n          throw firstError;\n        }\n        return update;\n      } finally {\n        isPerformingRefresh = false;\n      }\n    }\n    function register(type, id) {\n      {\n        if (type === null) {\n          return;\n        }\n        if (typeof type !== 'function' && typeof type !== 'object') {\n          return;\n        } // This can happen in an edge case, e.g. if we register\n        // return value of a HOC but it returns a cached component.\n        // Ignore anything but the first registration for each type.\n\n        if (allFamiliesByType.has(type)) {\n          return;\n        } // Create family or remember to update it.\n        // None of this bookkeeping affects reconciliation\n        // until the first performReactRefresh() call above.\n\n        var family = allFamiliesByID.get(id);\n        if (family === undefined) {\n          family = {\n            current: type\n          };\n          allFamiliesByID.set(id, family);\n        } else {\n          pendingUpdates.push([family, type]);\n        }\n        allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (type.$$typeof) {\n            case REACT_FORWARD_REF_TYPE:\n              register(type.render, id + '$render');\n              break;\n            case REACT_MEMO_TYPE:\n              register(type.type, id + '$type');\n              break;\n          }\n        }\n      }\n    }\n    function setSignature(type, key) {\n      var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n      {\n        allSignaturesByType.set(type, {\n          forceReset: forceReset,\n          ownKey: key,\n          fullKey: null,\n          getCustomHooks: getCustomHooks || function () {\n            return [];\n          }\n        });\n      }\n    } // This is lazily called during first render for a type.\n    // It captures Hook list at that time so inline requires don't break comparisons.\n\n    function collectCustomHooksForSignature(type) {\n      {\n        var signature = allSignaturesByType.get(type);\n        if (signature !== undefined) {\n          computeFullKey(signature);\n        }\n      }\n    }\n    function getFamilyByID(id) {\n      {\n        return allFamiliesByID.get(id);\n      }\n    }\n    function getFamilyByType(type) {\n      {\n        return allFamiliesByType.get(type);\n      }\n    }\n    function findAffectedHostInstances(families) {\n      {\n        var affectedInstances = new Set();\n        mountedRoots.forEach(function (root) {\n          var helpers = helpersByRoot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n          instancesForRoot.forEach(function (inst) {\n            affectedInstances.add(inst);\n          });\n        });\n        return affectedInstances;\n      }\n    }\n    function injectIntoGlobalHook(globalObject) {\n      {\n        // For React Native, the global hook will be set up by require('react-devtools-core').\n        // That code will run before us. So we need to monkeypatch functions on existing hook.\n        // For React Web, the global hook will be set up by the extension.\n        // This will also run before us.\n        var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n        if (hook === undefined) {\n          // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n          // Note that in this case it's important that renderer code runs *after* this method call.\n          // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n          var nextID = 0;\n          globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n            renderers: new Map(),\n            supportsFiber: true,\n            inject: function (injected) {\n              return nextID++;\n            },\n            onScheduleFiberRoot: function (id, root, children) {},\n            onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n            onCommitFiberUnmount: function () {}\n          };\n        } // Here, we just want to get a reference to scheduleRefresh.\n\n        var oldInject = hook.inject;\n        hook.inject = function (injected) {\n          var id = oldInject.apply(this, arguments);\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n          return id;\n        }; // Do the same for any already injected roots.\n        // This is useful if ReactDOM has already been initialized.\n        // https://github.com/facebook/react/issues/17626\n\n        hook.renderers.forEach(function (injected, id) {\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n        }); // We also want to track currently mounted roots.\n\n        var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n        var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n        hook.onScheduleFiberRoot = function (id, root, children) {\n          if (!isPerformingRefresh) {\n            // If it was intentionally scheduled, don't attempt to restore.\n            // This includes intentionally scheduled unmounts.\n            failedRoots.delete(root);\n            if (rootElements !== null) {\n              rootElements.set(root, children);\n            }\n          }\n          return oldOnScheduleFiberRoot.apply(this, arguments);\n        };\n        hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n          var helpers = helpersByRendererID.get(id);\n          if (helpers === undefined) {\n            return;\n          }\n          helpersByRoot.set(root, helpers);\n          var current = root.current;\n          var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n          // This logic is copy-pasted from similar logic in the DevTools backend.\n          // If this breaks with some refactoring, you'll want to update DevTools too.\n\n          if (alternate !== null) {\n            var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n            var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n            if (!wasMounted && isMounted) {\n              // Mount a new root.\n              mountedRoots.add(root);\n              failedRoots.delete(root);\n            } else if (wasMounted && isMounted) ;else if (wasMounted && !isMounted) {\n              // Unmount an existing root.\n              mountedRoots.delete(root);\n              if (didError) {\n                // We'll remount it on future edits.\n                failedRoots.add(root);\n              } else {\n                helpersByRoot.delete(root);\n              }\n            } else if (!wasMounted && !isMounted) {\n              if (didError) {\n                // We'll remount it on future edits.\n                failedRoots.add(root);\n              }\n            }\n          } else {\n            // Mount a new root.\n            mountedRoots.add(root);\n          }\n          return oldOnCommitFiberRoot.apply(this, arguments);\n        };\n      }\n    }\n    function hasUnrecoverableErrors() {\n      // TODO: delete this after removing dependency in RN.\n      return false;\n    } // Exposed for testing.\n\n    function _getMountedRootCount() {\n      {\n        return mountedRoots.size;\n      }\n    } // This is a wrapper over more primitive functions for setting signature.\n    // Signatures let us decide whether the Hook order has changed on refresh.\n    //\n    // This function is intended to be used as a transform target, e.g.:\n    // var _s = createSignatureFunctionForTransform()\n    //\n    // function Hello() {\n    //   const [foo, setFoo] = useState(0);\n    //   const value = useCustomHook();\n    //   _s(); /* Second call triggers collecting the custom Hook list.\n    //          * This doesn't happen during the module evaluation because we\n    //          * don't want to change the module order with inline requires.\n    //          * Next calls are noops. */\n    //   return <h1>Hi</h1>;\n    // }\n    //\n    // /* First call specifies the signature: */\n    // _s(\n    //   Hello,\n    //   'useState{[foo, setFoo]}(0)',\n    //   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n    // );\n\n    function createSignatureFunctionForTransform() {\n      {\n        // We'll fill in the signature in two steps.\n        // First, we'll know the signature itself. This happens outside the component.\n        // Then, we'll know the references to custom Hooks. This happens inside the component.\n        // After that, the returned function will be a fast path no-op.\n        var status = 'needsSignature';\n        var savedType;\n        var hasCustomHooks;\n        return function (type, key, forceReset, getCustomHooks) {\n          switch (status) {\n            case 'needsSignature':\n              if (type !== undefined) {\n                // If we received an argument, this is the initial registration call.\n                savedType = type;\n                hasCustomHooks = typeof getCustomHooks === 'function';\n                setSignature(type, key, forceReset, getCustomHooks); // The next call we expect is from inside a function, to fill in the custom Hooks.\n\n                status = 'needsCustomHooks';\n              }\n              break;\n            case 'needsCustomHooks':\n              if (hasCustomHooks) {\n                collectCustomHooksForSignature(savedType);\n              }\n              status = 'resolved';\n              break;\n          }\n          return type;\n        };\n      }\n    }\n    function isLikelyComponentType(type) {\n      {\n        switch (typeof type) {\n          case 'function':\n            {\n              // First, deal with classes.\n              if (type.prototype != null) {\n                if (type.prototype.isReactComponent) {\n                  // React class.\n                  return true;\n                }\n                var ownNames = Object.getOwnPropertyNames(type.prototype);\n                if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n                  // This looks like a class.\n                  return false;\n                } // eslint-disable-next-line no-proto\n\n                if (type.prototype.__proto__ !== Object.prototype) {\n                  // It has a superclass.\n                  return false;\n                } // Pass through.\n                // This looks like a regular function with empty prototype.\n              } // For plain functions and arrows, use name as a heuristic.\n\n              var name = type.name || type.displayName;\n              return typeof name === 'string' && /^[A-Z]/.test(name);\n            }\n          case 'object':\n            {\n              if (type != null) {\n                switch (type.$$typeof) {\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_MEMO_TYPE:\n                    // Definitely React components.\n                    return true;\n                  default:\n                    return false;\n                }\n              }\n              return false;\n            }\n          default:\n            {\n              return false;\n            }\n        }\n      }\n    }\n    exports._getMountedRootCount = _getMountedRootCount;\n    exports.collectCustomHooksForSignature = collectCustomHooksForSignature;\n    exports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\n    exports.findAffectedHostInstances = findAffectedHostInstances;\n    exports.getFamilyByID = getFamilyByID;\n    exports.getFamilyByType = getFamilyByType;\n    exports.hasUnrecoverableErrors = hasUnrecoverableErrors;\n    exports.injectIntoGlobalHook = injectIntoGlobalHook;\n    exports.isLikelyComponentType = isLikelyComponentType;\n    exports.performReactRefresh = performReactRefresh;\n    exports.register = register;\n    exports.setSignature = setSignature;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_BLOCK_TYPE", "REACT_SERVER_BLOCK_TYPE", "REACT_FUNDAMENTAL_TYPE", "REACT_RESPONDER_TYPE", "REACT_SCOPE_TYPE", "REACT_OPAQUE_ID_TYPE", "REACT_DEBUG_TRACING_MODE_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_LEGACY_HIDDEN_TYPE", "Symbol", "for", "symbolFor", "PossiblyWeakMap", "WeakMap", "Map", "allFamiliesByID", "allFamiliesByType", "allSignaturesByType", "updatedFamiliesByType", "pendingUpdates", "helpersByRendererID", "helpersByRoot", "mountedRoots", "Set", "failedRoots", "rootElements", "isPerformingRefresh", "computeFullKey", "signature", "<PERSON><PERSON><PERSON>", "own<PERSON>ey", "hooks", "getCustomHooks", "err", "forceReset", "i", "length", "hook", "nestedHookSignature", "get", "undefined", "nested<PERSON><PERSON><PERSON><PERSON>", "haveEqualSignatures", "prevType", "nextType", "prevSignature", "nextSignature", "isReactClass", "type", "prototype", "isReactComponent", "canPreserveStateBetween", "resolveFamily", "cloneMap", "map", "clone", "for<PERSON>ach", "value", "key", "set", "cloneSet", "add", "performReactRefresh", "staleFamilies", "updatedFamilies", "updates", "_ref", "family", "current", "update", "helpers", "setRefreshHandler", "<PERSON><PERSON><PERSON><PERSON>", "firstError", "failedRootsSnapshot", "mountedRootsSnapshot", "helpersByRootSnapshot", "root", "Error", "has", "element", "scheduleRoot", "scheduleRefresh", "register", "id", "push", "$$typeof", "render", "setSignature", "arguments", "collectCustomHooksForSignature", "getFamilyByID", "getFamilyByType", "findAffectedHostInstances", "families", "affectedInstances", "instancesForRoot", "findHostInstancesForRefresh", "inst", "injectIntoGlobalHook", "globalObject", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "nextID", "renderers", "supportsFiber", "inject", "injected", "onScheduleFiberRoot", "children", "onCommitFiberRoot", "maybePriorityLevel", "onCommitFiberUnmount", "oldInject", "apply", "oldOnCommitFiberRoot", "oldOnScheduleFiberRoot", "delete", "alternate", "wasMounted", "memoizedState", "isMounted", "hasUnrecoverableErrors", "_getMountedRootCount", "size", "createSignatureFunctionForTransform", "status", "savedType", "hasCustomHooks", "isLikelyComponentType", "ownNames", "Object", "getOwnPropertyNames", "__proto__", "name", "displayName", "test", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-refresh/cjs/react-refresh-runtime.development.js"], "sourcesContent": ["/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_RESPONDER_TYPE = 0xead6;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_RESPONDER_TYPE = symbolFor('react.responder');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\nvar PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n// It's OK to reference families, but use WeakMap/Set for types.\n\nvar allFamiliesByID = new Map();\nvar allFamiliesByType = new PossiblyWeakMap();\nvar allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n// that have actually been edited here. This keeps checks fast.\n// $FlowIssue\n\nvar updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n// It is an array of [Family, NextType] tuples.\n\nvar pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\nvar helpersByRendererID = new Map();\nvar helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\nvar mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\nvar failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n// It needs to be weak because we do this even for roots that failed to mount.\n// If there is no WeakMap, we won't attempt to do retrying.\n// $FlowIssue\n\nvar rootElements = // $FlowIssue\ntypeof WeakMap === 'function' ? new WeakMap() : null;\nvar isPerformingRefresh = false;\n\nfunction computeFullKey(signature) {\n  if (signature.fullKey !== null) {\n    return signature.fullKey;\n  }\n\n  var fullKey = signature.ownKey;\n  var hooks;\n\n  try {\n    hooks = signature.getCustomHooks();\n  } catch (err) {\n    // This can happen in an edge case, e.g. if expression like Foo.useSomething\n    // depends on Foo which is lazily initialized during rendering.\n    // In that case just assume we'll have to remount.\n    signature.forceReset = true;\n    signature.fullKey = fullKey;\n    return fullKey;\n  }\n\n  for (var i = 0; i < hooks.length; i++) {\n    var hook = hooks[i];\n\n    if (typeof hook !== 'function') {\n      // Something's wrong. Assume we need to remount.\n      signature.forceReset = true;\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n\n    var nestedHookSignature = allSignaturesByType.get(hook);\n\n    if (nestedHookSignature === undefined) {\n      // No signature means Hook wasn't in the source code, e.g. in a library.\n      // We'll skip it because we can assume it won't change during this session.\n      continue;\n    }\n\n    var nestedHookKey = computeFullKey(nestedHookSignature);\n\n    if (nestedHookSignature.forceReset) {\n      signature.forceReset = true;\n    }\n\n    fullKey += '\\n---\\n' + nestedHookKey;\n  }\n\n  signature.fullKey = fullKey;\n  return fullKey;\n}\n\nfunction haveEqualSignatures(prevType, nextType) {\n  var prevSignature = allSignaturesByType.get(prevType);\n  var nextSignature = allSignaturesByType.get(nextType);\n\n  if (prevSignature === undefined && nextSignature === undefined) {\n    return true;\n  }\n\n  if (prevSignature === undefined || nextSignature === undefined) {\n    return false;\n  }\n\n  if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n    return false;\n  }\n\n  if (nextSignature.forceReset) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction isReactClass(type) {\n  return type.prototype && type.prototype.isReactComponent;\n}\n\nfunction canPreserveStateBetween(prevType, nextType) {\n  if (isReactClass(prevType) || isReactClass(nextType)) {\n    return false;\n  }\n\n  if (haveEqualSignatures(prevType, nextType)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction resolveFamily(type) {\n  // Only check updated types to keep lookups fast.\n  return updatedFamiliesByType.get(type);\n} // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n\nfunction cloneMap(map) {\n  var clone = new Map();\n  map.forEach(function (value, key) {\n    clone.set(key, value);\n  });\n  return clone;\n}\n\nfunction cloneSet(set) {\n  var clone = new Set();\n  set.forEach(function (value) {\n    clone.add(value);\n  });\n  return clone;\n}\n\nfunction performReactRefresh() {\n\n  if (pendingUpdates.length === 0) {\n    return null;\n  }\n\n  if (isPerformingRefresh) {\n    return null;\n  }\n\n  isPerformingRefresh = true;\n\n  try {\n    var staleFamilies = new Set();\n    var updatedFamilies = new Set();\n    var updates = pendingUpdates;\n    pendingUpdates = [];\n    updates.forEach(function (_ref) {\n      var family = _ref[0],\n          nextType = _ref[1];\n      // Now that we got a real edit, we can create associations\n      // that will be read by the React reconciler.\n      var prevType = family.current;\n      updatedFamiliesByType.set(prevType, family);\n      updatedFamiliesByType.set(nextType, family);\n      family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n      if (canPreserveStateBetween(prevType, nextType)) {\n        updatedFamilies.add(family);\n      } else {\n        staleFamilies.add(family);\n      }\n    }); // TODO: rename these fields to something more meaningful.\n\n    var update = {\n      updatedFamilies: updatedFamilies,\n      // Families that will re-render preserving state\n      staleFamilies: staleFamilies // Families that will be remounted\n\n    };\n    helpersByRendererID.forEach(function (helpers) {\n      // Even if there are no roots, set the handler on first update.\n      // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n      helpers.setRefreshHandler(resolveFamily);\n    });\n    var didError = false;\n    var firstError = null; // We snapshot maps and sets that are mutated during commits.\n    // If we don't do this, there is a risk they will be mutated while\n    // we iterate over them. For example, trying to recover a failed root\n    // may cause another root to be added to the failed list -- an infinite loop.\n\n    var failedRootsSnapshot = cloneSet(failedRoots);\n    var mountedRootsSnapshot = cloneSet(mountedRoots);\n    var helpersByRootSnapshot = cloneMap(helpersByRoot);\n    failedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!failedRoots.has(root)) {// No longer failed.\n      }\n\n      if (rootElements === null) {\n        return;\n      }\n\n      if (!rootElements.has(root)) {\n        return;\n      }\n\n      var element = rootElements.get(root);\n\n      try {\n        helpers.scheduleRoot(root, element);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n    mountedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!mountedRoots.has(root)) {// No longer mounted.\n      }\n\n      try {\n        helpers.scheduleRefresh(root, update);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n\n    if (didError) {\n      throw firstError;\n    }\n\n    return update;\n  } finally {\n    isPerformingRefresh = false;\n  }\n}\nfunction register(type, id) {\n  {\n    if (type === null) {\n      return;\n    }\n\n    if (typeof type !== 'function' && typeof type !== 'object') {\n      return;\n    } // This can happen in an edge case, e.g. if we register\n    // return value of a HOC but it returns a cached component.\n    // Ignore anything but the first registration for each type.\n\n\n    if (allFamiliesByType.has(type)) {\n      return;\n    } // Create family or remember to update it.\n    // None of this bookkeeping affects reconciliation\n    // until the first performReactRefresh() call above.\n\n\n    var family = allFamiliesByID.get(id);\n\n    if (family === undefined) {\n      family = {\n        current: type\n      };\n      allFamiliesByID.set(id, family);\n    } else {\n      pendingUpdates.push([family, type]);\n    }\n\n    allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n    if (typeof type === 'object' && type !== null) {\n      switch (type.$$typeof) {\n        case REACT_FORWARD_REF_TYPE:\n          register(type.render, id + '$render');\n          break;\n\n        case REACT_MEMO_TYPE:\n          register(type.type, id + '$type');\n          break;\n      }\n    }\n  }\n}\nfunction setSignature(type, key) {\n  var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n\n  {\n    allSignaturesByType.set(type, {\n      forceReset: forceReset,\n      ownKey: key,\n      fullKey: null,\n      getCustomHooks: getCustomHooks || function () {\n        return [];\n      }\n    });\n  }\n} // This is lazily called during first render for a type.\n// It captures Hook list at that time so inline requires don't break comparisons.\n\nfunction collectCustomHooksForSignature(type) {\n  {\n    var signature = allSignaturesByType.get(type);\n\n    if (signature !== undefined) {\n      computeFullKey(signature);\n    }\n  }\n}\nfunction getFamilyByID(id) {\n  {\n    return allFamiliesByID.get(id);\n  }\n}\nfunction getFamilyByType(type) {\n  {\n    return allFamiliesByType.get(type);\n  }\n}\nfunction findAffectedHostInstances(families) {\n  {\n    var affectedInstances = new Set();\n    mountedRoots.forEach(function (root) {\n      var helpers = helpersByRoot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n      instancesForRoot.forEach(function (inst) {\n        affectedInstances.add(inst);\n      });\n    });\n    return affectedInstances;\n  }\n}\nfunction injectIntoGlobalHook(globalObject) {\n  {\n    // For React Native, the global hook will be set up by require('react-devtools-core').\n    // That code will run before us. So we need to monkeypatch functions on existing hook.\n    // For React Web, the global hook will be set up by the extension.\n    // This will also run before us.\n    var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n\n    if (hook === undefined) {\n      // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n      // Note that in this case it's important that renderer code runs *after* this method call.\n      // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n      var nextID = 0;\n      globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n        renderers: new Map(),\n        supportsFiber: true,\n        inject: function (injected) {\n          return nextID++;\n        },\n        onScheduleFiberRoot: function (id, root, children) {},\n        onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n        onCommitFiberUnmount: function () {}\n      };\n    } // Here, we just want to get a reference to scheduleRefresh.\n\n\n    var oldInject = hook.inject;\n\n    hook.inject = function (injected) {\n      var id = oldInject.apply(this, arguments);\n\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n\n      return id;\n    }; // Do the same for any already injected roots.\n    // This is useful if ReactDOM has already been initialized.\n    // https://github.com/facebook/react/issues/17626\n\n\n    hook.renderers.forEach(function (injected, id) {\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n    }); // We also want to track currently mounted roots.\n\n    var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n\n    var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n\n    hook.onScheduleFiberRoot = function (id, root, children) {\n      if (!isPerformingRefresh) {\n        // If it was intentionally scheduled, don't attempt to restore.\n        // This includes intentionally scheduled unmounts.\n        failedRoots.delete(root);\n\n        if (rootElements !== null) {\n          rootElements.set(root, children);\n        }\n      }\n\n      return oldOnScheduleFiberRoot.apply(this, arguments);\n    };\n\n    hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n      var helpers = helpersByRendererID.get(id);\n\n      if (helpers === undefined) {\n        return;\n      }\n\n      helpersByRoot.set(root, helpers);\n      var current = root.current;\n      var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n      // This logic is copy-pasted from similar logic in the DevTools backend.\n      // If this breaks with some refactoring, you'll want to update DevTools too.\n\n      if (alternate !== null) {\n        var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n        var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n\n        if (!wasMounted && isMounted) {\n          // Mount a new root.\n          mountedRoots.add(root);\n          failedRoots.delete(root);\n        } else if (wasMounted && isMounted) ; else if (wasMounted && !isMounted) {\n          // Unmount an existing root.\n          mountedRoots.delete(root);\n\n          if (didError) {\n            // We'll remount it on future edits.\n            failedRoots.add(root);\n          } else {\n            helpersByRoot.delete(root);\n          }\n        } else if (!wasMounted && !isMounted) {\n          if (didError) {\n            // We'll remount it on future edits.\n            failedRoots.add(root);\n          }\n        }\n      } else {\n        // Mount a new root.\n        mountedRoots.add(root);\n      }\n\n      return oldOnCommitFiberRoot.apply(this, arguments);\n    };\n  }\n}\nfunction hasUnrecoverableErrors() {\n  // TODO: delete this after removing dependency in RN.\n  return false;\n} // Exposed for testing.\n\nfunction _getMountedRootCount() {\n  {\n    return mountedRoots.size;\n  }\n} // This is a wrapper over more primitive functions for setting signature.\n// Signatures let us decide whether the Hook order has changed on refresh.\n//\n// This function is intended to be used as a transform target, e.g.:\n// var _s = createSignatureFunctionForTransform()\n//\n// function Hello() {\n//   const [foo, setFoo] = useState(0);\n//   const value = useCustomHook();\n//   _s(); /* Second call triggers collecting the custom Hook list.\n//          * This doesn't happen during the module evaluation because we\n//          * don't want to change the module order with inline requires.\n//          * Next calls are noops. */\n//   return <h1>Hi</h1>;\n// }\n//\n// /* First call specifies the signature: */\n// _s(\n//   Hello,\n//   'useState{[foo, setFoo]}(0)',\n//   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n// );\n\nfunction createSignatureFunctionForTransform() {\n  {\n    // We'll fill in the signature in two steps.\n    // First, we'll know the signature itself. This happens outside the component.\n    // Then, we'll know the references to custom Hooks. This happens inside the component.\n    // After that, the returned function will be a fast path no-op.\n    var status = 'needsSignature';\n    var savedType;\n    var hasCustomHooks;\n    return function (type, key, forceReset, getCustomHooks) {\n      switch (status) {\n        case 'needsSignature':\n          if (type !== undefined) {\n            // If we received an argument, this is the initial registration call.\n            savedType = type;\n            hasCustomHooks = typeof getCustomHooks === 'function';\n            setSignature(type, key, forceReset, getCustomHooks); // The next call we expect is from inside a function, to fill in the custom Hooks.\n\n            status = 'needsCustomHooks';\n          }\n\n          break;\n\n        case 'needsCustomHooks':\n          if (hasCustomHooks) {\n            collectCustomHooksForSignature(savedType);\n          }\n\n          status = 'resolved';\n          break;\n      }\n\n      return type;\n    };\n  }\n}\nfunction isLikelyComponentType(type) {\n  {\n    switch (typeof type) {\n      case 'function':\n        {\n          // First, deal with classes.\n          if (type.prototype != null) {\n            if (type.prototype.isReactComponent) {\n              // React class.\n              return true;\n            }\n\n            var ownNames = Object.getOwnPropertyNames(type.prototype);\n\n            if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n              // This looks like a class.\n              return false;\n            } // eslint-disable-next-line no-proto\n\n\n            if (type.prototype.__proto__ !== Object.prototype) {\n              // It has a superclass.\n              return false;\n            } // Pass through.\n            // This looks like a regular function with empty prototype.\n\n          } // For plain functions and arrows, use name as a heuristic.\n\n\n          var name = type.name || type.displayName;\n          return typeof name === 'string' && /^[A-Z]/.test(name);\n        }\n\n      case 'object':\n        {\n          if (type != null) {\n            switch (type.$$typeof) {\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_MEMO_TYPE:\n                // Definitely React components.\n                return true;\n\n              default:\n                return false;\n            }\n          }\n\n          return false;\n        }\n\n      default:\n        {\n          return false;\n        }\n    }\n  }\n}\n\nexports._getMountedRootCount = _getMountedRootCount;\nexports.collectCustomHooksForSignature = collectCustomHooksForSignature;\nexports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\nexports.findAffectedHostInstances = findAffectedHostInstances;\nexports.getFamilyByID = getFamilyByID;\nexports.getFamilyByType = getFamilyByType;\nexports.hasUnrecoverableErrors = hasUnrecoverableErrors;\nexports.injectIntoGlobalHook = injectIntoGlobalHook;\nexports.isLikelyComponentType = isLikelyComponentType;\nexports.performReactRefresh = performReactRefresh;\nexports.register = register;\nexports.setSignature = setSignature;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAIZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ;IACA;IACA;IACA;IACA;IACA,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,iBAAiB,GAAG,MAAM;IAC9B,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,wBAAwB,GAAG,MAAM;IACrC,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,uBAAuB,GAAG,MAAM;IACpC,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,6BAA6B,GAAG,MAAM;IAC1C,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,wBAAwB,GAAG,MAAM;IAErC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;MAC9C,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAG;MAC1BtB,kBAAkB,GAAGuB,SAAS,CAAC,eAAe,CAAC;MAC/CtB,iBAAiB,GAAGsB,SAAS,CAAC,cAAc,CAAC;MAC7CrB,mBAAmB,GAAGqB,SAAS,CAAC,gBAAgB,CAAC;MACjDpB,sBAAsB,GAAGoB,SAAS,CAAC,mBAAmB,CAAC;MACvDnB,mBAAmB,GAAGmB,SAAS,CAAC,gBAAgB,CAAC;MACjDlB,mBAAmB,GAAGkB,SAAS,CAAC,gBAAgB,CAAC;MACjDjB,kBAAkB,GAAGiB,SAAS,CAAC,eAAe,CAAC;MAC/ChB,sBAAsB,GAAGgB,SAAS,CAAC,mBAAmB,CAAC;MACvDf,mBAAmB,GAAGe,SAAS,CAAC,gBAAgB,CAAC;MACjDd,wBAAwB,GAAGc,SAAS,CAAC,qBAAqB,CAAC;MAC3Db,eAAe,GAAGa,SAAS,CAAC,YAAY,CAAC;MACzCZ,eAAe,GAAGY,SAAS,CAAC,YAAY,CAAC;MACzCX,gBAAgB,GAAGW,SAAS,CAAC,aAAa,CAAC;MAC3CV,uBAAuB,GAAGU,SAAS,CAAC,oBAAoB,CAAC;MACzDT,sBAAsB,GAAGS,SAAS,CAAC,mBAAmB,CAAC;MACvDR,oBAAoB,GAAGQ,SAAS,CAAC,iBAAiB,CAAC;MACnDP,gBAAgB,GAAGO,SAAS,CAAC,aAAa,CAAC;MAC3CN,oBAAoB,GAAGM,SAAS,CAAC,iBAAiB,CAAC;MACnDL,6BAA6B,GAAGK,SAAS,CAAC,wBAAwB,CAAC;MACnEJ,oBAAoB,GAAGI,SAAS,CAAC,iBAAiB,CAAC;MACnDH,wBAAwB,GAAGG,SAAS,CAAC,qBAAqB,CAAC;IAC7D;IAEA,IAAIC,eAAe,GAAG,OAAOC,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,GAAG,CAAC,CAAC;IACrE;;IAEA,IAAIC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/B,IAAIE,iBAAiB,GAAG,IAAIJ,eAAe,CAAC,CAAC;IAC7C,IAAIK,mBAAmB,GAAG,IAAIL,eAAe,CAAC,CAAC,CAAC,CAAC;IACjD;IACA;;IAEA,IAAIM,qBAAqB,GAAG,IAAIN,eAAe,CAAC,CAAC,CAAC,CAAC;IACnD;;IAEA,IAAIO,cAAc,GAAG,EAAE,CAAC,CAAC;;IAEzB,IAAIC,mBAAmB,GAAG,IAAIN,GAAG,CAAC,CAAC;IACnC,IAAIO,aAAa,GAAG,IAAIP,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE/B,IAAIQ,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9B,IAAIC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA;IACA;;IAEA,IAAIE,YAAY;IAAG;IACnB,OAAOZ,OAAO,KAAK,UAAU,GAAG,IAAIA,OAAO,CAAC,CAAC,GAAG,IAAI;IACpD,IAAIa,mBAAmB,GAAG,KAAK;IAE/B,SAASC,cAAcA,CAACC,SAAS,EAAE;MACjC,IAAIA,SAAS,CAACC,OAAO,KAAK,IAAI,EAAE;QAC9B,OAAOD,SAAS,CAACC,OAAO;MAC1B;MAEA,IAAIA,OAAO,GAAGD,SAAS,CAACE,MAAM;MAC9B,IAAIC,KAAK;MAET,IAAI;QACFA,KAAK,GAAGH,SAAS,CAACI,cAAc,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ;QACA;QACA;QACAL,SAAS,CAACM,UAAU,GAAG,IAAI;QAC3BN,SAAS,CAACC,OAAO,GAAGA,OAAO;QAC3B,OAAOA,OAAO;MAChB;MAEA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIE,IAAI,GAAGN,KAAK,CAACI,CAAC,CAAC;QAEnB,IAAI,OAAOE,IAAI,KAAK,UAAU,EAAE;UAC9B;UACAT,SAAS,CAACM,UAAU,GAAG,IAAI;UAC3BN,SAAS,CAACC,OAAO,GAAGA,OAAO;UAC3B,OAAOA,OAAO;QAChB;QAEA,IAAIS,mBAAmB,GAAGrB,mBAAmB,CAACsB,GAAG,CAACF,IAAI,CAAC;QAEvD,IAAIC,mBAAmB,KAAKE,SAAS,EAAE;UACrC;UACA;UACA;QACF;QAEA,IAAIC,aAAa,GAAGd,cAAc,CAACW,mBAAmB,CAAC;QAEvD,IAAIA,mBAAmB,CAACJ,UAAU,EAAE;UAClCN,SAAS,CAACM,UAAU,GAAG,IAAI;QAC7B;QAEAL,OAAO,IAAI,SAAS,GAAGY,aAAa;MACtC;MAEAb,SAAS,CAACC,OAAO,GAAGA,OAAO;MAC3B,OAAOA,OAAO;IAChB;IAEA,SAASa,mBAAmBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;MAC/C,IAAIC,aAAa,GAAG5B,mBAAmB,CAACsB,GAAG,CAACI,QAAQ,CAAC;MACrD,IAAIG,aAAa,GAAG7B,mBAAmB,CAACsB,GAAG,CAACK,QAAQ,CAAC;MAErD,IAAIC,aAAa,KAAKL,SAAS,IAAIM,aAAa,KAAKN,SAAS,EAAE;QAC9D,OAAO,IAAI;MACb;MAEA,IAAIK,aAAa,KAAKL,SAAS,IAAIM,aAAa,KAAKN,SAAS,EAAE;QAC9D,OAAO,KAAK;MACd;MAEA,IAAIb,cAAc,CAACkB,aAAa,CAAC,KAAKlB,cAAc,CAACmB,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MAEA,IAAIA,aAAa,CAACZ,UAAU,EAAE;QAC5B,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb;IAEA,SAASa,YAAYA,CAACC,IAAI,EAAE;MAC1B,OAAOA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACC,gBAAgB;IAC1D;IAEA,SAASC,uBAAuBA,CAACR,QAAQ,EAAEC,QAAQ,EAAE;MACnD,IAAIG,YAAY,CAACJ,QAAQ,CAAC,IAAII,YAAY,CAACH,QAAQ,CAAC,EAAE;QACpD,OAAO,KAAK;MACd;MAEA,IAAIF,mBAAmB,CAACC,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC3C,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;IAEA,SAASQ,aAAaA,CAACJ,IAAI,EAAE;MAC3B;MACA,OAAO9B,qBAAqB,CAACqB,GAAG,CAACS,IAAI,CAAC;IACxC,CAAC,CAAC;;IAGF,SAASK,QAAQA,CAACC,GAAG,EAAE;MACrB,IAAIC,KAAK,GAAG,IAAIzC,GAAG,CAAC,CAAC;MACrBwC,GAAG,CAACE,OAAO,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;QAChCH,KAAK,CAACI,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC;MACvB,CAAC,CAAC;MACF,OAAOF,KAAK;IACd;IAEA,SAASK,QAAQA,CAACD,GAAG,EAAE;MACrB,IAAIJ,KAAK,GAAG,IAAIhC,GAAG,CAAC,CAAC;MACrBoC,GAAG,CAACH,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC3BF,KAAK,CAACM,GAAG,CAACJ,KAAK,CAAC;MAClB,CAAC,CAAC;MACF,OAAOF,KAAK;IACd;IAEA,SAASO,mBAAmBA,CAAA,EAAG;MAE7B,IAAI3C,cAAc,CAACiB,MAAM,KAAK,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;MAEA,IAAIV,mBAAmB,EAAE;QACvB,OAAO,IAAI;MACb;MAEAA,mBAAmB,GAAG,IAAI;MAE1B,IAAI;QACF,IAAIqC,aAAa,GAAG,IAAIxC,GAAG,CAAC,CAAC;QAC7B,IAAIyC,eAAe,GAAG,IAAIzC,GAAG,CAAC,CAAC;QAC/B,IAAI0C,OAAO,GAAG9C,cAAc;QAC5BA,cAAc,GAAG,EAAE;QACnB8C,OAAO,CAACT,OAAO,CAAC,UAAUU,IAAI,EAAE;UAC9B,IAAIC,MAAM,GAAGD,IAAI,CAAC,CAAC,CAAC;YAChBtB,QAAQ,GAAGsB,IAAI,CAAC,CAAC,CAAC;UACtB;UACA;UACA,IAAIvB,QAAQ,GAAGwB,MAAM,CAACC,OAAO;UAC7BlD,qBAAqB,CAACyC,GAAG,CAAChB,QAAQ,EAAEwB,MAAM,CAAC;UAC3CjD,qBAAqB,CAACyC,GAAG,CAACf,QAAQ,EAAEuB,MAAM,CAAC;UAC3CA,MAAM,CAACC,OAAO,GAAGxB,QAAQ,CAAC,CAAC;;UAE3B,IAAIO,uBAAuB,CAACR,QAAQ,EAAEC,QAAQ,CAAC,EAAE;YAC/CoB,eAAe,CAACH,GAAG,CAACM,MAAM,CAAC;UAC7B,CAAC,MAAM;YACLJ,aAAa,CAACF,GAAG,CAACM,MAAM,CAAC;UAC3B;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIE,MAAM,GAAG;UACXL,eAAe,EAAEA,eAAe;UAChC;UACAD,aAAa,EAAEA,aAAa,CAAC;QAE/B,CAAC;QACD3C,mBAAmB,CAACoC,OAAO,CAAC,UAAUc,OAAO,EAAE;UAC7C;UACA;UACAA,OAAO,CAACC,iBAAiB,CAACnB,aAAa,CAAC;QAC1C,CAAC,CAAC;QACF,IAAIoB,QAAQ,GAAG,KAAK;QACpB,IAAIC,UAAU,GAAG,IAAI,CAAC,CAAC;QACvB;QACA;QACA;;QAEA,IAAIC,mBAAmB,GAAGd,QAAQ,CAACpC,WAAW,CAAC;QAC/C,IAAImD,oBAAoB,GAAGf,QAAQ,CAACtC,YAAY,CAAC;QACjD,IAAIsD,qBAAqB,GAAGvB,QAAQ,CAAChC,aAAa,CAAC;QACnDqD,mBAAmB,CAAClB,OAAO,CAAC,UAAUqB,IAAI,EAAE;UAC1C,IAAIP,OAAO,GAAGM,qBAAqB,CAACrC,GAAG,CAACsC,IAAI,CAAC;UAE7C,IAAIP,OAAO,KAAK9B,SAAS,EAAE;YACzB,MAAM,IAAIsC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAI,CAACtD,WAAW,CAACuD,GAAG,CAACF,IAAI,CAAC,EAAE,CAAC;UAAA;UAG7B,IAAIpD,YAAY,KAAK,IAAI,EAAE;YACzB;UACF;UAEA,IAAI,CAACA,YAAY,CAACsD,GAAG,CAACF,IAAI,CAAC,EAAE;YAC3B;UACF;UAEA,IAAIG,OAAO,GAAGvD,YAAY,CAACc,GAAG,CAACsC,IAAI,CAAC;UAEpC,IAAI;YACFP,OAAO,CAACW,YAAY,CAACJ,IAAI,EAAEG,OAAO,CAAC;UACrC,CAAC,CAAC,OAAO/C,GAAG,EAAE;YACZ,IAAI,CAACuC,QAAQ,EAAE;cACbA,QAAQ,GAAG,IAAI;cACfC,UAAU,GAAGxC,GAAG;YAClB,CAAC,CAAC;UAEJ;QACF,CAAC,CAAC;QACF0C,oBAAoB,CAACnB,OAAO,CAAC,UAAUqB,IAAI,EAAE;UAC3C,IAAIP,OAAO,GAAGM,qBAAqB,CAACrC,GAAG,CAACsC,IAAI,CAAC;UAE7C,IAAIP,OAAO,KAAK9B,SAAS,EAAE;YACzB,MAAM,IAAIsC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAI,CAACxD,YAAY,CAACyD,GAAG,CAACF,IAAI,CAAC,EAAE,CAAC;UAAA;UAG9B,IAAI;YACFP,OAAO,CAACY,eAAe,CAACL,IAAI,EAAER,MAAM,CAAC;UACvC,CAAC,CAAC,OAAOpC,GAAG,EAAE;YACZ,IAAI,CAACuC,QAAQ,EAAE;cACbA,QAAQ,GAAG,IAAI;cACfC,UAAU,GAAGxC,GAAG;YAClB,CAAC,CAAC;UAEJ;QACF,CAAC,CAAC;QAEF,IAAIuC,QAAQ,EAAE;UACZ,MAAMC,UAAU;QAClB;QAEA,OAAOJ,MAAM;MACf,CAAC,SAAS;QACR3C,mBAAmB,GAAG,KAAK;MAC7B;IACF;IACA,SAASyD,QAAQA,CAACnC,IAAI,EAAEoC,EAAE,EAAE;MAC1B;QACE,IAAIpC,IAAI,KAAK,IAAI,EAAE;UACjB;QACF;QAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1D;QACF,CAAC,CAAC;QACF;QACA;;QAGA,IAAIhC,iBAAiB,CAAC+D,GAAG,CAAC/B,IAAI,CAAC,EAAE;UAC/B;QACF,CAAC,CAAC;QACF;QACA;;QAGA,IAAImB,MAAM,GAAGpD,eAAe,CAACwB,GAAG,CAAC6C,EAAE,CAAC;QAEpC,IAAIjB,MAAM,KAAK3B,SAAS,EAAE;UACxB2B,MAAM,GAAG;YACPC,OAAO,EAAEpB;UACX,CAAC;UACDjC,eAAe,CAAC4C,GAAG,CAACyB,EAAE,EAAEjB,MAAM,CAAC;QACjC,CAAC,MAAM;UACLhD,cAAc,CAACkE,IAAI,CAAC,CAAClB,MAAM,EAAEnB,IAAI,CAAC,CAAC;QACrC;QAEAhC,iBAAiB,CAAC2C,GAAG,CAACX,IAAI,EAAEmB,MAAM,CAAC,CAAC,CAAC;;QAErC,IAAI,OAAOnB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;UAC7C,QAAQA,IAAI,CAACsC,QAAQ;YACnB,KAAK3F,sBAAsB;cACzBwF,QAAQ,CAACnC,IAAI,CAACuC,MAAM,EAAEH,EAAE,GAAG,SAAS,CAAC;cACrC;YAEF,KAAKtF,eAAe;cAClBqF,QAAQ,CAACnC,IAAI,CAACA,IAAI,EAAEoC,EAAE,GAAG,OAAO,CAAC;cACjC;UACJ;QACF;MACF;IACF;IACA,SAASI,YAAYA,CAACxC,IAAI,EAAEU,GAAG,EAAE;MAC/B,IAAIxB,UAAU,GAAGuD,SAAS,CAACrD,MAAM,GAAG,CAAC,IAAIqD,SAAS,CAAC,CAAC,CAAC,KAAKjD,SAAS,GAAGiD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC1F,IAAIzD,cAAc,GAAGyD,SAAS,CAACrD,MAAM,GAAG,CAAC,GAAGqD,SAAS,CAAC,CAAC,CAAC,GAAGjD,SAAS;MAEpE;QACEvB,mBAAmB,CAAC0C,GAAG,CAACX,IAAI,EAAE;UAC5Bd,UAAU,EAAEA,UAAU;UACtBJ,MAAM,EAAE4B,GAAG;UACX7B,OAAO,EAAE,IAAI;UACbG,cAAc,EAAEA,cAAc,IAAI,YAAY;YAC5C,OAAO,EAAE;UACX;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF;;IAEA,SAAS0D,8BAA8BA,CAAC1C,IAAI,EAAE;MAC5C;QACE,IAAIpB,SAAS,GAAGX,mBAAmB,CAACsB,GAAG,CAACS,IAAI,CAAC;QAE7C,IAAIpB,SAAS,KAAKY,SAAS,EAAE;UAC3Bb,cAAc,CAACC,SAAS,CAAC;QAC3B;MACF;IACF;IACA,SAAS+D,aAAaA,CAACP,EAAE,EAAE;MACzB;QACE,OAAOrE,eAAe,CAACwB,GAAG,CAAC6C,EAAE,CAAC;MAChC;IACF;IACA,SAASQ,eAAeA,CAAC5C,IAAI,EAAE;MAC7B;QACE,OAAOhC,iBAAiB,CAACuB,GAAG,CAACS,IAAI,CAAC;MACpC;IACF;IACA,SAAS6C,yBAAyBA,CAACC,QAAQ,EAAE;MAC3C;QACE,IAAIC,iBAAiB,GAAG,IAAIxE,GAAG,CAAC,CAAC;QACjCD,YAAY,CAACkC,OAAO,CAAC,UAAUqB,IAAI,EAAE;UACnC,IAAIP,OAAO,GAAGjD,aAAa,CAACkB,GAAG,CAACsC,IAAI,CAAC;UAErC,IAAIP,OAAO,KAAK9B,SAAS,EAAE;YACzB,MAAM,IAAIsC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAIkB,gBAAgB,GAAG1B,OAAO,CAAC2B,2BAA2B,CAACpB,IAAI,EAAEiB,QAAQ,CAAC;UAC1EE,gBAAgB,CAACxC,OAAO,CAAC,UAAU0C,IAAI,EAAE;YACvCH,iBAAiB,CAAClC,GAAG,CAACqC,IAAI,CAAC;UAC7B,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAOH,iBAAiB;MAC1B;IACF;IACA,SAASI,oBAAoBA,CAACC,YAAY,EAAE;MAC1C;QACE;QACA;QACA;QACA;QACA,IAAI/D,IAAI,GAAG+D,YAAY,CAACC,8BAA8B;QAEtD,IAAIhE,IAAI,KAAKG,SAAS,EAAE;UACtB;UACA;UACA;UACA,IAAI8D,MAAM,GAAG,CAAC;UACdF,YAAY,CAACC,8BAA8B,GAAGhE,IAAI,GAAG;YACnDkE,SAAS,EAAE,IAAIzF,GAAG,CAAC,CAAC;YACpB0F,aAAa,EAAE,IAAI;YACnBC,MAAM,EAAE,SAAAA,CAAUC,QAAQ,EAAE;cAC1B,OAAOJ,MAAM,EAAE;YACjB,CAAC;YACDK,mBAAmB,EAAE,SAAAA,CAAUvB,EAAE,EAAEP,IAAI,EAAE+B,QAAQ,EAAE,CAAC,CAAC;YACrDC,iBAAiB,EAAE,SAAAA,CAAUzB,EAAE,EAAEP,IAAI,EAAEiC,kBAAkB,EAAEtC,QAAQ,EAAE,CAAC,CAAC;YACvEuC,oBAAoB,EAAE,SAAAA,CAAA,EAAY,CAAC;UACrC,CAAC;QACH,CAAC,CAAC;;QAGF,IAAIC,SAAS,GAAG3E,IAAI,CAACoE,MAAM;QAE3BpE,IAAI,CAACoE,MAAM,GAAG,UAAUC,QAAQ,EAAE;UAChC,IAAItB,EAAE,GAAG4B,SAAS,CAACC,KAAK,CAAC,IAAI,EAAExB,SAAS,CAAC;UAEzC,IAAI,OAAOiB,QAAQ,CAACxB,eAAe,KAAK,UAAU,IAAI,OAAOwB,QAAQ,CAACnC,iBAAiB,KAAK,UAAU,EAAE;YACtG;YACAnD,mBAAmB,CAACuC,GAAG,CAACyB,EAAE,EAAEsB,QAAQ,CAAC;UACvC;UAEA,OAAOtB,EAAE;QACX,CAAC,CAAC,CAAC;QACH;QACA;;QAGA/C,IAAI,CAACkE,SAAS,CAAC/C,OAAO,CAAC,UAAUkD,QAAQ,EAAEtB,EAAE,EAAE;UAC7C,IAAI,OAAOsB,QAAQ,CAACxB,eAAe,KAAK,UAAU,IAAI,OAAOwB,QAAQ,CAACnC,iBAAiB,KAAK,UAAU,EAAE;YACtG;YACAnD,mBAAmB,CAACuC,GAAG,CAACyB,EAAE,EAAEsB,QAAQ,CAAC;UACvC;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIQ,oBAAoB,GAAG7E,IAAI,CAACwE,iBAAiB;QAEjD,IAAIM,sBAAsB,GAAG9E,IAAI,CAACsE,mBAAmB,IAAI,YAAY,CAAC,CAAC;QAEvEtE,IAAI,CAACsE,mBAAmB,GAAG,UAAUvB,EAAE,EAAEP,IAAI,EAAE+B,QAAQ,EAAE;UACvD,IAAI,CAAClF,mBAAmB,EAAE;YACxB;YACA;YACAF,WAAW,CAAC4F,MAAM,CAACvC,IAAI,CAAC;YAExB,IAAIpD,YAAY,KAAK,IAAI,EAAE;cACzBA,YAAY,CAACkC,GAAG,CAACkB,IAAI,EAAE+B,QAAQ,CAAC;YAClC;UACF;UAEA,OAAOO,sBAAsB,CAACF,KAAK,CAAC,IAAI,EAAExB,SAAS,CAAC;QACtD,CAAC;QAEDpD,IAAI,CAACwE,iBAAiB,GAAG,UAAUzB,EAAE,EAAEP,IAAI,EAAEiC,kBAAkB,EAAEtC,QAAQ,EAAE;UACzE,IAAIF,OAAO,GAAGlD,mBAAmB,CAACmB,GAAG,CAAC6C,EAAE,CAAC;UAEzC,IAAId,OAAO,KAAK9B,SAAS,EAAE;YACzB;UACF;UAEAnB,aAAa,CAACsC,GAAG,CAACkB,IAAI,EAAEP,OAAO,CAAC;UAChC,IAAIF,OAAO,GAAGS,IAAI,CAACT,OAAO;UAC1B,IAAIiD,SAAS,GAAGjD,OAAO,CAACiD,SAAS,CAAC,CAAC;UACnC;UACA;;UAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;YACtB,IAAIC,UAAU,GAAGD,SAAS,CAACE,aAAa,IAAI,IAAI,IAAIF,SAAS,CAACE,aAAa,CAACvC,OAAO,IAAI,IAAI;YAC3F,IAAIwC,SAAS,GAAGpD,OAAO,CAACmD,aAAa,IAAI,IAAI,IAAInD,OAAO,CAACmD,aAAa,CAACvC,OAAO,IAAI,IAAI;YAEtF,IAAI,CAACsC,UAAU,IAAIE,SAAS,EAAE;cAC5B;cACAlG,YAAY,CAACuC,GAAG,CAACgB,IAAI,CAAC;cACtBrD,WAAW,CAAC4F,MAAM,CAACvC,IAAI,CAAC;YAC1B,CAAC,MAAM,IAAIyC,UAAU,IAAIE,SAAS,EAAE,CAAC,KAAM,IAAIF,UAAU,IAAI,CAACE,SAAS,EAAE;cACvE;cACAlG,YAAY,CAAC8F,MAAM,CAACvC,IAAI,CAAC;cAEzB,IAAIL,QAAQ,EAAE;gBACZ;gBACAhD,WAAW,CAACqC,GAAG,CAACgB,IAAI,CAAC;cACvB,CAAC,MAAM;gBACLxD,aAAa,CAAC+F,MAAM,CAACvC,IAAI,CAAC;cAC5B;YACF,CAAC,MAAM,IAAI,CAACyC,UAAU,IAAI,CAACE,SAAS,EAAE;cACpC,IAAIhD,QAAQ,EAAE;gBACZ;gBACAhD,WAAW,CAACqC,GAAG,CAACgB,IAAI,CAAC;cACvB;YACF;UACF,CAAC,MAAM;YACL;YACAvD,YAAY,CAACuC,GAAG,CAACgB,IAAI,CAAC;UACxB;UAEA,OAAOqC,oBAAoB,CAACD,KAAK,CAAC,IAAI,EAAExB,SAAS,CAAC;QACpD,CAAC;MACH;IACF;IACA,SAASgC,sBAAsBA,CAAA,EAAG;MAChC;MACA,OAAO,KAAK;IACd,CAAC,CAAC;;IAEF,SAASC,oBAAoBA,CAAA,EAAG;MAC9B;QACE,OAAOpG,YAAY,CAACqG,IAAI;MAC1B;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,SAASC,mCAAmCA,CAAA,EAAG;MAC7C;QACE;QACA;QACA;QACA;QACA,IAAIC,MAAM,GAAG,gBAAgB;QAC7B,IAAIC,SAAS;QACb,IAAIC,cAAc;QAClB,OAAO,UAAU/E,IAAI,EAAEU,GAAG,EAAExB,UAAU,EAAEF,cAAc,EAAE;UACtD,QAAQ6F,MAAM;YACZ,KAAK,gBAAgB;cACnB,IAAI7E,IAAI,KAAKR,SAAS,EAAE;gBACtB;gBACAsF,SAAS,GAAG9E,IAAI;gBAChB+E,cAAc,GAAG,OAAO/F,cAAc,KAAK,UAAU;gBACrDwD,YAAY,CAACxC,IAAI,EAAEU,GAAG,EAAExB,UAAU,EAAEF,cAAc,CAAC,CAAC,CAAC;;gBAErD6F,MAAM,GAAG,kBAAkB;cAC7B;cAEA;YAEF,KAAK,kBAAkB;cACrB,IAAIE,cAAc,EAAE;gBAClBrC,8BAA8B,CAACoC,SAAS,CAAC;cAC3C;cAEAD,MAAM,GAAG,UAAU;cACnB;UACJ;UAEA,OAAO7E,IAAI;QACb,CAAC;MACH;IACF;IACA,SAASgF,qBAAqBA,CAAChF,IAAI,EAAE;MACnC;QACE,QAAQ,OAAOA,IAAI;UACjB,KAAK,UAAU;YACb;cACE;cACA,IAAIA,IAAI,CAACC,SAAS,IAAI,IAAI,EAAE;gBAC1B,IAAID,IAAI,CAACC,SAAS,CAACC,gBAAgB,EAAE;kBACnC;kBACA,OAAO,IAAI;gBACb;gBAEA,IAAI+E,QAAQ,GAAGC,MAAM,CAACC,mBAAmB,CAACnF,IAAI,CAACC,SAAS,CAAC;gBAEzD,IAAIgF,QAAQ,CAAC7F,MAAM,GAAG,CAAC,IAAI6F,QAAQ,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;kBACxD;kBACA,OAAO,KAAK;gBACd,CAAC,CAAC;;gBAGF,IAAIjF,IAAI,CAACC,SAAS,CAACmF,SAAS,KAAKF,MAAM,CAACjF,SAAS,EAAE;kBACjD;kBACA,OAAO,KAAK;gBACd,CAAC,CAAC;gBACF;cAEF,CAAC,CAAC;;cAGF,IAAIoF,IAAI,GAAGrF,IAAI,CAACqF,IAAI,IAAIrF,IAAI,CAACsF,WAAW;cACxC,OAAO,OAAOD,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAACE,IAAI,CAACF,IAAI,CAAC;YACxD;UAEF,KAAK,QAAQ;YACX;cACE,IAAIrF,IAAI,IAAI,IAAI,EAAE;gBAChB,QAAQA,IAAI,CAACsC,QAAQ;kBACnB,KAAK3F,sBAAsB;kBAC3B,KAAKG,eAAe;oBAClB;oBACA,OAAO,IAAI;kBAEb;oBACE,OAAO,KAAK;gBAChB;cACF;cAEA,OAAO,KAAK;YACd;UAEF;YACE;cACE,OAAO,KAAK;YACd;QACJ;MACF;IACF;IAEA0I,OAAO,CAACd,oBAAoB,GAAGA,oBAAoB;IACnDc,OAAO,CAAC9C,8BAA8B,GAAGA,8BAA8B;IACvE8C,OAAO,CAACZ,mCAAmC,GAAGA,mCAAmC;IACjFY,OAAO,CAAC3C,yBAAyB,GAAGA,yBAAyB;IAC7D2C,OAAO,CAAC7C,aAAa,GAAGA,aAAa;IACrC6C,OAAO,CAAC5C,eAAe,GAAGA,eAAe;IACzC4C,OAAO,CAACf,sBAAsB,GAAGA,sBAAsB;IACvDe,OAAO,CAACrC,oBAAoB,GAAGA,oBAAoB;IACnDqC,OAAO,CAACR,qBAAqB,GAAGA,qBAAqB;IACrDQ,OAAO,CAAC1E,mBAAmB,GAAGA,mBAAmB;IACjD0E,OAAO,CAACrD,QAAQ,GAAGA,QAAQ;IAC3BqD,OAAO,CAAChD,YAAY,GAAGA,YAAY;EACjC,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script"}