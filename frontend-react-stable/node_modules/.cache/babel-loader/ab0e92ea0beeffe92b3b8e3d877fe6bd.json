{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _toArray(arr) {\n  return _arrayWithHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport setRafTimeout from './setRafTimeout';\nexport default function createAnimateManager() {\n  var currStyle = {};\n  var handleChange = function handleChange() {\n    return null;\n  };\n  var shouldStop = false;\n  var setStyle = function setStyle(_style) {\n    if (shouldStop) {\n      return;\n    }\n    if (Array.isArray(_style)) {\n      if (!_style.length) {\n        return;\n      }\n      var styles = _style;\n      var _styles = _toArray(styles),\n        curr = _styles[0],\n        restStyles = _styles.slice(1);\n      if (typeof curr === 'number') {\n        setRafTimeout(setStyle.bind(null, restStyles), curr);\n        return;\n      }\n      setStyle(curr);\n      setRafTimeout(setStyle.bind(null, restStyles));\n      return;\n    }\n    if (_typeof(_style) === 'object') {\n      currStyle = _style;\n      handleChange(currStyle);\n    }\n    if (typeof _style === 'function') {\n      _style();\n    }\n  };\n  return {\n    stop: function stop() {\n      shouldStop = true;\n    },\n    start: function start(style) {\n      shouldStop = false;\n      setStyle(style);\n    },\n    subscribe: function subscribe(_handleChange) {\n      handleChange = _handleChange;\n      return function () {\n        handleChange = function handleChange() {\n          return null;\n        };\n      };\n    }\n  };\n}", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_toArray", "arr", "_arrayWithHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "len", "length", "i", "arr2", "iter", "isArray", "setRafTimeout", "createAnimateManager", "currStyle", "handleChange", "shouldStop", "setStyle", "_style", "styles", "_styles", "curr", "restStyles", "bind", "stop", "start", "style", "subscribe", "_handleChange"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-smooth/es6/AnimateManager.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toArray(arr) { return _arrayWithHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport setRafTimeout from './setRafTimeout';\nexport default function createAnimateManager() {\n  var currStyle = {};\n  var handleChange = function handleChange() {\n    return null;\n  };\n  var shouldStop = false;\n  var setStyle = function setStyle(_style) {\n    if (shouldStop) {\n      return;\n    }\n    if (Array.isArray(_style)) {\n      if (!_style.length) {\n        return;\n      }\n      var styles = _style;\n      var _styles = _toArray(styles),\n        curr = _styles[0],\n        restStyles = _styles.slice(1);\n      if (typeof curr === 'number') {\n        setRafTimeout(setStyle.bind(null, restStyles), curr);\n        return;\n      }\n      setStyle(curr);\n      setRafTimeout(setStyle.bind(null, restStyles));\n      return;\n    }\n    if (_typeof(_style) === 'object') {\n      currStyle = _style;\n      handleChange(currStyle);\n    }\n    if (typeof _style === 'function') {\n      _style();\n    }\n  };\n  return {\n    stop: function stop() {\n      shouldStop = true;\n    },\n    start: function start(style) {\n      shouldStop = false;\n      setStyle(style);\n    },\n    subscribe: function subscribe(_handleChange) {\n      handleChange = _handleChange;\n      return function () {\n        handleChange = function handleChange() {\n          return null;\n        };\n      };\n    }\n  };\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAACC,GAAG,EAAE;EAAE,OAAOC,eAAe,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AACzI,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACZ,SAAS,CAACa,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACT,WAAW,EAAEY,CAAC,GAAGH,CAAC,CAACT,WAAW,CAACiB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACV,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACR,GAAG,EAAEkB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGlB,GAAG,CAACmB,MAAM,EAAED,GAAG,GAAGlB,GAAG,CAACmB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIN,KAAK,CAACG,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGpB,GAAG,CAACoB,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASnB,gBAAgBA,CAACoB,IAAI,EAAE;EAAE,IAAI,OAAO3B,MAAM,KAAK,WAAW,IAAI2B,IAAI,CAAC3B,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAI0B,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOP,KAAK,CAACC,IAAI,CAACM,IAAI,CAAC;AAAE;AAC7J,SAASrB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIe,KAAK,CAACQ,OAAO,CAACvB,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,OAAOwB,aAAa,MAAM,iBAAiB;AAC3C,eAAe,SAASC,oBAAoBA,CAAA,EAAG;EAC7C,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,MAAM,EAAE;IACvC,IAAIF,UAAU,EAAE;MACd;IACF;IACA,IAAIb,KAAK,CAACQ,OAAO,CAACO,MAAM,CAAC,EAAE;MACzB,IAAI,CAACA,MAAM,CAACX,MAAM,EAAE;QAClB;MACF;MACA,IAAIY,MAAM,GAAGD,MAAM;MACnB,IAAIE,OAAO,GAAGjC,QAAQ,CAACgC,MAAM,CAAC;QAC5BE,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;QACjBE,UAAU,GAAGF,OAAO,CAACnB,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAI,OAAOoB,IAAI,KAAK,QAAQ,EAAE;QAC5BT,aAAa,CAACK,QAAQ,CAACM,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC,EAAED,IAAI,CAAC;QACpD;MACF;MACAJ,QAAQ,CAACI,IAAI,CAAC;MACdT,aAAa,CAACK,QAAQ,CAACM,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC,CAAC;MAC9C;IACF;IACA,IAAIzC,OAAO,CAACqC,MAAM,CAAC,KAAK,QAAQ,EAAE;MAChCJ,SAAS,GAAGI,MAAM;MAClBH,YAAY,CAACD,SAAS,CAAC;IACzB;IACA,IAAI,OAAOI,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,OAAO;IACLM,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBR,UAAU,GAAG,IAAI;IACnB,CAAC;IACDS,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;MAC3BV,UAAU,GAAG,KAAK;MAClBC,QAAQ,CAACS,KAAK,CAAC;IACjB,CAAC;IACDC,SAAS,EAAE,SAASA,SAASA,CAACC,aAAa,EAAE;MAC3Cb,YAAY,GAAGa,aAAa;MAC5B,OAAO,YAAY;QACjBb,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;UACrC,OAAO,IAAI;QACb,CAAC;MACH,CAAC;IACH;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}