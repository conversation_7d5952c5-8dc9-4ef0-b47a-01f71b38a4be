{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = map;\nfunction map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "map", "values", "mapper", "Symbol", "iterator", "TypeError", "Array", "from", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/map.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = map;\n\nfunction map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,SAASA,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC3B,IAAI,OAAOD,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAI,OAAOH,MAAM,KAAK,UAAU,EAAE,MAAM,IAAIG,SAAS,CAAC,0BAA0B,CAAC;EACjF,OAAOC,KAAK,CAACC,IAAI,CAACN,MAAM,EAAE,CAACH,KAAK,EAAEU,KAAK,KAAKN,MAAM,CAACJ,KAAK,EAAEU,KAAK,EAAEP,MAAM,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "script"}