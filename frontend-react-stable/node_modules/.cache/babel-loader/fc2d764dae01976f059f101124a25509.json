{"ast": null, "code": "import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nvar ESC = KeyCode.ESC,\n  TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n    setTriggerVisible = _ref.setTriggerVisible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus;\n  var focusMenuRef = React.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible && triggerRef.current) {\n      var _triggerRef$current, _triggerRef$current$t, _triggerRef$current$t2, _triggerRef$current$t3;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : (_triggerRef$current$t = _triggerRef$current.triggerRef) === null || _triggerRef$current$t === void 0 ? void 0 : (_triggerRef$current$t2 = _triggerRef$current$t.current) === null || _triggerRef$current$t2 === void 0 ? void 0 : (_triggerRef$current$t3 = _triggerRef$current$t2.focus) === null || _triggerRef$current$t3 === void 0 ? void 0 : _triggerRef$current$t3.call(_triggerRef$current$t2);\n      setTriggerVisible(false);\n      if (typeof onVisibleChange === 'function') {\n        onVisibleChange(false);\n      }\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _triggerRef$current2, _triggerRef$current2$, _triggerRef$current2$2, _triggerRef$current2$3;\n    var elements = getFocusNodeList((_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : (_triggerRef$current2$ = _triggerRef$current2.popupRef) === null || _triggerRef$current2$ === void 0 ? void 0 : (_triggerRef$current2$2 = _triggerRef$current2$.current) === null || _triggerRef$current2$2 === void 0 ? void 0 : (_triggerRef$current2$3 = _triggerRef$current2$2.getElement) === null || _triggerRef$current2$3 === void 0 ? void 0 : _triggerRef$current2$3.call(_triggerRef$current2$2));\n    var firstElement = elements[0];\n    if (firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus) {\n      firstElement.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener('keydown', handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        raf(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener('keydown', handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}", "map": {"version": 3, "names": ["React", "KeyCode", "raf", "getFocusNodeList", "ESC", "TAB", "useAccessibility", "_ref", "visible", "setTriggerVisible", "triggerRef", "onVisibleChange", "autoFocus", "focusMenuRef", "useRef", "handleCloseMenuAndReturnFocus", "current", "_triggerRef$current", "_triggerRef$current$t", "_triggerRef$current$t2", "_triggerRef$current$t3", "focus", "call", "focusMenu", "_triggerRef$current2", "_triggerRef$current2$", "_triggerRef$current2$2", "_triggerRef$current2$3", "elements", "popupRef", "getElement", "firstElement", "handleKeyDown", "event", "keyCode", "focusResult", "preventDefault", "useEffect", "window", "addEventListener", "removeEventListener"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dropdown/es/hooks/useAccessibility.js"], "sourcesContent": ["import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nvar ESC = KeyCode.ESC,\n    TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n      setTriggerVisible = _ref.setTriggerVisible,\n      triggerRef = _ref.triggerRef,\n      onVisibleChange = _ref.onVisibleChange,\n      autoFocus = _ref.autoFocus;\n  var focusMenuRef = React.useRef(false);\n\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible && triggerRef.current) {\n      var _triggerRef$current, _triggerRef$current$t, _triggerRef$current$t2, _triggerRef$current$t3;\n\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : (_triggerRef$current$t = _triggerRef$current.triggerRef) === null || _triggerRef$current$t === void 0 ? void 0 : (_triggerRef$current$t2 = _triggerRef$current$t.current) === null || _triggerRef$current$t2 === void 0 ? void 0 : (_triggerRef$current$t3 = _triggerRef$current$t2.focus) === null || _triggerRef$current$t3 === void 0 ? void 0 : _triggerRef$current$t3.call(_triggerRef$current$t2);\n      setTriggerVisible(false);\n\n      if (typeof onVisibleChange === 'function') {\n        onVisibleChange(false);\n      }\n    }\n  };\n\n  var focusMenu = function focusMenu() {\n    var _triggerRef$current2, _triggerRef$current2$, _triggerRef$current2$2, _triggerRef$current2$3;\n\n    var elements = getFocusNodeList((_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : (_triggerRef$current2$ = _triggerRef$current2.popupRef) === null || _triggerRef$current2$ === void 0 ? void 0 : (_triggerRef$current2$2 = _triggerRef$current2$.current) === null || _triggerRef$current2$2 === void 0 ? void 0 : (_triggerRef$current2$3 = _triggerRef$current2$2.getElement) === null || _triggerRef$current2$3 === void 0 ? void 0 : _triggerRef$current2$3.call(_triggerRef$current2$2));\n    var firstElement = elements[0];\n\n    if (firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus) {\n      firstElement.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n\n    return false;\n  };\n\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n\n      case TAB:\n        {\n          var focusResult = false;\n\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n\n          break;\n        }\n    }\n  };\n\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener('keydown', handleKeyDown);\n\n      if (autoFocus) {\n        // FIXME: hack with raf\n        raf(focusMenu, 3);\n      }\n\n      return function () {\n        window.removeEventListener('keydown', handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,IAAIC,GAAG,GAAGH,OAAO,CAACG,GAAG;EACjBC,GAAG,GAAGJ,OAAO,CAACI,GAAG;AACrB,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACtBC,iBAAiB,GAAGF,IAAI,CAACE,iBAAiB;IAC1CC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,eAAe,GAAGJ,IAAI,CAACI,eAAe;IACtCC,SAAS,GAAGL,IAAI,CAACK,SAAS;EAC9B,IAAIC,YAAY,GAAGb,KAAK,CAACc,MAAM,CAAC,KAAK,CAAC;EAEtC,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAAA,EAAG;IAC3E,IAAIP,OAAO,IAAIE,UAAU,CAACM,OAAO,EAAE;MACjC,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;MAE9F,CAACH,mBAAmB,GAAGP,UAAU,CAACM,OAAO,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,mBAAmB,CAACP,UAAU,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACF,OAAO,MAAM,IAAI,IAAIG,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,sBAAsB,CAACE,KAAK,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACE,IAAI,CAACH,sBAAsB,CAAC;MACxeV,iBAAiB,CAAC,KAAK,CAAC;MAExB,IAAI,OAAOE,eAAe,KAAK,UAAU,EAAE;QACzCA,eAAe,CAAC,KAAK,CAAC;MACxB;IACF;EACF,CAAC;EAED,IAAIY,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,oBAAoB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;IAE/F,IAAIC,QAAQ,GAAGzB,gBAAgB,CAAC,CAACqB,oBAAoB,GAAGd,UAAU,CAACM,OAAO,MAAM,IAAI,IAAIQ,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,oBAAoB,CAACK,QAAQ,MAAM,IAAI,IAAIJ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACT,OAAO,MAAM,IAAI,IAAIU,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,sBAAsB,CAACI,UAAU,MAAM,IAAI,IAAIH,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACL,IAAI,CAACI,sBAAsB,CAAC,CAAC;IAC/gB,IAAIK,YAAY,GAAGH,QAAQ,CAAC,CAAC,CAAC;IAE9B,IAAIG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACV,KAAK,EAAE;MAClFU,YAAY,CAACV,KAAK,CAAC,CAAC;MACpBR,YAAY,CAACG,OAAO,GAAG,IAAI;MAC3B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd,CAAC;EAED,IAAIgB,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,QAAQA,KAAK,CAACC,OAAO;MACnB,KAAK9B,GAAG;QACNW,6BAA6B,CAAC,CAAC;QAC/B;MAEF,KAAKV,GAAG;QACN;UACE,IAAI8B,WAAW,GAAG,KAAK;UAEvB,IAAI,CAACtB,YAAY,CAACG,OAAO,EAAE;YACzBmB,WAAW,GAAGZ,SAAS,CAAC,CAAC;UAC3B;UAEA,IAAIY,WAAW,EAAE;YACfF,KAAK,CAACG,cAAc,CAAC,CAAC;UACxB,CAAC,MAAM;YACLrB,6BAA6B,CAAC,CAAC;UACjC;UAEA;QACF;IACJ;EACF,CAAC;EAEDf,KAAK,CAACqC,SAAS,CAAC,YAAY;IAC1B,IAAI7B,OAAO,EAAE;MACX8B,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;MAEjD,IAAIpB,SAAS,EAAE;QACb;QACAV,GAAG,CAACqB,SAAS,EAAE,CAAC,CAAC;MACnB;MAEA,OAAO,YAAY;QACjBe,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;QACpDnB,YAAY,CAACG,OAAO,GAAG,KAAK;MAC9B,CAAC;IACH;IAEA,OAAO,YAAY;MACjBH,YAAY,CAACG,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}