{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function usePatchElement() {\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  var patchElement = React.useCallback(function (element) {\n    // append a new element to elements (and create a new ref)\n    setElements(function (originElements) {\n      return [].concat(_toConsumableArray(originElements), [element]);\n    });\n    // return a function that removes the new element out of elements (and create a new ref)\n    // it works a little like useEffect\n    return function () {\n      setElements(function (originElements) {\n        return originElements.filter(function (ele) {\n          return ele !== element;\n        });\n      });\n    };\n  }, []);\n  return [elements, patchElement];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "usePatchElement", "_React$useState", "useState", "_React$useState2", "elements", "setElements", "patchElement", "useCallback", "element", "originElements", "concat", "filter", "ele"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/hooks/usePatchElement.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function usePatchElement() {\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  var patchElement = React.useCallback(function (element) {\n    // append a new element to elements (and create a new ref)\n    setElements(function (originElements) {\n      return [].concat(_toConsumableArray(originElements), [element]);\n    });\n    // return a function that removes the new element out of elements (and create a new ref)\n    // it works a little like useEffect\n    return function () {\n      setElements(function (originElements) {\n        return originElements.filter(function (ele) {\n          return ele !== element;\n        });\n      });\n    };\n  }, []);\n  return [elements, patchElement];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,IAAIC,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGL,cAAc,CAACG,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,YAAY,GAAGP,KAAK,CAACQ,WAAW,CAAC,UAAUC,OAAO,EAAE;IACtD;IACAH,WAAW,CAAC,UAAUI,cAAc,EAAE;MACpC,OAAO,EAAE,CAACC,MAAM,CAACb,kBAAkB,CAACY,cAAc,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;IACjE,CAAC,CAAC;IACF;IACA;IACA,OAAO,YAAY;MACjBH,WAAW,CAAC,UAAUI,cAAc,EAAE;QACpC,OAAOA,cAAc,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAE;UAC1C,OAAOA,GAAG,KAAKJ,OAAO;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACJ,QAAQ,EAAEE,YAAY,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}