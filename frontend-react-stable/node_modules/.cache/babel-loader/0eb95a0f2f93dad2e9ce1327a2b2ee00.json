{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Convert a data object to FormData\n * @param {Object} obj\n * @param {?Object} [formData]\n * @returns {Object}\n **/\n\nfunction toFormData(obj, formData) {\n  // eslint-disable-next-line no-param-reassign\n  formData = formData || new FormData();\n  var stack = [];\n  function convertValue(value) {\n    if (value === null) return '';\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n    return value;\n  }\n  function build(data, parentKey) {\n    if (utils.isPlainObject(data) || utils.isArray(data)) {\n      if (stack.indexOf(data) !== -1) {\n        throw Error('Circular reference detected in ' + parentKey);\n      }\n      stack.push(data);\n      utils.forEach(data, function each(value, key) {\n        if (utils.isUndefined(value)) return;\n        var fullKey = parentKey ? parentKey + '.' + key : key;\n        var arr;\n        if (value && !parentKey && typeof value === 'object') {\n          if (utils.endsWith(key, '{}')) {\n            // eslint-disable-next-line no-param-reassign\n            value = JSON.stringify(value);\n          } else if (utils.endsWith(key, '[]') && (arr = utils.toArray(value))) {\n            // eslint-disable-next-line func-names\n            arr.forEach(function (el) {\n              !utils.isUndefined(el) && formData.append(fullKey, convertValue(el));\n            });\n            return;\n          }\n        }\n        build(value, fullKey);\n      });\n      stack.pop();\n    } else {\n      formData.append(parentKey, convertValue(data));\n    }\n  }\n  build(obj);\n  return formData;\n}\nmodule.exports = toFormData;", "map": {"version": 3, "names": ["utils", "require", "toFormData", "obj", "formData", "FormData", "stack", "convertValue", "value", "isDate", "toISOString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTypedArray", "Blob", "<PERSON><PERSON><PERSON>", "from", "build", "data", "parent<PERSON><PERSON>", "isPlainObject", "isArray", "indexOf", "Error", "push", "for<PERSON>ach", "each", "key", "isUndefined", "<PERSON><PERSON><PERSON>", "arr", "endsWith", "JSON", "stringify", "toArray", "el", "append", "pop", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/helpers/toFormData.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Convert a data object to FormData\n * @param {Object} obj\n * @param {?Object} [formData]\n * @returns {Object}\n **/\n\nfunction toFormData(obj, formData) {\n  // eslint-disable-next-line no-param-reassign\n  formData = formData || new FormData();\n\n  var stack = [];\n\n  function convertValue(value) {\n    if (value === null) return '';\n\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n\n    return value;\n  }\n\n  function build(data, parentKey) {\n    if (utils.isPlainObject(data) || utils.isArray(data)) {\n      if (stack.indexOf(data) !== -1) {\n        throw Error('Circular reference detected in ' + parentKey);\n      }\n\n      stack.push(data);\n\n      utils.forEach(data, function each(value, key) {\n        if (utils.isUndefined(value)) return;\n        var fullKey = parentKey ? parentKey + '.' + key : key;\n        var arr;\n\n        if (value && !parentKey && typeof value === 'object') {\n          if (utils.endsWith(key, '{}')) {\n            // eslint-disable-next-line no-param-reassign\n            value = JSON.stringify(value);\n          } else if (utils.endsWith(key, '[]') && (arr = utils.toArray(value))) {\n            // eslint-disable-next-line func-names\n            arr.forEach(function(el) {\n              !utils.isUndefined(el) && formData.append(fullKey, convertValue(el));\n            });\n            return;\n          }\n        }\n\n        build(value, fullKey);\n      });\n\n      stack.pop();\n    } else {\n      formData.append(parentKey, convertValue(data));\n    }\n  }\n\n  build(obj);\n\n  return formData;\n}\n\nmodule.exports = toFormData;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,UAAUA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACjC;EACAA,QAAQ,GAAGA,QAAQ,IAAI,IAAIC,QAAQ,CAAC,CAAC;EAErC,IAAIC,KAAK,GAAG,EAAE;EAEd,SAASC,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,EAAE;IAE7B,IAAIR,KAAK,CAACS,MAAM,CAACD,KAAK,CAAC,EAAE;MACvB,OAAOA,KAAK,CAACE,WAAW,CAAC,CAAC;IAC5B;IAEA,IAAIV,KAAK,CAACW,aAAa,CAACH,KAAK,CAAC,IAAIR,KAAK,CAACY,YAAY,CAACJ,KAAK,CAAC,EAAE;MAC3D,OAAO,OAAOK,IAAI,KAAK,UAAU,GAAG,IAAIA,IAAI,CAAC,CAACL,KAAK,CAAC,CAAC,GAAGM,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC;IAC5E;IAEA,OAAOA,KAAK;EACd;EAEA,SAASQ,KAAKA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC9B,IAAIlB,KAAK,CAACmB,aAAa,CAACF,IAAI,CAAC,IAAIjB,KAAK,CAACoB,OAAO,CAACH,IAAI,CAAC,EAAE;MACpD,IAAIX,KAAK,CAACe,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9B,MAAMK,KAAK,CAAC,iCAAiC,GAAGJ,SAAS,CAAC;MAC5D;MAEAZ,KAAK,CAACiB,IAAI,CAACN,IAAI,CAAC;MAEhBjB,KAAK,CAACwB,OAAO,CAACP,IAAI,EAAE,SAASQ,IAAIA,CAACjB,KAAK,EAAEkB,GAAG,EAAE;QAC5C,IAAI1B,KAAK,CAAC2B,WAAW,CAACnB,KAAK,CAAC,EAAE;QAC9B,IAAIoB,OAAO,GAAGV,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGQ,GAAG,GAAGA,GAAG;QACrD,IAAIG,GAAG;QAEP,IAAIrB,KAAK,IAAI,CAACU,SAAS,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;UACpD,IAAIR,KAAK,CAAC8B,QAAQ,CAACJ,GAAG,EAAE,IAAI,CAAC,EAAE;YAC7B;YACAlB,KAAK,GAAGuB,IAAI,CAACC,SAAS,CAACxB,KAAK,CAAC;UAC/B,CAAC,MAAM,IAAIR,KAAK,CAAC8B,QAAQ,CAACJ,GAAG,EAAE,IAAI,CAAC,KAAKG,GAAG,GAAG7B,KAAK,CAACiC,OAAO,CAACzB,KAAK,CAAC,CAAC,EAAE;YACpE;YACAqB,GAAG,CAACL,OAAO,CAAC,UAASU,EAAE,EAAE;cACvB,CAAClC,KAAK,CAAC2B,WAAW,CAACO,EAAE,CAAC,IAAI9B,QAAQ,CAAC+B,MAAM,CAACP,OAAO,EAAErB,YAAY,CAAC2B,EAAE,CAAC,CAAC;YACtE,CAAC,CAAC;YACF;UACF;QACF;QAEAlB,KAAK,CAACR,KAAK,EAAEoB,OAAO,CAAC;MACvB,CAAC,CAAC;MAEFtB,KAAK,CAAC8B,GAAG,CAAC,CAAC;IACb,CAAC,MAAM;MACLhC,QAAQ,CAAC+B,MAAM,CAACjB,SAAS,EAAEX,YAAY,CAACU,IAAI,CAAC,CAAC;IAChD;EACF;EAEAD,KAAK,CAACb,GAAG,CAAC;EAEV,OAAOC,QAAQ;AACjB;AAEAiC,MAAM,CAACC,OAAO,GAAGpC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}