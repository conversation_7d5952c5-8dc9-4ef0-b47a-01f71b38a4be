{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"src\", \"alt\", \"onClose\", \"afterClose\", \"visible\", \"icons\", \"rootClassName\", \"countRender\", \"scaleStep\"];\nimport * as React from 'react';\nimport Dialog from 'rc-dialog';\nimport classnames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { warning } from \"rc-util/es/warning\";\nimport useFrameSetState from './hooks/useFrameSetState';\nimport getFixScaleEleTransPosition from './getFixScaleEleTransPosition';\nimport { context } from './PreviewGroup';\nvar useState = React.useState,\n  useEffect = React.useEffect,\n  useCallback = React.useCallback,\n  useRef = React.useRef,\n  useContext = React.useContext;\nvar initialPosition = {\n  x: 0,\n  y: 0\n};\nvar Preview = function Preview(props) {\n  var _countRender;\n  var prefixCls = props.prefixCls,\n    src = props.src,\n    alt = props.alt,\n    onClose = props.onClose,\n    afterClose = props.afterClose,\n    visible = props.visible,\n    _props$icons = props.icons,\n    icons = _props$icons === void 0 ? {} : _props$icons,\n    rootClassName = props.rootClassName,\n    countRender = props.countRender,\n    _props$scaleStep = props.scaleStep,\n    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var rotateLeft = icons.rotateLeft,\n    rotateRight = icons.rotateRight,\n    zoomIn = icons.zoomIn,\n    zoomOut = icons.zoomOut,\n    close = icons.close,\n    left = icons.left,\n    right = icons.right;\n  var _useState = useState(1),\n    _useState2 = _slicedToArray(_useState, 2),\n    scale = _useState2[0],\n    setScale = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    rotate = _useState4[0],\n    setRotate = _useState4[1];\n  var _useFrameSetState = useFrameSetState(initialPosition),\n    _useFrameSetState2 = _slicedToArray(_useFrameSetState, 2),\n    position = _useFrameSetState2[0],\n    setPosition = _useFrameSetState2[1];\n  var imgRef = useRef();\n  var originPositionRef = useRef({\n    originX: 0,\n    originY: 0,\n    deltaX: 0,\n    deltaY: 0\n  });\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    isMoving = _useState6[0],\n    setMoving = _useState6[1];\n  var _useContext = useContext(context),\n    previewUrls = _useContext.previewUrls,\n    current = _useContext.current,\n    isPreviewGroup = _useContext.isPreviewGroup,\n    setCurrent = _useContext.setCurrent;\n  var previewGroupCount = previewUrls.size;\n  var previewUrlsKeys = Array.from(previewUrls.keys());\n  var currentPreviewIndex = previewUrlsKeys.indexOf(current);\n  var combinationSrc = isPreviewGroup ? previewUrls.get(current) : src;\n  var showLeftOrRightSwitches = isPreviewGroup && previewGroupCount > 1;\n  var showOperationsProgress = isPreviewGroup && previewGroupCount >= 1;\n  var _useState7 = useState({\n      wheelDirection: 0\n    }),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastWheelZoomDirection = _useState8[0],\n    setLastWheelZoomDirection = _useState8[1];\n  var onAfterClose = function onAfterClose() {\n    setScale(1);\n    setRotate(0);\n    setPosition(initialPosition);\n  };\n  var onZoomIn = function onZoomIn() {\n    setScale(function (value) {\n      return value + scaleStep;\n    });\n    setPosition(initialPosition);\n  };\n  var onZoomOut = function onZoomOut() {\n    if (scale > 1) {\n      setScale(function (value) {\n        return value - scaleStep;\n      });\n    }\n    setPosition(initialPosition);\n  };\n  var onRotateRight = function onRotateRight() {\n    setRotate(function (value) {\n      return value + 90;\n    });\n  };\n  var onRotateLeft = function onRotateLeft() {\n    setRotate(function (value) {\n      return value - 90;\n    });\n  };\n  var onSwitchLeft = function onSwitchLeft(event) {\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n    if (currentPreviewIndex > 0) {\n      setCurrent(previewUrlsKeys[currentPreviewIndex - 1]);\n    }\n  };\n  var onSwitchRight = function onSwitchRight(event) {\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n    if (currentPreviewIndex < previewGroupCount - 1) {\n      setCurrent(previewUrlsKeys[currentPreviewIndex + 1]);\n    }\n  };\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  var toolClassName = \"\".concat(prefixCls, \"-operations-operation\");\n  var iconClassName = \"\".concat(prefixCls, \"-operations-icon\");\n  var tools = [{\n    icon: close,\n    onClick: onClose,\n    type: 'close'\n  }, {\n    icon: zoomIn,\n    onClick: onZoomIn,\n    type: 'zoomIn'\n  }, {\n    icon: zoomOut,\n    onClick: onZoomOut,\n    type: 'zoomOut',\n    disabled: scale === 1\n  }, {\n    icon: rotateRight,\n    onClick: onRotateRight,\n    type: 'rotateRight'\n  }, {\n    icon: rotateLeft,\n    onClick: onRotateLeft,\n    type: 'rotateLeft'\n  }];\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale; // eslint-disable-next-line @typescript-eslint/no-shadow\n\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        _left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      setMoving(false);\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, _left, top);\n      if (fixState) {\n        setPosition(_objectSpread({}, fixState));\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (event.button !== 0) return;\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n    originPositionRef.current.deltaX = event.pageX - position.x;\n    originPositionRef.current.deltaY = event.pageY - position.y;\n    originPositionRef.current.originX = position.x;\n    originPositionRef.current.originY = position.y;\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      setPosition({\n        x: event.pageX - originPositionRef.current.deltaX,\n        y: event.pageY - originPositionRef.current.deltaY\n      });\n    }\n  };\n  var onWheelMove = function onWheelMove(event) {\n    if (!visible) return;\n    event.preventDefault();\n    var wheelDirection = event.deltaY;\n    setLastWheelZoomDirection({\n      wheelDirection: wheelDirection\n    });\n  };\n  var onKeyDown = useCallback(function (event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    event.preventDefault();\n    if (event.keyCode === KeyCode.LEFT) {\n      if (currentPreviewIndex > 0) {\n        setCurrent(previewUrlsKeys[currentPreviewIndex - 1]);\n      }\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      if (currentPreviewIndex < previewGroupCount - 1) {\n        setCurrent(previewUrlsKeys[currentPreviewIndex + 1]);\n      }\n    }\n  }, [currentPreviewIndex, previewGroupCount, previewUrlsKeys, setCurrent, showLeftOrRightSwitches, visible]);\n  var onDoubleClick = function onDoubleClick() {\n    if (visible) {\n      if (scale !== 1) {\n        setScale(1);\n      }\n      if (position.x !== initialPosition.x || position.y !== initialPosition.y) {\n        setPosition(initialPosition);\n      }\n    }\n  };\n  useEffect(function () {\n    var wheelDirection = lastWheelZoomDirection.wheelDirection;\n    if (wheelDirection > 0) {\n      onZoomOut();\n    } else if (wheelDirection < 0) {\n      onZoomIn();\n    }\n  }, [lastWheelZoomDirection]);\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n    var onScrollWheelListener = addEventListener(window, 'wheel', onWheelMove, {\n      passive: false\n    });\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n    try {\n      // Resolve if in iframe lost event\n\n      /* istanbul ignore next */\n      if (window.top !== window.self) {\n        onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n        onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n      }\n    } catch (error) {\n      /* istanbul ignore next */\n      warning(false, \"[rc-image] \".concat(error));\n    }\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n      onScrollWheelListener.remove();\n      onKeyDownListener.remove();\n      /* istanbul ignore next */\n\n      if (onTopMouseUpListener) onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n\n      if (onTopMouseMoveListener) onTopMouseMoveListener.remove();\n    };\n  }, [visible, isMoving, onKeyDown]);\n  return /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: \"zoom\",\n    maskTransitionName: \"fade\",\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    afterClose: onAfterClose,\n    visible: visible,\n    wrapClassName: wrapClassName,\n    rootClassName: rootClassName\n  }, restProps), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-operations\")\n  }, showOperationsProgress && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-operations-progress\")\n  }, (_countRender = countRender === null || countRender === void 0 ? void 0 : countRender(currentPreviewIndex + 1, previewGroupCount)) !== null && _countRender !== void 0 ? _countRender : \"\".concat(currentPreviewIndex + 1, \" / \").concat(previewGroupCount)), tools.map(function (_ref) {\n    var icon = _ref.icon,\n      onClick = _ref.onClick,\n      type = _ref.type,\n      disabled = _ref.disabled;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      className: classnames(toolClassName, _defineProperty({}, \"\".concat(prefixCls, \"-operations-operation-disabled\"), !!disabled)),\n      onClick: onClick,\n      key: type\n    }, /*#__PURE__*/React.isValidElement(icon) ? /*#__PURE__*/React.cloneElement(icon, {\n      className: iconClassName\n    }) : icon);\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\"),\n    style: {\n      transform: \"translate3d(\".concat(position.x, \"px, \").concat(position.y, \"px, 0)\")\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    width: props.width,\n    height: props.height,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick,\n    ref: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    src: combinationSrc,\n    alt: alt,\n    style: {\n      transform: \"scale3d(\".concat(scale, \", \").concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\")\n    }\n  })), showLeftOrRightSwitches && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-switch-left\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-left-disabled\"), currentPreviewIndex === 0)),\n    onClick: onSwitchLeft\n  }, left), showLeftOrRightSwitches && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-switch-right\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-right-disabled\"), currentPreviewIndex === previewGroupCount - 1)),\n    onClick: onSwitchRight\n  }, right));\n};\nexport default Preview;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "Dialog", "classnames", "addEventListener", "KeyCode", "warning", "useFrameSetState", "getFixScaleEleTransPosition", "context", "useState", "useEffect", "useCallback", "useRef", "useContext", "initialPosition", "x", "y", "Preview", "props", "_countRender", "prefixCls", "src", "alt", "onClose", "afterClose", "visible", "_props$icons", "icons", "rootClassName", "countRender", "_props$scaleStep", "scaleStep", "restProps", "rotateLeft", "rotateRight", "zoomIn", "zoomOut", "close", "left", "right", "_useState", "_useState2", "scale", "setScale", "_useState3", "_useState4", "rotate", "setRotate", "_useFrameSetState", "_useFrameSetState2", "position", "setPosition", "imgRef", "originPositionRef", "originX", "originY", "deltaX", "deltaY", "_useState5", "_useState6", "isMoving", "setMoving", "_useContext", "previewUrls", "current", "isPreviewGroup", "setCurrent", "previewGroupCount", "size", "previewUrlsKeys", "Array", "from", "keys", "currentPreviewIndex", "indexOf", "combinationSrc", "get", "showLeftOrRightSwitches", "showOperationsProgress", "_useState7", "wheelDirection", "_useState8", "lastWheelZoomDirection", "setLastWheelZoomDirection", "onAfterClose", "onZoomIn", "value", "onZoomOut", "onRotateRight", "onRotateLeft", "onSwitchLeft", "event", "preventDefault", "stopPropagation", "onSwitchRight", "wrapClassName", "concat", "toolClassName", "iconClassName", "tools", "icon", "onClick", "type", "disabled", "onMouseUp", "width", "offsetWidth", "height", "offsetHeight", "_imgRef$current$getBo", "getBoundingClientRect", "_left", "top", "isRotate", "fixState", "onMouseDown", "button", "pageX", "pageY", "onMouseMove", "onWheelMove", "onKeyDown", "keyCode", "LEFT", "RIGHT", "onDoubleClick", "onTopMouseUpListener", "onTopMouseMoveListener", "onMouseUpListener", "window", "onMouseMoveListener", "onScrollWheelListener", "passive", "onKeyDownListener", "self", "error", "remove", "createElement", "transitionName", "maskTransitionName", "closable", "keyboard", "className", "map", "_ref", "key", "isValidElement", "cloneElement", "style", "transform", "ref"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-image/es/Preview.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"src\", \"alt\", \"onClose\", \"afterClose\", \"visible\", \"icons\", \"rootClassName\", \"countRender\", \"scaleStep\"];\nimport * as React from 'react';\nimport Dialog from 'rc-dialog';\nimport classnames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { warning } from \"rc-util/es/warning\";\nimport useFrameSetState from './hooks/useFrameSetState';\nimport getFixScaleEleTransPosition from './getFixScaleEleTransPosition';\nimport { context } from './PreviewGroup';\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useCallback = React.useCallback,\n    useRef = React.useRef,\n    useContext = React.useContext;\nvar initialPosition = {\n  x: 0,\n  y: 0\n};\n\nvar Preview = function Preview(props) {\n  var _countRender;\n\n  var prefixCls = props.prefixCls,\n      src = props.src,\n      alt = props.alt,\n      onClose = props.onClose,\n      afterClose = props.afterClose,\n      visible = props.visible,\n      _props$icons = props.icons,\n      icons = _props$icons === void 0 ? {} : _props$icons,\n      rootClassName = props.rootClassName,\n      countRender = props.countRender,\n      _props$scaleStep = props.scaleStep,\n      scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var rotateLeft = icons.rotateLeft,\n      rotateRight = icons.rotateRight,\n      zoomIn = icons.zoomIn,\n      zoomOut = icons.zoomOut,\n      close = icons.close,\n      left = icons.left,\n      right = icons.right;\n\n  var _useState = useState(1),\n      _useState2 = _slicedToArray(_useState, 2),\n      scale = _useState2[0],\n      setScale = _useState2[1];\n\n  var _useState3 = useState(0),\n      _useState4 = _slicedToArray(_useState3, 2),\n      rotate = _useState4[0],\n      setRotate = _useState4[1];\n\n  var _useFrameSetState = useFrameSetState(initialPosition),\n      _useFrameSetState2 = _slicedToArray(_useFrameSetState, 2),\n      position = _useFrameSetState2[0],\n      setPosition = _useFrameSetState2[1];\n\n  var imgRef = useRef();\n  var originPositionRef = useRef({\n    originX: 0,\n    originY: 0,\n    deltaX: 0,\n    deltaY: 0\n  });\n\n  var _useState5 = useState(false),\n      _useState6 = _slicedToArray(_useState5, 2),\n      isMoving = _useState6[0],\n      setMoving = _useState6[1];\n\n  var _useContext = useContext(context),\n      previewUrls = _useContext.previewUrls,\n      current = _useContext.current,\n      isPreviewGroup = _useContext.isPreviewGroup,\n      setCurrent = _useContext.setCurrent;\n\n  var previewGroupCount = previewUrls.size;\n  var previewUrlsKeys = Array.from(previewUrls.keys());\n  var currentPreviewIndex = previewUrlsKeys.indexOf(current);\n  var combinationSrc = isPreviewGroup ? previewUrls.get(current) : src;\n  var showLeftOrRightSwitches = isPreviewGroup && previewGroupCount > 1;\n  var showOperationsProgress = isPreviewGroup && previewGroupCount >= 1;\n\n  var _useState7 = useState({\n    wheelDirection: 0\n  }),\n      _useState8 = _slicedToArray(_useState7, 2),\n      lastWheelZoomDirection = _useState8[0],\n      setLastWheelZoomDirection = _useState8[1];\n\n  var onAfterClose = function onAfterClose() {\n    setScale(1);\n    setRotate(0);\n    setPosition(initialPosition);\n  };\n\n  var onZoomIn = function onZoomIn() {\n    setScale(function (value) {\n      return value + scaleStep;\n    });\n    setPosition(initialPosition);\n  };\n\n  var onZoomOut = function onZoomOut() {\n    if (scale > 1) {\n      setScale(function (value) {\n        return value - scaleStep;\n      });\n    }\n\n    setPosition(initialPosition);\n  };\n\n  var onRotateRight = function onRotateRight() {\n    setRotate(function (value) {\n      return value + 90;\n    });\n  };\n\n  var onRotateLeft = function onRotateLeft() {\n    setRotate(function (value) {\n      return value - 90;\n    });\n  };\n\n  var onSwitchLeft = function onSwitchLeft(event) {\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n\n    if (currentPreviewIndex > 0) {\n      setCurrent(previewUrlsKeys[currentPreviewIndex - 1]);\n    }\n  };\n\n  var onSwitchRight = function onSwitchRight(event) {\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n\n    if (currentPreviewIndex < previewGroupCount - 1) {\n      setCurrent(previewUrlsKeys[currentPreviewIndex + 1]);\n    }\n  };\n\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  var toolClassName = \"\".concat(prefixCls, \"-operations-operation\");\n  var iconClassName = \"\".concat(prefixCls, \"-operations-icon\");\n  var tools = [{\n    icon: close,\n    onClick: onClose,\n    type: 'close'\n  }, {\n    icon: zoomIn,\n    onClick: onZoomIn,\n    type: 'zoomIn'\n  }, {\n    icon: zoomOut,\n    onClick: onZoomOut,\n    type: 'zoomOut',\n    disabled: scale === 1\n  }, {\n    icon: rotateRight,\n    onClick: onRotateRight,\n    type: 'rotateRight'\n  }, {\n    icon: rotateLeft,\n    onClick: onRotateLeft,\n    type: 'rotateLeft'\n  }];\n\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale; // eslint-disable-next-line @typescript-eslint/no-shadow\n\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n          _left = _imgRef$current$getBo.left,\n          top = _imgRef$current$getBo.top;\n\n      var isRotate = rotate % 180 !== 0;\n      setMoving(false);\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, _left, top);\n\n      if (fixState) {\n        setPosition(_objectSpread({}, fixState));\n      }\n    }\n  };\n\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (event.button !== 0) return;\n    event.preventDefault(); // Without this mask close will abnormal\n\n    event.stopPropagation();\n    originPositionRef.current.deltaX = event.pageX - position.x;\n    originPositionRef.current.deltaY = event.pageY - position.y;\n    originPositionRef.current.originX = position.x;\n    originPositionRef.current.originY = position.y;\n    setMoving(true);\n  };\n\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      setPosition({\n        x: event.pageX - originPositionRef.current.deltaX,\n        y: event.pageY - originPositionRef.current.deltaY\n      });\n    }\n  };\n\n  var onWheelMove = function onWheelMove(event) {\n    if (!visible) return;\n    event.preventDefault();\n    var wheelDirection = event.deltaY;\n    setLastWheelZoomDirection({\n      wheelDirection: wheelDirection\n    });\n  };\n\n  var onKeyDown = useCallback(function (event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    event.preventDefault();\n\n    if (event.keyCode === KeyCode.LEFT) {\n      if (currentPreviewIndex > 0) {\n        setCurrent(previewUrlsKeys[currentPreviewIndex - 1]);\n      }\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      if (currentPreviewIndex < previewGroupCount - 1) {\n        setCurrent(previewUrlsKeys[currentPreviewIndex + 1]);\n      }\n    }\n  }, [currentPreviewIndex, previewGroupCount, previewUrlsKeys, setCurrent, showLeftOrRightSwitches, visible]);\n\n  var onDoubleClick = function onDoubleClick() {\n    if (visible) {\n      if (scale !== 1) {\n        setScale(1);\n      }\n\n      if (position.x !== initialPosition.x || position.y !== initialPosition.y) {\n        setPosition(initialPosition);\n      }\n    }\n  };\n\n  useEffect(function () {\n    var wheelDirection = lastWheelZoomDirection.wheelDirection;\n\n    if (wheelDirection > 0) {\n      onZoomOut();\n    } else if (wheelDirection < 0) {\n      onZoomIn();\n    }\n  }, [lastWheelZoomDirection]);\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n    var onScrollWheelListener = addEventListener(window, 'wheel', onWheelMove, {\n      passive: false\n    });\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n\n    try {\n      // Resolve if in iframe lost event\n\n      /* istanbul ignore next */\n      if (window.top !== window.self) {\n        onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n        onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n      }\n    } catch (error) {\n      /* istanbul ignore next */\n      warning(false, \"[rc-image] \".concat(error));\n    }\n\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n      onScrollWheelListener.remove();\n      onKeyDownListener.remove();\n      /* istanbul ignore next */\n\n      if (onTopMouseUpListener) onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n\n      if (onTopMouseMoveListener) onTopMouseMoveListener.remove();\n    };\n  }, [visible, isMoving, onKeyDown]);\n  return /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: \"zoom\",\n    maskTransitionName: \"fade\",\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    afterClose: onAfterClose,\n    visible: visible,\n    wrapClassName: wrapClassName,\n    rootClassName: rootClassName\n  }, restProps), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-operations\")\n  }, showOperationsProgress && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-operations-progress\")\n  }, (_countRender = countRender === null || countRender === void 0 ? void 0 : countRender(currentPreviewIndex + 1, previewGroupCount)) !== null && _countRender !== void 0 ? _countRender : \"\".concat(currentPreviewIndex + 1, \" / \").concat(previewGroupCount)), tools.map(function (_ref) {\n    var icon = _ref.icon,\n        onClick = _ref.onClick,\n        type = _ref.type,\n        disabled = _ref.disabled;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      className: classnames(toolClassName, _defineProperty({}, \"\".concat(prefixCls, \"-operations-operation-disabled\"), !!disabled)),\n      onClick: onClick,\n      key: type\n    }, /*#__PURE__*/React.isValidElement(icon) ? /*#__PURE__*/React.cloneElement(icon, {\n      className: iconClassName\n    }) : icon);\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\"),\n    style: {\n      transform: \"translate3d(\".concat(position.x, \"px, \").concat(position.y, \"px, 0)\")\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    width: props.width,\n    height: props.height,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick,\n    ref: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    src: combinationSrc,\n    alt: alt,\n    style: {\n      transform: \"scale3d(\".concat(scale, \", \").concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\")\n    }\n  })), showLeftOrRightSwitches && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-switch-left\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-left-disabled\"), currentPreviewIndex === 0)),\n    onClick: onSwitchLeft\n  }, left), showLeftOrRightSwitches && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-switch-right\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-right-disabled\"), currentPreviewIndex === previewGroupCount - 1)),\n    onClick: onSwitchRight\n  }, right));\n};\n\nexport default Preview;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,CAAC;AACrI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,OAAO,QAAQ,gBAAgB;AACxC,IAAIC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;EACzBC,SAAS,GAAGV,KAAK,CAACU,SAAS;EAC3BC,WAAW,GAAGX,KAAK,CAACW,WAAW;EAC/BC,MAAM,GAAGZ,KAAK,CAACY,MAAM;EACrBC,UAAU,GAAGb,KAAK,CAACa,UAAU;AACjC,IAAIC,eAAe,GAAG;EACpBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AAED,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,YAAY;EAEhB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,GAAG,GAAGH,KAAK,CAACG,GAAG;IACfC,GAAG,GAAGJ,KAAK,CAACI,GAAG;IACfC,OAAO,GAAGL,KAAK,CAACK,OAAO;IACvBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,YAAY,GAAGR,KAAK,CAACS,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,aAAa,GAAGV,KAAK,CAACU,aAAa;IACnCC,WAAW,GAAGX,KAAK,CAACW,WAAW;IAC/BC,gBAAgB,GAAGZ,KAAK,CAACa,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,gBAAgB;IAChEE,SAAS,GAAGlC,wBAAwB,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EAE1D,IAAIkC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,IAAI,GAAGX,KAAK,CAACW,IAAI;IACjBC,KAAK,GAAGZ,KAAK,CAACY,KAAK;EAEvB,IAAIC,SAAS,GAAG/B,QAAQ,CAAC,CAAC,CAAC;IACvBgC,UAAU,GAAG5C,cAAc,CAAC2C,SAAS,EAAE,CAAC,CAAC;IACzCE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIG,UAAU,GAAGnC,QAAQ,CAAC,CAAC,CAAC;IACxBoC,UAAU,GAAGhD,cAAc,CAAC+C,UAAU,EAAE,CAAC,CAAC;IAC1CE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE7B,IAAIG,iBAAiB,GAAG1C,gBAAgB,CAACQ,eAAe,CAAC;IACrDmC,kBAAkB,GAAGpD,cAAc,CAACmD,iBAAiB,EAAE,CAAC,CAAC;IACzDE,QAAQ,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IAChCE,WAAW,GAAGF,kBAAkB,CAAC,CAAC,CAAC;EAEvC,IAAIG,MAAM,GAAGxC,MAAM,CAAC,CAAC;EACrB,IAAIyC,iBAAiB,GAAGzC,MAAM,CAAC;IAC7B0C,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,IAAIC,UAAU,GAAGjD,QAAQ,CAAC,KAAK,CAAC;IAC5BkD,UAAU,GAAG9D,cAAc,CAAC6D,UAAU,EAAE,CAAC,CAAC;IAC1CE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE7B,IAAIG,WAAW,GAAGjD,UAAU,CAACL,OAAO,CAAC;IACjCuD,WAAW,GAAGD,WAAW,CAACC,WAAW;IACrCC,OAAO,GAAGF,WAAW,CAACE,OAAO;IAC7BC,cAAc,GAAGH,WAAW,CAACG,cAAc;IAC3CC,UAAU,GAAGJ,WAAW,CAACI,UAAU;EAEvC,IAAIC,iBAAiB,GAAGJ,WAAW,CAACK,IAAI;EACxC,IAAIC,eAAe,GAAGC,KAAK,CAACC,IAAI,CAACR,WAAW,CAACS,IAAI,CAAC,CAAC,CAAC;EACpD,IAAIC,mBAAmB,GAAGJ,eAAe,CAACK,OAAO,CAACV,OAAO,CAAC;EAC1D,IAAIW,cAAc,GAAGV,cAAc,GAAGF,WAAW,CAACa,GAAG,CAACZ,OAAO,CAAC,GAAG3C,GAAG;EACpE,IAAIwD,uBAAuB,GAAGZ,cAAc,IAAIE,iBAAiB,GAAG,CAAC;EACrE,IAAIW,sBAAsB,GAAGb,cAAc,IAAIE,iBAAiB,IAAI,CAAC;EAErE,IAAIY,UAAU,GAAGtE,QAAQ,CAAC;MACxBuE,cAAc,EAAE;IAClB,CAAC,CAAC;IACEC,UAAU,GAAGpF,cAAc,CAACkF,UAAU,EAAE,CAAC,CAAC;IAC1CG,sBAAsB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtCE,yBAAyB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE7C,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzCzC,QAAQ,CAAC,CAAC,CAAC;IACXI,SAAS,CAAC,CAAC,CAAC;IACZI,WAAW,CAACrC,eAAe,CAAC;EAC9B,CAAC;EAED,IAAIuE,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC1C,QAAQ,CAAC,UAAU2C,KAAK,EAAE;MACxB,OAAOA,KAAK,GAAGvD,SAAS;IAC1B,CAAC,CAAC;IACFoB,WAAW,CAACrC,eAAe,CAAC;EAC9B,CAAC;EAED,IAAIyE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAI7C,KAAK,GAAG,CAAC,EAAE;MACbC,QAAQ,CAAC,UAAU2C,KAAK,EAAE;QACxB,OAAOA,KAAK,GAAGvD,SAAS;MAC1B,CAAC,CAAC;IACJ;IAEAoB,WAAW,CAACrC,eAAe,CAAC;EAC9B,CAAC;EAED,IAAI0E,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3CzC,SAAS,CAAC,UAAUuC,KAAK,EAAE;MACzB,OAAOA,KAAK,GAAG,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC1C,SAAS,CAAC,UAAUuC,KAAK,EAAE;MACzB,OAAOA,KAAK,GAAG,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAII,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9CA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAEvB,IAAIpB,mBAAmB,GAAG,CAAC,EAAE;MAC3BP,UAAU,CAACG,eAAe,CAACI,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,IAAIqB,aAAa,GAAG,SAASA,aAAaA,CAACH,KAAK,EAAE;IAChDA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAEvB,IAAIpB,mBAAmB,GAAGN,iBAAiB,GAAG,CAAC,EAAE;MAC/CD,UAAU,CAACG,eAAe,CAACI,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,IAAIsB,aAAa,GAAG7F,UAAU,CAACN,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoG,MAAM,CAAC5E,SAAS,EAAE,SAAS,CAAC,EAAEwC,QAAQ,CAAC,CAAC;EAC9F,IAAIqC,aAAa,GAAG,EAAE,CAACD,MAAM,CAAC5E,SAAS,EAAE,uBAAuB,CAAC;EACjE,IAAI8E,aAAa,GAAG,EAAE,CAACF,MAAM,CAAC5E,SAAS,EAAE,kBAAkB,CAAC;EAC5D,IAAI+E,KAAK,GAAG,CAAC;IACXC,IAAI,EAAE/D,KAAK;IACXgE,OAAO,EAAE9E,OAAO;IAChB+E,IAAI,EAAE;EACR,CAAC,EAAE;IACDF,IAAI,EAAEjE,MAAM;IACZkE,OAAO,EAAEhB,QAAQ;IACjBiB,IAAI,EAAE;EACR,CAAC,EAAE;IACDF,IAAI,EAAEhE,OAAO;IACbiE,OAAO,EAAEd,SAAS;IAClBe,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE7D,KAAK,KAAK;EACtB,CAAC,EAAE;IACD0D,IAAI,EAAElE,WAAW;IACjBmE,OAAO,EAAEb,aAAa;IACtBc,IAAI,EAAE;EACR,CAAC,EAAE;IACDF,IAAI,EAAEnE,UAAU;IAChBoE,OAAO,EAAEZ,YAAY;IACrBa,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,IAAIE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAI/E,OAAO,IAAImC,QAAQ,EAAE;MACvB,IAAI6C,KAAK,GAAGrD,MAAM,CAACY,OAAO,CAAC0C,WAAW,GAAGhE,KAAK;MAC9C,IAAIiE,MAAM,GAAGvD,MAAM,CAACY,OAAO,CAAC4C,YAAY,GAAGlE,KAAK,CAAC,CAAC;;MAElD,IAAImE,qBAAqB,GAAGzD,MAAM,CAACY,OAAO,CAAC8C,qBAAqB,CAAC,CAAC;QAC9DC,KAAK,GAAGF,qBAAqB,CAACvE,IAAI;QAClC0E,GAAG,GAAGH,qBAAqB,CAACG,GAAG;MAEnC,IAAIC,QAAQ,GAAGnE,MAAM,GAAG,GAAG,KAAK,CAAC;MACjCe,SAAS,CAAC,KAAK,CAAC;MAChB,IAAIqD,QAAQ,GAAG3G,2BAA2B,CAAC0G,QAAQ,GAAGN,MAAM,GAAGF,KAAK,EAAEQ,QAAQ,GAAGR,KAAK,GAAGE,MAAM,EAAEI,KAAK,EAAEC,GAAG,CAAC;MAE5G,IAAIE,QAAQ,EAAE;QACZ/D,WAAW,CAACxD,aAAa,CAAC,CAAC,CAAC,EAAEuH,QAAQ,CAAC,CAAC;MAC1C;IACF;EACF,CAAC;EAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACxB,KAAK,EAAE;IAC5C;IACA,IAAIA,KAAK,CAACyB,MAAM,KAAK,CAAC,EAAE;IACxBzB,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvBxC,iBAAiB,CAACW,OAAO,CAACR,MAAM,GAAGmC,KAAK,CAAC0B,KAAK,GAAGnE,QAAQ,CAACnC,CAAC;IAC3DsC,iBAAiB,CAACW,OAAO,CAACP,MAAM,GAAGkC,KAAK,CAAC2B,KAAK,GAAGpE,QAAQ,CAAClC,CAAC;IAC3DqC,iBAAiB,CAACW,OAAO,CAACV,OAAO,GAAGJ,QAAQ,CAACnC,CAAC;IAC9CsC,iBAAiB,CAACW,OAAO,CAACT,OAAO,GAAGL,QAAQ,CAAClC,CAAC;IAC9C6C,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,IAAI0D,WAAW,GAAG,SAASA,WAAWA,CAAC5B,KAAK,EAAE;IAC5C,IAAIlE,OAAO,IAAImC,QAAQ,EAAE;MACvBT,WAAW,CAAC;QACVpC,CAAC,EAAE4E,KAAK,CAAC0B,KAAK,GAAGhE,iBAAiB,CAACW,OAAO,CAACR,MAAM;QACjDxC,CAAC,EAAE2E,KAAK,CAAC2B,KAAK,GAAGjE,iBAAiB,CAACW,OAAO,CAACP;MAC7C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAI+D,WAAW,GAAG,SAASA,WAAWA,CAAC7B,KAAK,EAAE;IAC5C,IAAI,CAAClE,OAAO,EAAE;IACdkE,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAIZ,cAAc,GAAGW,KAAK,CAAClC,MAAM;IACjC0B,yBAAyB,CAAC;MACxBH,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIyC,SAAS,GAAG9G,WAAW,CAAC,UAAUgF,KAAK,EAAE;IAC3C,IAAI,CAAClE,OAAO,IAAI,CAACoD,uBAAuB,EAAE;IAC1Cc,KAAK,CAACC,cAAc,CAAC,CAAC;IAEtB,IAAID,KAAK,CAAC+B,OAAO,KAAKtH,OAAO,CAACuH,IAAI,EAAE;MAClC,IAAIlD,mBAAmB,GAAG,CAAC,EAAE;QAC3BP,UAAU,CAACG,eAAe,CAACI,mBAAmB,GAAG,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,MAAM,IAAIkB,KAAK,CAAC+B,OAAO,KAAKtH,OAAO,CAACwH,KAAK,EAAE;MAC1C,IAAInD,mBAAmB,GAAGN,iBAAiB,GAAG,CAAC,EAAE;QAC/CD,UAAU,CAACG,eAAe,CAACI,mBAAmB,GAAG,CAAC,CAAC,CAAC;MACtD;IACF;EACF,CAAC,EAAE,CAACA,mBAAmB,EAAEN,iBAAiB,EAAEE,eAAe,EAAEH,UAAU,EAAEW,uBAAuB,EAAEpD,OAAO,CAAC,CAAC;EAE3G,IAAIoG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIpG,OAAO,EAAE;MACX,IAAIiB,KAAK,KAAK,CAAC,EAAE;QACfC,QAAQ,CAAC,CAAC,CAAC;MACb;MAEA,IAAIO,QAAQ,CAACnC,CAAC,KAAKD,eAAe,CAACC,CAAC,IAAImC,QAAQ,CAAClC,CAAC,KAAKF,eAAe,CAACE,CAAC,EAAE;QACxEmC,WAAW,CAACrC,eAAe,CAAC;MAC9B;IACF;EACF,CAAC;EAEDJ,SAAS,CAAC,YAAY;IACpB,IAAIsE,cAAc,GAAGE,sBAAsB,CAACF,cAAc;IAE1D,IAAIA,cAAc,GAAG,CAAC,EAAE;MACtBO,SAAS,CAAC,CAAC;IACb,CAAC,MAAM,IAAIP,cAAc,GAAG,CAAC,EAAE;MAC7BK,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACH,sBAAsB,CAAC,CAAC;EAC5BxE,SAAS,CAAC,YAAY;IACpB,IAAIoH,oBAAoB;IACxB,IAAIC,sBAAsB;IAC1B,IAAIC,iBAAiB,GAAG7H,gBAAgB,CAAC8H,MAAM,EAAE,SAAS,EAAEzB,SAAS,EAAE,KAAK,CAAC;IAC7E,IAAI0B,mBAAmB,GAAG/H,gBAAgB,CAAC8H,MAAM,EAAE,WAAW,EAAEV,WAAW,EAAE,KAAK,CAAC;IACnF,IAAIY,qBAAqB,GAAGhI,gBAAgB,CAAC8H,MAAM,EAAE,OAAO,EAAET,WAAW,EAAE;MACzEY,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAIC,iBAAiB,GAAGlI,gBAAgB,CAAC8H,MAAM,EAAE,SAAS,EAAER,SAAS,EAAE,KAAK,CAAC;IAE7E,IAAI;MACF;;MAEA;MACA,IAAIQ,MAAM,CAACjB,GAAG,KAAKiB,MAAM,CAACK,IAAI,EAAE;QAC9BR,oBAAoB,GAAG3H,gBAAgB,CAAC8H,MAAM,CAACjB,GAAG,EAAE,SAAS,EAAER,SAAS,EAAE,KAAK,CAAC;QAChFuB,sBAAsB,GAAG5H,gBAAgB,CAAC8H,MAAM,CAACjB,GAAG,EAAE,WAAW,EAAEO,WAAW,EAAE,KAAK,CAAC;MACxF;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAlI,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC2F,MAAM,CAACuC,KAAK,CAAC,CAAC;IAC7C;IAEA,OAAO,YAAY;MACjBP,iBAAiB,CAACQ,MAAM,CAAC,CAAC;MAC1BN,mBAAmB,CAACM,MAAM,CAAC,CAAC;MAC5BL,qBAAqB,CAACK,MAAM,CAAC,CAAC;MAC9BH,iBAAiB,CAACG,MAAM,CAAC,CAAC;MAC1B;;MAEA,IAAIV,oBAAoB,EAAEA,oBAAoB,CAACU,MAAM,CAAC,CAAC;MACvD;;MAEA,IAAIT,sBAAsB,EAAEA,sBAAsB,CAACS,MAAM,CAAC,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAAC/G,OAAO,EAAEmC,QAAQ,EAAE6D,SAAS,CAAC,CAAC;EAClC,OAAO,aAAazH,KAAK,CAACyI,aAAa,CAACxI,MAAM,EAAEP,QAAQ,CAAC;IACvDgJ,cAAc,EAAE,MAAM;IACtBC,kBAAkB,EAAE,MAAM;IAC1BC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,IAAI;IACdzH,SAAS,EAAEA,SAAS;IACpBG,OAAO,EAAEA,OAAO;IAChBC,UAAU,EAAE4D,YAAY;IACxB3D,OAAO,EAAEA,OAAO;IAChBsE,aAAa,EAAEA,aAAa;IAC5BnE,aAAa,EAAEA;EACjB,CAAC,EAAEI,SAAS,CAAC,EAAE,aAAahC,KAAK,CAACyI,aAAa,CAAC,IAAI,EAAE;IACpDK,SAAS,EAAE,EAAE,CAAC9C,MAAM,CAAC5E,SAAS,EAAE,aAAa;EAC/C,CAAC,EAAE0D,sBAAsB,IAAI,aAAa9E,KAAK,CAACyI,aAAa,CAAC,IAAI,EAAE;IAClEK,SAAS,EAAE,EAAE,CAAC9C,MAAM,CAAC5E,SAAS,EAAE,sBAAsB;EACxD,CAAC,EAAE,CAACD,YAAY,GAAGU,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC4C,mBAAmB,GAAG,CAAC,EAAEN,iBAAiB,CAAC,MAAM,IAAI,IAAIhD,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC6E,MAAM,CAACvB,mBAAmB,GAAG,CAAC,EAAE,KAAK,CAAC,CAACuB,MAAM,CAAC7B,iBAAiB,CAAC,CAAC,EAAEgC,KAAK,CAAC4C,GAAG,CAAC,UAAUC,IAAI,EAAE;IACzR,IAAI5C,IAAI,GAAG4C,IAAI,CAAC5C,IAAI;MAChBC,OAAO,GAAG2C,IAAI,CAAC3C,OAAO;MACtBC,IAAI,GAAG0C,IAAI,CAAC1C,IAAI;MAChBC,QAAQ,GAAGyC,IAAI,CAACzC,QAAQ;IAC5B,OAAO,aAAavG,KAAK,CAACyI,aAAa,CAAC,IAAI,EAAE;MAC5CK,SAAS,EAAE5I,UAAU,CAAC+F,aAAa,EAAErG,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoG,MAAM,CAAC5E,SAAS,EAAE,gCAAgC,CAAC,EAAE,CAAC,CAACmF,QAAQ,CAAC,CAAC;MAC7HF,OAAO,EAAEA,OAAO;MAChB4C,GAAG,EAAE3C;IACP,CAAC,EAAE,aAAatG,KAAK,CAACkJ,cAAc,CAAC9C,IAAI,CAAC,GAAG,aAAapG,KAAK,CAACmJ,YAAY,CAAC/C,IAAI,EAAE;MACjF0C,SAAS,EAAE5C;IACb,CAAC,CAAC,GAAGE,IAAI,CAAC;EACZ,CAAC,CAAC,CAAC,EAAE,aAAapG,KAAK,CAACyI,aAAa,CAAC,KAAK,EAAE;IAC3CK,SAAS,EAAE,EAAE,CAAC9C,MAAM,CAAC5E,SAAS,EAAE,cAAc,CAAC;IAC/CgI,KAAK,EAAE;MACLC,SAAS,EAAE,cAAc,CAACrD,MAAM,CAAC9C,QAAQ,CAACnC,CAAC,EAAE,MAAM,CAAC,CAACiF,MAAM,CAAC9C,QAAQ,CAAClC,CAAC,EAAE,QAAQ;IAClF;EACF,CAAC,EAAE,aAAahB,KAAK,CAACyI,aAAa,CAAC,KAAK,EAAE;IACzChC,KAAK,EAAEvF,KAAK,CAACuF,KAAK;IAClBE,MAAM,EAAEzF,KAAK,CAACyF,MAAM;IACpBQ,WAAW,EAAEA,WAAW;IACxBU,aAAa,EAAEA,aAAa;IAC5ByB,GAAG,EAAElG,MAAM;IACX0F,SAAS,EAAE,EAAE,CAAC9C,MAAM,CAAC5E,SAAS,EAAE,MAAM,CAAC;IACvCC,GAAG,EAAEsD,cAAc;IACnBrD,GAAG,EAAEA,GAAG;IACR8H,KAAK,EAAE;MACLC,SAAS,EAAE,UAAU,CAACrD,MAAM,CAACtD,KAAK,EAAE,IAAI,CAAC,CAACsD,MAAM,CAACtD,KAAK,EAAE,cAAc,CAAC,CAACsD,MAAM,CAAClD,MAAM,EAAE,MAAM;IAC/F;EACF,CAAC,CAAC,CAAC,EAAE+B,uBAAuB,IAAI,aAAa7E,KAAK,CAACyI,aAAa,CAAC,KAAK,EAAE;IACtEK,SAAS,EAAE5I,UAAU,CAAC,EAAE,CAAC8F,MAAM,CAAC5E,SAAS,EAAE,cAAc,CAAC,EAAExB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoG,MAAM,CAAC5E,SAAS,EAAE,uBAAuB,CAAC,EAAEqD,mBAAmB,KAAK,CAAC,CAAC,CAAC;IAC1J4B,OAAO,EAAEX;EACX,CAAC,EAAEpD,IAAI,CAAC,EAAEuC,uBAAuB,IAAI,aAAa7E,KAAK,CAACyI,aAAa,CAAC,KAAK,EAAE;IAC3EK,SAAS,EAAE5I,UAAU,CAAC,EAAE,CAAC8F,MAAM,CAAC5E,SAAS,EAAE,eAAe,CAAC,EAAExB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoG,MAAM,CAAC5E,SAAS,EAAE,wBAAwB,CAAC,EAAEqD,mBAAmB,KAAKN,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAChLkC,OAAO,EAAEP;EACX,CAAC,EAAEvD,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,eAAetB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}