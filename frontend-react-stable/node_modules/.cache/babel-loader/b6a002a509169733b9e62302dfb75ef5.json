{"ast": null, "code": "var DescriptionsItem = function DescriptionsItem(_ref) {\n  var children = _ref.children;\n  return children;\n};\nexport default DescriptionsItem;", "map": {"version": 3, "names": ["DescriptionsItem", "_ref", "children"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/descriptions/Item.js"], "sourcesContent": ["var DescriptionsItem = function DescriptionsItem(_ref) {\n  var children = _ref.children;\n  return children;\n};\nexport default DescriptionsItem;"], "mappings": "AAAA,IAAIA,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EACrD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC;AACD,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}