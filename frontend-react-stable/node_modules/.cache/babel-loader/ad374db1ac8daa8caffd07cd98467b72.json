{"ast": null, "code": "import LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport * as React from 'react';\nimport Button from '../button';\nvar Operation = function Operation(_ref) {\n  var disabled = _ref.disabled,\n    moveToLeft = _ref.moveToLeft,\n    moveToRight = _ref.moveToRight,\n    _ref$leftArrowText = _ref.leftArrowText,\n    leftArrowText = _ref$leftArrowText === void 0 ? '' : _ref$leftArrowText,\n    _ref$rightArrowText = _ref.rightArrowText,\n    rightArrowText = _ref$rightArrowText === void 0 ? '' : _ref$rightArrowText,\n    leftActive = _ref.leftActive,\n    rightActive = _ref.rightActive,\n    className = _ref.className,\n    style = _ref.style,\n    direction = _ref.direction,\n    oneWay = _ref.oneWay;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !rightActive,\n    onClick: moveToRight,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null)\n  }, rightArrowText), !oneWay && /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !leftActive,\n    onClick: moveToLeft,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  }, leftArrowText));\n};\nexport default Operation;", "map": {"version": 3, "names": ["LeftOutlined", "RightOutlined", "React", "<PERSON><PERSON>", "Operation", "_ref", "disabled", "moveToLeft", "moveToRight", "_ref$leftArrowText", "leftArrowText", "_ref$rightArrowText", "rightArrowText", "leftActive", "rightActive", "className", "style", "direction", "oneWay", "createElement", "type", "size", "onClick", "icon"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/transfer/operation.js"], "sourcesContent": ["import LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport * as React from 'react';\nimport Button from '../button';\nvar Operation = function Operation(_ref) {\n  var disabled = _ref.disabled,\n    moveToLeft = _ref.moveToLeft,\n    moveToRight = _ref.moveToRight,\n    _ref$leftArrowText = _ref.leftArrowText,\n    leftArrowText = _ref$leftArrowText === void 0 ? '' : _ref$leftArrowText,\n    _ref$rightArrowText = _ref.rightArrowText,\n    rightArrowText = _ref$rightArrowText === void 0 ? '' : _ref$rightArrowText,\n    leftActive = _ref.leftActive,\n    rightActive = _ref.rightActive,\n    className = _ref.className,\n    style = _ref.style,\n    direction = _ref.direction,\n    oneWay = _ref.oneWay;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !rightActive,\n    onClick: moveToRight,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null)\n  }, rightArrowText), !oneWay && /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !leftActive,\n    onClick: moveToLeft,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  }, leftArrowText));\n};\nexport default Operation;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yCAAyC;AAClE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EACvC,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAC9BC,kBAAkB,GAAGJ,IAAI,CAACK,aAAa;IACvCA,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;IACvEE,mBAAmB,GAAGN,IAAI,CAACO,cAAc;IACzCA,cAAc,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,mBAAmB;IAC1EE,UAAU,GAAGR,IAAI,CAACQ,UAAU;IAC5BC,WAAW,GAAGT,IAAI,CAACS,WAAW;IAC9BC,SAAS,GAAGV,IAAI,CAACU,SAAS;IAC1BC,KAAK,GAAGX,IAAI,CAACW,KAAK;IAClBC,SAAS,GAAGZ,IAAI,CAACY,SAAS;IAC1BC,MAAM,GAAGb,IAAI,CAACa,MAAM;EACtB,OAAO,aAAahB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC7CJ,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAad,KAAK,CAACiB,aAAa,CAAChB,MAAM,EAAE;IAC1CiB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,OAAO;IACbf,QAAQ,EAAEA,QAAQ,IAAI,CAACQ,WAAW;IAClCQ,OAAO,EAAEd,WAAW;IACpBe,IAAI,EAAEN,SAAS,KAAK,KAAK,GAAG,aAAaf,KAAK,CAACiB,aAAa,CAAClB,aAAa,EAAE,IAAI,CAAC,GAAG,aAAaC,KAAK,CAACiB,aAAa,CAACnB,YAAY,EAAE,IAAI;EACzI,CAAC,EAAEY,cAAc,CAAC,EAAE,CAACM,MAAM,IAAI,aAAahB,KAAK,CAACiB,aAAa,CAAChB,MAAM,EAAE;IACtEiB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,OAAO;IACbf,QAAQ,EAAEA,QAAQ,IAAI,CAACO,UAAU;IACjCS,OAAO,EAAEf,UAAU;IACnBgB,IAAI,EAAEN,SAAS,KAAK,KAAK,GAAG,aAAaf,KAAK,CAACiB,aAAa,CAACnB,YAAY,EAAE,IAAI,CAAC,GAAG,aAAaE,KAAK,CAACiB,aAAa,CAAClB,aAAa,EAAE,IAAI;EACzI,CAAC,EAAES,aAAa,CAAC,CAAC;AACpB,CAAC;AACD,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}