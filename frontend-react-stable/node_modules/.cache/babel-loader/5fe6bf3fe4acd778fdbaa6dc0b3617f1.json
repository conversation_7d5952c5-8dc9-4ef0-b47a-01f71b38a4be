{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigConsumer, ConfigContext } from '../config-provider';\nimport raf from './raf';\nimport { cloneElement } from './reactNode';\nvar styleForPseudo;\n// Where el is the DOM element you'd like to test for visibility\nfunction isHidden(element) {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n  return !element || element.offsetParent === null || element.hidden;\n}\nfunction getValidateContainer(nodeRoot) {\n  if (nodeRoot instanceof Document) {\n    return nodeRoot.body;\n  }\n  return Array.from(nodeRoot.childNodes).find(function (ele) {\n    return (ele === null || ele === void 0 ? void 0 : ele.nodeType) === Node.ELEMENT_NODE;\n  });\n}\nfunction isNotGrey(color) {\n  // eslint-disable-next-line no-useless-escape\n  var match = (color || '').match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\d.]*)?\\)/);\n  if (match && match[1] && match[2] && match[3]) {\n    return !(match[1] === match[2] && match[2] === match[3]);\n  }\n  return true;\n}\nvar Wave = /*#__PURE__*/function (_React$Component) {\n  _inherits(Wave, _React$Component);\n  var _super = _createSuper(Wave);\n  function Wave() {\n    var _this;\n    _classCallCheck(this, Wave);\n    _this = _super.apply(this, arguments);\n    _this.containerRef = /*#__PURE__*/React.createRef();\n    _this.animationStart = false;\n    _this.destroyed = false;\n    _this.onClick = function (node, waveColor) {\n      var _a, _b;\n      var _this$props = _this.props,\n        insertExtraNode = _this$props.insertExtraNode,\n        disabled = _this$props.disabled;\n      if (disabled || !node || isHidden(node) || node.className.includes('-leave')) {\n        return;\n      }\n      _this.extraNode = document.createElement('div');\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        extraNode = _assertThisInitialize.extraNode;\n      var getPrefixCls = _this.context.getPrefixCls;\n      extraNode.className = \"\".concat(getPrefixCls(''), \"-click-animating-node\");\n      var attributeName = _this.getAttributeName();\n      node.setAttribute(attributeName, 'true');\n      // Not white or transparent or grey\n      if (waveColor && waveColor !== '#fff' && waveColor !== '#ffffff' && waveColor !== 'rgb(255, 255, 255)' && waveColor !== 'rgba(255, 255, 255, 1)' && isNotGrey(waveColor) && !/rgba\\((?:\\d*, ){3}0\\)/.test(waveColor) &&\n      // any transparent rgba color\n      waveColor !== 'transparent') {\n        extraNode.style.borderColor = waveColor;\n        var nodeRoot = ((_a = node.getRootNode) === null || _a === void 0 ? void 0 : _a.call(node)) || node.ownerDocument;\n        var nodeBody = (_b = getValidateContainer(nodeRoot)) !== null && _b !== void 0 ? _b : nodeRoot;\n        styleForPseudo = updateCSS(\"\\n      [\".concat(getPrefixCls(''), \"-click-animating-without-extra-node='true']::after, .\").concat(getPrefixCls(''), \"-click-animating-node {\\n        --antd-wave-shadow-color: \").concat(waveColor, \";\\n      }\"), 'antd-wave', {\n          csp: _this.csp,\n          attachTo: nodeBody\n        });\n      }\n      if (insertExtraNode) {\n        node.appendChild(extraNode);\n      }\n      ['transition', 'animation'].forEach(function (name) {\n        node.addEventListener(\"\".concat(name, \"start\"), _this.onTransitionStart);\n        node.addEventListener(\"\".concat(name, \"end\"), _this.onTransitionEnd);\n      });\n    };\n    _this.onTransitionStart = function (e) {\n      if (_this.destroyed) {\n        return;\n      }\n      var node = _this.containerRef.current;\n      if (!e || e.target !== node || _this.animationStart) {\n        return;\n      }\n      _this.resetEffect(node);\n    };\n    _this.onTransitionEnd = function (e) {\n      if (!e || e.animationName !== 'fadeEffect') {\n        return;\n      }\n      _this.resetEffect(e.target);\n    };\n    _this.bindAnimationEvent = function (node) {\n      if (!node || !node.getAttribute || node.getAttribute('disabled') || node.className.includes('disabled')) {\n        return;\n      }\n      var onClick = function onClick(e) {\n        // Fix radio button click twice\n        if (e.target.tagName === 'INPUT' || isHidden(e.target)) {\n          return;\n        }\n        _this.resetEffect(node);\n        // Get wave color from target\n        var waveColor = getComputedStyle(node).getPropertyValue('border-top-color') ||\n        // Firefox Compatible\n        getComputedStyle(node).getPropertyValue('border-color') || getComputedStyle(node).getPropertyValue('background-color');\n        _this.clickWaveTimeoutId = window.setTimeout(function () {\n          return _this.onClick(node, waveColor);\n        }, 0);\n        raf.cancel(_this.animationStartId);\n        _this.animationStart = true;\n        // Render to trigger transition event cost 3 frames. Let's delay 10 frames to reset this.\n        _this.animationStartId = raf(function () {\n          _this.animationStart = false;\n        }, 10);\n      };\n      node.addEventListener('click', onClick, true);\n      return {\n        cancel: function cancel() {\n          node.removeEventListener('click', onClick, true);\n        }\n      };\n    };\n    _this.renderWave = function (_ref) {\n      var csp = _ref.csp;\n      var children = _this.props.children;\n      _this.csp = csp;\n      if (! /*#__PURE__*/React.isValidElement(children)) return children;\n      var ref = _this.containerRef;\n      if (supportRef(children)) {\n        ref = composeRef(children.ref, _this.containerRef);\n      }\n      return cloneElement(children, {\n        ref: ref\n      });\n    };\n    return _this;\n  }\n  _createClass(Wave, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      var node = this.containerRef.current;\n      if (!node || node.nodeType !== 1) {\n        return;\n      }\n      this.instance = this.bindAnimationEvent(node);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.instance) {\n        this.instance.cancel();\n      }\n      if (this.clickWaveTimeoutId) {\n        clearTimeout(this.clickWaveTimeoutId);\n      }\n      this.destroyed = true;\n    }\n  }, {\n    key: \"getAttributeName\",\n    value: function getAttributeName() {\n      var getPrefixCls = this.context.getPrefixCls;\n      var insertExtraNode = this.props.insertExtraNode;\n      return insertExtraNode ? \"\".concat(getPrefixCls(''), \"-click-animating\") : \"\".concat(getPrefixCls(''), \"-click-animating-without-extra-node\");\n    }\n  }, {\n    key: \"resetEffect\",\n    value: function resetEffect(node) {\n      var _this2 = this;\n      if (!node || node === this.extraNode || !(node instanceof Element)) {\n        return;\n      }\n      var insertExtraNode = this.props.insertExtraNode;\n      var attributeName = this.getAttributeName();\n      node.setAttribute(attributeName, 'false'); // edge has bug on `removeAttribute` #14466\n      if (styleForPseudo) {\n        styleForPseudo.innerHTML = '';\n      }\n      if (insertExtraNode && this.extraNode && node.contains(this.extraNode)) {\n        node.removeChild(this.extraNode);\n      }\n      ['transition', 'animation'].forEach(function (name) {\n        node.removeEventListener(\"\".concat(name, \"start\"), _this2.onTransitionStart);\n        node.removeEventListener(\"\".concat(name, \"end\"), _this2.onTransitionEnd);\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, this.renderWave);\n    }\n  }]);\n  return Wave;\n}(React.Component);\nWave.contextType = ConfigContext;\nexport default Wave;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "updateCSS", "composeRef", "supportRef", "React", "ConfigConsumer", "ConfigContext", "raf", "cloneElement", "style<PERSON>or<PERSON><PERSON><PERSON>", "isHidden", "element", "process", "env", "NODE_ENV", "offsetParent", "hidden", "getValidateContainer", "nodeRoot", "Document", "body", "Array", "from", "childNodes", "find", "ele", "nodeType", "Node", "ELEMENT_NODE", "isNotGrey", "color", "match", "Wave", "_React$Component", "_super", "_this", "apply", "arguments", "containerRef", "createRef", "animationStart", "destroyed", "onClick", "node", "waveColor", "_a", "_b", "_this$props", "props", "insertExtraNode", "disabled", "className", "includes", "extraNode", "document", "createElement", "_assertThisInitialize", "getPrefixCls", "context", "concat", "attributeName", "getAttributeName", "setAttribute", "test", "style", "borderColor", "getRootNode", "call", "ownerDocument", "nodeBody", "csp", "attachTo", "append<PERSON><PERSON><PERSON>", "for<PERSON>ach", "name", "addEventListener", "onTransitionStart", "onTransitionEnd", "e", "current", "target", "resetEffect", "animationName", "bindAnimationEvent", "getAttribute", "tagName", "getComputedStyle", "getPropertyValue", "clickWaveTimeoutId", "window", "setTimeout", "cancel", "animationStartId", "removeEventListener", "renderWave", "_ref", "children", "isValidElement", "ref", "key", "value", "componentDidMount", "instance", "componentWillUnmount", "clearTimeout", "_this2", "Element", "innerHTML", "contains", "<PERSON><PERSON><PERSON><PERSON>", "render", "Component", "contextType"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/wave.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigConsumer, ConfigContext } from '../config-provider';\nimport raf from './raf';\nimport { cloneElement } from './reactNode';\nvar styleForPseudo;\n// Where el is the DOM element you'd like to test for visibility\nfunction isHidden(element) {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n  return !element || element.offsetParent === null || element.hidden;\n}\nfunction getValidateContainer(nodeRoot) {\n  if (nodeRoot instanceof Document) {\n    return nodeRoot.body;\n  }\n  return Array.from(nodeRoot.childNodes).find(function (ele) {\n    return (ele === null || ele === void 0 ? void 0 : ele.nodeType) === Node.ELEMENT_NODE;\n  });\n}\nfunction isNotGrey(color) {\n  // eslint-disable-next-line no-useless-escape\n  var match = (color || '').match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\d.]*)?\\)/);\n  if (match && match[1] && match[2] && match[3]) {\n    return !(match[1] === match[2] && match[2] === match[3]);\n  }\n  return true;\n}\nvar Wave = /*#__PURE__*/function (_React$Component) {\n  _inherits(Wave, _React$Component);\n  var _super = _createSuper(Wave);\n  function Wave() {\n    var _this;\n    _classCallCheck(this, Wave);\n    _this = _super.apply(this, arguments);\n    _this.containerRef = /*#__PURE__*/React.createRef();\n    _this.animationStart = false;\n    _this.destroyed = false;\n    _this.onClick = function (node, waveColor) {\n      var _a, _b;\n      var _this$props = _this.props,\n        insertExtraNode = _this$props.insertExtraNode,\n        disabled = _this$props.disabled;\n      if (disabled || !node || isHidden(node) || node.className.includes('-leave')) {\n        return;\n      }\n      _this.extraNode = document.createElement('div');\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        extraNode = _assertThisInitialize.extraNode;\n      var getPrefixCls = _this.context.getPrefixCls;\n      extraNode.className = \"\".concat(getPrefixCls(''), \"-click-animating-node\");\n      var attributeName = _this.getAttributeName();\n      node.setAttribute(attributeName, 'true');\n      // Not white or transparent or grey\n      if (waveColor && waveColor !== '#fff' && waveColor !== '#ffffff' && waveColor !== 'rgb(255, 255, 255)' && waveColor !== 'rgba(255, 255, 255, 1)' && isNotGrey(waveColor) && !/rgba\\((?:\\d*, ){3}0\\)/.test(waveColor) &&\n      // any transparent rgba color\n      waveColor !== 'transparent') {\n        extraNode.style.borderColor = waveColor;\n        var nodeRoot = ((_a = node.getRootNode) === null || _a === void 0 ? void 0 : _a.call(node)) || node.ownerDocument;\n        var nodeBody = (_b = getValidateContainer(nodeRoot)) !== null && _b !== void 0 ? _b : nodeRoot;\n        styleForPseudo = updateCSS(\"\\n      [\".concat(getPrefixCls(''), \"-click-animating-without-extra-node='true']::after, .\").concat(getPrefixCls(''), \"-click-animating-node {\\n        --antd-wave-shadow-color: \").concat(waveColor, \";\\n      }\"), 'antd-wave', {\n          csp: _this.csp,\n          attachTo: nodeBody\n        });\n      }\n      if (insertExtraNode) {\n        node.appendChild(extraNode);\n      }\n      ['transition', 'animation'].forEach(function (name) {\n        node.addEventListener(\"\".concat(name, \"start\"), _this.onTransitionStart);\n        node.addEventListener(\"\".concat(name, \"end\"), _this.onTransitionEnd);\n      });\n    };\n    _this.onTransitionStart = function (e) {\n      if (_this.destroyed) {\n        return;\n      }\n      var node = _this.containerRef.current;\n      if (!e || e.target !== node || _this.animationStart) {\n        return;\n      }\n      _this.resetEffect(node);\n    };\n    _this.onTransitionEnd = function (e) {\n      if (!e || e.animationName !== 'fadeEffect') {\n        return;\n      }\n      _this.resetEffect(e.target);\n    };\n    _this.bindAnimationEvent = function (node) {\n      if (!node || !node.getAttribute || node.getAttribute('disabled') || node.className.includes('disabled')) {\n        return;\n      }\n      var onClick = function onClick(e) {\n        // Fix radio button click twice\n        if (e.target.tagName === 'INPUT' || isHidden(e.target)) {\n          return;\n        }\n        _this.resetEffect(node);\n        // Get wave color from target\n        var waveColor = getComputedStyle(node).getPropertyValue('border-top-color') ||\n        // Firefox Compatible\n        getComputedStyle(node).getPropertyValue('border-color') || getComputedStyle(node).getPropertyValue('background-color');\n        _this.clickWaveTimeoutId = window.setTimeout(function () {\n          return _this.onClick(node, waveColor);\n        }, 0);\n        raf.cancel(_this.animationStartId);\n        _this.animationStart = true;\n        // Render to trigger transition event cost 3 frames. Let's delay 10 frames to reset this.\n        _this.animationStartId = raf(function () {\n          _this.animationStart = false;\n        }, 10);\n      };\n      node.addEventListener('click', onClick, true);\n      return {\n        cancel: function cancel() {\n          node.removeEventListener('click', onClick, true);\n        }\n      };\n    };\n    _this.renderWave = function (_ref) {\n      var csp = _ref.csp;\n      var children = _this.props.children;\n      _this.csp = csp;\n      if (! /*#__PURE__*/React.isValidElement(children)) return children;\n      var ref = _this.containerRef;\n      if (supportRef(children)) {\n        ref = composeRef(children.ref, _this.containerRef);\n      }\n      return cloneElement(children, {\n        ref: ref\n      });\n    };\n    return _this;\n  }\n  _createClass(Wave, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      var node = this.containerRef.current;\n      if (!node || node.nodeType !== 1) {\n        return;\n      }\n      this.instance = this.bindAnimationEvent(node);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.instance) {\n        this.instance.cancel();\n      }\n      if (this.clickWaveTimeoutId) {\n        clearTimeout(this.clickWaveTimeoutId);\n      }\n      this.destroyed = true;\n    }\n  }, {\n    key: \"getAttributeName\",\n    value: function getAttributeName() {\n      var getPrefixCls = this.context.getPrefixCls;\n      var insertExtraNode = this.props.insertExtraNode;\n      return insertExtraNode ? \"\".concat(getPrefixCls(''), \"-click-animating\") : \"\".concat(getPrefixCls(''), \"-click-animating-without-extra-node\");\n    }\n  }, {\n    key: \"resetEffect\",\n    value: function resetEffect(node) {\n      var _this2 = this;\n      if (!node || node === this.extraNode || !(node instanceof Element)) {\n        return;\n      }\n      var insertExtraNode = this.props.insertExtraNode;\n      var attributeName = this.getAttributeName();\n      node.setAttribute(attributeName, 'false'); // edge has bug on `removeAttribute` #14466\n      if (styleForPseudo) {\n        styleForPseudo.innerHTML = '';\n      }\n      if (insertExtraNode && this.extraNode && node.contains(this.extraNode)) {\n        node.removeChild(this.extraNode);\n      }\n      ['transition', 'animation'].forEach(function (name) {\n        node.removeEventListener(\"\".concat(name, \"start\"), _this2.onTransitionStart);\n        node.removeEventListener(\"\".concat(name, \"end\"), _this2.onTransitionEnd);\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, this.renderWave);\n    }\n  }]);\n  return Wave;\n}(React.Component);\nWave.contextType = ConfigContext;\nexport default Wave;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,EAAEC,aAAa,QAAQ,oBAAoB;AAClE,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,YAAY,QAAQ,aAAa;AAC1C,IAAIC,cAAc;AAClB;AACA,SAASC,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAO,KAAK;EACd;EACA,OAAO,CAACH,OAAO,IAAIA,OAAO,CAACI,YAAY,KAAK,IAAI,IAAIJ,OAAO,CAACK,MAAM;AACpE;AACA,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;EACtC,IAAIA,QAAQ,YAAYC,QAAQ,EAAE;IAChC,OAAOD,QAAQ,CAACE,IAAI;EACtB;EACA,OAAOC,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAACK,UAAU,CAAC,CAACC,IAAI,CAAC,UAAUC,GAAG,EAAE;IACzD,OAAO,CAACA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,QAAQ,MAAMC,IAAI,CAACC,YAAY;EACvF,CAAC,CAAC;AACJ;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB;EACA,IAAIC,KAAK,GAAG,CAACD,KAAK,IAAI,EAAE,EAAEC,KAAK,CAAC,yCAAyC,CAAC;EAC1E,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;IAC7C,OAAO,EAAEA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1D;EACA,OAAO,IAAI;AACb;AACA,IAAIC,IAAI,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAClDlC,SAAS,CAACiC,IAAI,EAAEC,gBAAgB,CAAC;EACjC,IAAIC,MAAM,GAAGlC,YAAY,CAACgC,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTvC,eAAe,CAAC,IAAI,EAAEoC,IAAI,CAAC;IAC3BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrCF,KAAK,CAACG,YAAY,GAAG,aAAalC,KAAK,CAACmC,SAAS,CAAC,CAAC;IACnDJ,KAAK,CAACK,cAAc,GAAG,KAAK;IAC5BL,KAAK,CAACM,SAAS,GAAG,KAAK;IACvBN,KAAK,CAACO,OAAO,GAAG,UAAUC,IAAI,EAAEC,SAAS,EAAE;MACzC,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIC,WAAW,GAAGZ,KAAK,CAACa,KAAK;QAC3BC,eAAe,GAAGF,WAAW,CAACE,eAAe;QAC7CC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;MACjC,IAAIA,QAAQ,IAAI,CAACP,IAAI,IAAIjC,QAAQ,CAACiC,IAAI,CAAC,IAAIA,IAAI,CAACQ,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC5E;MACF;MACAjB,KAAK,CAACkB,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/C,IAAIC,qBAAqB,GAAG1D,sBAAsB,CAACqC,KAAK,CAAC;QACvDkB,SAAS,GAAGG,qBAAqB,CAACH,SAAS;MAC7C,IAAII,YAAY,GAAGtB,KAAK,CAACuB,OAAO,CAACD,YAAY;MAC7CJ,SAAS,CAACF,SAAS,GAAG,EAAE,CAACQ,MAAM,CAACF,YAAY,CAAC,EAAE,CAAC,EAAE,uBAAuB,CAAC;MAC1E,IAAIG,aAAa,GAAGzB,KAAK,CAAC0B,gBAAgB,CAAC,CAAC;MAC5ClB,IAAI,CAACmB,YAAY,CAACF,aAAa,EAAE,MAAM,CAAC;MACxC;MACA,IAAIhB,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,oBAAoB,IAAIA,SAAS,KAAK,wBAAwB,IAAIf,SAAS,CAACe,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAACmB,IAAI,CAACnB,SAAS,CAAC;MACpN;MACAA,SAAS,KAAK,aAAa,EAAE;QAC3BS,SAAS,CAACW,KAAK,CAACC,WAAW,GAAGrB,SAAS;QACvC,IAAI1B,QAAQ,GAAG,CAAC,CAAC2B,EAAE,GAAGF,IAAI,CAACuB,WAAW,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACxB,IAAI,CAAC,KAAKA,IAAI,CAACyB,aAAa;QACjH,IAAIC,QAAQ,GAAG,CAACvB,EAAE,GAAG7B,oBAAoB,CAACC,QAAQ,CAAC,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG5B,QAAQ;QAC9FT,cAAc,GAAGR,SAAS,CAAC,WAAW,CAAC0D,MAAM,CAACF,YAAY,CAAC,EAAE,CAAC,EAAE,uDAAuD,CAAC,CAACE,MAAM,CAACF,YAAY,CAAC,EAAE,CAAC,EAAE,6DAA6D,CAAC,CAACE,MAAM,CAACf,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,EAAE;UAC7P0B,GAAG,EAAEnC,KAAK,CAACmC,GAAG;UACdC,QAAQ,EAAEF;QACZ,CAAC,CAAC;MACJ;MACA,IAAIpB,eAAe,EAAE;QACnBN,IAAI,CAAC6B,WAAW,CAACnB,SAAS,CAAC;MAC7B;MACA,CAAC,YAAY,EAAE,WAAW,CAAC,CAACoB,OAAO,CAAC,UAAUC,IAAI,EAAE;QAClD/B,IAAI,CAACgC,gBAAgB,CAAC,EAAE,CAAChB,MAAM,CAACe,IAAI,EAAE,OAAO,CAAC,EAAEvC,KAAK,CAACyC,iBAAiB,CAAC;QACxEjC,IAAI,CAACgC,gBAAgB,CAAC,EAAE,CAAChB,MAAM,CAACe,IAAI,EAAE,KAAK,CAAC,EAAEvC,KAAK,CAAC0C,eAAe,CAAC;MACtE,CAAC,CAAC;IACJ,CAAC;IACD1C,KAAK,CAACyC,iBAAiB,GAAG,UAAUE,CAAC,EAAE;MACrC,IAAI3C,KAAK,CAACM,SAAS,EAAE;QACnB;MACF;MACA,IAAIE,IAAI,GAAGR,KAAK,CAACG,YAAY,CAACyC,OAAO;MACrC,IAAI,CAACD,CAAC,IAAIA,CAAC,CAACE,MAAM,KAAKrC,IAAI,IAAIR,KAAK,CAACK,cAAc,EAAE;QACnD;MACF;MACAL,KAAK,CAAC8C,WAAW,CAACtC,IAAI,CAAC;IACzB,CAAC;IACDR,KAAK,CAAC0C,eAAe,GAAG,UAAUC,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,IAAIA,CAAC,CAACI,aAAa,KAAK,YAAY,EAAE;QAC1C;MACF;MACA/C,KAAK,CAAC8C,WAAW,CAACH,CAAC,CAACE,MAAM,CAAC;IAC7B,CAAC;IACD7C,KAAK,CAACgD,kBAAkB,GAAG,UAAUxC,IAAI,EAAE;MACzC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyC,YAAY,IAAIzC,IAAI,CAACyC,YAAY,CAAC,UAAU,CAAC,IAAIzC,IAAI,CAACQ,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACvG;MACF;MACA,IAAIV,OAAO,GAAG,SAASA,OAAOA,CAACoC,CAAC,EAAE;QAChC;QACA,IAAIA,CAAC,CAACE,MAAM,CAACK,OAAO,KAAK,OAAO,IAAI3E,QAAQ,CAACoE,CAAC,CAACE,MAAM,CAAC,EAAE;UACtD;QACF;QACA7C,KAAK,CAAC8C,WAAW,CAACtC,IAAI,CAAC;QACvB;QACA,IAAIC,SAAS,GAAG0C,gBAAgB,CAAC3C,IAAI,CAAC,CAAC4C,gBAAgB,CAAC,kBAAkB,CAAC;QAC3E;QACAD,gBAAgB,CAAC3C,IAAI,CAAC,CAAC4C,gBAAgB,CAAC,cAAc,CAAC,IAAID,gBAAgB,CAAC3C,IAAI,CAAC,CAAC4C,gBAAgB,CAAC,kBAAkB,CAAC;QACtHpD,KAAK,CAACqD,kBAAkB,GAAGC,MAAM,CAACC,UAAU,CAAC,YAAY;UACvD,OAAOvD,KAAK,CAACO,OAAO,CAACC,IAAI,EAAEC,SAAS,CAAC;QACvC,CAAC,EAAE,CAAC,CAAC;QACLrC,GAAG,CAACoF,MAAM,CAACxD,KAAK,CAACyD,gBAAgB,CAAC;QAClCzD,KAAK,CAACK,cAAc,GAAG,IAAI;QAC3B;QACAL,KAAK,CAACyD,gBAAgB,GAAGrF,GAAG,CAAC,YAAY;UACvC4B,KAAK,CAACK,cAAc,GAAG,KAAK;QAC9B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACDG,IAAI,CAACgC,gBAAgB,CAAC,OAAO,EAAEjC,OAAO,EAAE,IAAI,CAAC;MAC7C,OAAO;QACLiD,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;UACxBhD,IAAI,CAACkD,mBAAmB,CAAC,OAAO,EAAEnD,OAAO,EAAE,IAAI,CAAC;QAClD;MACF,CAAC;IACH,CAAC;IACDP,KAAK,CAAC2D,UAAU,GAAG,UAAUC,IAAI,EAAE;MACjC,IAAIzB,GAAG,GAAGyB,IAAI,CAACzB,GAAG;MAClB,IAAI0B,QAAQ,GAAG7D,KAAK,CAACa,KAAK,CAACgD,QAAQ;MACnC7D,KAAK,CAACmC,GAAG,GAAGA,GAAG;MACf,IAAI,EAAE,aAAalE,KAAK,CAAC6F,cAAc,CAACD,QAAQ,CAAC,EAAE,OAAOA,QAAQ;MAClE,IAAIE,GAAG,GAAG/D,KAAK,CAACG,YAAY;MAC5B,IAAInC,UAAU,CAAC6F,QAAQ,CAAC,EAAE;QACxBE,GAAG,GAAGhG,UAAU,CAAC8F,QAAQ,CAACE,GAAG,EAAE/D,KAAK,CAACG,YAAY,CAAC;MACpD;MACA,OAAO9B,YAAY,CAACwF,QAAQ,EAAE;QAC5BE,GAAG,EAAEA;MACP,CAAC,CAAC;IACJ,CAAC;IACD,OAAO/D,KAAK;EACd;EACAtC,YAAY,CAACmC,IAAI,EAAE,CAAC;IAClBmE,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAC5D,SAAS,GAAG,KAAK;MACtB,IAAIE,IAAI,GAAG,IAAI,CAACL,YAAY,CAACyC,OAAO;MACpC,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAACjB,QAAQ,KAAK,CAAC,EAAE;QAChC;MACF;MACA,IAAI,CAAC4E,QAAQ,GAAG,IAAI,CAACnB,kBAAkB,CAACxC,IAAI,CAAC;IAC/C;EACF,CAAC,EAAE;IACDwD,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASG,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACX,MAAM,CAAC,CAAC;MACxB;MACA,IAAI,IAAI,CAACH,kBAAkB,EAAE;QAC3BgB,YAAY,CAAC,IAAI,CAAChB,kBAAkB,CAAC;MACvC;MACA,IAAI,CAAC/C,SAAS,GAAG,IAAI;IACvB;EACF,CAAC,EAAE;IACD0D,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAASvC,gBAAgBA,CAAA,EAAG;MACjC,IAAIJ,YAAY,GAAG,IAAI,CAACC,OAAO,CAACD,YAAY;MAC5C,IAAIR,eAAe,GAAG,IAAI,CAACD,KAAK,CAACC,eAAe;MAChD,OAAOA,eAAe,GAAG,EAAE,CAACU,MAAM,CAACF,YAAY,CAAC,EAAE,CAAC,EAAE,kBAAkB,CAAC,GAAG,EAAE,CAACE,MAAM,CAACF,YAAY,CAAC,EAAE,CAAC,EAAE,qCAAqC,CAAC;IAC/I;EACF,CAAC,EAAE;IACD0C,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASnB,WAAWA,CAACtC,IAAI,EAAE;MAChC,IAAI8D,MAAM,GAAG,IAAI;MACjB,IAAI,CAAC9D,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACU,SAAS,IAAI,EAAEV,IAAI,YAAY+D,OAAO,CAAC,EAAE;QAClE;MACF;MACA,IAAIzD,eAAe,GAAG,IAAI,CAACD,KAAK,CAACC,eAAe;MAChD,IAAIW,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3ClB,IAAI,CAACmB,YAAY,CAACF,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;MAC3C,IAAInD,cAAc,EAAE;QAClBA,cAAc,CAACkG,SAAS,GAAG,EAAE;MAC/B;MACA,IAAI1D,eAAe,IAAI,IAAI,CAACI,SAAS,IAAIV,IAAI,CAACiE,QAAQ,CAAC,IAAI,CAACvD,SAAS,CAAC,EAAE;QACtEV,IAAI,CAACkE,WAAW,CAAC,IAAI,CAACxD,SAAS,CAAC;MAClC;MACA,CAAC,YAAY,EAAE,WAAW,CAAC,CAACoB,OAAO,CAAC,UAAUC,IAAI,EAAE;QAClD/B,IAAI,CAACkD,mBAAmB,CAAC,EAAE,CAAClC,MAAM,CAACe,IAAI,EAAE,OAAO,CAAC,EAAE+B,MAAM,CAAC7B,iBAAiB,CAAC;QAC5EjC,IAAI,CAACkD,mBAAmB,CAAC,EAAE,CAAClC,MAAM,CAACe,IAAI,EAAE,KAAK,CAAC,EAAE+B,MAAM,CAAC5B,eAAe,CAAC;MAC1E,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDsB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASU,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAa1G,KAAK,CAACmD,aAAa,CAAClD,cAAc,EAAE,IAAI,EAAE,IAAI,CAACyF,UAAU,CAAC;IAChF;EACF,CAAC,CAAC,CAAC;EACH,OAAO9D,IAAI;AACb,CAAC,CAAC5B,KAAK,CAAC2G,SAAS,CAAC;AAClB/E,IAAI,CAACgF,WAAW,GAAG1G,aAAa;AAChC,eAAe0B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}