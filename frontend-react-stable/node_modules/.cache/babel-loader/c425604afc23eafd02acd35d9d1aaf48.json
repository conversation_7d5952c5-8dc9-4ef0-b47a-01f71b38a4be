{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Provider } from 'react-redux';\nimport { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport 'antd/dist/antd.css'; // 导入Ant Design样式\nimport { store } from './store/store';\nimport App from './App';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {\n      children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n        locale: zhCN,\n        children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 3\n}, this), document.getElementById('root'));", "map": {"version": 3, "names": ["React", "ReactDOM", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "store", "App", "jsxDEV", "_jsxDEV", "render", "StrictMode", "children", "locale", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById"], "sources": ["/home/<USER>/frontend-react-stable/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport 'antd/dist/antd.css'; // 导入Ant Design样式\nimport { store } from './store/store';\nimport App from './App';\nimport './index.css';\n\nReactDOM.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <BrowserRouter>\n        <ConfigProvider locale={zhCN}>\n          <App />\n        </ConfigProvider>\n      </BrowserRouter>\n    </Provider>\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,uBAAuB;AACxC,OAAO,oBAAoB,CAAC,CAAC;AAC7B,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErBR,QAAQ,CAACS,MAAM,cACbD,OAAA,CAACT,KAAK,CAACW,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACP,QAAQ;IAACI,KAAK,EAAEA,KAAM;IAAAM,QAAA,eACrBH,OAAA,CAACN,aAAa;MAAAS,QAAA,eACZH,OAAA,CAACL,cAAc;QAACS,MAAM,EAAER,IAAK;QAAAO,QAAA,eAC3BH,OAAA,CAACF,GAAG;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CAAC,EACnBC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}