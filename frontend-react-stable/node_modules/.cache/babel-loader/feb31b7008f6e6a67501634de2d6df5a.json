{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\nimport ActionButton from '../_util/ActionButton';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { ConfigContext } from '../config-provider';\nexport var Overlay = function Overlay(props) {\n  var prefixCls = props.prefixCls,\n    okButtonProps = props.okButtonProps,\n    cancelButtonProps = props.cancelButtonProps,\n    title = props.title,\n    cancelText = props.cancelText,\n    okText = props.okText,\n    okType = props.okType,\n    icon = props.icon,\n    _props$showCancel = props.showCancel,\n    showCancel = _props$showCancel === void 0 ? true : _props$showCancel,\n    close = props.close,\n    onConfirm = props.onConfirm,\n    onCancel = props.onCancel;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Popconfirm\",\n    defaultLocale: defaultLocale.Popconfirm\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-inner-content\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message\")\n    }, icon && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-message-icon\")\n    }, icon), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message-title\")\n    }, getRenderPropValue(title))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-buttons\")\n    }, showCancel && /*#__PURE__*/React.createElement(Button, _extends({\n      onClick: onCancel,\n      size: \"small\"\n    }, cancelButtonProps), cancelText !== null && cancelText !== void 0 ? cancelText : contextLocale.cancelText), /*#__PURE__*/React.createElement(ActionButton, {\n      buttonProps: _extends(_extends({\n        size: 'small'\n      }, convertLegacyProps(okType)), okButtonProps),\n      actionFn: onConfirm,\n      close: close,\n      prefixCls: getPrefixCls('btn'),\n      quitOnNullishReturnValue: true,\n      emitEvent: true\n    }, okText !== null && okText !== void 0 ? okText : contextLocale.okText)));\n  });\n};", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON>", "convertLegacyProps", "ActionButton", "LocaleReceiver", "defaultLocale", "getRenderPropValue", "ConfigContext", "Overlay", "props", "prefixCls", "okButtonProps", "cancelButtonProps", "title", "cancelText", "okText", "okType", "icon", "_props$showCancel", "showCancel", "close", "onConfirm", "onCancel", "_React$useContext", "useContext", "getPrefixCls", "createElement", "componentName", "Popconfirm", "contextLocale", "className", "concat", "onClick", "size", "buttonProps", "actionFn", "quitOnNullishReturnValue", "emitEvent"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/popconfirm/PurePanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\nimport ActionButton from '../_util/ActionButton';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { ConfigContext } from '../config-provider';\nexport var Overlay = function Overlay(props) {\n  var prefixCls = props.prefixCls,\n    okButtonProps = props.okButtonProps,\n    cancelButtonProps = props.cancelButtonProps,\n    title = props.title,\n    cancelText = props.cancelText,\n    okText = props.okText,\n    okType = props.okType,\n    icon = props.icon,\n    _props$showCancel = props.showCancel,\n    showCancel = _props$showCancel === void 0 ? true : _props$showCancel,\n    close = props.close,\n    onConfirm = props.onConfirm,\n    onCancel = props.onCancel;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Popconfirm\",\n    defaultLocale: defaultLocale.Popconfirm\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-inner-content\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message\")\n    }, icon && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-message-icon\")\n    }, icon), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message-title\")\n    }, getRenderPropValue(title))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-buttons\")\n    }, showCancel && /*#__PURE__*/React.createElement(Button, _extends({\n      onClick: onCancel,\n      size: \"small\"\n    }, cancelButtonProps), cancelText !== null && cancelText !== void 0 ? cancelText : contextLocale.cancelText), /*#__PURE__*/React.createElement(ActionButton, {\n      buttonProps: _extends(_extends({\n        size: 'small'\n      }, convertLegacyProps(okType)), okButtonProps),\n      actionFn: onConfirm,\n      close: close,\n      prefixCls: getPrefixCls('btn'),\n      quitOnNullishReturnValue: true,\n      emitEvent: true\n    }, okText !== null && okText !== void 0 ? okText : contextLocale.okText)));\n  });\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;IAC3CC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,iBAAiB,GAAGT,KAAK,CAACU,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;EAC3B,IAAIC,iBAAiB,GAAGvB,KAAK,CAACwB,UAAU,CAACjB,aAAa,CAAC;IACrDkB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,OAAO,aAAazB,KAAK,CAAC0B,aAAa,CAACtB,cAAc,EAAE;IACtDuB,aAAa,EAAE,YAAY;IAC3BtB,aAAa,EAAEA,aAAa,CAACuB;EAC/B,CAAC,EAAE,UAAUC,aAAa,EAAE;IAC1B,OAAO,aAAa7B,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;MAC7CI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,gBAAgB;IAClD,CAAC,EAAE,aAAaV,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;MACzCI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAEO,IAAI,IAAI,aAAajB,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;MAClDI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,eAAe;IACjD,CAAC,EAAEO,IAAI,CAAC,EAAE,aAAajB,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;MAChDI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,gBAAgB;IAClD,CAAC,EAAEJ,kBAAkB,CAACO,KAAK,CAAC,CAAC,CAAC,EAAE,aAAab,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;MACtEI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAES,UAAU,IAAI,aAAanB,KAAK,CAAC0B,aAAa,CAACzB,MAAM,EAAEF,QAAQ,CAAC;MACjEiC,OAAO,EAAEV,QAAQ;MACjBW,IAAI,EAAE;IACR,CAAC,EAAErB,iBAAiB,CAAC,EAAEE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGe,aAAa,CAACf,UAAU,CAAC,EAAE,aAAad,KAAK,CAAC0B,aAAa,CAACvB,YAAY,EAAE;MAC3J+B,WAAW,EAAEnC,QAAQ,CAACA,QAAQ,CAAC;QAC7BkC,IAAI,EAAE;MACR,CAAC,EAAE/B,kBAAkB,CAACc,MAAM,CAAC,CAAC,EAAEL,aAAa,CAAC;MAC9CwB,QAAQ,EAAEd,SAAS;MACnBD,KAAK,EAAEA,KAAK;MACZV,SAAS,EAAEe,YAAY,CAAC,KAAK,CAAC;MAC9BW,wBAAwB,EAAE,IAAI;MAC9BC,SAAS,EAAE;IACb,CAAC,EAAEtB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGc,aAAa,CAACd,MAAM,CAAC,CAAC,CAAC;EAC5E,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}