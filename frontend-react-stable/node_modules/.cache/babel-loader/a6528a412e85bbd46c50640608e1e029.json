{"ast": null, "code": "export default {\n  // Options.jsx\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination.jsx\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};", "map": {"version": 3, "names": ["items_per_page", "jump_to", "jump_to_confirm", "page", "prev_page", "next_page", "prev_5", "next_5", "prev_3", "next_3", "page_size"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-pagination/es/locale/zh_CN.js"], "sourcesContent": ["export default {\n  // Options.jsx\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination.jsx\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};"], "mappings": "AAAA,eAAe;EACb;EACAA,cAAc,EAAE,KAAK;EACrBC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,IAAI;EACrBC,IAAI,EAAE,GAAG;EACT;EACAC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}