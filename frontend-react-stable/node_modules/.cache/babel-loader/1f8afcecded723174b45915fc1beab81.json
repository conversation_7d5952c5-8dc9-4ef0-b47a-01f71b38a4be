{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Retrieves a DOM node via a ref, and does not invoke `findDOMNode`.\n */\nexport function getDOM(node) {\n  if (node && _typeof(node) === 'object' && isDOM(node.nativeElement)) {\n    return node.nativeElement;\n  }\n  if (isDOM(node)) {\n    return node;\n  }\n  return null;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  var domNode = getDOM(node);\n  if (domNode) {\n    return domNode;\n  }\n  if (node instanceof React.Component) {\n    var _ReactDOM$findDOMNode;\n    return (_ReactDOM$findDOMNode = ReactDOM.findDOMNode) === null || _ReactDOM$findDOMNode === void 0 ? void 0 : _ReactDOM$findDOMNode.call(ReactDOM, node);\n  }\n  return null;\n}", "map": {"version": 3, "names": ["_typeof", "React", "ReactDOM", "isDOM", "node", "HTMLElement", "SVGElement", "getDOM", "nativeElement", "findDOMNode", "domNode", "Component", "_ReactDOM$findDOMNode", "call"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/Dom/findDOMNode.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Retrieves a DOM node via a ref, and does not invoke `findDOMNode`.\n */\nexport function getDOM(node) {\n  if (node && _typeof(node) === 'object' && isDOM(node.nativeElement)) {\n    return node.nativeElement;\n  }\n  if (isDOM(node)) {\n    return node;\n  }\n  return null;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  var domNode = getDOM(node);\n  if (domNode) {\n    return domNode;\n  }\n  if (node instanceof React.Component) {\n    var _ReactDOM$findDOMNode;\n    return (_ReactDOM$findDOMNode = ReactDOM.findDOMNode) === null || _ReactDOM$findDOMNode === void 0 ? void 0 : _ReactDOM$findDOMNode.call(ReactDOM, node);\n  }\n  return null;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B;EACA;EACA,OAAOA,IAAI,YAAYC,WAAW,IAAID,IAAI,YAAYE,UAAU;AAClE;;AAEA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACH,IAAI,EAAE;EAC3B,IAAIA,IAAI,IAAIJ,OAAO,CAACI,IAAI,CAAC,KAAK,QAAQ,IAAID,KAAK,CAACC,IAAI,CAACI,aAAa,CAAC,EAAE;IACnE,OAAOJ,IAAI,CAACI,aAAa;EAC3B;EACA,IAAIL,KAAK,CAACC,IAAI,CAAC,EAAE;IACf,OAAOA,IAAI;EACb;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,eAAe,SAASK,WAAWA,CAACL,IAAI,EAAE;EACxC,IAAIM,OAAO,GAAGH,MAAM,CAACH,IAAI,CAAC;EAC1B,IAAIM,OAAO,EAAE;IACX,OAAOA,OAAO;EAChB;EACA,IAAIN,IAAI,YAAYH,KAAK,CAACU,SAAS,EAAE;IACnC,IAAIC,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAGV,QAAQ,CAACO,WAAW,MAAM,IAAI,IAAIG,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,IAAI,CAACX,QAAQ,EAAEE,IAAI,CAAC;EAC1J;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}