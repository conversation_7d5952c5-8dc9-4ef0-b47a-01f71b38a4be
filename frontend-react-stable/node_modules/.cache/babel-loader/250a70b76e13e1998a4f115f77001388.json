{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport var ANT_MARK = 'internalMark';\nvar LocaleProvider = function LocaleProvider(props) {\n  var _props$locale = props.locale,\n    locale = _props$locale === void 0 ? {} : _props$locale,\n    children = props.children,\n    _ANT_MARK__ = props._ANT_MARK__;\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(_ANT_MARK__ === ANT_MARK, 'LocaleProvider', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale') : void 0;\n  }\n  React.useEffect(function () {\n    changeConfirmLocale(locale && locale.Modal);\n    return function () {\n      changeConfirmLocale();\n    };\n  }, [locale]);\n  var getMemoizedContextValue = React.useMemo(function () {\n    return _extends(_extends({}, locale), {\n      exist: true\n    });\n  }, [locale]);\n  return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    value: getMemoizedContextValue\n  }, children);\n};\nexport default LocaleProvider;", "map": {"version": 3, "names": ["_extends", "React", "warning", "changeConfirmLocale", "LocaleContext", "ANT_MARK", "LocaleProvider", "props", "_props$locale", "locale", "children", "_ANT_MARK__", "process", "env", "NODE_ENV", "useEffect", "Modal", "getMemoizedContextValue", "useMemo", "exist", "createElement", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/locale-provider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport var ANT_MARK = 'internalMark';\nvar LocaleProvider = function LocaleProvider(props) {\n  var _props$locale = props.locale,\n    locale = _props$locale === void 0 ? {} : _props$locale,\n    children = props.children,\n    _ANT_MARK__ = props._ANT_MARK__;\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(_ANT_MARK__ === ANT_MARK, 'LocaleProvider', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale') : void 0;\n  }\n  React.useEffect(function () {\n    changeConfirmLocale(locale && locale.Modal);\n    return function () {\n      changeConfirmLocale();\n    };\n  }, [locale]);\n  var getMemoizedContextValue = React.useMemo(function () {\n    return _extends(_extends({}, locale), {\n      exist: true\n    });\n  }, [locale]);\n  return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    value: getMemoizedContextValue\n  }, children);\n};\nexport default LocaleProvider;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAO,IAAIC,QAAQ,GAAG,cAAc;AACpC,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,aAAa,GAAGD,KAAK,CAACE,MAAM;IAC9BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;IACtDE,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;EACjC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,OAAO,CAACS,WAAW,KAAKN,QAAQ,EAAE,gBAAgB,EAAE,+GAA+G,CAAC,GAAG,KAAK,CAAC;EACvN;EACAJ,KAAK,CAACc,SAAS,CAAC,YAAY;IAC1BZ,mBAAmB,CAACM,MAAM,IAAIA,MAAM,CAACO,KAAK,CAAC;IAC3C,OAAO,YAAY;MACjBb,mBAAmB,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACM,MAAM,CAAC,CAAC;EACZ,IAAIQ,uBAAuB,GAAGhB,KAAK,CAACiB,OAAO,CAAC,YAAY;IACtD,OAAOlB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAES,MAAM,CAAC,EAAE;MACpCU,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,OAAO,aAAaR,KAAK,CAACmB,aAAa,CAAChB,aAAa,CAACiB,QAAQ,EAAE;IAC9DC,KAAK,EAAEL;EACT,CAAC,EAAEP,QAAQ,CAAC;AACd,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}