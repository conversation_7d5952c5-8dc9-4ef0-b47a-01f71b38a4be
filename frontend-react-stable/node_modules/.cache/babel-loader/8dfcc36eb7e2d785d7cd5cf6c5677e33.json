{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.minutes = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nvar _duration = require(\"./duration.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar minute = (0, _interval.default)(function (date) {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration.durationSecond);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationMinute);\n}, function (start, end) {\n  return (end - start) / _duration.durationMinute;\n}, function (date) {\n  return date.getMinutes();\n});\nvar _default = minute;\nexports.default = _default;\nvar minutes = minute.range;\nexports.minutes = minutes;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "minutes", "default", "_interval", "_interopRequireDefault", "require", "_duration", "obj", "__esModule", "minute", "date", "setTime", "getMilliseconds", "getSeconds", "durationSecond", "step", "durationMinute", "start", "end", "getMinutes", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/minute.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.minutes = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nvar _duration = require(\"./duration.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar minute = (0, _interval.default)(function (date) {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration.durationSecond);\n}, function (date, step) {\n  date.setTime(+date + step * _duration.durationMinute);\n}, function (start, end) {\n  return (end - start) / _duration.durationMinute;\n}, function (date) {\n  return date.getMinutes();\n});\nvar _default = minute;\nexports.default = _default;\nvar minutes = minute.range;\nexports.minutes = minutes;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE1C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,IAAIE,MAAM,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACD,OAAO,EAAE,UAAUQ,IAAI,EAAE;EAClDA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,GAAGP,SAAS,CAACQ,cAAc,CAAC;AAC5F,CAAC,EAAE,UAAUJ,IAAI,EAAEK,IAAI,EAAE;EACvBL,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGK,IAAI,GAAGT,SAAS,CAACU,cAAc,CAAC;AACvD,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIX,SAAS,CAACU,cAAc;AACjD,CAAC,EAAE,UAAUN,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACS,UAAU,CAAC,CAAC;AAC1B,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGX,MAAM;AACrBV,OAAO,CAACG,OAAO,GAAGkB,QAAQ;AAC1B,IAAInB,OAAO,GAAGQ,MAAM,CAACY,KAAK;AAC1BtB,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}