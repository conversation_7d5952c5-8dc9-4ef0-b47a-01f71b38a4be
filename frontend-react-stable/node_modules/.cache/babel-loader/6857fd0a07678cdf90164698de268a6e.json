{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { RadioOptionTypeContextProvider } from './context';\nimport Radio from './radio';\nvar RadioButton = function RadioButton(props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    radioProps = __rest(props, [\"prefixCls\"]);\n  var prefixCls = getPrefixCls('radio', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(RadioOptionTypeContextProvider, {\n    value: \"button\"\n  }, /*#__PURE__*/React.createElement(Radio, _extends({\n    prefixCls: prefixCls\n  }, radioProps, {\n    type: \"radio\",\n    ref: ref\n  })));\n};\nexport default /*#__PURE__*/React.forwardRef(RadioButton);", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "ConfigContext", "RadioOptionTypeContextProvider", "Radio", "RadioButton", "props", "ref", "_React$useContext", "useContext", "getPrefixCls", "customizePrefixCls", "prefixCls", "radioProps", "createElement", "value", "type", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/radio/radioButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { RadioOptionTypeContextProvider } from './context';\nimport Radio from './radio';\nvar RadioButton = function RadioButton(props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    radioProps = __rest(props, [\"prefixCls\"]);\n  var prefixCls = getPrefixCls('radio', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(RadioOptionTypeContextProvider, {\n    value: \"button\"\n  }, /*#__PURE__*/React.createElement(Radio, _extends({\n    prefixCls: prefixCls\n  }, radioProps, {\n    type: \"radio\",\n    ref: ref\n  })));\n};\nexport default /*#__PURE__*/React.forwardRef(RadioButton);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,8BAA8B,QAAQ,WAAW;AAC1D,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,IAAIC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU,CAACP,aAAa,CAAC;IACrDQ,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;IACtCC,UAAU,GAAG1B,MAAM,CAACmB,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,IAAIM,SAAS,GAAGF,YAAY,CAAC,OAAO,EAAEC,kBAAkB,CAAC;EACzD,OAAO,aAAaV,KAAK,CAACa,aAAa,CAACX,8BAA8B,EAAE;IACtEY,KAAK,EAAE;EACT,CAAC,EAAE,aAAad,KAAK,CAACa,aAAa,CAACV,KAAK,EAAElB,QAAQ,CAAC;IAClD0B,SAAS,EAAEA;EACb,CAAC,EAAEC,UAAU,EAAE;IACbG,IAAI,EAAE,OAAO;IACbT,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAe,aAAaN,KAAK,CAACgB,UAAU,CAACZ,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}