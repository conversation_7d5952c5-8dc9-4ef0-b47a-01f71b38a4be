{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _basis = require(\"./basis.js\");\nfunction Bundle(context, beta) {\n  this._basis = new _basis.Basis(context);\n  this._beta = beta;\n}\nBundle.prototype = {\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function () {\n    var x = this._x,\n      y = this._y,\n      j = x.length - 1;\n    if (j > 0) {\n      var x0 = x[0],\n        y0 = y[0],\n        dx = x[j] - x0,\n        dy = y[j] - y0,\n        i = -1,\n        t;\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(this._beta * x[i] + (1 - this._beta) * (x0 + t * dx), this._beta * y[i] + (1 - this._beta) * (y0 + t * dy));\n      }\n    }\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\nvar _default = function custom(beta) {\n  function bundle(context) {\n    return beta === 1 ? new _basis.Basis(context) : new Bundle(context, beta);\n  }\n  bundle.beta = function (beta) {\n    return custom(+beta);\n  };\n  return bundle;\n}(0.85);\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_basis", "require", "Bundle", "context", "beta", "<PERSON><PERSON>", "_beta", "prototype", "lineStart", "_x", "_y", "lineEnd", "x", "y", "j", "length", "x0", "y0", "dx", "dy", "i", "t", "point", "push", "_default", "custom", "bundle"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/curve/bundle.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _basis = require(\"./basis.js\");\n\nfunction Bundle(context, beta) {\n  this._basis = new _basis.Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n\n    this._basis.lineStart();\n  },\n  lineEnd: function () {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n\n        this._basis.point(this._beta * x[i] + (1 - this._beta) * (x0 + t * dx), this._beta * y[i] + (1 - this._beta) * (y0 + t * dy));\n      }\n    }\n\n    this._x = this._y = null;\n\n    this._basis.lineEnd();\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n\n    this._y.push(+y);\n  }\n};\n\nvar _default = function custom(beta) {\n  function bundle(context) {\n    return beta === 1 ? new _basis.Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function (beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n}(0.85);\n\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AAElC,SAASC,MAAMA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC7B,IAAI,CAACJ,MAAM,GAAG,IAAIA,MAAM,CAACK,KAAK,CAACF,OAAO,CAAC;EACvC,IAAI,CAACG,KAAK,GAAGF,IAAI;AACnB;AAEAF,MAAM,CAACK,SAAS,GAAG;EACjBC,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,EAAE,GAAG,EAAE;IAEZ,IAAI,CAACV,MAAM,CAACQ,SAAS,CAAC,CAAC;EACzB,CAAC;EACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAIC,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAGF,CAAC,CAACG,MAAM,GAAG,CAAC;IAEpB,IAAID,CAAC,GAAG,CAAC,EAAE;MACT,IAAIE,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;QACTK,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;QACTK,EAAE,GAAGN,CAAC,CAACE,CAAC,CAAC,GAAGE,EAAE;QACdG,EAAE,GAAGN,CAAC,CAACC,CAAC,CAAC,GAAGG,EAAE;QACdG,CAAC,GAAG,CAAC,CAAC;QACNC,CAAC;MAEL,OAAO,EAAED,CAAC,IAAIN,CAAC,EAAE;QACfO,CAAC,GAAGD,CAAC,GAAGN,CAAC;QAET,IAAI,CAACd,MAAM,CAACsB,KAAK,CAAC,IAAI,CAAChB,KAAK,GAAGM,CAAC,CAACQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,KAAKU,EAAE,GAAGK,CAAC,GAAGH,EAAE,CAAC,EAAE,IAAI,CAACZ,KAAK,GAAGO,CAAC,CAACO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,KAAKW,EAAE,GAAGI,CAAC,GAAGF,EAAE,CAAC,CAAC;MAC/H;IACF;IAEA,IAAI,CAACV,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI;IAExB,IAAI,CAACV,MAAM,CAACW,OAAO,CAAC,CAAC;EACvB,CAAC;EACDW,KAAK,EAAE,SAAAA,CAAUV,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAI,CAACJ,EAAE,CAACc,IAAI,CAAC,CAACX,CAAC,CAAC;IAEhB,IAAI,CAACF,EAAE,CAACa,IAAI,CAAC,CAACV,CAAC,CAAC;EAClB;AACF,CAAC;AAED,IAAIW,QAAQ,GAAG,SAASC,MAAMA,CAACrB,IAAI,EAAE;EACnC,SAASsB,MAAMA,CAACvB,OAAO,EAAE;IACvB,OAAOC,IAAI,KAAK,CAAC,GAAG,IAAIJ,MAAM,CAACK,KAAK,CAACF,OAAO,CAAC,GAAG,IAAID,MAAM,CAACC,OAAO,EAAEC,IAAI,CAAC;EAC3E;EAEAsB,MAAM,CAACtB,IAAI,GAAG,UAAUA,IAAI,EAAE;IAC5B,OAAOqB,MAAM,CAAC,CAACrB,IAAI,CAAC;EACtB,CAAC;EAED,OAAOsB,MAAM;AACf,CAAC,CAAC,IAAI,CAAC;AAEP7B,OAAO,CAACE,OAAO,GAAGyB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}