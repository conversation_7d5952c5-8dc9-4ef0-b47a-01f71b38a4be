{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport { cloneElement } from '../_util/reactNode';\nimport scrollTo from '../_util/scrollTo';\nimport { throttleByAnimationFrame } from '../_util/throttleByAnimationFrame';\nvar BackTopContent = function BackTopContent(props) {\n  var prefixCls = props.prefixCls,\n    rootPrefixCls = props.rootPrefixCls,\n    children = props.children,\n    visible = props.visible;\n  var defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-icon\")\n  }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: \"\".concat(rootPrefixCls, \"-fade\")\n  }, function (_ref) {\n    var motionClassName = _ref.className;\n    return cloneElement(children || defaultElement, function (_ref2) {\n      var className = _ref2.className;\n      return {\n        className: classNames(motionClassName, className)\n      };\n    });\n  });\n};\nvar BackTop = function BackTop(props) {\n  var _useMergedState = useMergedState(false, {\n      value: props.visible\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    visible = _useMergedState2[0],\n    setVisible = _useMergedState2[1];\n  var ref = /*#__PURE__*/React.createRef();\n  var scrollEvent = React.useRef();\n  var getDefaultTarget = function getDefaultTarget() {\n    return ref.current && ref.current.ownerDocument ? ref.current.ownerDocument : window;\n  };\n  var handleScroll = throttleByAnimationFrame(function (e) {\n    var _props$visibilityHeig = props.visibilityHeight,\n      visibilityHeight = _props$visibilityHeig === void 0 ? 400 : _props$visibilityHeig;\n    var scrollTop = getScroll(e.target, true);\n    setVisible(scrollTop > visibilityHeight);\n  });\n  var bindScrollEvent = function bindScrollEvent() {\n    var target = props.target;\n    var getTarget = target || getDefaultTarget;\n    var container = getTarget();\n    scrollEvent.current = addEventListener(container, 'scroll', function (e) {\n      handleScroll(e);\n    });\n    handleScroll({\n      target: container\n    });\n  };\n  React.useEffect(function () {\n    bindScrollEvent();\n    return function () {\n      if (scrollEvent.current) {\n        scrollEvent.current.remove();\n      }\n      handleScroll.cancel();\n    };\n  }, [props.target]);\n  var scrollToTop = function scrollToTop(e) {\n    var onClick = props.onClick,\n      target = props.target,\n      _props$duration = props.duration,\n      duration = _props$duration === void 0 ? 450 : _props$duration;\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration: duration\n    });\n    if (typeof onClick === 'function') {\n      onClick(e);\n    }\n  };\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var classString = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  // fix https://fb.me/react-unknown-prop\n  var divProps = omit(props, ['prefixCls', 'className', 'children', 'visibilityHeight', 'target', 'visible']);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(BackTopContent, {\n    prefixCls: prefixCls,\n    rootPrefixCls: rootPrefixCls,\n    visible: visible\n  }, props.children));\n};\nexport default /*#__PURE__*/React.memo(BackTop);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "VerticalAlignTopOutlined", "classNames", "CSSMotion", "addEventListener", "useMergedState", "omit", "React", "ConfigContext", "getScroll", "cloneElement", "scrollTo", "throttleByAnimationFrame", "BackTop<PERSON><PERSON>nt", "props", "prefixCls", "rootPrefixCls", "children", "visible", "defaultElement", "createElement", "className", "concat", "motionName", "_ref", "motionClassName", "_ref2", "BackTop", "_useMergedState", "value", "_useMergedState2", "setVisible", "ref", "createRef", "scrollEvent", "useRef", "getDefaultTarget", "current", "ownerDocument", "window", "handleScroll", "e", "_props$visibilityHeig", "visibilityHeight", "scrollTop", "target", "bindScrollEvent", "get<PERSON><PERSON><PERSON>", "container", "useEffect", "remove", "cancel", "scrollToTop", "onClick", "_props$duration", "duration", "getContainer", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "_props$className", "classString", "divProps", "memo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/back-top/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport { cloneElement } from '../_util/reactNode';\nimport scrollTo from '../_util/scrollTo';\nimport { throttleByAnimationFrame } from '../_util/throttleByAnimationFrame';\nvar BackTopContent = function BackTopContent(props) {\n  var prefixCls = props.prefixCls,\n    rootPrefixCls = props.rootPrefixCls,\n    children = props.children,\n    visible = props.visible;\n  var defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-icon\")\n  }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: \"\".concat(rootPrefixCls, \"-fade\")\n  }, function (_ref) {\n    var motionClassName = _ref.className;\n    return cloneElement(children || defaultElement, function (_ref2) {\n      var className = _ref2.className;\n      return {\n        className: classNames(motionClassName, className)\n      };\n    });\n  });\n};\nvar BackTop = function BackTop(props) {\n  var _useMergedState = useMergedState(false, {\n      value: props.visible\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    visible = _useMergedState2[0],\n    setVisible = _useMergedState2[1];\n  var ref = /*#__PURE__*/React.createRef();\n  var scrollEvent = React.useRef();\n  var getDefaultTarget = function getDefaultTarget() {\n    return ref.current && ref.current.ownerDocument ? ref.current.ownerDocument : window;\n  };\n  var handleScroll = throttleByAnimationFrame(function (e) {\n    var _props$visibilityHeig = props.visibilityHeight,\n      visibilityHeight = _props$visibilityHeig === void 0 ? 400 : _props$visibilityHeig;\n    var scrollTop = getScroll(e.target, true);\n    setVisible(scrollTop > visibilityHeight);\n  });\n  var bindScrollEvent = function bindScrollEvent() {\n    var target = props.target;\n    var getTarget = target || getDefaultTarget;\n    var container = getTarget();\n    scrollEvent.current = addEventListener(container, 'scroll', function (e) {\n      handleScroll(e);\n    });\n    handleScroll({\n      target: container\n    });\n  };\n  React.useEffect(function () {\n    bindScrollEvent();\n    return function () {\n      if (scrollEvent.current) {\n        scrollEvent.current.remove();\n      }\n      handleScroll.cancel();\n    };\n  }, [props.target]);\n  var scrollToTop = function scrollToTop(e) {\n    var onClick = props.onClick,\n      target = props.target,\n      _props$duration = props.duration,\n      duration = _props$duration === void 0 ? 450 : _props$duration;\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration: duration\n    });\n    if (typeof onClick === 'function') {\n      onClick(e);\n    }\n  };\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var classString = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  // fix https://fb.me/react-unknown-prop\n  var divProps = omit(props, ['prefixCls', 'className', 'children', 'visibilityHeight', 'target', 'visible']);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(BackTopContent, {\n    prefixCls: prefixCls,\n    rootPrefixCls: rootPrefixCls,\n    visible: visible\n  }, props.children));\n};\nexport default /*#__PURE__*/React.memo(BackTop);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,qDAAqD;AAC1F,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;EACzB,IAAIC,cAAc,GAAG,aAAaZ,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IAC3DC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAE,aAAaR,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,OAAO;EACzC,CAAC,EAAE,aAAaR,KAAK,CAACa,aAAa,CAACnB,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC;EACrE,OAAO,aAAaM,KAAK,CAACa,aAAa,CAACjB,SAAS,EAAE;IACjDe,OAAO,EAAEA,OAAO;IAChBK,UAAU,EAAE,EAAE,CAACD,MAAM,CAACN,aAAa,EAAE,OAAO;EAC9C,CAAC,EAAE,UAAUQ,IAAI,EAAE;IACjB,IAAIC,eAAe,GAAGD,IAAI,CAACH,SAAS;IACpC,OAAOX,YAAY,CAACO,QAAQ,IAAIE,cAAc,EAAE,UAAUO,KAAK,EAAE;MAC/D,IAAIL,SAAS,GAAGK,KAAK,CAACL,SAAS;MAC/B,OAAO;QACLA,SAAS,EAAEnB,UAAU,CAACuB,eAAe,EAAEJ,SAAS;MAClD,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAIM,OAAO,GAAG,SAASA,OAAOA,CAACb,KAAK,EAAE;EACpC,IAAIc,eAAe,GAAGvB,cAAc,CAAC,KAAK,EAAE;MACxCwB,KAAK,EAAEf,KAAK,CAACI;IACf,CAAC,CAAC;IACFY,gBAAgB,GAAG9B,cAAc,CAAC4B,eAAe,EAAE,CAAC,CAAC;IACrDV,OAAO,GAAGY,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIE,GAAG,GAAG,aAAazB,KAAK,CAAC0B,SAAS,CAAC,CAAC;EACxC,IAAIC,WAAW,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAChC,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,OAAOJ,GAAG,CAACK,OAAO,IAAIL,GAAG,CAACK,OAAO,CAACC,aAAa,GAAGN,GAAG,CAACK,OAAO,CAACC,aAAa,GAAGC,MAAM;EACtF,CAAC;EACD,IAAIC,YAAY,GAAG5B,wBAAwB,CAAC,UAAU6B,CAAC,EAAE;IACvD,IAAIC,qBAAqB,GAAG5B,KAAK,CAAC6B,gBAAgB;MAChDA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IACnF,IAAIE,SAAS,GAAGnC,SAAS,CAACgC,CAAC,CAACI,MAAM,EAAE,IAAI,CAAC;IACzCd,UAAU,CAACa,SAAS,GAAGD,gBAAgB,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAID,MAAM,GAAG/B,KAAK,CAAC+B,MAAM;IACzB,IAAIE,SAAS,GAAGF,MAAM,IAAIT,gBAAgB;IAC1C,IAAIY,SAAS,GAAGD,SAAS,CAAC,CAAC;IAC3Bb,WAAW,CAACG,OAAO,GAAGjC,gBAAgB,CAAC4C,SAAS,EAAE,QAAQ,EAAE,UAAUP,CAAC,EAAE;MACvED,YAAY,CAACC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFD,YAAY,CAAC;MACXK,MAAM,EAAEG;IACV,CAAC,CAAC;EACJ,CAAC;EACDzC,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1BH,eAAe,CAAC,CAAC;IACjB,OAAO,YAAY;MACjB,IAAIZ,WAAW,CAACG,OAAO,EAAE;QACvBH,WAAW,CAACG,OAAO,CAACa,MAAM,CAAC,CAAC;MAC9B;MACAV,YAAY,CAACW,MAAM,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACrC,KAAK,CAAC+B,MAAM,CAAC,CAAC;EAClB,IAAIO,WAAW,GAAG,SAASA,WAAWA,CAACX,CAAC,EAAE;IACxC,IAAIY,OAAO,GAAGvC,KAAK,CAACuC,OAAO;MACzBR,MAAM,GAAG/B,KAAK,CAAC+B,MAAM;MACrBS,eAAe,GAAGxC,KAAK,CAACyC,QAAQ;MAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,eAAe;IAC/D3C,QAAQ,CAAC,CAAC,EAAE;MACV6C,YAAY,EAAEX,MAAM,IAAIT,gBAAgB;MACxCmB,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;MACjCA,OAAO,CAACZ,CAAC,CAAC;IACZ;EACF,CAAC;EACD,IAAIgB,iBAAiB,GAAGlD,KAAK,CAACmD,UAAU,CAAClD,aAAa,CAAC;IACrDmD,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,kBAAkB,GAAG/C,KAAK,CAACC,SAAS;IACtC+C,gBAAgB,GAAGhD,KAAK,CAACO,SAAS;IAClCA,SAAS,GAAGyC,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;EACjE,IAAI/C,SAAS,GAAG4C,YAAY,CAAC,UAAU,EAAEE,kBAAkB,CAAC;EAC5D,IAAI7C,aAAa,GAAG2C,YAAY,CAAC,CAAC;EAClC,IAAII,WAAW,GAAG7D,UAAU,CAACa,SAAS,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuB,MAAM,CAACP,SAAS,EAAE,MAAM,CAAC,EAAE6C,SAAS,KAAK,KAAK,CAAC,EAAEvC,SAAS,CAAC;EAC1H;EACA,IAAI2C,QAAQ,GAAG1D,IAAI,CAACQ,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;EAC3G,OAAO,aAAaP,KAAK,CAACa,aAAa,CAAC,KAAK,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEkE,QAAQ,EAAE;IACpE3C,SAAS,EAAE0C,WAAW;IACtBV,OAAO,EAAED,WAAW;IACpBpB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAazB,KAAK,CAACa,aAAa,CAACP,cAAc,EAAE;IACnDE,SAAS,EAAEA,SAAS;IACpBC,aAAa,EAAEA,aAAa;IAC5BE,OAAO,EAAEA;EACX,CAAC,EAAEJ,KAAK,CAACG,QAAQ,CAAC,CAAC;AACrB,CAAC;AACD,eAAe,aAAaV,KAAK,CAAC0D,IAAI,CAACtC,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}