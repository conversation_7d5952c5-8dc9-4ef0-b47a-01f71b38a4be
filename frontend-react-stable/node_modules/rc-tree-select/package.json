{"name": "rc-tree-select", "version": "5.5.5", "description": "tree-select ui component for react", "keywords": ["react", "react-component", "react-tree-select", "tree-select"], "homepage": "https://github.com/react-component/tree-select", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/tree-select.git"}, "bugs": {"url": "https://github.com/react-component/tree-select/issues"}, "files": ["es", "lib", "dist", "assets/*.less", "assets/*.css", "assets/*.png", "assets/*.gif"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "lint": "eslint src/ examples/ --ext .tsx,.ts,.jsx,.js", "prettier": "prettier '{src,tests}/**/*.{ts,tsx}' 'tests/**/*.js' --write", "test": "rc-test", "now-build": "npm run build"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/react": "^12.0.0", "@types/jest": "^26.0.5", "@types/react": "^16.8.19", "@types/react-dom": "^16.8.4", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.2", "dumi": "^1.1.12", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.1.1", "enzyme-to-json": "^3.4.0", "eslint": "^7.1.0", "father": "^4.0.0", "glob": "^7.1.6", "np": "^6.2.0", "rc-dialog": "^7.5.7", "rc-field-form": "^1.0.0", "rc-test": "^7.0.4", "rc-trigger": "^4.0.0", "rc-virtual-list": "^1.1.0", "react": "^16.8.0", "react-dom": "^16.8.0", "typescript": "^3.5.2"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-select": "~14.1.0", "rc-tree": "~5.7.0", "rc-util": "^5.16.1"}}