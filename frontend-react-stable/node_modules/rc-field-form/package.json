{"name": "rc-field-form", "version": "1.27.4", "description": "React Form Component", "typings": "es/index.d.ts", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-form", "form"], "homepage": "https://github.com/react-component/field-form", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/field-form.git"}, "bugs": {"url": "https://github.com/react-component/field-form/issues"}, "files": ["lib", "es", "dist", "assets/*.css"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father-build", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "father test", "test:coverage": "umi-test --coverage", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.18.0", "async-validator": "^4.1.0", "rc-util": "^5.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^12.1.5", "@types/enzyme": "^3.10.5", "@types/jest": "^29.2.5", "@types/lodash": "^4.14.135", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.5.2", "@umijs/test": "^3.2.27", "dumi": "^1.1.0", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.0.2", "enzyme-to-json": "^3.1.4", "eslint": "^7.18.0", "father": "^2.13.6", "father-build": "^1.18.6", "gh-pages": "^3.1.0", "jest": "^29.3.1", "np": "^5.0.3", "prettier": "^2.1.2", "react": "^16.14.0", "react-dnd": "^8.0.3", "react-dnd-html5-backend": "^8.0.3", "react-dom": "^16.14.0", "react-redux": "^4.4.10", "redux": "^3.7.2", "typescript": "^4.6.3"}}