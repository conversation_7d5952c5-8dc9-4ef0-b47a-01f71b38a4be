{"name": "rc-select", "version": "14.1.18", "description": "React Select", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-select", "select"], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "files": ["assets/*.css", "assets/*.less", "es", "lib"], "homepage": "http://github.com/react-component/select", "repository": {"type": "git", "url": "**************:react-component/select.git"}, "bugs": {"url": "http://github.com/react-component/select/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch 14.1.x", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "tsc": "tsc --noEmit", "now-build": "npm run build"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-overflow": "^1.0.0", "rc-trigger": "^5.0.4", "rc-util": "^5.16.1", "rc-virtual-list": "^3.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@types/enzyme": "^3.10.9", "@types/jest": "^26.0.24", "@types/react": "^17.0.15", "@types/react-dom": "^17.0.3", "cross-env": "^7.0.0", "dumi": "^1.1.32", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.4.0", "eslint": "^7.1.0", "father": "^4.0.0", "jsonp": "^0.2.1", "np": "^7.5.0", "prettier": "^2.7.1", "rc-dialog": "^9.0.0", "rc-test": "^7.0.9", "typescript": "^4.2.3"}}