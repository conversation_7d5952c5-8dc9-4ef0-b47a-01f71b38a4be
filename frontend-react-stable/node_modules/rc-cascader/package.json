{"name": "rc-cascader", "version": "3.7.3", "description": "cascade select ui component for react", "keywords": ["react", "react-component", "react-cascader", "react-select", "select", "cascade", "cascader"], "homepage": "https://github.com/react-component/cascader", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/cascader.git"}, "bugs": {"url": "https://github.com/react-component/cascader/issues"}, "files": ["lib", "es", "assets/*.css", "assets/*.less"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "deploy": "UMI_ENV=gh npm run build && gh-pages -d .doc", "compile": "father build", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish --any-branch", "lint": "eslint src/ examples/ --ext .tsx,.ts,.jsx,.jsx", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run build"}, "devDependencies": {"@types/classnames": "^2.2.6", "@types/enzyme": "^3.1.15", "@types/jest": "^27.0.2", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "@types/warning": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^2.1.10", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.0.2", "enzyme-to-json": "^3.2.1", "father": "^2.13.2", "gh-pages": "^3.1.0", "glob": "^7.1.6", "np": "^7.6.0", "prettier": "^2.7.1", "rc-form": "^2.4.0", "rc-trigger": "^5.0.4", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^4.4.2", "less": "^3.13.1"}, "dependencies": {"@babel/runtime": "^7.12.5", "array-tree-filter": "^2.1.0", "classnames": "^2.3.1", "rc-select": "~14.1.0", "rc-tree": "~5.7.0", "rc-util": "^5.6.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}