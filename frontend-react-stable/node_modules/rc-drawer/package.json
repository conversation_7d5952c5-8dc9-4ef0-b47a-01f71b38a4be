{"name": "rc-drawer", "version": "6.0.3", "description": "drawer component for react", "keywords": ["react", "react-component", "react-drawer", "drawer", "drawer-menu", "rc-drawer-menu", "react-drawer-menu", "animation", "drawer-motion", "drawer-animation"], "homepage": "https://github.com/react-component/drawer", "author": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/react-component/drawer.git"}, "bugs": {"url": "https://github.com/react-component/drawer/issues"}, "files": ["lib", "assets/*.css", "es"], "licenses": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father-build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "test": "umi-test", "now-build": "npm run build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-6", "classnames": "^2.2.6", "rc-motion": "^2.6.1", "rc-util": "^5.21.2"}, "devDependencies": {"@ant-design/icons": "^4.7.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^12.1.5", "@types/classnames": "^2.2.9", "@types/jest": "^27.0.2", "@types/raf": "^3.4.0", "@types/react": "^17.0.9", "@types/react-dom": "^17.0.6", "@types/warning": "^3.0.0", "@umijs/fabric": "^2.0.0", "@umijs/test": "^3.5.23", "antd": "^4.20.2", "dumi": "^1.1.40", "eslint": "^7.0.0", "father": "^2.30.21", "father-build": "^1.22.1", "glob": "^7.1.6", "less": "^3.10.3", "np": "^7.5.0", "prettier": "^2.6.2", "react": "^16.10.2", "react-dom": "^16.10.2", "typescript": "^4.6.4"}}