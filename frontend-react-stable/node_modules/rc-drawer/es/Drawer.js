import _extends from "@babel/runtime/helpers/esm/extends";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import * as React from 'react';
import Portal from '@rc-component/portal';
import DrawerPopup from './DrawerPopup';
import { warnCheck } from './util';
var Drawer = function Drawer(props) {
  var open = props.open,
    getContainer = props.getContainer,
    forceRender = props.forceRender,
    prefixCls = props.prefixCls,
    afterOpenChange = props.afterOpenChange,
    destroyOnClose = props.destroyOnClose,
    mask = props.mask;
  var _React$useState = React.useState(false),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    animatedVisible = _React$useState2[0],
    setAnimatedVisible = _React$useState2[1];
  // ============================= Warn =============================
  if (process.env.NODE_ENV !== 'production') {
    warnCheck(props);
  }
  // ============================= Open =============================
  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {
    setAnimatedVisible(nextVisible);
    afterOpenChange === null || afterOpenChange === void 0 ? void 0 : afterOpenChange(nextVisible);
  };
  // ============================ Render ============================
  if (!forceRender && !animatedVisible && !open && destroyOnClose) {
    return null;
  }
  var sharedDrawerProps = _objectSpread(_objectSpread({}, props), {}, {
    prefixCls: prefixCls,
    afterOpenChange: internalAfterOpenChange
  });
  return /*#__PURE__*/React.createElement(Portal, {
    open: open || forceRender || animatedVisible,
    autoDestroy: false,
    getContainer: getContainer,
    autoLock: mask && (open || animatedVisible)
  }, /*#__PURE__*/React.createElement(DrawerPopup, _extends({}, sharedDrawerProps, {
    inline: getContainer === false
  })));
};
// Default Value.
// Since spread with default value will make this all over components.
// Let's maintain this in one place.
Drawer.defaultProps = {
  open: false,
  prefixCls: 'rc-drawer',
  placement: 'right',
  autoFocus: true,
  keyboard: true,
  width: 378,
  mask: true,
  maskClosable: true
};
if (process.env.NODE_ENV !== 'production') {
  Drawer.displayName = 'Drawer';
}
export default Drawer;