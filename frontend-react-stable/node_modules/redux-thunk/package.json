{"name": "redux-thunk", "version": "2.4.2", "license": "MIT", "description": "Thunk middleware for Redux.", "repository": "github:reduxjs/redux-thunk", "bugs": "https://github.com/reduxjs/redux-thunk/issues", "homepage": "https://github.com/reduxjs/redux-thunk", "keywords": ["redux", "thunk", "middleware", "redux-middleware", "flux"], "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "sideEffects": false, "files": ["lib", "es", "src", "dist", "extend-redux.d.ts"], "scripts": {"clean": "<PERSON><PERSON>f lib dist es", "prepublishOnly": "npm run clean && npm run lint && npm run test && npm run build", "format": "prettier --write {src,test,typescript_test}/**/*.{js,ts}", "format:check": "prettier --check {src,test,typescript_test}/**/*.{js,ts}", "lint": "eslint {src,test,typescript_test}/**/*.{js,ts}", "test": "jest", "test:cov": "jest --coverage", "test:typescript": "npm run test:typescript:main && npm run test:typescript:extended", "test:typescript:main": "tsc --noEmit -p typescript_test/tsconfig.json", "test:typescript:extended": "tsc --noEmit -p typescript_test/typescript_extended/tsconfig.json", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir lib ", "build:es": "babel src/*.ts --ignore src/types.ts --extensions .ts --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/redux-thunk.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/redux-thunk.min.js", "build:types": "tsc", "build": "rimraf dist lib es && npm run build:types && npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "api-types": "api-extractor run --local"}, "peerDependencies": {"redux": "^4"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@babel/register": "^7.15.3", "@microsoft/api-extractor": "^7.18.16", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.3.1", "prettier": "^2.4.1", "redux": "^4", "rimraf": "^3.0.2", "rollup": "^2.58.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "27.0.7", "typescript": "^4.4"}}