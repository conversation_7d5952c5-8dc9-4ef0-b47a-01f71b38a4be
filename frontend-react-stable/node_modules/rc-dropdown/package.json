{"name": "rc-dropdown", "version": "4.0.1", "description": "dropdown ui component for react", "keywords": ["react", "react-dropdown"], "homepage": "http://github.com/react-component/dropdown", "maintainers": ["<EMAIL>", "<EMAIL>"], "repository": {"type": "git", "url": "**************:react-component/dropdown.git"}, "bugs": {"url": "http://github.com/react-component/dropdown/issues"}, "files": ["lib", "es", "assets/*.css"], "main": "lib/index", "module": "./es/index", "license": "MIT", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run build"}, "devDependencies": {"@types/classnames": "^2.2.6", "@types/enzyme": "^3.1.15", "@types/jest": "^26.0.12", "@types/react": "^16.8.19", "@types/react-dom": "^16.8.4", "@types/warning": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.38", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.0.2", "enzyme-to-json": "^3.4.0", "father": "^2.13.2", "jquery": "^3.3.1", "less": "^3.11.1", "np": "^6.0.0", "rc-menu": "^9.5.2", "react": "^16.11.0", "react-dom": "^16.11.0", "regenerator-runtime": "^0.13.9", "typescript": "^4.0.2"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}, "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.6", "rc-trigger": "^5.3.1", "rc-util": "^5.17.0"}}