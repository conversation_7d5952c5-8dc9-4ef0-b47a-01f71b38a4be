{"name": "rc-input", "version": "0.1.4", "description": "React input component", "keywords": ["react", "react-component", "react-input", "input", "antd", "ant-design"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib", "dist"], "homepage": "https://github.com/react-component/input", "repository": {"type": "git", "url": "**************:react-component/input.git"}, "bugs": {"url": "http://github.com/react-component/input/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "gh-pages": "npm run docs:build && npm run docs:deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "pretty-quick": "pretty-quick", "lint-staged": "lint-staged", "test": "umi-test test", "coverage": "father test --coverage", "prepare": "husky install"}, "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.18.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^14.0.0-beta", "@types/classnames": "^2.2.9", "@types/jest": "^26.0.24", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "@umijs/fabric": "^2.0.8", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "dumi": "^1.1.0", "eslint": "^7.0.0", "father": "^4", "gh-pages": "^3.1.0", "husky": "^8.0.1", "less": "^3.10.3", "lint-staged": "^13.0.3", "np": "^7.0.0", "prettier": "^2.0.5", "pretty-quick": "^3.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.0.5", "umi-test": "^1.9.7"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}}