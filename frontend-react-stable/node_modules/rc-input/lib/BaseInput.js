"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _typeof3 = require("@babel/runtime/helpers/typeof");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));

var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));

var _react = _interopRequireWildcard(require("react"));

var _classnames = _interopRequireDefault(require("classnames"));

var _commonUtils = require("./utils/commonUtils");

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof3(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

var BaseInput = function BaseInput(props) {
  var inputElement = props.inputElement,
      prefixCls = props.prefixCls,
      prefix = props.prefix,
      suffix = props.suffix,
      addonBefore = props.addonBefore,
      addonAfter = props.addonAfter,
      className = props.className,
      style = props.style,
      affixWrapperClassName = props.affixWrapperClassName,
      groupClassName = props.groupClassName,
      wrapperClassName = props.wrapperClassName,
      disabled = props.disabled,
      readOnly = props.readOnly,
      focused = props.focused,
      triggerFocus = props.triggerFocus,
      allowClear = props.allowClear,
      value = props.value,
      handleReset = props.handleReset,
      hidden = props.hidden;
  var containerRef = (0, _react.useRef)(null);

  var onInputClick = function onInputClick(e) {
    var _containerRef$current;

    if ((_containerRef$current = containerRef.current) !== null && _containerRef$current !== void 0 && _containerRef$current.contains(e.target)) {
      triggerFocus === null || triggerFocus === void 0 ? void 0 : triggerFocus();
    }
  }; // ================== Clear Icon ================== //


  var getClearIcon = function getClearIcon() {
    var _classNames;

    if (!allowClear) {
      return null;
    }

    var needClear = !disabled && !readOnly && value;
    var clearIconCls = "".concat(prefixCls, "-clear-icon");
    var iconNode = (0, _typeof2.default)(allowClear) === 'object' && allowClear !== null && allowClear !== void 0 && allowClear.clearIcon ? allowClear.clearIcon : '✖';
    return /*#__PURE__*/_react.default.createElement("span", {
      onClick: handleReset // Do not trigger onBlur when clear input
      // https://github.com/ant-design/ant-design/issues/31200
      ,
      onMouseDown: function onMouseDown(e) {
        return e.preventDefault();
      },
      className: (0, _classnames.default)(clearIconCls, (_classNames = {}, (0, _defineProperty2.default)(_classNames, "".concat(clearIconCls, "-hidden"), !needClear), (0, _defineProperty2.default)(_classNames, "".concat(clearIconCls, "-has-suffix"), !!suffix), _classNames)),
      role: "button",
      tabIndex: -1
    }, iconNode);
  };

  var element = /*#__PURE__*/(0, _react.cloneElement)(inputElement, {
    value: value,
    hidden: hidden
  }); // ================== Prefix & Suffix ================== //

  if ((0, _commonUtils.hasPrefixSuffix)(props)) {
    var _classNames2;

    var affixWrapperPrefixCls = "".concat(prefixCls, "-affix-wrapper");
    var affixWrapperCls = (0, _classnames.default)(affixWrapperPrefixCls, (_classNames2 = {}, (0, _defineProperty2.default)(_classNames2, "".concat(affixWrapperPrefixCls, "-disabled"), disabled), (0, _defineProperty2.default)(_classNames2, "".concat(affixWrapperPrefixCls, "-focused"), focused), (0, _defineProperty2.default)(_classNames2, "".concat(affixWrapperPrefixCls, "-readonly"), readOnly), (0, _defineProperty2.default)(_classNames2, "".concat(affixWrapperPrefixCls, "-input-with-clear-btn"), suffix && allowClear && value), _classNames2), !(0, _commonUtils.hasAddon)(props) && className, affixWrapperClassName);

    var suffixNode = (suffix || allowClear) && /*#__PURE__*/_react.default.createElement("span", {
      className: "".concat(prefixCls, "-suffix")
    }, getClearIcon(), suffix);

    element = /*#__PURE__*/_react.default.createElement("span", {
      className: affixWrapperCls,
      style: style,
      hidden: !(0, _commonUtils.hasAddon)(props) && hidden,
      onClick: onInputClick,
      ref: containerRef
    }, prefix && /*#__PURE__*/_react.default.createElement("span", {
      className: "".concat(prefixCls, "-prefix")
    }, prefix), /*#__PURE__*/(0, _react.cloneElement)(inputElement, {
      style: null,
      value: value,
      hidden: null
    }), suffixNode);
  } // ================== Addon ================== //


  if ((0, _commonUtils.hasAddon)(props)) {
    var wrapperCls = "".concat(prefixCls, "-group");
    var addonCls = "".concat(wrapperCls, "-addon");
    var mergedWrapperClassName = (0, _classnames.default)("".concat(prefixCls, "-wrapper"), wrapperCls, wrapperClassName);
    var mergedGroupClassName = (0, _classnames.default)("".concat(prefixCls, "-group-wrapper"), className, groupClassName); // Need another wrapper for changing display:table to display:inline-block
    // and put style prop in wrapper

    return /*#__PURE__*/_react.default.createElement("span", {
      className: mergedGroupClassName,
      style: style,
      hidden: hidden
    }, /*#__PURE__*/_react.default.createElement("span", {
      className: mergedWrapperClassName
    }, addonBefore && /*#__PURE__*/_react.default.createElement("span", {
      className: addonCls
    }, addonBefore), /*#__PURE__*/(0, _react.cloneElement)(element, {
      style: null,
      hidden: null
    }), addonAfter && /*#__PURE__*/_react.default.createElement("span", {
      className: addonCls
    }, addonAfter)));
  }

  return element;
};

var _default = BaseInput;
exports.default = _default;