import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _typeof from "@babel/runtime/helpers/esm/typeof";
import React, { cloneElement, useRef } from 'react';
import classNames from 'classnames';
import { hasAddon, hasPrefixSuffix } from "./utils/commonUtils";

var BaseInput = function BaseInput(props) {
  var inputElement = props.inputElement,
      prefixCls = props.prefixCls,
      prefix = props.prefix,
      suffix = props.suffix,
      addonBefore = props.addonBefore,
      addonAfter = props.addonAfter,
      className = props.className,
      style = props.style,
      affixWrapperClassName = props.affixWrapperClassName,
      groupClassName = props.groupClassName,
      wrapperClassName = props.wrapperClassName,
      disabled = props.disabled,
      readOnly = props.readOnly,
      focused = props.focused,
      triggerFocus = props.triggerFocus,
      allowClear = props.allowClear,
      value = props.value,
      handleReset = props.handleReset,
      hidden = props.hidden;
  var containerRef = useRef(null);

  var onInputClick = function onInputClick(e) {
    var _containerRef$current;

    if ((_containerRef$current = containerRef.current) !== null && _containerRef$current !== void 0 && _containerRef$current.contains(e.target)) {
      triggerFocus === null || triggerFocus === void 0 ? void 0 : triggerFocus();
    }
  }; // ================== Clear Icon ================== //


  var getClearIcon = function getClearIcon() {
    var _classNames;

    if (!allowClear) {
      return null;
    }

    var needClear = !disabled && !readOnly && value;
    var clearIconCls = "".concat(prefixCls, "-clear-icon");
    var iconNode = _typeof(allowClear) === 'object' && allowClear !== null && allowClear !== void 0 && allowClear.clearIcon ? allowClear.clearIcon : '✖';
    return /*#__PURE__*/React.createElement("span", {
      onClick: handleReset // Do not trigger onBlur when clear input
      // https://github.com/ant-design/ant-design/issues/31200
      ,
      onMouseDown: function onMouseDown(e) {
        return e.preventDefault();
      },
      className: classNames(clearIconCls, (_classNames = {}, _defineProperty(_classNames, "".concat(clearIconCls, "-hidden"), !needClear), _defineProperty(_classNames, "".concat(clearIconCls, "-has-suffix"), !!suffix), _classNames)),
      role: "button",
      tabIndex: -1
    }, iconNode);
  };

  var element = /*#__PURE__*/cloneElement(inputElement, {
    value: value,
    hidden: hidden
  }); // ================== Prefix & Suffix ================== //

  if (hasPrefixSuffix(props)) {
    var _classNames2;

    var affixWrapperPrefixCls = "".concat(prefixCls, "-affix-wrapper");
    var affixWrapperCls = classNames(affixWrapperPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, "".concat(affixWrapperPrefixCls, "-disabled"), disabled), _defineProperty(_classNames2, "".concat(affixWrapperPrefixCls, "-focused"), focused), _defineProperty(_classNames2, "".concat(affixWrapperPrefixCls, "-readonly"), readOnly), _defineProperty(_classNames2, "".concat(affixWrapperPrefixCls, "-input-with-clear-btn"), suffix && allowClear && value), _classNames2), !hasAddon(props) && className, affixWrapperClassName);
    var suffixNode = (suffix || allowClear) && /*#__PURE__*/React.createElement("span", {
      className: "".concat(prefixCls, "-suffix")
    }, getClearIcon(), suffix);
    element = /*#__PURE__*/React.createElement("span", {
      className: affixWrapperCls,
      style: style,
      hidden: !hasAddon(props) && hidden,
      onClick: onInputClick,
      ref: containerRef
    }, prefix && /*#__PURE__*/React.createElement("span", {
      className: "".concat(prefixCls, "-prefix")
    }, prefix), /*#__PURE__*/cloneElement(inputElement, {
      style: null,
      value: value,
      hidden: null
    }), suffixNode);
  } // ================== Addon ================== //


  if (hasAddon(props)) {
    var wrapperCls = "".concat(prefixCls, "-group");
    var addonCls = "".concat(wrapperCls, "-addon");
    var mergedWrapperClassName = classNames("".concat(prefixCls, "-wrapper"), wrapperCls, wrapperClassName);
    var mergedGroupClassName = classNames("".concat(prefixCls, "-group-wrapper"), className, groupClassName); // Need another wrapper for changing display:table to display:inline-block
    // and put style prop in wrapper

    return /*#__PURE__*/React.createElement("span", {
      className: mergedGroupClassName,
      style: style,
      hidden: hidden
    }, /*#__PURE__*/React.createElement("span", {
      className: mergedWrapperClassName
    }, addonBefore && /*#__PURE__*/React.createElement("span", {
      className: addonCls
    }, addonBefore), /*#__PURE__*/cloneElement(element, {
      style: null,
      hidden: null
    }), addonAfter && /*#__PURE__*/React.createElement("span", {
      className: addonCls
    }, addonAfter)));
  }

  return element;
};

export default BaseInput;