{"name": "rc-dialog", "version": "9.0.4", "description": "dialog ui component for react", "keywords": ["react", "react-component", "react-dialog", "dialog", "ui"], "homepage": "http://github.com/react-component/dialog", "author": "<EMAIL>", "repository": {"type": "git", "url": "**************:react-component/dialog.git"}, "bugs": {"url": "http://github.com/react-component/dialog/issues"}, "license": "MIT", "files": ["lib", "es", "assets/*.css", "dist"], "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css && lessc assets/bootstrap.less assets/bootstrap.css", "deploy": "npm run docs:build && npm run docs:deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch=9.0.x", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "lint:tsc": "tsc -p tsconfig.json --noEmit", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-8", "classnames": "^2.2.6", "rc-motion": "^2.3.0", "rc-util": "^5.21.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.0.0", "@types/enzyme": "^3.10.7", "@types/jest": "^26.0.14", "@types/keyv": "3.1.4", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@umijs/fabric": "^3.0.0", "bluebird": "~3.7.2", "bootstrap": "^4.3.1", "cross-env": "^7.0.0", "dumi": "^1.1.0", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-react": "^7.20.6", "father": "^2.29.6", "father-build": "^1.18.6", "gh-pages": "^4.0.0", "glob": "^7.1.6", "jquery": "^3.3.1", "less": "^3.12.2", "np": "^7.0.0", "prettier": "^2.1.1", "rc-drawer": "^4.4.0", "rc-select": "^12.1.13", "react": "^16.9.0", "react-dom": "^16.9.0", "react-draggable": "^4.4.3", "typescript": "^4.0.2"}}