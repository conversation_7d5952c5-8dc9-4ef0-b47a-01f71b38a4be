{"name": "@ant-design/colors", "version": "6.0.0", "description": "Color palettes calculator of Ant Design", "main": "dist/index.js", "module": "dist/index.esm.js", "files": ["lib", "dist/index.js", "dist/index.esm.js", "dist/src/index.d.ts", "dist/src/generate.d.ts"], "typings": "dist/src/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-colors.git"}, "bugs": {"url": "https://github.com/ant-design/ant-design-colors/issues"}, "scripts": {"tsc": "tsc --noEmit", "compile": "father build --esm --cjs", "prepublishOnly": "npm run compile && np --no-cleanup --no-publish", "lint": "eslint src --ext .ts", "lint:fix": "prettier --write '{src,tests}/**/*.ts'", "jest": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls", "test": "npm run tsc && npm run lint && npm run jest"}, "author": "afc163 <<EMAIL>>", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.7.0", "coveralls": "^3.0.3", "eslint": "^7.1.0", "eslint-config-prettier": "^7.0.0", "eslint-plugin-prettier": "^3.0.1", "father": "^2.29.5", "jest": "^26.0.1", "np": "^7.0.0", "prettier": "^2.0.0", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "homepage": "https://github.com/ant-design/ant-design-colors#readme"}