{"name": "@ant-design/react-slick", "version": "0.29.2", "description": " React port of slick carousel", "main": "./lib", "module": "./es", "types": "./types.d.ts", "files": ["dist", "lib", "es", "types.d.ts"], "scripts": {"start": "gulp server", "demo": "SINGLE_DEMO=true gulp server", "build-dev": "gulp clean && gulp copy && webpack", "lib": "babel ./src --out-dir ./lib", "es": "ESMODULES=true babel ./src --out-dir ./es", "build": "npm run lib && npm run es && gulp dist", "prepublishOnly": "npm run build", "test": "npm run lint && jest", "test:watch": "jest --watch", "lint": "eslint src", "gen": "node examples/scripts/generateExampleConfigs.js && node examples/scripts/generateExamples.js && xdg-open docs/jquery.html", "precommit": "lint-staged"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ant-design/react-slick"}, "keywords": ["slick", "carousel", "Image slider", "orbit", "slider", "react-component"], "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "autoprefixer": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.4", "babel-preset-airbnb": "^5.0.0", "css-loader": "^5.0.0", "deepmerge": "^1.1.0", "del": "^2.2.2", "enzyme": "^3.2.0", "enzyme-adapter-react-16": "^1.1.0", "es5-shim": "^4.5.9", "eslint": "^8.4.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-react": "^7.27.1", "express": "^4.14.0", "foundation-apps": "^1.2.0", "gulp": "^4.0.0", "husky": "^0.14.3", "jasmine-core": "^2.5.2", "jest": "^26.4.2", "jquery": "^3.2.1", "js-beautify": "^1.7.5", "json-loader": "^0.5.4", "lint-staged": "^12.1.2", "opn": "^5.4.0", "postcss-loader": "^1.3.3", "prettier": "^2.1.2", "raf": "^3.4.0", "react": "^16.0.0", "react-dom": "^16.0.0", "react-test-renderer": "^16.0.0", "sinon": "^2.1.0", "slick-carousel": "^1.8.1", "style-loader": "^0.16.1", "uglifyjs-webpack-plugin": "^2.0.1", "webpack": "^4.21.0", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.9", "why-did-you-update": "^0.1.1"}, "dependencies": {"@babel/runtime": "^7.10.4", "classnames": "^2.2.5", "json2mq": "^0.2.0", "lodash": "^4.17.21", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">=16.9.0"}, "jest": {"setupFiles": ["./test-setup.js"], "testPathIgnorePatterns": ["/__tests__/scripts.js", "/__tests__/testUtils.js"]}, "lint-staged": {"*.{js,json,md}": ["prettier --write", "git add"]}, "npmName": "react-slick", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js"]}], "bugs": {"url": "https://github.com/ant-design/react-slick/issues"}, "homepage": "https://react-slick.neostack.com", "collective": {"type": "opencollective", "url": "https://opencollective.com/react-slick", "logo": "https://opencollective.com/opencollective/logo.txt"}, "publishConfig": {"access": "public"}}