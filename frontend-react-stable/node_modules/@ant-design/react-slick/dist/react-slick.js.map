{"version": 3, "sources": ["webpack://Slider/webpack/universalModuleDefinition", "webpack://Slider/webpack/bootstrap", "webpack://Slider/./src/index.js", "webpack://Slider/./src/slider.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/extends.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/objectSpread2.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/defineProperty.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/classCallCheck.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/createClass.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/assertThisInitialized.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/inherits.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/setPrototypeOf.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/createSuper.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/getPrototypeOf.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/isNativeReflectConstruct.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/possibleConstructorReturn.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/typeof.js", "webpack://Slider/external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}", "webpack://Slider/./src/inner-slider.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/objectWithoutProperties.js", "webpack://Slider/./node_modules/_@babel_runtime@7.18.3@@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "webpack://Slider/./src/initial-state.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/debounce.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/isObject.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/now.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_root.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_freeGlobal.js", "webpack://Slider/(webpack)/buildin/global.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/toNumber.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_baseTrim.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_trimmedEndIndex.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/isSymbol.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_baseGetTag.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_Symbol.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_getRawTag.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/_objectToString.js", "webpack://Slider/./node_modules/_lodash@4.17.21@lodash/isObjectLike.js", "webpack://Slider/./node_modules/_classnames@2.3.1@classnames/index.js", "webpack://Slider/./src/utils/innerSliderUtils.js", "webpack://Slider/./src/track.js", "webpack://Slider/./src/dots.js", "webpack://Slider/./src/arrows.js", "webpack://Slider/./node_modules/_resize-observer-polyfill@1.5.1@resize-observer-polyfill/dist/ResizeObserver.es.js", "webpack://Slider/./node_modules/_json2mq@0.2.0@json2mq/index.js", "webpack://Slider/./node_modules/_string-convert@0.2.1@string-convert/camel2hyphen.js", "webpack://Slider/./src/default-props.js"], "names": ["Slide<PERSON>", "props", "ref", "innerSlider", "slick<PERSON>rev", "slickNext", "slide", "dontAnimate", "slickGoTo", "pause", "autoPlay", "state", "breakpoint", "_responsiveMediaHandlers", "query", "handler", "mql", "window", "matchMedia", "listener", "matches", "addListener", "push", "responsive", "breakpoints", "map", "breakpt", "sort", "x", "y", "for<PERSON>ach", "index", "b<PERSON><PERSON><PERSON>", "json2mq", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "canUseDOM", "media", "setState", "slice", "obj", "removeListener", "settings", "newProps", "filter", "resp", "defaultProps", "centerMode", "slidesToScroll", "process", "console", "warn", "fade", "slidesToShow", "children", "React", "Children", "toArray", "child", "trim", "variableWidth", "rows", "slidesPerRow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentWidth", "i", "length", "newSlide", "j", "row", "k", "style", "width", "cloneElement", "key", "tabIndex", "display", "className", "unslick", "innerSliderRefHandler", "Component", "InnerSlider", "list", "track", "adaptiveHeight", "elem", "querySelector", "currentSlide", "height", "getHeight", "onInit", "lazyLoad", "slidesToLoad", "getOnDemandLazySlides", "prevState", "lazyLoadedList", "concat", "onLazyLoad", "spec", "listRef", "trackRef", "updateState", "adaptHeight", "autoplay", "lazyLoadTimer", "setInterval", "progressiveLazyLoad", "ro", "ResizeObserver", "animating", "onWindowResized", "callbackTimers", "setTimeout", "speed", "observe", "document", "querySelectorAll", "Array", "prototype", "call", "onfocus", "pauseOnFocus", "onSlideFocus", "onblur", "onSlideBlur", "addEventListener", "attachEvent", "animationEndCallback", "clearTimeout", "clearInterval", "timer", "removeEventListener", "detachEvent", "autoplayTimer", "disconnect", "prevProps", "checkImagesLoad", "onReInit", "setTrackStyle", "didPropsChange", "count", "changeSlide", "message", "autoplaySpeed", "debouncedResize", "cancel", "debounce", "resizeWindow", "isTrackMounted", "Boolean", "node", "callback", "updatedState", "initializedState", "slideIndex", "targetLeft", "getTrackLeft", "left", "trackStyle", "getTrackCSS", "trackWidth", "trackLeft", "childrenWidths", "preClones", "getPreClones", "slideCount", "postClones", "getPostClones", "childrenCount", "slideWidth", "images", "imagesCount", "loadedCount", "image", "onclick", "parentNode", "focus", "prevClickHandler", "onload", "onerror", "onLazyLoadError", "indexOf", "asNavFor", "beforeChange", "afterChange", "<PERSON><PERSON><PERSON><PERSON>", "useCSS", "nextState", "value", "waitForAnimate", "asNavForIndex", "firstBatch", "options", "targetSlide", "focusOnSelect", "nodes", "e", "clickable", "stopPropagation", "preventDefault", "dir", "<PERSON><PERSON><PERSON><PERSON>", "accessibility", "rtl", "event", "returnValue", "ontouchmove", "verticalSwiping", "disableBodyScroll", "swipeStart", "swipe", "draggable", "swipeMove", "swipeEnd", "triggerSlideHandler", "undefined", "enableBodyScroll", "Number", "isNaN", "nextIndex", "canGoNext", "playType", "autoplaying", "play", "pauseType", "classnames", "vertical", "trackProps", "extractObject", "pauseOnHover", "onMouseEnter", "onTrackOver", "onMouseLeave", "onTrackLeave", "onMouseOver", "<PERSON><PERSON><PERSON><PERSON>", "dots", "dotProps", "pauseOnDotsHover", "clickHandler", "onDotsLeave", "onDotsOver", "prevArrow", "nextArrow", "arrowProps", "arrows", "verticalHeightStyle", "listHeight", "centerPaddingStyle", "padding", "centerPadding", "listStyle", "touchMove", "listProps", "onClick", "onMouseDown", "onMouseMove", "dragging", "onMouseUp", "onTouchStart", "onTouchMove", "onTouchEnd", "touchEnd", "onTouchCancel", "onKeyDown", "innerSliderProps", "listRefHandler", "trackRefHandler", "initialState", "initialSlide", "ssrState", "ssrInit", "Object", "keys", "hasOwnProperty", "currentDirection", "currentLeft", "direction", "edgeDragged", "initialized", "listWidth", "scrolling", "slideHeight", "swipeLeft", "swiped", "swiping", "touchObject", "startX", "startY", "curX", "curY", "clamp", "number", "lowerBound", "upperBound", "Math", "max", "min", "safePreventDefault", "passiveEvents", "includes", "_reactName", "onDemandSlides", "startIndex", "lazyStartIndex", "endIndex", "lazyEndIndex", "getRequiredLazySlides", "requiredSlides", "lazySlidesOnLeft", "lazySlidesOnRight", "floor", "parseInt", "getWidth", "offsetWidth", "offsetHeight", "getSwipeDirection", "xDist", "yDist", "r", "swipeAngle", "atan2", "round", "PI", "abs", "canGo", "infinite", "newObject", "listNode", "ceil", "trackNode", "centerPaddingAdj", "animationSlide", "finalSlide", "animationLeft", "finalLeft", "getTrackAnimateCSS", "indexOffset", "previousInt", "slideOffset", "unevenOffset", "previousTargetSlide", "siblingDirection", "target", "tagName", "match", "keyCode", "type", "touches", "pageX", "clientX", "pageY", "clientY", "swipeToSlide", "edgeFriction", "onEdge", "swipeEvent", "curL<PERSON>t", "swipe<PERSON><PERSON><PERSON>", "sqrt", "pow", "verticalSwipeLength", "positionOffset", "dotCount", "swipeDirection", "touchSwipeLength", "touchThreshold", "onSwipe", "minSwipe", "activeSlide", "getSlideCount", "checkNavigable", "getNavigableIndexes", "counter", "indexes", "navigables", "prevNavigable", "n", "centerOffset", "swipedSlide", "slickList", "slides", "from", "every", "offsetLeft", "offsetTop", "currentIndex", "slidesTraversed", "dataset", "checkSpecKeys", "keysArray", "reduce", "error", "trackHeight", "trackChildren", "getTotalSlides", "opacity", "transition", "WebkitTransition", "useTransform", "WebkitTransform", "transform", "msTransform", "marginLeft", "marginTop", "cssEase", "verticalOffset", "slidesToOffset", "targetSlideIndex", "trackElem", "childNodes", "slidesOnRight", "slidesOnLeft", "right", "createElement", "getSlideClasses", "slickActive", "slickCenter", "slickCloned", "focusedSlide", "<PERSON><PERSON><PERSON><PERSON>", "getSlideStyle", "position", "top", "<PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON>", "renderSlides", "preCloneSlides", "postCloneSlides", "childOnClickOptions", "childStyle", "slideClass", "slideClasses", "outline", "preCloneNo", "reverse", "Track", "mouseEvents", "handleRef", "PureComponent", "getDotCount", "Dots", "_rightBound", "rightBound", "_leftBound", "leftBound", "dotOptions", "bind", "customPaging", "appendDots", "dotsClass", "PrevArrow", "prevClasses", "prev<PERSON><PERSON><PERSON>", "prevArrowProps", "customProps", "NextArrow", "nextClasses", "<PERSON><PERSON><PERSON><PERSON>", "nextArrowProps", "easing"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA;AAAA;AAAA;AAEeA,8GAAf,E;;;;;;;ACFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;;;;;;;;;AAEb;AACA;AACA;AACA;AACA;;IAEqBA,M;;;;;EACnB,gBAAYC,KAAZ,EAAmB;IAAA;;IAAA;;IACjB,0BAAMA,KAAN;;IADiB,kMAQK,UAAAC,GAAG;MAAA,OAAK,MAAKC,WAAL,GAAmBD,GAAxB;IAAA,CARR;;IAAA,sLAwEP;MAAA,OAAM,MAAKC,WAAL,CAAiBC,SAAjB,EAAN;IAAA,CAxEO;;IAAA,sLA0EP;MAAA,OAAM,MAAKD,WAAL,CAAiBE,SAAjB,EAAN;IAAA,CA1EO;;IAAA,sLA4EP,UAACC,KAAD;MAAA,IAAQC,WAAR,uEAAsB,KAAtB;MAAA,OACV,MAAKJ,WAAL,CAAiBK,SAAjB,CAA2BF,KAA3B,EAAkCC,WAAlC,CADU;IAAA,CA5EO;;IAAA,uLA+EN;MAAA,OAAM,MAAKJ,WAAL,CAAiBM,KAAjB,CAAuB,QAAvB,CAAN;IAAA,CA/EM;;IAAA,sLAiFP;MAAA,OAAM,MAAKN,WAAL,CAAiBO,QAAjB,CAA0B,MAA1B,CAAN;IAAA,CAjFO;;IAEjB,MAAKC,KAAL,GAAa;MACXC,UAAU,EAAE;IADD,CAAb;IAGA,MAAKC,wBAAL,GAAgC,EAAhC;IALiB;EAMlB;;;;WAID,eAAMC,KAAN,EAAaC,OAAb,EAAsB;MACpB;MACA,IAAMC,GAAG,GAAGC,MAAM,CAACC,UAAP,CAAkBJ,KAAlB,CAAZ;;MACA,IAAMK,QAAQ,GAAG,SAAXA,QAAW,OAAiB;QAAA,IAAdC,OAAc,QAAdA,OAAc;;QAChC,IAAIA,OAAJ,EAAa;UACXL,OAAO;QACR;MACF,CAJD;;MAKAC,GAAG,CAACK,WAAJ,CAAgBF,QAAhB;MACAA,QAAQ,CAACH,GAAD,CAAR;;MACA,KAAKH,wBAAL,CAA8BS,IAA9B,CAAmC;QAAEN,GAAG,EAAHA,GAAF;QAAOF,KAAK,EAALA,KAAP;QAAcK,QAAQ,EAARA;MAAd,CAAnC;IACD,C,CAED;;;;WACA,6BAAoB;MAAA;;MAClB;MACA;MACA;MACA;MACA;MACA,IAAI,KAAKlB,KAAL,CAAWsB,UAAf,EAA2B;QACzB,IAAIC,WAAW,GAAG,KAAKvB,KAAL,CAAWsB,UAAX,CAAsBE,GAAtB,CAChB,UAAAC,OAAO;UAAA,OAAIA,OAAO,CAACd,UAAZ;QAAA,CADS,CAAlB,CADyB,CAIzB;;QACAY,WAAW,CAACG,IAAZ,CAAiB,UAACC,CAAD,EAAIC,CAAJ;UAAA,OAAUD,CAAC,GAAGC,CAAd;QAAA,CAAjB;QAEAL,WAAW,CAACM,OAAZ,CAAoB,UAAClB,UAAD,EAAamB,KAAb,EAAuB;UACzC;UACA,IAAIC,MAAJ;;UACA,IAAID,KAAK,KAAK,CAAd,EAAiB;YACfC,MAAM,GAAGC,+CAAO,CAAC;cAAEC,QAAQ,EAAE,CAAZ;cAAeC,QAAQ,EAAEvB;YAAzB,CAAD,CAAhB;UACD,CAFD,MAEO;YACLoB,MAAM,GAAGC,+CAAO,CAAC;cACfC,QAAQ,EAAEV,WAAW,CAACO,KAAK,GAAG,CAAT,CAAX,GAAyB,CADpB;cAEfI,QAAQ,EAAEvB;YAFK,CAAD,CAAhB;UAID,CAVwC,CAWzC;;;UACAwB,0EAAS,MACP,MAAI,CAACC,KAAL,CAAWL,MAAX,EAAmB,YAAM;YACvB,MAAI,CAACM,QAAL,CAAc;cAAE1B,UAAU,EAAEA;YAAd,CAAd;UACD,CAFD,CADF;QAID,CAhBD,EAPyB,CAyBzB;QACA;;QACA,IAAIE,KAAK,GAAGmB,+CAAO,CAAC;UAAEC,QAAQ,EAAEV,WAAW,CAACe,KAAZ,CAAkB,CAAC,CAAnB,EAAsB,CAAtB;QAAZ,CAAD,CAAnB;QAEAH,0EAAS,MACP,KAAKC,KAAL,CAAWvB,KAAX,EAAkB,YAAM;UACtB,MAAI,CAACwB,QAAL,CAAc;YAAE1B,UAAU,EAAE;UAAd,CAAd;QACD,CAFD,CADF;MAID;IACF;;;WAED,gCAAuB;MACrB,KAAKC,wBAAL,CAA8BiB,OAA9B,CAAsC,UAASU,GAAT,EAAc;QAClDA,GAAG,CAACxB,GAAJ,CAAQyB,cAAR,CAAuBD,GAAG,CAACrB,QAA3B;MACD,CAFD;IAGD;;;WAaD,kBAAS;MAAA;;MACP,IAAIuB,QAAJ;MACA,IAAIC,QAAJ;;MACA,IAAI,KAAKhC,KAAL,CAAWC,UAAf,EAA2B;QACzB+B,QAAQ,GAAG,KAAK1C,KAAL,CAAWsB,UAAX,CAAsBqB,MAAtB,CACT,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACjC,UAAL,KAAoB,MAAI,CAACD,KAAL,CAAWC,UAAnC;QAAA,CADK,CAAX;QAGA8B,QAAQ,GACNC,QAAQ,CAAC,CAAD,CAAR,CAAYD,QAAZ,KAAyB,SAAzB,GACI,SADJ,2OAESI,uDAFT,GAE0B,KAAK7C,KAF/B,GAEyC0C,QAAQ,CAAC,CAAD,CAAR,CAAYD,QAFrD,CADF;MAID,CARD,MAQO;QACLA,QAAQ,GAAG,4JAAKI,uDAAR,GAAyB,KAAK7C,KAA9B,CAAR;MACD,CAbM,CAeP;;;MACA,IAAIyC,QAAQ,CAACK,UAAb,EAAyB;QACvB,IACEL,QAAQ,CAACM,cAAT,GAA0B,CAA1B,IACAC,MAAA,KAAyB,YAF3B,EAGE;UACAC,OAAO,CAACC,IAAR,4EACsET,QAAQ,CAACM,cAD/E;QAGD;;QACDN,QAAQ,CAACM,cAAT,GAA0B,CAA1B;MACD,CA1BM,CA2BP;;;MACA,IAAIN,QAAQ,CAACU,IAAb,EAAmB;QACjB,IAAIV,QAAQ,CAACW,YAAT,GAAwB,CAAxB,IAA6BJ,MAAA,KAAyB,YAA1D,EAAwE;UACtEC,OAAO,CAACC,IAAR,6EACuET,QAAQ,CAACW,YADhF;QAGD;;QACD,IACEX,QAAQ,CAACM,cAAT,GAA0B,CAA1B,IACAC,MAAA,KAAyB,YAF3B,EAGE;UACAC,OAAO,CAACC,IAAR,+EACyET,QAAQ,CAACM,cADlF;QAGD;;QACDN,QAAQ,CAACW,YAAT,GAAwB,CAAxB;QACAX,QAAQ,CAACM,cAAT,GAA0B,CAA1B;MACD,CA5CM,CA8CP;;;MACA,IAAIM,QAAQ,GAAGC,4CAAK,CAACC,QAAN,CAAeC,OAAf,CAAuB,KAAKxD,KAAL,CAAWqD,QAAlC,CAAf,CA/CO,CAiDP;MACA;;MACAA,QAAQ,GAAGA,QAAQ,CAACV,MAAT,CAAgB,UAAAc,KAAK,EAAI;QAClC,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;UAC7B,OAAO,CAAC,CAACA,KAAK,CAACC,IAAN,EAAT;QACD;;QACD,OAAO,CAAC,CAACD,KAAT;MACD,CALU,CAAX,CAnDO,CA0DP;;MACA,IACEhB,QAAQ,CAACkB,aAAT,KACClB,QAAQ,CAACmB,IAAT,GAAgB,CAAhB,IAAqBnB,QAAQ,CAACoB,YAAT,GAAwB,CAD9C,CADF,EAGE;QACAZ,OAAO,CAACC,IAAR;QAGAT,QAAQ,CAACkB,aAAT,GAAyB,KAAzB;MACD;;MACD,IAAIG,WAAW,GAAG,EAAlB;MACA,IAAIC,YAAY,GAAG,IAAnB;;MACA,KACE,IAAIC,CAAC,GAAG,CADV,EAEEA,CAAC,GAAGX,QAAQ,CAACY,MAFf,EAGED,CAAC,IAAIvB,QAAQ,CAACmB,IAAT,GAAgBnB,QAAQ,CAACoB,YAHhC,EAIE;QACA,IAAIK,QAAQ,GAAG,EAAf;;QACA,KACE,IAAIC,CAAC,GAAGH,CADV,EAEEG,CAAC,GAAGH,CAAC,GAAGvB,QAAQ,CAACmB,IAAT,GAAgBnB,QAAQ,CAACoB,YAFnC,EAGEM,CAAC,IAAI1B,QAAQ,CAACoB,YAHhB,EAIE;UACA,IAAIO,GAAG,GAAG,EAAV;;UACA,KAAK,IAAIC,CAAC,GAAGF,CAAb,EAAgBE,CAAC,GAAGF,CAAC,GAAG1B,QAAQ,CAACoB,YAAjC,EAA+CQ,CAAC,IAAI,CAApD,EAAuD;YACrD,IAAI5B,QAAQ,CAACkB,aAAT,IAA0BN,QAAQ,CAACgB,CAAD,CAAR,CAAYrE,KAAZ,CAAkBsE,KAAhD,EAAuD;cACrDP,YAAY,GAAGV,QAAQ,CAACgB,CAAD,CAAR,CAAYrE,KAAZ,CAAkBsE,KAAlB,CAAwBC,KAAvC;YACD;;YACD,IAAIF,CAAC,IAAIhB,QAAQ,CAACY,MAAlB,EAA0B;YAC1BG,GAAG,CAAC/C,IAAJ,eACEiC,4CAAK,CAACkB,YAAN,CAAmBnB,QAAQ,CAACgB,CAAD,CAA3B,EAAgC;cAC9BI,GAAG,EAAE,MAAMT,CAAN,GAAU,KAAKG,CAAf,GAAmBE,CADM;cAE9BK,QAAQ,EAAE,CAAC,CAFmB;cAG9BJ,KAAK,EAAE;gBACLC,KAAK,YAAK,MAAM9B,QAAQ,CAACoB,YAApB,MADA;gBAELc,OAAO,EAAE;cAFJ;YAHuB,CAAhC,CADF;UAUD;;UACDT,QAAQ,CAAC7C,IAAT,eAAc;YAAK,GAAG,EAAE,KAAK2C,CAAL,GAASG;UAAnB,GAAuBC,GAAvB,CAAd;QACD;;QACD,IAAI3B,QAAQ,CAACkB,aAAb,EAA4B;UAC1BG,WAAW,CAACzC,IAAZ,eACE;YAAK,GAAG,EAAE2C,CAAV;YAAa,KAAK,EAAE;cAAEO,KAAK,EAAER;YAAT;UAApB,GACGG,QADH,CADF;QAKD,CAND,MAMO;UACLJ,WAAW,CAACzC,IAAZ,eAAiB;YAAK,GAAG,EAAE2C;UAAV,GAAcE,QAAd,CAAjB;QACD;MACF;;MAED,IAAIzB,QAAQ,KAAK,SAAjB,EAA4B;QAC1B,IAAMmC,SAAS,GAAG,qBAAqB,KAAK5E,KAAL,CAAW4E,SAAX,IAAwB,EAA7C,CAAlB;QACA,oBAAO;UAAK,SAAS,EAAEA;QAAhB,GAA4BvB,QAA5B,CAAP;MACD,CAHD,MAGO,IAAIS,WAAW,CAACG,MAAZ,IAAsBxB,QAAQ,CAACW,YAAnC,EAAiD;QACtDX,QAAQ,CAACoC,OAAT,GAAmB,IAAnB;MACD;;MACD,oBACE,2DAAC,yDAAD;QACE,KAAK,EAAE,KAAK7E,KAAL,CAAWsE,KADpB;QAEE,GAAG,EAAE,KAAKQ;MAFZ,GAGMrC,QAHN,GAKGqB,WALH,CADF;IASD;;;;EAlNiCR,4CAAK,CAACyB,S;;;;;;;;ACR1C;AACA;AACA,mBAAmB,sBAAsB;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;;AAEA,wG;;;;;;ACjBA,qBAAqB,mBAAO,CAAC,CAAqB;;AAElD;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA;;AAEA,8G;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA,+G;;;;;;ACfA;AACA;AACA;AACA;AACA;;AAEA,+G;;;;;;ACNA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,4G;;;;;;ACnBA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,sH;;;;;;ACRA,qBAAqB,mBAAO,CAAC,CAAqB;;AAElD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;;AAEA,yG;;;;;;ACpBA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,+G;;;;;;ACRA,qBAAqB,mBAAO,CAAC,EAAqB;;AAElD,+BAA+B,mBAAO,CAAC,EAA+B;;AAEtE,gCAAgC,mBAAO,CAAC,EAAgC;;AAExE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA,4G;;;;;;ACvBA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,+G;;;;;;ACPA;AACA;AACA;AACA;;AAEA;AACA,gFAAgF;AAChF;AACA,GAAG;AACH;AACA;AACA;;AAEA,yH;;;;;;ACbA,cAAc,mBAAO,CAAC,EAAa;;AAEnC,4BAA4B,mBAAO,CAAC,CAA4B;;AAEhE;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA,0H;;;;;;ACdA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA,uG;;;;;;ACVA,iD;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;;;;;;;;;;;;AAEb;AACA;AACA;AACA;AACA;AAkBA;AACA;AACA;AACA;AAEO,IAAMC,WAAb;EAAA;;EAAA;;EACE,qBAAYhF,KAAZ,EAAmB;IAAA;;IAAA;;IACjB,0BAAMA,KAAN;;IADiB,2LAeF,UAACC,GAAD;MAAA,OAAU,MAAKgF,IAAL,GAAYhF,GAAtB;IAAA,CAfE;;IAAA,4LAgBD,UAACA,GAAD;MAAA,OAAU,MAAKiF,KAAL,GAAajF,GAAvB;IAAA,CAhBC;;IAAA,wLAiBL,YAAM;MAClB,IAAI,MAAKD,KAAL,CAAWmF,cAAX,IAA6B,MAAKF,IAAtC,EAA4C;QAC1C,IAAMG,IAAI,GAAG,MAAKH,IAAL,CAAUI,aAAV,yBACK,MAAK3E,KAAL,CAAW4E,YADhB,SAAb;;QAGA,MAAKL,IAAL,CAAUX,KAAV,CAAgBiB,MAAhB,GAAyBC,0EAAS,CAACJ,IAAD,CAAT,GAAkB,IAA3C;MACD;IACF,CAxBkB;;IAAA,8LAyBC,YAAM;MACxB,MAAKpF,KAAL,CAAWyF,MAAX,IAAqB,MAAKzF,KAAL,CAAWyF,MAAX,EAArB;;MACA,IAAI,MAAKzF,KAAL,CAAW0F,QAAf,EAAyB;QACvB,IAAIC,YAAY,GAAGC,sFAAqB,CAAC,4JACpC,MAAK5F,KAD8B,GAEnC,MAAKU,KAF8B,EAAxC;;QAIA,IAAIiF,YAAY,CAAC1B,MAAb,GAAsB,CAA1B,EAA6B;UAC3B,MAAK5B,QAAL,CAAc,UAACwD,SAAD;YAAA,OAAgB;cAC5BC,cAAc,EAAED,SAAS,CAACC,cAAV,CAAyBC,MAAzB,CAAgCJ,YAAhC;YADY,CAAhB;UAAA,CAAd;;UAGA,IAAI,MAAK3F,KAAL,CAAWgG,UAAf,EAA2B;YACzB,MAAKhG,KAAL,CAAWgG,UAAX,CAAsBL,YAAtB;UACD;QACF;MACF;;MACD,IAAIM,IAAI,GAAG;QAAEC,OAAO,EAAE,MAAKjB,IAAnB;QAAyBkB,QAAQ,EAAE,MAAKjB;MAAxC,GAAkD,MAAKlF,KAAvD,CAAR;;MACA,MAAKoG,WAAL,CAAiBH,IAAjB,EAAuB,IAAvB,EAA6B,YAAM;QACjC,MAAKI,WAAL;;QACA,MAAKrG,KAAL,CAAWsG,QAAX,IAAuB,MAAK7F,QAAL,CAAc,SAAd,CAAvB;MACD,CAHD;;MAIA,IAAI,MAAKT,KAAL,CAAW0F,QAAX,KAAwB,aAA5B,EAA2C;QACzC,MAAKa,aAAL,GAAqBC,WAAW,CAAC,MAAKC,mBAAN,EAA2B,IAA3B,CAAhC;MACD;;MACD,MAAKC,EAAL,GAAU,IAAIC,iEAAJ,CAAmB,YAAM;QACjC,IAAI,MAAKjG,KAAL,CAAWkG,SAAf,EAA0B;UACxB,MAAKC,eAAL,CAAqB,KAArB,EADwB,CACK;;;UAC7B,MAAKC,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CAAC;YAAA,OAAM,MAAKF,eAAL,EAAN;UAAA,CAAD,EAA+B,MAAK7G,KAAL,CAAWgH,KAA1C,CADZ;QAGD,CALD,MAKO;UACL,MAAKH,eAAL;QACD;MACF,CATS,CAAV;;MAUA,MAAKH,EAAL,CAAQO,OAAR,CAAgB,MAAKhC,IAArB;;MACAiC,QAAQ,CAACC,gBAAT,IACEC,KAAK,CAACC,SAAN,CAAgBxF,OAAhB,CAAwByF,IAAxB,CACEJ,QAAQ,CAACC,gBAAT,CAA0B,cAA1B,CADF,EAEE,UAAC9G,KAAD,EAAW;QACTA,KAAK,CAACkH,OAAN,GAAgB,MAAKvH,KAAL,CAAWwH,YAAX,GAA0B,MAAKC,YAA/B,GAA8C,IAA9D;QACApH,KAAK,CAACqH,MAAN,GAAe,MAAK1H,KAAL,CAAWwH,YAAX,GAA0B,MAAKG,WAA/B,GAA6C,IAA5D;MACD,CALH,CADF;;MAQA,IAAI3G,MAAM,CAAC4G,gBAAX,EAA6B;QAC3B5G,MAAM,CAAC4G,gBAAP,CAAwB,QAAxB,EAAkC,MAAKf,eAAvC;MACD,CAFD,MAEO;QACL7F,MAAM,CAAC6G,WAAP,CAAmB,UAAnB,EAA+B,MAAKhB,eAApC;MACD;IACF,CAzEkB;;IAAA,iMA0EI,YAAM;MAC3B,IAAI,MAAKiB,oBAAT,EAA+B;QAC7BC,YAAY,CAAC,MAAKD,oBAAN,CAAZ;MACD;;MACD,IAAI,MAAKvB,aAAT,EAAwB;QACtByB,aAAa,CAAC,MAAKzB,aAAN,CAAb;MACD;;MACD,IAAI,MAAKO,cAAL,CAAoB7C,MAAxB,EAAgC;QAC9B,MAAK6C,cAAL,CAAoBjF,OAApB,CAA4B,UAACoG,KAAD;UAAA,OAAWF,YAAY,CAACE,KAAD,CAAvB;QAAA,CAA5B;;QACA,MAAKnB,cAAL,GAAsB,EAAtB;MACD;;MACD,IAAI9F,MAAM,CAAC4G,gBAAX,EAA6B;QAC3B5G,MAAM,CAACkH,mBAAP,CAA2B,QAA3B,EAAqC,MAAKrB,eAA1C;MACD,CAFD,MAEO;QACL7F,MAAM,CAACmH,WAAP,CAAmB,UAAnB,EAA+B,MAAKtB,eAApC;MACD;;MACD,IAAI,MAAKuB,aAAT,EAAwB;QACtBJ,aAAa,CAAC,MAAKI,aAAN,CAAb;MACD;;MACD,MAAK1B,EAAL,CAAQ2B,UAAR;IACD,CA9FkB;;IAAA,+LA0HE,UAACC,SAAD,EAAe;MAClC,MAAKC,eAAL;;MACA,MAAKvI,KAAL,CAAWwI,QAAX,IAAuB,MAAKxI,KAAL,CAAWwI,QAAX,EAAvB;;MACA,IAAI,MAAKxI,KAAL,CAAW0F,QAAf,EAAyB;QACvB,IAAIC,YAAY,GAAGC,sFAAqB,CAAC,4JACpC,MAAK5F,KAD8B,GAEnC,MAAKU,KAF8B,EAAxC;;QAIA,IAAIiF,YAAY,CAAC1B,MAAb,GAAsB,CAA1B,EAA6B;UAC3B,MAAK5B,QAAL,CAAc,UAACwD,SAAD;YAAA,OAAgB;cAC5BC,cAAc,EAAED,SAAS,CAACC,cAAV,CAAyBC,MAAzB,CAAgCJ,YAAhC;YADY,CAAhB;UAAA,CAAd;;UAGA,IAAI,MAAK3F,KAAL,CAAWgG,UAAf,EAA2B;YACzB,MAAKhG,KAAL,CAAWgG,UAAX,CAAsBL,YAAtB;UACD;QACF;MACF,CAhBiC,CAiBlC;MACA;MACA;;;MACA,MAAKU,WAAL;;MACA,IAAIJ,IAAI,GAAG;QACTC,OAAO,EAAE,MAAKjB,IADR;QAENkB,QAAQ,EAAE,MAAKjB;MAFT,GAGH,MAAKlF,KAHF,GAIH,MAAKU,KAJF,CAAR;;MAMA,IAAM+H,aAAa,GAAG,MAAKC,cAAL,CAAoBJ,SAApB,CAAtB;;MACAG,aAAa,IACX,MAAKrC,WAAL,CAAiBH,IAAjB,EAAuBwC,aAAvB,EAAsC,YAAM;QAC1C,IACE,MAAK/H,KAAL,CAAW4E,YAAX,IAA2BhC,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,MAAK3I,KAAL,CAAWqD,QAAhC,CAD7B,EAEE;UACA,MAAKuF,WAAL,CAAiB;YACfC,OAAO,EAAE,OADM;YAEf/G,KAAK,EACHwB,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,MAAK3I,KAAL,CAAWqD,QAAhC,IACA,MAAKrD,KAAL,CAAWoD,YAJE;YAKfkC,YAAY,EAAE,MAAK5E,KAAL,CAAW4E;UALV,CAAjB;QAOD;;QACD,IACEgD,SAAS,CAAChC,QAAV,KAAuB,MAAKtG,KAAL,CAAWsG,QAAlC,IACAgC,SAAS,CAACQ,aAAV,KAA4B,MAAK9I,KAAL,CAAW8I,aAFzC,EAGE;UACA,IAAI,CAACR,SAAS,CAAChC,QAAX,IAAuB,MAAKtG,KAAL,CAAWsG,QAAtC,EAAgD;YAC9C,MAAK7F,QAAL,CAAc,SAAd;UACD,CAFD,MAEO,IAAI,MAAKT,KAAL,CAAWsG,QAAf,EAAyB;YAC9B,MAAK7F,QAAL,CAAc,QAAd;UACD,CAFM,MAEA;YACL,MAAKD,KAAL,CAAW,QAAX;UACD;QACF;MACF,CAxBD,CADF;IA0BD,CAhLkB;;IAAA,4LAiLD,UAACiI,aAAD,EAAmB;MACnC,IAAI,MAAKM,eAAT,EAA0B,MAAKA,eAAL,CAAqBC,MAArB;MAC1B,MAAKD,eAAL,GAAuBE,uDAAQ,CAAC;QAAA,OAAM,MAAKC,YAAL,CAAkBT,aAAlB,CAAN;MAAA,CAAD,EAAyC,EAAzC,CAA/B;;MACA,MAAKM,eAAL;IACD,CArLkB;;IAAA,yLAsLJ,YAA0B;MAAA,IAAzBN,aAAyB,uEAAT,IAAS;MACvC,IAAMU,cAAc,GAAGC,OAAO,CAAC,MAAKlE,KAAL,IAAc,MAAKA,KAAL,CAAWmE,IAA1B,CAA9B,CADuC,CAEvC;;MACA,IAAI,CAACF,cAAL,EAAqB;;MACrB,IAAIlD,IAAI,GAAG;QACTC,OAAO,EAAE,MAAKjB,IADR;QAENkB,QAAQ,EAAE,MAAKjB;MAFT,GAGH,MAAKlF,KAHF,GAIH,MAAKU,KAJF,CAAR;;MAMA,MAAK0F,WAAL,CAAiBH,IAAjB,EAAuBwC,aAAvB,EAAsC,YAAM;QAC1C,IAAI,MAAKzI,KAAL,CAAWsG,QAAf,EAAyB,MAAK7F,QAAL,CAAc,QAAd,EAAzB,KACK,MAAKD,KAAL,CAAW,QAAX;MACN,CAHD,EAVuC,CAcvC;;;MACA,MAAK6B,QAAL,CAAc;QACZuE,SAAS,EAAE;MADC,CAAd;;MAGAmB,YAAY,CAAC,MAAKD,oBAAN,CAAZ;MACA,OAAO,MAAKA,oBAAZ;IACD,CA1MkB;;IAAA,wLA2ML,UAAC7B,IAAD,EAAOwC,aAAP,EAAsBa,QAAtB,EAAmC;MAC/C,IAAIC,YAAY,GAAGC,iFAAgB,CAACvD,IAAD,CAAnC;MACAA,IAAI,GAAG,wOAAKA,IAAR,GAAiBsD,YAAjB;QAA+BE,UAAU,EAAEF,YAAY,CAACjE;MAAxD,EAAJ;MACA,IAAIoE,UAAU,GAAGC,6EAAY,CAAC1D,IAAD,CAA7B;MACAA,IAAI,GAAG,4JAAKA,IAAR;QAAc2D,IAAI,EAAEF;MAApB,EAAJ;MACA,IAAIG,UAAU,GAAGC,4EAAW,CAAC7D,IAAD,CAA5B;;MACA,IACEwC,aAAa,IACbnF,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,MAAK3I,KAAL,CAAWqD,QAAhC,MACEC,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB1C,IAAI,CAAC5C,QAA1B,CAHJ,EAIE;QACAkG,YAAY,CAAC,YAAD,CAAZ,GAA6BM,UAA7B;MACD;;MACD,MAAKxH,QAAL,CAAckH,YAAd,EAA4BD,QAA5B;IACD,CAzNkB;;IAAA,oLA2NT,YAAM;MACd,IAAI,MAAKtJ,KAAL,CAAW2D,aAAf,EAA8B;QAC5B,IAAIoG,WAAU,GAAG,CAAjB;QAAA,IACEC,UAAS,GAAG,CADd;QAEA,IAAIC,cAAc,GAAG,EAArB;QACA,IAAIC,SAAS,GAAGC,6EAAY,CAAC,wOACxB,MAAKnK,KADkB,GAEvB,MAAKU,KAFkB;UAG1B0J,UAAU,EAAE,MAAKpK,KAAL,CAAWqD,QAAX,CAAoBY;QAHN,GAA5B;QAKA,IAAIoG,UAAU,GAAGC,8EAAa,CAAC,wOAC1B,MAAKtK,KADoB,GAEzB,MAAKU,KAFoB;UAG5B0J,UAAU,EAAE,MAAKpK,KAAL,CAAWqD,QAAX,CAAoBY;QAHJ,GAA9B;;QAKA,MAAKjE,KAAL,CAAWqD,QAAX,CAAoBxB,OAApB,CAA4B,UAAC4B,KAAD,EAAW;UACrCwG,cAAc,CAAC5I,IAAf,CAAoBoC,KAAK,CAACzD,KAAN,CAAYsE,KAAZ,CAAkBC,KAAtC;UACAwF,WAAU,IAAItG,KAAK,CAACzD,KAAN,CAAYsE,KAAZ,CAAkBC,KAAhC;QACD,CAHD;;QAIA,KAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkG,SAApB,EAA+BlG,CAAC,EAAhC,EAAoC;UAClCgG,UAAS,IAAIC,cAAc,CAACA,cAAc,CAAChG,MAAf,GAAwB,CAAxB,GAA4BD,CAA7B,CAA3B;UACA+F,WAAU,IAAIE,cAAc,CAACA,cAAc,CAAChG,MAAf,GAAwB,CAAxB,GAA4BD,CAA7B,CAA5B;QACD;;QACD,KAAK,IAAIA,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGqG,UAApB,EAAgCrG,EAAC,EAAjC,EAAqC;UACnC+F,WAAU,IAAIE,cAAc,CAACjG,EAAD,CAA5B;QACD;;QACD,KAAK,IAAIA,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,MAAKtD,KAAL,CAAW4E,YAA/B,EAA6CtB,GAAC,EAA9C,EAAkD;UAChDgG,UAAS,IAAIC,cAAc,CAACjG,GAAD,CAA3B;QACD;;QACD,IAAI6F,WAAU,GAAG;UACftF,KAAK,EAAEwF,WAAU,GAAG,IADL;UAEfH,IAAI,EAAE,CAACI,UAAD,GAAa;QAFJ,CAAjB;;QAIA,IAAI,MAAKhK,KAAL,CAAW8C,UAAf,EAA2B;UACzB,IAAIiB,YAAY,aAAMkG,cAAc,CAAC,MAAKvJ,KAAL,CAAW4E,YAAZ,CAApB,OAAhB;UACAuE,WAAU,CAACD,IAAX,kBAA0BC,WAAU,CAACD,IAArC,wBAAuD7F,YAAvD;QACD;;QACD,OAAO;UACL8F,UAAU,EAAVA;QADK,CAAP;MAGD;;MACD,IAAIU,aAAa,GAAGjH,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,MAAK3I,KAAL,CAAWqD,QAAhC,CAApB;;MACA,IAAM4C,IAAI,GAAG,wOAAK,MAAKjG,KAAb,GAAuB,MAAKU,KAA5B;QAAmC0J,UAAU,EAAEG;MAA/C,EAAV;;MACA,IAAIH,UAAU,GAAGD,6EAAY,CAAClE,IAAD,CAAZ,GAAqBqE,8EAAa,CAACrE,IAAD,CAAlC,GAA2CsE,aAA5D;MACA,IAAIR,UAAU,GAAI,MAAM,MAAK/J,KAAL,CAAWoD,YAAlB,GAAkCgH,UAAnD;MACA,IAAII,UAAU,GAAG,MAAMJ,UAAvB;MACA,IAAIJ,SAAS,GACV,CAACQ,UAAD,IACEL,6EAAY,CAAClE,IAAD,CAAZ,GAAqB,MAAKvF,KAAL,CAAW4E,YADlC,IAECyE,UAFF,GAGA,GAJF;;MAKA,IAAI,MAAK/J,KAAL,CAAW8C,UAAf,EAA2B;QACzBkH,SAAS,IAAI,CAAC,MAAOQ,UAAU,GAAGT,UAAd,GAA4B,GAAnC,IAA0C,CAAvD;MACD;;MACD,IAAIF,UAAU,GAAG;QACftF,KAAK,EAAEwF,UAAU,GAAG,GADL;QAEfH,IAAI,EAAEI,SAAS,GAAG;MAFH,CAAjB;MAIA,OAAO;QACLQ,UAAU,EAAEA,UAAU,GAAG,GADpB;QAELX,UAAU,EAAEA;MAFP,CAAP;IAID,CAzRkB;;IAAA,4LA0RD,YAAM;MACtB,IAAIY,MAAM,GACP,MAAKxF,IAAL,IACC,MAAKA,IAAL,CAAUkC,gBADX,IAEC,MAAKlC,IAAL,CAAUkC,gBAAV,CAA2B,kBAA3B,CAFF,IAGA,EAJF;MAKA,IAAIuD,WAAW,GAAGD,MAAM,CAACxG,MAAzB;MAAA,IACE0G,WAAW,GAAG,CADhB;MAEAvD,KAAK,CAACC,SAAN,CAAgBxF,OAAhB,CAAwByF,IAAxB,CAA6BmD,MAA7B,EAAqC,UAACG,KAAD,EAAW;QAC9C,IAAM9J,OAAO,GAAG,SAAVA,OAAU;UAAA,OACd,EAAE6J,WAAF,IAAiBA,WAAW,IAAID,WAAhC,IAA+C,MAAK7D,eAAL,EADjC;QAAA,CAAhB;;QAEA,IAAI,CAAC+D,KAAK,CAACC,OAAX,EAAoB;UAClBD,KAAK,CAACC,OAAN,GAAgB;YAAA,OAAMD,KAAK,CAACE,UAAN,CAAiBC,KAAjB,EAAN;UAAA,CAAhB;QACD,CAFD,MAEO;UACL,IAAMC,gBAAgB,GAAGJ,KAAK,CAACC,OAA/B;;UACAD,KAAK,CAACC,OAAN,GAAgB,YAAM;YACpBG,gBAAgB;YAChBJ,KAAK,CAACE,UAAN,CAAiBC,KAAjB;UACD,CAHD;QAID;;QACD,IAAI,CAACH,KAAK,CAACK,MAAX,EAAmB;UACjB,IAAI,MAAKjL,KAAL,CAAW0F,QAAf,EAAyB;YACvBkF,KAAK,CAACK,MAAN,GAAe,YAAM;cACnB,MAAK5E,WAAL;;cACA,MAAKS,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CAAC,MAAKF,eAAN,EAAuB,MAAK7G,KAAL,CAAWgH,KAAlC,CADZ;YAGD,CALD;UAMD,CAPD,MAOO;YACL4D,KAAK,CAACK,MAAN,GAAenK,OAAf;;YACA8J,KAAK,CAACM,OAAN,GAAgB,YAAM;cACpBpK,OAAO;cACP,MAAKd,KAAL,CAAWmL,eAAX,IAA8B,MAAKnL,KAAL,CAAWmL,eAAX,EAA9B;YACD,CAHD;UAID;QACF;MACF,CA5BD;IA6BD,CA/TkB;;IAAA,gMAgUG,YAAM;MAC1B,IAAIxF,YAAY,GAAG,EAAnB;;MACA,IAAMM,IAAI,GAAG,4JAAK,MAAKjG,KAAb,GAAuB,MAAKU,KAA5B,CAAV;;MACA,KACE,IAAIoB,KAAK,GAAG,MAAKpB,KAAL,CAAW4E,YADzB,EAEExD,KAAK,GAAG,MAAKpB,KAAL,CAAW0J,UAAX,GAAwBE,8EAAa,CAACrE,IAAD,CAF/C,EAGEnE,KAAK,EAHP,EAIE;QACA,IAAI,MAAKpB,KAAL,CAAWoF,cAAX,CAA0BsF,OAA1B,CAAkCtJ,KAAlC,IAA2C,CAA/C,EAAkD;UAChD6D,YAAY,CAACtE,IAAb,CAAkBS,KAAlB;UACA;QACD;MACF;;MACD,KACE,IAAIA,MAAK,GAAG,MAAKpB,KAAL,CAAW4E,YAAX,GAA0B,CADxC,EAEExD,MAAK,IAAI,CAACqI,6EAAY,CAAClE,IAAD,CAFxB,EAGEnE,MAAK,EAHP,EAIE;QACA,IAAI,MAAKpB,KAAL,CAAWoF,cAAX,CAA0BsF,OAA1B,CAAkCtJ,MAAlC,IAA2C,CAA/C,EAAkD;UAChD6D,YAAY,CAACtE,IAAb,CAAkBS,MAAlB;UACA;QACD;MACF;;MACD,IAAI6D,YAAY,CAAC1B,MAAb,GAAsB,CAA1B,EAA6B;QAC3B,MAAK5B,QAAL,CAAc,UAAC3B,KAAD;UAAA,OAAY;YACxBoF,cAAc,EAAEpF,KAAK,CAACoF,cAAN,CAAqBC,MAArB,CAA4BJ,YAA5B;UADQ,CAAZ;QAAA,CAAd;;QAGA,IAAI,MAAK3F,KAAL,CAAWgG,UAAf,EAA2B;UACzB,MAAKhG,KAAL,CAAWgG,UAAX,CAAsBL,YAAtB;QACD;MACF,CAPD,MAOO;QACL,IAAI,MAAKY,aAAT,EAAwB;UACtByB,aAAa,CAAC,MAAKzB,aAAN,CAAb;UACA,OAAO,MAAKA,aAAZ;QACD;MACF;IACF,CApWkB;;IAAA,yLAqWJ,UAACzE,KAAD,EAAgC;MAAA,IAAxBxB,WAAwB,uEAAV,KAAU;MAC7C,kBACE,MAAKN,KADP;MAAA,IAAQqL,QAAR,eAAQA,QAAR;MAAA,IAAkBC,YAAlB,eAAkBA,YAAlB;MAAA,IAAgCtF,UAAhC,eAAgCA,UAAhC;MAAA,IAA4CgB,KAA5C,eAA4CA,KAA5C;MAAA,IAAmDuE,WAAnD,eAAmDA,WAAnD,CAD6C,CAG7C;;MACA,IAAMjG,YAAY,GAAG,MAAK5E,KAAL,CAAW4E,YAAhC;;MACA,oBAA2BkG,6EAAY,CAAC;QACtC1J,KAAK,EAALA;MADqC,GAElC,MAAK9B,KAF6B,GAGlC,MAAKU,KAH6B;QAIrCyF,QAAQ,EAAE,MAAKjB,KAJsB;QAKrCuG,MAAM,EAAE,MAAKzL,KAAL,CAAWyL,MAAX,IAAqB,CAACnL;MALO,GAAvC;MAAA,IAAMI,KAAN,iBAAMA,KAAN;MAAA,IAAagL,SAAb,iBAAaA,SAAb;;MAOA,IAAI,CAAChL,KAAL,EAAY;MACZ4K,YAAY,IAAIA,YAAY,CAAChG,YAAD,EAAe5E,KAAK,CAAC4E,YAArB,CAA5B;MACA,IAAIK,YAAY,GAAGjF,KAAK,CAACoF,cAAN,CAAqBnD,MAArB,CACjB,UAACgJ,KAAD;QAAA,OAAW,MAAKjL,KAAL,CAAWoF,cAAX,CAA0BsF,OAA1B,CAAkCO,KAAlC,IAA2C,CAAtD;MAAA,CADiB,CAAnB;MAGA3F,UAAU,IAAIL,YAAY,CAAC1B,MAAb,GAAsB,CAApC,IAAyC+B,UAAU,CAACL,YAAD,CAAnD;;MACA,IAAI,CAAC,MAAK3F,KAAL,CAAW4L,cAAZ,IAA8B,MAAK9D,oBAAvC,EAA6D;QAC3DC,YAAY,CAAC,MAAKD,oBAAN,CAAZ;QACAyD,WAAW,IAAIA,WAAW,CAACjG,YAAD,CAA1B;QACA,OAAO,MAAKwC,oBAAZ;MACD;;MACD,MAAKzF,QAAL,CAAc3B,KAAd,EAAqB,YAAM;QACzB;QACA,IAAI2K,QAAQ,IAAI,MAAKQ,aAAL,KAAuB/J,KAAvC,EAA8C;UAC5C,MAAK+J,aAAL,GAAqB/J,KAArB;UACAuJ,QAAQ,CAACnL,WAAT,CAAqBsL,YAArB,CAAkC1J,KAAlC;QACD;;QACD,IAAI,CAAC4J,SAAL,EAAgB;QAChB,MAAK5D,oBAAL,GAA4Bf,UAAU,CAAC,YAAM;UAC3C,IAAQH,SAAR,GAAqC8E,SAArC,CAAQ9E,SAAR;UAAA,IAAsBkF,UAAtB,yFAAqCJ,SAArC;;UACA,MAAKrJ,QAAL,CAAcyJ,UAAd,EAA0B,YAAM;YAC9B,MAAKhF,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CAAC;cAAA,OAAM,MAAK1E,QAAL,CAAc;gBAAEuE,SAAS,EAATA;cAAF,CAAd,CAAN;YAAA,CAAD,EAAqC,EAArC,CADZ;;YAGA2E,WAAW,IAAIA,WAAW,CAAC7K,KAAK,CAAC4E,YAAP,CAA1B;YACA,OAAO,MAAKwC,oBAAZ;UACD,CAND;QAOD,CATqC,EASnCd,KATmC,CAAtC;MAUD,CAjBD;IAkBD,CA9YkB;;IAAA,wLA+YL,UAAC+E,OAAD,EAAkC;MAAA,IAAxBzL,WAAwB,uEAAV,KAAU;;MAC9C,IAAM2F,IAAI,GAAG,4JAAK,MAAKjG,KAAb,GAAuB,MAAKU,KAA5B,CAAV;;MACA,IAAIsL,WAAW,GAAGpD,4EAAW,CAAC3C,IAAD,EAAO8F,OAAP,CAA7B;MACA,IAAIC,WAAW,KAAK,CAAhB,IAAqB,CAACA,WAA1B,EAAuC;;MACvC,IAAI1L,WAAW,KAAK,IAApB,EAA0B;QACxB,MAAKkL,YAAL,CAAkBQ,WAAlB,EAA+B1L,WAA/B;MACD,CAFD,MAEO;QACL,MAAKkL,YAAL,CAAkBQ,WAAlB;MACD;;MACD,MAAKhM,KAAL,CAAWsG,QAAX,IAAuB,MAAK7F,QAAL,CAAc,QAAd,CAAvB;;MACA,IAAI,MAAKT,KAAL,CAAWiM,aAAf,EAA8B;QAC5B,IAAMC,KAAK,GAAG,MAAKjH,IAAL,CAAUkC,gBAAV,CAA2B,gBAA3B,CAAd;;QACA+E,KAAK,CAAC,CAAD,CAAL,IAAYA,KAAK,CAAC,CAAD,CAAL,CAASnB,KAAT,EAAZ;MACD;IACF,CA7ZkB;;IAAA,yLA8ZJ,UAACoB,CAAD,EAAO;MACpB,IAAI,MAAKC,SAAL,KAAmB,KAAvB,EAA8B;QAC5BD,CAAC,CAACE,eAAF;QACAF,CAAC,CAACG,cAAF;MACD;;MACD,MAAKF,SAAL,GAAiB,IAAjB;IACD,CApakB;;IAAA,uLAqaN,UAACD,CAAD,EAAO;MAClB,IAAII,GAAG,GAAGC,2EAAU,CAACL,CAAD,EAAI,MAAKnM,KAAL,CAAWyM,aAAf,EAA8B,MAAKzM,KAAL,CAAW0M,GAAzC,CAApB;MACAH,GAAG,KAAK,EAAR,IAAc,MAAK3D,WAAL,CAAiB;QAAEC,OAAO,EAAE0D;MAAX,CAAjB,CAAd;IACD,CAxakB;;IAAA,0LAyaH,UAACR,OAAD,EAAa;MAC3B,MAAKnD,WAAL,CAAiBmD,OAAjB;IACD,CA3akB;;IAAA,8LA4aC,YAAM;MACxB,IAAMO,cAAc,GAAG,SAAjBA,cAAiB,CAACH,CAAD,EAAO;QAC5BA,CAAC,GAAGA,CAAC,IAAInL,MAAM,CAAC2L,KAAhB;QACA,IAAIR,CAAC,CAACG,cAAN,EAAsBH,CAAC,CAACG,cAAF;QACtBH,CAAC,CAACS,WAAF,GAAgB,KAAhB;MACD,CAJD;;MAKA5L,MAAM,CAAC6L,WAAP,GAAqBP,cAArB;IACD,CAnbkB;;IAAA,6LAobA,YAAM;MACvBtL,MAAM,CAAC6L,WAAP,GAAqB,IAArB;IACD,CAtbkB;;IAAA,uLAubN,UAACV,CAAD,EAAO;MAClB,IAAI,MAAKnM,KAAL,CAAW8M,eAAf,EAAgC;QAC9B,MAAKC,iBAAL;MACD;;MACD,IAAIrM,KAAK,GAAGsM,2EAAU,CAACb,CAAD,EAAI,MAAKnM,KAAL,CAAWiN,KAAf,EAAsB,MAAKjN,KAAL,CAAWkN,SAAjC,CAAtB;MACAxM,KAAK,KAAK,EAAV,IAAgB,MAAK2B,QAAL,CAAc3B,KAAd,CAAhB;IACD,CA7bkB;;IAAA,sLA8bP,UAACyL,CAAD,EAAO;MACjB,IAAIzL,KAAK,GAAGyM,0EAAS,CAAChB,CAAD,8EAAC,4JACjB,MAAKnM,KADW,GAEhB,MAAKU,KAFW;QAGnByF,QAAQ,EAAE,MAAKjB,KAHI;QAInBgB,OAAO,EAAE,MAAKjB,IAJK;QAKnBwE,UAAU,EAAE,MAAK/I,KAAL,CAAW4E;MALJ,GAArB;MAOA,IAAI,CAAC5E,KAAL,EAAY;;MACZ,IAAIA,KAAK,CAAC,SAAD,CAAT,EAAsB;QACpB,MAAK0L,SAAL,GAAiB,KAAjB;MACD;;MACD,MAAK/J,QAAL,CAAc3B,KAAd;IACD,CA3ckB;;IAAA,qLA4cR,UAACyL,CAAD,EAAO;MAChB,IAAIzL,KAAK,GAAG0M,yEAAQ,CAACjB,CAAD,8EAAC,4JAChB,MAAKnM,KADU,GAEf,MAAKU,KAFU;QAGlByF,QAAQ,EAAE,MAAKjB,KAHG;QAIlBgB,OAAO,EAAE,MAAKjB,IAJI;QAKlBwE,UAAU,EAAE,MAAK/I,KAAL,CAAW4E;MALL,GAApB;MAOA,IAAI,CAAC5E,KAAL,EAAY;MACZ,IAAI2M,mBAAmB,GAAG3M,KAAK,CAAC,qBAAD,CAA/B;MACA,OAAOA,KAAK,CAAC,qBAAD,CAAZ;;MACA,MAAK2B,QAAL,CAAc3B,KAAd;;MACA,IAAI2M,mBAAmB,KAAKC,SAA5B,EAAuC;;MACvC,MAAK9B,YAAL,CAAkB6B,mBAAlB;;MACA,IAAI,MAAKrN,KAAL,CAAW8M,eAAf,EAAgC;QAC9B,MAAKS,gBAAL;MACD;IACF,CA7dkB;;IAAA,qLA8dR,UAACpB,CAAD,EAAO;MAChB,MAAKiB,QAAL,CAAcjB,CAAd;;MACA,MAAKC,SAAL,GAAiB,IAAjB;IACD,CAjekB;;IAAA,sLAkeP,YAAM;MAChB;MACA;MACA;MACA,MAAKtF,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CAAC;QAAA,OAAM,MAAK6B,WAAL,CAAiB;UAAEC,OAAO,EAAE;QAAX,CAAjB,CAAN;MAAA,CAAD,EAAkD,CAAlD,CADZ;IAGD,CAzekB;;IAAA,sLA0eP,YAAM;MAChB,MAAK/B,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CAAC;QAAA,OAAM,MAAK6B,WAAL,CAAiB;UAAEC,OAAO,EAAE;QAAX,CAAjB,CAAN;MAAA,CAAD,EAA8C,CAA9C,CADZ;IAGD,CA9ekB;;IAAA,sLA+eP,UAACxI,KAAD,EAAgC;MAAA,IAAxBC,WAAwB,uEAAV,KAAU;MAC1CD,KAAK,GAAGmN,MAAM,CAACnN,KAAD,CAAd;MACA,IAAIoN,KAAK,CAACpN,KAAD,CAAT,EAAkB,OAAO,EAAP;;MAClB,MAAKyG,cAAL,CAAoBzF,IAApB,CACE0F,UAAU,CACR;QAAA,OACE,MAAK6B,WAAL,CACE;UACEC,OAAO,EAAE,OADX;UAEE/G,KAAK,EAAEzB,KAFT;UAGEiF,YAAY,EAAE,MAAK5E,KAAL,CAAW4E;QAH3B,CADF,EAMEhF,WANF,CADF;MAAA,CADQ,EAUR,CAVQ,CADZ;IAcD,CAhgBkB;;IAAA,iLAigBZ,YAAM;MACX,IAAIoN,SAAJ;;MACA,IAAI,MAAK1N,KAAL,CAAW0M,GAAf,EAAoB;QAClBgB,SAAS,GAAG,MAAKhN,KAAL,CAAW4E,YAAX,GAA0B,MAAKtF,KAAL,CAAW+C,cAAjD;MACD,CAFD,MAEO;QACL,IAAI4K,0EAAS,CAAC,4JAAK,MAAK3N,KAAX,GAAqB,MAAKU,KAA1B,EAAb,EAAiD;UAC/CgN,SAAS,GAAG,MAAKhN,KAAL,CAAW4E,YAAX,GAA0B,MAAKtF,KAAL,CAAW+C,cAAjD;QACD,CAFD,MAEO;UACL,OAAO,KAAP;QACD;MACF;;MAED,MAAKyI,YAAL,CAAkBkC,SAAlB;IACD,CA9gBkB;;IAAA,qLAghBR,UAACE,QAAD,EAAc;MACvB,IAAI,MAAKxF,aAAT,EAAwB;QACtBJ,aAAa,CAAC,MAAKI,aAAN,CAAb;MACD;;MACD,IAAMyF,WAAW,GAAG,MAAKnN,KAAL,CAAWmN,WAA/B;;MACA,IAAID,QAAQ,KAAK,QAAjB,EAA2B;QACzB,IACEC,WAAW,KAAK,SAAhB,IACAA,WAAW,KAAK,SADhB,IAEAA,WAAW,KAAK,QAHlB,EAIE;UACA;QACD;MACF,CARD,MAQO,IAAID,QAAQ,KAAK,OAAjB,EAA0B;QAC/B,IAAIC,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,SAAhD,EAA2D;UACzD;QACD;MACF,CAJM,MAIA,IAAID,QAAQ,KAAK,MAAjB,EAAyB;QAC9B,IAAIC,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,SAAhD,EAA2D;UACzD;QACD;MACF;;MACD,MAAKzF,aAAL,GAAqB5B,WAAW,CAAC,MAAKsH,IAAN,EAAY,MAAK9N,KAAL,CAAW8I,aAAX,GAA2B,EAAvC,CAAhC;;MACA,MAAKzG,QAAL,CAAc;QAAEwL,WAAW,EAAE;MAAf,CAAd;IACD,CAxiBkB;;IAAA,kLAyiBX,UAACE,SAAD,EAAe;MACrB,IAAI,MAAK3F,aAAT,EAAwB;QACtBJ,aAAa,CAAC,MAAKI,aAAN,CAAb;QACA,MAAKA,aAAL,GAAqB,IAArB;MACD;;MACD,IAAMyF,WAAW,GAAG,MAAKnN,KAAL,CAAWmN,WAA/B;;MACA,IAAIE,SAAS,KAAK,QAAlB,EAA4B;QAC1B,MAAK1L,QAAL,CAAc;UAAEwL,WAAW,EAAE;QAAf,CAAd;MACD,CAFD,MAEO,IAAIE,SAAS,KAAK,SAAlB,EAA6B;QAClC,IAAIF,WAAW,KAAK,SAAhB,IAA6BA,WAAW,KAAK,SAAjD,EAA4D;UAC1D,MAAKxL,QAAL,CAAc;YAAEwL,WAAW,EAAE;UAAf,CAAd;QACD;MACF,CAJM,MAIA;QACL;QACA,IAAIA,WAAW,KAAK,SAApB,EAA+B;UAC7B,MAAKxL,QAAL,CAAc;YAAEwL,WAAW,EAAE;UAAf,CAAd;QACD;MACF;IACF,CA3jBkB;;IAAA,uLA4jBN;MAAA,OAAM,MAAK7N,KAAL,CAAWsG,QAAX,IAAuB,MAAK9F,KAAL,CAAW,SAAX,CAA7B;IAAA,CA5jBM;;IAAA,wLA6jBL;MAAA,OACZ,MAAKR,KAAL,CAAWsG,QAAX,IACA,MAAK5F,KAAL,CAAWmN,WAAX,KAA2B,SAD3B,IAEA,MAAKpN,QAAL,CAAc,OAAd,CAHY;IAAA,CA7jBK;;IAAA,wLAikBL;MAAA,OAAM,MAAKT,KAAL,CAAWsG,QAAX,IAAuB,MAAK9F,KAAL,CAAW,SAAX,CAA7B;IAAA,CAjkBK;;IAAA,yLAkkBJ;MAAA,OACb,MAAKR,KAAL,CAAWsG,QAAX,IACA,MAAK5F,KAAL,CAAWmN,WAAX,KAA2B,SAD3B,IAEA,MAAKpN,QAAL,CAAc,OAAd,CAHa;IAAA,CAlkBI;;IAAA,yLAskBJ;MAAA,OAAM,MAAKT,KAAL,CAAWsG,QAAX,IAAuB,MAAK9F,KAAL,CAAW,SAAX,CAA7B;IAAA,CAtkBI;;IAAA,wLAukBL;MAAA,OACZ,MAAKR,KAAL,CAAWsG,QAAX,IACA,MAAK5F,KAAL,CAAWmN,WAAX,KAA2B,SAD3B,IAEA,MAAKpN,QAAL,CAAc,MAAd,CAHY;IAAA,CAvkBK;;IAAA,mLA4kBV,YAAM;MACb,IAAImE,SAAS,GAAGoJ,kDAAU,CAAC,cAAD,EAAiB,MAAKhO,KAAL,CAAW4E,SAA5B,EAAuC;QAC/D,kBAAkB,MAAK5E,KAAL,CAAWiO,QADkC;QAE/D,qBAAqB;MAF0C,CAAvC,CAA1B;;MAIA,IAAIhI,IAAI,GAAG,4JAAK,MAAKjG,KAAb,GAAuB,MAAKU,KAA5B,CAAR;;MACA,IAAIwN,UAAU,GAAGC,8EAAa,CAAClI,IAAD,EAAO,CACnC,MADmC,EAEnC,SAFmC,EAGnC,OAHmC,EAInC,UAJmC,EAKnC,YALmC,EAMnC,eANmC,EAOnC,cAPmC,EAQnC,UARmC,EASnC,gBATmC,EAUnC,KAVmC,EAWnC,YAXmC,EAYnC,aAZmC,EAanC,YAbmC,EAcnC,UAdmC,EAenC,cAfmC,EAgBnC,gBAhBmC,EAiBnC,YAjBmC,EAkBnC,YAlBmC,EAmBnC,eAnBmC,EAoBnC,SApBmC,EAqBnC,eArBmC,EAsBnC,aAtBmC,EAuBnC,QAvBmC,CAAP,CAA9B;MAyBA,IAAQmI,YAAR,GAAyB,MAAKpO,KAA9B,CAAQoO,YAAR;MACAF,UAAU,GAAG,4JACRA,UADK;QAERG,YAAY,EAAED,YAAY,GAAG,MAAKE,WAAR,GAAsB,IAFxC;QAGRC,YAAY,EAAEH,YAAY,GAAG,MAAKI,YAAR,GAAuB,IAHzC;QAIRC,WAAW,EAAEL,YAAY,GAAG,MAAKE,WAAR,GAAsB,IAJvC;QAKRrC,aAAa,EACX,MAAKjM,KAAL,CAAWiM,aAAX,IAA4B,MAAKG,SAAjC,GAA6C,MAAKsC,aAAlD,GAAkE;MAN5D,EAAV;MASA,IAAIC,IAAJ;;MACA,IACE,MAAK3O,KAAL,CAAW2O,IAAX,KAAoB,IAApB,IACA,MAAKjO,KAAL,CAAW0J,UAAX,IAAyB,MAAKpK,KAAL,CAAWoD,YAFtC,EAGE;QACA,IAAIwL,QAAQ,GAAGT,8EAAa,CAAClI,IAAD,EAAO,CACjC,WADiC,EAEjC,YAFiC,EAGjC,cAHiC,EAIjC,cAJiC,EAKjC,gBALiC,EAMjC,cANiC,EAOjC,UAPiC,EAQjC,cARiC,EASjC,UATiC,EAUjC,YAViC,CAAP,CAA5B;QAYA,IAAQ4I,gBAAR,GAA6B,MAAK7O,KAAlC,CAAQ6O,gBAAR;QACAD,QAAQ,GAAG,4JACNA,QADG;UAENE,YAAY,EAAE,MAAKlG,WAFb;UAGNyF,YAAY,EAAEQ,gBAAgB,GAAG,MAAKE,WAAR,GAAsB,IAH9C;UAINN,WAAW,EAAEI,gBAAgB,GAAG,MAAKG,UAAR,GAAqB,IAJ5C;UAKNT,YAAY,EAAEM,gBAAgB,GAAG,MAAKE,WAAR,GAAsB;QAL9C,EAAR;QAOAJ,IAAI,gBAAG,4DAAC,2CAAD,EAAUC,QAAV,CAAP;MACD;;MAED,IAAIK,SAAJ,EAAeC,SAAf;MACA,IAAIC,UAAU,GAAGhB,8EAAa,CAAClI,IAAD,EAAO,CACnC,UADmC,EAEnC,YAFmC,EAGnC,cAHmC,EAInC,YAJmC,EAKnC,cALmC,EAMnC,WANmC,EAOnC,WAPmC,CAAP,CAA9B;MASAkJ,UAAU,CAACL,YAAX,GAA0B,MAAKlG,WAA/B;;MAEA,IAAI,MAAK5I,KAAL,CAAWoP,MAAf,EAAuB;QACrBH,SAAS,gBAAG,4DAAC,kDAAD,EAAeE,UAAf,CAAZ;QACAD,SAAS,gBAAG,4DAAC,kDAAD,EAAeC,UAAf,CAAZ;MACD;;MAED,IAAIE,mBAAmB,GAAG,IAA1B;;MAEA,IAAI,MAAKrP,KAAL,CAAWiO,QAAf,EAAyB;QACvBoB,mBAAmB,GAAG;UACpB9J,MAAM,EAAE,MAAK7E,KAAL,CAAW4O;QADC,CAAtB;MAGD;;MAED,IAAIC,kBAAkB,GAAG,IAAzB;;MAEA,IAAI,MAAKvP,KAAL,CAAWiO,QAAX,KAAwB,KAA5B,EAAmC;QACjC,IAAI,MAAKjO,KAAL,CAAW8C,UAAX,KAA0B,IAA9B,EAAoC;UAClCyM,kBAAkB,GAAG;YACnBC,OAAO,EAAE,SAAS,MAAKxP,KAAL,CAAWyP;UADV,CAArB;QAGD;MACF,CAND,MAMO;QACL,IAAI,MAAKzP,KAAL,CAAW8C,UAAX,KAA0B,IAA9B,EAAoC;UAClCyM,kBAAkB,GAAG;YACnBC,OAAO,EAAE,MAAKxP,KAAL,CAAWyP,aAAX,GAA2B;UADjB,CAArB;QAGD;MACF;;MAED,IAAMC,SAAS,GAAG,4JAAKL,mBAAR,GAAgCE,kBAAhC,CAAf;;MACA,IAAMI,SAAS,GAAG,MAAK3P,KAAL,CAAW2P,SAA7B;MACA,IAAIC,SAAS,GAAG;QACdhL,SAAS,EAAE,YADG;QAEdN,KAAK,EAAEoL,SAFO;QAGdG,OAAO,EAAE,MAAKf,YAHA;QAIdgB,WAAW,EAAEH,SAAS,GAAG,MAAK3C,UAAR,GAAqB,IAJ7B;QAKd+C,WAAW,EAAE,MAAKrP,KAAL,CAAWsP,QAAX,IAAuBL,SAAvB,GAAmC,MAAKxC,SAAxC,GAAoD,IALnD;QAMd8C,SAAS,EAAEN,SAAS,GAAG,MAAKvC,QAAR,GAAmB,IANzB;QAOdmB,YAAY,EAAE,MAAK7N,KAAL,CAAWsP,QAAX,IAAuBL,SAAvB,GAAmC,MAAKvC,QAAxC,GAAmD,IAPnD;QAQd8C,YAAY,EAAEP,SAAS,GAAG,MAAK3C,UAAR,GAAqB,IAR9B;QASdmD,WAAW,EAAE,MAAKzP,KAAL,CAAWsP,QAAX,IAAuBL,SAAvB,GAAmC,MAAKxC,SAAxC,GAAoD,IATnD;QAUdiD,UAAU,EAAET,SAAS,GAAG,MAAKU,QAAR,GAAmB,IAV1B;QAWdC,aAAa,EAAE,MAAK5P,KAAL,CAAWsP,QAAX,IAAuBL,SAAvB,GAAmC,MAAKvC,QAAxC,GAAmD,IAXpD;QAYdmD,SAAS,EAAE,MAAKvQ,KAAL,CAAWyM,aAAX,GAA2B,MAAKD,UAAhC,GAA6C;MAZ1C,CAAhB;MAeA,IAAIgE,gBAAgB,GAAG;QACrB5L,SAAS,EAAEA,SADU;QAErB2H,GAAG,EAAE,KAFgB;QAGrBjI,KAAK,EAAE,MAAKtE,KAAL,CAAWsE;MAHG,CAAvB;;MAMA,IAAI,MAAKtE,KAAL,CAAW6E,OAAf,EAAwB;QACtB+K,SAAS,GAAG;UAAEhL,SAAS,EAAE;QAAb,CAAZ;QACA4L,gBAAgB,GAAG;UAAE5L,SAAS,EAATA;QAAF,CAAnB;MACD;;MACD,oBACE,mEAAS4L,gBAAT,EACG,CAAC,MAAKxQ,KAAL,CAAW6E,OAAZ,GAAsBoK,SAAtB,GAAkC,EADrC,eAEE;QAAK,GAAG,EAAE,MAAKwB;MAAf,GAAmCb,SAAnC,gBACE,4DAAC,6CAAD;QAAO,GAAG,EAAE,MAAKc;MAAjB,GAAsCxC,UAAtC,GACG,MAAKlO,KAAL,CAAWqD,QADd,CADF,CAFF,EAOG,CAAC,MAAKrD,KAAL,CAAW6E,OAAZ,GAAsBqK,SAAtB,GAAkC,EAPrC,EAQG,CAAC,MAAKlP,KAAL,CAAW6E,OAAZ,GAAsB8J,IAAtB,GAA6B,EARhC,CADF;IAYD,CAjuBkB;;IAEjB,MAAK1J,IAAL,GAAY,IAAZ;IACA,MAAKC,KAAL,GAAa,IAAb;IACA,MAAKxE,KAAL,+JACKiQ,uDADL;MAEErL,YAAY,EAAE,MAAKtF,KAAL,CAAW4Q,YAF3B;MAGExG,UAAU,EAAE9G,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,MAAK3I,KAAL,CAAWqD,QAAhC;IAHd;IAKA,MAAKyD,cAAL,GAAsB,EAAtB;IACA,MAAKsF,SAAL,GAAiB,IAAjB;IACA,MAAKrD,eAAL,GAAuB,IAAvB;;IACA,IAAM8H,QAAQ,GAAG,MAAKC,OAAL,EAAjB;;IACA,MAAKpQ,KAAL,+JAAkB,MAAKA,KAAvB,GAAiCmQ,QAAjC;IAbiB;EAclB;;EAfH;IAAA;IAAA,OAiGE,wBAAevI,SAAf,EAA0B;MACxB,IAAIG,aAAa,GAAG,KAApB;;MACA,iCAAgBsI,MAAM,CAACC,IAAP,CAAY,KAAKhR,KAAjB,CAAhB,oCAAyC;QAApC,IAAIyE,GAAG,oBAAP;;QACH;QACA,IAAI,CAAC6D,SAAS,CAAC2I,cAAV,CAAyBxM,GAAzB,CAAL,EAAoC;UAClCgE,aAAa,GAAG,IAAhB;UACA;QACD;;QACD,IACE,qEAAOH,SAAS,CAAC7D,GAAD,CAAhB,MAA0B,QAA1B,IACA,OAAO6D,SAAS,CAAC7D,GAAD,CAAhB,KAA0B,UAF5B,EAGE;UACA;QACD;;QACD,IAAI6D,SAAS,CAAC7D,GAAD,CAAT,KAAmB,KAAKzE,KAAL,CAAWyE,GAAX,CAAvB,EAAwC;UACtCgE,aAAa,GAAG,IAAhB;UACA;QACD;MACF;;MACD,OACEA,aAAa,IACbnF,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB,KAAK3I,KAAL,CAAWqD,QAAhC,MACEC,6CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqBL,SAAS,CAACjF,QAA/B,CAHJ;IAKD;EAzHH;;EAAA;AAAA,EAAiCC,6CAAK,CAACyB,SAAvC,E;;;;;;AC7BA,mCAAmC,mBAAO,CAAC,EAAmC;;AAE9E;AACA;AACA;AACA;;AAEA;AACA;;AAEA,eAAe,6BAA6B;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,wH;;;;;;ACrBA;AACA;AACA;AACA;AACA;;AAEA,aAAa,uBAAuB;AACpC;AACA;AACA;AACA;;AAEA;AACA;;AAEA,6H;;;;;;;ACfA;AAAA,IAAM4L,YAAY,GAAG;EACnB/J,SAAS,EAAE,KADQ;EAEnBiH,WAAW,EAAE,IAFM;EAGnBqD,gBAAgB,EAAE,CAHC;EAInBC,WAAW,EAAE,IAJM;EAKnB7L,YAAY,EAAE,CALK;EAMnB8L,SAAS,EAAE,CANQ;EAOnBpB,QAAQ,EAAE,KAPS;EAQnBqB,WAAW,EAAE,KARM;EASnBC,WAAW,EAAE,KATM;EAUnBxL,cAAc,EAAE,EAVG;EAWnBwJ,UAAU,EAAE,IAXO;EAYnBiC,SAAS,EAAE,IAZQ;EAanBC,SAAS,EAAE,KAbQ;EAcnBpH,UAAU,EAAE,IAdO;EAenBqH,WAAW,EAAE,IAfM;EAgBnBjH,UAAU,EAAE,IAhBO;EAiBnBkH,SAAS,EAAE,IAjBQ;EAkBnBC,MAAM,EAAE,KAlBW;EAkBJ;EACfC,OAAO,EAAE,KAnBU;EAoBnBC,WAAW,EAAE;IAAEC,MAAM,EAAE,CAAV;IAAaC,MAAM,EAAE,CAArB;IAAwBC,IAAI,EAAE,CAA9B;IAAiCC,IAAI,EAAE;EAAvC,CApBM;EAqBnBpI,UAAU,EAAE,EArBO;EAsBnBE,UAAU,EAAE,CAtBO;EAuBnBiC,WAAW,EAAE;AAvBM,CAArB;AA0Be2E,2EAAf,E;;;;;;AC1BA,eAAe,mBAAO,CAAC,EAAY;AACnC,UAAU,mBAAO,CAAC,EAAO;AACzB,eAAe,mBAAO,CAAC,EAAY;;AAEnC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,OAAO;AAClB,WAAW,OAAO,YAAY;AAC9B,WAAW,QAAQ;AACnB;AACA,WAAW,OAAO;AAClB;AACA,WAAW,QAAQ;AACnB;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,8CAA8C,kBAAkB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC9LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,QAAQ;AACrB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC9BA,WAAW,mBAAO,CAAC,EAAS;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;;;;;;;ACtBA,iBAAiB,mBAAO,CAAC,EAAe;;AAExC;AACA;;AAEA;AACA;;AAEA;;;;;;;ACRA;AACA;;AAEA;;;;;;;;ACHA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;ACnBA,eAAe,mBAAO,CAAC,EAAa;AACpC,eAAe,mBAAO,CAAC,EAAY;AACnC,eAAe,mBAAO,CAAC,EAAY;;AAEnC;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC/DA,sBAAsB,mBAAO,CAAC,EAAoB;;AAElD;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AClBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;AClBA,iBAAiB,mBAAO,CAAC,EAAe;AACxC,mBAAmB,mBAAO,CAAC,EAAgB;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC5BA,aAAa,mBAAO,CAAC,EAAW;AAChC,gBAAgB,mBAAO,CAAC,EAAc;AACtC,qBAAqB,mBAAO,CAAC,EAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC3BA,WAAW,mBAAO,CAAC,EAAS;;AAE5B;AACA;;AAEA;;;;;;;ACLA,aAAa,mBAAO,CAAC,EAAW;;AAEhC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC7CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,EAAE;AACb,aAAa,QAAQ;AACrB;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,gBAAgB;;AAEhB;AACA;;AAEA,iBAAiB,sBAAsB;AACvC;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;AAEA,KAAK,KAA6B;AAClC;AACA;AACA,EAAE,UAAU,IAA4E;AACxF;AACA,EAAE,iCAAqB,EAAE,mCAAE;AAC3B;AACA,GAAG;AAAA,oGAAC;AACJ,EAAE,MAAM,EAEN;AACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzDD;AAEO,SAASuB,KAAT,CAAeC,MAAf,EAAuBC,UAAvB,EAAmCC,UAAnC,EAA+C;EACpD,OAAOC,IAAI,CAACC,GAAL,CAASH,UAAT,EAAqBE,IAAI,CAACE,GAAL,CAASL,MAAT,EAAiBE,UAAjB,CAArB,CAAP;AACD;AAEM,IAAMI,kBAAkB,GAAG,SAArBA,kBAAqB,CAAA9F,KAAK,EAAI;EACzC,IAAM+F,aAAa,GAAG,CAAC,cAAD,EAAiB,aAAjB,EAAgC,SAAhC,CAAtB;;EACA,IAAG,CAACA,aAAa,CAACC,QAAd,CAAuBhG,KAAK,CAACiG,UAA7B,CAAJ,EAA8C;IAC5CjG,KAAK,CAACL,cAAN;EACD;AACF,CALM;AAOA,IAAM1G,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAAK,IAAI,EAAI;EAC3C,IAAI4M,cAAc,GAAG,EAArB;EACA,IAAIC,UAAU,GAAGC,cAAc,CAAC9M,IAAD,CAA/B;EACA,IAAI+M,QAAQ,GAAGC,YAAY,CAAChN,IAAD,CAA3B;;EACA,KAAK,IAAIwD,UAAU,GAAGqJ,UAAtB,EAAkCrJ,UAAU,GAAGuJ,QAA/C,EAAyDvJ,UAAU,EAAnE,EAAuE;IACrE,IAAIxD,IAAI,CAACH,cAAL,CAAoBsF,OAApB,CAA4B3B,UAA5B,IAA0C,CAA9C,EAAiD;MAC/CoJ,cAAc,CAACxR,IAAf,CAAoBoI,UAApB;IACD;EACF;;EACD,OAAOoJ,cAAP;AACD,CAVM,C,CAYP;;AACO,IAAMK,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAAjN,IAAI,EAAI;EAC3C,IAAIkN,cAAc,GAAG,EAArB;EACA,IAAIL,UAAU,GAAGC,cAAc,CAAC9M,IAAD,CAA/B;EACA,IAAI+M,QAAQ,GAAGC,YAAY,CAAChN,IAAD,CAA3B;;EACA,KAAK,IAAIwD,UAAU,GAAGqJ,UAAtB,EAAkCrJ,UAAU,GAAGuJ,QAA/C,EAAyDvJ,UAAU,EAAnE,EAAuE;IACrE0J,cAAc,CAAC9R,IAAf,CAAoBoI,UAApB;EACD;;EACD,OAAO0J,cAAP;AACD,CARM,C,CAUP;;AACO,IAAMJ,cAAc,GAAG,SAAjBA,cAAiB,CAAA9M,IAAI;EAAA,OAChCA,IAAI,CAACX,YAAL,GAAoB8N,gBAAgB,CAACnN,IAAD,CADJ;AAAA,CAA3B;AAEA,IAAMgN,YAAY,GAAG,SAAfA,YAAe,CAAAhN,IAAI;EAAA,OAAIA,IAAI,CAACX,YAAL,GAAoB+N,iBAAiB,CAACpN,IAAD,CAAzC;AAAA,CAAzB;AACA,IAAMmN,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAAnN,IAAI;EAAA,OAClCA,IAAI,CAACnD,UAAL,GACIwP,IAAI,CAACgB,KAAL,CAAWrN,IAAI,CAAC7C,YAAL,GAAoB,CAA/B,KACCmQ,QAAQ,CAACtN,IAAI,CAACwJ,aAAN,CAAR,GAA+B,CAA/B,GAAmC,CAAnC,GAAuC,CADxC,CADJ,GAGI,CAJ8B;AAAA,CAA7B;AAKA,IAAM4D,iBAAiB,GAAG,SAApBA,iBAAoB,CAAApN,IAAI;EAAA,OACnCA,IAAI,CAACnD,UAAL,GACIwP,IAAI,CAACgB,KAAL,CAAW,CAACrN,IAAI,CAAC7C,YAAL,GAAoB,CAArB,IAA0B,CAArC,IACA,CADA,IAECmQ,QAAQ,CAACtN,IAAI,CAACwJ,aAAN,CAAR,GAA+B,CAA/B,GAAmC,CAAnC,GAAuC,CAFxC,CADJ,GAIIxJ,IAAI,CAAC7C,YAL0B;AAAA,CAA9B,C,CAOP;;AACO,IAAMoQ,QAAQ,GAAG,SAAXA,QAAW,CAAApO,IAAI;EAAA,OAAKA,IAAI,IAAIA,IAAI,CAACqO,WAAd,IAA8B,CAAlC;AAAA,CAArB;AACA,IAAMjO,SAAS,GAAG,SAAZA,SAAY,CAAAJ,IAAI;EAAA,OAAKA,IAAI,IAAIA,IAAI,CAACsO,YAAd,IAA+B,CAAnC;AAAA,CAAtB;AACA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAAC9B,WAAD,EAA0C;EAAA,IAA5B/E,eAA4B,uEAAV,KAAU;EACzE,IAAI8G,KAAJ,EAAWC,KAAX,EAAkBC,CAAlB,EAAqBC,UAArB;EACAH,KAAK,GAAG/B,WAAW,CAACC,MAAZ,GAAqBD,WAAW,CAACG,IAAzC;EACA6B,KAAK,GAAGhC,WAAW,CAACE,MAAZ,GAAqBF,WAAW,CAACI,IAAzC;EACA6B,CAAC,GAAGxB,IAAI,CAAC0B,KAAL,CAAWH,KAAX,EAAkBD,KAAlB,CAAJ;EACAG,UAAU,GAAGzB,IAAI,CAAC2B,KAAL,CAAYH,CAAC,GAAG,GAAL,GAAYxB,IAAI,CAAC4B,EAA5B,CAAb;;EACA,IAAIH,UAAU,GAAG,CAAjB,EAAoB;IAClBA,UAAU,GAAG,MAAMzB,IAAI,CAAC6B,GAAL,CAASJ,UAAT,CAAnB;EACD;;EACD,IACGA,UAAU,IAAI,EAAd,IAAoBA,UAAU,IAAI,CAAnC,IACCA,UAAU,IAAI,GAAd,IAAqBA,UAAU,IAAI,GAFtC,EAGE;IACA,OAAO,MAAP;EACD;;EACD,IAAIA,UAAU,IAAI,GAAd,IAAqBA,UAAU,IAAI,GAAvC,EAA4C;IAC1C,OAAO,OAAP;EACD;;EACD,IAAIjH,eAAe,KAAK,IAAxB,EAA8B;IAC5B,IAAIiH,UAAU,IAAI,EAAd,IAAoBA,UAAU,IAAI,GAAtC,EAA2C;MACzC,OAAO,IAAP;IACD,CAFD,MAEO;MACL,OAAO,MAAP;IACD;EACF;;EAED,OAAO,UAAP;AACD,CA3BM,C,CA6BP;;AACO,IAAMpG,SAAS,GAAG,SAAZA,SAAY,CAAA1H,IAAI,EAAI;EAC/B,IAAImO,KAAK,GAAG,IAAZ;;EACA,IAAI,CAACnO,IAAI,CAACoO,QAAV,EAAoB;IAClB,IAAIpO,IAAI,CAACnD,UAAL,IAAmBmD,IAAI,CAACX,YAAL,IAAqBW,IAAI,CAACmE,UAAL,GAAkB,CAA9D,EAAiE;MAC/DgK,KAAK,GAAG,KAAR;IACD,CAFD,MAEO,IACLnO,IAAI,CAACmE,UAAL,IAAmBnE,IAAI,CAAC7C,YAAxB,IACA6C,IAAI,CAACX,YAAL,IAAqBW,IAAI,CAACmE,UAAL,GAAkBnE,IAAI,CAAC7C,YAFvC,EAGL;MACAgR,KAAK,GAAG,KAAR;IACD;EACF;;EACD,OAAOA,KAAP;AACD,CAbM,C,CAeP;;AACO,IAAMjG,aAAa,GAAG,SAAhBA,aAAgB,CAAClI,IAAD,EAAO+K,IAAP,EAAgB;EAC3C,IAAIsD,SAAS,GAAG,EAAhB;EACAtD,IAAI,CAACnP,OAAL,CAAa,UAAA4C,GAAG;IAAA,OAAK6P,SAAS,CAAC7P,GAAD,CAAT,GAAiBwB,IAAI,CAACxB,GAAD,CAA1B;EAAA,CAAhB;EACA,OAAO6P,SAAP;AACD,CAJM,C,CAMP;;AACO,IAAM9K,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAAvD,IAAI,EAAI;EACtC;EACA,IAAImE,UAAU,GAAG9G,4CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB1C,IAAI,CAAC5C,QAA1B,CAAjB;EACA,IAAMkR,QAAQ,GAAGtO,IAAI,CAACC,OAAtB;EACA,IAAIqL,SAAS,GAAGe,IAAI,CAACkC,IAAL,CAAUhB,QAAQ,CAACe,QAAD,CAAlB,CAAhB;EACA,IAAME,SAAS,GAAGxO,IAAI,CAACE,QAAL,IAAiBF,IAAI,CAACE,QAAL,CAAckD,IAAjD;EACA,IAAIU,UAAU,GAAGuI,IAAI,CAACkC,IAAL,CAAUhB,QAAQ,CAACiB,SAAD,CAAlB,CAAjB;EACA,IAAIjK,UAAJ;;EACA,IAAI,CAACvE,IAAI,CAACgI,QAAV,EAAoB;IAClB,IAAIyG,gBAAgB,GAAGzO,IAAI,CAACnD,UAAL,IAAmByQ,QAAQ,CAACtN,IAAI,CAACwJ,aAAN,CAAR,GAA+B,CAAzE;;IACA,IACE,OAAOxJ,IAAI,CAACwJ,aAAZ,KAA8B,QAA9B,IACAxJ,IAAI,CAACwJ,aAAL,CAAmBnN,KAAnB,CAAyB,CAAC,CAA1B,MAAiC,GAFnC,EAGE;MACAoS,gBAAgB,IAAInD,SAAS,GAAG,GAAhC;IACD;;IACD/G,UAAU,GAAG8H,IAAI,CAACkC,IAAL,CAAU,CAACjD,SAAS,GAAGmD,gBAAb,IAAiCzO,IAAI,CAAC7C,YAAhD,CAAb;EACD,CATD,MASO;IACLoH,UAAU,GAAG+G,SAAb;EACD;;EACD,IAAIE,WAAW,GACb8C,QAAQ,IAAI/O,SAAS,CAAC+O,QAAQ,CAAClP,aAAT,CAAuB,kBAAvB,CAAD,CADvB;EAEA,IAAIiK,UAAU,GAAGmC,WAAW,GAAGxL,IAAI,CAAC7C,YAApC;EACA,IAAIkC,YAAY,GACdW,IAAI,CAACX,YAAL,KAAsBgI,SAAtB,GAAkCrH,IAAI,CAAC2K,YAAvC,GAAsD3K,IAAI,CAACX,YAD7D;;EAEA,IAAIW,IAAI,CAACyG,GAAL,IAAYzG,IAAI,CAACX,YAAL,KAAsBgI,SAAtC,EAAiD;IAC/ChI,YAAY,GAAG8E,UAAU,GAAG,CAAb,GAAiBnE,IAAI,CAAC2K,YAArC;EACD;;EACD,IAAI9K,cAAc,GAAGG,IAAI,CAACH,cAAL,IAAuB,EAA5C;EACA,IAAIH,YAAY,GAAGC,qBAAqB,CAAC,4JACpCK,IADmC;IAEtCX,YAAY,EAAZA,YAFsC;IAGtCQ,cAAc,EAAdA;EAHsC,GAAxC;EAKAA,cAAc,GAAGA,cAAc,CAACC,MAAf,CAAsBJ,YAAtB,CAAjB;EAEA,IAAIjF,KAAK,GAAG;IACV0J,UAAU,EAAVA,UADU;IAEVI,UAAU,EAAVA,UAFU;IAGV+G,SAAS,EAATA,SAHU;IAIVxH,UAAU,EAAVA,UAJU;IAKVzE,YAAY,EAAZA,YALU;IAMVmM,WAAW,EAAXA,WANU;IAOVnC,UAAU,EAAVA,UAPU;IAQVxJ,cAAc,EAAdA;EARU,CAAZ;;EAWA,IAAIG,IAAI,CAAC4H,WAAL,KAAqB,IAArB,IAA6B5H,IAAI,CAACK,QAAtC,EAAgD;IAC9C5F,KAAK,CAAC,aAAD,CAAL,GAAuB,SAAvB;EACD;;EAED,OAAOA,KAAP;AACD,CApDM;AAsDA,IAAM8K,YAAY,GAAG,SAAfA,YAAe,CAAAvF,IAAI,EAAI;EAClC,IACE2F,cADF,GAaI3F,IAbJ,CACE2F,cADF;EAAA,IAEEhF,SAFF,GAaIX,IAbJ,CAEEW,SAFF;EAAA,IAGEzD,IAHF,GAaI8C,IAbJ,CAGE9C,IAHF;EAAA,IAIEkR,QAJF,GAaIpO,IAbJ,CAIEoO,QAJF;EAAA,IAKEvS,KALF,GAaImE,IAbJ,CAKEnE,KALF;EAAA,IAMEsI,UANF,GAaInE,IAbJ,CAMEmE,UANF;EAAA,IAOE1E,QAPF,GAaIO,IAbJ,CAOEP,QAPF;EAAA,IAQEJ,YARF,GAaIW,IAbJ,CAQEX,YARF;EAAA,IASExC,UATF,GAaImD,IAbJ,CASEnD,UATF;EAAA,IAUEC,cAVF,GAaIkD,IAbJ,CAUElD,cAVF;EAAA,IAWEK,YAXF,GAaI6C,IAbJ,CAWE7C,YAXF;EAAA,IAYEqI,MAZF,GAaIxF,IAbJ,CAYEwF,MAZF;EAcA,IAAM3F,cAAN,GAAyBG,IAAzB,CAAMH,cAAN;EACA,IAAI8F,cAAc,IAAIhF,SAAtB,EAAiC,OAAO,EAAP;EACjC,IAAI+N,cAAc,GAAG7S,KAArB;EAAA,IACE8S,UADF;EAAA,IAEEC,aAFF;EAAA,IAGEC,SAHF;EAIA,IAAIpU,KAAK,GAAG,EAAZ;EAAA,IACEgL,SAAS,GAAG,EADd;EAEA,IAAMM,WAAW,GAAGqI,QAAQ,GAAGvS,KAAH,GAAWoQ,KAAK,CAACpQ,KAAD,EAAQ,CAAR,EAAWsI,UAAU,GAAG,CAAxB,CAA5C;;EACA,IAAIjH,IAAJ,EAAU;IACR,IAAI,CAACkR,QAAD,KAAcvS,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAIsI,UAApC,CAAJ,EAAqD,OAAO,EAAP;;IACrD,IAAItI,KAAK,GAAG,CAAZ,EAAe;MACb6S,cAAc,GAAG7S,KAAK,GAAGsI,UAAzB;IACD,CAFD,MAEO,IAAItI,KAAK,IAAIsI,UAAb,EAAyB;MAC9BuK,cAAc,GAAG7S,KAAK,GAAGsI,UAAzB;IACD;;IACD,IAAI1E,QAAQ,IAAII,cAAc,CAACsF,OAAf,CAAuBuJ,cAAvB,IAAyC,CAAzD,EAA4D;MAC1D7O,cAAc,GAAGA,cAAc,CAACC,MAAf,CAAsB4O,cAAtB,CAAjB;IACD;;IACDjU,KAAK,GAAG;MACNkG,SAAS,EAAE,IADL;MAENtB,YAAY,EAAEqP,cAFR;MAGN7O,cAAc,EAAdA,cAHM;MAINkG,WAAW,EAAE2I;IAJP,CAAR;IAMAjJ,SAAS,GAAG;MAAE9E,SAAS,EAAE,KAAb;MAAoBoF,WAAW,EAAE2I;IAAjC,CAAZ;EACD,CAjBD,MAiBO;IACLC,UAAU,GAAGD,cAAb;;IACA,IAAIA,cAAc,GAAG,CAArB,EAAwB;MACtBC,UAAU,GAAGD,cAAc,GAAGvK,UAA9B;MACA,IAAI,CAACiK,QAAL,EAAeO,UAAU,GAAG,CAAb,CAAf,KACK,IAAIxK,UAAU,GAAGrH,cAAb,KAAgC,CAApC,EACH6R,UAAU,GAAGxK,UAAU,GAAIA,UAAU,GAAGrH,cAAxC;IACH,CALD,MAKO,IAAI,CAAC4K,SAAS,CAAC1H,IAAD,CAAV,IAAoB0O,cAAc,GAAGrP,YAAzC,EAAuD;MAC5DqP,cAAc,GAAGC,UAAU,GAAGtP,YAA9B;IACD,CAFM,MAEA,IAAIxC,UAAU,IAAI6R,cAAc,IAAIvK,UAApC,EAAgD;MACrDuK,cAAc,GAAGN,QAAQ,GAAGjK,UAAH,GAAgBA,UAAU,GAAG,CAAtD;MACAwK,UAAU,GAAGP,QAAQ,GAAG,CAAH,GAAOjK,UAAU,GAAG,CAAzC;IACD,CAHM,MAGA,IAAIuK,cAAc,IAAIvK,UAAtB,EAAkC;MACvCwK,UAAU,GAAGD,cAAc,GAAGvK,UAA9B;MACA,IAAI,CAACiK,QAAL,EAAeO,UAAU,GAAGxK,UAAU,GAAGhH,YAA1B,CAAf,KACK,IAAIgH,UAAU,GAAGrH,cAAb,KAAgC,CAApC,EAAuC6R,UAAU,GAAG,CAAb;IAC7C;;IAED,IAAI,CAACP,QAAD,IAAaM,cAAc,GAAGvR,YAAjB,IAAiCgH,UAAlD,EAA8D;MAC5DwK,UAAU,GAAGxK,UAAU,GAAGhH,YAA1B;IACD;;IAEDyR,aAAa,GAAGlL,YAAY,CAAC,4JAAK1D,IAAN;MAAYwD,UAAU,EAAEkL;IAAxB,GAA5B;IACAG,SAAS,GAAGnL,YAAY,CAAC,4JAAK1D,IAAN;MAAYwD,UAAU,EAAEmL;IAAxB,GAAxB;;IACA,IAAI,CAACP,QAAL,EAAe;MACb,IAAIQ,aAAa,KAAKC,SAAtB,EAAiCH,cAAc,GAAGC,UAAjB;MACjCC,aAAa,GAAGC,SAAhB;IACD;;IACD,IAAIpP,QAAJ,EAAc;MACZI,cAAc,GAAGA,cAAc,CAACC,MAAf,CACfH,qBAAqB,CAAC,4JAAKK,IAAN;QAAYX,YAAY,EAAEqP;MAA1B,GADN,CAAjB;IAGD;;IACD,IAAI,CAAClJ,MAAL,EAAa;MACX/K,KAAK,GAAG;QACN4E,YAAY,EAAEsP,UADR;QAEN/K,UAAU,EAAEC,WAAW,CAAC,4JAAK7D,IAAN;UAAY2D,IAAI,EAAEkL;QAAlB,GAFjB;QAGNhP,cAAc,EAAdA,cAHM;QAINkG,WAAW,EAAXA;MAJM,CAAR;IAMD,CAPD,MAOO;MACLtL,KAAK,GAAG;QACNkG,SAAS,EAAE,IADL;QAENtB,YAAY,EAAEsP,UAFR;QAGN/K,UAAU,EAAEkL,kBAAkB,CAAC,4JAAK9O,IAAN;UAAY2D,IAAI,EAAEiL;QAAlB,GAHxB;QAIN/O,cAAc,EAAdA,cAJM;QAKNkG,WAAW,EAAXA;MALM,CAAR;MAOAN,SAAS,GAAG;QACV9E,SAAS,EAAE,KADD;QAEVtB,YAAY,EAAEsP,UAFJ;QAGV/K,UAAU,EAAEC,WAAW,CAAC,4JAAK7D,IAAN;UAAY2D,IAAI,EAAEkL;QAAlB,GAHb;QAIVpD,SAAS,EAAE,IAJD;QAKV1F,WAAW,EAAXA;MALU,CAAZ;IAOD;EACF;;EACD,OAAO;IAAEtL,KAAK,EAALA,KAAF;IAASgL,SAAS,EAATA;EAAT,CAAP;AACD,CAnGM;AAqGA,IAAM9C,WAAW,GAAG,SAAdA,WAAc,CAAC3C,IAAD,EAAO8F,OAAP,EAAmB;EAC5C,IAAIiJ,WAAJ,EAAiBC,WAAjB,EAA8BC,WAA9B,EAA2CC,YAA3C,EAAyDnJ,WAAzD;EACA,IACEjJ,cADF,GAQIkD,IARJ,CACElD,cADF;EAAA,IAEEK,YAFF,GAQI6C,IARJ,CAEE7C,YAFF;EAAA,IAGEgH,UAHF,GAQInE,IARJ,CAGEmE,UAHF;EAAA,IAIE9E,YAJF,GAQIW,IARJ,CAIEX,YAJF;EAAA,IAKe8P,mBALf,GAQInP,IARJ,CAKE+F,WALF;EAAA,IAMEtG,QANF,GAQIO,IARJ,CAMEP,QANF;EAAA,IAOE2O,QAPF,GAQIpO,IARJ,CAOEoO,QAPF;EASAc,YAAY,GAAG/K,UAAU,GAAGrH,cAAb,KAAgC,CAA/C;EACAiS,WAAW,GAAGG,YAAY,GAAG,CAAH,GAAO,CAAC/K,UAAU,GAAG9E,YAAd,IAA8BvC,cAA/D;;EACA,IAAIgJ,OAAO,CAAClD,OAAR,KAAoB,UAAxB,EAAoC;IAClCqM,WAAW,GACTF,WAAW,KAAK,CAAhB,GAAoBjS,cAApB,GAAqCK,YAAY,GAAG4R,WADtD;IAEAhJ,WAAW,GAAG1G,YAAY,GAAG4P,WAA7B;;IACA,IAAIxP,QAAQ,IAAI,CAAC2O,QAAjB,EAA2B;MACzBY,WAAW,GAAG3P,YAAY,GAAG4P,WAA7B;MACAlJ,WAAW,GAAGiJ,WAAW,KAAK,CAAC,CAAjB,GAAqB7K,UAAU,GAAG,CAAlC,GAAsC6K,WAApD;IACD;;IACD,IAAI,CAACZ,QAAL,EAAe;MACbrI,WAAW,GAAGoJ,mBAAmB,GAAGrS,cAApC;IACD;EACF,CAXD,MAWO,IAAIgJ,OAAO,CAAClD,OAAR,KAAoB,MAAxB,EAAgC;IACrCqM,WAAW,GAAGF,WAAW,KAAK,CAAhB,GAAoBjS,cAApB,GAAqCiS,WAAnD;IACAhJ,WAAW,GAAG1G,YAAY,GAAG4P,WAA7B;;IACA,IAAIxP,QAAQ,IAAI,CAAC2O,QAAjB,EAA2B;MACzBrI,WAAW,GACR,CAAC1G,YAAY,GAAGvC,cAAhB,IAAkCqH,UAAnC,GAAiD4K,WADnD;IAED;;IACD,IAAI,CAACX,QAAL,EAAe;MACbrI,WAAW,GAAGoJ,mBAAmB,GAAGrS,cAApC;IACD;EACF,CAVM,MAUA,IAAIgJ,OAAO,CAAClD,OAAR,KAAoB,MAAxB,EAAgC;IACrC;IACAmD,WAAW,GAAGD,OAAO,CAACjK,KAAR,GAAgBiK,OAAO,CAAChJ,cAAtC;EACD,CAHM,MAGA,IAAIgJ,OAAO,CAAClD,OAAR,KAAoB,UAAxB,EAAoC;IACzC;IACAmD,WAAW,GAAGD,OAAO,CAACjK,KAAtB;;IACA,IAAIuS,QAAJ,EAAc;MACZ,IAAIjD,SAAS,GAAGiE,gBAAgB,CAAC,4JAAKpP,IAAN;QAAY+F,WAAW,EAAXA;MAAZ,GAAhC;;MACA,IAAIA,WAAW,GAAGD,OAAO,CAACzG,YAAtB,IAAsC8L,SAAS,KAAK,MAAxD,EAAgE;QAC9DpF,WAAW,GAAGA,WAAW,GAAG5B,UAA5B;MACD,CAFD,MAEO,IAAI4B,WAAW,GAAGD,OAAO,CAACzG,YAAtB,IAAsC8L,SAAS,KAAK,OAAxD,EAAiE;QACtEpF,WAAW,GAAGA,WAAW,GAAG5B,UAA5B;MACD;IACF;EACF,CAXM,MAWA,IAAI2B,OAAO,CAAClD,OAAR,KAAoB,OAAxB,EAAiC;IACtCmD,WAAW,GAAGwB,MAAM,CAACzB,OAAO,CAACjK,KAAT,CAApB;EACD;;EACD,OAAOkK,WAAP;AACD,CApDM;AAqDA,IAAMQ,UAAU,GAAG,SAAbA,UAAa,CAACL,CAAD,EAAIM,aAAJ,EAAmBC,GAAnB,EAA2B;EACnD,IAAIP,CAAC,CAACmJ,MAAF,CAASC,OAAT,CAAiBC,KAAjB,CAAuB,uBAAvB,KAAmD,CAAC/I,aAAxD,EACE,OAAO,EAAP;EACF,IAAIN,CAAC,CAACsJ,OAAF,KAAc,EAAlB,EAAsB,OAAO/I,GAAG,GAAG,MAAH,GAAY,UAAtB;EACtB,IAAIP,CAAC,CAACsJ,OAAF,KAAc,EAAlB,EAAsB,OAAO/I,GAAG,GAAG,UAAH,GAAgB,MAA1B;EACtB,OAAO,EAAP;AACD,CANM;AAQA,IAAMM,UAAU,GAAG,SAAbA,UAAa,CAACb,CAAD,EAAIc,KAAJ,EAAWC,SAAX,EAAyB;EACjDf,CAAC,CAACmJ,MAAF,CAASC,OAAT,KAAqB,KAArB,IAA8B9C,kBAAkB,CAACtG,CAAD,CAAhD;EACA,IAAI,CAACc,KAAD,IAAW,CAACC,SAAD,IAAcf,CAAC,CAACuJ,IAAF,CAAOtK,OAAP,CAAe,OAAf,MAA4B,CAAC,CAA1D,EAA8D,OAAO,EAAP;EAC9D,OAAO;IACL4E,QAAQ,EAAE,IADL;IAEL6B,WAAW,EAAE;MACXC,MAAM,EAAE3F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaC,KAAzB,GAAiCzJ,CAAC,CAAC0J,OADhC;MAEX9D,MAAM,EAAE5F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaG,KAAzB,GAAiC3J,CAAC,CAAC4J,OAFhC;MAGX/D,IAAI,EAAE7F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaC,KAAzB,GAAiCzJ,CAAC,CAAC0J,OAH9B;MAIX5D,IAAI,EAAE9F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaG,KAAzB,GAAiC3J,CAAC,CAAC4J;IAJ9B;EAFR,CAAP;AASD,CAZM;AAaA,IAAM5I,SAAS,GAAG,SAAZA,SAAY,CAAChB,CAAD,EAAIlG,IAAJ,EAAa;EACpC;EACA,IACEuL,SADF,GAoBIvL,IApBJ,CACEuL,SADF;EAAA,IAEE5K,SAFF,GAoBIX,IApBJ,CAEEW,SAFF;EAAA,IAGEqH,QAHF,GAoBIhI,IApBJ,CAGEgI,QAHF;EAAA,IAIE+H,YAJF,GAoBI/P,IApBJ,CAIE+P,YAJF;EAAA,IAKElJ,eALF,GAoBI7G,IApBJ,CAKE6G,eALF;EAAA,IAMEJ,GANF,GAoBIzG,IApBJ,CAMEyG,GANF;EAAA,IAOEpH,YAPF,GAoBIW,IApBJ,CAOEX,YAPF;EAAA,IAQE2Q,YARF,GAoBIhQ,IApBJ,CAQEgQ,YARF;EAAA,IASE5E,WATF,GAoBIpL,IApBJ,CASEoL,WATF;EAAA,IAUE6E,MAVF,GAoBIjQ,IApBJ,CAUEiQ,MAVF;EAAA,IAWEvE,MAXF,GAoBI1L,IApBJ,CAWE0L,MAXF;EAAA,IAYEC,OAZF,GAoBI3L,IApBJ,CAYE2L,OAZF;EAAA,IAaExH,UAbF,GAoBInE,IApBJ,CAaEmE,UAbF;EAAA,IAcErH,cAdF,GAoBIkD,IApBJ,CAcElD,cAdF;EAAA,IAeEsR,QAfF,GAoBIpO,IApBJ,CAeEoO,QAfF;EAAA,IAgBExC,WAhBF,GAoBI5L,IApBJ,CAgBE4L,WAhBF;EAAA,IAiBEsE,UAjBF,GAoBIlQ,IApBJ,CAiBEkQ,UAjBF;EAAA,IAkBE7G,UAlBF,GAoBIrJ,IApBJ,CAkBEqJ,UAlBF;EAAA,IAmBEiC,SAnBF,GAoBItL,IApBJ,CAmBEsL,SAnBF;EAqBA,IAAIC,SAAJ,EAAe;EACf,IAAI5K,SAAJ,EAAe,OAAO6L,kBAAkB,CAACtG,CAAD,CAAzB;EACf,IAAI8B,QAAQ,IAAI+H,YAAZ,IAA4BlJ,eAAhC,EAAiD2F,kBAAkB,CAACtG,CAAD,CAAlB;EACjD,IAAIuF,SAAJ;EAAA,IACEhR,KAAK,GAAG,EADV;EAEA,IAAI0V,OAAO,GAAGzM,YAAY,CAAC1D,IAAD,CAA1B;EACA4L,WAAW,CAACG,IAAZ,GAAmB7F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaC,KAAzB,GAAiCzJ,CAAC,CAAC0J,OAAtD;EACAhE,WAAW,CAACI,IAAZ,GAAmB9F,CAAC,CAACwJ,OAAF,GAAYxJ,CAAC,CAACwJ,OAAF,CAAU,CAAV,EAAaG,KAAzB,GAAiC3J,CAAC,CAAC4J,OAAtD;EACAlE,WAAW,CAACwE,WAAZ,GAA0B/D,IAAI,CAAC2B,KAAL,CACxB3B,IAAI,CAACgE,IAAL,CAAUhE,IAAI,CAACiE,GAAL,CAAS1E,WAAW,CAACG,IAAZ,GAAmBH,WAAW,CAACC,MAAxC,EAAgD,CAAhD,CAAV,CADwB,CAA1B;EAGA,IAAI0E,mBAAmB,GAAGlE,IAAI,CAAC2B,KAAL,CACxB3B,IAAI,CAACgE,IAAL,CAAUhE,IAAI,CAACiE,GAAL,CAAS1E,WAAW,CAACI,IAAZ,GAAmBJ,WAAW,CAACE,MAAxC,EAAgD,CAAhD,CAAV,CADwB,CAA1B;;EAGA,IAAI,CAACjF,eAAD,IAAoB,CAAC8E,OAArB,IAAgC4E,mBAAmB,GAAG,EAA1D,EAA8D;IAC5D,OAAO;MAAEhF,SAAS,EAAE;IAAb,CAAP;EACD;;EACD,IAAI1E,eAAJ,EAAqB+E,WAAW,CAACwE,WAAZ,GAA0BG,mBAA1B;EACrB,IAAIC,cAAc,GAChB,CAAC,CAAC/J,GAAD,GAAO,CAAP,GAAW,CAAC,CAAb,KAAmBmF,WAAW,CAACG,IAAZ,GAAmBH,WAAW,CAACC,MAA/B,GAAwC,CAAxC,GAA4C,CAAC,CAAhE,CADF;EAEA,IAAIhF,eAAJ,EACE2J,cAAc,GAAG5E,WAAW,CAACI,IAAZ,GAAmBJ,WAAW,CAACE,MAA/B,GAAwC,CAAxC,GAA4C,CAAC,CAA9D;EAEF,IAAI2E,QAAQ,GAAGpE,IAAI,CAACkC,IAAL,CAAUpK,UAAU,GAAGrH,cAAvB,CAAf;EACA,IAAI4T,cAAc,GAAGhD,iBAAiB,CAAC1N,IAAI,CAAC4L,WAAN,EAAmB/E,eAAnB,CAAtC;EACA,IAAI8J,gBAAgB,GAAG/E,WAAW,CAACwE,WAAnC;;EACA,IAAI,CAAChC,QAAL,EAAe;IACb,IACG/O,YAAY,KAAK,CAAjB,KAAuBqR,cAAc,KAAK,OAAnB,IAA8BA,cAAc,KAAK,MAAxE,CAAD,IACCrR,YAAY,GAAG,CAAf,IAAoBoR,QAApB,KAAiCC,cAAc,KAAK,MAAnB,IAA6BA,cAAc,KAAK,IAAjF,CADD,IAEC,CAAChJ,SAAS,CAAC1H,IAAD,CAAV,KAAqB0Q,cAAc,KAAK,MAAnB,IAA6BA,cAAc,KAAK,IAArE,CAHH,EAIE;MACAC,gBAAgB,GAAG/E,WAAW,CAACwE,WAAZ,GAA0BJ,YAA7C;;MACA,IAAI5E,WAAW,KAAK,KAAhB,IAAyB6E,MAA7B,EAAqC;QACnCA,MAAM,CAACS,cAAD,CAAN;QACAjW,KAAK,CAAC,aAAD,CAAL,GAAuB,IAAvB;MACD;IACF;EACF;;EACD,IAAI,CAACiR,MAAD,IAAWwE,UAAf,EAA2B;IACzBA,UAAU,CAACQ,cAAD,CAAV;IACAjW,KAAK,CAAC,QAAD,CAAL,GAAkB,IAAlB;EACD;;EACD,IAAI,CAACuN,QAAL,EAAe;IACb,IAAI,CAACvB,GAAL,EAAU;MACRgF,SAAS,GAAG0E,OAAO,GAAGQ,gBAAgB,GAAGH,cAAzC;IACD,CAFD,MAEO;MACL/E,SAAS,GAAG0E,OAAO,GAAGQ,gBAAgB,GAAGH,cAAzC;IACD;EACF,CAND,MAMO;IACL/E,SAAS,GACP0E,OAAO,GAAGQ,gBAAgB,IAAItH,UAAU,GAAGiC,SAAjB,CAAhB,GAA8CkF,cAD1D;EAED;;EACD,IAAI3J,eAAJ,EAAqB;IACnB4E,SAAS,GAAG0E,OAAO,GAAGQ,gBAAgB,GAAGH,cAAzC;EACD;;EACD/V,KAAK,GAAG,4JACHA,KADA;IAEHmR,WAAW,EAAXA,WAFG;IAGHH,SAAS,EAATA,SAHG;IAIH7H,UAAU,EAAEC,WAAW,CAAC,4JAAK7D,IAAN;MAAY2D,IAAI,EAAE8H;IAAlB;EAJpB,EAAL;;EAMA,IACEY,IAAI,CAAC6B,GAAL,CAAStC,WAAW,CAACG,IAAZ,GAAmBH,WAAW,CAACC,MAAxC,IACAQ,IAAI,CAAC6B,GAAL,CAAStC,WAAW,CAACI,IAAZ,GAAmBJ,WAAW,CAACE,MAAxC,IAAkD,GAFpD,EAGE;IACA,OAAOrR,KAAP;EACD;;EACD,IAAImR,WAAW,CAACwE,WAAZ,GAA0B,EAA9B,EAAkC;IAChC3V,KAAK,CAAC,SAAD,CAAL,GAAmB,IAAnB;IACA+R,kBAAkB,CAACtG,CAAD,CAAlB;EACD;;EACD,OAAOzL,KAAP;AACD,CAhGM;AAiGA,IAAM0M,QAAQ,GAAG,SAAXA,QAAW,CAACjB,CAAD,EAAIlG,IAAJ,EAAa;EACnC,IACE+J,QADF,GAcI/J,IAdJ,CACE+J,QADF;EAAA,IAEE/C,KAFF,GAcIhH,IAdJ,CAEEgH,KAFF;EAAA,IAGE4E,WAHF,GAcI5L,IAdJ,CAGE4L,WAHF;EAAA,IAIEN,SAJF,GAcItL,IAdJ,CAIEsL,SAJF;EAAA,IAKEsF,cALF,GAcI5Q,IAdJ,CAKE4Q,cALF;EAAA,IAME/J,eANF,GAcI7G,IAdJ,CAME6G,eANF;EAAA,IAOEwC,UAPF,GAcIrJ,IAdJ,CAOEqJ,UAPF;EAAA,IAQE0G,YARF,GAcI/P,IAdJ,CAQE+P,YARF;EAAA,IASExE,SATF,GAcIvL,IAdJ,CASEuL,SATF;EAAA,IAUEsF,OAVF,GAcI7Q,IAdJ,CAUE6Q,OAVF;EAAA,IAWE9K,WAXF,GAcI/F,IAdJ,CAWE+F,WAXF;EAAA,IAYE1G,YAZF,GAcIW,IAdJ,CAYEX,YAZF;EAAA,IAaE+O,QAbF,GAcIpO,IAdJ,CAaEoO,QAbF;;EAeA,IAAI,CAACrE,QAAL,EAAe;IACb,IAAI/C,KAAJ,EAAWwF,kBAAkB,CAACtG,CAAD,CAAlB;IACX,OAAO,EAAP;EACD;;EACD,IAAI4K,QAAQ,GAAGjK,eAAe,GAC1BwC,UAAU,GAAGuH,cADa,GAE1BtF,SAAS,GAAGsF,cAFhB;EAGA,IAAIF,cAAc,GAAGhD,iBAAiB,CAAC9B,WAAD,EAAc/E,eAAd,CAAtC,CAvBmC,CAwBnC;;EACA,IAAIpM,KAAK,GAAG;IACVsP,QAAQ,EAAE,KADA;IAEVqB,WAAW,EAAE,KAFH;IAGVG,SAAS,EAAE,KAHD;IAIVI,OAAO,EAAE,KAJC;IAKVD,MAAM,EAAE,KALE;IAMVD,SAAS,EAAE,IAND;IAOVG,WAAW,EAAE;EAPH,CAAZ;;EASA,IAAIL,SAAJ,EAAe;IACb,OAAO9Q,KAAP;EACD;;EACD,IAAI,CAACmR,WAAW,CAACwE,WAAjB,EAA8B;IAC5B,OAAO3V,KAAP;EACD;;EACD,IAAImR,WAAW,CAACwE,WAAZ,GAA0BU,QAA9B,EAAwC;IACtCtE,kBAAkB,CAACtG,CAAD,CAAlB;;IACA,IAAI2K,OAAJ,EAAa;MACXA,OAAO,CAACH,cAAD,CAAP;IACD;;IACD,IAAIvM,UAAJ,EAAgBlG,QAAhB;IACA,IAAI8S,WAAW,GAAG3C,QAAQ,GAAG/O,YAAH,GAAkB0G,WAA5C;;IACA,QAAQ2K,cAAR;MACE,KAAK,MAAL;MACA,KAAK,IAAL;QACEzS,QAAQ,GAAG8S,WAAW,GAAGC,aAAa,CAAChR,IAAD,CAAtC;QACAmE,UAAU,GAAG4L,YAAY,GAAGkB,cAAc,CAACjR,IAAD,EAAO/B,QAAP,CAAjB,GAAoCA,QAA7D;QACAxD,KAAK,CAAC,kBAAD,CAAL,GAA4B,CAA5B;QACA;;MACF,KAAK,OAAL;MACA,KAAK,MAAL;QACEwD,QAAQ,GAAG8S,WAAW,GAAGC,aAAa,CAAChR,IAAD,CAAtC;QACAmE,UAAU,GAAG4L,YAAY,GAAGkB,cAAc,CAACjR,IAAD,EAAO/B,QAAP,CAAjB,GAAoCA,QAA7D;QACAxD,KAAK,CAAC,kBAAD,CAAL,GAA4B,CAA5B;QACA;;MACF;QACE0J,UAAU,GAAG4M,WAAb;IAdJ;;IAgBAtW,KAAK,CAAC,qBAAD,CAAL,GAA+B0J,UAA/B;EACD,CAxBD,MAwBO;IACL;IACA,IAAI+G,WAAW,GAAGxH,YAAY,CAAC1D,IAAD,CAA9B;IACAvF,KAAK,CAAC,YAAD,CAAL,GAAsBqU,kBAAkB,CAAC,4JAAK9O,IAAN;MAAY2D,IAAI,EAAEuH;IAAlB,GAAxC;EACD;;EACD,OAAOzQ,KAAP;AACD,CAtEM;AAuEA,IAAMyW,mBAAmB,GAAG,SAAtBA,mBAAsB,CAAAlR,IAAI,EAAI;EACzC,IAAIsM,GAAG,GAAGtM,IAAI,CAACoO,QAAL,GAAgBpO,IAAI,CAACmE,UAAL,GAAkB,CAAlC,GAAsCnE,IAAI,CAACmE,UAArD;EACA,IAAIzJ,UAAU,GAAGsF,IAAI,CAACoO,QAAL,GAAgBpO,IAAI,CAAC7C,YAAL,GAAoB,CAAC,CAArC,GAAyC,CAA1D;EACA,IAAIgU,OAAO,GAAGnR,IAAI,CAACoO,QAAL,GAAgBpO,IAAI,CAAC7C,YAAL,GAAoB,CAAC,CAArC,GAAyC,CAAvD;EACA,IAAIiU,OAAO,GAAG,EAAd;;EACA,OAAO1W,UAAU,GAAG4R,GAApB,EAAyB;IACvB8E,OAAO,CAAChW,IAAR,CAAaV,UAAb;IACAA,UAAU,GAAGyW,OAAO,GAAGnR,IAAI,CAAClD,cAA5B;IACAqU,OAAO,IAAI9E,IAAI,CAACE,GAAL,CAASvM,IAAI,CAAClD,cAAd,EAA8BkD,IAAI,CAAC7C,YAAnC,CAAX;EACD;;EACD,OAAOiU,OAAP;AACD,CAXM;AAYA,IAAMH,cAAc,GAAG,SAAjBA,cAAiB,CAACjR,IAAD,EAAOnE,KAAP,EAAiB;EAC7C,IAAMwV,UAAU,GAAGH,mBAAmB,CAAClR,IAAD,CAAtC;EACA,IAAIsR,aAAa,GAAG,CAApB;;EACA,IAAIzV,KAAK,GAAGwV,UAAU,CAACA,UAAU,CAACrT,MAAX,GAAoB,CAArB,CAAtB,EAA+C;IAC7CnC,KAAK,GAAGwV,UAAU,CAACA,UAAU,CAACrT,MAAX,GAAoB,CAArB,CAAlB;EACD,CAFD,MAEO;IACL,KAAK,IAAIuT,CAAT,IAAcF,UAAd,EAA0B;MACxB,IAAIxV,KAAK,GAAGwV,UAAU,CAACE,CAAD,CAAtB,EAA2B;QACzB1V,KAAK,GAAGyV,aAAR;QACA;MACD;;MACDA,aAAa,GAAGD,UAAU,CAACE,CAAD,CAA1B;IACD;EACF;;EACD,OAAO1V,KAAP;AACD,CAfM;AAgBA,IAAMmV,aAAa,GAAG,SAAhBA,aAAgB,CAAAhR,IAAI,EAAI;EACnC,IAAMwR,YAAY,GAAGxR,IAAI,CAACnD,UAAL,GACjBmD,IAAI,CAACuE,UAAL,GAAkB8H,IAAI,CAACgB,KAAL,CAAWrN,IAAI,CAAC7C,YAAL,GAAoB,CAA/B,CADD,GAEjB,CAFJ;;EAGA,IAAI6C,IAAI,CAAC+P,YAAT,EAAuB;IACrB,IAAI0B,WAAJ;IACA,IAAMC,SAAS,GAAG1R,IAAI,CAACC,OAAvB;IACA,IAAM0R,MAAM,GACTD,SAAS,CAACxQ,gBAAV,IACCwQ,SAAS,CAACxQ,gBAAV,CAA2B,cAA3B,CADF,IAEA,EAHF;IAIAC,KAAK,CAACyQ,IAAN,CAAWD,MAAX,EAAmBE,KAAnB,CAAyB,UAAAzX,KAAK,EAAI;MAChC,IAAI,CAAC4F,IAAI,CAACgI,QAAV,EAAoB;QAClB,IACE5N,KAAK,CAAC0X,UAAN,GAAmBN,YAAnB,GAAkCjE,QAAQ,CAACnT,KAAD,CAAR,GAAkB,CAApD,GACA4F,IAAI,CAACyL,SAAL,GAAiB,CAAC,CAFpB,EAGE;UACAgG,WAAW,GAAGrX,KAAd;UACA,OAAO,KAAP;QACD;MACF,CARD,MAQO;QACL,IAAIA,KAAK,CAAC2X,SAAN,GAAkBxS,SAAS,CAACnF,KAAD,CAAT,GAAmB,CAArC,GAAyC4F,IAAI,CAACyL,SAAL,GAAiB,CAAC,CAA/D,EAAkE;UAChEgG,WAAW,GAAGrX,KAAd;UACA,OAAO,KAAP;QACD;MACF;;MAED,OAAO,IAAP;IACD,CAjBD;;IAmBA,IAAI,CAACqX,WAAL,EAAkB;MAChB,OAAO,CAAP;IACD;;IACD,IAAMO,YAAY,GAChBhS,IAAI,CAACyG,GAAL,KAAa,IAAb,GACIzG,IAAI,CAACmE,UAAL,GAAkBnE,IAAI,CAACX,YAD3B,GAEIW,IAAI,CAACX,YAHX;IAIA,IAAM4S,eAAe,GACnB5F,IAAI,CAAC6B,GAAL,CAASuD,WAAW,CAACS,OAAZ,CAAoBrW,KAApB,GAA4BmW,YAArC,KAAsD,CADxD;IAEA,OAAOC,eAAP;EACD,CApCD,MAoCO;IACL,OAAOjS,IAAI,CAAClD,cAAZ;EACD;AACF,CA3CM;AA6CA,IAAMqV,aAAa,GAAG,SAAhBA,aAAgB,CAACnS,IAAD,EAAOoS,SAAP;EAAA,OAC3B;IACAA,SAAS,CAACC,MAAV,CAAiB,UAAC3M,KAAD,EAAQlH,GAAR;MAAA,OAAgBkH,KAAK,IAAI1F,IAAI,CAACgL,cAAL,CAAoBxM,GAApB,CAAzB;IAAA,CAAjB,EAAoE,IAApE,IACI,IADJ,GAEIxB,OAAO,CAACsV,KAAR,CAAc,eAAd,EAA+BtS,IAA/B;EAJuB;AAAA,CAAtB;AAMA,IAAM6D,WAAW,GAAG,SAAdA,WAAc,CAAA7D,IAAI,EAAI;EACjCmS,aAAa,CAACnS,IAAD,EAAO,CAClB,MADkB,EAElB,eAFkB,EAGlB,YAHkB,EAIlB,cAJkB,EAKlB,YALkB,CAAP,CAAb;EAOA,IAAI8D,UAAJ,EAAgByO,WAAhB;EACA,IAAMC,aAAa,GAAGxS,IAAI,CAACmE,UAAL,GAAkB,IAAInE,IAAI,CAAC7C,YAAjD;;EACA,IAAI,CAAC6C,IAAI,CAACgI,QAAV,EAAoB;IAClBlE,UAAU,GAAG2O,cAAc,CAACzS,IAAD,CAAd,GAAuBA,IAAI,CAACuE,UAAzC;EACD,CAFD,MAEO;IACLgO,WAAW,GAAGC,aAAa,GAAGxS,IAAI,CAACwL,WAAnC;EACD;;EACD,IAAInN,KAAK,GAAG;IACVqU,OAAO,EAAE,CADC;IAEVC,UAAU,EAAE,EAFF;IAGVC,gBAAgB,EAAE;EAHR,CAAZ;;EAKA,IAAI5S,IAAI,CAAC6S,YAAT,EAAuB;IACrB,IAAIC,eAAe,GAAG,CAAC9S,IAAI,CAACgI,QAAN,GAClB,iBAAiBhI,IAAI,CAAC2D,IAAtB,GAA6B,eADX,GAElB,sBAAsB3D,IAAI,CAAC2D,IAA3B,GAAkC,UAFtC;IAGA,IAAIoP,SAAS,GAAG,CAAC/S,IAAI,CAACgI,QAAN,GACZ,iBAAiBhI,IAAI,CAAC2D,IAAtB,GAA6B,eADjB,GAEZ,sBAAsB3D,IAAI,CAAC2D,IAA3B,GAAkC,UAFtC;IAGA,IAAIqP,WAAW,GAAG,CAAChT,IAAI,CAACgI,QAAN,GACd,gBAAgBhI,IAAI,CAAC2D,IAArB,GAA4B,KADd,GAEd,gBAAgB3D,IAAI,CAAC2D,IAArB,GAA4B,KAFhC;IAGAtF,KAAK,GAAG,4JACHA,KADA;MAEHyU,eAAe,EAAfA,eAFG;MAGHC,SAAS,EAATA,SAHG;MAIHC,WAAW,EAAXA;IAJG,EAAL;EAMD,CAhBD,MAgBO;IACL,IAAIhT,IAAI,CAACgI,QAAT,EAAmB;MACjB3J,KAAK,CAAC,KAAD,CAAL,GAAe2B,IAAI,CAAC2D,IAApB;IACD,CAFD,MAEO;MACLtF,KAAK,CAAC,MAAD,CAAL,GAAgB2B,IAAI,CAAC2D,IAArB;IACD;EACF;;EACD,IAAI3D,IAAI,CAAC9C,IAAT,EAAemB,KAAK,GAAG;IAAEqU,OAAO,EAAE;EAAX,CAAR;EACf,IAAI5O,UAAJ,EAAgBzF,KAAK,CAACC,KAAN,GAAcwF,UAAd;EAChB,IAAIyO,WAAJ,EAAiBlU,KAAK,CAACiB,MAAN,GAAeiT,WAAf,CA7CgB,CA+CjC;;EACA,IAAIxX,MAAM,IAAI,CAACA,MAAM,CAAC4G,gBAAlB,IAAsC5G,MAAM,CAAC6G,WAAjD,EAA8D;IAC5D,IAAI,CAAC5B,IAAI,CAACgI,QAAV,EAAoB;MAClB3J,KAAK,CAAC4U,UAAN,GAAmBjT,IAAI,CAAC2D,IAAL,GAAY,IAA/B;IACD,CAFD,MAEO;MACLtF,KAAK,CAAC6U,SAAN,GAAkBlT,IAAI,CAAC2D,IAAL,GAAY,IAA9B;IACD;EACF;;EAED,OAAOtF,KAAP;AACD,CAzDM;AA0DA,IAAMyQ,kBAAkB,GAAG,SAArBA,kBAAqB,CAAA9O,IAAI,EAAI;EACxCmS,aAAa,CAACnS,IAAD,EAAO,CAClB,MADkB,EAElB,eAFkB,EAGlB,YAHkB,EAIlB,cAJkB,EAKlB,YALkB,EAMlB,OANkB,EAOlB,SAPkB,CAAP,CAAb;EASA,IAAI3B,KAAK,GAAGwF,WAAW,CAAC7D,IAAD,CAAvB,CAVwC,CAWxC;;EACA,IAAIA,IAAI,CAAC6S,YAAT,EAAuB;IACrBxU,KAAK,CAACuU,gBAAN,GACE,uBAAuB5S,IAAI,CAACe,KAA5B,GAAoC,KAApC,GAA4Cf,IAAI,CAACmT,OADnD;IAEA9U,KAAK,CAACsU,UAAN,GAAmB,eAAe3S,IAAI,CAACe,KAApB,GAA4B,KAA5B,GAAoCf,IAAI,CAACmT,OAA5D;EACD,CAJD,MAIO;IACL,IAAInT,IAAI,CAACgI,QAAT,EAAmB;MACjB3J,KAAK,CAACsU,UAAN,GAAmB,SAAS3S,IAAI,CAACe,KAAd,GAAsB,KAAtB,GAA8Bf,IAAI,CAACmT,OAAtD;IACD,CAFD,MAEO;MACL9U,KAAK,CAACsU,UAAN,GAAmB,UAAU3S,IAAI,CAACe,KAAf,GAAuB,KAAvB,GAA+Bf,IAAI,CAACmT,OAAvD;IACD;EACF;;EACD,OAAO9U,KAAP;AACD,CAxBM;AAyBA,IAAMqF,YAAY,GAAG,SAAfA,YAAe,CAAA1D,IAAI,EAAI;EAClC,IAAIA,IAAI,CAACpB,OAAT,EAAkB;IAChB,OAAO,CAAP;EACD;;EAEDuT,aAAa,CAACnS,IAAD,EAAO,CAClB,YADkB,EAElB,UAFkB,EAGlB,UAHkB,EAIlB,YAJkB,EAKlB,YALkB,EAMlB,cANkB,EAOlB,gBAPkB,EAQlB,YARkB,EASlB,WATkB,EAUlB,eAVkB,EAWlB,aAXkB,CAAP,CAAb;EAcA,IACEwD,UADF,GAcIxD,IAdJ,CACEwD,UADF;EAAA,IAEEtD,QAFF,GAcIF,IAdJ,CAEEE,QAFF;EAAA,IAGEkO,QAHF,GAcIpO,IAdJ,CAGEoO,QAHF;EAAA,IAIEvR,UAJF,GAcImD,IAdJ,CAIEnD,UAJF;EAAA,IAKEsH,UALF,GAcInE,IAdJ,CAKEmE,UALF;EAAA,IAMEhH,YANF,GAcI6C,IAdJ,CAME7C,YANF;EAAA,IAOEL,cAPF,GAcIkD,IAdJ,CAOElD,cAPF;EAAA,IAQEyH,UARF,GAcIvE,IAdJ,CAQEuE,UARF;EAAA,IASE+G,SATF,GAcItL,IAdJ,CASEsL,SATF;EAAA,IAUE5N,aAVF,GAcIsC,IAdJ,CAUEtC,aAVF;EAAA,IAWE8N,WAXF,GAcIxL,IAdJ,CAWEwL,WAXF;EAAA,IAYEtO,IAZF,GAcI8C,IAdJ,CAYE9C,IAZF;EAAA,IAaE8K,QAbF,GAcIhI,IAdJ,CAaEgI,QAbF;EAgBA,IAAIiH,WAAW,GAAG,CAAlB;EACA,IAAIxL,UAAJ;EACA,IAAIsC,WAAJ;EACA,IAAIqN,cAAc,GAAG,CAArB;;EAEA,IAAIlW,IAAI,IAAI8C,IAAI,CAACmE,UAAL,KAAoB,CAAhC,EAAmC;IACjC,OAAO,CAAP;EACD;;EAED,IAAIkP,cAAc,GAAG,CAArB;;EACA,IAAIjF,QAAJ,EAAc;IACZiF,cAAc,GAAG,CAACnP,YAAY,CAAClE,IAAD,CAA9B,CADY,CAC0B;IACtC;;IACA,IACEmE,UAAU,GAAGrH,cAAb,KAAgC,CAAhC,IACA0G,UAAU,GAAG1G,cAAb,GAA8BqH,UAFhC,EAGE;MACAkP,cAAc,GAAG,EAAE7P,UAAU,GAAGW,UAAb,GACfhH,YAAY,IAAIqG,UAAU,GAAGW,UAAjB,CADG,GAEfA,UAAU,GAAGrH,cAFA,CAAjB;IAGD,CAVW,CAWZ;;;IACA,IAAID,UAAJ,EAAgB;MACdwW,cAAc,IAAI/F,QAAQ,CAACnQ,YAAY,GAAG,CAAhB,CAA1B;IACD;EACF,CAfD,MAeO;IACL,IACEgH,UAAU,GAAGrH,cAAb,KAAgC,CAAhC,IACA0G,UAAU,GAAG1G,cAAb,GAA8BqH,UAFhC,EAGE;MACAkP,cAAc,GAAGlW,YAAY,GAAIgH,UAAU,GAAGrH,cAA9C;IACD;;IACD,IAAID,UAAJ,EAAgB;MACdwW,cAAc,GAAG/F,QAAQ,CAACnQ,YAAY,GAAG,CAAhB,CAAzB;IACD;EACF;;EACD8R,WAAW,GAAGoE,cAAc,GAAG9O,UAA/B;EACA6O,cAAc,GAAGC,cAAc,GAAG7H,WAAlC;;EAEA,IAAI,CAACxD,QAAL,EAAe;IACbvE,UAAU,GAAGD,UAAU,GAAGe,UAAb,GAA0B,CAAC,CAA3B,GAA+B0K,WAA5C;EACD,CAFD,MAEO;IACLxL,UAAU,GAAGD,UAAU,GAAGgI,WAAb,GAA2B,CAAC,CAA5B,GAAgC4H,cAA7C;EACD;;EAED,IAAI1V,aAAa,KAAK,IAAtB,EAA4B;IAC1B,IAAI4V,gBAAJ;IACA,IAAMC,SAAS,GAAGrT,QAAQ,IAAIA,QAAQ,CAACkD,IAAvC;IACAkQ,gBAAgB,GAAG9P,UAAU,GAAGU,YAAY,CAAClE,IAAD,CAA5C;IACA+F,WAAW,GAAGwN,SAAS,IAAIA,SAAS,CAACC,UAAV,CAAqBF,gBAArB,CAA3B;IACA7P,UAAU,GAAGsC,WAAW,GAAGA,WAAW,CAAC+L,UAAZ,GAAyB,CAAC,CAA7B,GAAiC,CAAzD;;IACA,IAAIjV,UAAU,KAAK,IAAnB,EAAyB;MACvByW,gBAAgB,GAAGlF,QAAQ,GACvB5K,UAAU,GAAGU,YAAY,CAAClE,IAAD,CADF,GAEvBwD,UAFJ;MAGAuC,WAAW,GAAGwN,SAAS,IAAIA,SAAS,CAACnW,QAAV,CAAmBkW,gBAAnB,CAA3B;MACA7P,UAAU,GAAG,CAAb;;MACA,KAAK,IAAIrJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGkZ,gBAA5B,EAA8ClZ,KAAK,EAAnD,EAAuD;QACrDqJ,UAAU,IACR8P,SAAS,IACTA,SAAS,CAACnW,QAAV,CAAmBhD,KAAnB,CADA,IAEAmZ,SAAS,CAACnW,QAAV,CAAmBhD,KAAnB,EAA0BoT,WAH5B;MAID;;MACD/J,UAAU,IAAI6J,QAAQ,CAACtN,IAAI,CAACwJ,aAAN,CAAtB;MACA/F,UAAU,IAAIsC,WAAW,IAAI,CAACuF,SAAS,GAAGvF,WAAW,CAACyH,WAAzB,IAAwC,CAArE;IACD;EACF;;EAED,OAAO/J,UAAP;AACD,CAxGM;AA0GA,IAAMS,YAAY,GAAG,SAAfA,YAAe,CAAAlE,IAAI,EAAI;EAClC,IAAIA,IAAI,CAACpB,OAAL,IAAgB,CAACoB,IAAI,CAACoO,QAA1B,EAAoC;IAClC,OAAO,CAAP;EACD;;EACD,IAAIpO,IAAI,CAACtC,aAAT,EAAwB;IACtB,OAAOsC,IAAI,CAACmE,UAAZ;EACD;;EACD,OAAOnE,IAAI,CAAC7C,YAAL,IAAqB6C,IAAI,CAACnD,UAAL,GAAkB,CAAlB,GAAsB,CAA3C,CAAP;AACD,CARM;AAUA,IAAMwH,aAAa,GAAG,SAAhBA,aAAgB,CAAArE,IAAI,EAAI;EACnC,IAAIA,IAAI,CAACpB,OAAL,IAAgB,CAACoB,IAAI,CAACoO,QAA1B,EAAoC;IAClC,OAAO,CAAP;EACD;;EACD,OAAOpO,IAAI,CAACmE,UAAZ;AACD,CALM;AAOA,IAAMsO,cAAc,GAAG,SAAjBA,cAAiB,CAAAzS,IAAI;EAAA,OAChCA,IAAI,CAACmE,UAAL,KAAoB,CAApB,GACI,CADJ,GAEID,YAAY,CAAClE,IAAD,CAAZ,GAAqBA,IAAI,CAACmE,UAA1B,GAAuCE,aAAa,CAACrE,IAAD,CAHxB;AAAA,CAA3B;AAIA,IAAMoP,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAApP,IAAI,EAAI;EACtC,IAAIA,IAAI,CAAC+F,WAAL,GAAmB/F,IAAI,CAACX,YAA5B,EAA0C;IACxC,IAAIW,IAAI,CAAC+F,WAAL,GAAmB/F,IAAI,CAACX,YAAL,GAAoBoU,aAAa,CAACzT,IAAD,CAAxD,EAAgE;MAC9D,OAAO,MAAP;IACD;;IACD,OAAO,OAAP;EACD,CALD,MAKO;IACL,IAAIA,IAAI,CAAC+F,WAAL,GAAmB/F,IAAI,CAACX,YAAL,GAAoBqU,YAAY,CAAC1T,IAAD,CAAvD,EAA+D;MAC7D,OAAO,OAAP;IACD;;IACD,OAAO,MAAP;EACD;AACF,CAZM;AAcA,IAAMyT,aAAa,GAAG,SAAhBA,aAAgB,OAKvB;EAAA,IAJJtW,YAII,QAJJA,YAII;EAAA,IAHJN,UAGI,QAHJA,UAGI;EAAA,IAFJ4J,GAEI,QAFJA,GAEI;EAAA,IADJ+C,aACI,QADJA,aACI;;EACJ;EACA,IAAI3M,UAAJ,EAAgB;IACd,IAAI8W,KAAK,GAAG,CAACxW,YAAY,GAAG,CAAhB,IAAqB,CAArB,GAAyB,CAArC;IACA,IAAImQ,QAAQ,CAAC9D,aAAD,CAAR,GAA0B,CAA9B,EAAiCmK,KAAK,IAAI,CAAT;IACjC,IAAIlN,GAAG,IAAItJ,YAAY,GAAG,CAAf,KAAqB,CAAhC,EAAmCwW,KAAK,IAAI,CAAT;IACnC,OAAOA,KAAP;EACD;;EACD,IAAIlN,GAAJ,EAAS;IACP,OAAO,CAAP;EACD;;EACD,OAAOtJ,YAAY,GAAG,CAAtB;AACD,CAjBM;AAmBA,IAAMuW,YAAY,GAAG,SAAfA,YAAe,QAKtB;EAAA,IAJJvW,YAII,SAJJA,YAII;EAAA,IAHJN,UAGI,SAHJA,UAGI;EAAA,IAFJ4J,GAEI,SAFJA,GAEI;EAAA,IADJ+C,aACI,SADJA,aACI;;EACJ;EACA,IAAI3M,UAAJ,EAAgB;IACd,IAAI8G,IAAI,GAAG,CAACxG,YAAY,GAAG,CAAhB,IAAqB,CAArB,GAAyB,CAApC;IACA,IAAImQ,QAAQ,CAAC9D,aAAD,CAAR,GAA0B,CAA9B,EAAiC7F,IAAI,IAAI,CAAR;IACjC,IAAI,CAAC8C,GAAD,IAAQtJ,YAAY,GAAG,CAAf,KAAqB,CAAjC,EAAoCwG,IAAI,IAAI,CAAR;IACpC,OAAOA,IAAP;EACD;;EACD,IAAI8C,GAAJ,EAAS;IACP,OAAOtJ,YAAY,GAAG,CAAtB;EACD;;EACD,OAAO,CAAP;AACD,CAjBM;AAmBA,IAAMjB,SAAS,GAAG,SAAZA,SAAY;EAAA,OACvB,CAAC,EACC,OAAOnB,MAAP,KAAkB,WAAlB,IACAA,MAAM,CAACkG,QADP,IAEAlG,MAAM,CAACkG,QAAP,CAAgB2S,aAHjB,CADsB;AAAA,CAAlB,C;;;;;;;AC90BP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;;;;;;;;;AAEb;AACA;CAOA;;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAkB,CAAA7T,IAAI,EAAI;EAC9B,IAAI8T,WAAJ,EAAiBC,WAAjB,EAA8BC,WAA9B;EACA,IAAIxC,YAAJ,EAAkB3V,KAAlB;;EAEA,IAAImE,IAAI,CAACyG,GAAT,EAAc;IACZ5K,KAAK,GAAGmE,IAAI,CAACmE,UAAL,GAAkB,CAAlB,GAAsBnE,IAAI,CAACnE,KAAnC;EACD,CAFD,MAEO;IACLA,KAAK,GAAGmE,IAAI,CAACnE,KAAb;EACD;;EACDmY,WAAW,GAAGnY,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAImE,IAAI,CAACmE,UAAzC;;EACA,IAAInE,IAAI,CAACnD,UAAT,EAAqB;IACnB2U,YAAY,GAAGnF,IAAI,CAACgB,KAAL,CAAWrN,IAAI,CAAC7C,YAAL,GAAoB,CAA/B,CAAf;IACA4W,WAAW,GAAG,CAAClY,KAAK,GAAGmE,IAAI,CAACX,YAAd,IAA8BW,IAAI,CAACmE,UAAnC,KAAkD,CAAhE;;IACA,IACEtI,KAAK,GAAGmE,IAAI,CAACX,YAAL,GAAoBmS,YAApB,GAAmC,CAA3C,IACA3V,KAAK,IAAImE,IAAI,CAACX,YAAL,GAAoBmS,YAF/B,EAGE;MACAsC,WAAW,GAAG,IAAd;IACD;EACF,CATD,MASO;IACLA,WAAW,GACT9T,IAAI,CAACX,YAAL,IAAqBxD,KAArB,IACAA,KAAK,GAAGmE,IAAI,CAACX,YAAL,GAAoBW,IAAI,CAAC7C,YAFnC;EAGD;;EAED,IAAI8W,YAAJ;;EACA,IAAIjU,IAAI,CAAC+F,WAAL,GAAmB,CAAvB,EAA0B;IACxBkO,YAAY,GAAGjU,IAAI,CAAC+F,WAAL,GAAmB/F,IAAI,CAACmE,UAAvC;EACD,CAFD,MAEO,IAAInE,IAAI,CAAC+F,WAAL,IAAoB/F,IAAI,CAACmE,UAA7B,EAAyC;IAC9C8P,YAAY,GAAGjU,IAAI,CAAC+F,WAAL,GAAmB/F,IAAI,CAACmE,UAAvC;EACD,CAFM,MAEA;IACL8P,YAAY,GAAGjU,IAAI,CAAC+F,WAApB;EACD;;EACD,IAAImO,YAAY,GAAGrY,KAAK,KAAKoY,YAA7B;EACA,OAAO;IACL,eAAe,IADV;IAEL,gBAAgBH,WAFX;IAGL,gBAAgBC,WAHX;IAIL,gBAAgBC,WAJX;IAKL,iBAAiBE,YALZ,CAKyB;;EALzB,CAAP;AAOD,CAzCD;;AA2CA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAAnU,IAAI,EAAI;EAC5B,IAAI3B,KAAK,GAAG,EAAZ;;EAEA,IAAI2B,IAAI,CAACtC,aAAL,KAAuB2J,SAAvB,IAAoCrH,IAAI,CAACtC,aAAL,KAAuB,KAA/D,EAAsE;IACpEW,KAAK,CAACC,KAAN,GAAc0B,IAAI,CAACuE,UAAnB;EACD;;EAED,IAAIvE,IAAI,CAAC9C,IAAT,EAAe;IACbmB,KAAK,CAAC+V,QAAN,GAAiB,UAAjB;;IACA,IAAIpU,IAAI,CAACgI,QAAT,EAAmB;MACjB3J,KAAK,CAACgW,GAAN,GAAY,CAACrU,IAAI,CAACnE,KAAN,GAAcyR,QAAQ,CAACtN,IAAI,CAACwL,WAAN,CAAlC;IACD,CAFD,MAEO;MACLnN,KAAK,CAACsF,IAAN,GAAa,CAAC3D,IAAI,CAACnE,KAAN,GAAcyR,QAAQ,CAACtN,IAAI,CAACuE,UAAN,CAAnC;IACD;;IACDlG,KAAK,CAACqU,OAAN,GAAgB1S,IAAI,CAACX,YAAL,KAAsBW,IAAI,CAACnE,KAA3B,GAAmC,CAAnC,GAAuC,CAAvD;;IACA,IAAImE,IAAI,CAACwF,MAAT,EAAiB;MACfnH,KAAK,CAACsU,UAAN,GACE,aACA3S,IAAI,CAACe,KADL,GAEA,KAFA,GAGAf,IAAI,CAACmT,OAHL,GAIA,IAJA,GAKA,aALA,GAMAnT,IAAI,CAACe,KANL,GAOA,KAPA,GAQAf,IAAI,CAACmT,OATP;IAUD;EACF;;EAED,OAAO9U,KAAP;AACD,CA9BD;;AAgCA,IAAMiW,MAAM,GAAG,SAATA,MAAS,CAAC9W,KAAD,EAAQ+W,WAAR;EAAA,OAAwB/W,KAAK,CAACgB,GAAN,GAAY,GAAZ,GAAkB+V,WAA1C;AAAA,CAAf;;AAEA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAAAxU,IAAI,EAAI;EAC3B,IAAIxB,GAAJ;EACA,IAAImT,MAAM,GAAG,EAAb;EACA,IAAI8C,cAAc,GAAG,EAArB;EACA,IAAIC,eAAe,GAAG,EAAtB;EACA,IAAIpQ,aAAa,GAAGjH,4CAAK,CAACC,QAAN,CAAeoF,KAAf,CAAqB1C,IAAI,CAAC5C,QAA1B,CAApB;EACA,IAAIyP,UAAU,GAAGC,+EAAc,CAAC9M,IAAD,CAA/B;EACA,IAAI+M,QAAQ,GAAGC,6EAAY,CAAChN,IAAD,CAA3B;EAEA3C,4CAAK,CAACC,QAAN,CAAe1B,OAAf,CAAuBoE,IAAI,CAAC5C,QAA5B,EAAsC,UAAC+B,IAAD,EAAOtD,KAAP,EAAiB;IACrD,IAAI2B,KAAJ;IACA,IAAImX,mBAAmB,GAAG;MACxB/R,OAAO,EAAE,UADe;MAExB/G,KAAK,EAAEA,KAFiB;MAGxBiB,cAAc,EAAEkD,IAAI,CAAClD,cAHG;MAIxBuC,YAAY,EAAEW,IAAI,CAACX;IAJK,CAA1B,CAFqD,CASrD;;IACA,IACE,CAACW,IAAI,CAACP,QAAN,IACCO,IAAI,CAACP,QAAL,IAAiBO,IAAI,CAACH,cAAL,CAAoBsF,OAApB,CAA4BtJ,KAA5B,KAAsC,CAF1D,EAGE;MACA2B,KAAK,GAAG2B,IAAR;IACD,CALD,MAKO;MACL3B,KAAK,gBAAG,uEAAR;IACD;;IACD,IAAIoX,UAAU,GAAGT,aAAa,CAAC,4JAAKnU,IAAN;MAAYnE,KAAK,EAALA;IAAZ,GAA9B;IACA,IAAIgZ,UAAU,GAAGrX,KAAK,CAACzD,KAAN,CAAY4E,SAAZ,IAAyB,EAA1C;IACA,IAAImW,YAAY,GAAGjB,eAAe,CAAC,4JAAK7T,IAAN;MAAYnE,KAAK,EAALA;IAAZ,GAAlC,CApBqD,CAqBrD;;IACA8V,MAAM,CAACvW,IAAP,eACEiC,4CAAK,CAACkB,YAAN,CAAmBf,KAAnB,EAA0B;MACxBgB,GAAG,EAAE,aAAa8V,MAAM,CAAC9W,KAAD,EAAQ3B,KAAR,CADA;MAExB,cAAcA,KAFU;MAGxB8C,SAAS,EAAEoJ,iDAAU,CAAC+M,YAAD,EAAeD,UAAf,CAHG;MAIxBpW,QAAQ,EAAE,IAJc;MAKxB,eAAe,CAACqW,YAAY,CAAC,cAAD,CALJ;MAMxBzW,KAAK,EAAE;QAAE0W,OAAO,EAAE;MAAb,GAAyBvX,KAAK,CAACzD,KAAN,CAAYsE,KAAZ,IAAqB,EAA9C,GAAsDuW,UAAtD,CANmB;MAOxBhL,OAAO,EAAE,iBAAA1D,CAAC,EAAI;QACZ1I,KAAK,CAACzD,KAAN,IAAeyD,KAAK,CAACzD,KAAN,CAAY6P,OAA3B,IAAsCpM,KAAK,CAACzD,KAAN,CAAY6P,OAAZ,CAAoB1D,CAApB,CAAtC;;QACA,IAAIlG,IAAI,CAACgG,aAAT,EAAwB;UACtBhG,IAAI,CAACgG,aAAL,CAAmB2O,mBAAnB;QACD;MACF;IAZuB,CAA1B,CADF,EAtBqD,CAuCrD;;IACA,IAAI3U,IAAI,CAACoO,QAAL,IAAiBpO,IAAI,CAAC9C,IAAL,KAAc,KAAnC,EAA0C;MACxC,IAAI8X,UAAU,GAAG1Q,aAAa,GAAGzI,KAAjC;;MACA,IACEmZ,UAAU,IAAI9Q,6EAAY,CAAClE,IAAD,CAA1B,IACAsE,aAAa,KAAKtE,IAAI,CAAC7C,YAFzB,EAGE;QACAqB,GAAG,GAAG,CAACwW,UAAP;;QACA,IAAIxW,GAAG,IAAIqO,UAAX,EAAuB;UACrBrP,KAAK,GAAG2B,IAAR;QACD;;QACD2V,YAAY,GAAGjB,eAAe,CAAC,4JAAK7T,IAAN;UAAYnE,KAAK,EAAE2C;QAAnB,GAA9B;QACAiW,cAAc,CAACrZ,IAAf,eACEiC,4CAAK,CAACkB,YAAN,CAAmBf,KAAnB,EAA0B;UACxBgB,GAAG,EAAE,cAAc8V,MAAM,CAAC9W,KAAD,EAAQgB,GAAR,CADD;UAExB,cAAcA,GAFU;UAGxBC,QAAQ,EAAE,IAHc;UAIxBE,SAAS,EAAEoJ,iDAAU,CAAC+M,YAAD,EAAeD,UAAf,CAJG;UAKxB,eAAe,CAACC,YAAY,CAAC,cAAD,CALJ;UAMxBzW,KAAK,EAAE,4JAAMb,KAAK,CAACzD,KAAN,CAAYsE,KAAZ,IAAqB,EAA7B,GAAqCuW,UAArC,CANmB;UAOxBhL,OAAO,EAAE,iBAAA1D,CAAC,EAAI;YACZ1I,KAAK,CAACzD,KAAN,IAAeyD,KAAK,CAACzD,KAAN,CAAY6P,OAA3B,IAAsCpM,KAAK,CAACzD,KAAN,CAAY6P,OAAZ,CAAoB1D,CAApB,CAAtC;;YACA,IAAIlG,IAAI,CAACgG,aAAT,EAAwB;cACtBhG,IAAI,CAACgG,aAAL,CAAmB2O,mBAAnB;YACD;UACF;QAZuB,CAA1B,CADF;MAgBD;;MAED,IAAIrQ,aAAa,KAAKtE,IAAI,CAAC7C,YAA3B,EAAyC;QACvCqB,GAAG,GAAG8F,aAAa,GAAGzI,KAAtB;;QACA,IAAI2C,GAAG,GAAGuO,QAAV,EAAoB;UAClBvP,KAAK,GAAG2B,IAAR;QACD;;QACD2V,YAAY,GAAGjB,eAAe,CAAC,4JAAK7T,IAAN;UAAYnE,KAAK,EAAE2C;QAAnB,GAA9B;QACAkW,eAAe,CAACtZ,IAAhB,eACEiC,4CAAK,CAACkB,YAAN,CAAmBf,KAAnB,EAA0B;UACxBgB,GAAG,EAAE,eAAe8V,MAAM,CAAC9W,KAAD,EAAQgB,GAAR,CADF;UAExB,cAAcA,GAFU;UAGxBC,QAAQ,EAAE,IAHc;UAIxBE,SAAS,EAAEoJ,iDAAU,CAAC+M,YAAD,EAAeD,UAAf,CAJG;UAKxB,eAAe,CAACC,YAAY,CAAC,cAAD,CALJ;UAMxBzW,KAAK,EAAE,4JAAMb,KAAK,CAACzD,KAAN,CAAYsE,KAAZ,IAAqB,EAA7B,GAAqCuW,UAArC,CANmB;UAOxBhL,OAAO,EAAE,iBAAA1D,CAAC,EAAI;YACZ1I,KAAK,CAACzD,KAAN,IAAeyD,KAAK,CAACzD,KAAN,CAAY6P,OAA3B,IAAsCpM,KAAK,CAACzD,KAAN,CAAY6P,OAAZ,CAAoB1D,CAApB,CAAtC;;YACA,IAAIlG,IAAI,CAACgG,aAAT,EAAwB;cACtBhG,IAAI,CAACgG,aAAL,CAAmB2O,mBAAnB;YACD;UACF;QAZuB,CAA1B,CADF;MAgBD;IACF;EACF,CA7FD;;EA+FA,IAAI3U,IAAI,CAACyG,GAAT,EAAc;IACZ,OAAOgO,cAAc,CAAC3U,MAAf,CAAsB6R,MAAtB,EAA8B+C,eAA9B,EAA+CO,OAA/C,EAAP;EACD,CAFD,MAEO;IACL,OAAOR,cAAc,CAAC3U,MAAf,CAAsB6R,MAAtB,EAA8B+C,eAA9B,CAAP;EACD;AACF,CA7GD;;AA+GO,IAAMQ,KAAb;EAAA;;EAAA;;EAAA;IAAA;;IAAA;;IAAA;MAAA;IAAA;;IAAA;;IAAA,iLACS,IADT;;IAAA,sLAGc,UAAAlb,GAAG,EAAI;MACjB,MAAKoJ,IAAL,GAAYpJ,GAAZ;IACD,CALH;;IAAA;EAAA;;EAAA;IAAA;IAAA,OAOE,kBAAS;MACP,IAAM2X,MAAM,GAAG6C,YAAY,CAAC,KAAKza,KAAN,CAA3B;MACA,kBAAoD,KAAKA,KAAzD;MAAA,IAAQqO,YAAR,eAAQA,YAAR;MAAA,IAAsBI,WAAtB,eAAsBA,WAAtB;MAAA,IAAmCF,YAAnC,eAAmCA,YAAnC;MACA,IAAM6M,WAAW,GAAG;QAAE/M,YAAY,EAAZA,YAAF;QAAgBI,WAAW,EAAXA,WAAhB;QAA6BF,YAAY,EAAZA;MAA7B,CAApB;MACA,oBACE;QACE,GAAG,EAAE,KAAK8M,SADZ;QAEE,SAAS,EAAC,aAFZ;QAGE,KAAK,EAAE,KAAKrb,KAAL,CAAW6J;MAHpB,GAIMuR,WAJN,GAMGxD,MANH,CADF;IAUD;EArBH;;EAAA;AAAA,EAA2BtU,4CAAK,CAACgY,aAAjC,E;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;;;;;;AAEb;AACA;AACA;;AAEA,IAAMC,WAAW,GAAG,SAAdA,WAAc,CAAAtV,IAAI,EAAI;EAC1B,IAAI0I,IAAJ;;EAEA,IAAI1I,IAAI,CAACoO,QAAT,EAAmB;IACjB1F,IAAI,GAAG2D,IAAI,CAACkC,IAAL,CAAUvO,IAAI,CAACmE,UAAL,GAAkBnE,IAAI,CAAClD,cAAjC,CAAP;EACD,CAFD,MAEO;IACL4L,IAAI,GACF2D,IAAI,CAACkC,IAAL,CAAU,CAACvO,IAAI,CAACmE,UAAL,GAAkBnE,IAAI,CAAC7C,YAAxB,IAAwC6C,IAAI,CAAClD,cAAvD,IACA,CAFF;EAGD;;EAED,OAAO4L,IAAP;AACD,CAZD;;AAcO,IAAM6M,IAAb;EAAA;;EAAA;;EAAA;IAAA;;IAAA;EAAA;;EAAA;IAAA;IAAA,OACE,sBAAazP,OAAb,EAAsBI,CAAtB,EAAyB;MACvB;MACA;MACAA,CAAC,CAACG,cAAF;MACA,KAAKtM,KAAL,CAAW8O,YAAX,CAAwB/C,OAAxB;IACD;EANH;IAAA;IAAA,OAOE,kBAAS;MACP,kBASI,KAAK/L,KATT;MAAA,IACEqO,YADF,eACEA,YADF;MAAA,IAEEI,WAFF,eAEEA,WAFF;MAAA,IAGEF,YAHF,eAGEA,YAHF;MAAA,IAIE8F,QAJF,eAIEA,QAJF;MAAA,IAKEtR,cALF,eAKEA,cALF;MAAA,IAMEK,YANF,eAMEA,YANF;MAAA,IAOEgH,UAPF,eAOEA,UAPF;MAAA,IAQE9E,YARF,eAQEA,YARF;MAUA,IAAIoR,QAAQ,GAAG6E,WAAW,CAAC;QACzBnR,UAAU,EAAVA,UADyB;QAEzBrH,cAAc,EAAdA,cAFyB;QAGzBK,YAAY,EAAZA,YAHyB;QAIzBiR,QAAQ,EAARA;MAJyB,CAAD,CAA1B;MAOA,IAAM+G,WAAW,GAAG;QAAE/M,YAAY,EAAZA,YAAF;QAAgBI,WAAW,EAAXA,WAAhB;QAA6BF,YAAY,EAAZA;MAA7B,CAApB;MACA,IAAII,IAAI,GAAG,EAAX;;MACA,KAAK,IAAI3K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0S,QAApB,EAA8B1S,CAAC,EAA/B,EAAmC;QACjC,IAAIyX,WAAW,GAAG,CAACzX,CAAC,GAAG,CAAL,IAAUjB,cAAV,GAA2B,CAA7C;;QACA,IAAI2Y,UAAU,GAAGrH,QAAQ,GACrBoH,WADqB,GAErBvJ,qEAAK,CAACuJ,WAAD,EAAc,CAAd,EAAiBrR,UAAU,GAAG,CAA9B,CAFT;;QAGA,IAAIuR,UAAU,GAAGD,UAAU,IAAI3Y,cAAc,GAAG,CAArB,CAA3B;;QACA,IAAI6Y,SAAS,GAAGvH,QAAQ,GACpBsH,UADoB,GAEpBzJ,qEAAK,CAACyJ,UAAD,EAAa,CAAb,EAAgBvR,UAAU,GAAG,CAA7B,CAFT;QAIA,IAAIxF,SAAS,GAAGoJ,iDAAU,CAAC;UACzB,gBAAgBqG,QAAQ,GACpB/O,YAAY,IAAIsW,SAAhB,IAA6BtW,YAAY,IAAIoW,UADzB,GAEpBpW,YAAY,KAAKsW;QAHI,CAAD,CAA1B;QAMA,IAAIC,UAAU,GAAG;UACfhT,OAAO,EAAE,MADM;UAEf/G,KAAK,EAAEkC,CAFQ;UAGfjB,cAAc,EAAdA,cAHe;UAIfuC,YAAY,EAAZA;QAJe,CAAjB;QAOA,IAAIuK,OAAO,GAAG,KAAKf,YAAL,CAAkBgN,IAAlB,CAAuB,IAAvB,EAA6BD,UAA7B,CAAd;QACAlN,IAAI,GAAGA,IAAI,CAAC5I,MAAL,eACL;UAAI,GAAG,EAAE/B,CAAT;UAAY,SAAS,EAAEY;QAAvB,gBACGtB,4CAAK,CAACkB,YAAN,CAAmB,KAAKxE,KAAL,CAAW+b,YAAX,CAAwB/X,CAAxB,CAAnB,EAA+C;UAAE6L,OAAO,EAAPA;QAAF,CAA/C,CADH,CADK,CAAP;MAKD;;MAED,oBAAOvM,4CAAK,CAACkB,YAAN,CAAmB,KAAKxE,KAAL,CAAWgc,UAAX,CAAsBrN,IAAtB,CAAnB;QACL/J,SAAS,EAAE,KAAK5E,KAAL,CAAWic;MADjB,GAEFb,WAFE,EAAP;IAID;EA9DH;;EAAA;AAAA,EAA0B9X,4CAAK,CAACgY,aAAhC,E;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;;;;;;;AAEb;AACA;AACA;AAEO,IAAMY,SAAb;EAAA;;EAAA;;EAAA;IAAA;;IAAA;EAAA;;EAAA;IAAA;IAAA,OACE,sBAAanQ,OAAb,EAAsBI,CAAtB,EAAyB;MACvB,IAAIA,CAAJ,EAAO;QACLA,CAAC,CAACG,cAAF;MACD;;MACD,KAAKtM,KAAL,CAAW8O,YAAX,CAAwB/C,OAAxB,EAAiCI,CAAjC;IACD;EANH;IAAA;IAAA,OAOE,kBAAS;MACP,IAAIgQ,WAAW,GAAG;QAAE,eAAe,IAAjB;QAAuB,cAAc;MAArC,CAAlB;MACA,IAAIC,WAAW,GAAG,KAAKtN,YAAL,CAAkBgN,IAAlB,CAAuB,IAAvB,EAA6B;QAAEjT,OAAO,EAAE;MAAX,CAA7B,CAAlB;;MAEA,IACE,CAAC,KAAK7I,KAAL,CAAWqU,QAAZ,KACC,KAAKrU,KAAL,CAAWsF,YAAX,KAA4B,CAA5B,IACC,KAAKtF,KAAL,CAAWoK,UAAX,IAAyB,KAAKpK,KAAL,CAAWoD,YAFtC,CADF,EAIE;QACA+Y,WAAW,CAAC,gBAAD,CAAX,GAAgC,IAAhC;QACAC,WAAW,GAAG,IAAd;MACD;;MAED,IAAIC,cAAc,GAAG;QACnB5X,GAAG,EAAE,GADc;QAEnB,aAAa,MAFM;QAGnBG,SAAS,EAAEoJ,iDAAU,CAACmO,WAAD,CAHF;QAInB7X,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAX,CAJY;QAKnBkL,OAAO,EAAEuM;MALU,CAArB;MAOA,IAAIE,WAAW,GAAG;QAChBhX,YAAY,EAAE,KAAKtF,KAAL,CAAWsF,YADT;QAEhB8E,UAAU,EAAE,KAAKpK,KAAL,CAAWoK;MAFP,CAAlB;MAIA,IAAI6E,SAAJ;;MAEA,IAAI,KAAKjP,KAAL,CAAWiP,SAAf,EAA0B;QACxBA,SAAS,gBAAG3L,4CAAK,CAACkB,YAAN,CAAmB,KAAKxE,KAAL,CAAWiP,SAA9B,8JACPoN,cADO,GAEPC,WAFO,EAAZ;MAID,CALD,MAKO;QACLrN,SAAS,gBACP;UAAQ,GAAG,EAAC,GAAZ;UAAgB,IAAI,EAAC;QAArB,GAAkCoN,cAAlC,GACG,GADH,aADF;MAMD;;MAED,OAAOpN,SAAP;IACD;EAhDH;;EAAA;AAAA,EAA+B3L,4CAAK,CAACgY,aAArC;AAmDO,IAAMiB,SAAb;EAAA;;EAAA;;EAAA;IAAA;;IAAA;EAAA;;EAAA;IAAA;IAAA,OACE,sBAAaxQ,OAAb,EAAsBI,CAAtB,EAAyB;MACvB,IAAIA,CAAJ,EAAO;QACLA,CAAC,CAACG,cAAF;MACD;;MACD,KAAKtM,KAAL,CAAW8O,YAAX,CAAwB/C,OAAxB,EAAiCI,CAAjC;IACD;EANH;IAAA;IAAA,OAOE,kBAAS;MACP,IAAIqQ,WAAW,GAAG;QAAE,eAAe,IAAjB;QAAuB,cAAc;MAArC,CAAlB;MACA,IAAIC,WAAW,GAAG,KAAK3N,YAAL,CAAkBgN,IAAlB,CAAuB,IAAvB,EAA6B;QAAEjT,OAAO,EAAE;MAAX,CAA7B,CAAlB;;MAEA,IAAI,CAAC8E,yEAAS,CAAC,KAAK3N,KAAN,CAAd,EAA4B;QAC1Bwc,WAAW,CAAC,gBAAD,CAAX,GAAgC,IAAhC;QACAC,WAAW,GAAG,IAAd;MACD;;MAED,IAAIC,cAAc,GAAG;QACnBjY,GAAG,EAAE,GADc;QAEnB,aAAa,MAFM;QAGnBG,SAAS,EAAEoJ,iDAAU,CAACwO,WAAD,CAHF;QAInBlY,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAX,CAJY;QAKnBkL,OAAO,EAAE4M;MALU,CAArB;MAOA,IAAIH,WAAW,GAAG;QAChBhX,YAAY,EAAE,KAAKtF,KAAL,CAAWsF,YADT;QAEhB8E,UAAU,EAAE,KAAKpK,KAAL,CAAWoK;MAFP,CAAlB;MAIA,IAAI8E,SAAJ;;MAEA,IAAI,KAAKlP,KAAL,CAAWkP,SAAf,EAA0B;QACxBA,SAAS,gBAAG5L,4CAAK,CAACkB,YAAN,CAAmB,KAAKxE,KAAL,CAAWkP,SAA9B,8JACPwN,cADO,GAEPJ,WAFO,EAAZ;MAID,CALD,MAKO;QACLpN,SAAS,gBACP;UAAQ,GAAG,EAAC,GAAZ;UAAgB,IAAI,EAAC;QAArB,GAAkCwN,cAAlC,GACG,GADH,SADF;MAMD;;MAED,OAAOxN,SAAP;IACD;EA5CH;;EAAA;AAAA,EAA+B5L,4CAAK,CAACgY,aAArC,E;;;;;;;ACzDA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,aAAa;AAC5B,eAAe,EAAE;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS;AAC5B,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA,iCAAiC,YAAY;AAC7C,mDAAmD,gBAAgB;AACnE;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gCAAgC,6BAA6B,EAAE,aAAa;AAC5G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,qDAAqD,mCAAmC,EAAE;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA,6CAA6C,gBAAgB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,WAAW,UAAU;AACrB,aAAa;AACb;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA,6CAA6C,yBAAyB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAmB;AAC9B;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,kCAAkC,iEAAiE;AACnG;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,8CAA8C;AAC9C,CAAC;AACD;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,YAAY;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA,eAAe,yBAAyB;AACxC;AACA,eAAe,eAAe;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAEc,oEAAK,EAAC;;;;;;;;AC/5BrB,mBAAmB,mBAAO,CAAC,EAA6B;;AAExD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA,yB;;;;;;AClDA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA,8B;;;;;;;ACRA;AAAA;AAAA;AAAA;AAEA,IAAIzY,YAAY,GAAG;EACjB4J,aAAa,EAAE,IADE;EAEjBtH,cAAc,EAAE,KAFC;EAGjBoG,WAAW,EAAE,IAHI;EAIjByQ,UAAU,EAAE,oBAAArN,IAAI;IAAA,oBAAI;MAAI,KAAK,EAAE;QAAEhK,OAAO,EAAE;MAAX;IAAX,GAAkCgK,IAAlC,CAAJ;EAAA,CAJC;EAKjBS,MAAM,EAAE,IALS;EAMjB9I,QAAQ,EAAE,KANO;EAOjBwC,aAAa,EAAE,IAPE;EAQjBwC,YAAY,EAAE,IARG;EASjBxI,UAAU,EAAE,KATK;EAUjB2M,aAAa,EAAE,MAVE;EAWjB7K,SAAS,EAAE,EAXM;EAYjBwU,OAAO,EAAE,MAZQ;EAajB2C,YAAY,EAAE,sBAAA/X,CAAC;IAAA,oBAAI,2EAASA,CAAC,GAAG,CAAb,CAAJ;EAAA,CAbE;EAcjB2K,IAAI,EAAE,KAdW;EAejBsN,SAAS,EAAE,YAfM;EAgBjB/O,SAAS,EAAE,IAhBM;EAiBjByP,MAAM,EAAE,QAjBS;EAkBjB1G,YAAY,EAAE,IAlBG;EAmBjB9S,IAAI,EAAE,KAnBW;EAoBjB8I,aAAa,EAAE,KApBE;EAqBjBoI,QAAQ,EAAE,IArBO;EAsBjBzD,YAAY,EAAE,CAtBG;EAuBjBlL,QAAQ,EAAE,IAvBO;EAwBjBwJ,SAAS,EAAE,IAxBM;EAyBjBgH,MAAM,EAAE,IAzBS;EA0BjBzQ,MAAM,EAAE,IA1BS;EA2BjB0F,eAAe,EAAE,IA3BA;EA4BjB3C,QAAQ,EAAE,IA5BO;EA6BjBqG,gBAAgB,EAAE,KA7BD;EA8BjBrH,YAAY,EAAE,KA9BG;EA+BjB4G,YAAY,EAAE,IA/BG;EAgCjBa,SAAS,EAAE,IAhCM;EAiCjB3N,UAAU,EAAE,IAjCK;EAkCjBsC,IAAI,EAAE,CAlCW;EAmCjB8I,GAAG,EAAE,KAnCY;EAoCjBrM,KAAK,EAAE,KApCU;EAqCjBwD,YAAY,EAAE,CArCG;EAsCjBd,cAAc,EAAE,CAtCC;EAuCjBK,YAAY,EAAE,CAvCG;EAwCjB4D,KAAK,EAAE,GAxCU;EAyCjBiG,KAAK,EAAE,IAzCU;EA0CjBkJ,UAAU,EAAE,IA1CK;EA2CjBH,YAAY,EAAE,KA3CG;EA4CjBrG,SAAS,EAAE,IA5CM;EA6CjBkH,cAAc,EAAE,CA7CC;EA8CjBpL,MAAM,EAAE,IA9CS;EA+CjBqN,YAAY,EAAE,IA/CG;EAgDjBnV,aAAa,EAAE,KAhDE;EAiDjBsK,QAAQ,EAAE,KAjDO;EAkDjBrC,cAAc,EAAE;AAlDC,CAAnB;AAqDe/I,2EAAf,E", "file": "react-slick.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Slider\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"Slider\"] = factory(root[\"React\"]);\n})(window, function(__WEBPACK_EXTERNAL_MODULE__15__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import Slider from \"./slider\";\n\nexport default Slider;\n", "\"use strict\";\n\nimport React from \"react\";\nimport { InnerSlider } from \"./inner-slider\";\nimport json2mq from \"json2mq\";\nimport defaultProps from \"./default-props\";\nimport { canUseDOM } from \"./utils/innerSliderUtils\";\n\nexport default class Slider extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      breakpoint: null\n    };\n    this._responsiveMediaHandlers = [];\n  }\n\n  innerSliderRefHandler = ref => (this.innerSlider = ref);\n\n  media(query, handler) {\n    // javascript handler for  css media query\n    const mql = window.matchMedia(query);\n    const listener = ({ matches }) => {\n      if (matches) {\n        handler();\n      }\n    };\n    mql.addListener(listener);\n    listener(mql);\n    this._responsiveMediaHandlers.push({ mql, query, listener });\n  }\n\n  // handles responsive breakpoints\n  componentDidMount() {\n    // performance monitoring\n    //if (process.env.NODE_ENV !== 'production') {\n    //const { whyDidYouUpdate } = require('why-did-you-update')\n    //whyDidYouUpdate(React)\n    //}\n    if (this.props.responsive) {\n      let breakpoints = this.props.responsive.map(\n        breakpt => breakpt.breakpoint\n      );\n      // sort them in increasing order of their numerical value\n      breakpoints.sort((x, y) => x - y);\n\n      breakpoints.forEach((breakpoint, index) => {\n        // media query for each breakpoint\n        let bQuery;\n        if (index === 0) {\n          bQuery = json2mq({ minWidth: 0, maxWidth: breakpoint });\n        } else {\n          bQuery = json2mq({\n            minWidth: breakpoints[index - 1] + 1,\n            maxWidth: breakpoint\n          });\n        }\n        // when not using server side rendering\n        canUseDOM() &&\n          this.media(bQuery, () => {\n            this.setState({ breakpoint: breakpoint });\n          });\n      });\n\n      // Register media query for full screen. Need to support resize from small to large\n      // convert javascript object to media query string\n      let query = json2mq({ minWidth: breakpoints.slice(-1)[0] });\n\n      canUseDOM() &&\n        this.media(query, () => {\n          this.setState({ breakpoint: null });\n        });\n    }\n  }\n\n  componentWillUnmount() {\n    this._responsiveMediaHandlers.forEach(function(obj) {\n      obj.mql.removeListener(obj.listener);\n    });\n  }\n\n  slickPrev = () => this.innerSlider.slickPrev();\n\n  slickNext = () => this.innerSlider.slickNext();\n\n  slickGoTo = (slide, dontAnimate = false) =>\n    this.innerSlider.slickGoTo(slide, dontAnimate);\n\n  slickPause = () => this.innerSlider.pause(\"paused\");\n\n  slickPlay = () => this.innerSlider.autoPlay(\"play\");\n\n  render() {\n    var settings;\n    var newProps;\n    if (this.state.breakpoint) {\n      newProps = this.props.responsive.filter(\n        resp => resp.breakpoint === this.state.breakpoint\n      );\n      settings =\n        newProps[0].settings === \"unslick\"\n          ? \"unslick\"\n          : { ...defaultProps, ...this.props, ...newProps[0].settings };\n    } else {\n      settings = { ...defaultProps, ...this.props };\n    }\n\n    // force scrolling by one if centerMode is on\n    if (settings.centerMode) {\n      if (\n        settings.slidesToScroll > 1 &&\n        process.env.NODE_ENV !== \"production\"\n      ) {\n        console.warn(\n          `slidesToScroll should be equal to 1 in centerMode, you are using ${settings.slidesToScroll}`\n        );\n      }\n      settings.slidesToScroll = 1;\n    }\n    // force showing one slide and scrolling by one if the fade mode is on\n    if (settings.fade) {\n      if (settings.slidesToShow > 1 && process.env.NODE_ENV !== \"production\") {\n        console.warn(\n          `slidesToShow should be equal to 1 when fade is true, you're using ${settings.slidesToShow}`\n        );\n      }\n      if (\n        settings.slidesToScroll > 1 &&\n        process.env.NODE_ENV !== \"production\"\n      ) {\n        console.warn(\n          `slidesToScroll should be equal to 1 when fade is true, you're using ${settings.slidesToScroll}`\n        );\n      }\n      settings.slidesToShow = 1;\n      settings.slidesToScroll = 1;\n    }\n\n    // makes sure that children is an array, even when there is only 1 child\n    let children = React.Children.toArray(this.props.children);\n\n    // Children may contain false or null, so we should filter them\n    // children may also contain string filled with spaces (in certain cases where we use jsx strings)\n    children = children.filter(child => {\n      if (typeof child === \"string\") {\n        return !!child.trim();\n      }\n      return !!child;\n    });\n\n    // rows and slidesPerRow logic is handled here\n    if (\n      settings.variableWidth &&\n      (settings.rows > 1 || settings.slidesPerRow > 1)\n    ) {\n      console.warn(\n        `variableWidth is not supported in case of rows > 1 or slidesPerRow > 1`\n      );\n      settings.variableWidth = false;\n    }\n    let newChildren = [];\n    let currentWidth = null;\n    for (\n      let i = 0;\n      i < children.length;\n      i += settings.rows * settings.slidesPerRow\n    ) {\n      let newSlide = [];\n      for (\n        let j = i;\n        j < i + settings.rows * settings.slidesPerRow;\n        j += settings.slidesPerRow\n      ) {\n        let row = [];\n        for (let k = j; k < j + settings.slidesPerRow; k += 1) {\n          if (settings.variableWidth && children[k].props.style) {\n            currentWidth = children[k].props.style.width;\n          }\n          if (k >= children.length) break;\n          row.push(\n            React.cloneElement(children[k], {\n              key: 100 * i + 10 * j + k,\n              tabIndex: -1,\n              style: {\n                width: `${100 / settings.slidesPerRow}%`,\n                display: \"inline-block\"\n              }\n            })\n          );\n        }\n        newSlide.push(<div key={10 * i + j}>{row}</div>);\n      }\n      if (settings.variableWidth) {\n        newChildren.push(\n          <div key={i} style={{ width: currentWidth }}>\n            {newSlide}\n          </div>\n        );\n      } else {\n        newChildren.push(<div key={i}>{newSlide}</div>);\n      }\n    }\n\n    if (settings === \"unslick\") {\n      const className = \"regular slider \" + (this.props.className || \"\");\n      return <div className={className}>{children}</div>;\n    } else if (newChildren.length <= settings.slidesToShow) {\n      settings.unslick = true;\n    }\n    return (\n      <InnerSlider\n        style={this.props.style}\n        ref={this.innerSliderRefHandler}\n        {...settings}\n      >\n        {newChildren}\n      </InnerSlider>\n    );\n  }\n}\n", "function _extends() {\n  module.exports = _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _extends.apply(this, arguments);\n}\n\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var setPrototypeOf = require(\"./setPrototypeOf.js\");\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n\nmodule.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\n\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var getPrototypeOf = require(\"./getPrototypeOf.js\");\n\nvar isNativeReflectConstruct = require(\"./isNativeReflectConstruct.js\");\n\nvar possibleConstructorReturn = require(\"./possibleConstructorReturn.js\");\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return possibleConstructorReturn(this, result);\n  };\n}\n\nmodule.exports = _createSuper, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _getPrototypeOf(o);\n}\n\nmodule.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\n\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return assertThisInitialized(self);\n}\n\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(obj);\n}\n\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "module.exports = __WEBPACK_EXTERNAL_MODULE__15__;", "\"use strict\";\n\nimport React from \"react\";\nimport initialState from \"./initial-state\";\nimport debounce from \"lodash/debounce\";\nimport classnames from \"classnames\";\nimport {\n  getOnDemandLazySlides,\n  extractObject,\n  initializedState,\n  getHeight,\n  canGoNext,\n  slideHandler,\n  changeSlide,\n  keyHandler,\n  swipeStart,\n  swipeMove,\n  swipeEnd,\n  getPreClones,\n  getPostClones,\n  getTrackLeft,\n  getTrackCSS,\n} from \"./utils/innerSliderUtils\";\n\nimport { Track } from \"./track\";\nimport { Dots } from \"./dots\";\nimport { PrevArrow, NextArrow } from \"./arrows\";\nimport ResizeObserver from \"resize-observer-polyfill\";\n\nexport class InnerSlider extends React.Component {\n  constructor(props) {\n    super(props);\n    this.list = null;\n    this.track = null;\n    this.state = {\n      ...initialState,\n      currentSlide: this.props.initialSlide,\n      slideCount: React.Children.count(this.props.children),\n    };\n    this.callbackTimers = [];\n    this.clickable = true;\n    this.debouncedResize = null;\n    const ssrState = this.ssrInit();\n    this.state = { ...this.state, ...ssrState };\n  }\n  listRefHandler = (ref) => (this.list = ref);\n  trackRefHandler = (ref) => (this.track = ref);\n  adaptHeight = () => {\n    if (this.props.adaptiveHeight && this.list) {\n      const elem = this.list.querySelector(\n        `[data-index=\"${this.state.currentSlide}\"]`\n      );\n      this.list.style.height = getHeight(elem) + \"px\";\n    }\n  };\n  componentDidMount = () => {\n    this.props.onInit && this.props.onInit();\n    if (this.props.lazyLoad) {\n      let slidesToLoad = getOnDemandLazySlides({\n        ...this.props,\n        ...this.state,\n      });\n      if (slidesToLoad.length > 0) {\n        this.setState((prevState) => ({\n          lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad),\n        }));\n        if (this.props.onLazyLoad) {\n          this.props.onLazyLoad(slidesToLoad);\n        }\n      }\n    }\n    let spec = { listRef: this.list, trackRef: this.track, ...this.props };\n    this.updateState(spec, true, () => {\n      this.adaptHeight();\n      this.props.autoplay && this.autoPlay(\"playing\");\n    });\n    if (this.props.lazyLoad === \"progressive\") {\n      this.lazyLoadTimer = setInterval(this.progressiveLazyLoad, 1000);\n    }\n    this.ro = new ResizeObserver(() => {\n      if (this.state.animating) {\n        this.onWindowResized(false); // don't set trackStyle hence don't break animation\n        this.callbackTimers.push(\n          setTimeout(() => this.onWindowResized(), this.props.speed)\n        );\n      } else {\n        this.onWindowResized();\n      }\n    });\n    this.ro.observe(this.list);\n    document.querySelectorAll &&\n      Array.prototype.forEach.call(\n        document.querySelectorAll(\".slick-slide\"),\n        (slide) => {\n          slide.onfocus = this.props.pauseOnFocus ? this.onSlideFocus : null;\n          slide.onblur = this.props.pauseOnFocus ? this.onSlideBlur : null;\n        }\n      );\n    if (window.addEventListener) {\n      window.addEventListener(\"resize\", this.onWindowResized);\n    } else {\n      window.attachEvent(\"onresize\", this.onWindowResized);\n    }\n  };\n  componentWillUnmount = () => {\n    if (this.animationEndCallback) {\n      clearTimeout(this.animationEndCallback);\n    }\n    if (this.lazyLoadTimer) {\n      clearInterval(this.lazyLoadTimer);\n    }\n    if (this.callbackTimers.length) {\n      this.callbackTimers.forEach((timer) => clearTimeout(timer));\n      this.callbackTimers = [];\n    }\n    if (window.addEventListener) {\n      window.removeEventListener(\"resize\", this.onWindowResized);\n    } else {\n      window.detachEvent(\"onresize\", this.onWindowResized);\n    }\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n    }\n    this.ro.disconnect();\n  };\n\n  didPropsChange(prevProps) {\n    let setTrackStyle = false;\n    for (let key of Object.keys(this.props)) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (!prevProps.hasOwnProperty(key)) {\n        setTrackStyle = true;\n        break;\n      }\n      if (\n        typeof prevProps[key] === \"object\" ||\n        typeof prevProps[key] === \"function\"\n      ) {\n        continue;\n      }\n      if (prevProps[key] !== this.props[key]) {\n        setTrackStyle = true;\n        break;\n      }\n    }\n    return (\n      setTrackStyle ||\n      React.Children.count(this.props.children) !==\n        React.Children.count(prevProps.children)\n    );\n  }\n\n  componentDidUpdate = (prevProps) => {\n    this.checkImagesLoad();\n    this.props.onReInit && this.props.onReInit();\n    if (this.props.lazyLoad) {\n      let slidesToLoad = getOnDemandLazySlides({\n        ...this.props,\n        ...this.state,\n      });\n      if (slidesToLoad.length > 0) {\n        this.setState((prevState) => ({\n          lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad),\n        }));\n        if (this.props.onLazyLoad) {\n          this.props.onLazyLoad(slidesToLoad);\n        }\n      }\n    }\n    // if (this.props.onLazyLoad) {\n    //   this.props.onLazyLoad([leftMostSlide])\n    // }\n    this.adaptHeight();\n    let spec = {\n      listRef: this.list,\n      trackRef: this.track,\n      ...this.props,\n      ...this.state,\n    };\n    const setTrackStyle = this.didPropsChange(prevProps);\n    setTrackStyle &&\n      this.updateState(spec, setTrackStyle, () => {\n        if (\n          this.state.currentSlide >= React.Children.count(this.props.children)\n        ) {\n          this.changeSlide({\n            message: \"index\",\n            index:\n              React.Children.count(this.props.children) -\n              this.props.slidesToShow,\n            currentSlide: this.state.currentSlide,\n          });\n        }\n        if (\n          prevProps.autoplay !== this.props.autoplay ||\n          prevProps.autoplaySpeed !== this.props.autoplaySpeed\n        ) {\n          if (!prevProps.autoplay && this.props.autoplay) {\n            this.autoPlay(\"playing\");\n          } else if (this.props.autoplay) {\n            this.autoPlay(\"update\");\n          } else {\n            this.pause(\"paused\");\n          }\n        }\n      });\n  };\n  onWindowResized = (setTrackStyle) => {\n    if (this.debouncedResize) this.debouncedResize.cancel();\n    this.debouncedResize = debounce(() => this.resizeWindow(setTrackStyle), 50);\n    this.debouncedResize();\n  };\n  resizeWindow = (setTrackStyle = true) => {\n    const isTrackMounted = Boolean(this.track && this.track.node);\n    // prevent warning: setting state on unmounted component (server side rendering)\n    if (!isTrackMounted) return;\n    let spec = {\n      listRef: this.list,\n      trackRef: this.track,\n      ...this.props,\n      ...this.state,\n    };\n    this.updateState(spec, setTrackStyle, () => {\n      if (this.props.autoplay) this.autoPlay(\"update\");\n      else this.pause(\"paused\");\n    });\n    // animating state should be cleared while resizing, otherwise autoplay stops working\n    this.setState({\n      animating: false,\n    });\n    clearTimeout(this.animationEndCallback);\n    delete this.animationEndCallback;\n  };\n  updateState = (spec, setTrackStyle, callback) => {\n    let updatedState = initializedState(spec);\n    spec = { ...spec, ...updatedState, slideIndex: updatedState.currentSlide };\n    let targetLeft = getTrackLeft(spec);\n    spec = { ...spec, left: targetLeft };\n    let trackStyle = getTrackCSS(spec);\n    if (\n      setTrackStyle ||\n      React.Children.count(this.props.children) !==\n        React.Children.count(spec.children)\n    ) {\n      updatedState[\"trackStyle\"] = trackStyle;\n    }\n    this.setState(updatedState, callback);\n  };\n\n  ssrInit = () => {\n    if (this.props.variableWidth) {\n      let trackWidth = 0,\n        trackLeft = 0;\n      let childrenWidths = [];\n      let preClones = getPreClones({\n        ...this.props,\n        ...this.state,\n        slideCount: this.props.children.length,\n      });\n      let postClones = getPostClones({\n        ...this.props,\n        ...this.state,\n        slideCount: this.props.children.length,\n      });\n      this.props.children.forEach((child) => {\n        childrenWidths.push(child.props.style.width);\n        trackWidth += child.props.style.width;\n      });\n      for (let i = 0; i < preClones; i++) {\n        trackLeft += childrenWidths[childrenWidths.length - 1 - i];\n        trackWidth += childrenWidths[childrenWidths.length - 1 - i];\n      }\n      for (let i = 0; i < postClones; i++) {\n        trackWidth += childrenWidths[i];\n      }\n      for (let i = 0; i < this.state.currentSlide; i++) {\n        trackLeft += childrenWidths[i];\n      }\n      let trackStyle = {\n        width: trackWidth + \"px\",\n        left: -trackLeft + \"px\",\n      };\n      if (this.props.centerMode) {\n        let currentWidth = `${childrenWidths[this.state.currentSlide]}px`;\n        trackStyle.left = `calc(${trackStyle.left} + (100% - ${currentWidth}) / 2 ) `;\n      }\n      return {\n        trackStyle,\n      };\n    }\n    let childrenCount = React.Children.count(this.props.children);\n    const spec = { ...this.props, ...this.state, slideCount: childrenCount };\n    let slideCount = getPreClones(spec) + getPostClones(spec) + childrenCount;\n    let trackWidth = (100 / this.props.slidesToShow) * slideCount;\n    let slideWidth = 100 / slideCount;\n    let trackLeft =\n      (-slideWidth *\n        (getPreClones(spec) + this.state.currentSlide) *\n        trackWidth) /\n      100;\n    if (this.props.centerMode) {\n      trackLeft += (100 - (slideWidth * trackWidth) / 100) / 2;\n    }\n    let trackStyle = {\n      width: trackWidth + \"%\",\n      left: trackLeft + \"%\",\n    };\n    return {\n      slideWidth: slideWidth + \"%\",\n      trackStyle: trackStyle,\n    };\n  };\n  checkImagesLoad = () => {\n    let images =\n      (this.list &&\n        this.list.querySelectorAll &&\n        this.list.querySelectorAll(\".slick-slide img\")) ||\n      [];\n    let imagesCount = images.length,\n      loadedCount = 0;\n    Array.prototype.forEach.call(images, (image) => {\n      const handler = () =>\n        ++loadedCount && loadedCount >= imagesCount && this.onWindowResized();\n      if (!image.onclick) {\n        image.onclick = () => image.parentNode.focus();\n      } else {\n        const prevClickHandler = image.onclick;\n        image.onclick = () => {\n          prevClickHandler();\n          image.parentNode.focus();\n        };\n      }\n      if (!image.onload) {\n        if (this.props.lazyLoad) {\n          image.onload = () => {\n            this.adaptHeight();\n            this.callbackTimers.push(\n              setTimeout(this.onWindowResized, this.props.speed)\n            );\n          };\n        } else {\n          image.onload = handler;\n          image.onerror = () => {\n            handler();\n            this.props.onLazyLoadError && this.props.onLazyLoadError();\n          };\n        }\n      }\n    });\n  };\n  progressiveLazyLoad = () => {\n    let slidesToLoad = [];\n    const spec = { ...this.props, ...this.state };\n    for (\n      let index = this.state.currentSlide;\n      index < this.state.slideCount + getPostClones(spec);\n      index++\n    ) {\n      if (this.state.lazyLoadedList.indexOf(index) < 0) {\n        slidesToLoad.push(index);\n        break;\n      }\n    }\n    for (\n      let index = this.state.currentSlide - 1;\n      index >= -getPreClones(spec);\n      index--\n    ) {\n      if (this.state.lazyLoadedList.indexOf(index) < 0) {\n        slidesToLoad.push(index);\n        break;\n      }\n    }\n    if (slidesToLoad.length > 0) {\n      this.setState((state) => ({\n        lazyLoadedList: state.lazyLoadedList.concat(slidesToLoad),\n      }));\n      if (this.props.onLazyLoad) {\n        this.props.onLazyLoad(slidesToLoad);\n      }\n    } else {\n      if (this.lazyLoadTimer) {\n        clearInterval(this.lazyLoadTimer);\n        delete this.lazyLoadTimer;\n      }\n    }\n  };\n  slideHandler = (index, dontAnimate = false) => {\n    const { asNavFor, beforeChange, onLazyLoad, speed, afterChange } =\n      this.props;\n    // capture currentslide before state is updated\n    const currentSlide = this.state.currentSlide;\n    let { state, nextState } = slideHandler({\n      index,\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      useCSS: this.props.useCSS && !dontAnimate,\n    });\n    if (!state) return;\n    beforeChange && beforeChange(currentSlide, state.currentSlide);\n    let slidesToLoad = state.lazyLoadedList.filter(\n      (value) => this.state.lazyLoadedList.indexOf(value) < 0\n    );\n    onLazyLoad && slidesToLoad.length > 0 && onLazyLoad(slidesToLoad);\n    if (!this.props.waitForAnimate && this.animationEndCallback) {\n      clearTimeout(this.animationEndCallback);\n      afterChange && afterChange(currentSlide);\n      delete this.animationEndCallback;\n    }\n    this.setState(state, () => {\n      // asNavForIndex check is to avoid recursive calls of slideHandler in waitForAnimate=false mode\n      if (asNavFor && this.asNavForIndex !== index) {\n        this.asNavForIndex = index;\n        asNavFor.innerSlider.slideHandler(index);\n      }\n      if (!nextState) return;\n      this.animationEndCallback = setTimeout(() => {\n        const { animating, ...firstBatch } = nextState;\n        this.setState(firstBatch, () => {\n          this.callbackTimers.push(\n            setTimeout(() => this.setState({ animating }), 10)\n          );\n          afterChange && afterChange(state.currentSlide);\n          delete this.animationEndCallback;\n        });\n      }, speed);\n    });\n  };\n  changeSlide = (options, dontAnimate = false) => {\n    const spec = { ...this.props, ...this.state };\n    let targetSlide = changeSlide(spec, options);\n    if (targetSlide !== 0 && !targetSlide) return;\n    if (dontAnimate === true) {\n      this.slideHandler(targetSlide, dontAnimate);\n    } else {\n      this.slideHandler(targetSlide);\n    }\n    this.props.autoplay && this.autoPlay(\"update\");\n    if (this.props.focusOnSelect) {\n      const nodes = this.list.querySelectorAll(\".slick-current\");\n      nodes[0] && nodes[0].focus();\n    }\n  };\n  clickHandler = (e) => {\n    if (this.clickable === false) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    this.clickable = true;\n  };\n  keyHandler = (e) => {\n    let dir = keyHandler(e, this.props.accessibility, this.props.rtl);\n    dir !== \"\" && this.changeSlide({ message: dir });\n  };\n  selectHandler = (options) => {\n    this.changeSlide(options);\n  };\n  disableBodyScroll = () => {\n    const preventDefault = (e) => {\n      e = e || window.event;\n      if (e.preventDefault) e.preventDefault();\n      e.returnValue = false;\n    };\n    window.ontouchmove = preventDefault;\n  };\n  enableBodyScroll = () => {\n    window.ontouchmove = null;\n  };\n  swipeStart = (e) => {\n    if (this.props.verticalSwiping) {\n      this.disableBodyScroll();\n    }\n    let state = swipeStart(e, this.props.swipe, this.props.draggable);\n    state !== \"\" && this.setState(state);\n  };\n  swipeMove = (e) => {\n    let state = swipeMove(e, {\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      listRef: this.list,\n      slideIndex: this.state.currentSlide,\n    });\n    if (!state) return;\n    if (state[\"swiping\"]) {\n      this.clickable = false;\n    }\n    this.setState(state);\n  };\n  swipeEnd = (e) => {\n    let state = swipeEnd(e, {\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      listRef: this.list,\n      slideIndex: this.state.currentSlide,\n    });\n    if (!state) return;\n    let triggerSlideHandler = state[\"triggerSlideHandler\"];\n    delete state[\"triggerSlideHandler\"];\n    this.setState(state);\n    if (triggerSlideHandler === undefined) return;\n    this.slideHandler(triggerSlideHandler);\n    if (this.props.verticalSwiping) {\n      this.enableBodyScroll();\n    }\n  };\n  touchEnd = (e) => {\n    this.swipeEnd(e);\n    this.clickable = true;\n  };\n  slickPrev = () => {\n    // this and fellow methods are wrapped in setTimeout\n    // to make sure initialize setState has happened before\n    // any of such methods are called\n    this.callbackTimers.push(\n      setTimeout(() => this.changeSlide({ message: \"previous\" }), 0)\n    );\n  };\n  slickNext = () => {\n    this.callbackTimers.push(\n      setTimeout(() => this.changeSlide({ message: \"next\" }), 0)\n    );\n  };\n  slickGoTo = (slide, dontAnimate = false) => {\n    slide = Number(slide);\n    if (isNaN(slide)) return \"\";\n    this.callbackTimers.push(\n      setTimeout(\n        () =>\n          this.changeSlide(\n            {\n              message: \"index\",\n              index: slide,\n              currentSlide: this.state.currentSlide,\n            },\n            dontAnimate\n          ),\n        0\n      )\n    );\n  };\n  play = () => {\n    var nextIndex;\n    if (this.props.rtl) {\n      nextIndex = this.state.currentSlide - this.props.slidesToScroll;\n    } else {\n      if (canGoNext({ ...this.props, ...this.state })) {\n        nextIndex = this.state.currentSlide + this.props.slidesToScroll;\n      } else {\n        return false;\n      }\n    }\n\n    this.slideHandler(nextIndex);\n  };\n\n  autoPlay = (playType) => {\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n    }\n    const autoplaying = this.state.autoplaying;\n    if (playType === \"update\") {\n      if (\n        autoplaying === \"hovered\" ||\n        autoplaying === \"focused\" ||\n        autoplaying === \"paused\"\n      ) {\n        return;\n      }\n    } else if (playType === \"leave\") {\n      if (autoplaying === \"paused\" || autoplaying === \"focused\") {\n        return;\n      }\n    } else if (playType === \"blur\") {\n      if (autoplaying === \"paused\" || autoplaying === \"hovered\") {\n        return;\n      }\n    }\n    this.autoplayTimer = setInterval(this.play, this.props.autoplaySpeed + 50);\n    this.setState({ autoplaying: \"playing\" });\n  };\n  pause = (pauseType) => {\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n      this.autoplayTimer = null;\n    }\n    const autoplaying = this.state.autoplaying;\n    if (pauseType === \"paused\") {\n      this.setState({ autoplaying: \"paused\" });\n    } else if (pauseType === \"focused\") {\n      if (autoplaying === \"hovered\" || autoplaying === \"playing\") {\n        this.setState({ autoplaying: \"focused\" });\n      }\n    } else {\n      // pauseType  is 'hovered'\n      if (autoplaying === \"playing\") {\n        this.setState({ autoplaying: \"hovered\" });\n      }\n    }\n  };\n  onDotsOver = () => this.props.autoplay && this.pause(\"hovered\");\n  onDotsLeave = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"hovered\" &&\n    this.autoPlay(\"leave\");\n  onTrackOver = () => this.props.autoplay && this.pause(\"hovered\");\n  onTrackLeave = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"hovered\" &&\n    this.autoPlay(\"leave\");\n  onSlideFocus = () => this.props.autoplay && this.pause(\"focused\");\n  onSlideBlur = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"focused\" &&\n    this.autoPlay(\"blur\");\n\n  render = () => {\n    var className = classnames(\"slick-slider\", this.props.className, {\n      \"slick-vertical\": this.props.vertical,\n      \"slick-initialized\": true,\n    });\n    let spec = { ...this.props, ...this.state };\n    let trackProps = extractObject(spec, [\n      \"fade\",\n      \"cssEase\",\n      \"speed\",\n      \"infinite\",\n      \"centerMode\",\n      \"focusOnSelect\",\n      \"currentSlide\",\n      \"lazyLoad\",\n      \"lazyLoadedList\",\n      \"rtl\",\n      \"slideWidth\",\n      \"slideHeight\",\n      \"listHeight\",\n      \"vertical\",\n      \"slidesToShow\",\n      \"slidesToScroll\",\n      \"slideCount\",\n      \"trackStyle\",\n      \"variableWidth\",\n      \"unslick\",\n      \"centerPadding\",\n      \"targetSlide\",\n      \"useCSS\",\n    ]);\n    const { pauseOnHover } = this.props;\n    trackProps = {\n      ...trackProps,\n      onMouseEnter: pauseOnHover ? this.onTrackOver : null,\n      onMouseLeave: pauseOnHover ? this.onTrackLeave : null,\n      onMouseOver: pauseOnHover ? this.onTrackOver : null,\n      focusOnSelect:\n        this.props.focusOnSelect && this.clickable ? this.selectHandler : null,\n    };\n\n    var dots;\n    if (\n      this.props.dots === true &&\n      this.state.slideCount >= this.props.slidesToShow\n    ) {\n      let dotProps = extractObject(spec, [\n        \"dotsClass\",\n        \"slideCount\",\n        \"slidesToShow\",\n        \"currentSlide\",\n        \"slidesToScroll\",\n        \"clickHandler\",\n        \"children\",\n        \"customPaging\",\n        \"infinite\",\n        \"appendDots\",\n      ]);\n      const { pauseOnDotsHover } = this.props;\n      dotProps = {\n        ...dotProps,\n        clickHandler: this.changeSlide,\n        onMouseEnter: pauseOnDotsHover ? this.onDotsLeave : null,\n        onMouseOver: pauseOnDotsHover ? this.onDotsOver : null,\n        onMouseLeave: pauseOnDotsHover ? this.onDotsLeave : null,\n      };\n      dots = <Dots {...dotProps} />;\n    }\n\n    var prevArrow, nextArrow;\n    let arrowProps = extractObject(spec, [\n      \"infinite\",\n      \"centerMode\",\n      \"currentSlide\",\n      \"slideCount\",\n      \"slidesToShow\",\n      \"prevArrow\",\n      \"nextArrow\",\n    ]);\n    arrowProps.clickHandler = this.changeSlide;\n\n    if (this.props.arrows) {\n      prevArrow = <PrevArrow {...arrowProps} />;\n      nextArrow = <NextArrow {...arrowProps} />;\n    }\n\n    var verticalHeightStyle = null;\n\n    if (this.props.vertical) {\n      verticalHeightStyle = {\n        height: this.state.listHeight,\n      };\n    }\n\n    var centerPaddingStyle = null;\n\n    if (this.props.vertical === false) {\n      if (this.props.centerMode === true) {\n        centerPaddingStyle = {\n          padding: \"0px \" + this.props.centerPadding,\n        };\n      }\n    } else {\n      if (this.props.centerMode === true) {\n        centerPaddingStyle = {\n          padding: this.props.centerPadding + \" 0px\",\n        };\n      }\n    }\n\n    const listStyle = { ...verticalHeightStyle, ...centerPaddingStyle };\n    const touchMove = this.props.touchMove;\n    let listProps = {\n      className: \"slick-list\",\n      style: listStyle,\n      onClick: this.clickHandler,\n      onMouseDown: touchMove ? this.swipeStart : null,\n      onMouseMove: this.state.dragging && touchMove ? this.swipeMove : null,\n      onMouseUp: touchMove ? this.swipeEnd : null,\n      onMouseLeave: this.state.dragging && touchMove ? this.swipeEnd : null,\n      onTouchStart: touchMove ? this.swipeStart : null,\n      onTouchMove: this.state.dragging && touchMove ? this.swipeMove : null,\n      onTouchEnd: touchMove ? this.touchEnd : null,\n      onTouchCancel: this.state.dragging && touchMove ? this.swipeEnd : null,\n      onKeyDown: this.props.accessibility ? this.keyHandler : null,\n    };\n\n    let innerSliderProps = {\n      className: className,\n      dir: \"ltr\",\n      style: this.props.style,\n    };\n\n    if (this.props.unslick) {\n      listProps = { className: \"slick-list\" };\n      innerSliderProps = { className };\n    }\n    return (\n      <div {...innerSliderProps}>\n        {!this.props.unslick ? prevArrow : \"\"}\n        <div ref={this.listRefHandler} {...listProps}>\n          <Track ref={this.trackRefHandler} {...trackProps}>\n            {this.props.children}\n          </Track>\n        </div>\n        {!this.props.unslick ? nextArrow : \"\"}\n        {!this.props.unslick ? dots : \"\"}\n      </div>\n    );\n  };\n}\n", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "const initialState = {\n  animating: false,\n  autoplaying: null,\n  currentDirection: 0,\n  currentLeft: null,\n  currentSlide: 0,\n  direction: 1,\n  dragging: false,\n  edgeDragged: false,\n  initialized: false,\n  lazyLoadedList: [],\n  listHeight: null,\n  listWidth: null,\n  scrolling: false,\n  slideCount: null,\n  slideHeight: null,\n  slideWidth: null,\n  swipeLeft: null,\n  swiped: false, // used by swipeEvent. differentites between touch and swipe.\n  swiping: false,\n  touchObject: { startX: 0, startY: 0, curX: 0, curY: 0 },\n  trackStyle: {},\n  trackWidth: 0,\n  targetSlide: 0\n};\n\nexport default initialState;\n", "var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nmodule.exports = isObject;\n", "var root = require('./_root');\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nmodule.exports = now;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "var baseTrim = require('./_baseTrim'),\n    isObject = require('./isObject'),\n    isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = toNumber;\n", "var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nmodule.exports = baseTrim;\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nmodule.exports = trimmedEndIndex;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nmodule.exports = isSymbol;\n", "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n", "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nmodule.exports = isObjectLike;\n", "/*!\n  Copyright (c) 2018 <PERSON>.\n  Licensed under the MIT License (MIT), see\n  http://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames() {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\tif (arg.length) {\n\t\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\t\tif (inner) {\n\t\t\t\t\t\tclasses.push(inner);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tif (arg.toString === Object.prototype.toString) {\n\t\t\t\t\tfor (var key in arg) {\n\t\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tclasses.push(arg.toString());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import React from \"react\";\n\nexport function clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\n\nexport const safePreventDefault = event => {\n  const passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if(!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n}\n\nexport const getOnDemandLazySlides = spec => {\n  let onDemandSlides = [];\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n  for (let slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nexport const getRequiredLazySlides = spec => {\n  let requiredSlides = [];\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n  for (let slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nexport const lazyStartIndex = spec =>\n  spec.currentSlide - lazySlidesOnLeft(spec);\nexport const lazyEndIndex = spec => spec.currentSlide + lazySlidesOnRight(spec);\nexport const lazySlidesOnLeft = spec =>\n  spec.centerMode\n    ? Math.floor(spec.slidesToShow / 2) +\n      (parseInt(spec.centerPadding) > 0 ? 1 : 0)\n    : 0;\nexport const lazySlidesOnRight = spec =>\n  spec.centerMode\n    ? Math.floor((spec.slidesToShow - 1) / 2) +\n      1 +\n      (parseInt(spec.centerPadding) > 0 ? 1 : 0)\n    : spec.slidesToShow;\n\n// get width of an element\nexport const getWidth = elem => (elem && elem.offsetWidth) || 0;\nexport const getHeight = elem => (elem && elem.offsetHeight) || 0;\nexport const getSwipeDirection = (touchObject, verticalSwiping = false) => {\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round((r * 180) / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (\n    (swipeAngle <= 45 && swipeAngle >= 0) ||\n    (swipeAngle <= 360 && swipeAngle >= 315)\n  ) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n\n  return \"vertical\";\n};\n\n// whether or not we can go next\nexport const canGoNext = spec => {\n  let canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (\n      spec.slideCount <= spec.slidesToShow ||\n      spec.currentSlide >= spec.slideCount - spec.slidesToShow\n    ) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nexport const extractObject = (spec, keys) => {\n  let newObject = {};\n  keys.forEach(key => (newObject[key] = spec[key]));\n  return newObject;\n};\n\n// get initialized state\nexport const initializedState = spec => {\n  // spec also contains listRef, trackRef\n  let slideCount = React.Children.count(spec.children);\n  const listNode = spec.listRef;\n  let listWidth = Math.ceil(getWidth(listNode));\n  const trackNode = spec.trackRef && spec.trackRef.node;\n  let trackWidth = Math.ceil(getWidth(trackNode));\n  let slideWidth;\n  if (!spec.vertical) {\n    let centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (\n      typeof spec.centerPadding === \"string\" &&\n      spec.centerPadding.slice(-1) === \"%\"\n    ) {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  let slideHeight =\n    listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  let listHeight = slideHeight * spec.slidesToShow;\n  let currentSlide =\n    spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  let lazyLoadedList = spec.lazyLoadedList || [];\n  let slidesToLoad = getOnDemandLazySlides({\n    ...spec,\n    currentSlide,\n    lazyLoadedList\n  });\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n\n  let state = {\n    slideCount,\n    slideWidth,\n    listWidth,\n    trackWidth,\n    currentSlide,\n    slideHeight,\n    listHeight,\n    lazyLoadedList\n  };\n\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n\n  return state;\n};\n\nexport const slideHandler = spec => {\n  const {\n    waitForAnimate,\n    animating,\n    fade,\n    infinite,\n    index,\n    slideCount,\n    lazyLoad,\n    currentSlide,\n    centerMode,\n    slidesToScroll,\n    slidesToShow,\n    useCSS\n  } = spec;\n  let { lazyLoadedList } = spec;\n  if (waitForAnimate && animating) return {};\n  let animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  let state = {},\n    nextState = {};\n  const targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = { animating: false, targetSlide: animationSlide };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;\n      else if (slideCount % slidesToScroll !== 0)\n        finalSlide = slideCount - (slideCount % slidesToScroll);\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;\n      else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n\n    animationLeft = getTrackLeft({ ...spec, slideIndex: animationSlide });\n    finalLeft = getTrackLeft({ ...spec, slideIndex: finalSlide });\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(\n        getOnDemandLazySlides({ ...spec, currentSlide: animationSlide })\n      );\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS({ ...spec, left: finalLeft }),\n        lazyLoadedList,\n        targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS({ ...spec, left: animationLeft }),\n        lazyLoadedList,\n        targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS({ ...spec, left: finalLeft }),\n        swipeLeft: null,\n        targetSlide\n      };\n    }\n  }\n  return { state, nextState };\n};\n\nexport const changeSlide = (spec, options) => {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  const {\n    slidesToScroll,\n    slidesToShow,\n    slideCount,\n    currentSlide,\n    targetSlide: previousTargetSlide,\n    lazyLoad,\n    infinite\n  } = spec;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset =\n      indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide =\n        ((currentSlide + slidesToScroll) % slideCount) + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      let direction = siblingDirection({ ...spec, targetSlide });\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nexport const keyHandler = (e, accessibility, rtl) => {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility)\n    return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\n\nexport const swipeStart = (e, swipe, draggable) => {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || (!draggable && e.type.indexOf(\"mouse\") !== -1)) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nexport const swipeMove = (e, spec) => {\n  // spec also contains, trackRef and slideIndex\n  const {\n    scrolling,\n    animating,\n    vertical,\n    swipeToSlide,\n    verticalSwiping,\n    rtl,\n    currentSlide,\n    edgeFriction,\n    edgeDragged,\n    onEdge,\n    swiped,\n    swiping,\n    slideCount,\n    slidesToScroll,\n    infinite,\n    touchObject,\n    swipeEvent,\n    listHeight,\n    listWidth\n  } = spec;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  let swipeLeft,\n    state = {};\n  let curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(\n    Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2))\n  );\n  let verticalSwipeLength = Math.round(\n    Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2))\n  );\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return { scrolling: true };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  let positionOffset =\n    (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping)\n    positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n\n  let dotCount = Math.ceil(slideCount / slidesToScroll);\n  let swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  let touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (\n      (currentSlide === 0 && (swipeDirection === \"right\" || swipeDirection === \"down\")) ||\n      (currentSlide + 1 >= dotCount && (swipeDirection === \"left\" || swipeDirection === \"up\")) ||\n      (!canGoNext(spec) && (swipeDirection === \"left\" || swipeDirection === \"up\"))\n    ) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft =\n      curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = {\n    ...state,\n    touchObject,\n    swipeLeft,\n    trackStyle: getTrackCSS({ ...spec, left: swipeLeft })\n  };\n  if (\n    Math.abs(touchObject.curX - touchObject.startX) <\n    Math.abs(touchObject.curY - touchObject.startY) * 0.8\n  ) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nexport const swipeEnd = (e, spec) => {\n  const {\n    dragging,\n    swipe,\n    touchObject,\n    listWidth,\n    touchThreshold,\n    verticalSwiping,\n    listHeight,\n    swipeToSlide,\n    scrolling,\n    onSwipe,\n    targetSlide,\n    currentSlide,\n    infinite\n  } = spec;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  let minSwipe = verticalSwiping\n    ? listHeight / touchThreshold\n    : listWidth / touchThreshold;\n  let swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  let state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    let slideCount, newSlide;\n    let activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    let currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS({ ...spec, left: currentLeft });\n  }\n  return state;\n};\nexport const getNavigableIndexes = spec => {\n  let max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  let breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  let counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  let indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nexport const checkNavigable = (spec, index) => {\n  const navigables = getNavigableIndexes(spec);\n  let prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (let n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nexport const getSlideCount = spec => {\n  const centerOffset = spec.centerMode\n    ? spec.slideWidth * Math.floor(spec.slidesToShow / 2)\n    : 0;\n  if (spec.swipeToSlide) {\n    let swipedSlide;\n    const slickList = spec.listRef;\n    const slides =\n      (slickList.querySelectorAll &&\n        slickList.querySelectorAll(\".slick-slide\")) ||\n      [];\n    Array.from(slides).every(slide => {\n      if (!spec.vertical) {\n        if (\n          slide.offsetLeft - centerOffset + getWidth(slide) / 2 >\n          spec.swipeLeft * -1\n        ) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n\n      return true;\n    });\n\n    if (!swipedSlide) {\n      return 0;\n    }\n    const currentIndex =\n      spec.rtl === true\n        ? spec.slideCount - spec.currentSlide\n        : spec.currentSlide;\n    const slidesTraversed =\n      Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\n\nexport const checkSpecKeys = (spec, keysArray) =>\n  // eslint-disable-next-line no-prototype-builtins\n  keysArray.reduce((value, key) => value && spec.hasOwnProperty(key), true)\n    ? null\n    : console.error(\"Keys Missing:\", spec);\n\nexport const getTrackCSS = spec => {\n  checkSpecKeys(spec, [\n    \"left\",\n    \"variableWidth\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slideWidth\"\n  ]);\n  let trackWidth, trackHeight;\n  const trackChildren = spec.slideCount + 2 * spec.slidesToShow;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  let style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    let WebkitTransform = !spec.vertical\n      ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\"\n      : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    let transform = !spec.vertical\n      ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\"\n      : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    let msTransform = !spec.vertical\n      ? \"translateX(\" + spec.left + \"px)\"\n      : \"translateY(\" + spec.left + \"px)\";\n    style = {\n      ...style,\n      WebkitTransform,\n      transform,\n      msTransform\n    };\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = { opacity: 1 };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n\n  return style;\n};\nexport const getTrackAnimateCSS = spec => {\n  checkSpecKeys(spec, [\n    \"left\",\n    \"variableWidth\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slideWidth\",\n    \"speed\",\n    \"cssEase\"\n  ]);\n  let style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition =\n      \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nexport const getTrackLeft = spec => {\n  if (spec.unslick) {\n    return 0;\n  }\n\n  checkSpecKeys(spec, [\n    \"slideIndex\",\n    \"trackRef\",\n    \"infinite\",\n    \"centerMode\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slidesToScroll\",\n    \"slideWidth\",\n    \"listWidth\",\n    \"variableWidth\",\n    \"slideHeight\"\n  ]);\n\n  const {\n    slideIndex,\n    trackRef,\n    infinite,\n    centerMode,\n    slideCount,\n    slidesToShow,\n    slidesToScroll,\n    slideWidth,\n    listWidth,\n    variableWidth,\n    slideHeight,\n    fade,\n    vertical\n  } = spec;\n\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n\n  let slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (\n      slideCount % slidesToScroll !== 0 &&\n      slideIndex + slidesToScroll > slideCount\n    ) {\n      slidesToOffset = -(slideIndex > slideCount\n        ? slidesToShow - (slideIndex - slideCount)\n        : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (\n      slideCount % slidesToScroll !== 0 &&\n      slideIndex + slidesToScroll > slideCount\n    ) {\n      slidesToOffset = slidesToShow - (slideCount % slidesToScroll);\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    const trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite\n        ? slideIndex + getPreClones(spec)\n        : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (let slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -=\n          trackElem &&\n          trackElem.children[slide] &&\n          trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n\n  return targetLeft;\n};\n\nexport const getPreClones = spec => {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\n\nexport const getPostClones = spec => {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\n\nexport const getTotalSlides = spec =>\n  spec.slideCount === 1\n    ? 1\n    : getPreClones(spec) + spec.slideCount + getPostClones(spec);\nexport const siblingDirection = spec => {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\n\nexport const slidesOnRight = ({\n  slidesToShow,\n  centerMode,\n  rtl,\n  centerPadding\n}) => {\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    let right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\n\nexport const slidesOnLeft = ({\n  slidesToShow,\n  centerMode,\n  rtl,\n  centerPadding\n}) => {\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    let left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\n\nexport const canUseDOM = () =>\n  !!(\n    typeof window !== \"undefined\" &&\n    window.document &&\n    window.document.createElement\n  );\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport {\n  lazyStartIndex,\n  lazyEndIndex,\n  getPreClones\n} from \"./utils/innerSliderUtils\";\n\n// given specifications/props for a slide, fetch all the classes that need to be applied to the slide\nconst getSlideClasses = spec => {\n  let slickActive, slickCenter, slickCloned;\n  let centerOffset, index;\n\n  if (spec.rtl) {\n    index = spec.slideCount - 1 - spec.index;\n  } else {\n    index = spec.index;\n  }\n  slickCloned = index < 0 || index >= spec.slideCount;\n  if (spec.centerMode) {\n    centerOffset = Math.floor(spec.slidesToShow / 2);\n    slickCenter = (index - spec.currentSlide) % spec.slideCount === 0;\n    if (\n      index > spec.currentSlide - centerOffset - 1 &&\n      index <= spec.currentSlide + centerOffset\n    ) {\n      slickActive = true;\n    }\n  } else {\n    slickActive =\n      spec.currentSlide <= index &&\n      index < spec.currentSlide + spec.slidesToShow;\n  }\n\n  let focusedSlide;\n  if (spec.targetSlide < 0) {\n    focusedSlide = spec.targetSlide + spec.slideCount;\n  } else if (spec.targetSlide >= spec.slideCount) {\n    focusedSlide = spec.targetSlide - spec.slideCount;\n  } else {\n    focusedSlide = spec.targetSlide;\n  }\n  let slickCurrent = index === focusedSlide;\n  return {\n    \"slick-slide\": true,\n    \"slick-active\": slickActive,\n    \"slick-center\": slickCenter,\n    \"slick-cloned\": slickCloned,\n    \"slick-current\": slickCurrent // dubious in case of RTL\n  };\n};\n\nconst getSlideStyle = spec => {\n  let style = {};\n\n  if (spec.variableWidth === undefined || spec.variableWidth === false) {\n    style.width = spec.slideWidth;\n  }\n\n  if (spec.fade) {\n    style.position = \"relative\";\n    if (spec.vertical) {\n      style.top = -spec.index * parseInt(spec.slideHeight);\n    } else {\n      style.left = -spec.index * parseInt(spec.slideWidth);\n    }\n    style.opacity = spec.currentSlide === spec.index ? 1 : 0;\n    if (spec.useCSS) {\n      style.transition =\n        \"opacity \" +\n        spec.speed +\n        \"ms \" +\n        spec.cssEase +\n        \", \" +\n        \"visibility \" +\n        spec.speed +\n        \"ms \" +\n        spec.cssEase;\n    }\n  }\n\n  return style;\n};\n\nconst getKey = (child, fallbackKey) => child.key + \"-\" + fallbackKey;\n\nconst renderSlides = spec => {\n  let key;\n  let slides = [];\n  let preCloneSlides = [];\n  let postCloneSlides = [];\n  let childrenCount = React.Children.count(spec.children);\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n\n  React.Children.forEach(spec.children, (elem, index) => {\n    let child;\n    let childOnClickOptions = {\n      message: \"children\",\n      index: index,\n      slidesToScroll: spec.slidesToScroll,\n      currentSlide: spec.currentSlide\n    };\n\n    // in case of lazyLoad, whether or not we want to fetch the slide\n    if (\n      !spec.lazyLoad ||\n      (spec.lazyLoad && spec.lazyLoadedList.indexOf(index) >= 0)\n    ) {\n      child = elem;\n    } else {\n      child = <div />;\n    }\n    let childStyle = getSlideStyle({ ...spec, index });\n    let slideClass = child.props.className || \"\";\n    let slideClasses = getSlideClasses({ ...spec, index });\n    // push a cloned element of the desired slide\n    slides.push(\n      React.cloneElement(child, {\n        key: \"original\" + getKey(child, index),\n        \"data-index\": index,\n        className: classnames(slideClasses, slideClass),\n        tabIndex: \"-1\",\n        \"aria-hidden\": !slideClasses[\"slick-active\"],\n        style: { outline: \"none\", ...(child.props.style || {}), ...childStyle },\n        onClick: e => {\n          child.props && child.props.onClick && child.props.onClick(e);\n          if (spec.focusOnSelect) {\n            spec.focusOnSelect(childOnClickOptions);\n          }\n        }\n      })\n    );\n\n    // if slide needs to be precloned or postcloned\n    if (spec.infinite && spec.fade === false) {\n      let preCloneNo = childrenCount - index;\n      if (\n        preCloneNo <= getPreClones(spec) &&\n        childrenCount !== spec.slidesToShow\n      ) {\n        key = -preCloneNo;\n        if (key >= startIndex) {\n          child = elem;\n        }\n        slideClasses = getSlideClasses({ ...spec, index: key });\n        preCloneSlides.push(\n          React.cloneElement(child, {\n            key: \"precloned\" + getKey(child, key),\n            \"data-index\": key,\n            tabIndex: \"-1\",\n            className: classnames(slideClasses, slideClass),\n            \"aria-hidden\": !slideClasses[\"slick-active\"],\n            style: { ...(child.props.style || {}), ...childStyle },\n            onClick: e => {\n              child.props && child.props.onClick && child.props.onClick(e);\n              if (spec.focusOnSelect) {\n                spec.focusOnSelect(childOnClickOptions);\n              }\n            }\n          })\n        );\n      }\n\n      if (childrenCount !== spec.slidesToShow) {\n        key = childrenCount + index;\n        if (key < endIndex) {\n          child = elem;\n        }\n        slideClasses = getSlideClasses({ ...spec, index: key });\n        postCloneSlides.push(\n          React.cloneElement(child, {\n            key: \"postcloned\" + getKey(child, key),\n            \"data-index\": key,\n            tabIndex: \"-1\",\n            className: classnames(slideClasses, slideClass),\n            \"aria-hidden\": !slideClasses[\"slick-active\"],\n            style: { ...(child.props.style || {}), ...childStyle },\n            onClick: e => {\n              child.props && child.props.onClick && child.props.onClick(e);\n              if (spec.focusOnSelect) {\n                spec.focusOnSelect(childOnClickOptions);\n              }\n            }\n          })\n        );\n      }\n    }\n  });\n\n  if (spec.rtl) {\n    return preCloneSlides.concat(slides, postCloneSlides).reverse();\n  } else {\n    return preCloneSlides.concat(slides, postCloneSlides);\n  }\n};\n\nexport class Track extends React.PureComponent {\n  node = null;\n\n  handleRef = ref => {\n    this.node = ref;\n  };\n\n  render() {\n    const slides = renderSlides(this.props);\n    const { onMouseEnter, onMouseOver, onMouseLeave } = this.props;\n    const mouseEvents = { onMouseEnter, onMouseOver, onMouseLeave };\n    return (\n      <div\n        ref={this.handleRef}\n        className=\"slick-track\"\n        style={this.props.trackStyle}\n        {...mouseEvents}\n      >\n        {slides}\n      </div>\n    );\n  }\n}\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\n\nconst getDotCount = spec => {\n  let dots;\n\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots =\n      Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) +\n      1;\n  }\n\n  return dots;\n};\n\nexport class Dots extends React.PureComponent {\n  clickHandler(options, e) {\n    // In Autoplay the focus stays on clicked button even after transition\n    // to next slide. That only goes away by click somewhere outside\n    e.preventDefault();\n    this.props.clickHandler(options);\n  }\n  render() {\n    const {\n      onMouseEnter,\n      onMouseOver,\n      onMouseLeave,\n      infinite,\n      slidesToScroll,\n      slidesToShow,\n      slideCount,\n      currentSlide\n    } = this.props;\n    let dotCount = getDotCount({\n      slideCount,\n      slidesToScroll,\n      slidesToShow,\n      infinite\n    });\n\n    const mouseEvents = { onMouseEnter, onMouseOver, onMouseLeave };\n    let dots = [];\n    for (let i = 0; i < dotCount; i++) {\n      let _rightBound = (i + 1) * slidesToScroll - 1;\n      let rightBound = infinite\n        ? _rightBound\n        : clamp(_rightBound, 0, slideCount - 1);\n      let _leftBound = rightBound - (slidesToScroll - 1);\n      let leftBound = infinite\n        ? _leftBound\n        : clamp(_leftBound, 0, slideCount - 1);\n\n      let className = classnames({\n        \"slick-active\": infinite\n          ? currentSlide >= leftBound && currentSlide <= rightBound\n          : currentSlide === leftBound\n      });\n\n      let dotOptions = {\n        message: \"dots\",\n        index: i,\n        slidesToScroll,\n        currentSlide\n      };\n\n      let onClick = this.clickHandler.bind(this, dotOptions);\n      dots = dots.concat(\n        <li key={i} className={className}>\n          {React.cloneElement(this.props.customPaging(i), { onClick })}\n        </li>\n      );\n    }\n\n    return React.cloneElement(this.props.appendDots(dots), {\n      className: this.props.dotsClass,\n      ...mouseEvents\n    });\n  }\n}\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { canGoNext } from \"./utils/innerSliderUtils\";\n\nexport class PrevArrow extends React.PureComponent {\n  clickHandler(options, e) {\n    if (e) {\n      e.preventDefault();\n    }\n    this.props.clickHandler(options, e);\n  }\n  render() {\n    let prevClasses = { \"slick-arrow\": true, \"slick-prev\": true };\n    let prevHandler = this.clickHandler.bind(this, { message: \"previous\" });\n\n    if (\n      !this.props.infinite &&\n      (this.props.currentSlide === 0 ||\n        this.props.slideCount <= this.props.slidesToShow)\n    ) {\n      prevClasses[\"slick-disabled\"] = true;\n      prevHandler = null;\n    }\n\n    let prevArrowProps = {\n      key: \"0\",\n      \"data-role\": \"none\",\n      className: classnames(prevClasses),\n      style: { display: \"block\" },\n      onClick: prevHandler\n    };\n    let customProps = {\n      currentSlide: this.props.currentSlide,\n      slideCount: this.props.slideCount\n    };\n    let prevArrow;\n\n    if (this.props.prevArrow) {\n      prevArrow = React.cloneElement(this.props.prevArrow, {\n        ...prevArrowProps,\n        ...customProps\n      });\n    } else {\n      prevArrow = (\n        <button key=\"0\" type=\"button\" {...prevArrowProps}>\n          {\" \"}\n          Previous\n        </button>\n      );\n    }\n\n    return prevArrow;\n  }\n}\n\nexport class NextArrow extends React.PureComponent {\n  clickHandler(options, e) {\n    if (e) {\n      e.preventDefault();\n    }\n    this.props.clickHandler(options, e);\n  }\n  render() {\n    let nextClasses = { \"slick-arrow\": true, \"slick-next\": true };\n    let nextHandler = this.clickHandler.bind(this, { message: \"next\" });\n\n    if (!canGoNext(this.props)) {\n      nextClasses[\"slick-disabled\"] = true;\n      nextHandler = null;\n    }\n\n    let nextArrowProps = {\n      key: \"1\",\n      \"data-role\": \"none\",\n      className: classnames(nextClasses),\n      style: { display: \"block\" },\n      onClick: nextHandler\n    };\n    let customProps = {\n      currentSlide: this.props.currentSlide,\n      slideCount: this.props.slideCount\n    };\n    let nextArrow;\n\n    if (this.props.nextArrow) {\n      nextArrow = React.cloneElement(this.props.nextArrow, {\n        ...nextArrowProps,\n        ...customProps\n      });\n    } else {\n      nextArrow = (\n        <button key=\"1\" type=\"button\" {...nextArrowProps}>\n          {\" \"}\n          Next\n        </button>\n      );\n    }\n\n    return nextArrow;\n  }\n}\n", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "var camel2hyphen = require('string-convert/camel2hyphen');\n\nvar isDimension = function (feature) {\n  var re = /[height|width]$/;\n  return re.test(feature);\n};\n\nvar obj2mq = function (obj) {\n  var mq = '';\n  var features = Object.keys(obj);\n  features.forEach(function (feature, index) {\n    var value = obj[feature];\n    feature = camel2hyphen(feature);\n    // Add px to dimension features\n    if (isDimension(feature) && typeof value === 'number') {\n      value = value + 'px';\n    }\n    if (value === true) {\n      mq += feature;\n    } else if (value === false) {\n      mq += 'not ' + feature;\n    } else {\n      mq += '(' + feature + ': ' + value + ')';\n    }\n    if (index < features.length-1) {\n      mq += ' and '\n    }\n  });\n  return mq;\n};\n\nvar json2mq = function (query) {\n  var mq = '';\n  if (typeof query === 'string') {\n    return query;\n  }\n  // Handling array of media queries\n  if (query instanceof Array) {\n    query.forEach(function (q, index) {\n      mq += obj2mq(q);\n      if (index < query.length-1) {\n        mq += ', '\n      }\n    });\n    return mq;\n  }\n  // Handling single media query\n  return obj2mq(query);\n};\n\nmodule.exports = json2mq;", "var camel2hyphen = function (str) {\n  return str\n          .replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n          })\n          .toLowerCase();\n};\n\nmodule.exports = camel2hyphen;", "import React from \"react\";\n\nlet defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: dots => <ul style={{ display: \"block\" }}>{dots}</ul>,\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: i => <button>{i + 1}</button>,\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true\n};\n\nexport default defaultProps;\n"], "sourceRoot": ""}