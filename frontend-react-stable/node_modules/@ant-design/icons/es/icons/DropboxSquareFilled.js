import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DropboxSquareFilledSvg from "@ant-design/icons-svg/es/asn/DropboxSquareFilled";
import AntdIcon from '../components/AntdIcon';
var DropboxSquareFilled = function DropboxSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DropboxSquareFilledSvg
  }));
};
DropboxSquareFilled.displayName = 'DropboxSquareFilled';
export default /*#__PURE__*/React.forwardRef(DropboxSquareFilled);