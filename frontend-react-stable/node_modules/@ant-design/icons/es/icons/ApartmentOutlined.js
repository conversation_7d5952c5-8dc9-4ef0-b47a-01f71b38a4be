import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ApartmentOutlinedSvg from "@ant-design/icons-svg/es/asn/ApartmentOutlined";
import AntdIcon from '../components/AntdIcon';
var ApartmentOutlined = function ApartmentOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ApartmentOutlinedSvg
  }));
};
ApartmentOutlined.displayName = 'ApartmentOutlined';
export default /*#__PURE__*/React.forwardRef(ApartmentOutlined);