import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/UpCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var UpCircleTwoTone = function UpCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpCircleTwoToneSvg
  }));
};
UpCircleTwoTone.displayName = 'UpCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(UpCircleTwoTone);