import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/DownSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var DownSquareTwoTone = function DownSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownSquareTwoToneSvg
  }));
};
DownSquareTwoTone.displayName = 'DownSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(DownSquareTwoTone);