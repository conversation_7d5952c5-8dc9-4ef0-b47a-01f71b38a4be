import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightCircleFilledSvg from "@ant-design/icons-svg/es/asn/RightCircleFilled";
import AntdIcon from '../components/AntdIcon';
var RightCircleFilled = function RightCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightCircleFilledSvg
  }));
};
RightCircleFilled.displayName = 'RightCircleFilled';
export default /*#__PURE__*/React.forwardRef(RightCircleFilled);