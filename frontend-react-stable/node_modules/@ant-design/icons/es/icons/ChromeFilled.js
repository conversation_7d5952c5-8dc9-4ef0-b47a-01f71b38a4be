import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ChromeFilledSvg from "@ant-design/icons-svg/es/asn/ChromeFilled";
import AntdIcon from '../components/AntdIcon';
var ChromeFilled = function ChromeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ChromeFilledSvg
  }));
};
ChromeFilled.displayName = 'ChromeFilled';
export default /*#__PURE__*/React.forwardRef(ChromeFilled);