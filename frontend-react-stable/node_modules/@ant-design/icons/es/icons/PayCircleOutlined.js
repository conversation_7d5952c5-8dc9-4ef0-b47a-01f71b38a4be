import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PayCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PayCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PayCircleOutlined = function PayCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PayCircleOutlinedSvg
  }));
};
PayCircleOutlined.displayName = 'PayCircleOutlined';
export default /*#__PURE__*/React.forwardRef(PayCircleOutlined);