import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PoundCircleFilledSvg from "@ant-design/icons-svg/es/asn/PoundCircleFilled";
import AntdIcon from '../components/AntdIcon';
var PoundCircleFilled = function PoundCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PoundCircleFilledSvg
  }));
};
PoundCircleFilled.displayName = 'PoundCircleFilled';
export default /*#__PURE__*/React.forwardRef(PoundCircleFilled);