import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PauseOutlinedSvg from "@ant-design/icons-svg/es/asn/PauseOutlined";
import AntdIcon from '../components/AntdIcon';
var PauseOutlined = function PauseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PauseOutlinedSvg
  }));
};
PauseOutlined.displayName = 'PauseOutlined';
export default /*#__PURE__*/React.forwardRef(PauseOutlined);