import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SecurityScanFilledSvg from "@ant-design/icons-svg/es/asn/SecurityScanFilled";
import AntdIcon from '../components/AntdIcon';
var SecurityScanFilled = function SecurityScanFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SecurityScanFilledSvg
  }));
};
SecurityScanFilled.displayName = 'SecurityScanFilled';
export default /*#__PURE__*/React.forwardRef(SecurityScanFilled);