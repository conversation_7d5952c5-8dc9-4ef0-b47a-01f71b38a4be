import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GitlabFilledSvg from "@ant-design/icons-svg/es/asn/GitlabFilled";
import AntdIcon from '../components/AntdIcon';
var GitlabFilled = function GitlabFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GitlabFilledSvg
  }));
};
GitlabFilled.displayName = 'GitlabFilled';
export default /*#__PURE__*/React.forwardRef(GitlabFilled);