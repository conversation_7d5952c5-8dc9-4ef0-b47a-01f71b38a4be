import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ColumnHeightOutlinedSvg from "@ant-design/icons-svg/es/asn/ColumnHeightOutlined";
import AntdIcon from '../components/AntdIcon';
var ColumnHeightOutlined = function ColumnHeightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ColumnHeightOutlinedSvg
  }));
};
ColumnHeightOutlined.displayName = 'ColumnHeightOutlined';
export default /*#__PURE__*/React.forwardRef(ColumnHeightOutlined);