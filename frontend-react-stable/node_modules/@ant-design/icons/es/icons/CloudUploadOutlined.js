import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudUploadOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudUploadOutlined";
import AntdIcon from '../components/AntdIcon';
var CloudUploadOutlined = function CloudUploadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudUploadOutlinedSvg
  }));
};
CloudUploadOutlined.displayName = 'CloudUploadOutlined';
export default /*#__PURE__*/React.forwardRef(CloudUploadOutlined);