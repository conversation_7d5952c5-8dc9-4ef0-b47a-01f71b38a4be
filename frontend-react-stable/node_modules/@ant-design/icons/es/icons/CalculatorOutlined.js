import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalculatorOutlinedSvg from "@ant-design/icons-svg/es/asn/CalculatorOutlined";
import AntdIcon from '../components/AntdIcon';
var CalculatorOutlined = function CalculatorOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalculatorOutlinedSvg
  }));
};
CalculatorOutlined.displayName = 'CalculatorOutlined';
export default /*#__PURE__*/React.forwardRef(CalculatorOutlined);