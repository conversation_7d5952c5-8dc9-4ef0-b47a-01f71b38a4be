import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CompassOutlinedSvg from "@ant-design/icons-svg/es/asn/CompassOutlined";
import AntdIcon from '../components/AntdIcon';
var CompassOutlined = function CompassOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CompassOutlinedSvg
  }));
};
CompassOutlined.displayName = 'CompassOutlined';
export default /*#__PURE__*/React.forwardRef(CompassOutlined);