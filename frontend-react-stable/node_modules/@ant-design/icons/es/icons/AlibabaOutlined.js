import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON>babaOutlinedSvg from "@ant-design/icons-svg/es/asn/AlibabaOutlined";
import AntdIcon from '../components/AntdIcon';
var AlibabaOutlined = function AlibabaOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlibabaOutlinedSvg
  }));
};
AlibabaOutlined.displayName = 'AlibabaOutlined';
export default /*#__PURE__*/React.forwardRef(AlibabaOutlined);