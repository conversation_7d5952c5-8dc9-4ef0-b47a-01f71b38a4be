import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LockFilledSvg from "@ant-design/icons-svg/es/asn/LockFilled";
import AntdIcon from '../components/AntdIcon';
var LockFilled = function LockFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LockFilledSvg
  }));
};
LockFilled.displayName = 'LockFilled';
export default /*#__PURE__*/React.forwardRef(LockFilled);