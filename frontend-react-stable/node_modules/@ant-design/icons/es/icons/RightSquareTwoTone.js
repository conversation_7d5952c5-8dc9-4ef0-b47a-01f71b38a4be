import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/RightSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var RightSquareTwoTone = function RightSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightSquareTwoToneSvg
  }));
};
RightSquareTwoTone.displayName = 'RightSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(RightSquareTwoTone);