import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SkypeOutlinedSvg from "@ant-design/icons-svg/es/asn/SkypeOutlined";
import AntdIcon from '../components/AntdIcon';
var SkypeOutlined = function SkypeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SkypeOutlinedSvg
  }));
};
SkypeOutlined.displayName = 'SkypeOutlined';
export default /*#__PURE__*/React.forwardRef(SkypeOutlined);