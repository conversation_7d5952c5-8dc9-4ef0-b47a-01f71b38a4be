import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileTwoToneSvg from "@ant-design/icons-svg/es/asn/FileTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileTwoTone = function FileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileTwoToneSvg
  }));
};
FileTwoTone.displayName = 'FileTwoTone';
export default /*#__PURE__*/React.forwardRef(FileTwoTone);