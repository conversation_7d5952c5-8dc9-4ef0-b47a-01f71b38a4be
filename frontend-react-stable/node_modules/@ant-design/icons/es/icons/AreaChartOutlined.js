import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AreaChartOutlinedSvg from "@ant-design/icons-svg/es/asn/AreaChartOutlined";
import AntdIcon from '../components/AntdIcon';
var AreaChartOutlined = function AreaChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AreaChartOutlinedSvg
  }));
};
AreaChartOutlined.displayName = 'AreaChartOutlined';
export default /*#__PURE__*/React.forwardRef(AreaChartOutlined);