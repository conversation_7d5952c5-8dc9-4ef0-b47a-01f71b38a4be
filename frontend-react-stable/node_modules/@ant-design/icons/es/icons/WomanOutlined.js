import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WomanOutlinedSvg from "@ant-design/icons-svg/es/asn/WomanOutlined";
import AntdIcon from '../components/AntdIcon';
var WomanOutlined = function WomanOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WomanOutlinedSvg
  }));
};
WomanOutlined.displayName = 'WomanOutlined';
export default /*#__PURE__*/React.forwardRef(WomanOutlined);