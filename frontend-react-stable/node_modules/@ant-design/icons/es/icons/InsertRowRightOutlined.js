import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsertRowRightOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowRightOutlined";
import AntdIcon from '../components/AntdIcon';
var InsertRowRightOutlined = function InsertRowRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsertRowRightOutlinedSvg
  }));
};
InsertRowRightOutlined.displayName = 'InsertRowRightOutlined';
export default /*#__PURE__*/React.forwardRef(InsertRowRightOutlined);