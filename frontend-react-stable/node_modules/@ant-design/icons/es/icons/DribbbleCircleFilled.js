import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DribbbleCircleFilledSvg from "@ant-design/icons-svg/es/asn/DribbbleCircleFilled";
import AntdIcon from '../components/AntdIcon';
var DribbbleCircleFilled = function DribbbleCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DribbbleCircleFilledSvg
  }));
};
DribbbleCircleFilled.displayName = 'DribbbleCircleFilled';
export default /*#__PURE__*/React.forwardRef(DribbbleCircleFilled);