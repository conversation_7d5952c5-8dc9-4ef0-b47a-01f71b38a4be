import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HomeOutlinedSvg from "@ant-design/icons-svg/es/asn/HomeOutlined";
import AntdIcon from '../components/AntdIcon';
var HomeOutlined = function HomeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HomeOutlinedSvg
  }));
};
HomeOutlined.displayName = 'HomeOutlined';
export default /*#__PURE__*/React.forwardRef(HomeOutlined);