import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwapRightOutlinedSvg from "@ant-design/icons-svg/es/asn/SwapRightOutlined";
import AntdIcon from '../components/AntdIcon';
var SwapRightOutlined = function SwapRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwapRightOutlinedSvg
  }));
};
SwapRightOutlined.displayName = 'SwapRightOutlined';
export default /*#__PURE__*/React.forwardRef(SwapRightOutlined);