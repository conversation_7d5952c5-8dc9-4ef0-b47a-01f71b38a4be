import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IdcardFilledSvg from "@ant-design/icons-svg/es/asn/IdcardFilled";
import AntdIcon from '../components/AntdIcon';
var IdcardFilled = function IdcardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IdcardFilledSvg
  }));
};
IdcardFilled.displayName = 'IdcardFilled';
export default /*#__PURE__*/React.forwardRef(IdcardFilled);