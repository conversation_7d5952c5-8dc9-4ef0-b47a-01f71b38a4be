import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InfoOutlinedSvg from "@ant-design/icons-svg/es/asn/InfoOutlined";
import AntdIcon from '../components/AntdIcon';
var InfoOutlined = function InfoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InfoOutlinedSvg
  }));
};
InfoOutlined.displayName = 'InfoOutlined';
export default /*#__PURE__*/React.forwardRef(InfoOutlined);