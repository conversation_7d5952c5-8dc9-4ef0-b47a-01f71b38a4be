import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseSquareFilledSvg from "@ant-design/icons-svg/es/asn/CloseSquareFilled";
import AntdIcon from '../components/AntdIcon';
var CloseSquareFilled = function CloseSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseSquareFilledSvg
  }));
};
CloseSquareFilled.displayName = 'CloseSquareFilled';
export default /*#__PURE__*/React.forwardRef(CloseSquareFilled);