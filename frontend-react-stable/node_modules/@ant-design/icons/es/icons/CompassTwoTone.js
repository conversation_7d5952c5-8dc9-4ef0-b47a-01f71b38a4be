import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CompassTwoToneSvg from "@ant-design/icons-svg/es/asn/CompassTwoTone";
import AntdIcon from '../components/AntdIcon';
var CompassTwoTone = function CompassTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CompassTwoToneSvg
  }));
};
CompassTwoTone.displayName = 'CompassTwoTone';
export default /*#__PURE__*/React.forwardRef(CompassTwoTone);