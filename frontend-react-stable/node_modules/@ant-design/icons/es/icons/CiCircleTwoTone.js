import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/CiCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var CiCircleTwoTone = function CiCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiCircleTwoToneSvg
  }));
};
CiCircleTwoTone.displayName = 'CiCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(CiCircleTwoTone);