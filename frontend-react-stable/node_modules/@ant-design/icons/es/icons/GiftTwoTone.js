import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GiftTwoToneSvg from "@ant-design/icons-svg/es/asn/GiftTwoTone";
import AntdIcon from '../components/AntdIcon';
var GiftTwoTone = function GiftTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GiftTwoToneSvg
  }));
};
GiftTwoTone.displayName = 'GiftTwoTone';
export default /*#__PURE__*/React.forwardRef(GiftTwoTone);