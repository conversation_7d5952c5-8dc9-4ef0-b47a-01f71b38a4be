import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlaySquareOutlinedSvg from "@ant-design/icons-svg/es/asn/PlaySquareOutlined";
import AntdIcon from '../components/AntdIcon';
var PlaySquareOutlined = function PlaySquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlaySquareOutlinedSvg
  }));
};
PlaySquareOutlined.displayName = 'PlaySquareOutlined';
export default /*#__PURE__*/React.forwardRef(PlaySquareOutlined);