import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeleteTwoToneSvg from "@ant-design/icons-svg/es/asn/DeleteTwoTone";
import AntdIcon from '../components/AntdIcon';
var DeleteTwoTone = function DeleteTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeleteTwoToneSvg
  }));
};
DeleteTwoTone.displayName = 'DeleteTwoTone';
export default /*#__PURE__*/React.forwardRef(DeleteTwoTone);