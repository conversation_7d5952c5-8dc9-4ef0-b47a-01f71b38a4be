import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SaveTwoToneSvg from "@ant-design/icons-svg/es/asn/SaveTwoTone";
import AntdIcon from '../components/AntdIcon';
var SaveTwoTone = function SaveTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SaveTwoToneSvg
  }));
};
SaveTwoTone.displayName = 'SaveTwoTone';
export default /*#__PURE__*/React.forwardRef(SaveTwoTone);