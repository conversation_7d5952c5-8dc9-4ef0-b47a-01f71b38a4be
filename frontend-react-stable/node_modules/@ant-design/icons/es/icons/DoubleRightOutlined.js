import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DoubleRightOutlinedSvg from "@ant-design/icons-svg/es/asn/DoubleRightOutlined";
import AntdIcon from '../components/AntdIcon';
var DoubleRightOutlined = function DoubleRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DoubleRightOutlinedSvg
  }));
};
DoubleRightOutlined.displayName = 'DoubleRightOutlined';
export default /*#__PURE__*/React.forwardRef(DoubleRightOutlined);