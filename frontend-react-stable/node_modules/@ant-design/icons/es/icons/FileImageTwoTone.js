import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileImageTwoToneSvg from "@ant-design/icons-svg/es/asn/FileImageTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileImageTwoTone = function FileImageTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileImageTwoToneSvg
  }));
};
FileImageTwoTone.displayName = 'FileImageTwoTone';
export default /*#__PURE__*/React.forwardRef(FileImageTwoTone);