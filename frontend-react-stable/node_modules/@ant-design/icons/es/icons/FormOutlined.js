import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FormOutlinedSvg from "@ant-design/icons-svg/es/asn/FormOutlined";
import AntdIcon from '../components/AntdIcon';
var FormOutlined = function FormOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FormOutlinedSvg
  }));
};
FormOutlined.displayName = 'FormOutlined';
export default /*#__PURE__*/React.forwardRef(FormOutlined);