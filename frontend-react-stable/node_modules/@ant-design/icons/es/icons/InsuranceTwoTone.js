import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsuranceTwoToneSvg from "@ant-design/icons-svg/es/asn/InsuranceTwoTone";
import AntdIcon from '../components/AntdIcon';
var InsuranceTwoTone = function InsuranceTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsuranceTwoToneSvg
  }));
};
InsuranceTwoTone.displayName = 'InsuranceTwoTone';
export default /*#__PURE__*/React.forwardRef(InsuranceTwoTone);