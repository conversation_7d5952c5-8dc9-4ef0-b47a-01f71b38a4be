import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ControlOutlinedSvg from "@ant-design/icons-svg/es/asn/ControlOutlined";
import AntdIcon from '../components/AntdIcon';
var ControlOutlined = function ControlOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ControlOutlinedSvg
  }));
};
ControlOutlined.displayName = 'ControlOutlined';
export default /*#__PURE__*/React.forwardRef(ControlOutlined);