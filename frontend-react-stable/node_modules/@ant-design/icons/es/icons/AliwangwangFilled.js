import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AliwangwangFilledSvg from "@ant-design/icons-svg/es/asn/AliwangwangFilled";
import AntdIcon from '../components/AntdIcon';
var AliwangwangFilled = function AliwangwangFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AliwangwangFilledSvg
  }));
};
AliwangwangFilled.displayName = 'AliwangwangFilled';
export default /*#__PURE__*/React.forwardRef(AliwangwangFilled);