import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AccountBookFilledSvg from "@ant-design/icons-svg/es/asn/AccountBookFilled";
import AntdIcon from '../components/AntdIcon';
var AccountBookFilled = function AccountBookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AccountBookFilledSvg
  }));
};
AccountBookFilled.displayName = 'AccountBookFilled';
export default /*#__PURE__*/React.forwardRef(AccountBookFilled);