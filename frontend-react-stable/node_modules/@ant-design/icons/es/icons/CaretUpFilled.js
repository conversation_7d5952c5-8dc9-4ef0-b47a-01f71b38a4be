import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretUpFilledSvg from "@ant-design/icons-svg/es/asn/CaretUpFilled";
import AntdIcon from '../components/AntdIcon';
var CaretUpFilled = function CaretUpFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretUpFilledSvg
  }));
};
CaretUpFilled.displayName = 'CaretUpFilled';
export default /*#__PURE__*/React.forwardRef(CaretUpFilled);