import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MobileOutlinedSvg from "@ant-design/icons-svg/es/asn/MobileOutlined";
import AntdIcon from '../components/AntdIcon';
var MobileOutlined = function MobileOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MobileOutlinedSvg
  }));
};
MobileOutlined.displayName = 'MobileOutlined';
export default /*#__PURE__*/React.forwardRef(MobileOutlined);