import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BellTwoToneSvg from "@ant-design/icons-svg/es/asn/BellTwoTone";
import AntdIcon from '../components/AntdIcon';
var BellTwoTone = function BellTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BellTwoToneSvg
  }));
};
BellTwoTone.displayName = 'BellTwoTone';
export default /*#__PURE__*/React.forwardRef(BellTwoTone);