import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DragOutlinedSvg from "@ant-design/icons-svg/es/asn/DragOutlined";
import AntdIcon from '../components/AntdIcon';
var DragOutlined = function DragOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DragOutlinedSvg
  }));
};
DragOutlined.displayName = 'DragOutlined';
export default /*#__PURE__*/React.forwardRef(DragOutlined);