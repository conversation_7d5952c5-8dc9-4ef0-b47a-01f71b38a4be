import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SafetyCertificateOutlinedSvg from "@ant-design/icons-svg/es/asn/SafetyCertificateOutlined";
import AntdIcon from '../components/AntdIcon';
var SafetyCertificateOutlined = function SafetyCertificateOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SafetyCertificateOutlinedSvg
  }));
};
SafetyCertificateOutlined.displayName = 'SafetyCertificateOutlined';
export default /*#__PURE__*/React.forwardRef(SafetyCertificateOutlined);