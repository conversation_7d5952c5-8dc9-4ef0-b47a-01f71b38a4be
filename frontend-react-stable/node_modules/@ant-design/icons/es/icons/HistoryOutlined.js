import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HistoryOutlinedSvg from "@ant-design/icons-svg/es/asn/HistoryOutlined";
import AntdIcon from '../components/AntdIcon';
var HistoryOutlined = function HistoryOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HistoryOutlinedSvg
  }));
};
HistoryOutlined.displayName = 'HistoryOutlined';
export default /*#__PURE__*/React.forwardRef(HistoryOutlined);