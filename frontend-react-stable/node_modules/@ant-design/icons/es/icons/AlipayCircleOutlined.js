import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON>payCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/AlipayCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var AlipayCircleOutlined = function AlipayCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlipayCircleOutlinedSvg
  }));
};
AlipayCircleOutlined.displayName = 'AlipayCircleOutlined';
export default /*#__PURE__*/React.forwardRef(AlipayCircleOutlined);