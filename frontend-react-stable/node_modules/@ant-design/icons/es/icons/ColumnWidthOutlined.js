import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ColumnWidthOutlinedSvg from "@ant-design/icons-svg/es/asn/ColumnWidthOutlined";
import AntdIcon from '../components/AntdIcon';
var ColumnWidthOutlined = function ColumnWidthOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ColumnWidthOutlinedSvg
  }));
};
ColumnWidthOutlined.displayName = 'ColumnWidthOutlined';
export default /*#__PURE__*/React.forwardRef(ColumnWidthOutlined);