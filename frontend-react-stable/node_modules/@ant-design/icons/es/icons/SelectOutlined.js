import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SelectOutlinedSvg from "@ant-design/icons-svg/es/asn/SelectOutlined";
import AntdIcon from '../components/AntdIcon';
var SelectOutlined = function SelectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SelectOutlinedSvg
  }));
};
SelectOutlined.displayName = 'SelectOutlined';
export default /*#__PURE__*/React.forwardRef(SelectOutlined);