import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodepenCircleFilledSvg from "@ant-design/icons-svg/es/asn/CodepenCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CodepenCircleFilled = function CodepenCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodepenCircleFilledSvg
  }));
};
CodepenCircleFilled.displayName = 'CodepenCircleFilled';
export default /*#__PURE__*/React.forwardRef(CodepenCircleFilled);