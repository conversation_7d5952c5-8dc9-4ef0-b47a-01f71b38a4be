import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DatabaseOutlinedSvg from "@ant-design/icons-svg/es/asn/DatabaseOutlined";
import AntdIcon from '../components/AntdIcon';
var DatabaseOutlined = function DatabaseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DatabaseOutlinedSvg
  }));
};
DatabaseOutlined.displayName = 'DatabaseOutlined';
export default /*#__PURE__*/React.forwardRef(DatabaseOutlined);