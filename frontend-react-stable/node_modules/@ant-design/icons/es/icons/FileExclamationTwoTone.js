import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExclamationTwoToneSvg from "@ant-design/icons-svg/es/asn/FileExclamationTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileExclamationTwoTone = function FileExclamationTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExclamationTwoToneSvg
  }));
};
FileExclamationTwoTone.displayName = 'FileExclamationTwoTone';
export default /*#__PURE__*/React.forwardRef(FileExclamationTwoTone);