import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownCircleFilledSvg from "@ant-design/icons-svg/es/asn/DownCircleFilled";
import AntdIcon from '../components/AntdIcon';
var DownCircleFilled = function DownCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownCircleFilledSvg
  }));
};
DownCircleFilled.displayName = 'DownCircleFilled';
export default /*#__PURE__*/React.forwardRef(DownCircleFilled);