import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagsOutlinedSvg from "@ant-design/icons-svg/es/asn/TagsOutlined";
import AntdIcon from '../components/AntdIcon';
var TagsOutlined = function TagsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagsOutlinedSvg
  }));
};
TagsOutlined.displayName = 'TagsOutlined';
export default /*#__PURE__*/React.forwardRef(TagsOutlined);