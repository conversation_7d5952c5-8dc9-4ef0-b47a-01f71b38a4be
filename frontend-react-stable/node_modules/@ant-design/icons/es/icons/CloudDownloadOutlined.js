import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudDownloadOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudDownloadOutlined";
import AntdIcon from '../components/AntdIcon';
var CloudDownloadOutlined = function CloudDownloadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudDownloadOutlinedSvg
  }));
};
CloudDownloadOutlined.displayName = 'CloudDownloadOutlined';
export default /*#__PURE__*/React.forwardRef(CloudDownloadOutlined);