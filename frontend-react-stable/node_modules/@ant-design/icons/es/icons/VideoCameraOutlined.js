import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Video<PERSON>ameraOutlinedSvg from "@ant-design/icons-svg/es/asn/VideoCameraOutlined";
import AntdIcon from '../components/AntdIcon';
var VideoCameraOutlined = function VideoCameraOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VideoCameraOutlinedSvg
  }));
};
VideoCameraOutlined.displayName = 'VideoCameraOutlined';
export default /*#__PURE__*/React.forwardRef(VideoCameraOutlined);