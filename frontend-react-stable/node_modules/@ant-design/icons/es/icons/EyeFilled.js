import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeFilledSvg from "@ant-design/icons-svg/es/asn/EyeFilled";
import AntdIcon from '../components/AntdIcon';
var EyeFilled = function EyeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeFilledSvg
  }));
};
EyeFilled.displayName = 'EyeFilled';
export default /*#__PURE__*/React.forwardRef(EyeFilled);