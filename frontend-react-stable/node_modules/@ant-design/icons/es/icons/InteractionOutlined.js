import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InteractionOutlinedSvg from "@ant-design/icons-svg/es/asn/InteractionOutlined";
import AntdIcon from '../components/AntdIcon';
var InteractionOutlined = function InteractionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InteractionOutlinedSvg
  }));
};
InteractionOutlined.displayName = 'InteractionOutlined';
export default /*#__PURE__*/React.forwardRef(InteractionOutlined);