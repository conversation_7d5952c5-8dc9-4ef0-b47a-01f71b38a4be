import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LinkedinOutlinedSvg from "@ant-design/icons-svg/es/asn/LinkedinOutlined";
import AntdIcon from '../components/AntdIcon';
var LinkedinOutlined = function LinkedinOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LinkedinOutlinedSvg
  }));
};
LinkedinOutlined.displayName = 'LinkedinOutlined';
export default /*#__PURE__*/React.forwardRef(LinkedinOutlined);