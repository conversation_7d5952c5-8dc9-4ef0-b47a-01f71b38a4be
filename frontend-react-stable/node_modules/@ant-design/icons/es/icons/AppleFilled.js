import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppleFilledSvg from "@ant-design/icons-svg/es/asn/AppleFilled";
import AntdIcon from '../components/AntdIcon';
var AppleFilled = function AppleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppleFilledSvg
  }));
};
AppleFilled.displayName = 'AppleFilled';
export default /*#__PURE__*/React.forwardRef(AppleFilled);