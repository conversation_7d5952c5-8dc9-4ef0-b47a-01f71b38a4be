import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HolderOutlinedSvg from "@ant-design/icons-svg/es/asn/HolderOutlined";
import AntdIcon from '../components/AntdIcon';
var HolderOutlined = function HolderOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HolderOutlinedSvg
  }));
};
HolderOutlined.displayName = 'HolderOutlined';
export default /*#__PURE__*/React.forwardRef(HolderOutlined);