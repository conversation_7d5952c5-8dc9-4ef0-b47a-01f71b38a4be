import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DiffFilledSvg from "@ant-design/icons-svg/es/asn/DiffFilled";
import AntdIcon from '../components/AntdIcon';
var DiffFilled = function DiffFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DiffFilledSvg
  }));
};
DiffFilled.displayName = 'DiffFilled';
export default /*#__PURE__*/React.forwardRef(DiffFilled);