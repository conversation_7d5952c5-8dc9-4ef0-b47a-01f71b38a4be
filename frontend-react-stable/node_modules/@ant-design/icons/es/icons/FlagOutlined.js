import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FlagOutlinedSvg from "@ant-design/icons-svg/es/asn/FlagOutlined";
import AntdIcon from '../components/AntdIcon';
var FlagOutlined = function FlagOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FlagOutlinedSvg
  }));
};
FlagOutlined.displayName = 'FlagOutlined';
export default /*#__PURE__*/React.forwardRef(FlagOutlined);