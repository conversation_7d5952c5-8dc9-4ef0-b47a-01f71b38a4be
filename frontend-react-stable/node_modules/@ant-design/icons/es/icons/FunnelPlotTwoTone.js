import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FunnelPlotTwoToneSvg from "@ant-design/icons-svg/es/asn/FunnelPlotTwoTone";
import AntdIcon from '../components/AntdIcon';
var FunnelPlotTwoTone = function FunnelPlotTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FunnelPlotTwoToneSvg
  }));
};
FunnelPlotTwoTone.displayName = 'FunnelPlotTwoTone';
export default /*#__PURE__*/React.forwardRef(FunnelPlotTwoTone);