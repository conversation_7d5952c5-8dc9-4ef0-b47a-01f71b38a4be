import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderAddOutlinedSvg from "@ant-design/icons-svg/es/asn/FolderAddOutlined";
import AntdIcon from '../components/AntdIcon';
var FolderAddOutlined = function FolderAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderAddOutlinedSvg
  }));
};
FolderAddOutlined.displayName = 'FolderAddOutlined';
export default /*#__PURE__*/React.forwardRef(FolderAddOutlined);