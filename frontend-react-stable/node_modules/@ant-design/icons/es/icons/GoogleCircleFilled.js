import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoogleCircleFilledSvg from "@ant-design/icons-svg/es/asn/GoogleCircleFilled";
import AntdIcon from '../components/AntdIcon';
var GoogleCircleFilled = function GoogleCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoogleCircleFilledSvg
  }));
};
GoogleCircleFilled.displayName = 'GoogleCircleFilled';
export default /*#__PURE__*/React.forwardRef(GoogleCircleFilled);