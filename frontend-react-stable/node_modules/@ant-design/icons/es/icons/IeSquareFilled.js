import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IeSquareFilledSvg from "@ant-design/icons-svg/es/asn/IeSquareFilled";
import AntdIcon from '../components/AntdIcon';
var IeSquareFilled = function IeSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IeSquareFilledSvg
  }));
};
IeSquareFilled.displayName = 'IeSquareFilled';
export default /*#__PURE__*/React.forwardRef(IeSquareFilled);