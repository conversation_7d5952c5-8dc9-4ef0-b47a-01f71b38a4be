import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyrightOutlinedSvg from "@ant-design/icons-svg/es/asn/CopyrightOutlined";
import AntdIcon from '../components/AntdIcon';
var CopyrightOutlined = function CopyrightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyrightOutlinedSvg
  }));
};
CopyrightOutlined.displayName = 'CopyrightOutlined';
export default /*#__PURE__*/React.forwardRef(CopyrightOutlined);