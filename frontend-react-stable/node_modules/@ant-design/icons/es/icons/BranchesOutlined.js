import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BranchesOutlinedSvg from "@ant-design/icons-svg/es/asn/BranchesOutlined";
import AntdIcon from '../components/AntdIcon';
var BranchesOutlined = function BranchesOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BranchesOutlinedSvg
  }));
};
BranchesOutlined.displayName = 'BranchesOutlined';
export default /*#__PURE__*/React.forwardRef(BranchesOutlined);