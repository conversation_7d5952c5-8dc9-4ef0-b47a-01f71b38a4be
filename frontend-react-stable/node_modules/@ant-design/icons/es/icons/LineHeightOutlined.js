import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LineHeightOutlinedSvg from "@ant-design/icons-svg/es/asn/LineHeightOutlined";
import AntdIcon from '../components/AntdIcon';
var LineHeightOutlined = function LineHeightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LineHeightOutlinedSvg
  }));
};
LineHeightOutlined.displayName = 'LineHeightOutlined';
export default /*#__PURE__*/React.forwardRef(LineHeightOutlined);