import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MediumCircleFilledSvg from "@ant-design/icons-svg/es/asn/MediumCircleFilled";
import AntdIcon from '../components/AntdIcon';
var MediumCircleFilled = function MediumCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MediumCircleFilledSvg
  }));
};
MediumCircleFilled.displayName = 'MediumCircleFilled';
export default /*#__PURE__*/React.forwardRef(MediumCircleFilled);