import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SmileFilledSvg from "@ant-design/icons-svg/es/asn/SmileFilled";
import AntdIcon from '../components/AntdIcon';
var SmileFilled = function SmileFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SmileFilledSvg
  }));
};
SmileFilled.displayName = 'SmileFilled';
export default /*#__PURE__*/React.forwardRef(SmileFilled);