import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeFilledSvg from "@ant-design/icons-svg/es/asn/CodeFilled";
import AntdIcon from '../components/AntdIcon';
var CodeFilled = function CodeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeFilledSvg
  }));
};
CodeFilled.displayName = 'CodeFilled';
export default /*#__PURE__*/React.forwardRef(CodeFilled);