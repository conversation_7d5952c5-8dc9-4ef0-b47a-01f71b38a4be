import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LaptopOutlinedSvg from "@ant-design/icons-svg/es/asn/LaptopOutlined";
import AntdIcon from '../components/AntdIcon';
var LaptopOutlined = function LaptopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LaptopOutlinedSvg
  }));
};
LaptopOutlined.displayName = 'LaptopOutlined';
export default /*#__PURE__*/React.forwardRef(LaptopOutlined);