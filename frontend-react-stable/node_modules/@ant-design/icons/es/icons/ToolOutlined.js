import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ToolOutlinedSvg from "@ant-design/icons-svg/es/asn/ToolOutlined";
import AntdIcon from '../components/AntdIcon';
var ToolOutlined = function ToolOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ToolOutlinedSvg
  }));
};
ToolOutlined.displayName = 'ToolOutlined';
export default /*#__PURE__*/React.forwardRef(ToolOutlined);