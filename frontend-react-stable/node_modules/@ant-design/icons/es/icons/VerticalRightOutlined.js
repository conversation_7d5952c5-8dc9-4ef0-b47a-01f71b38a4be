import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerticalRightOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalRightOutlined";
import AntdIcon from '../components/AntdIcon';
var VerticalRightOutlined = function VerticalRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerticalRightOutlinedSvg
  }));
};
VerticalRightOutlined.displayName = 'VerticalRightOutlined';
export default /*#__PURE__*/React.forwardRef(VerticalRightOutlined);