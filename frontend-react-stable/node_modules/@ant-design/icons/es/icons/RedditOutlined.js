import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedditOutlinedSvg from "@ant-design/icons-svg/es/asn/RedditOutlined";
import AntdIcon from '../components/AntdIcon';
var RedditOutlined = function RedditOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedditOutlinedSvg
  }));
};
RedditOutlined.displayName = 'RedditOutlined';
export default /*#__PURE__*/React.forwardRef(RedditOutlined);