import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CompressOutlinedSvg from "@ant-design/icons-svg/es/asn/CompressOutlined";
import AntdIcon from '../components/AntdIcon';
var CompressOutlined = function CompressOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CompressOutlinedSvg
  }));
};
CompressOutlined.displayName = 'CompressOutlined';
export default /*#__PURE__*/React.forwardRef(CompressOutlined);