import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CameraTwoToneSvg from "@ant-design/icons-svg/es/asn/CameraTwoTone";
import AntdIcon from '../components/AntdIcon';
var CameraTwoTone = function CameraTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CameraTwoToneSvg
  }));
};
CameraTwoTone.displayName = 'CameraTwoTone';
export default /*#__PURE__*/React.forwardRef(CameraTwoTone);