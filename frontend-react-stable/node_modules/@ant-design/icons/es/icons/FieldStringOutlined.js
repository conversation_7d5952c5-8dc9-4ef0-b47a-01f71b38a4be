import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FieldStringOutlinedSvg from "@ant-design/icons-svg/es/asn/FieldStringOutlined";
import AntdIcon from '../components/AntdIcon';
var FieldStringOutlined = function FieldStringOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FieldStringOutlinedSvg
  }));
};
FieldStringOutlined.displayName = 'FieldStringOutlined';
export default /*#__PURE__*/React.forwardRef(FieldStringOutlined);