import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PhoneOutlinedSvg from "@ant-design/icons-svg/es/asn/PhoneOutlined";
import AntdIcon from '../components/AntdIcon';
var PhoneOutlined = function PhoneOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PhoneOutlinedSvg
  }));
};
PhoneOutlined.displayName = 'PhoneOutlined';
export default /*#__PURE__*/React.forwardRef(PhoneOutlined);