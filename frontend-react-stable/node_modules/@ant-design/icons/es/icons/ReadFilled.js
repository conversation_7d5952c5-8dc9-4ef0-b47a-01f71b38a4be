import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReadFilledSvg from "@ant-design/icons-svg/es/asn/ReadFilled";
import AntdIcon from '../components/AntdIcon';
var ReadFilled = function ReadFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReadFilledSvg
  }));
};
ReadFilled.displayName = 'ReadFilled';
export default /*#__PURE__*/React.forwardRef(ReadFilled);