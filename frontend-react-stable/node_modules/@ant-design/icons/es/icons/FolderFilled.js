import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderFilledSvg from "@ant-design/icons-svg/es/asn/FolderFilled";
import AntdIcon from '../components/AntdIcon';
var FolderFilled = function FolderFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderFilledSvg
  }));
};
FolderFilled.displayName = 'FolderFilled';
export default /*#__PURE__*/React.forwardRef(FolderFilled);