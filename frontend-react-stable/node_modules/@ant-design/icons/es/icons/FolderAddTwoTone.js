import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderAddTwoToneSvg from "@ant-design/icons-svg/es/asn/FolderAddTwoTone";
import AntdIcon from '../components/AntdIcon';
var FolderAddTwoTone = function FolderAddTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderAddTwoToneSvg
  }));
};
FolderAddTwoTone.displayName = 'FolderAddTwoTone';
export default /*#__PURE__*/React.forwardRef(FolderAddTwoTone);