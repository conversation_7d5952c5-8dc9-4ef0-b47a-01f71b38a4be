import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DesktopOutlinedSvg from "@ant-design/icons-svg/es/asn/DesktopOutlined";
import AntdIcon from '../components/AntdIcon';
var DesktopOutlined = function DesktopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DesktopOutlinedSvg
  }));
};
DesktopOutlined.displayName = 'DesktopOutlined';
export default /*#__PURE__*/React.forwardRef(DesktopOutlined);