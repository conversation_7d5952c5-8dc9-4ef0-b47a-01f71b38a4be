import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderInnerOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderInnerOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderInnerOutlined = function BorderInnerOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderInnerOutlinedSvg
  }));
};
BorderInnerOutlined.displayName = 'BorderInnerOutlined';
export default /*#__PURE__*/React.forwardRef(BorderInnerOutlined);