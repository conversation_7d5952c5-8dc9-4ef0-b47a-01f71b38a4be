import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QuestionCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/QuestionCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var QuestionCircleOutlined = function QuestionCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QuestionCircleOutlinedSvg
  }));
};
QuestionCircleOutlined.displayName = 'QuestionCircleOutlined';
export default /*#__PURE__*/React.forwardRef(QuestionCircleOutlined);