import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlignLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/AlignLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var AlignLeftOutlined = function AlignLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlignLeftOutlinedSvg
  }));
};
AlignLeftOutlined.displayName = 'AlignLeftOutlined';
export default /*#__PURE__*/React.forwardRef(AlignLeftOutlined);