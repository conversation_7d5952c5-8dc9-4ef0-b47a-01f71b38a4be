import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalendarFilledSvg from "@ant-design/icons-svg/es/asn/CalendarFilled";
import AntdIcon from '../components/AntdIcon';
var CalendarFilled = function CalendarFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalendarFilledSvg
  }));
};
CalendarFilled.displayName = 'CalendarFilled';
export default /*#__PURE__*/React.forwardRef(CalendarFilled);