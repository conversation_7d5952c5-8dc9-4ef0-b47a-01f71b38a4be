import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DribbbleSquareFilledSvg from "@ant-design/icons-svg/es/asn/DribbbleSquareFilled";
import AntdIcon from '../components/AntdIcon';
var DribbbleSquareFilled = function DribbbleSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DribbbleSquareFilledSvg
  }));
};
DribbbleSquareFilled.displayName = 'DribbbleSquareFilled';
export default /*#__PURE__*/React.forwardRef(DribbbleSquareFilled);