import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BehanceOutlinedSvg from "@ant-design/icons-svg/es/asn/BehanceOutlined";
import AntdIcon from '../components/AntdIcon';
var BehanceOutlined = function BehanceOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BehanceOutlinedSvg
  }));
};
BehanceOutlined.displayName = 'BehanceOutlined';
export default /*#__PURE__*/React.forwardRef(BehanceOutlined);