import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeSandboxCircleFilledSvg from "@ant-design/icons-svg/es/asn/CodeSandboxCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeSandboxCircleFilledSvg
  }));
};
CodeSandboxCircleFilled.displayName = 'CodeSandboxCircleFilled';
export default /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);