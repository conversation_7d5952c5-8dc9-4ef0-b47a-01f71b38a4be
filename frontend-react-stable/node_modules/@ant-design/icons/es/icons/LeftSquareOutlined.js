import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var LeftSquareOutlined = function LeftSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftSquareOutlinedSvg
  }));
};
LeftSquareOutlined.displayName = 'LeftSquareOutlined';
export default /*#__PURE__*/React.forwardRef(LeftSquareOutlined);