import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FastForwardFilledSvg from "@ant-design/icons-svg/es/asn/FastForwardFilled";
import AntdIcon from '../components/AntdIcon';
var FastForwardFilled = function FastForwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FastForwardFilledSvg
  }));
};
FastForwardFilled.displayName = 'FastForwardFilled';
export default /*#__PURE__*/React.forwardRef(FastForwardFilled);