import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LineOutlinedSvg from "@ant-design/icons-svg/es/asn/LineOutlined";
import AntdIcon from '../components/AntdIcon';
var LineOutlined = function LineOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LineOutlinedSvg
  }));
};
LineOutlined.displayName = 'LineOutlined';
export default /*#__PURE__*/React.forwardRef(LineOutlined);