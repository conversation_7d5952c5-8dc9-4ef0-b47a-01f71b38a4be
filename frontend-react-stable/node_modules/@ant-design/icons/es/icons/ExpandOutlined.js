import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExpandOutlinedSvg from "@ant-design/icons-svg/es/asn/ExpandOutlined";
import AntdIcon from '../components/AntdIcon';
var ExpandOutlined = function ExpandOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExpandOutlinedSvg
  }));
};
ExpandOutlined.displayName = 'ExpandOutlined';
export default /*#__PURE__*/React.forwardRef(ExpandOutlined);