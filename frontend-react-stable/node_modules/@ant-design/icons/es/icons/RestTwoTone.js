import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RestTwoToneSvg from "@ant-design/icons-svg/es/asn/RestTwoTone";
import AntdIcon from '../components/AntdIcon';
var RestTwoTone = function RestTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RestTwoToneSvg
  }));
};
RestTwoTone.displayName = 'RestTwoTone';
export default /*#__PURE__*/React.forwardRef(RestTwoTone);