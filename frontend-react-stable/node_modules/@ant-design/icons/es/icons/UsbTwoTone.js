import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UsbTwoToneSvg from "@ant-design/icons-svg/es/asn/UsbTwoTone";
import AntdIcon from '../components/AntdIcon';
var UsbTwoTone = function UsbTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UsbTwoToneSvg
  }));
};
UsbTwoTone.displayName = 'UsbTwoTone';
export default /*#__PURE__*/React.forwardRef(UsbTwoTone);