import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeInvisibleOutlinedSvg from "@ant-design/icons-svg/es/asn/EyeInvisibleOutlined";
import AntdIcon from '../components/AntdIcon';
var EyeInvisibleOutlined = function EyeInvisibleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeInvisibleOutlinedSvg
  }));
};
EyeInvisibleOutlined.displayName = 'EyeInvisibleOutlined';
export default /*#__PURE__*/React.forwardRef(EyeInvisibleOutlined);