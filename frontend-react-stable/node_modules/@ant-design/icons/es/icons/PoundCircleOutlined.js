import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PoundCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PoundCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PoundCircleOutlined = function PoundCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PoundCircleOutlinedSvg
  }));
};
PoundCircleOutlined.displayName = 'PoundCircleOutlined';
export default /*#__PURE__*/React.forwardRef(PoundCircleOutlined);