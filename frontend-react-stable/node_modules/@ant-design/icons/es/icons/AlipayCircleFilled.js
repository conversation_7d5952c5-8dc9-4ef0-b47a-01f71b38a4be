import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlipayCircleFilledSvg from "@ant-design/icons-svg/es/asn/AlipayCircleFilled";
import AntdIcon from '../components/AntdIcon';
var AlipayCircleFilled = function AlipayCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlipayCircleFilledSvg
  }));
};
AlipayCircleFilled.displayName = 'AlipayCircleFilled';
export default /*#__PURE__*/React.forwardRef(AlipayCircleFilled);