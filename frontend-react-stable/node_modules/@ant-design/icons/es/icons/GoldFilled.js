import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoldFilledSvg from "@ant-design/icons-svg/es/asn/GoldFilled";
import AntdIcon from '../components/AntdIcon';
var GoldFilled = function GoldFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoldFilledSvg
  }));
};
GoldFilled.displayName = 'GoldFilled';
export default /*#__PURE__*/React.forwardRef(GoldFilled);