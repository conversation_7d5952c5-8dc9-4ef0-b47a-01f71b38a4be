import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NodeIndexOutlinedSvg from "@ant-design/icons-svg/es/asn/NodeIndexOutlined";
import AntdIcon from '../components/AntdIcon';
var NodeIndexOutlined = function NodeIndexOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NodeIndexOutlinedSvg
  }));
};
NodeIndexOutlined.displayName = 'NodeIndexOutlined';
export default /*#__PURE__*/React.forwardRef(NodeIndexOutlined);