import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WeiboCircleFilledSvg from "@ant-design/icons-svg/es/asn/WeiboCircleFilled";
import AntdIcon from '../components/AntdIcon';
var WeiboCircleFilled = function WeiboCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WeiboCircleFilledSvg
  }));
};
WeiboCircleFilled.displayName = 'WeiboCircleFilled';
export default /*#__PURE__*/React.forwardRef(WeiboCircleFilled);