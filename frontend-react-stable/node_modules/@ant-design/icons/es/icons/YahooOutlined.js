import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YahooOutlinedSvg from "@ant-design/icons-svg/es/asn/YahooOutlined";
import AntdIcon from '../components/AntdIcon';
var YahooOutlined = function YahooOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YahooOutlinedSvg
  }));
};
YahooOutlined.displayName = 'YahooOutlined';
export default /*#__PURE__*/React.forwardRef(YahooOutlined);