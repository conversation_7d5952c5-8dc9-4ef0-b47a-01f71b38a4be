import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DiffTwoToneSvg from "@ant-design/icons-svg/es/asn/DiffTwoTone";
import AntdIcon from '../components/AntdIcon';
var DiffTwoTone = function DiffTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DiffTwoToneSvg
  }));
};
DiffTwoTone.displayName = 'DiffTwoTone';
export default /*#__PURE__*/React.forwardRef(DiffTwoTone);