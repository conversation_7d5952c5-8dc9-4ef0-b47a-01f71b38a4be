import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseOutlined";
import AntdIcon from '../components/AntdIcon';
var CloseOutlined = function CloseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseOutlinedSvg
  }));
};
CloseOutlined.displayName = 'CloseOutlined';
export default /*#__PURE__*/React.forwardRef(CloseOutlined);