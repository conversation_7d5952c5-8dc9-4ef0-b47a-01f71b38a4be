import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DingtalkSquareFilledSvg from "@ant-design/icons-svg/es/asn/DingtalkSquareFilled";
import AntdIcon from '../components/AntdIcon';
var DingtalkSquareFilled = function DingtalkSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DingtalkSquareFilledSvg
  }));
};
DingtalkSquareFilled.displayName = 'DingtalkSquareFilled';
export default /*#__PURE__*/React.forwardRef(DingtalkSquareFilled);