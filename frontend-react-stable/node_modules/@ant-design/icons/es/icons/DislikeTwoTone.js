import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DislikeTwoToneSvg from "@ant-design/icons-svg/es/asn/DislikeTwoTone";
import AntdIcon from '../components/AntdIcon';
var DislikeTwoTone = function DislikeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DislikeTwoToneSvg
  }));
};
DislikeTwoTone.displayName = 'DislikeTwoTone';
export default /*#__PURE__*/React.forwardRef(DislikeTwoTone);