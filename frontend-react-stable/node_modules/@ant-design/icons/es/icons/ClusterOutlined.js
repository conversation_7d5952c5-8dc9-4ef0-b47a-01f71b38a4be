import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ClusterOutlinedSvg from "@ant-design/icons-svg/es/asn/ClusterOutlined";
import AntdIcon from '../components/AntdIcon';
var ClusterOutlined = function ClusterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ClusterOutlinedSvg
  }));
};
ClusterOutlined.displayName = 'ClusterOutlined';
export default /*#__PURE__*/React.forwardRef(ClusterOutlined);