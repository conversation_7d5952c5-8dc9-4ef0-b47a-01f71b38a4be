import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BugFilledSvg from "@ant-design/icons-svg/es/asn/BugFilled";
import AntdIcon from '../components/AntdIcon';
var BugFilled = function BugFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BugFilledSvg
  }));
};
BugFilled.displayName = 'BugFilled';
export default /*#__PURE__*/React.forwardRef(BugFilled);