import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePptTwoToneSvg from "@ant-design/icons-svg/es/asn/FilePptTwoTone";
import AntdIcon from '../components/AntdIcon';
var FilePptTwoTone = function FilePptTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePptTwoToneSvg
  }));
};
FilePptTwoTone.displayName = 'FilePptTwoTone';
export default /*#__PURE__*/React.forwardRef(FilePptTwoTone);