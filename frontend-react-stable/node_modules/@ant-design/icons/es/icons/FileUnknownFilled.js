import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileUnknownFilledSvg from "@ant-design/icons-svg/es/asn/FileUnknownFilled";
import AntdIcon from '../components/AntdIcon';
var FileUnknownFilled = function FileUnknownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileUnknownFilledSvg
  }));
};
FileUnknownFilled.displayName = 'FileUnknownFilled';
export default /*#__PURE__*/React.forwardRef(FileUnknownFilled);