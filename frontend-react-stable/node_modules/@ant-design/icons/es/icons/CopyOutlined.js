import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyOutlinedSvg from "@ant-design/icons-svg/es/asn/CopyOutlined";
import AntdIcon from '../components/AntdIcon';
var CopyOutlined = function CopyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyOutlinedSvg
  }));
};
CopyOutlined.displayName = 'CopyOutlined';
export default /*#__PURE__*/React.forwardRef(CopyOutlined);