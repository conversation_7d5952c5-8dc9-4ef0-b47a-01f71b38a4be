import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SmallDashOutlinedSvg from "@ant-design/icons-svg/es/asn/SmallDashOutlined";
import AntdIcon from '../components/AntdIcon';
var SmallDashOutlined = function SmallDashOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SmallDashOutlinedSvg
  }));
};
SmallDashOutlined.displayName = 'SmallDashOutlined';
export default /*#__PURE__*/React.forwardRef(SmallDashOutlined);