import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UsbFilledSvg from "@ant-design/icons-svg/es/asn/UsbFilled";
import AntdIcon from '../components/AntdIcon';
var UsbFilled = function UsbFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UsbFilledSvg
  }));
};
UsbFilled.displayName = 'UsbFilled';
export default /*#__PURE__*/React.forwardRef(UsbFilled);