import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ToolFilledSvg from "@ant-design/icons-svg/es/asn/ToolFilled";
import AntdIcon from '../components/AntdIcon';
var ToolFilled = function ToolFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ToolFilledSvg
  }));
};
ToolFilled.displayName = 'ToolFilled';
export default /*#__PURE__*/React.forwardRef(ToolFilled);