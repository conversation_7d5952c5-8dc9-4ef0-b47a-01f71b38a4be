import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsertRowBelowOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowBelowOutlined";
import AntdIcon from '../components/AntdIcon';
var InsertRowBelowOutlined = function InsertRowBelowOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsertRowBelowOutlinedSvg
  }));
};
InsertRowBelowOutlined.displayName = 'InsertRowBelowOutlined';
export default /*#__PURE__*/React.forwardRef(InsertRowBelowOutlined);