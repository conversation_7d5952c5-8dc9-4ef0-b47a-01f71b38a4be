import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ControlFilledSvg from "@ant-design/icons-svg/es/asn/ControlFilled";
import AntdIcon from '../components/AntdIcon';
var ControlFilled = function ControlFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ControlFilledSvg
  }));
};
ControlFilled.displayName = 'ControlFilled';
export default /*#__PURE__*/React.forwardRef(ControlFilled);