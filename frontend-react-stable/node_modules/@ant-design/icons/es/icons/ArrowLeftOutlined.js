import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ArrowLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/ArrowLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var ArrowLeftOutlined = function ArrowLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ArrowLeftOutlinedSvg
  }));
};
ArrowLeftOutlined.displayName = 'ArrowLeftOutlined';
export default /*#__PURE__*/React.forwardRef(ArrowLeftOutlined);