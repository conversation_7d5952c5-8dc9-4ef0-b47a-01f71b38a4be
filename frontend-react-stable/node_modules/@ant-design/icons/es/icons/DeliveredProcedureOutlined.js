import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeliveredProcedureOutlinedSvg from "@ant-design/icons-svg/es/asn/DeliveredProcedureOutlined";
import AntdIcon from '../components/AntdIcon';
var DeliveredProcedureOutlined = function DeliveredProcedureOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeliveredProcedureOutlinedSvg
  }));
};
DeliveredProcedureOutlined.displayName = 'DeliveredProcedureOutlined';
export default /*#__PURE__*/React.forwardRef(DeliveredProcedureOutlined);