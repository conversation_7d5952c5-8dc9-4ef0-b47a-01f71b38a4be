import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProfileTwoToneSvg from "@ant-design/icons-svg/es/asn/ProfileTwoTone";
import AntdIcon from '../components/AntdIcon';
var ProfileTwoTone = function ProfileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ProfileTwoToneSvg
  }));
};
ProfileTwoTone.displayName = 'ProfileTwoTone';
export default /*#__PURE__*/React.forwardRef(ProfileTwoTone);