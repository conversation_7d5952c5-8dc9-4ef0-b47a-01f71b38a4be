import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalendarTwoToneSvg from "@ant-design/icons-svg/es/asn/CalendarTwoTone";
import AntdIcon from '../components/AntdIcon';
var CalendarTwoTone = function CalendarTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalendarTwoToneSvg
  }));
};
CalendarTwoTone.displayName = 'CalendarTwoTone';
export default /*#__PURE__*/React.forwardRef(CalendarTwoTone);