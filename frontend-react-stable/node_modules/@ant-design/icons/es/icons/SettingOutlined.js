import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SettingOutlinedSvg from "@ant-design/icons-svg/es/asn/SettingOutlined";
import AntdIcon from '../components/AntdIcon';
var SettingOutlined = function SettingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SettingOutlinedSvg
  }));
};
SettingOutlined.displayName = 'SettingOutlined';
export default /*#__PURE__*/React.forwardRef(SettingOutlined);