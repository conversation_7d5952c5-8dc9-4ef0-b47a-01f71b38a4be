import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiCircleFilledSvg from "@ant-design/icons-svg/es/asn/CiCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CiCircleFilled = function CiCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiCircleFilledSvg
  }));
};
CiCircleFilled.displayName = 'CiCircleFilled';
export default /*#__PURE__*/React.forwardRef(CiCircleFilled);