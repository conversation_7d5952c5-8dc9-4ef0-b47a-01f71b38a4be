import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackSquareFilledSvg from "@ant-design/icons-svg/es/asn/SlackSquareFilled";
import AntdIcon from '../components/AntdIcon';
var SlackSquareFilled = function SlackSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackSquareFilledSvg
  }));
};
SlackSquareFilled.displayName = 'SlackSquareFilled';
export default /*#__PURE__*/React.forwardRef(SlackSquareFilled);