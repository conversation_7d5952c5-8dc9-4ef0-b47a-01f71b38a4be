import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BulbFilledSvg from "@ant-design/icons-svg/es/asn/BulbFilled";
import AntdIcon from '../components/AntdIcon';
var BulbFilled = function BulbFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BulbFilledSvg
  }));
};
BulbFilled.displayName = 'BulbFilled';
export default /*#__PURE__*/React.forwardRef(BulbFilled);