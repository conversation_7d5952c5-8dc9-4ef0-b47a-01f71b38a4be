import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WhatsAppOutlinedSvg from "@ant-design/icons-svg/es/asn/WhatsAppOutlined";
import AntdIcon from '../components/AntdIcon';
var WhatsAppOutlined = function WhatsAppOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WhatsAppOutlinedSvg
  }));
};
WhatsAppOutlined.displayName = 'WhatsAppOutlined';
export default /*#__PURE__*/React.forwardRef(WhatsAppOutlined);