import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MedicineBoxTwoToneSvg from "@ant-design/icons-svg/es/asn/MedicineBoxTwoTone";
import AntdIcon from '../components/AntdIcon';
var MedicineBoxTwoTone = function MedicineBoxTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MedicineBoxTwoToneSvg
  }));
};
MedicineBoxTwoTone.displayName = 'MedicineBoxTwoTone';
export default /*#__PURE__*/React.forwardRef(MedicineBoxTwoTone);