import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/CloseCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var CloseCircleTwoTone = function CloseCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseCircleTwoToneSvg
  }));
};
CloseCircleTwoTone.displayName = 'CloseCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(CloseCircleTwoTone);