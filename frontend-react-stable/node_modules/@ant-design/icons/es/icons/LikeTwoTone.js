import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LikeTwoToneSvg from "@ant-design/icons-svg/es/asn/LikeTwoTone";
import AntdIcon from '../components/AntdIcon';
var LikeTwoTone = function LikeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LikeTwoToneSvg
  }));
};
LikeTwoTone.displayName = 'LikeTwoTone';
export default /*#__PURE__*/React.forwardRef(LikeTwoTone);