import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePptFilledSvg from "@ant-design/icons-svg/es/asn/FilePptFilled";
import AntdIcon from '../components/AntdIcon';
var FilePptFilled = function FilePptFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePptFilledSvg
  }));
};
FilePptFilled.displayName = 'FilePptFilled';
export default /*#__PURE__*/React.forwardRef(FilePptFilled);