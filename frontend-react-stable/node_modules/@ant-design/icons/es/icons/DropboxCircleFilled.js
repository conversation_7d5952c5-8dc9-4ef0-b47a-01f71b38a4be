import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DropboxCircleFilledSvg from "@ant-design/icons-svg/es/asn/DropboxCircleFilled";
import AntdIcon from '../components/AntdIcon';
var DropboxCircleFilled = function DropboxCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DropboxCircleFilledSvg
  }));
};
DropboxCircleFilled.displayName = 'DropboxCircleFilled';
export default /*#__PURE__*/React.forwardRef(DropboxCircleFilled);