import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RotateRightOutlinedSvg from "@ant-design/icons-svg/es/asn/RotateRightOutlined";
import AntdIcon from '../components/AntdIcon';
var RotateRightOutlined = function RotateRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RotateRightOutlinedSvg
  }));
};
RotateRightOutlined.displayName = 'RotateRightOutlined';
export default /*#__PURE__*/React.forwardRef(RotateRightOutlined);