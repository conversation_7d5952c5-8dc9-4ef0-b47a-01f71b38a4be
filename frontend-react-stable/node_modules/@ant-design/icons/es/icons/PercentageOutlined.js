import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PercentageOutlinedSvg from "@ant-design/icons-svg/es/asn/PercentageOutlined";
import AntdIcon from '../components/AntdIcon';
var PercentageOutlined = function PercentageOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PercentageOutlinedSvg
  }));
};
PercentageOutlined.displayName = 'PercentageOutlined';
export default /*#__PURE__*/React.forwardRef(PercentageOutlined);