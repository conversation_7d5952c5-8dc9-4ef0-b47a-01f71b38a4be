import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TwitterOutlinedSvg from "@ant-design/icons-svg/es/asn/TwitterOutlined";
import AntdIcon from '../components/AntdIcon';
var TwitterOutlined = function TwitterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TwitterOutlinedSvg
  }));
};
TwitterOutlined.displayName = 'TwitterOutlined';
export default /*#__PURE__*/React.forwardRef(TwitterOutlined);