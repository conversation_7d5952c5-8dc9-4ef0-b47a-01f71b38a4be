import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BehanceSquareFilledSvg from "@ant-design/icons-svg/es/asn/BehanceSquareFilled";
import AntdIcon from '../components/AntdIcon';
var BehanceSquareFilled = function BehanceSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BehanceSquareFilledSvg
  }));
};
BehanceSquareFilled.displayName = 'BehanceSquareFilled';
export default /*#__PURE__*/React.forwardRef(BehanceSquareFilled);