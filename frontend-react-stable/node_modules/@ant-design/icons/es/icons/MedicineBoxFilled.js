import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MedicineBoxFilledSvg from "@ant-design/icons-svg/es/asn/MedicineBoxFilled";
import AntdIcon from '../components/AntdIcon';
var MedicineBoxFilled = function MedicineBoxFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MedicineBoxFilledSvg
  }));
};
MedicineBoxFilled.displayName = 'MedicineBoxFilled';
export default /*#__PURE__*/React.forwardRef(MedicineBoxFilled);