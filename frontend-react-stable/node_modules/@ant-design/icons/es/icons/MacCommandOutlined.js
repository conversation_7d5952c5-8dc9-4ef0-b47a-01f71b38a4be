import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON><PERSON>ommandOutlinedSvg from "@ant-design/icons-svg/es/asn/MacCommandOutlined";
import AntdIcon from '../components/AntdIcon';
var MacCommandOutlined = function MacCommandOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MacCommandOutlinedSvg
  }));
};
MacCommandOutlined.displayName = 'MacCommandOutlined';
export default /*#__PURE__*/React.forwardRef(MacCommandOutlined);