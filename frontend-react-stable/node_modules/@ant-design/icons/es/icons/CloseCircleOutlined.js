import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CloseCircleOutlined = function CloseCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseCircleOutlinedSvg
  }));
};
CloseCircleOutlined.displayName = 'CloseCircleOutlined';
export default /*#__PURE__*/React.forwardRef(CloseCircleOutlined);