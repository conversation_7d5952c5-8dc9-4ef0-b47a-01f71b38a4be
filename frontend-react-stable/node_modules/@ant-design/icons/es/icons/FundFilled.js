import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FundFilledSvg from "@ant-design/icons-svg/es/asn/FundFilled";
import AntdIcon from '../components/AntdIcon';
var FundFilled = function FundFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FundFilledSvg
  }));
};
FundFilled.displayName = 'FundFilled';
export default /*#__PURE__*/React.forwardRef(FundFilled);