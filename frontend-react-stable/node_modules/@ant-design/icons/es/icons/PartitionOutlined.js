import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PartitionOutlinedSvg from "@ant-design/icons-svg/es/asn/PartitionOutlined";
import AntdIcon from '../components/AntdIcon';
var PartitionOutlined = function PartitionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PartitionOutlinedSvg
  }));
};
PartitionOutlined.displayName = 'PartitionOutlined';
export default /*#__PURE__*/React.forwardRef(PartitionOutlined);