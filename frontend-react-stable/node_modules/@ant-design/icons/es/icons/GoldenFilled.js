import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoldenFilledSvg from "@ant-design/icons-svg/es/asn/GoldenFilled";
import AntdIcon from '../components/AntdIcon';
var GoldenFilled = function GoldenFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoldenFilledSvg
  }));
};
GoldenFilled.displayName = 'GoldenFilled';
export default /*#__PURE__*/React.forwardRef(GoldenFilled);