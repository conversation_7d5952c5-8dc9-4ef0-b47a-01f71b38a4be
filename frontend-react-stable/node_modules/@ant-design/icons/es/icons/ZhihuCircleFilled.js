import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ZhihuCircleFilledSvg from "@ant-design/icons-svg/es/asn/ZhihuCircleFilled";
import AntdIcon from '../components/AntdIcon';
var ZhihuCircleFilled = function ZhihuCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ZhihuCircleFilledSvg
  }));
};
ZhihuCircleFilled.displayName = 'ZhihuCircleFilled';
export default /*#__PURE__*/React.forwardRef(ZhihuCircleFilled);