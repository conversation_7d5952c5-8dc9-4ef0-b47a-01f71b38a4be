import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UserOutlinedSvg from "@ant-design/icons-svg/es/asn/UserOutlined";
import AntdIcon from '../components/AntdIcon';
var UserOutlined = function UserOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UserOutlinedSvg
  }));
};
UserOutlined.displayName = 'UserOutlined';
export default /*#__PURE__*/React.forwardRef(UserOutlined);