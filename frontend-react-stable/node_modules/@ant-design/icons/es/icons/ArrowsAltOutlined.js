import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ArrowsAltOutlinedSvg from "@ant-design/icons-svg/es/asn/ArrowsAltOutlined";
import AntdIcon from '../components/AntdIcon';
var ArrowsAltOutlined = function ArrowsAltOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ArrowsAltOutlinedSvg
  }));
};
ArrowsAltOutlined.displayName = 'ArrowsAltOutlined';
export default /*#__PURE__*/React.forwardRef(ArrowsAltOutlined);