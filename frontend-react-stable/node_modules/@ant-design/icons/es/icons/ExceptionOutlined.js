import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExceptionOutlinedSvg from "@ant-design/icons-svg/es/asn/ExceptionOutlined";
import AntdIcon from '../components/AntdIcon';
var ExceptionOutlined = function ExceptionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExceptionOutlinedSvg
  }));
};
ExceptionOutlined.displayName = 'ExceptionOutlined';
export default /*#__PURE__*/React.forwardRef(ExceptionOutlined);