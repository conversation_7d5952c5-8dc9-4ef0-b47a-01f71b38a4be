import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StepForwardOutlinedSvg from "@ant-design/icons-svg/es/asn/StepForwardOutlined";
import AntdIcon from '../components/AntdIcon';
var StepForwardOutlined = function StepForwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StepForwardOutlinedSvg
  }));
};
StepForwardOutlined.displayName = 'StepForwardOutlined';
export default /*#__PURE__*/React.forwardRef(StepForwardOutlined);