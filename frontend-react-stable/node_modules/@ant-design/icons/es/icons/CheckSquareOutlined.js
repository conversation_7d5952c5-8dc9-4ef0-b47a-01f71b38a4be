import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/CheckSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var CheckSquareOutlined = function CheckSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckSquareOutlinedSvg
  }));
};
CheckSquareOutlined.displayName = 'CheckSquareOutlined';
export default /*#__PURE__*/React.forwardRef(CheckSquareOutlined);