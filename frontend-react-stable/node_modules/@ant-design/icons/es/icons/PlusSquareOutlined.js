import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/PlusSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var PlusSquareOutlined = function PlusSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlusSquareOutlinedSvg
  }));
};
PlusSquareOutlined.displayName = 'PlusSquareOutlined';
export default /*#__PURE__*/React.forwardRef(PlusSquareOutlined);