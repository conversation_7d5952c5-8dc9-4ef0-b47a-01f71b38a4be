import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StarFilledSvg from "@ant-design/icons-svg/es/asn/StarFilled";
import AntdIcon from '../components/AntdIcon';
var StarFilled = function StarFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StarFilledSvg
  }));
};
StarFilled.displayName = 'StarFilled';
export default /*#__PURE__*/React.forwardRef(StarFilled);