import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ClockCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/ClockCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var ClockCircleOutlined = function ClockCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ClockCircleOutlinedSvg
  }));
};
ClockCircleOutlined.displayName = 'ClockCircleOutlined';
export default /*#__PURE__*/React.forwardRef(ClockCircleOutlined);