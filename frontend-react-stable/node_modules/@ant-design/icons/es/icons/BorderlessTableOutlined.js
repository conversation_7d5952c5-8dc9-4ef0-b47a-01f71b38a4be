import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderlessTableOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderlessTableOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderlessTableOutlined = function BorderlessTableOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderlessTableOutlinedSvg
  }));
};
BorderlessTableOutlined.displayName = 'BorderlessTableOutlined';
export default /*#__PURE__*/React.forwardRef(BorderlessTableOutlined);