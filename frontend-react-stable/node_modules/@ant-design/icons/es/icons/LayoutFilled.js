import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LayoutFilledSvg from "@ant-design/icons-svg/es/asn/LayoutFilled";
import AntdIcon from '../components/AntdIcon';
var LayoutFilled = function LayoutFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LayoutFilledSvg
  }));
};
LayoutFilled.displayName = 'LayoutFilled';
export default /*#__PURE__*/React.forwardRef(LayoutFilled);