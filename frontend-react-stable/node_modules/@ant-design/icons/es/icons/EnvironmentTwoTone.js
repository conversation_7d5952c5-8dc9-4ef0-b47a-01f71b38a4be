import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EnvironmentTwoToneSvg from "@ant-design/icons-svg/es/asn/EnvironmentTwoTone";
import AntdIcon from '../components/AntdIcon';
var EnvironmentTwoTone = function EnvironmentTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EnvironmentTwoToneSvg
  }));
};
EnvironmentTwoTone.displayName = 'EnvironmentTwoTone';
export default /*#__PURE__*/React.forwardRef(EnvironmentTwoTone);