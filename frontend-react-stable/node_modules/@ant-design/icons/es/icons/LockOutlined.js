import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LockOutlinedSvg from "@ant-design/icons-svg/es/asn/LockOutlined";
import AntdIcon from '../components/AntdIcon';
var LockOutlined = function LockOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LockOutlinedSvg
  }));
};
LockOutlined.displayName = 'LockOutlined';
export default /*#__PURE__*/React.forwardRef(LockOutlined);