import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BackwardFilledSvg from "@ant-design/icons-svg/es/asn/BackwardFilled";
import AntdIcon from '../components/AntdIcon';
var BackwardFilled = function BackwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BackwardFilledSvg
  }));
};
BackwardFilled.displayName = 'BackwardFilled';
export default /*#__PURE__*/React.forwardRef(BackwardFilled);