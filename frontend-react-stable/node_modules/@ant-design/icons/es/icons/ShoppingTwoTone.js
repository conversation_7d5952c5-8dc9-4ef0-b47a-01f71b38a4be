import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingTwoToneSvg from "@ant-design/icons-svg/es/asn/ShoppingTwoTone";
import AntdIcon from '../components/AntdIcon';
var ShoppingTwoTone = function ShoppingTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingTwoToneSvg
  }));
};
ShoppingTwoTone.displayName = 'ShoppingTwoTone';
export default /*#__PURE__*/React.forwardRef(ShoppingTwoTone);