import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MenuUnfoldOutlinedSvg from "@ant-design/icons-svg/es/asn/MenuUnfoldOutlined";
import AntdIcon from '../components/AntdIcon';
var MenuUnfoldOutlined = function MenuUnfoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MenuUnfoldOutlinedSvg
  }));
};
MenuUnfoldOutlined.displayName = 'MenuUnfoldOutlined';
export default /*#__PURE__*/React.forwardRef(MenuUnfoldOutlined);