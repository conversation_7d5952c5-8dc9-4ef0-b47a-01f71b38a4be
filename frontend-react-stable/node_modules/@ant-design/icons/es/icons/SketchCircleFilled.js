import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SketchCircleFilledSvg from "@ant-design/icons-svg/es/asn/SketchCircleFilled";
import AntdIcon from '../components/AntdIcon';
var SketchCircleFilled = function SketchCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SketchCircleFilledSvg
  }));
};
SketchCircleFilled.displayName = 'SketchCircleFilled';
export default /*#__PURE__*/React.forwardRef(SketchCircleFilled);