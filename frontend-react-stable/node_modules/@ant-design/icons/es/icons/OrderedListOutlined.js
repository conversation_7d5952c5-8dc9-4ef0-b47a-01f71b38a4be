import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import OrderedListOutlinedSvg from "@ant-design/icons-svg/es/asn/OrderedListOutlined";
import AntdIcon from '../components/AntdIcon';
var OrderedListOutlined = function OrderedListOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: OrderedListOutlinedSvg
  }));
};
OrderedListOutlined.displayName = 'OrderedListOutlined';
export default /*#__PURE__*/React.forwardRef(OrderedListOutlined);