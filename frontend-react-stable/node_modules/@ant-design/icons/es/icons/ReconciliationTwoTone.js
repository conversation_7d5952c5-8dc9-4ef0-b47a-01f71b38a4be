import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReconciliationTwoToneSvg from "@ant-design/icons-svg/es/asn/ReconciliationTwoTone";
import AntdIcon from '../components/AntdIcon';
var ReconciliationTwoTone = function ReconciliationTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReconciliationTwoToneSvg
  }));
};
ReconciliationTwoTone.displayName = 'ReconciliationTwoTone';
export default /*#__PURE__*/React.forwardRef(ReconciliationTwoTone);