import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrademarkCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/TrademarkCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var TrademarkCircleTwoTone = function TrademarkCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrademarkCircleTwoToneSvg
  }));
};
TrademarkCircleTwoTone.displayName = 'TrademarkCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(TrademarkCircleTwoTone);