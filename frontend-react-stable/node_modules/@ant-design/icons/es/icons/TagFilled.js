import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagFilledSvg from "@ant-design/icons-svg/es/asn/TagFilled";
import AntdIcon from '../components/AntdIcon';
var TagFilled = function TagFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagFilledSvg
  }));
};
TagFilled.displayName = 'TagFilled';
export default /*#__PURE__*/React.forwardRef(TagFilled);