import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DingtalkCircleFilledSvg from "@ant-design/icons-svg/es/asn/DingtalkCircleFilled";
import AntdIcon from '../components/AntdIcon';
var DingtalkCircleFilled = function DingtalkCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DingtalkCircleFilledSvg
  }));
};
DingtalkCircleFilled.displayName = 'DingtalkCircleFilled';
export default /*#__PURE__*/React.forwardRef(DingtalkCircleFilled);