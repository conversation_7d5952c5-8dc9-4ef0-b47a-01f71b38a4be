import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwitcherTwoToneSvg from "@ant-design/icons-svg/es/asn/SwitcherTwoTone";
import AntdIcon from '../components/AntdIcon';
var SwitcherTwoTone = function SwitcherTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwitcherTwoToneSvg
  }));
};
SwitcherTwoTone.displayName = 'SwitcherTwoTone';
export default /*#__PURE__*/React.forwardRef(SwitcherTwoTone);