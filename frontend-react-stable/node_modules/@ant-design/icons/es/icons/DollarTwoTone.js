import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DollarTwoToneSvg from "@ant-design/icons-svg/es/asn/DollarTwoTone";
import AntdIcon from '../components/AntdIcon';
var DollarTwoTone = function DollarTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DollarTwoToneSvg
  }));
};
DollarTwoTone.displayName = 'DollarTwoTone';
export default /*#__PURE__*/React.forwardRef(DollarTwoTone);