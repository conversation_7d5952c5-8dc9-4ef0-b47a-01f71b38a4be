import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YoutubeFilledSvg from "@ant-design/icons-svg/es/asn/YoutubeFilled";
import AntdIcon from '../components/AntdIcon';
var YoutubeFilled = function YoutubeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YoutubeFilledSvg
  }));
};
YoutubeFilled.displayName = 'YoutubeFilled';
export default /*#__PURE__*/React.forwardRef(YoutubeFilled);