import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MergeCellsOutlinedSvg from "@ant-design/icons-svg/es/asn/MergeCellsOutlined";
import AntdIcon from '../components/AntdIcon';
var MergeCellsOutlined = function MergeCellsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MergeCellsOutlinedSvg
  }));
};
MergeCellsOutlined.displayName = 'MergeCellsOutlined';
export default /*#__PURE__*/React.forwardRef(MergeCellsOutlined);