import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlayCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PlayCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var PlayCircleTwoTone = function PlayCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlayCircleTwoToneSvg
  }));
};
PlayCircleTwoTone.displayName = 'PlayCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(PlayCircleTwoTone);