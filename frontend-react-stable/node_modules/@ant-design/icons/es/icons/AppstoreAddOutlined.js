import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppstoreAddOutlinedSvg from "@ant-design/icons-svg/es/asn/AppstoreAddOutlined";
import AntdIcon from '../components/AntdIcon';
var AppstoreAddOutlined = function AppstoreAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppstoreAddOutlinedSvg
  }));
};
AppstoreAddOutlined.displayName = 'AppstoreAddOutlined';
export default /*#__PURE__*/React.forwardRef(AppstoreAddOutlined);