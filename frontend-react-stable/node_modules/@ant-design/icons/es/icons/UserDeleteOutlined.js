import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UserDeleteOutlinedSvg from "@ant-design/icons-svg/es/asn/UserDeleteOutlined";
import AntdIcon from '../components/AntdIcon';
var UserDeleteOutlined = function UserDeleteOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UserDeleteOutlinedSvg
  }));
};
UserDeleteOutlined.displayName = 'UserDeleteOutlined';
export default /*#__PURE__*/React.forwardRef(UserDeleteOutlined);