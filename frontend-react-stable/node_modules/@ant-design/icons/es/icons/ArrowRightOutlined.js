import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ArrowRightOutlinedSvg from "@ant-design/icons-svg/es/asn/ArrowRightOutlined";
import AntdIcon from '../components/AntdIcon';
var ArrowRightOutlined = function ArrowRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ArrowRightOutlinedSvg
  }));
};
ArrowRightOutlined.displayName = 'ArrowRightOutlined';
export default /*#__PURE__*/React.forwardRef(ArrowRightOutlined);