import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SkinFilledSvg from "@ant-design/icons-svg/es/asn/SkinFilled";
import AntdIcon from '../components/AntdIcon';
var SkinFilled = function SkinFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SkinFilledSvg
  }));
};
SkinFilled.displayName = 'SkinFilled';
export default /*#__PURE__*/React.forwardRef(SkinFilled);