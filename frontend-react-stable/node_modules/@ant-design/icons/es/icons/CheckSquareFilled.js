import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckSquareFilledSvg from "@ant-design/icons-svg/es/asn/CheckSquareFilled";
import AntdIcon from '../components/AntdIcon';
var CheckSquareFilled = function CheckSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckSquareFilledSvg
  }));
};
CheckSquareFilled.displayName = 'CheckSquareFilled';
export default /*#__PURE__*/React.forwardRef(CheckSquareFilled);