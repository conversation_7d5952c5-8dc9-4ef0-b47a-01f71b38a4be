import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeleteRowOutlinedSvg from "@ant-design/icons-svg/es/asn/DeleteRowOutlined";
import AntdIcon from '../components/AntdIcon';
var DeleteRowOutlined = function DeleteRowOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeleteRowOutlinedSvg
  }));
};
DeleteRowOutlined.displayName = 'DeleteRowOutlined';
export default /*#__PURE__*/React.forwardRef(DeleteRowOutlined);