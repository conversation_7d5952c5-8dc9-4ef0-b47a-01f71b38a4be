import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ScheduleTwoToneSvg from "@ant-design/icons-svg/es/asn/ScheduleTwoTone";
import AntdIcon from '../components/AntdIcon';
var ScheduleTwoTone = function ScheduleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ScheduleTwoToneSvg
  }));
};
ScheduleTwoTone.displayName = 'ScheduleTwoTone';
export default /*#__PURE__*/React.forwardRef(ScheduleTwoTone);