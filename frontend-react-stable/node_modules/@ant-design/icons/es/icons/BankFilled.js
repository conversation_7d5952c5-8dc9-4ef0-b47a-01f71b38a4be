import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BankFilledSvg from "@ant-design/icons-svg/es/asn/BankFilled";
import AntdIcon from '../components/AntdIcon';
var BankFilled = function BankFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BankFilledSvg
  }));
};
BankFilled.displayName = 'BankFilled';
export default /*#__PURE__*/React.forwardRef(BankFilled);