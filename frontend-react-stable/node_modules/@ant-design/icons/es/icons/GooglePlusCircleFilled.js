import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GooglePlusCircleFilledSvg from "@ant-design/icons-svg/es/asn/GooglePlusCircleFilled";
import AntdIcon from '../components/AntdIcon';
var GooglePlusCircleFilled = function GooglePlusCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GooglePlusCircleFilledSvg
  }));
};
GooglePlusCircleFilled.displayName = 'GooglePlusCircleFilled';
export default /*#__PURE__*/React.forwardRef(GooglePlusCircleFilled);