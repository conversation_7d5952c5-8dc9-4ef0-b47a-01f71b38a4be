import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StopOutlinedSvg from "@ant-design/icons-svg/es/asn/StopOutlined";
import AntdIcon from '../components/AntdIcon';
var StopOutlined = function StopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StopOutlinedSvg
  }));
};
StopOutlined.displayName = 'StopOutlined';
export default /*#__PURE__*/React.forwardRef(StopOutlined);