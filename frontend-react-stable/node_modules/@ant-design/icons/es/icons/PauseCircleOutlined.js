import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PauseCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PauseCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PauseCircleOutlined = function PauseCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PauseCircleOutlinedSvg
  }));
};
PauseCircleOutlined.displayName = 'PauseCircleOutlined';
export default /*#__PURE__*/React.forwardRef(PauseCircleOutlined);