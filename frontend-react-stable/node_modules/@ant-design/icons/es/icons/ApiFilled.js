import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ApiFilledSvg from "@ant-design/icons-svg/es/asn/ApiFilled";
import AntdIcon from '../components/AntdIcon';
var ApiFilled = function ApiFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ApiFilledSvg
  }));
};
ApiFilled.displayName = 'ApiFilled';
export default /*#__PURE__*/React.forwardRef(ApiFilled);