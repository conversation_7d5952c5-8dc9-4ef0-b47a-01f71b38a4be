import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BulbOutlinedSvg from "@ant-design/icons-svg/es/asn/BulbOutlined";
import AntdIcon from '../components/AntdIcon';
var BulbOutlined = function BulbOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BulbOutlinedSvg
  }));
};
BulbOutlined.displayName = 'BulbOutlined';
export default /*#__PURE__*/React.forwardRef(BulbOutlined);