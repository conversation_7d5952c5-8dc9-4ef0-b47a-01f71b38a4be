import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DashboardOutlinedSvg from "@ant-design/icons-svg/es/asn/DashboardOutlined";
import AntdIcon from '../components/AntdIcon';
var DashboardOutlined = function DashboardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DashboardOutlinedSvg
  }));
};
DashboardOutlined.displayName = 'DashboardOutlined';
export default /*#__PURE__*/React.forwardRef(DashboardOutlined);