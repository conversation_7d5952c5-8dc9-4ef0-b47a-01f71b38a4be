import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyFilledSvg from "@ant-design/icons-svg/es/asn/CopyFilled";
import AntdIcon from '../components/AntdIcon';
var CopyFilled = function CopyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyFilledSvg
  }));
};
CopyFilled.displayName = 'CopyFilled';
export default /*#__PURE__*/React.forwardRef(CopyFilled);