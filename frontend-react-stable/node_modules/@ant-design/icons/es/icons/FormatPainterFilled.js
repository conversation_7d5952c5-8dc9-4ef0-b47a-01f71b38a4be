import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FormatPainterFilledSvg from "@ant-design/icons-svg/es/asn/FormatPainterFilled";
import AntdIcon from '../components/AntdIcon';
var FormatPainterFilled = function FormatPainterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FormatPainterFilledSvg
  }));
};
FormatPainterFilled.displayName = 'FormatPainterFilled';
export default /*#__PURE__*/React.forwardRef(FormatPainterFilled);