import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileImageFilledSvg from "@ant-design/icons-svg/es/asn/FileImageFilled";
import AntdIcon from '../components/AntdIcon';
var FileImageFilled = function FileImageFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileImageFilledSvg
  }));
};
FileImageFilled.displayName = 'FileImageFilled';
export default /*#__PURE__*/React.forwardRef(FileImageFilled);