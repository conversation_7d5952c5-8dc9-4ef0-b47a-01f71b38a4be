import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EuroCircleFilledSvg from "@ant-design/icons-svg/es/asn/EuroCircleFilled";
import AntdIcon from '../components/AntdIcon';
var EuroCircleFilled = function EuroCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EuroCircleFilledSvg
  }));
};
EuroCircleFilled.displayName = 'EuroCircleFilled';
export default /*#__PURE__*/React.forwardRef(EuroCircleFilled);