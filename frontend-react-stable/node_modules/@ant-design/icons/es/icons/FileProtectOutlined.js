import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileProtectOutlinedSvg from "@ant-design/icons-svg/es/asn/FileProtectOutlined";
import AntdIcon from '../components/AntdIcon';
var FileProtectOutlined = function FileProtectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileProtectOutlinedSvg
  }));
};
FileProtectOutlined.displayName = 'FileProtectOutlined';
export default /*#__PURE__*/React.forwardRef(FileProtectOutlined);