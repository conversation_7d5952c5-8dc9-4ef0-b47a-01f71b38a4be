import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ConsoleSqlOutlinedSvg from "@ant-design/icons-svg/es/asn/ConsoleSqlOutlined";
import AntdIcon from '../components/AntdIcon';
var ConsoleSqlOutlined = function ConsoleSqlOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ConsoleSqlOutlinedSvg
  }));
};
ConsoleSqlOutlined.displayName = 'ConsoleSqlOutlined';
export default /*#__PURE__*/React.forwardRef(ConsoleSqlOutlined);