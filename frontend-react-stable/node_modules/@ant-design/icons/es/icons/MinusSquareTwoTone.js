import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/MinusSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var MinusSquareTwoTone = function MinusSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusSquareTwoToneSvg
  }));
};
MinusSquareTwoTone.displayName = 'MinusSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(MinusSquareTwoTone);