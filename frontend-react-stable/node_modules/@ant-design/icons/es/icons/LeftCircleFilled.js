import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftCircleFilledSvg from "@ant-design/icons-svg/es/asn/LeftCircleFilled";
import AntdIcon from '../components/AntdIcon';
var LeftCircleFilled = function LeftCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftCircleFilledSvg
  }));
};
LeftCircleFilled.displayName = 'LeftCircleFilled';
export default /*#__PURE__*/React.forwardRef(LeftCircleFilled);