import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppstoreFilledSvg from "@ant-design/icons-svg/es/asn/AppstoreFilled";
import AntdIcon from '../components/AntdIcon';
var AppstoreFilled = function AppstoreFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppstoreFilledSvg
  }));
};
AppstoreFilled.displayName = 'AppstoreFilled';
export default /*#__PURE__*/React.forwardRef(AppstoreFilled);