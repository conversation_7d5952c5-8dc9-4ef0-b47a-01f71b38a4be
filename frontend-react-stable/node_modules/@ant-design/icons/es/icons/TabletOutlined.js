import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TabletOutlinedSvg from "@ant-design/icons-svg/es/asn/TabletOutlined";
import AntdIcon from '../components/AntdIcon';
var TabletOutlined = function TabletOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TabletOutlinedSvg
  }));
};
TabletOutlined.displayName = 'TabletOutlined';
export default /*#__PURE__*/React.forwardRef(TabletOutlined);