import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileMarkdownFilledSvg from "@ant-design/icons-svg/es/asn/FileMarkdownFilled";
import AntdIcon from '../components/AntdIcon';
var FileMarkdownFilled = function FileMarkdownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileMarkdownFilledSvg
  }));
};
FileMarkdownFilled.displayName = 'FileMarkdownFilled';
export default /*#__PURE__*/React.forwardRef(FileMarkdownFilled);