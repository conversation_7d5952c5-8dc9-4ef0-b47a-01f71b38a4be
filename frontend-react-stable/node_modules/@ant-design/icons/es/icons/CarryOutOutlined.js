import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarryOutOutlinedSvg from "@ant-design/icons-svg/es/asn/CarryOutOutlined";
import AntdIcon from '../components/AntdIcon';
var CarryOutOutlined = function CarryOutOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarryOutOutlinedSvg
  }));
};
CarryOutOutlined.displayName = 'CarryOutOutlined';
export default /*#__PURE__*/React.forwardRef(CarryOutOutlined);