import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileTextFilledSvg from "@ant-design/icons-svg/es/asn/FileTextFilled";
import AntdIcon from '../components/AntdIcon';
var FileTextFilled = function FileTextFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileTextFilledSvg
  }));
};
FileTextFilled.displayName = 'FileTextFilled';
export default /*#__PURE__*/React.forwardRef(FileTextFilled);