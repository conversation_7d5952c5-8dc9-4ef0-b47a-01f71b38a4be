import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FormatPainterOutlinedSvg from "@ant-design/icons-svg/es/asn/FormatPainterOutlined";
import AntdIcon from '../components/AntdIcon';
var FormatPainterOutlined = function FormatPainterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FormatPainterOutlinedSvg
  }));
};
FormatPainterOutlined.displayName = 'FormatPainterOutlined';
export default /*#__PURE__*/React.forwardRef(FormatPainterOutlined);