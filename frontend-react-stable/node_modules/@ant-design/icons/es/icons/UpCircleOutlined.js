import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/UpCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var UpCircleOutlined = function UpCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpCircleOutlinedSvg
  }));
};
UpCircleOutlined.displayName = 'UpCircleOutlined';
export default /*#__PURE__*/React.forwardRef(UpCircleOutlined);