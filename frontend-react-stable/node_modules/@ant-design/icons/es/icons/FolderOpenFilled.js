import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderOpenFilledSvg from "@ant-design/icons-svg/es/asn/FolderOpenFilled";
import AntdIcon from '../components/AntdIcon';
var FolderOpenFilled = function FolderOpenFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderOpenFilledSvg
  }));
};
FolderOpenFilled.displayName = 'FolderOpenFilled';
export default /*#__PURE__*/React.forwardRef(FolderOpenFilled);