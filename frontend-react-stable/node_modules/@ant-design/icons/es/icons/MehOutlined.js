import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MehOutlinedSvg from "@ant-design/icons-svg/es/asn/MehOutlined";
import AntdIcon from '../components/AntdIcon';
var MehOutlined = function MehOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MehOutlinedSvg
  }));
};
MehOutlined.displayName = 'MehOutlined';
export default /*#__PURE__*/React.forwardRef(MehOutlined);