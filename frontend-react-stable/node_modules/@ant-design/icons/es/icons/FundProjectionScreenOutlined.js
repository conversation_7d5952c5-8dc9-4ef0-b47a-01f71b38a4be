import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FundProjectionScreenOutlinedSvg from "@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined";
import AntdIcon from '../components/AntdIcon';
var FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FundProjectionScreenOutlinedSvg
  }));
};
FundProjectionScreenOutlined.displayName = 'FundProjectionScreenOutlined';
export default /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);