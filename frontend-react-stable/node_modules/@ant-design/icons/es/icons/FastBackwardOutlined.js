import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FastBackwardOutlinedSvg from "@ant-design/icons-svg/es/asn/FastBackwardOutlined";
import AntdIcon from '../components/AntdIcon';
var FastBackwardOutlined = function FastBackwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FastBackwardOutlinedSvg
  }));
};
FastBackwardOutlined.displayName = 'FastBackwardOutlined';
export default /*#__PURE__*/React.forwardRef(FastBackwardOutlined);