import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlertOutlinedSvg from "@ant-design/icons-svg/es/asn/AlertOutlined";
import AntdIcon from '../components/AntdIcon';
var AlertOutlined = function AlertOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlertOutlinedSvg
  }));
};
AlertOutlined.displayName = 'AlertOutlined';
export default /*#__PURE__*/React.forwardRef(AlertOutlined);