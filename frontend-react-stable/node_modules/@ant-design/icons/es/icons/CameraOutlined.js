import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CameraOutlinedSvg from "@ant-design/icons-svg/es/asn/CameraOutlined";
import AntdIcon from '../components/AntdIcon';
var CameraOutlined = function CameraOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CameraOutlinedSvg
  }));
};
CameraOutlined.displayName = 'CameraOutlined';
export default /*#__PURE__*/React.forwardRef(CameraOutlined);