import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UsergroupAddOutlinedSvg from "@ant-design/icons-svg/es/asn/UsergroupAddOutlined";
import AntdIcon from '../components/AntdIcon';
var UsergroupAddOutlined = function UsergroupAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UsergroupAddOutlinedSvg
  }));
};
UsergroupAddOutlined.displayName = 'UsergroupAddOutlined';
export default /*#__PURE__*/React.forwardRef(UsergroupAddOutlined);