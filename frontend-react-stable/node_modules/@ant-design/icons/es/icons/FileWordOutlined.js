import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileWordOutlinedSvg from "@ant-design/icons-svg/es/asn/FileWordOutlined";
import AntdIcon from '../components/AntdIcon';
var FileWordOutlined = function FileWordOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileWordOutlinedSvg
  }));
};
FileWordOutlined.displayName = 'FileWordOutlined';
export default /*#__PURE__*/React.forwardRef(FileWordOutlined);