import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoogleOutlinedSvg from "@ant-design/icons-svg/es/asn/GoogleOutlined";
import AntdIcon from '../components/AntdIcon';
var GoogleOutlined = function GoogleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoogleOutlinedSvg
  }));
};
GoogleOutlined.displayName = 'GoogleOutlined';
export default /*#__PURE__*/React.forwardRef(GoogleOutlined);