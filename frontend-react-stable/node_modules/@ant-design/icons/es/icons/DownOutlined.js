import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownOutlinedSvg from "@ant-design/icons-svg/es/asn/DownOutlined";
import AntdIcon from '../components/AntdIcon';
var DownOutlined = function DownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownOutlinedSvg
  }));
};
DownOutlined.displayName = 'DownOutlined';
export default /*#__PURE__*/React.forwardRef(DownOutlined);