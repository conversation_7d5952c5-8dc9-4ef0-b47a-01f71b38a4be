import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IssuesCloseOutlinedSvg from "@ant-design/icons-svg/es/asn/IssuesCloseOutlined";
import AntdIcon from '../components/AntdIcon';
var IssuesCloseOutlined = function IssuesCloseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IssuesCloseOutlinedSvg
  }));
};
IssuesCloseOutlined.displayName = 'IssuesCloseOutlined';
export default /*#__PURE__*/React.forwardRef(IssuesCloseOutlined);