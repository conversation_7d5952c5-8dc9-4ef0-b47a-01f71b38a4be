import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TwitterCircleFilledSvg from "@ant-design/icons-svg/es/asn/TwitterCircleFilled";
import AntdIcon from '../components/AntdIcon';
var TwitterCircleFilled = function TwitterCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TwitterCircleFilledSvg
  }));
};
TwitterCircleFilled.displayName = 'TwitterCircleFilled';
export default /*#__PURE__*/React.forwardRef(TwitterCircleFilled);