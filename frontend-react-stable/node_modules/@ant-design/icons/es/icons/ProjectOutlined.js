import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProjectOutlinedSvg from "@ant-design/icons-svg/es/asn/ProjectOutlined";
import AntdIcon from '../components/AntdIcon';
var ProjectOutlined = function ProjectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ProjectOutlinedSvg
  }));
};
ProjectOutlined.displayName = 'ProjectOutlined';
export default /*#__PURE__*/React.forwardRef(ProjectOutlined);