import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PushpinTwoToneSvg from "@ant-design/icons-svg/es/asn/PushpinTwoTone";
import AntdIcon from '../components/AntdIcon';
var PushpinTwoTone = function PushpinTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PushpinTwoToneSvg
  }));
};
PushpinTwoTone.displayName = 'PushpinTwoTone';
export default /*#__PURE__*/React.forwardRef(PushpinTwoTone);