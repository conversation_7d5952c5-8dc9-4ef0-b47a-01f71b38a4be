import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DoubleLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/DoubleLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var DoubleLeftOutlined = function DoubleLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DoubleLeftOutlinedSvg
  }));
};
DoubleLeftOutlined.displayName = 'DoubleLeftOutlined';
export default /*#__PURE__*/React.forwardRef(DoubleLeftOutlined);