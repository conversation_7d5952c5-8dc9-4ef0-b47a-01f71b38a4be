import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/DownCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var DownCircleTwoTone = function DownCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownCircleTwoToneSvg
  }));
};
DownCircleTwoTone.displayName = 'DownCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(DownCircleTwoTone);