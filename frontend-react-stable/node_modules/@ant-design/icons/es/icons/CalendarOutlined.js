import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalendarOutlinedSvg from "@ant-design/icons-svg/es/asn/CalendarOutlined";
import AntdIcon from '../components/AntdIcon';
var CalendarOutlined = function CalendarOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalendarOutlinedSvg
  }));
};
CalendarOutlined.displayName = 'CalendarOutlined';
export default /*#__PURE__*/React.forwardRef(CalendarOutlined);