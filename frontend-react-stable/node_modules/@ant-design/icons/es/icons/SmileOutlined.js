import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SmileOutlinedSvg from "@ant-design/icons-svg/es/asn/SmileOutlined";
import AntdIcon from '../components/AntdIcon';
var SmileOutlined = function SmileOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SmileOutlinedSvg
  }));
};
SmileOutlined.displayName = 'SmileOutlined';
export default /*#__PURE__*/React.forwardRef(SmileOutlined);