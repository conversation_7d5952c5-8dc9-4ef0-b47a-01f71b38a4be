import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StarOutlinedSvg from "@ant-design/icons-svg/es/asn/StarOutlined";
import AntdIcon from '../components/AntdIcon';
var StarOutlined = function StarOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StarOutlinedSvg
  }));
};
StarOutlined.displayName = 'StarOutlined';
export default /*#__PURE__*/React.forwardRef(StarOutlined);