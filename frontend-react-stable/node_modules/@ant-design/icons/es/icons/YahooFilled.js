import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YahooFilledSvg from "@ant-design/icons-svg/es/asn/YahooFilled";
import AntdIcon from '../components/AntdIcon';
var YahooFilled = function YahooFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YahooFilledSvg
  }));
};
YahooFilled.displayName = 'YahooFilled';
export default /*#__PURE__*/React.forwardRef(YahooFilled);