import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WeiboSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/WeiboSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var WeiboSquareOutlined = function WeiboSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WeiboSquareOutlinedSvg
  }));
};
WeiboSquareOutlined.displayName = 'WeiboSquareOutlined';
export default /*#__PURE__*/React.forwardRef(WeiboSquareOutlined);