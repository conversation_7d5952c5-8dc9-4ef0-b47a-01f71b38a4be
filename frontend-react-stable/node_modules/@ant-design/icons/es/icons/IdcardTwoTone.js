import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IdcardTwoToneSvg from "@ant-design/icons-svg/es/asn/IdcardTwoTone";
import AntdIcon from '../components/AntdIcon';
var IdcardTwoTone = function IdcardTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IdcardTwoToneSvg
  }));
};
IdcardTwoTone.displayName = 'IdcardTwoTone';
export default /*#__PURE__*/React.forwardRef(IdcardTwoTone);