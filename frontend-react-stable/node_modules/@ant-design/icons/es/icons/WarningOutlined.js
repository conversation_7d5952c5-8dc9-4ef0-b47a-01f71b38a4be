import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WarningOutlinedSvg from "@ant-design/icons-svg/es/asn/WarningOutlined";
import AntdIcon from '../components/AntdIcon';
var WarningOutlined = function WarningOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WarningOutlinedSvg
  }));
};
WarningOutlined.displayName = 'WarningOutlined';
export default /*#__PURE__*/React.forwardRef(WarningOutlined);