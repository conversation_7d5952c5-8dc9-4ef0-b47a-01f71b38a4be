import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarTwoToneSvg from "@ant-design/icons-svg/es/asn/CarTwoTone";
import AntdIcon from '../components/AntdIcon';
var CarTwoTone = function CarTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarTwoToneSvg
  }));
};
CarTwoTone.displayName = 'CarTwoTone';
export default /*#__PURE__*/React.forwardRef(CarTwoTone);