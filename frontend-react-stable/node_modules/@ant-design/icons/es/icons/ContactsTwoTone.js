import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContactsTwoToneSvg from "@ant-design/icons-svg/es/asn/ContactsTwoTone";
import AntdIcon from '../components/AntdIcon';
var ContactsTwoTone = function ContactsTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContactsTwoToneSvg
  }));
};
ContactsTwoTone.displayName = 'ContactsTwoTone';
export default /*#__PURE__*/React.forwardRef(ContactsTwoTone);