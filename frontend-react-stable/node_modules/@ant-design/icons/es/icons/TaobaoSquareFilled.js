import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TaobaoSquareFilledSvg from "@ant-design/icons-svg/es/asn/TaobaoSquareFilled";
import AntdIcon from '../components/AntdIcon';
var TaobaoSquareFilled = function TaobaoSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TaobaoSquareFilledSvg
  }));
};
TaobaoSquareFilled.displayName = 'TaobaoSquareFilled';
export default /*#__PURE__*/React.forwardRef(TaobaoSquareFilled);