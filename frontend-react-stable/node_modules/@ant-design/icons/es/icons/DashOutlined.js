import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DashOutlinedSvg from "@ant-design/icons-svg/es/asn/DashOutlined";
import AntdIcon from '../components/AntdIcon';
var DashOutlined = function DashOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DashOutlinedSvg
  }));
};
DashOutlined.displayName = 'DashOutlined';
export default /*#__PURE__*/React.forwardRef(DashOutlined);