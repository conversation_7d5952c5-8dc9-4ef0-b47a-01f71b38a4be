import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RobotFilledSvg from "@ant-design/icons-svg/es/asn/RobotFilled";
import AntdIcon from '../components/AntdIcon';
var RobotFilled = function RobotFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RobotFilledSvg
  }));
};
RobotFilled.displayName = 'RobotFilled';
export default /*#__PURE__*/React.forwardRef(RobotFilled);