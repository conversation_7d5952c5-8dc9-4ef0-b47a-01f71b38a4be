import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeploymentUnitOutlinedSvg from "@ant-design/icons-svg/es/asn/DeploymentUnitOutlined";
import AntdIcon from '../components/AntdIcon';
var DeploymentUnitOutlined = function DeploymentUnitOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeploymentUnitOutlinedSvg
  }));
};
DeploymentUnitOutlined.displayName = 'DeploymentUnitOutlined';
export default /*#__PURE__*/React.forwardRef(DeploymentUnitOutlined);