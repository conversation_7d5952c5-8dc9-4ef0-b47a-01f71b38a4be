import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyrightCircleFilledSvg from "@ant-design/icons-svg/es/asn/CopyrightCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CopyrightCircleFilled = function CopyrightCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyrightCircleFilledSvg
  }));
};
CopyrightCircleFilled.displayName = 'CopyrightCircleFilled';
export default /*#__PURE__*/React.forwardRef(CopyrightCircleFilled);