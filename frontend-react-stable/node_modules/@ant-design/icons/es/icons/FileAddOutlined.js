import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileAddOutlinedSvg from "@ant-design/icons-svg/es/asn/FileAddOutlined";
import AntdIcon from '../components/AntdIcon';
var FileAddOutlined = function FileAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileAddOutlinedSvg
  }));
};
FileAddOutlined.displayName = 'FileAddOutlined';
export default /*#__PURE__*/React.forwardRef(FileAddOutlined);