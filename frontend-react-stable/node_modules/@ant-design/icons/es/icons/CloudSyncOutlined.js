import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudSyncOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudSyncOutlined";
import AntdIcon from '../components/AntdIcon';
var CloudSyncOutlined = function CloudSyncOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudSyncOutlinedSvg
  }));
};
CloudSyncOutlined.displayName = 'CloudSyncOutlined';
export default /*#__PURE__*/React.forwardRef(CloudSyncOutlined);