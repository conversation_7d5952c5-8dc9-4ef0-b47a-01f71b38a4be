import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BookFilledSvg from "@ant-design/icons-svg/es/asn/BookFilled";
import AntdIcon from '../components/AntdIcon';
var BookFilled = function BookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BookFilledSvg
  }));
};
BookFilled.displayName = 'BookFilled';
export default /*#__PURE__*/React.forwardRef(BookFilled);