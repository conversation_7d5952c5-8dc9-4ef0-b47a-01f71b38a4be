import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PieChartFilledSvg from "@ant-design/icons-svg/es/asn/PieChartFilled";
import AntdIcon from '../components/AntdIcon';
var PieChartFilled = function PieChartFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PieChartFilledSvg
  }));
};
PieChartFilled.displayName = 'PieChartFilled';
export default /*#__PURE__*/React.forwardRef(PieChartFilled);