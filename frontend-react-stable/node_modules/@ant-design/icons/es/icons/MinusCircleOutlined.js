import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/MinusCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var MinusCircleOutlined = function MinusCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusCircleOutlinedSvg
  }));
};
MinusCircleOutlined.displayName = 'MinusCircleOutlined';
export default /*#__PURE__*/React.forwardRef(MinusCircleOutlined);