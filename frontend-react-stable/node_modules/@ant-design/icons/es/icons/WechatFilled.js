import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WechatFilledSvg from "@ant-design/icons-svg/es/asn/WechatFilled";
import AntdIcon from '../components/AntdIcon';
var WechatFilled = function WechatFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WechatFilledSvg
  }));
};
WechatFilled.displayName = 'WechatFilled';
export default /*#__PURE__*/React.forwardRef(WechatFilled);