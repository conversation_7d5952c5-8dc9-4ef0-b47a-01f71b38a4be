import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePdfOutlinedSvg from "@ant-design/icons-svg/es/asn/FilePdfOutlined";
import AntdIcon from '../components/AntdIcon';
var FilePdfOutlined = function FilePdfOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePdfOutlinedSvg
  }));
};
FilePdfOutlined.displayName = 'FilePdfOutlined';
export default /*#__PURE__*/React.forwardRef(FilePdfOutlined);