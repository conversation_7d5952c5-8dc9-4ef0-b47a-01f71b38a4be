import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderOpenOutlinedSvg from "@ant-design/icons-svg/es/asn/FolderOpenOutlined";
import AntdIcon from '../components/AntdIcon';
var FolderOpenOutlined = function FolderOpenOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderOpenOutlinedSvg
  }));
};
FolderOpenOutlined.displayName = 'FolderOpenOutlined';
export default /*#__PURE__*/React.forwardRef(FolderOpenOutlined);