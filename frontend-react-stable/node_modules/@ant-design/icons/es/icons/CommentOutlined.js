import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CommentOutlinedSvg from "@ant-design/icons-svg/es/asn/CommentOutlined";
import AntdIcon from '../components/AntdIcon';
var CommentOutlined = function CommentOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CommentOutlinedSvg
  }));
};
CommentOutlined.displayName = 'CommentOutlined';
export default /*#__PURE__*/React.forwardRef(CommentOutlined);