import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ToolTwoToneSvg from "@ant-design/icons-svg/es/asn/ToolTwoTone";
import AntdIcon from '../components/AntdIcon';
var ToolTwoTone = function ToolTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ToolTwoToneSvg
  }));
};
ToolTwoTone.displayName = 'ToolTwoTone';
export default /*#__PURE__*/React.forwardRef(ToolTwoTone);