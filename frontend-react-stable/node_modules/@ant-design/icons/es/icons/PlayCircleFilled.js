import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlayCircleFilledSvg from "@ant-design/icons-svg/es/asn/PlayCircleFilled";
import AntdIcon from '../components/AntdIcon';
var PlayCircleFilled = function PlayCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlayCircleFilledSvg
  }));
};
PlayCircleFilled.displayName = 'PlayCircleFilled';
export default /*#__PURE__*/React.forwardRef(PlayCircleFilled);