import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UserSwitchOutlinedSvg from "@ant-design/icons-svg/es/asn/UserSwitchOutlined";
import AntdIcon from '../components/AntdIcon';
var UserSwitchOutlined = function UserSwitchOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UserSwitchOutlinedSvg
  }));
};
UserSwitchOutlined.displayName = 'UserSwitchOutlined';
export default /*#__PURE__*/React.forwardRef(UserSwitchOutlined);