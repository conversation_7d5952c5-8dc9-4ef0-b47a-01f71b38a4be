import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagsFilledSvg from "@ant-design/icons-svg/es/asn/TagsFilled";
import AntdIcon from '../components/AntdIcon';
var TagsFilled = function TagsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagsFilledSvg
  }));
};
TagsFilled.displayName = 'TagsFilled';
export default /*#__PURE__*/React.forwardRef(TagsFilled);