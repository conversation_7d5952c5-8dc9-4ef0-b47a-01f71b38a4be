import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderLeftOutlined = function BorderLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderLeftOutlinedSvg
  }));
};
BorderLeftOutlined.displayName = 'BorderLeftOutlined';
export default /*#__PURE__*/React.forwardRef(BorderLeftOutlined);