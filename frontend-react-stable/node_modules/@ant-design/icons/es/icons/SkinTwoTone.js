import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SkinTwoToneSvg from "@ant-design/icons-svg/es/asn/SkinTwoTone";
import AntdIcon from '../components/AntdIcon';
var SkinTwoTone = function SkinTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SkinTwoToneSvg
  }));
};
SkinTwoTone.displayName = 'SkinTwoTone';
export default /*#__PURE__*/React.forwardRef(SkinTwoTone);