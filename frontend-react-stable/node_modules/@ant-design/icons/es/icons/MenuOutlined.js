import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MenuOutlinedSvg from "@ant-design/icons-svg/es/asn/MenuOutlined";
import AntdIcon from '../components/AntdIcon';
var MenuOutlined = function MenuOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MenuOutlinedSvg
  }));
};
MenuOutlined.displayName = 'MenuOutlined';
export default /*#__PURE__*/React.forwardRef(MenuOutlined);