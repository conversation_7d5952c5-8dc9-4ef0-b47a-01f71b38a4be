import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MessageTwoToneSvg from "@ant-design/icons-svg/es/asn/MessageTwoTone";
import AntdIcon from '../components/AntdIcon';
var MessageTwoTone = function MessageTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MessageTwoToneSvg
  }));
};
MessageTwoTone.displayName = 'MessageTwoTone';
export default /*#__PURE__*/React.forwardRef(MessageTwoTone);