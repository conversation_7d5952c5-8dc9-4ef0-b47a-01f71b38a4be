import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DribbbleSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/DribbbleSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var DribbbleSquareOutlined = function DribbbleSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DribbbleSquareOutlinedSvg
  }));
};
DribbbleSquareOutlined.displayName = 'DribbbleSquareOutlined';
export default /*#__PURE__*/React.forwardRef(DribbbleSquareOutlined);