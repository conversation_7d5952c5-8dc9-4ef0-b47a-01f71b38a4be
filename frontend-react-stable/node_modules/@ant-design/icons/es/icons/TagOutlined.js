import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagOutlinedSvg from "@ant-design/icons-svg/es/asn/TagOutlined";
import AntdIcon from '../components/AntdIcon';
var TagOutlined = function TagOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagOutlinedSvg
  }));
};
TagOutlined.displayName = 'TagOutlined';
export default /*#__PURE__*/React.forwardRef(TagOutlined);