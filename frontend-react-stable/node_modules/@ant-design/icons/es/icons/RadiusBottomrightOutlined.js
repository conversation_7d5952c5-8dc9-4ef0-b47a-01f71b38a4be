import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RadiusBottomrightOutlinedSvg from "@ant-design/icons-svg/es/asn/RadiusBottomrightOutlined";
import AntdIcon from '../components/AntdIcon';
var RadiusBottomrightOutlined = function RadiusBottomrightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadiusBottomrightOutlinedSvg
  }));
};
RadiusBottomrightOutlined.displayName = 'RadiusBottomrightOutlined';
export default /*#__PURE__*/React.forwardRef(RadiusBottomrightOutlined);