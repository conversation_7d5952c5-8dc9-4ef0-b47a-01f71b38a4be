import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HighlightFilledSvg from "@ant-design/icons-svg/es/asn/HighlightFilled";
import AntdIcon from '../components/AntdIcon';
var HighlightFilled = function HighlightFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HighlightFilledSvg
  }));
};
HighlightFilled.displayName = 'HighlightFilled';
export default /*#__PURE__*/React.forwardRef(HighlightFilled);