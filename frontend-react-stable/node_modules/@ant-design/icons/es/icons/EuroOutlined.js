import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EuroOutlinedSvg from "@ant-design/icons-svg/es/asn/EuroOutlined";
import AntdIcon from '../components/AntdIcon';
var EuroOutlined = function EuroOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EuroOutlinedSvg
  }));
};
EuroOutlined.displayName = 'EuroOutlined';
export default /*#__PURE__*/React.forwardRef(EuroOutlined);