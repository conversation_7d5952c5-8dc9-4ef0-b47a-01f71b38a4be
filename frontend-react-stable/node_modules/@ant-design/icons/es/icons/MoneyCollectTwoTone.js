import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoneyCollectTwoToneSvg from "@ant-design/icons-svg/es/asn/MoneyCollectTwoTone";
import AntdIcon from '../components/AntdIcon';
var MoneyCollectTwoTone = function MoneyCollectTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoneyCollectTwoToneSvg
  }));
};
MoneyCollectTwoTone.displayName = 'MoneyCollectTwoTone';
export default /*#__PURE__*/React.forwardRef(MoneyCollectTwoTone);