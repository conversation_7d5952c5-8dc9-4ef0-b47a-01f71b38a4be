import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CustomerServiceTwoToneSvg from "@ant-design/icons-svg/es/asn/CustomerServiceTwoTone";
import AntdIcon from '../components/AntdIcon';
var CustomerServiceTwoTone = function CustomerServiceTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CustomerServiceTwoToneSvg
  }));
};
CustomerServiceTwoTone.displayName = 'CustomerServiceTwoTone';
export default /*#__PURE__*/React.forwardRef(CustomerServiceTwoTone);