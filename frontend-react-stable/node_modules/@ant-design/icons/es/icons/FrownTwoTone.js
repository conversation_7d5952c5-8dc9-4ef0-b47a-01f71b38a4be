import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FrownTwoToneSvg from "@ant-design/icons-svg/es/asn/FrownTwoTone";
import AntdIcon from '../components/AntdIcon';
var FrownTwoTone = function FrownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FrownTwoToneSvg
  }));
};
FrownTwoTone.displayName = 'FrownTwoTone';
export default /*#__PURE__*/React.forwardRef(FrownTwoTone);