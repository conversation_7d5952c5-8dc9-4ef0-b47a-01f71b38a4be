import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BehanceSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/BehanceSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var BehanceSquareOutlined = function BehanceSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BehanceSquareOutlinedSvg
  }));
};
BehanceSquareOutlined.displayName = 'BehanceSquareOutlined';
export default /*#__PURE__*/React.forwardRef(BehanceSquareOutlined);