import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InfoCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/InfoCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var InfoCircleOutlined = function InfoCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InfoCircleOutlinedSvg
  }));
};
InfoCircleOutlined.displayName = 'InfoCircleOutlined';
export default /*#__PURE__*/React.forwardRef(InfoCircleOutlined);