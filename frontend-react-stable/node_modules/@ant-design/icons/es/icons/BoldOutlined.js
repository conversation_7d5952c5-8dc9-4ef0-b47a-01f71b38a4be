import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BoldOutlinedSvg from "@ant-design/icons-svg/es/asn/BoldOutlined";
import AntdIcon from '../components/AntdIcon';
var BoldOutlined = function BoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BoldOutlinedSvg
  }));
};
BoldOutlined.displayName = 'BoldOutlined';
export default /*#__PURE__*/React.forwardRef(BoldOutlined);