import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SnippetsTwoToneSvg from "@ant-design/icons-svg/es/asn/SnippetsTwoTone";
import AntdIcon from '../components/AntdIcon';
var SnippetsTwoTone = function SnippetsTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SnippetsTwoToneSvg
  }));
};
SnippetsTwoTone.displayName = 'SnippetsTwoTone';
export default /*#__PURE__*/React.forwardRef(SnippetsTwoTone);