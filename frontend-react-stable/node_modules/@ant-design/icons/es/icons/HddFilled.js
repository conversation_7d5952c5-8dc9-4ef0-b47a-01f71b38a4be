import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HddFilledSvg from "@ant-design/icons-svg/es/asn/HddFilled";
import AntdIcon from '../components/AntdIcon';
var HddFilled = function HddFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HddFilledSvg
  }));
};
HddFilled.displayName = 'HddFilled';
export default /*#__PURE__*/React.forwardRef(HddFilled);