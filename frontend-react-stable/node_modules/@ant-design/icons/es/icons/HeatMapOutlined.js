import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HeatMapOutlinedSvg from "@ant-design/icons-svg/es/asn/HeatMapOutlined";
import AntdIcon from '../components/AntdIcon';
var HeatMapOutlined = function HeatMapOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HeatMapOutlinedSvg
  }));
};
HeatMapOutlined.displayName = 'HeatMapOutlined';
export default /*#__PURE__*/React.forwardRef(HeatMapOutlined);