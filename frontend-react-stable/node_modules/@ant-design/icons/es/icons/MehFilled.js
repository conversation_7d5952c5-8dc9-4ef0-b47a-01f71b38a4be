import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MehFilledSvg from "@ant-design/icons-svg/es/asn/MehFilled";
import AntdIcon from '../components/AntdIcon';
var MehFilled = function MehFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MehFilledSvg
  }));
};
MehFilled.displayName = 'MehFilled';
export default /*#__PURE__*/React.forwardRef(MehFilled);