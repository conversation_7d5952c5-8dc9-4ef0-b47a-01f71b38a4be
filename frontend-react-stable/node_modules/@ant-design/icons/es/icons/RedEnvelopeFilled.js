import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedEnvelopeFilledSvg from "@ant-design/icons-svg/es/asn/RedEnvelopeFilled";
import AntdIcon from '../components/AntdIcon';
var RedEnvelopeFilled = function RedEnvelopeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedEnvelopeFilledSvg
  }));
};
RedEnvelopeFilled.displayName = 'RedEnvelopeFilled';
export default /*#__PURE__*/React.forwardRef(RedEnvelopeFilled);