import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FlagFilledSvg from "@ant-design/icons-svg/es/asn/FlagFilled";
import AntdIcon from '../components/AntdIcon';
var FlagFilled = function FlagFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FlagFilledSvg
  }));
};
FlagFilled.displayName = 'FlagFilled';
export default /*#__PURE__*/React.forwardRef(FlagFilled);