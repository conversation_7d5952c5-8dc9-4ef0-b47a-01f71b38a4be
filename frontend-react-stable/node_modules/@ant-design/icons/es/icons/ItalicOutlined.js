import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ItalicOutlinedSvg from "@ant-design/icons-svg/es/asn/ItalicOutlined";
import AntdIcon from '../components/AntdIcon';
var ItalicOutlined = function ItalicOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ItalicOutlinedSvg
  }));
};
ItalicOutlined.displayName = 'ItalicOutlined';
export default /*#__PURE__*/React.forwardRef(ItalicOutlined);