import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PhoneFilledSvg from "@ant-design/icons-svg/es/asn/PhoneFilled";
import AntdIcon from '../components/AntdIcon';
var PhoneFilled = function PhoneFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PhoneFilledSvg
  }));
};
PhoneFilled.displayName = 'PhoneFilled';
export default /*#__PURE__*/React.forwardRef(PhoneFilled);