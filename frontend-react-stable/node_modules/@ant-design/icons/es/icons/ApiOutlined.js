import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ApiOutlinedSvg from "@ant-design/icons-svg/es/asn/ApiOutlined";
import AntdIcon from '../components/AntdIcon';
var ApiOutlined = function ApiOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ApiOutlinedSvg
  }));
};
ApiOutlined.displayName = 'ApiOutlined';
export default /*#__PURE__*/React.forwardRef(ApiOutlined);