import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwitcherFilledSvg from "@ant-design/icons-svg/es/asn/SwitcherFilled";
import AntdIcon from '../components/AntdIcon';
var SwitcherFilled = function SwitcherFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwitcherFilledSvg
  }));
};
SwitcherFilled.displayName = 'SwitcherFilled';
export default /*#__PURE__*/React.forwardRef(SwitcherFilled);