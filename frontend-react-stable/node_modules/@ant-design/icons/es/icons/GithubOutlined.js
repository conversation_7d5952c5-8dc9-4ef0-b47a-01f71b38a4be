import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GithubOutlinedSvg from "@ant-design/icons-svg/es/asn/GithubOutlined";
import AntdIcon from '../components/AntdIcon';
var GithubOutlined = function GithubOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GithubOutlinedSvg
  }));
};
GithubOutlined.displayName = 'GithubOutlined';
export default /*#__PURE__*/React.forwardRef(GithubOutlined);