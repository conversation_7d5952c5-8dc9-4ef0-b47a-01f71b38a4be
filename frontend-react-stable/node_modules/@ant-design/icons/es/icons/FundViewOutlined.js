import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FundViewOutlinedSvg from "@ant-design/icons-svg/es/asn/FundViewOutlined";
import AntdIcon from '../components/AntdIcon';
var FundViewOutlined = function FundViewOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FundViewOutlinedSvg
  }));
};
FundViewOutlined.displayName = 'FundViewOutlined';
export default /*#__PURE__*/React.forwardRef(FundViewOutlined);