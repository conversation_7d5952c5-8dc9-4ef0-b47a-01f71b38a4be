import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VideoCameraAddOutlinedSvg from "@ant-design/icons-svg/es/asn/VideoCameraAddOutlined";
import AntdIcon from '../components/AntdIcon';
var VideoCameraAddOutlined = function VideoCameraAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VideoCameraAddOutlinedSvg
  }));
};
VideoCameraAddOutlined.displayName = 'VideoCameraAddOutlined';
export default /*#__PURE__*/React.forwardRef(VideoCameraAddOutlined);