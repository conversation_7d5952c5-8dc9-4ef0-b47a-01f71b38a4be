import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretDownFilledSvg from "@ant-design/icons-svg/es/asn/CaretDownFilled";
import AntdIcon from '../components/AntdIcon';
var CaretDownFilled = function CaretDownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretDownFilledSvg
  }));
};
CaretDownFilled.displayName = 'CaretDownFilled';
export default /*#__PURE__*/React.forwardRef(CaretDownFilled);