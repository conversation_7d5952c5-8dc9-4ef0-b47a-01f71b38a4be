import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlipaySquareFilledSvg from "@ant-design/icons-svg/es/asn/AlipaySquareFilled";
import AntdIcon from '../components/AntdIcon';
var AlipaySquareFilled = function AlipaySquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlipaySquareFilledSvg
  }));
};
AlipaySquareFilled.displayName = 'AlipaySquareFilled';
export default /*#__PURE__*/React.forwardRef(AlipaySquareFilled);