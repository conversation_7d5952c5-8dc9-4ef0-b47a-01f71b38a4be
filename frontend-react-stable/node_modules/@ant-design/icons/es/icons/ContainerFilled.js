import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContainerFilledSvg from "@ant-design/icons-svg/es/asn/ContainerFilled";
import AntdIcon from '../components/AntdIcon';
var ContainerFilled = function ContainerFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContainerFilledSvg
  }));
};
ContainerFilled.displayName = 'ContainerFilled';
export default /*#__PURE__*/React.forwardRef(ContainerFilled);