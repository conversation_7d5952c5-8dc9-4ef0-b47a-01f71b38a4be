import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownSquareFilledSvg from "@ant-design/icons-svg/es/asn/DownSquareFilled";
import AntdIcon from '../components/AntdIcon';
var DownSquareFilled = function DownSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownSquareFilledSvg
  }));
};
DownSquareFilled.displayName = 'DownSquareFilled';
export default /*#__PURE__*/React.forwardRef(DownSquareFilled);