import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProjectTwoToneSvg from "@ant-design/icons-svg/es/asn/ProjectTwoTone";
import AntdIcon from '../components/AntdIcon';
var ProjectTwoTone = function ProjectTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ProjectTwoToneSvg
  }));
};
ProjectTwoTone.displayName = 'ProjectTwoTone';
export default /*#__PURE__*/React.forwardRef(ProjectTwoTone);