import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InfoCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/InfoCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var InfoCircleTwoTone = function InfoCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InfoCircleTwoToneSvg
  }));
};
InfoCircleTwoTone.displayName = 'InfoCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(InfoCircleTwoTone);