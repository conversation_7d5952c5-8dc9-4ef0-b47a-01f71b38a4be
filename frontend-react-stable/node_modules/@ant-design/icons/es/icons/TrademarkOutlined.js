import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrademarkOutlinedSvg from "@ant-design/icons-svg/es/asn/TrademarkOutlined";
import AntdIcon from '../components/AntdIcon';
var TrademarkOutlined = function TrademarkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrademarkOutlinedSvg
  }));
};
TrademarkOutlined.displayName = 'TrademarkOutlined';
export default /*#__PURE__*/React.forwardRef(TrademarkOutlined);