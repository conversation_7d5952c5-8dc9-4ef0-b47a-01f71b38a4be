import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeSandboxSquareFilledSvg from "@ant-design/icons-svg/es/asn/CodeSandboxSquareFilled";
import AntdIcon from '../components/AntdIcon';
var CodeSandboxSquareFilled = function CodeSandboxSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeSandboxSquareFilledSvg
  }));
};
CodeSandboxSquareFilled.displayName = 'CodeSandboxSquareFilled';
export default /*#__PURE__*/React.forwardRef(CodeSandboxSquareFilled);