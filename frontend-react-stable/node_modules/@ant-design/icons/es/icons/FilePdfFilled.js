import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePdfFilledSvg from "@ant-design/icons-svg/es/asn/FilePdfFilled";
import AntdIcon from '../components/AntdIcon';
var FilePdfFilled = function FilePdfFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePdfFilledSvg
  }));
};
FilePdfFilled.displayName = 'FilePdfFilled';
export default /*#__PURE__*/React.forwardRef(FilePdfFilled);