import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QqCircleFilledSvg from "@ant-design/icons-svg/es/asn/QqCircleFilled";
import AntdIcon from '../components/AntdIcon';
var QqCircleFilled = function QqCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QqCircleFilledSvg
  }));
};
QqCircleFilled.displayName = 'QqCircleFilled';
export default /*#__PURE__*/React.forwardRef(QqCircleFilled);