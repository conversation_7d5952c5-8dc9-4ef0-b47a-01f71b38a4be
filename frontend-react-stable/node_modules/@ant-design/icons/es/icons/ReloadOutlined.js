import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReloadOutlinedSvg from "@ant-design/icons-svg/es/asn/ReloadOutlined";
import AntdIcon from '../components/AntdIcon';
var ReloadOutlined = function ReloadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReloadOutlinedSvg
  }));
};
ReloadOutlined.displayName = 'ReloadOutlined';
export default /*#__PURE__*/React.forwardRef(ReloadOutlined);