import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DingtalkOutlinedSvg from "@ant-design/icons-svg/es/asn/DingtalkOutlined";
import AntdIcon from '../components/AntdIcon';
var DingtalkOutlined = function DingtalkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DingtalkOutlinedSvg
  }));
};
DingtalkOutlined.displayName = 'DingtalkOutlined';
export default /*#__PURE__*/React.forwardRef(DingtalkOutlined);