import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FallOutlinedSvg from "@ant-design/icons-svg/es/asn/FallOutlined";
import AntdIcon from '../components/AntdIcon';
var FallOutlined = function FallOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FallOutlinedSvg
  }));
};
FallOutlined.displayName = 'FallOutlined';
export default /*#__PURE__*/React.forwardRef(FallOutlined);