import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderRightOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderRightOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderRightOutlined = function BorderRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderRightOutlinedSvg
  }));
};
BorderRightOutlined.displayName = 'BorderRightOutlined';
export default /*#__PURE__*/React.forwardRef(BorderRightOutlined);