import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileAddFilledSvg from "@ant-design/icons-svg/es/asn/FileAddFilled";
import AntdIcon from '../components/AntdIcon';
var FileAddFilled = function FileAddFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileAddFilledSvg
  }));
};
FileAddFilled.displayName = 'FileAddFilled';
export default /*#__PURE__*/React.forwardRef(FileAddFilled);