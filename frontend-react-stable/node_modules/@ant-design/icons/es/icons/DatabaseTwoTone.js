import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DatabaseTwoToneSvg from "@ant-design/icons-svg/es/asn/DatabaseTwoTone";
import AntdIcon from '../components/AntdIcon';
var DatabaseTwoTone = function DatabaseTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DatabaseTwoToneSvg
  }));
};
DatabaseTwoTone.displayName = 'DatabaseTwoTone';
export default /*#__PURE__*/React.forwardRef(DatabaseTwoTone);