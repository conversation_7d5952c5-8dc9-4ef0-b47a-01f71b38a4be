import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WindowsFilledSvg from "@ant-design/icons-svg/es/asn/WindowsFilled";
import AntdIcon from '../components/AntdIcon';
var WindowsFilled = function WindowsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WindowsFilledSvg
  }));
};
WindowsFilled.displayName = 'WindowsFilled';
export default /*#__PURE__*/React.forwardRef(WindowsFilled);