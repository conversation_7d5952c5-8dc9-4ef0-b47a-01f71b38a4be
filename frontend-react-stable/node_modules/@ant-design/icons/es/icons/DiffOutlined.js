import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DiffOutlinedSvg from "@ant-design/icons-svg/es/asn/DiffOutlined";
import AntdIcon from '../components/AntdIcon';
var DiffOutlined = function DiffOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DiffOutlinedSvg
  }));
};
DiffOutlined.displayName = 'DiffOutlined';
export default /*#__PURE__*/React.forwardRef(DiffOutlined);