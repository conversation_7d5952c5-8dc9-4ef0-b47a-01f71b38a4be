import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WeiboOutlinedSvg from "@ant-design/icons-svg/es/asn/WeiboOutlined";
import AntdIcon from '../components/AntdIcon';
var WeiboOutlined = function WeiboOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WeiboOutlinedSvg
  }));
};
WeiboOutlined.displayName = 'WeiboOutlined';
export default /*#__PURE__*/React.forwardRef(WeiboOutlined);