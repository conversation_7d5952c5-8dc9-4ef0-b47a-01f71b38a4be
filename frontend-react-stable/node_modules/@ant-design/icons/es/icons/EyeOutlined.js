import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeOutlinedSvg from "@ant-design/icons-svg/es/asn/EyeOutlined";
import AntdIcon from '../components/AntdIcon';
var EyeOutlined = function EyeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeOutlinedSvg
  }));
};
EyeOutlined.displayName = 'EyeOutlined';
export default /*#__PURE__*/React.forwardRef(EyeOutlined);