import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BuildTwoToneSvg from "@ant-design/icons-svg/es/asn/BuildTwoTone";
import AntdIcon from '../components/AntdIcon';
var BuildTwoTone = function BuildTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BuildTwoToneSvg
  }));
};
BuildTwoTone.displayName = 'BuildTwoTone';
export default /*#__PURE__*/React.forwardRef(BuildTwoTone);