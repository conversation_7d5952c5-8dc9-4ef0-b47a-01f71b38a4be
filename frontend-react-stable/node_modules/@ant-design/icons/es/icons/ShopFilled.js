import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShopFilledSvg from "@ant-design/icons-svg/es/asn/ShopFilled";
import AntdIcon from '../components/AntdIcon';
var ShopFilled = function ShopFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShopFilledSvg
  }));
};
ShopFilled.displayName = 'ShopFilled';
export default /*#__PURE__*/React.forwardRef(ShopFilled);