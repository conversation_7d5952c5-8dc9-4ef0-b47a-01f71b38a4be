import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UngroupOutlinedSvg from "@ant-design/icons-svg/es/asn/UngroupOutlined";
import AntdIcon from '../components/AntdIcon';
var UngroupOutlined = function UngroupOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UngroupOutlinedSvg
  }));
};
UngroupOutlined.displayName = 'UngroupOutlined';
export default /*#__PURE__*/React.forwardRef(UngroupOutlined);