import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlidersTwoToneSvg from "@ant-design/icons-svg/es/asn/SlidersTwoTone";
import AntdIcon from '../components/AntdIcon';
var SlidersTwoTone = function SlidersTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlidersTwoToneSvg
  }));
};
SlidersTwoTone.displayName = 'SlidersTwoTone';
export default /*#__PURE__*/React.forwardRef(SlidersTwoTone);