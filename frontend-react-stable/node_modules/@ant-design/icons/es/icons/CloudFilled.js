import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudFilledSvg from "@ant-design/icons-svg/es/asn/CloudFilled";
import AntdIcon from '../components/AntdIcon';
var CloudFilled = function CloudFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudFilledSvg
  }));
};
CloudFilled.displayName = 'CloudFilled';
export default /*#__PURE__*/React.forwardRef(CloudFilled);