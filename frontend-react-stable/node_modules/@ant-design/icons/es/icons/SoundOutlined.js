import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SoundOutlinedSvg from "@ant-design/icons-svg/es/asn/SoundOutlined";
import AntdIcon from '../components/AntdIcon';
var SoundOutlined = function SoundOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SoundOutlinedSvg
  }));
};
SoundOutlined.displayName = 'SoundOutlined';
export default /*#__PURE__*/React.forwardRef(SoundOutlined);