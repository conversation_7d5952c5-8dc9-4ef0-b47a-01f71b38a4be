import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlayCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PlayCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PlayCircleOutlined = function PlayCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlayCircleOutlinedSvg
  }));
};
PlayCircleOutlined.displayName = 'PlayCircleOutlined';
export default /*#__PURE__*/React.forwardRef(PlayCircleOutlined);