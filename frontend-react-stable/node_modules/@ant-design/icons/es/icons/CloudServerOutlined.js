import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudServerOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudServerOutlined";
import AntdIcon from '../components/AntdIcon';
var CloudServerOutlined = function CloudServerOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudServerOutlinedSvg
  }));
};
CloudServerOutlined.displayName = 'CloudServerOutlined';
export default /*#__PURE__*/React.forwardRef(CloudServerOutlined);