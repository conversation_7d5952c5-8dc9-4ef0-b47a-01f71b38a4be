import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MediumOutlinedSvg from "@ant-design/icons-svg/es/asn/MediumOutlined";
import AntdIcon from '../components/AntdIcon';
var MediumOutlined = function MediumOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MediumOutlinedSvg
  }));
};
MediumOutlined.displayName = 'MediumOutlined';
export default /*#__PURE__*/React.forwardRef(MediumOutlined);