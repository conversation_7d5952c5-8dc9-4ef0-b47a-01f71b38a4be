import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SoundTwoToneSvg from "@ant-design/icons-svg/es/asn/SoundTwoTone";
import AntdIcon from '../components/AntdIcon';
var SoundTwoTone = function SoundTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SoundTwoToneSvg
  }));
};
SoundTwoTone.displayName = 'SoundTwoTone';
export default /*#__PURE__*/React.forwardRef(SoundTwoTone);