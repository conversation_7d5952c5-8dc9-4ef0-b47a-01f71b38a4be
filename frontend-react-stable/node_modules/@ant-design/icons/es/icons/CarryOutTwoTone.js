import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarryOutTwoToneSvg from "@ant-design/icons-svg/es/asn/CarryOutTwoTone";
import AntdIcon from '../components/AntdIcon';
var CarryOutTwoTone = function CarryOutTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarryOutTwoToneSvg
  }));
};
CarryOutTwoTone.displayName = 'CarryOutTwoTone';
export default /*#__PURE__*/React.forwardRef(CarryOutTwoTone);