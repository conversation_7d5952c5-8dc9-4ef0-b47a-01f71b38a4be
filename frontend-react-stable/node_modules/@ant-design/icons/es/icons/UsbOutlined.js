import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UsbOutlinedSvg from "@ant-design/icons-svg/es/asn/UsbOutlined";
import AntdIcon from '../components/AntdIcon';
var UsbOutlined = function UsbOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UsbOutlinedSvg
  }));
};
UsbOutlined.displayName = 'UsbOutlined';
export default /*#__PURE__*/React.forwardRef(UsbOutlined);