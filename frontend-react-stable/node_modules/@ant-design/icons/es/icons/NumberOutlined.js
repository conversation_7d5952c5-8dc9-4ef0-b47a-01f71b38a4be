import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NumberOutlinedSvg from "@ant-design/icons-svg/es/asn/NumberOutlined";
import AntdIcon from '../components/AntdIcon';
var NumberOutlined = function NumberOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NumberOutlinedSvg
  }));
};
NumberOutlined.displayName = 'NumberOutlined';
export default /*#__PURE__*/React.forwardRef(NumberOutlined);