import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileSearchOutlinedSvg from "@ant-design/icons-svg/es/asn/FileSearchOutlined";
import AntdIcon from '../components/AntdIcon';
var FileSearchOutlined = function FileSearchOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileSearchOutlinedSvg
  }));
};
FileSearchOutlined.displayName = 'FileSearchOutlined';
export default /*#__PURE__*/React.forwardRef(FileSearchOutlined);