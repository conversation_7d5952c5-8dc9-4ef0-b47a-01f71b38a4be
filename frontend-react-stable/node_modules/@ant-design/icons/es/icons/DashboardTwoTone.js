import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DashboardTwoToneSvg from "@ant-design/icons-svg/es/asn/DashboardTwoTone";
import AntdIcon from '../components/AntdIcon';
var DashboardTwoTone = function DashboardTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DashboardTwoToneSvg
  }));
};
DashboardTwoTone.displayName = 'DashboardTwoTone';
export default /*#__PURE__*/React.forwardRef(DashboardTwoTone);