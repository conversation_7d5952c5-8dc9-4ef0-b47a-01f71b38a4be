import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TableOutlinedSvg from "@ant-design/icons-svg/es/asn/TableOutlined";
import AntdIcon from '../components/AntdIcon';
var TableOutlined = function TableOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TableOutlinedSvg
  }));
};
TableOutlined.displayName = 'TableOutlined';
export default /*#__PURE__*/React.forwardRef(TableOutlined);