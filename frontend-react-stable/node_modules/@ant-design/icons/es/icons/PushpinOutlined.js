import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PushpinOutlinedSvg from "@ant-design/icons-svg/es/asn/PushpinOutlined";
import AntdIcon from '../components/AntdIcon';
var PushpinOutlined = function PushpinOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PushpinOutlinedSvg
  }));
};
PushpinOutlined.displayName = 'PushpinOutlined';
export default /*#__PURE__*/React.forwardRef(PushpinOutlined);