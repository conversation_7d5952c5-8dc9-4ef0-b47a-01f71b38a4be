import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import KeyOutlinedSvg from "@ant-design/icons-svg/es/asn/KeyOutlined";
import AntdIcon from '../components/AntdIcon';
var KeyOutlined = function KeyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: KeyOutlinedSvg
  }));
};
KeyOutlined.displayName = 'KeyOutlined';
export default /*#__PURE__*/React.forwardRef(KeyOutlined);