import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AudioMutedOutlinedSvg from "@ant-design/icons-svg/es/asn/AudioMutedOutlined";
import AntdIcon from '../components/AntdIcon';
var AudioMutedOutlined = function AudioMutedOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AudioMutedOutlinedSvg
  }));
};
AudioMutedOutlined.displayName = 'AudioMutedOutlined';
export default /*#__PURE__*/React.forwardRef(AudioMutedOutlined);