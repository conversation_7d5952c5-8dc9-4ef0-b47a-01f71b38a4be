import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HourglassFilledSvg from "@ant-design/icons-svg/es/asn/HourglassFilled";
import AntdIcon from '../components/AntdIcon';
var HourglassFilled = function HourglassFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HourglassFilledSvg
  }));
};
HourglassFilled.displayName = 'HourglassFilled';
export default /*#__PURE__*/React.forwardRef(HourglassFilled);