import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ForwardOutlinedSvg from "@ant-design/icons-svg/es/asn/ForwardOutlined";
import AntdIcon from '../components/AntdIcon';
var ForwardOutlined = function ForwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ForwardOutlinedSvg
  }));
};
ForwardOutlined.displayName = 'ForwardOutlined';
export default /*#__PURE__*/React.forwardRef(ForwardOutlined);