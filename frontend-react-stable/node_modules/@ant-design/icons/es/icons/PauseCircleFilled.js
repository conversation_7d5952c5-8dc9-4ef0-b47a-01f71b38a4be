import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PauseCircleFilledSvg from "@ant-design/icons-svg/es/asn/PauseCircleFilled";
import AntdIcon from '../components/AntdIcon';
var PauseCircleFilled = function PauseCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PauseCircleFilledSvg
  }));
};
PauseCircleFilled.displayName = 'PauseCircleFilled';
export default /*#__PURE__*/React.forwardRef(PauseCircleFilled);