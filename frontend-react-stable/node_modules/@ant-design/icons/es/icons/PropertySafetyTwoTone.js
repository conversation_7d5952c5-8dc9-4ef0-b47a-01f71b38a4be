import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PropertySafetyTwoToneSvg from "@ant-design/icons-svg/es/asn/PropertySafetyTwoTone";
import AntdIcon from '../components/AntdIcon';
var PropertySafetyTwoTone = function PropertySafetyTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PropertySafetyTwoToneSvg
  }));
};
PropertySafetyTwoTone.displayName = 'PropertySafetyTwoTone';
export default /*#__PURE__*/React.forwardRef(PropertySafetyTwoTone);