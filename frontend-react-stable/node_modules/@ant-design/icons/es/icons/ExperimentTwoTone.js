import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExperimentTwoToneSvg from "@ant-design/icons-svg/es/asn/ExperimentTwoTone";
import AntdIcon from '../components/AntdIcon';
var ExperimentTwoTone = function ExperimentTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExperimentTwoToneSvg
  }));
};
ExperimentTwoTone.displayName = 'ExperimentTwoTone';
export default /*#__PURE__*/React.forwardRef(ExperimentTwoTone);