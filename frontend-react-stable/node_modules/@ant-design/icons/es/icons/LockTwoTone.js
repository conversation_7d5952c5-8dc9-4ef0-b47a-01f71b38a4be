import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LockTwoToneSvg from "@ant-design/icons-svg/es/asn/LockTwoTone";
import AntdIcon from '../components/AntdIcon';
var LockTwoTone = function LockTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LockTwoToneSvg
  }));
};
LockTwoTone.displayName = 'LockTwoTone';
export default /*#__PURE__*/React.forwardRef(LockTwoTone);