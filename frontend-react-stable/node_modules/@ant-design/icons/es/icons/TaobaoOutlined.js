import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TaobaoOutlinedSvg from "@ant-design/icons-svg/es/asn/TaobaoOutlined";
import AntdIcon from '../components/AntdIcon';
var TaobaoOutlined = function TaobaoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TaobaoOutlinedSvg
  }));
};
TaobaoOutlined.displayName = 'TaobaoOutlined';
export default /*#__PURE__*/React.forwardRef(TaobaoOutlined);