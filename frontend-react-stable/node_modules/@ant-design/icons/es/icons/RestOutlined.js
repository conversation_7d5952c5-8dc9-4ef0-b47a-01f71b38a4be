import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RestOutlinedSvg from "@ant-design/icons-svg/es/asn/RestOutlined";
import AntdIcon from '../components/AntdIcon';
var RestOutlined = function RestOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RestOutlinedSvg
  }));
};
RestOutlined.displayName = 'RestOutlined';
export default /*#__PURE__*/React.forwardRef(RestOutlined);