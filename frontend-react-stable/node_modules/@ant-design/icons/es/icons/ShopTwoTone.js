import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShopTwoToneSvg from "@ant-design/icons-svg/es/asn/ShopTwoTone";
import AntdIcon from '../components/AntdIcon';
var ShopTwoTone = function ShopTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShopTwoToneSvg
  }));
};
ShopTwoTone.displayName = 'ShopTwoTone';
export default /*#__PURE__*/React.forwardRef(ShopTwoTone);