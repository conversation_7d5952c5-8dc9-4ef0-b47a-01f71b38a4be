import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ZoomOutOutlinedSvg from "@ant-design/icons-svg/es/asn/ZoomOutOutlined";
import AntdIcon from '../components/AntdIcon';
var ZoomOutOutlined = function ZoomOutOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ZoomOutOutlinedSvg
  }));
};
ZoomOutOutlined.displayName = 'ZoomOutOutlined';
export default /*#__PURE__*/React.forwardRef(ZoomOutOutlined);