import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusSquareFilledSvg from "@ant-design/icons-svg/es/asn/MinusSquareFilled";
import AntdIcon from '../components/AntdIcon';
var MinusSquareFilled = function MinusSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusSquareFilledSvg
  }));
};
MinusSquareFilled.displayName = 'MinusSquareFilled';
export default /*#__PURE__*/React.forwardRef(MinusSquareFilled);