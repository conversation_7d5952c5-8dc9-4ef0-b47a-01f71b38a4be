import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RocketOutlinedSvg from "@ant-design/icons-svg/es/asn/RocketOutlined";
import AntdIcon from '../components/AntdIcon';
var RocketOutlined = function RocketOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RocketOutlinedSvg
  }));
};
RocketOutlined.displayName = 'RocketOutlined';
export default /*#__PURE__*/React.forwardRef(RocketOutlined);