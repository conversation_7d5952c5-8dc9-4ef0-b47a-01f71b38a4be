import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IeOutlinedSvg from "@ant-design/icons-svg/es/asn/IeOutlined";
import AntdIcon from '../components/AntdIcon';
var IeOutlined = function IeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IeOutlinedSvg
  }));
};
IeOutlined.displayName = 'IeOutlined';
export default /*#__PURE__*/React.forwardRef(IeOutlined);