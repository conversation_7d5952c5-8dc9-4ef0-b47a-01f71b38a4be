import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExcelTwoToneSvg from "@ant-design/icons-svg/es/asn/FileExcelTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileExcelTwoTone = function FileExcelTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExcelTwoToneSvg
  }));
};
FileExcelTwoTone.displayName = 'FileExcelTwoTone';
export default /*#__PURE__*/React.forwardRef(FileExcelTwoTone);