import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpOutlinedSvg from "@ant-design/icons-svg/es/asn/UpOutlined";
import AntdIcon from '../components/AntdIcon';
var UpOutlined = function UpOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpOutlinedSvg
  }));
};
UpOutlined.displayName = 'UpOutlined';
export default /*#__PURE__*/React.forwardRef(UpOutlined);