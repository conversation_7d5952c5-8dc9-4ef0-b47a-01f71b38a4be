import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WifiOutlinedSvg from "@ant-design/icons-svg/es/asn/WifiOutlined";
import AntdIcon from '../components/AntdIcon';
var WifiOutlined = function WifiOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WifiOutlinedSvg
  }));
};
WifiOutlined.displayName = 'WifiOutlined';
export default /*#__PURE__*/React.forwardRef(WifiOutlined);