import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FrownOutlinedSvg from "@ant-design/icons-svg/es/asn/FrownOutlined";
import AntdIcon from '../components/AntdIcon';
var FrownOutlined = function FrownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FrownOutlinedSvg
  }));
};
FrownOutlined.displayName = 'FrownOutlined';
export default /*#__PURE__*/React.forwardRef(FrownOutlined);