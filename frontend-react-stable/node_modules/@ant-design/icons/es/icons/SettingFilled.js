import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SettingFilledSvg from "@ant-design/icons-svg/es/asn/SettingFilled";
import AntdIcon from '../components/AntdIcon';
var SettingFilled = function SettingFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SettingFilledSvg
  }));
};
SettingFilled.displayName = 'SettingFilled';
export default /*#__PURE__*/React.forwardRef(SettingFilled);