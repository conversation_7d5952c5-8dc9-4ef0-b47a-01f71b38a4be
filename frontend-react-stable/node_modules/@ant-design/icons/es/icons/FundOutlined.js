import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FundOutlinedSvg from "@ant-design/icons-svg/es/asn/FundOutlined";
import AntdIcon from '../components/AntdIcon';
var FundOutlined = function FundOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FundOutlinedSvg
  }));
};
FundOutlined.displayName = 'FundOutlined';
export default /*#__PURE__*/React.forwardRef(FundOutlined);