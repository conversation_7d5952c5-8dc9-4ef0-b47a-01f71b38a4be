import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MinusOutlinedSvg from "@ant-design/icons-svg/es/asn/MinusOutlined";
import AntdIcon from '../components/AntdIcon';
var MinusOutlined = function MinusOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MinusOutlinedSvg
  }));
};
MinusOutlined.displayName = 'MinusOutlined';
export default /*#__PURE__*/React.forwardRef(MinusOutlined);