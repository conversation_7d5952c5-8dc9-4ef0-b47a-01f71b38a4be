import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AndroidOutlinedSvg from "@ant-design/icons-svg/es/asn/AndroidOutlined";
import AntdIcon from '../components/AntdIcon';
var AndroidOutlined = function AndroidOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AndroidOutlinedSvg
  }));
};
AndroidOutlined.displayName = 'AndroidOutlined';
export default /*#__PURE__*/React.forwardRef(AndroidOutlined);