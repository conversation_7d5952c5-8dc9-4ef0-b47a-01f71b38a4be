import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ManOutlinedSvg from "@ant-design/icons-svg/es/asn/ManOutlined";
import AntdIcon from '../components/AntdIcon';
var ManOutlined = function ManOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ManOutlinedSvg
  }));
};
ManOutlined.displayName = 'ManOutlined';
export default /*#__PURE__*/React.forwardRef(ManOutlined);