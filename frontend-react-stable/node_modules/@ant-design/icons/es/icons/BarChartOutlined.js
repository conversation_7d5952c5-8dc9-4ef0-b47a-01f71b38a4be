import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Bar<PERSON>hartOutlinedSvg from "@ant-design/icons-svg/es/asn/BarChartOutlined";
import AntdIcon from '../components/AntdIcon';
var BarChartOutlined = function BarChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BarChartOutlinedSvg
  }));
};
BarChartOutlined.displayName = 'BarChartOutlined';
export default /*#__PURE__*/React.forwardRef(BarChartOutlined);