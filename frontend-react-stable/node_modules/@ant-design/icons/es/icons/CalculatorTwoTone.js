import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalculatorTwoToneSvg from "@ant-design/icons-svg/es/asn/CalculatorTwoTone";
import AntdIcon from '../components/AntdIcon';
var CalculatorTwoTone = function CalculatorTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalculatorTwoToneSvg
  }));
};
CalculatorTwoTone.displayName = 'CalculatorTwoTone';
export default /*#__PURE__*/React.forwardRef(CalculatorTwoTone);