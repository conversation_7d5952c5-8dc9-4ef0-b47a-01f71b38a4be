import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InteractionFilledSvg from "@ant-design/icons-svg/es/asn/InteractionFilled";
import AntdIcon from '../components/AntdIcon';
var InteractionFilled = function InteractionFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InteractionFilledSvg
  }));
};
InteractionFilled.displayName = 'InteractionFilled';
export default /*#__PURE__*/React.forwardRef(InteractionFilled);