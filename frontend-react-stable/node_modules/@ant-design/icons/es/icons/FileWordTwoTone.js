import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileWordTwoToneSvg from "@ant-design/icons-svg/es/asn/FileWordTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileWordTwoTone = function FileWordTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileWordTwoToneSvg
  }));
};
FileWordTwoTone.displayName = 'FileWordTwoTone';
export default /*#__PURE__*/React.forwardRef(FileWordTwoTone);