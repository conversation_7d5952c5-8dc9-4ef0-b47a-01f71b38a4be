import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YuqueOutlinedSvg from "@ant-design/icons-svg/es/asn/YuqueOutlined";
import AntdIcon from '../components/AntdIcon';
var YuqueOutlined = function YuqueOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YuqueOutlinedSvg
  }));
};
YuqueOutlined.displayName = 'YuqueOutlined';
export default /*#__PURE__*/React.forwardRef(YuqueOutlined);