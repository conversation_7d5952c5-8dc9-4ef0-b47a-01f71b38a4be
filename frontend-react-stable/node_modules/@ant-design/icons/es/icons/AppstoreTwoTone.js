import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppstoreTwoToneSvg from "@ant-design/icons-svg/es/asn/AppstoreTwoTone";
import AntdIcon from '../components/AntdIcon';
var AppstoreTwoTone = function AppstoreTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppstoreTwoToneSvg
  }));
};
AppstoreTwoTone.displayName = 'AppstoreTwoTone';
export default /*#__PURE__*/React.forwardRef(AppstoreTwoTone);