import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DashboardFilledSvg from "@ant-design/icons-svg/es/asn/DashboardFilled";
import AntdIcon from '../components/AntdIcon';
var DashboardFilled = function DashboardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DashboardFilledSvg
  }));
};
DashboardFilled.displayName = 'DashboardFilled';
export default /*#__PURE__*/React.forwardRef(DashboardFilled);