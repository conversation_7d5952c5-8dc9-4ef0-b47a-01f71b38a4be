import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SubnodeOutlinedSvg from "@ant-design/icons-svg/es/asn/SubnodeOutlined";
import AntdIcon from '../components/AntdIcon';
var SubnodeOutlined = function SubnodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SubnodeOutlinedSvg
  }));
};
SubnodeOutlined.displayName = 'SubnodeOutlined';
export default /*#__PURE__*/React.forwardRef(SubnodeOutlined);