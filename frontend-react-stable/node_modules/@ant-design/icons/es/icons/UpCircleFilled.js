import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpCircleFilledSvg from "@ant-design/icons-svg/es/asn/UpCircleFilled";
import AntdIcon from '../components/AntdIcon';
var UpCircleFilled = function UpCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpCircleFilledSvg
  }));
};
UpCircleFilled.displayName = 'UpCircleFilled';
export default /*#__PURE__*/React.forwardRef(UpCircleFilled);