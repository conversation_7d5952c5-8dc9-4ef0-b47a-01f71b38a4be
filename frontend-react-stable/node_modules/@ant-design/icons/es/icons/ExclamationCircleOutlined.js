import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExclamationCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/ExclamationCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var ExclamationCircleOutlined = function ExclamationCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExclamationCircleOutlinedSvg
  }));
};
ExclamationCircleOutlined.displayName = 'ExclamationCircleOutlined';
export default /*#__PURE__*/React.forwardRef(ExclamationCircleOutlined);