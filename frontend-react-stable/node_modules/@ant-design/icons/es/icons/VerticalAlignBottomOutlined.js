import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerticalAlignBottomOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalAlignBottomOutlined";
import AntdIcon from '../components/AntdIcon';
var VerticalAlignBottomOutlined = function VerticalAlignBottomOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerticalAlignBottomOutlinedSvg
  }));
};
VerticalAlignBottomOutlined.displayName = 'VerticalAlignBottomOutlined';
export default /*#__PURE__*/React.forwardRef(VerticalAlignBottomOutlined);