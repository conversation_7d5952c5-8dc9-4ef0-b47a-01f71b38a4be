import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GlobalOutlinedSvg from "@ant-design/icons-svg/es/asn/GlobalOutlined";
import AntdIcon from '../components/AntdIcon';
var GlobalOutlined = function GlobalOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GlobalOutlinedSvg
  }));
};
GlobalOutlined.displayName = 'GlobalOutlined';
export default /*#__PURE__*/React.forwardRef(GlobalOutlined);