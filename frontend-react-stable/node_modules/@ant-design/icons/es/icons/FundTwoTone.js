import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FundTwoToneSvg from "@ant-design/icons-svg/es/asn/FundTwoTone";
import AntdIcon from '../components/AntdIcon';
var FundTwoTone = function FundTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FundTwoToneSvg
  }));
};
FundTwoTone.displayName = 'FundTwoTone';
export default /*#__PURE__*/React.forwardRef(FundTwoTone);