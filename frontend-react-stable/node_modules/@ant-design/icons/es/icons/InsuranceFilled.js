import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsuranceFilledSvg from "@ant-design/icons-svg/es/asn/InsuranceFilled";
import AntdIcon from '../components/AntdIcon';
var InsuranceFilled = function InsuranceFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsuranceFilledSvg
  }));
};
InsuranceFilled.displayName = 'InsuranceFilled';
export default /*#__PURE__*/React.forwardRef(InsuranceFilled);