import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BoxPlotTwoToneSvg from "@ant-design/icons-svg/es/asn/BoxPlotTwoTone";
import AntdIcon from '../components/AntdIcon';
var BoxPlotTwoTone = function BoxPlotTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BoxPlotTwoToneSvg
  }));
};
BoxPlotTwoTone.displayName = 'BoxPlotTwoTone';
export default /*#__PURE__*/React.forwardRef(BoxPlotTwoTone);