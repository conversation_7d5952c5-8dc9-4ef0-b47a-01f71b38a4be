import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GatewayOutlinedSvg from "@ant-design/icons-svg/es/asn/GatewayOutlined";
import AntdIcon from '../components/AntdIcon';
var GatewayOutlined = function GatewayOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GatewayOutlinedSvg
  }));
};
GatewayOutlined.displayName = 'GatewayOutlined';
export default /*#__PURE__*/React.forwardRef(GatewayOutlined);