import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QqSquareFilledSvg from "@ant-design/icons-svg/es/asn/QqSquareFilled";
import AntdIcon from '../components/AntdIcon';
var QqSquareFilled = function QqSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QqSquareFilledSvg
  }));
};
QqSquareFilled.displayName = 'QqSquareFilled';
export default /*#__PURE__*/React.forwardRef(QqSquareFilled);