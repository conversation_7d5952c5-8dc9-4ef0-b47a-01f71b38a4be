import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeInvisibleFilledSvg from "@ant-design/icons-svg/es/asn/EyeInvisibleFilled";
import AntdIcon from '../components/AntdIcon';
var EyeInvisibleFilled = function EyeInvisibleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeInvisibleFilledSvg
  }));
};
EyeInvisibleFilled.displayName = 'EyeInvisibleFilled';
export default /*#__PURE__*/React.forwardRef(EyeInvisibleFilled);