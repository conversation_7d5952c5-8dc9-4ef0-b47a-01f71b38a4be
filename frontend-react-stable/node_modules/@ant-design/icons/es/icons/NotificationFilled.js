import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NotificationFilledSvg from "@ant-design/icons-svg/es/asn/NotificationFilled";
import AntdIcon from '../components/AntdIcon';
var NotificationFilled = function NotificationFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NotificationFilledSvg
  }));
};
NotificationFilled.displayName = 'NotificationFilled';
export default /*#__PURE__*/React.forwardRef(NotificationFilled);