import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContactsFilledSvg from "@ant-design/icons-svg/es/asn/ContactsFilled";
import AntdIcon from '../components/AntdIcon';
var ContactsFilled = function ContactsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContactsFilledSvg
  }));
};
ContactsFilled.displayName = 'ContactsFilled';
export default /*#__PURE__*/React.forwardRef(ContactsFilled);