import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON><PERSON>huOutlinedSvg from "@ant-design/icons-svg/es/asn/ZhihuOutlined";
import AntdIcon from '../components/AntdIcon';
var ZhihuOutlined = function ZhihuOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ZhihuOutlinedSvg
  }));
};
ZhihuOutlined.displayName = 'ZhihuOutlined';
export default /*#__PURE__*/React.forwardRef(ZhihuOutlined);