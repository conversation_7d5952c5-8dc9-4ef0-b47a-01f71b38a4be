import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ClockCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/ClockCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var ClockCircleTwoTone = function ClockCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ClockCircleTwoToneSvg
  }));
};
ClockCircleTwoTone.displayName = 'ClockCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(ClockCircleTwoTone);