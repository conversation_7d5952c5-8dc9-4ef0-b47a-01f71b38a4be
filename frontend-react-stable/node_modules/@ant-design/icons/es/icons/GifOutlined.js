import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GifOutlinedSvg from "@ant-design/icons-svg/es/asn/GifOutlined";
import AntdIcon from '../components/AntdIcon';
var GifOutlined = function GifOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GifOutlinedSvg
  }));
};
GifOutlined.displayName = 'GifOutlined';
export default /*#__PURE__*/React.forwardRef(GifOutlined);