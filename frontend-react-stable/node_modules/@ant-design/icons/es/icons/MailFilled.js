import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MailFilledSvg from "@ant-design/icons-svg/es/asn/MailFilled";
import AntdIcon from '../components/AntdIcon';
var MailFilled = function MailFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MailFilledSvg
  }));
};
MailFilled.displayName = 'MailFilled';
export default /*#__PURE__*/React.forwardRef(MailFilled);