import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileZipTwoToneSvg from "@ant-design/icons-svg/es/asn/FileZipTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileZipTwoTone = function FileZipTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileZipTwoToneSvg
  }));
};
FileZipTwoTone.displayName = 'FileZipTwoTone';
export default /*#__PURE__*/React.forwardRef(FileZipTwoTone);