import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DribbbleOutlinedSvg from "@ant-design/icons-svg/es/asn/DribbbleOutlined";
import AntdIcon from '../components/AntdIcon';
var DribbbleOutlined = function DribbbleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DribbbleOutlinedSvg
  }));
};
DribbbleOutlined.displayName = 'DribbbleOutlined';
export default /*#__PURE__*/React.forwardRef(DribbbleOutlined);