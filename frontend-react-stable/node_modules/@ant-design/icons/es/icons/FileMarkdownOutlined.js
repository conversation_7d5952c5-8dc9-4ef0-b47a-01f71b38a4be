import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileMarkdownOutlinedSvg from "@ant-design/icons-svg/es/asn/FileMarkdownOutlined";
import AntdIcon from '../components/AntdIcon';
var FileMarkdownOutlined = function FileMarkdownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileMarkdownOutlinedSvg
  }));
};
FileMarkdownOutlined.displayName = 'FileMarkdownOutlined';
export default /*#__PURE__*/React.forwardRef(FileMarkdownOutlined);