import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedEnvelopeOutlinedSvg from "@ant-design/icons-svg/es/asn/RedEnvelopeOutlined";
import AntdIcon from '../components/AntdIcon';
var RedEnvelopeOutlined = function RedEnvelopeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedEnvelopeOutlinedSvg
  }));
};
RedEnvelopeOutlined.displayName = 'RedEnvelopeOutlined';
export default /*#__PURE__*/React.forwardRef(RedEnvelopeOutlined);