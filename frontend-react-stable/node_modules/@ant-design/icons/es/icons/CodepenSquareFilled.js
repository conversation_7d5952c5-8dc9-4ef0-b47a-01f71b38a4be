import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodepenSquareFilledSvg from "@ant-design/icons-svg/es/asn/CodepenSquareFilled";
import AntdIcon from '../components/AntdIcon';
var CodepenSquareFilled = function CodepenSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodepenSquareFilledSvg
  }));
};
CodepenSquareFilled.displayName = 'CodepenSquareFilled';
export default /*#__PURE__*/React.forwardRef(CodepenSquareFilled);