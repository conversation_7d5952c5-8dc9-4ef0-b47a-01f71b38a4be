import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NotificationTwoToneSvg from "@ant-design/icons-svg/es/asn/NotificationTwoTone";
import AntdIcon from '../components/AntdIcon';
var NotificationTwoTone = function NotificationTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NotificationTwoToneSvg
  }));
};
NotificationTwoTone.displayName = 'NotificationTwoTone';
export default /*#__PURE__*/React.forwardRef(NotificationTwoTone);