import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var CloseSquareOutlined = function CloseSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseSquareOutlinedSvg
  }));
};
CloseSquareOutlined.displayName = 'CloseSquareOutlined';
export default /*#__PURE__*/React.forwardRef(CloseSquareOutlined);