import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SyncOutlinedSvg from "@ant-design/icons-svg/es/asn/SyncOutlined";
import AntdIcon from '../components/AntdIcon';
var SyncOutlined = function SyncOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SyncOutlinedSvg
  }));
};
SyncOutlined.displayName = 'SyncOutlined';
export default /*#__PURE__*/React.forwardRef(SyncOutlined);