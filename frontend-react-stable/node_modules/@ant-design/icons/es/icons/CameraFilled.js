import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CameraFilledSvg from "@ant-design/icons-svg/es/asn/CameraFilled";
import AntdIcon from '../components/AntdIcon';
var CameraFilled = function CameraFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CameraFilledSvg
  }));
};
CameraFilled.displayName = 'CameraFilled';
export default /*#__PURE__*/React.forwardRef(CameraFilled);