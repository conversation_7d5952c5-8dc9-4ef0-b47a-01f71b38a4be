import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DollarCircleFilledSvg from "@ant-design/icons-svg/es/asn/DollarCircleFilled";
import AntdIcon from '../components/AntdIcon';
var DollarCircleFilled = function DollarCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DollarCircleFilledSvg
  }));
};
DollarCircleFilled.displayName = 'DollarCircleFilled';
export default /*#__PURE__*/React.forwardRef(DollarCircleFilled);