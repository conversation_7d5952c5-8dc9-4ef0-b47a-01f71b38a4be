import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerticalLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var VerticalLeftOutlined = function VerticalLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerticalLeftOutlinedSvg
  }));
};
VerticalLeftOutlined.displayName = 'VerticalLeftOutlined';
export default /*#__PURE__*/React.forwardRef(VerticalLeftOutlined);