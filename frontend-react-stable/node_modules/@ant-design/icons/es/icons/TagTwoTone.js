import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagTwoToneSvg from "@ant-design/icons-svg/es/asn/TagTwoTone";
import AntdIcon from '../components/AntdIcon';
var TagTwoTone = function TagTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagTwoToneSvg
  }));
};
TagTwoTone.displayName = 'TagTwoTone';
export default /*#__PURE__*/React.forwardRef(TagTwoTone);