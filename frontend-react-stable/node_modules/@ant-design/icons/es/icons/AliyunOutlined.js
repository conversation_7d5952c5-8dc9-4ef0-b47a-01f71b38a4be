import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON>yunOutlinedSvg from "@ant-design/icons-svg/es/asn/<PERSON>yunOutlined";
import AntdIcon from '../components/AntdIcon';
var AliyunOutlined = function AliyunOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: <PERSON>yunOutlinedSvg
  }));
};
AliyunOutlined.displayName = 'AliyunOutlined';
export default /*#__PURE__*/React.forwardRef(AliyunOutlined);