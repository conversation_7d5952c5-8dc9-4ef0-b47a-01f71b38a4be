import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SaveFilledSvg from "@ant-design/icons-svg/es/asn/SaveFilled";
import AntdIcon from '../components/AntdIcon';
var SaveFilled = function SaveFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SaveFilledSvg
  }));
};
SaveFilled.displayName = 'SaveFilled';
export default /*#__PURE__*/React.forwardRef(SaveFilled);