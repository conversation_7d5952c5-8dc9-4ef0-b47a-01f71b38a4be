import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TaobaoCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/TaobaoCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var TaobaoCircleOutlined = function TaobaoCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TaobaoCircleOutlinedSvg
  }));
};
TaobaoCircleOutlined.displayName = 'TaobaoCircleOutlined';
export default /*#__PURE__*/React.forwardRef(TaobaoCircleOutlined);