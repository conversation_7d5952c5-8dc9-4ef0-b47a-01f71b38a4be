import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContainerTwoToneSvg from "@ant-design/icons-svg/es/asn/ContainerTwoTone";
import AntdIcon from '../components/AntdIcon';
var ContainerTwoTone = function ContainerTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContainerTwoToneSvg
  }));
};
ContainerTwoTone.displayName = 'ContainerTwoTone';
export default /*#__PURE__*/React.forwardRef(ContainerTwoTone);