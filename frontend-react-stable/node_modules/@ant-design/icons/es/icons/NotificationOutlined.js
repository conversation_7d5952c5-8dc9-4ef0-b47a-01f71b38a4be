import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NotificationOutlinedSvg from "@ant-design/icons-svg/es/asn/NotificationOutlined";
import AntdIcon from '../components/AntdIcon';
var NotificationOutlined = function NotificationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NotificationOutlinedSvg
  }));
};
NotificationOutlined.displayName = 'NotificationOutlined';
export default /*#__PURE__*/React.forwardRef(NotificationOutlined);