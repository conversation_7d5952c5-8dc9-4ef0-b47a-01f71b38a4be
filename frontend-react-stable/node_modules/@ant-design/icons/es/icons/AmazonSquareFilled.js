import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AmazonSquareFilledSvg from "@ant-design/icons-svg/es/asn/AmazonSquareFilled";
import AntdIcon from '../components/AntdIcon';
var AmazonSquareFilled = function AmazonSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AmazonSquareFilledSvg
  }));
};
AmazonSquareFilled.displayName = 'AmazonSquareFilled';
export default /*#__PURE__*/React.forwardRef(AmazonSquareFilled);