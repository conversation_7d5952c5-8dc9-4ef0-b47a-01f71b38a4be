import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EditOutlinedSvg from "@ant-design/icons-svg/es/asn/EditOutlined";
import AntdIcon from '../components/AntdIcon';
var EditOutlined = function EditOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EditOutlinedSvg
  }));
};
EditOutlined.displayName = 'EditOutlined';
export default /*#__PURE__*/React.forwardRef(EditOutlined);