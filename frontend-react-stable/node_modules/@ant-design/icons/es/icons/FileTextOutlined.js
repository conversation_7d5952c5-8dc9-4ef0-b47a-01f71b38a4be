import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileTextOutlinedSvg from "@ant-design/icons-svg/es/asn/FileTextOutlined";
import AntdIcon from '../components/AntdIcon';
var FileTextOutlined = function FileTextOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileTextOutlinedSvg
  }));
};
FileTextOutlined.displayName = 'FileTextOutlined';
export default /*#__PURE__*/React.forwardRef(FileTextOutlined);