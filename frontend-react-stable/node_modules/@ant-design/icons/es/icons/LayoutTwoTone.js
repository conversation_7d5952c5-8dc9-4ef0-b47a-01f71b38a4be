import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LayoutTwoToneSvg from "@ant-design/icons-svg/es/asn/LayoutTwoTone";
import AntdIcon from '../components/AntdIcon';
var LayoutTwoTone = function LayoutTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LayoutTwoToneSvg
  }));
};
LayoutTwoTone.displayName = 'LayoutTwoTone';
export default /*#__PURE__*/React.forwardRef(LayoutTwoTone);