import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BugOutlinedSvg from "@ant-design/icons-svg/es/asn/BugOutlined";
import AntdIcon from '../components/AntdIcon';
var BugOutlined = function BugOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BugOutlinedSvg
  }));
};
BugOutlined.displayName = 'BugOutlined';
export default /*#__PURE__*/React.forwardRef(BugOutlined);