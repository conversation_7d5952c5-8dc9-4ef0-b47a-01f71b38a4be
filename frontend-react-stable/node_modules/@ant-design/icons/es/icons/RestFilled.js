import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RestFilledSvg from "@ant-design/icons-svg/es/asn/RestFilled";
import AntdIcon from '../components/AntdIcon';
var RestFilled = function RestFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RestFilledSvg
  }));
};
RestFilled.displayName = 'RestFilled';
export default /*#__PURE__*/React.forwardRef(RestFilled);