import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileGifOutlinedSvg from "@ant-design/icons-svg/es/asn/FileGifOutlined";
import AntdIcon from '../components/AntdIcon';
var FileGifOutlined = function FileGifOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileGifOutlinedSvg
  }));
};
FileGifOutlined.displayName = 'FileGifOutlined';
export default /*#__PURE__*/React.forwardRef(FileGifOutlined);