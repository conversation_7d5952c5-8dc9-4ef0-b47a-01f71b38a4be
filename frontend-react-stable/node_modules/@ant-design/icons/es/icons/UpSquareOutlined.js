import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/UpSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var UpSquareOutlined = function UpSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpSquareOutlinedSvg
  }));
};
UpSquareOutlined.displayName = 'UpSquareOutlined';
export default /*#__PURE__*/React.forwardRef(UpSquareOutlined);