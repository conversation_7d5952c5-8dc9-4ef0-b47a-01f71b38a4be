import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderTwoToneSvg from "@ant-design/icons-svg/es/asn/FolderTwoTone";
import AntdIcon from '../components/AntdIcon';
var FolderTwoTone = function FolderTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderTwoToneSvg
  }));
};
FolderTwoTone.displayName = 'FolderTwoTone';
export default /*#__PURE__*/React.forwardRef(FolderTwoTone);