import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RollbackOutlinedSvg from "@ant-design/icons-svg/es/asn/RollbackOutlined";
import AntdIcon from '../components/AntdIcon';
var RollbackOutlined = function RollbackOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RollbackOutlinedSvg
  }));
};
RollbackOutlined.displayName = 'RollbackOutlined';
export default /*#__PURE__*/React.forwardRef(RollbackOutlined);