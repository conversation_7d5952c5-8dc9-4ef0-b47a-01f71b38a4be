import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EnterOutlinedSvg from "@ant-design/icons-svg/es/asn/EnterOutlined";
import AntdIcon from '../components/AntdIcon';
var EnterOutlined = function EnterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EnterOutlinedSvg
  }));
};
EnterOutlined.displayName = 'EnterOutlined';
export default /*#__PURE__*/React.forwardRef(EnterOutlined);