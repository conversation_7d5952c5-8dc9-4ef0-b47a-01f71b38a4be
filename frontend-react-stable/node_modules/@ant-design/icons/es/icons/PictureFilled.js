import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PictureFilledSvg from "@ant-design/icons-svg/es/asn/PictureFilled";
import AntdIcon from '../components/AntdIcon';
var PictureFilled = function PictureFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PictureFilledSvg
  }));
};
PictureFilled.displayName = 'PictureFilled';
export default /*#__PURE__*/React.forwardRef(PictureFilled);