import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EnvironmentFilledSvg from "@ant-design/icons-svg/es/asn/EnvironmentFilled";
import AntdIcon from '../components/AntdIcon';
var EnvironmentFilled = function EnvironmentFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EnvironmentFilledSvg
  }));
};
EnvironmentFilled.displayName = 'EnvironmentFilled';
export default /*#__PURE__*/React.forwardRef(EnvironmentFilled);