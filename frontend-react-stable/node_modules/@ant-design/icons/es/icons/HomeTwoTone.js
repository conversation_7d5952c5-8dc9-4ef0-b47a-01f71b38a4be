import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HomeTwoToneSvg from "@ant-design/icons-svg/es/asn/HomeTwoTone";
import AntdIcon from '../components/AntdIcon';
var HomeTwoTone = function HomeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HomeTwoToneSvg
  }));
};
HomeTwoTone.displayName = 'HomeTwoTone';
export default /*#__PURE__*/React.forwardRef(HomeTwoTone);