import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON><PERSON><PERSON>OutlinedSvg from "@ant-design/icons-svg/es/asn/DotChartOutlined";
import AntdIcon from '../components/AntdIcon';
var DotChartOutlined = function DotChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: Dot<PERSON>hartOutlinedSvg
  }));
};
DotChartOutlined.displayName = 'DotChartOutlined';
export default /*#__PURE__*/React.forwardRef(DotChartOutlined);