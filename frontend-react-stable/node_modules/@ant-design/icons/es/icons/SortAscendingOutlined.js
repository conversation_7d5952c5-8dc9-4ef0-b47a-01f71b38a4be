import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SortAscendingOutlinedSvg from "@ant-design/icons-svg/es/asn/SortAscendingOutlined";
import AntdIcon from '../components/AntdIcon';
var SortAscendingOutlined = function SortAscendingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SortAscendingOutlinedSvg
  }));
};
SortAscendingOutlined.displayName = 'SortAscendingOutlined';
export default /*#__PURE__*/React.forwardRef(SortAscendingOutlined);