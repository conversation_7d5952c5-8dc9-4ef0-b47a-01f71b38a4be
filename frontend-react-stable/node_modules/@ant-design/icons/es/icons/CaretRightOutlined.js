import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretRightOutlinedSvg from "@ant-design/icons-svg/es/asn/CaretRightOutlined";
import AntdIcon from '../components/AntdIcon';
var CaretRightOutlined = function CaretRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretRightOutlinedSvg
  }));
};
CaretRightOutlined.displayName = 'CaretRightOutlined';
export default /*#__PURE__*/React.forwardRef(CaretRightOutlined);