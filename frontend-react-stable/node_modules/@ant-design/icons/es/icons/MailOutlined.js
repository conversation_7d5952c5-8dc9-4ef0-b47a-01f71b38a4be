import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MailOutlinedSvg from "@ant-design/icons-svg/es/asn/MailOutlined";
import AntdIcon from '../components/AntdIcon';
var MailOutlined = function MailOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MailOutlinedSvg
  }));
};
MailOutlined.displayName = 'MailOutlined';
export default /*#__PURE__*/React.forwardRef(MailOutlined);