import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LikeOutlinedSvg from "@ant-design/icons-svg/es/asn/LikeOutlined";
import AntdIcon from '../components/AntdIcon';
var LikeOutlined = function LikeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LikeOutlinedSvg
  }));
};
LikeOutlined.displayName = 'LikeOutlined';
export default /*#__PURE__*/React.forwardRef(LikeOutlined);