import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ZhihuSquareFilledSvg from "@ant-design/icons-svg/es/asn/ZhihuSquareFilled";
import AntdIcon from '../components/AntdIcon';
var ZhihuSquareFilled = function ZhihuSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ZhihuSquareFilledSvg
  }));
};
ZhihuSquareFilled.displayName = 'ZhihuSquareFilled';
export default /*#__PURE__*/React.forwardRef(ZhihuSquareFilled);