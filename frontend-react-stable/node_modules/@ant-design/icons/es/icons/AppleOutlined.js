import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppleOutlinedSvg from "@ant-design/icons-svg/es/asn/AppleOutlined";
import AntdIcon from '../components/AntdIcon';
var AppleOutlined = function AppleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppleOutlinedSvg
  }));
};
AppleOutlined.displayName = 'AppleOutlined';
export default /*#__PURE__*/React.forwardRef(AppleOutlined);