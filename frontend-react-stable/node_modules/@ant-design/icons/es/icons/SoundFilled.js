import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SoundFilledSvg from "@ant-design/icons-svg/es/asn/SoundFilled";
import AntdIcon from '../components/AntdIcon';
var SoundFilled = function SoundFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SoundFilledSvg
  }));
};
SoundFilled.displayName = 'SoundFilled';
export default /*#__PURE__*/React.forwardRef(SoundFilled);