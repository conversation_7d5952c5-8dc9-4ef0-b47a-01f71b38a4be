import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileJpgOutlinedSvg from "@ant-design/icons-svg/es/asn/FileJpgOutlined";
import AntdIcon from '../components/AntdIcon';
var FileJpgOutlined = function FileJpgOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileJpgOutlinedSvg
  }));
};
FileJpgOutlined.displayName = 'FileJpgOutlined';
export default /*#__PURE__*/React.forwardRef(FileJpgOutlined);