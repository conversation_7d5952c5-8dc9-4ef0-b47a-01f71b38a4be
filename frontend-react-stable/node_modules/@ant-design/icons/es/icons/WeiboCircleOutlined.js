import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WeiboCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/WeiboCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var WeiboCircleOutlined = function WeiboCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WeiboCircleOutlinedSvg
  }));
};
WeiboCircleOutlined.displayName = 'WeiboCircleOutlined';
export default /*#__PURE__*/React.forwardRef(WeiboCircleOutlined);