import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretRightFilledSvg from "@ant-design/icons-svg/es/asn/CaretRightFilled";
import AntdIcon from '../components/AntdIcon';
var CaretRightFilled = function CaretRightFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretRightFilledSvg
  }));
};
CaretRightFilled.displayName = 'CaretRightFilled';
export default /*#__PURE__*/React.forwardRef(CaretRightFilled);