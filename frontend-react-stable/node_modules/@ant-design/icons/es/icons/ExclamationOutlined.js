import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExclamationOutlinedSvg from "@ant-design/icons-svg/es/asn/ExclamationOutlined";
import AntdIcon from '../components/AntdIcon';
var ExclamationOutlined = function ExclamationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExclamationOutlinedSvg
  }));
};
ExclamationOutlined.displayName = 'ExclamationOutlined';
export default /*#__PURE__*/React.forwardRef(ExclamationOutlined);