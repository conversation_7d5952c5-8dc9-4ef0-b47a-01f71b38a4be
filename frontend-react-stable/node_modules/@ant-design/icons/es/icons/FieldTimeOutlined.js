import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FieldTimeOutlinedSvg from "@ant-design/icons-svg/es/asn/FieldTimeOutlined";
import AntdIcon from '../components/AntdIcon';
var FieldTimeOutlined = function FieldTimeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FieldTimeOutlinedSvg
  }));
};
FieldTimeOutlined.displayName = 'FieldTimeOutlined';
export default /*#__PURE__*/React.forwardRef(FieldTimeOutlined);