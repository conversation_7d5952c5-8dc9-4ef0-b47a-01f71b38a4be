import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretDownOutlinedSvg from "@ant-design/icons-svg/es/asn/CaretDownOutlined";
import AntdIcon from '../components/AntdIcon';
var CaretDownOutlined = function CaretDownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretDownOutlinedSvg
  }));
};
CaretDownOutlined.displayName = 'CaretDownOutlined';
export default /*#__PURE__*/React.forwardRef(CaretDownOutlined);