import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PaperClipOutlinedSvg from "@ant-design/icons-svg/es/asn/PaperClipOutlined";
import AntdIcon from '../components/AntdIcon';
var PaperClipOutlined = function PaperClipOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PaperClipOutlinedSvg
  }));
};
PaperClipOutlined.displayName = 'PaperClipOutlined';
export default /*#__PURE__*/React.forwardRef(PaperClipOutlined);