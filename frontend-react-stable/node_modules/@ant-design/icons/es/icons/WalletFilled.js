import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WalletFilledSvg from "@ant-design/icons-svg/es/asn/WalletFilled";
import AntdIcon from '../components/AntdIcon';
var WalletFilled = function WalletFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WalletFilledSvg
  }));
};
WalletFilled.displayName = 'WalletFilled';
export default /*#__PURE__*/React.forwardRef(WalletFilled);