import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HomeFilledSvg from "@ant-design/icons-svg/es/asn/HomeFilled";
import AntdIcon from '../components/AntdIcon';
var HomeFilled = function HomeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HomeFilledSvg
  }));
};
HomeFilled.displayName = 'HomeFilled';
export default /*#__PURE__*/React.forwardRef(HomeFilled);