import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ScanOutlinedSvg from "@ant-design/icons-svg/es/asn/ScanOutlined";
import AntdIcon from '../components/AntdIcon';
var ScanOutlined = function ScanOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ScanOutlinedSvg
  }));
};
ScanOutlined.displayName = 'ScanOutlined';
export default /*#__PURE__*/React.forwardRef(ScanOutlined);