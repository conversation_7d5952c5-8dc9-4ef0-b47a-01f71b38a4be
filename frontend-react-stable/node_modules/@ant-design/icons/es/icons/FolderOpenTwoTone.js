import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderOpenTwoToneSvg from "@ant-design/icons-svg/es/asn/FolderOpenTwoTone";
import AntdIcon from '../components/AntdIcon';
var FolderOpenTwoTone = function FolderOpenTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderOpenTwoToneSvg
  }));
};
FolderOpenTwoTone.displayName = 'FolderOpenTwoTone';
export default /*#__PURE__*/React.forwardRef(FolderOpenTwoTone);