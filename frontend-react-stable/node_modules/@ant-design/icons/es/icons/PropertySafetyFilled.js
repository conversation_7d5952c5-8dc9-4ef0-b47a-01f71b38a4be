import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PropertySafetyFilledSvg from "@ant-design/icons-svg/es/asn/PropertySafetyFilled";
import AntdIcon from '../components/AntdIcon';
var PropertySafetyFilled = function PropertySafetyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PropertySafetyFilledSvg
  }));
};
PropertySafetyFilled.displayName = 'PropertySafetyFilled';
export default /*#__PURE__*/React.forwardRef(PropertySafetyFilled);