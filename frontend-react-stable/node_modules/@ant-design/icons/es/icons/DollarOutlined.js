import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DollarOutlinedSvg from "@ant-design/icons-svg/es/asn/DollarOutlined";
import AntdIcon from '../components/AntdIcon';
var DollarOutlined = function DollarOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DollarOutlinedSvg
  }));
};
DollarOutlined.displayName = 'DollarOutlined';
export default /*#__PURE__*/React.forwardRef(DollarOutlined);