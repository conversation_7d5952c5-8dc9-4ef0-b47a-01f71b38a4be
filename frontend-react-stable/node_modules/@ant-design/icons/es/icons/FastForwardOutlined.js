import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FastForwardOutlinedSvg from "@ant-design/icons-svg/es/asn/FastForwardOutlined";
import AntdIcon from '../components/AntdIcon';
var FastForwardOutlined = function FastForwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FastForwardOutlinedSvg
  }));
};
FastForwardOutlined.displayName = 'FastForwardOutlined';
export default /*#__PURE__*/React.forwardRef(FastForwardOutlined);