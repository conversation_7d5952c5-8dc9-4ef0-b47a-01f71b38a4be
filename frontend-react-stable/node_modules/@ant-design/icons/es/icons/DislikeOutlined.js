import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DislikeOutlinedSvg from "@ant-design/icons-svg/es/asn/DislikeOutlined";
import AntdIcon from '../components/AntdIcon';
var DislikeOutlined = function DislikeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DislikeOutlinedSvg
  }));
};
DislikeOutlined.displayName = 'DislikeOutlined';
export default /*#__PURE__*/React.forwardRef(DislikeOutlined);