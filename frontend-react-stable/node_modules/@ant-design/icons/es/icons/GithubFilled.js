import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GithubFilledSvg from "@ant-design/icons-svg/es/asn/GithubFilled";
import AntdIcon from '../components/AntdIcon';
var GithubFilled = function GithubFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GithubFilledSvg
  }));
};
GithubFilled.displayName = 'GithubFilled';
export default /*#__PURE__*/React.forwardRef(GithubFilled);