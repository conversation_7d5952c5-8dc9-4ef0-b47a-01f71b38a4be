import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckOutlinedSvg from "@ant-design/icons-svg/es/asn/CheckOutlined";
import AntdIcon from '../components/AntdIcon';
var CheckOutlined = function CheckOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckOutlinedSvg
  }));
};
CheckOutlined.displayName = 'CheckOutlined';
export default /*#__PURE__*/React.forwardRef(CheckOutlined);