import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/CloseSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var CloseSquareTwoTone = function CloseSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseSquareTwoToneSvg
  }));
};
CloseSquareTwoTone.displayName = 'CloseSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(CloseSquareTwoTone);