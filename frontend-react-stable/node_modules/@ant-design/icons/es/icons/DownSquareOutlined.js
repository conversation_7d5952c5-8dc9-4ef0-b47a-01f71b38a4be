import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/DownSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var DownSquareOutlined = function DownSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownSquareOutlinedSvg
  }));
};
DownSquareOutlined.displayName = 'DownSquareOutlined';
export default /*#__PURE__*/React.forwardRef(DownSquareOutlined);