import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InsuranceOutlinedSvg from "@ant-design/icons-svg/es/asn/InsuranceOutlined";
import AntdIcon from '../components/AntdIcon';
var InsuranceOutlined = function InsuranceOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InsuranceOutlinedSvg
  }));
};
InsuranceOutlined.displayName = 'InsuranceOutlined';
export default /*#__PURE__*/React.forwardRef(InsuranceOutlined);