import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ArrowUpOutlinedSvg from "@ant-design/icons-svg/es/asn/ArrowUpOutlined";
import AntdIcon from '../components/AntdIcon';
var ArrowUpOutlined = function ArrowUpOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ArrowUpOutlinedSvg
  }));
};
ArrowUpOutlined.displayName = 'ArrowUpOutlined';
export default /*#__PURE__*/React.forwardRef(ArrowUpOutlined);