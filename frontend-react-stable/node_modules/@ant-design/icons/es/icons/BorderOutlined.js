import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderOutlined = function BorderOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderOutlinedSvg
  }));
};
BorderOutlined.displayName = 'BorderOutlined';
export default /*#__PURE__*/React.forwardRef(BorderOutlined);