import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoldOutlinedSvg from "@ant-design/icons-svg/es/asn/GoldOutlined";
import AntdIcon from '../components/AntdIcon';
var GoldOutlined = function GoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoldOutlinedSvg
  }));
};
GoldOutlined.displayName = 'GoldOutlined';
export default /*#__PURE__*/React.forwardRef(GoldOutlined);