import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GroupOutlinedSvg from "@ant-design/icons-svg/es/asn/GroupOutlined";
import AntdIcon from '../components/AntdIcon';
var GroupOutlined = function GroupOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GroupOutlinedSvg
  }));
};
GroupOutlined.displayName = 'GroupOutlined';
export default /*#__PURE__*/React.forwardRef(GroupOutlined);