import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EnvironmentOutlinedSvg from "@ant-design/icons-svg/es/asn/EnvironmentOutlined";
import AntdIcon from '../components/AntdIcon';
var EnvironmentOutlined = function EnvironmentOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EnvironmentOutlinedSvg
  }));
};
EnvironmentOutlined.displayName = 'EnvironmentOutlined';
export default /*#__PURE__*/React.forwardRef(EnvironmentOutlined);