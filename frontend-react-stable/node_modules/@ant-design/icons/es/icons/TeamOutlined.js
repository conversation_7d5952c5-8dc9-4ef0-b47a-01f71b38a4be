import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TeamOutlinedSvg from "@ant-design/icons-svg/es/asn/TeamOutlined";
import AntdIcon from '../components/AntdIcon';
var TeamOutlined = function TeamOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TeamOutlinedSvg
  }));
};
TeamOutlined.displayName = 'TeamOutlined';
export default /*#__PURE__*/React.forwardRef(TeamOutlined);