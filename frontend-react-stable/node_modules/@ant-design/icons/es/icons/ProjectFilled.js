import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProjectFilledSvg from "@ant-design/icons-svg/es/asn/ProjectFilled";
import AntdIcon from '../components/AntdIcon';
var ProjectFilled = function ProjectFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ProjectFilledSvg
  }));
};
ProjectFilled.displayName = 'ProjectFilled';
export default /*#__PURE__*/React.forwardRef(ProjectFilled);