import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeTwoToneSvg from "@ant-design/icons-svg/es/asn/CodeTwoTone";
import AntdIcon from '../components/AntdIcon';
var CodeTwoTone = function CodeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeTwoToneSvg
  }));
};
CodeTwoTone.displayName = 'CodeTwoTone';
export default /*#__PURE__*/React.forwardRef(CodeTwoTone);