import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NodeExpandOutlinedSvg from "@ant-design/icons-svg/es/asn/NodeExpandOutlined";
import AntdIcon from '../components/AntdIcon';
var NodeExpandOutlined = function NodeExpandOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NodeExpandOutlinedSvg
  }));
};
NodeExpandOutlined.displayName = 'NodeExpandOutlined';
export default /*#__PURE__*/React.forwardRef(NodeExpandOutlined);