import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightSquareFilledSvg from "@ant-design/icons-svg/es/asn/RightSquareFilled";
import AntdIcon from '../components/AntdIcon';
var RightSquareFilled = function RightSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightSquareFilledSvg
  }));
};
RightSquareFilled.displayName = 'RightSquareFilled';
export default /*#__PURE__*/React.forwardRef(RightSquareFilled);