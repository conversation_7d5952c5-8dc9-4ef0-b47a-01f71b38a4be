import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WeiboSquareFilledSvg from "@ant-design/icons-svg/es/asn/WeiboSquareFilled";
import AntdIcon from '../components/AntdIcon';
var WeiboSquareFilled = function WeiboSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WeiboSquareFilledSvg
  }));
};
WeiboSquareFilled.displayName = 'WeiboSquareFilled';
export default /*#__PURE__*/React.forwardRef(WeiboSquareFilled);