import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlignCenterOutlinedSvg from "@ant-design/icons-svg/es/asn/AlignCenterOutlined";
import AntdIcon from '../components/AntdIcon';
var AlignCenterOutlined = function AlignCenterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlignCenterOutlinedSvg
  }));
};
AlignCenterOutlined.displayName = 'AlignCenterOutlined';
export default /*#__PURE__*/React.forwardRef(AlignCenterOutlined);