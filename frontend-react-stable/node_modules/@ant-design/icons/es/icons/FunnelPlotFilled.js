import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FunnelPlotFilledSvg from "@ant-design/icons-svg/es/asn/FunnelPlotFilled";
import AntdIcon from '../components/AntdIcon';
var FunnelPlotFilled = function FunnelPlotFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FunnelPlotFilledSvg
  }));
};
FunnelPlotFilled.displayName = 'FunnelPlotFilled';
export default /*#__PURE__*/React.forwardRef(FunnelPlotFilled);