import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiOutlinedSvg from "@ant-design/icons-svg/es/asn/CiOutlined";
import AntdIcon from '../components/AntdIcon';
var CiOutlined = function CiOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiOutlinedSvg
  }));
};
CiOutlined.displayName = 'CiOutlined';
export default /*#__PURE__*/React.forwardRef(CiOutlined);