import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AccountBookTwoToneSvg from "@ant-design/icons-svg/es/asn/AccountBookTwoTone";
import AntdIcon from '../components/AntdIcon';
var AccountBookTwoTone = function AccountBookTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AccountBookTwoToneSvg
  }));
};
AccountBookTwoTone.displayName = 'AccountBookTwoTone';
export default /*#__PURE__*/React.forwardRef(AccountBookTwoTone);