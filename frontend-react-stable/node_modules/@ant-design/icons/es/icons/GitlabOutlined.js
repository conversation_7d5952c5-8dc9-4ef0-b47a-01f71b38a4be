import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GitlabOutlinedSvg from "@ant-design/icons-svg/es/asn/GitlabOutlined";
import AntdIcon from '../components/AntdIcon';
var GitlabOutlined = function GitlabOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GitlabOutlinedSvg
  }));
};
GitlabOutlined.displayName = 'GitlabOutlined';
export default /*#__PURE__*/React.forwardRef(GitlabOutlined);