import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlaySquareTwoToneSvg from "@ant-design/icons-svg/es/asn/PlaySquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var PlaySquareTwoTone = function PlaySquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlaySquareTwoToneSvg
  }));
};
PlaySquareTwoTone.displayName = 'PlaySquareTwoTone';
export default /*#__PURE__*/React.forwardRef(PlaySquareTwoTone);