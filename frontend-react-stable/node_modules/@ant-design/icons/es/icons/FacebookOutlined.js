import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FacebookOutlinedSvg from "@ant-design/icons-svg/es/asn/FacebookOutlined";
import AntdIcon from '../components/AntdIcon';
var FacebookOutlined = function FacebookOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FacebookOutlinedSvg
  }));
};
FacebookOutlined.displayName = 'FacebookOutlined';
export default /*#__PURE__*/React.forwardRef(FacebookOutlined);