import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AmazonCircleFilledSvg from "@ant-design/icons-svg/es/asn/AmazonCircleFilled";
import AntdIcon from '../components/AntdIcon';
var AmazonCircleFilled = function AmazonCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AmazonCircleFilledSvg
  }));
};
AmazonCircleFilled.displayName = 'AmazonCircleFilled';
export default /*#__PURE__*/React.forwardRef(AmazonCircleFilled);