import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CrownOutlinedSvg from "@ant-design/icons-svg/es/asn/CrownOutlined";
import AntdIcon from '../components/AntdIcon';
var CrownOutlined = function CrownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CrownOutlinedSvg
  }));
};
CrownOutlined.displayName = 'CrownOutlined';
export default /*#__PURE__*/React.forwardRef(CrownOutlined);