import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EditFilledSvg from "@ant-design/icons-svg/es/asn/EditFilled";
import AntdIcon from '../components/AntdIcon';
var EditFilled = function EditFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EditFilledSvg
  }));
};
EditFilled.displayName = 'EditFilled';
export default /*#__PURE__*/React.forwardRef(EditFilled);