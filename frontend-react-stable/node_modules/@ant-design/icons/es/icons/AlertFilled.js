import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlertFilledSvg from "@ant-design/icons-svg/es/asn/AlertFilled";
import AntdIcon from '../components/AntdIcon';
var AlertFilled = function AlertFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlertFilledSvg
  }));
};
AlertFilled.displayName = 'AlertFilled';
export default /*#__PURE__*/React.forwardRef(AlertFilled);