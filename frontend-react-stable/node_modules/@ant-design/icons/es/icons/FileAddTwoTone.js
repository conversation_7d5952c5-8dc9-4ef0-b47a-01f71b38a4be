import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileAddTwoToneSvg from "@ant-design/icons-svg/es/asn/FileAddTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileAddTwoTone = function FileAddTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileAddTwoToneSvg
  }));
};
FileAddTwoTone.displayName = 'FileAddTwoTone';
export default /*#__PURE__*/React.forwardRef(FileAddTwoTone);