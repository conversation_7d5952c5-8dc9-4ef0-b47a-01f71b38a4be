import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiTwoToneSvg from "@ant-design/icons-svg/es/asn/CiTwoTone";
import AntdIcon from '../components/AntdIcon';
var CiTwoTone = function CiTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiTwoToneSvg
  }));
};
CiTwoTone.displayName = 'CiTwoTone';
export default /*#__PURE__*/React.forwardRef(CiTwoTone);