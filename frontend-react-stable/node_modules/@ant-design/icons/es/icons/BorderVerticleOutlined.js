import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderVerticleOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderVerticleOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderVerticleOutlined = function BorderVerticleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderVerticleOutlinedSvg
  }));
};
BorderVerticleOutlined.displayName = 'BorderVerticleOutlined';
export default /*#__PURE__*/React.forwardRef(BorderVerticleOutlined);