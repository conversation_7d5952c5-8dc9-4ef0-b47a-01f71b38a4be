import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PlusCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PlusCircleOutlined = function PlusCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlusCircleOutlinedSvg
  }));
};
PlusCircleOutlined.displayName = 'PlusCircleOutlined';
export default /*#__PURE__*/React.forwardRef(PlusCircleOutlined);