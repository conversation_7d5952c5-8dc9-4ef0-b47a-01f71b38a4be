import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExpandAltOutlinedSvg from "@ant-design/icons-svg/es/asn/ExpandAltOutlined";
import AntdIcon from '../components/AntdIcon';
var ExpandAltOutlined = function ExpandAltOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExpandAltOutlinedSvg
  }));
};
ExpandAltOutlined.displayName = 'ExpandAltOutlined';
export default /*#__PURE__*/React.forwardRef(ExpandAltOutlined);