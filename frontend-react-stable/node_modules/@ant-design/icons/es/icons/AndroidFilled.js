import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AndroidFilledSvg from "@ant-design/icons-svg/es/asn/AndroidFilled";
import AntdIcon from '../components/AntdIcon';
var AndroidFilled = function AndroidFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AndroidFilledSvg
  }));
};
AndroidFilled.displayName = 'AndroidFilled';
export default /*#__PURE__*/React.forwardRef(AndroidFilled);