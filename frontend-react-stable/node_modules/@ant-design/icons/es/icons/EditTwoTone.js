import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EditTwoToneSvg from "@ant-design/icons-svg/es/asn/EditTwoTone";
import AntdIcon from '../components/AntdIcon';
var EditTwoTone = function EditTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EditTwoToneSvg
  }));
};
EditTwoTone.displayName = 'EditTwoTone';
export default /*#__PURE__*/React.forwardRef(EditTwoTone);