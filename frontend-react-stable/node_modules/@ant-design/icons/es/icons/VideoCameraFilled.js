import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VideoCameraFilledSvg from "@ant-design/icons-svg/es/asn/VideoCameraFilled";
import AntdIcon from '../components/AntdIcon';
var VideoCameraFilled = function VideoCameraFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VideoCameraFilledSvg
  }));
};
VideoCameraFilled.displayName = 'VideoCameraFilled';
export default /*#__PURE__*/React.forwardRef(VideoCameraFilled);