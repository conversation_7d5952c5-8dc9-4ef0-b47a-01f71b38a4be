import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudTwoToneSvg from "@ant-design/icons-svg/es/asn/CloudTwoTone";
import AntdIcon from '../components/AntdIcon';
var CloudTwoTone = function CloudTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudTwoToneSvg
  }));
};
CloudTwoTone.displayName = 'CloudTwoTone';
export default /*#__PURE__*/React.forwardRef(CloudTwoTone);