import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UploadOutlinedSvg from "@ant-design/icons-svg/es/asn/UploadOutlined";
import AntdIcon from '../components/AntdIcon';
var UploadOutlined = function UploadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UploadOutlinedSvg
  }));
};
UploadOutlined.displayName = 'UploadOutlined';
export default /*#__PURE__*/React.forwardRef(UploadOutlined);