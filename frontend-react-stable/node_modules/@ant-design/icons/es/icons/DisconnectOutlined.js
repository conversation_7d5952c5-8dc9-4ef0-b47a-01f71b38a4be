import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DisconnectOutlinedSvg from "@ant-design/icons-svg/es/asn/DisconnectOutlined";
import AntdIcon from '../components/AntdIcon';
var DisconnectOutlined = function DisconnectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DisconnectOutlinedSvg
  }));
};
DisconnectOutlined.displayName = 'DisconnectOutlined';
export default /*#__PURE__*/React.forwardRef(DisconnectOutlined);