import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PoundCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PoundCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var PoundCircleTwoTone = function PoundCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PoundCircleTwoToneSvg
  }));
};
PoundCircleTwoTone.displayName = 'PoundCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(PoundCircleTwoTone);