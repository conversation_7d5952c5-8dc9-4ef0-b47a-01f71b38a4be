import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TranslationOutlinedSvg from "@ant-design/icons-svg/es/asn/TranslationOutlined";
import AntdIcon from '../components/AntdIcon';
var TranslationOutlined = function TranslationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TranslationOutlinedSvg
  }));
};
TranslationOutlined.displayName = 'TranslationOutlined';
export default /*#__PURE__*/React.forwardRef(TranslationOutlined);