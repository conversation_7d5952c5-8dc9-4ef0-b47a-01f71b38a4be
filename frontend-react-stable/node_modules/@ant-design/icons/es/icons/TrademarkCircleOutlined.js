import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrademarkCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/TrademarkCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var TrademarkCircleOutlined = function TrademarkCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrademarkCircleOutlinedSvg
  }));
};
TrademarkCircleOutlined.displayName = 'TrademarkCircleOutlined';
export default /*#__PURE__*/React.forwardRef(TrademarkCircleOutlined);