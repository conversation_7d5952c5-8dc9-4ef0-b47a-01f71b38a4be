import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QuestionOutlinedSvg from "@ant-design/icons-svg/es/asn/QuestionOutlined";
import AntdIcon from '../components/AntdIcon';
var QuestionOutlined = function QuestionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QuestionOutlinedSvg
  }));
};
QuestionOutlined.displayName = 'QuestionOutlined';
export default /*#__PURE__*/React.forwardRef(QuestionOutlined);