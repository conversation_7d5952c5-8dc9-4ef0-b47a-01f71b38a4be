import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AudioFilledSvg from "@ant-design/icons-svg/es/asn/AudioFilled";
import AntdIcon from '../components/AntdIcon';
var AudioFilled = function AudioFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AudioFilledSvg
  }));
};
AudioFilled.displayName = 'AudioFilled';
export default /*#__PURE__*/React.forwardRef(AudioFilled);