import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FireTwoToneSvg from "@ant-design/icons-svg/es/asn/FireTwoTone";
import AntdIcon from '../components/AntdIcon';
var FireTwoTone = function FireTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FireTwoToneSvg
  }));
};
FireTwoTone.displayName = 'FireTwoTone';
export default /*#__PURE__*/React.forwardRef(FireTwoTone);