import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExcelOutlinedSvg from "@ant-design/icons-svg/es/asn/FileExcelOutlined";
import AntdIcon from '../components/AntdIcon';
var FileExcelOutlined = function FileExcelOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExcelOutlinedSvg
  }));
};
FileExcelOutlined.displayName = 'FileExcelOutlined';
export default /*#__PURE__*/React.forwardRef(FileExcelOutlined);