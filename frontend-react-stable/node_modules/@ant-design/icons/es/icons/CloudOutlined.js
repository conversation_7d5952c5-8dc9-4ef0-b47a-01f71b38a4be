import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloudOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudOutlined";
import AntdIcon from '../components/AntdIcon';
var CloudOutlined = function CloudOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloudOutlinedSvg
  }));
};
CloudOutlined.displayName = 'CloudOutlined';
export default /*#__PURE__*/React.forwardRef(CloudOutlined);