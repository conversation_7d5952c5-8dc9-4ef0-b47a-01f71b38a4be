import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ImportOutlinedSvg from "@ant-design/icons-svg/es/asn/ImportOutlined";
import AntdIcon from '../components/AntdIcon';
var ImportOutlined = function ImportOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ImportOutlinedSvg
  }));
};
ImportOutlined.displayName = 'ImportOutlined';
export default /*#__PURE__*/React.forwardRef(ImportOutlined);