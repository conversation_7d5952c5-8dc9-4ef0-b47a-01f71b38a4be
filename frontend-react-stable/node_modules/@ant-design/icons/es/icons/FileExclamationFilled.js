import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExclamationFilledSvg from "@ant-design/icons-svg/es/asn/FileExclamationFilled";
import AntdIcon from '../components/AntdIcon';
var FileExclamationFilled = function FileExclamationFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExclamationFilledSvg
  }));
};
FileExclamationFilled.displayName = 'FileExclamationFilled';
export default /*#__PURE__*/React.forwardRef(FileExclamationFilled);