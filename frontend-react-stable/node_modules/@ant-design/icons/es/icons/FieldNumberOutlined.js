import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FieldNumberOutlinedSvg from "@ant-design/icons-svg/es/asn/FieldNumberOutlined";
import AntdIcon from '../components/AntdIcon';
var FieldNumberOutlined = function FieldNumberOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FieldNumberOutlinedSvg
  }));
};
FieldNumberOutlined.displayName = 'FieldNumberOutlined';
export default /*#__PURE__*/React.forwardRef(FieldNumberOutlined);