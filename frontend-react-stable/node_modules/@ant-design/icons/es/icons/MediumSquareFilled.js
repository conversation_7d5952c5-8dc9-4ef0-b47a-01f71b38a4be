import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MediumSquareFilledSvg from "@ant-design/icons-svg/es/asn/MediumSquareFilled";
import AntdIcon from '../components/AntdIcon';
var MediumSquareFilled = function MediumSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MediumSquareFilledSvg
  }));
};
MediumSquareFilled.displayName = 'MediumSquareFilled';
export default /*#__PURE__*/React.forwardRef(MediumSquareFilled);