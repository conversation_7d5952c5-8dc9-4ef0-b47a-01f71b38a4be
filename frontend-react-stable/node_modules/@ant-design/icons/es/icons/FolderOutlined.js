import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderOutlinedSvg from "@ant-design/icons-svg/es/asn/FolderOutlined";
import AntdIcon from '../components/AntdIcon';
var FolderOutlined = function FolderOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderOutlinedSvg
  }));
};
FolderOutlined.displayName = 'FolderOutlined';
export default /*#__PURE__*/React.forwardRef(FolderOutlined);