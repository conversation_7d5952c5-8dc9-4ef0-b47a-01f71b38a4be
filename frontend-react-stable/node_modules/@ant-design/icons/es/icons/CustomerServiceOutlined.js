import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CustomerServiceOutlinedSvg from "@ant-design/icons-svg/es/asn/CustomerServiceOutlined";
import AntdIcon from '../components/AntdIcon';
var CustomerServiceOutlined = function CustomerServiceOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CustomerServiceOutlinedSvg
  }));
};
CustomerServiceOutlined.displayName = 'CustomerServiceOutlined';
export default /*#__PURE__*/React.forwardRef(CustomerServiceOutlined);