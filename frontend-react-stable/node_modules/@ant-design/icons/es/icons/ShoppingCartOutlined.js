import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingCartOutlinedSvg from "@ant-design/icons-svg/es/asn/ShoppingCartOutlined";
import AntdIcon from '../components/AntdIcon';
var ShoppingCartOutlined = function ShoppingCartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingCartOutlinedSvg
  }));
};
ShoppingCartOutlined.displayName = 'ShoppingCartOutlined';
export default /*#__PURE__*/React.forwardRef(ShoppingCartOutlined);