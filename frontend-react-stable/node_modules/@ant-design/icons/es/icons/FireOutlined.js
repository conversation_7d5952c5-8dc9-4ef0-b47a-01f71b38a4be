import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FireOutlinedSvg from "@ant-design/icons-svg/es/asn/FireOutlined";
import AntdIcon from '../components/AntdIcon';
var FireOutlined = function FireOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FireOutlinedSvg
  }));
};
FireOutlined.displayName = 'FireOutlined';
export default /*#__PURE__*/React.forwardRef(FireOutlined);