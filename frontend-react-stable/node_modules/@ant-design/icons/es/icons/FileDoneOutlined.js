import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileDoneOutlinedSvg from "@ant-design/icons-svg/es/asn/FileDoneOutlined";
import AntdIcon from '../components/AntdIcon';
var FileDoneOutlined = function FileDoneOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileDoneOutlinedSvg
  }));
};
FileDoneOutlined.displayName = 'FileDoneOutlined';
export default /*#__PURE__*/React.forwardRef(FileDoneOutlined);