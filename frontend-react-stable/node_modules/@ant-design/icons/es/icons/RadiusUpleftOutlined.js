import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RadiusUpleftOutlinedSvg from "@ant-design/icons-svg/es/asn/RadiusUpleftOutlined";
import AntdIcon from '../components/AntdIcon';
var RadiusUpleftOutlined = function RadiusUpleftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadiusUpleftOutlinedSvg
  }));
};
RadiusUpleftOutlined.displayName = 'RadiusUpleftOutlined';
export default /*#__PURE__*/React.forwardRef(RadiusUpleftOutlined);