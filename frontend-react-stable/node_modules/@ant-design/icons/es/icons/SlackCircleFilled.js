import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackCircleFilledSvg from "@ant-design/icons-svg/es/asn/SlackCircleFilled";
import AntdIcon from '../components/AntdIcon';
var SlackCircleFilled = function SlackCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackCircleFilledSvg
  }));
};
SlackCircleFilled.displayName = 'SlackCircleFilled';
export default /*#__PURE__*/React.forwardRef(SlackCircleFilled);