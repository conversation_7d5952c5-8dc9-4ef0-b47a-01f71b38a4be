import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrophyFilledSvg from "@ant-design/icons-svg/es/asn/TrophyFilled";
import AntdIcon from '../components/AntdIcon';
var TrophyFilled = function TrophyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrophyFilledSvg
  }));
};
TrophyFilled.displayName = 'TrophyFilled';
export default /*#__PURE__*/React.forwardRef(TrophyFilled);