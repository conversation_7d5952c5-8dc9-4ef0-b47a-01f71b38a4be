import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MenuFoldOutlinedSvg from "@ant-design/icons-svg/es/asn/MenuFoldOutlined";
import AntdIcon from '../components/AntdIcon';
var MenuFoldOutlined = function MenuFoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MenuFoldOutlinedSvg
  }));
};
MenuFoldOutlined.displayName = 'MenuFoldOutlined';
export default /*#__PURE__*/React.forwardRef(MenuFoldOutlined);