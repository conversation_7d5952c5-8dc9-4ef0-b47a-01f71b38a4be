import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HourglassTwoToneSvg from "@ant-design/icons-svg/es/asn/HourglassTwoTone";
import AntdIcon from '../components/AntdIcon';
var HourglassTwoTone = function HourglassTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HourglassTwoToneSvg
  }));
};
HourglassTwoTone.displayName = 'HourglassTwoTone';
export default /*#__PURE__*/React.forwardRef(HourglassTwoTone);