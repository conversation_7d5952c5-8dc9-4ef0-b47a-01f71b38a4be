import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GooglePlusOutlinedSvg from "@ant-design/icons-svg/es/asn/GooglePlusOutlined";
import AntdIcon from '../components/AntdIcon';
var GooglePlusOutlined = function GooglePlusOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GooglePlusOutlinedSvg
  }));
};
GooglePlusOutlined.displayName = 'GooglePlusOutlined';
export default /*#__PURE__*/React.forwardRef(GooglePlusOutlined);