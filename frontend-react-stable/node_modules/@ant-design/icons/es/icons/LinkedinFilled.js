import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LinkedinFilledSvg from "@ant-design/icons-svg/es/asn/LinkedinFilled";
import AntdIcon from '../components/AntdIcon';
var LinkedinFilled = function LinkedinFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LinkedinFilledSvg
  }));
};
LinkedinFilled.displayName = 'LinkedinFilled';
export default /*#__PURE__*/React.forwardRef(LinkedinFilled);