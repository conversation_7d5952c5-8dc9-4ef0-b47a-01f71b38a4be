import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SendOutlinedSvg from "@ant-design/icons-svg/es/asn/SendOutlined";
import AntdIcon from '../components/AntdIcon';
var SendOutlined = function SendOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SendOutlinedSvg
  }));
};
SendOutlined.displayName = 'SendOutlined';
export default /*#__PURE__*/React.forwardRef(SendOutlined);