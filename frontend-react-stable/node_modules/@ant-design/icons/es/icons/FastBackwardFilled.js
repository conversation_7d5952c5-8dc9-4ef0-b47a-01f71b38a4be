import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FastBackwardFilledSvg from "@ant-design/icons-svg/es/asn/FastBackwardFilled";
import AntdIcon from '../components/AntdIcon';
var FastBackwardFilled = function FastBackwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FastBackwardFilledSvg
  }));
};
FastBackwardFilled.displayName = 'FastBackwardFilled';
export default /*#__PURE__*/React.forwardRef(FastBackwardFilled);