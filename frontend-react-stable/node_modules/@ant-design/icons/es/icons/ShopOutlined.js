import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShopOutlinedSvg from "@ant-design/icons-svg/es/asn/ShopOutlined";
import AntdIcon from '../components/AntdIcon';
var ShopOutlined = function ShopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShopOutlinedSvg
  }));
};
ShopOutlined.displayName = 'ShopOutlined';
export default /*#__PURE__*/React.forwardRef(ShopOutlined);