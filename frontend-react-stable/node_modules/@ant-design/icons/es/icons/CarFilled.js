import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarFilledSvg from "@ant-design/icons-svg/es/asn/CarFilled";
import AntdIcon from '../components/AntdIcon';
var CarFilled = function CarFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarFilledSvg
  }));
};
CarFilled.displayName = 'CarFilled';
export default /*#__PURE__*/React.forwardRef(CarFilled);