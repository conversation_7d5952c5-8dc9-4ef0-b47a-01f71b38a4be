import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TransactionOutlinedSvg from "@ant-design/icons-svg/es/asn/TransactionOutlined";
import AntdIcon from '../components/AntdIcon';
var TransactionOutlined = function TransactionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TransactionOutlinedSvg
  }));
};
TransactionOutlined.displayName = 'TransactionOutlined';
export default /*#__PURE__*/React.forwardRef(TransactionOutlined);