import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UndoOutlinedSvg from "@ant-design/icons-svg/es/asn/UndoOutlined";
import AntdIcon from '../components/AntdIcon';
var UndoOutlined = function UndoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UndoOutlinedSvg
  }));
};
UndoOutlined.displayName = 'UndoOutlined';
export default /*#__PURE__*/React.forwardRef(UndoOutlined);