import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Html5TwoToneSvg from "@ant-design/icons-svg/es/asn/Html5TwoTone";
import AntdIcon from '../components/AntdIcon';
var Html5TwoTone = function Html5TwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: Html5TwoToneSvg
  }));
};
Html5TwoTone.displayName = 'Html5TwoTone';
export default /*#__PURE__*/React.forwardRef(Html5TwoTone);