import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AntDesignOutlinedSvg from "@ant-design/icons-svg/es/asn/AntDesignOutlined";
import AntdIcon from '../components/AntdIcon';
var AntDesignOutlined = function AntDesignOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AntDesignOutlinedSvg
  }));
};
AntDesignOutlined.displayName = 'AntDesignOutlined';
export default /*#__PURE__*/React.forwardRef(AntDesignOutlined);