import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PicLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/PicLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var PicLeftOutlined = function PicLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PicLeftOutlinedSvg
  }));
};
PicLeftOutlined.displayName = 'PicLeftOutlined';
export default /*#__PURE__*/React.forwardRef(PicLeftOutlined);