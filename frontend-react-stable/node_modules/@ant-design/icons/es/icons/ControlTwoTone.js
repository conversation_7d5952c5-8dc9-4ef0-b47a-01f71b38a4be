import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ControlTwoToneSvg from "@ant-design/icons-svg/es/asn/ControlTwoTone";
import AntdIcon from '../components/AntdIcon';
var ControlTwoTone = function ControlTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ControlTwoToneSvg
  }));
};
ControlTwoTone.displayName = 'ControlTwoTone';
export default /*#__PURE__*/React.forwardRef(ControlTwoTone);