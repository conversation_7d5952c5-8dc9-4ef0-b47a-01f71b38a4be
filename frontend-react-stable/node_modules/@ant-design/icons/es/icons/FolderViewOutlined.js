import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderViewOutlinedSvg from "@ant-design/icons-svg/es/asn/FolderViewOutlined";
import AntdIcon from '../components/AntdIcon';
var FolderViewOutlined = function FolderViewOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderViewOutlinedSvg
  }));
};
FolderViewOutlined.displayName = 'FolderViewOutlined';
export default /*#__PURE__*/React.forwardRef(FolderViewOutlined);