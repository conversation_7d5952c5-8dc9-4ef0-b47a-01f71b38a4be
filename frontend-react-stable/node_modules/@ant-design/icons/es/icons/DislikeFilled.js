import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DislikeFilledSvg from "@ant-design/icons-svg/es/asn/DislikeFilled";
import AntdIcon from '../components/AntdIcon';
var DislikeFilled = function DislikeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DislikeFilledSvg
  }));
};
DislikeFilled.displayName = 'DislikeFilled';
export default /*#__PURE__*/React.forwardRef(DislikeFilled);