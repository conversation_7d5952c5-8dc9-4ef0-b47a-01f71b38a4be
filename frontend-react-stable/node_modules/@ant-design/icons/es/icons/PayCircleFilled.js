import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PayCircleFilledSvg from "@ant-design/icons-svg/es/asn/PayCircleFilled";
import AntdIcon from '../components/AntdIcon';
var PayCircleFilled = function PayCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PayCircleFilledSvg
  }));
};
PayCircleFilled.displayName = 'PayCircleFilled';
export default /*#__PURE__*/React.forwardRef(PayCircleFilled);