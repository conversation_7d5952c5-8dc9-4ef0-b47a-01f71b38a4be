import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RotateLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/RotateLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var RotateLeftOutlined = function RotateLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RotateLeftOutlinedSvg
  }));
};
RotateLeftOutlined.displayName = 'RotateLeftOutlined';
export default /*#__PURE__*/React.forwardRef(RotateLeftOutlined);