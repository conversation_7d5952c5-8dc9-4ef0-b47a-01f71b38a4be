import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RadiusBottomleftOutlinedSvg from "@ant-design/icons-svg/es/asn/RadiusBottomleftOutlined";
import AntdIcon from '../components/AntdIcon';
var RadiusBottomleftOutlined = function RadiusBottomleftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadiusBottomleftOutlinedSvg
  }));
};
RadiusBottomleftOutlined.displayName = 'RadiusBottomleftOutlined';
export default /*#__PURE__*/React.forwardRef(RadiusBottomleftOutlined);