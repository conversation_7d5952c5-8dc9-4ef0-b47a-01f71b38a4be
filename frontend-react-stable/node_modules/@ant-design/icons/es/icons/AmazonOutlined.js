import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AmazonOutlinedSvg from "@ant-design/icons-svg/es/asn/AmazonOutlined";
import AntdIcon from '../components/AntdIcon';
var AmazonOutlined = function AmazonOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AmazonOutlinedSvg
  }));
};
AmazonOutlined.displayName = 'AmazonOutlined';
export default /*#__PURE__*/React.forwardRef(AmazonOutlined);