import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileZipOutlinedSvg from "@ant-design/icons-svg/es/asn/FileZipOutlined";
import AntdIcon from '../components/AntdIcon';
var FileZipOutlined = function FileZipOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileZipOutlinedSvg
  }));
};
FileZipOutlined.displayName = 'FileZipOutlined';
export default /*#__PURE__*/React.forwardRef(FileZipOutlined);