import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SearchOutlinedSvg from "@ant-design/icons-svg/es/asn/SearchOutlined";
import AntdIcon from '../components/AntdIcon';
var SearchOutlined = function SearchOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SearchOutlinedSvg
  }));
};
SearchOutlined.displayName = 'SearchOutlined';
export default /*#__PURE__*/React.forwardRef(SearchOutlined);