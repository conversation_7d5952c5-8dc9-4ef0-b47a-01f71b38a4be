import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileUnknownOutlinedSvg from "@ant-design/icons-svg/es/asn/FileUnknownOutlined";
import AntdIcon from '../components/AntdIcon';
var FileUnknownOutlined = function FileUnknownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileUnknownOutlinedSvg
  }));
};
FileUnknownOutlined.displayName = 'FileUnknownOutlined';
export default /*#__PURE__*/React.forwardRef(FileUnknownOutlined);