import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SafetyCertificateFilledSvg from "@ant-design/icons-svg/es/asn/SafetyCertificateFilled";
import AntdIcon from '../components/AntdIcon';
var SafetyCertificateFilled = function SafetyCertificateFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SafetyCertificateFilledSvg
  }));
};
SafetyCertificateFilled.displayName = 'SafetyCertificateFilled';
export default /*#__PURE__*/React.forwardRef(SafetyCertificateFilled);