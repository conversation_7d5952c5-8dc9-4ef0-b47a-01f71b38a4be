import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EuroTwoToneSvg from "@ant-design/icons-svg/es/asn/EuroTwoTone";
import AntdIcon from '../components/AntdIcon';
var EuroTwoTone = function EuroTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EuroTwoToneSvg
  }));
};
EuroTwoTone.displayName = 'EuroTwoTone';
export default /*#__PURE__*/React.forwardRef(EuroTwoTone);