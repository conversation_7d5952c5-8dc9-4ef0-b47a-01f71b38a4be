import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WarningTwoToneSvg from "@ant-design/icons-svg/es/asn/WarningTwoTone";
import AntdIcon from '../components/AntdIcon';
var WarningTwoTone = function WarningTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WarningTwoToneSvg
  }));
};
WarningTwoTone.displayName = 'WarningTwoTone';
export default /*#__PURE__*/React.forwardRef(WarningTwoTone);