import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedoOutlinedSvg from "@ant-design/icons-svg/es/asn/RedoOutlined";
import AntdIcon from '../components/AntdIcon';
var RedoOutlined = function RedoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedoOutlinedSvg
  }));
};
RedoOutlined.displayName = 'RedoOutlined';
export default /*#__PURE__*/React.forwardRef(RedoOutlined);