import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DollarCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/DollarCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var DollarCircleOutlined = function DollarCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DollarCircleOutlinedSvg
  }));
};
DollarCircleOutlined.displayName = 'DollarCircleOutlined';
export default /*#__PURE__*/React.forwardRef(DollarCircleOutlined);