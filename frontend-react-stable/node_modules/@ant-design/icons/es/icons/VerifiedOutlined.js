import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerifiedOutlinedSvg from "@ant-design/icons-svg/es/asn/VerifiedOutlined";
import AntdIcon from '../components/AntdIcon';
var VerifiedOutlined = function VerifiedOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerifiedOutlinedSvg
  }));
};
VerifiedOutlined.displayName = 'VerifiedOutlined';
export default /*#__PURE__*/React.forwardRef(VerifiedOutlined);