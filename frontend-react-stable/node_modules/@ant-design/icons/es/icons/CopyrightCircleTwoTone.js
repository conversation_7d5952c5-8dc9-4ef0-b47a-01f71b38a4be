import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyrightCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/CopyrightCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var CopyrightCircleTwoTone = function CopyrightCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyrightCircleTwoToneSvg
  }));
};
CopyrightCircleTwoTone.displayName = 'CopyrightCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(CopyrightCircleTwoTone);