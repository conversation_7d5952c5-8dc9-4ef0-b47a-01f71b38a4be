import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyrightCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CopyrightCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CopyrightCircleOutlined = function CopyrightCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyrightCircleOutlinedSvg
  }));
};
CopyrightCircleOutlined.displayName = 'CopyrightCircleOutlined';
export default /*#__PURE__*/React.forwardRef(CopyrightCircleOutlined);