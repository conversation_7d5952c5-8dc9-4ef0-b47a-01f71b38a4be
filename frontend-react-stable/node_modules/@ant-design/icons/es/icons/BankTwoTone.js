import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BankTwoToneSvg from "@ant-design/icons-svg/es/asn/BankTwoTone";
import AntdIcon from '../components/AntdIcon';
var BankTwoTone = function BankTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BankTwoToneSvg
  }));
};
BankTwoTone.displayName = 'BankTwoTone';
export default /*#__PURE__*/React.forwardRef(BankTwoTone);