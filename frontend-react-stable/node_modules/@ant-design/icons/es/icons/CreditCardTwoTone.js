import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CreditCardTwoToneSvg from "@ant-design/icons-svg/es/asn/CreditCardTwoTone";
import AntdIcon from '../components/AntdIcon';
var CreditCardTwoTone = function CreditCardTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CreditCardTwoToneSvg
  }));
};
CreditCardTwoTone.displayName = 'CreditCardTwoTone';
export default /*#__PURE__*/React.forwardRef(CreditCardTwoTone);