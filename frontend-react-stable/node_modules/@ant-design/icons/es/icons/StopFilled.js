import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StopFilledSvg from "@ant-design/icons-svg/es/asn/StopFilled";
import AntdIcon from '../components/AntdIcon';
var StopFilled = function StopFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StopFilledSvg
  }));
};
StopFilled.displayName = 'StopFilled';
export default /*#__PURE__*/React.forwardRef(StopFilled);