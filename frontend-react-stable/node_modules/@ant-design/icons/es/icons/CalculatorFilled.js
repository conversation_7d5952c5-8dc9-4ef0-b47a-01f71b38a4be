import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CalculatorFilledSvg from "@ant-design/icons-svg/es/asn/CalculatorFilled";
import AntdIcon from '../components/AntdIcon';
var CalculatorFilled = function CalculatorFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CalculatorFilledSvg
  }));
};
CalculatorFilled.displayName = 'CalculatorFilled';
export default /*#__PURE__*/React.forwardRef(CalculatorFilled);