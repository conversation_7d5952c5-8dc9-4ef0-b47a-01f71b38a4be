import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ChromeOutlinedSvg from "@ant-design/icons-svg/es/asn/ChromeOutlined";
import AntdIcon from '../components/AntdIcon';
var ChromeOutlined = function ChromeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ChromeOutlinedSvg
  }));
};
ChromeOutlined.displayName = 'ChromeOutlined';
export default /*#__PURE__*/React.forwardRef(ChromeOutlined);