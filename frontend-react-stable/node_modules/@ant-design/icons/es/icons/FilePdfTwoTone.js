import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePdfTwoToneSvg from "@ant-design/icons-svg/es/asn/FilePdfTwoTone";
import AntdIcon from '../components/AntdIcon';
var FilePdfTwoTone = function FilePdfTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePdfTwoToneSvg
  }));
};
FilePdfTwoTone.displayName = 'FilePdfTwoTone';
export default /*#__PURE__*/React.forwardRef(FilePdfTwoTone);