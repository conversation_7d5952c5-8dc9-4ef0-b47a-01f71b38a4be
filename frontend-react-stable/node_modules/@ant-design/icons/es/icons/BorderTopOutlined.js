import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderTopOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderTopOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderTopOutlined = function BorderTopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderTopOutlinedSvg
  }));
};
BorderTopOutlined.displayName = 'BorderTopOutlined';
export default /*#__PURE__*/React.forwardRef(BorderTopOutlined);