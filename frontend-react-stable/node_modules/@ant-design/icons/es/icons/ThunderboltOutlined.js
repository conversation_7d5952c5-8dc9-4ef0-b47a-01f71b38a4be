import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ThunderboltOutlinedSvg from "@ant-design/icons-svg/es/asn/ThunderboltOutlined";
import AntdIcon from '../components/AntdIcon';
var ThunderboltOutlined = function ThunderboltOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ThunderboltOutlinedSvg
  }));
};
ThunderboltOutlined.displayName = 'ThunderboltOutlined';
export default /*#__PURE__*/React.forwardRef(ThunderboltOutlined);