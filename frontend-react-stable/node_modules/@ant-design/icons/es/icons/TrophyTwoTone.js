import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrophyTwoToneSvg from "@ant-design/icons-svg/es/asn/TrophyTwoTone";
import AntdIcon from '../components/AntdIcon';
var TrophyTwoTone = function TrophyTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrophyTwoToneSvg
  }));
};
TrophyTwoTone.displayName = 'TrophyTwoTone';
export default /*#__PURE__*/React.forwardRef(TrophyTwoTone);