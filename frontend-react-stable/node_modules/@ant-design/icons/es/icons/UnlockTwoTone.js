import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnlockTwoToneSvg from "@ant-design/icons-svg/es/asn/UnlockTwoTone";
import AntdIcon from '../components/AntdIcon';
var UnlockTwoTone = function UnlockTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnlockTwoToneSvg
  }));
};
UnlockTwoTone.displayName = 'UnlockTwoTone';
export default /*#__PURE__*/React.forwardRef(UnlockTwoTone);