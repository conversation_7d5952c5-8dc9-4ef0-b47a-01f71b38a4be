import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RocketFilledSvg from "@ant-design/icons-svg/es/asn/RocketFilled";
import AntdIcon from '../components/AntdIcon';
var RocketFilled = function RocketFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RocketFilledSvg
  }));
};
RocketFilled.displayName = 'RocketFilled';
export default /*#__PURE__*/React.forwardRef(RocketFilled);