import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PrinterFilledSvg from "@ant-design/icons-svg/es/asn/PrinterFilled";
import AntdIcon from '../components/AntdIcon';
var PrinterFilled = function PrinterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PrinterFilledSvg
  }));
};
PrinterFilled.displayName = 'PrinterFilled';
export default /*#__PURE__*/React.forwardRef(PrinterFilled);