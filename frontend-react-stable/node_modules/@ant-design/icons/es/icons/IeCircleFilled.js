import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IeCircleFilledSvg from "@ant-design/icons-svg/es/asn/IeCircleFilled";
import AntdIcon from '../components/AntdIcon';
var IeCircleFilled = function IeCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IeCircleFilledSvg
  }));
};
IeCircleFilled.displayName = 'IeCircleFilled';
export default /*#__PURE__*/React.forwardRef(IeCircleFilled);