import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderHorizontalOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderHorizontalOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderHorizontalOutlined = function BorderHorizontalOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderHorizontalOutlinedSvg
  }));
};
BorderHorizontalOutlined.displayName = 'BorderHorizontalOutlined';
export default /*#__PURE__*/React.forwardRef(BorderHorizontalOutlined);