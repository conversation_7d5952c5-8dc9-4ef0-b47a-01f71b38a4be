import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeleteOutlinedSvg from "@ant-design/icons-svg/es/asn/DeleteOutlined";
import AntdIcon from '../components/AntdIcon';
var DeleteOutlined = function DeleteOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeleteOutlinedSvg
  }));
};
DeleteOutlined.displayName = 'DeleteOutlined';
export default /*#__PURE__*/React.forwardRef(DeleteOutlined);