import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderBottomOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderBottomOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderBottomOutlined = function BorderBottomOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderBottomOutlinedSvg
  }));
};
BorderBottomOutlined.displayName = 'BorderBottomOutlined';
export default /*#__PURE__*/React.forwardRef(BorderBottomOutlined);