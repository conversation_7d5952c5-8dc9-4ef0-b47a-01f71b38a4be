import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusCircleFilledSvg from "@ant-design/icons-svg/es/asn/PlusCircleFilled";
import AntdIcon from '../components/AntdIcon';
var PlusCircleFilled = function PlusCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlusCircleFilledSvg
  }));
};
PlusCircleFilled.displayName = 'PlusCircleFilled';
export default /*#__PURE__*/React.forwardRef(PlusCircleFilled);