import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FacebookFilledSvg from "@ant-design/icons-svg/es/asn/FacebookFilled";
import AntdIcon from '../components/AntdIcon';
var FacebookFilled = function FacebookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FacebookFilledSvg
  }));
};
FacebookFilled.displayName = 'FacebookFilled';
export default /*#__PURE__*/React.forwardRef(FacebookFilled);