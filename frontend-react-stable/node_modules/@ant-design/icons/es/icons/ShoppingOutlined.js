import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingOutlinedSvg from "@ant-design/icons-svg/es/asn/ShoppingOutlined";
import AntdIcon from '../components/AntdIcon';
var ShoppingOutlined = function ShoppingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingOutlinedSvg
  }));
};
ShoppingOutlined.displayName = 'ShoppingOutlined';
export default /*#__PURE__*/React.forwardRef(ShoppingOutlined);