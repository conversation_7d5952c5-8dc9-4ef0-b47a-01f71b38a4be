import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BgColorsOutlinedSvg from "@ant-design/icons-svg/es/asn/BgColorsOutlined";
import AntdIcon from '../components/AntdIcon';
var BgColorsOutlined = function BgColorsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BgColorsOutlinedSvg
  }));
};
BgColorsOutlined.displayName = 'BgColorsOutlined';
export default /*#__PURE__*/React.forwardRef(BgColorsOutlined);