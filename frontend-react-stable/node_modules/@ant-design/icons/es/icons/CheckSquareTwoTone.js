import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/CheckSquareTwoTone";
import AntdIcon from '../components/AntdIcon';
var CheckSquareTwoTone = function CheckSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckSquareTwoToneSvg
  }));
};
CheckSquareTwoTone.displayName = 'CheckSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(CheckSquareTwoTone);