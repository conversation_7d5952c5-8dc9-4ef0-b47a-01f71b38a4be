import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StockOutlinedSvg from "@ant-design/icons-svg/es/asn/StockOutlined";
import AntdIcon from '../components/AntdIcon';
var StockOutlined = function StockOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StockOutlinedSvg
  }));
};
StockOutlined.displayName = 'StockOutlined';
export default /*#__PURE__*/React.forwardRef(StockOutlined);