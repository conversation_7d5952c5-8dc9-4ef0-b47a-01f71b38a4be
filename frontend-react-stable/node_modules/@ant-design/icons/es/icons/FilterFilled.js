import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilterFilledSvg from "@ant-design/icons-svg/es/asn/FilterFilled";
import AntdIcon from '../components/AntdIcon';
var FilterFilled = function FilterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilterFilledSvg
  }));
};
FilterFilled.displayName = 'FilterFilled';
export default /*#__PURE__*/React.forwardRef(FilterFilled);