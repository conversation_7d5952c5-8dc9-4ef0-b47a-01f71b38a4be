import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilePptOutlinedSvg from "@ant-design/icons-svg/es/asn/FilePptOutlined";
import AntdIcon from '../components/AntdIcon';
var FilePptOutlined = function FilePptOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilePptOutlinedSvg
  }));
};
FilePptOutlined.displayName = 'FilePptOutlined';
export default /*#__PURE__*/React.forwardRef(FilePptOutlined);