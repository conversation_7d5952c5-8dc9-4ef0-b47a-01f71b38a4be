import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoldTwoToneSvg from "@ant-design/icons-svg/es/asn/GoldTwoTone";
import AntdIcon from '../components/AntdIcon';
var GoldTwoTone = function GoldTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoldTwoToneSvg
  }));
};
GoldTwoTone.displayName = 'GoldTwoTone';
export default /*#__PURE__*/React.forwardRef(GoldTwoTone);