import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodepenCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CodepenCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CodepenCircleOutlined = function CodepenCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodepenCircleOutlinedSvg
  }));
};
CodepenCircleOutlined.displayName = 'CodepenCircleOutlined';
export default /*#__PURE__*/React.forwardRef(CodepenCircleOutlined);