import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SafetyCertificateTwoToneSvg from "@ant-design/icons-svg/es/asn/SafetyCertificateTwoTone";
import AntdIcon from '../components/AntdIcon';
var SafetyCertificateTwoTone = function SafetyCertificateTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SafetyCertificateTwoToneSvg
  }));
};
SafetyCertificateTwoTone.displayName = 'SafetyCertificateTwoTone';
export default /*#__PURE__*/React.forwardRef(SafetyCertificateTwoTone);