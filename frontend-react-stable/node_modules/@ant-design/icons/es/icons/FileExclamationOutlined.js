import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExclamationOutlinedSvg from "@ant-design/icons-svg/es/asn/FileExclamationOutlined";
import AntdIcon from '../components/AntdIcon';
var FileExclamationOutlined = function FileExclamationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExclamationOutlinedSvg
  }));
};
FileExclamationOutlined.displayName = 'FileExclamationOutlined';
export default /*#__PURE__*/React.forwardRef(FileExclamationOutlined);