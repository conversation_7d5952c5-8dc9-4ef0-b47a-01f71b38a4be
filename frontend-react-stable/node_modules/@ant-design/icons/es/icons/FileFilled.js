import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileFilledSvg from "@ant-design/icons-svg/es/asn/FileFilled";
import AntdIcon from '../components/AntdIcon';
var FileFilled = function FileFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileFilledSvg
  }));
};
FileFilled.displayName = 'FileFilled';
export default /*#__PURE__*/React.forwardRef(FileFilled);