import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PieChartTwoToneSvg from "@ant-design/icons-svg/es/asn/PieChartTwoTone";
import AntdIcon from '../components/AntdIcon';
var PieChartTwoTone = function PieChartTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PieChartTwoToneSvg
  }));
};
PieChartTwoTone.displayName = 'PieChartTwoTone';
export default /*#__PURE__*/React.forwardRef(PieChartTwoTone);