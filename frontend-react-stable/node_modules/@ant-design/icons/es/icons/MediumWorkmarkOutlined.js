import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MediumWorkmarkOutlinedSvg from "@ant-design/icons-svg/es/asn/MediumWorkmarkOutlined";
import AntdIcon from '../components/AntdIcon';
var MediumWorkmarkOutlined = function MediumWorkmarkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MediumWorkmarkOutlinedSvg
  }));
};
MediumWorkmarkOutlined.displayName = 'MediumWorkmarkOutlined';
export default /*#__PURE__*/React.forwardRef(MediumWorkmarkOutlined);