import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretUpOutlinedSvg from "@ant-design/icons-svg/es/asn/CaretUpOutlined";
import AntdIcon from '../components/AntdIcon';
var CaretUpOutlined = function CaretUpOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretUpOutlinedSvg
  }));
};
CaretUpOutlined.displayName = 'CaretUpOutlined';
export default /*#__PURE__*/React.forwardRef(CaretUpOutlined);