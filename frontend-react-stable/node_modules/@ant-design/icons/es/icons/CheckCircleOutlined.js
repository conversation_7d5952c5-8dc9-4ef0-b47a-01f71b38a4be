import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CheckCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CheckCircleOutlined = function CheckCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckCircleOutlinedSvg
  }));
};
CheckCircleOutlined.displayName = 'CheckCircleOutlined';
export default /*#__PURE__*/React.forwardRef(CheckCircleOutlined);