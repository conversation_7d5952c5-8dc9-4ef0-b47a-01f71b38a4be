import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HighlightTwoToneSvg from "@ant-design/icons-svg/es/asn/HighlightTwoTone";
import AntdIcon from '../components/AntdIcon';
var HighlightTwoTone = function HighlightTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HighlightTwoToneSvg
  }));
};
HighlightTwoTone.displayName = 'HighlightTwoTone';
export default /*#__PURE__*/React.forwardRef(HighlightTwoTone);