import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FireFilledSvg from "@ant-design/icons-svg/es/asn/FireFilled";
import AntdIcon from '../components/AntdIcon';
var FireFilled = function FireFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FireFilledSvg
  }));
};
FireFilled.displayName = 'FireFilled';
export default /*#__PURE__*/React.forwardRef(FireFilled);