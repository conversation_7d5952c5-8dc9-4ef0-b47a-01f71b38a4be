import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SecurityScanOutlinedSvg from "@ant-design/icons-svg/es/asn/SecurityScanOutlined";
import AntdIcon from '../components/AntdIcon';
var SecurityScanOutlined = function SecurityScanOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SecurityScanOutlinedSvg
  }));
};
SecurityScanOutlined.displayName = 'SecurityScanOutlined';
export default /*#__PURE__*/React.forwardRef(SecurityScanOutlined);