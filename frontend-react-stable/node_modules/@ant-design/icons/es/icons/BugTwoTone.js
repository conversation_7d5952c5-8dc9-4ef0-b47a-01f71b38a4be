import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BugTwoToneSvg from "@ant-design/icons-svg/es/asn/BugTwoTone";
import AntdIcon from '../components/AntdIcon';
var BugTwoTone = function BugTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BugTwoToneSvg
  }));
};
BugTwoTone.displayName = 'BugTwoTone';
export default /*#__PURE__*/React.forwardRef(BugTwoTone);