import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwapOutlinedSvg from "@ant-design/icons-svg/es/asn/SwapOutlined";
import AntdIcon from '../components/AntdIcon';
var SwapOutlined = function SwapOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwapOutlinedSvg
  }));
};
SwapOutlined.displayName = 'SwapOutlined';
export default /*#__PURE__*/React.forwardRef(SwapOutlined);