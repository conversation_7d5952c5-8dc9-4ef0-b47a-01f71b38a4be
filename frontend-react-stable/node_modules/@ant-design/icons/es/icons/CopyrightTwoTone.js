import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyrightTwoToneSvg from "@ant-design/icons-svg/es/asn/CopyrightTwoTone";
import AntdIcon from '../components/AntdIcon';
var CopyrightTwoTone = function CopyrightTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyrightTwoToneSvg
  }));
};
CopyrightTwoTone.displayName = 'CopyrightTwoTone';
export default /*#__PURE__*/React.forwardRef(CopyrightTwoTone);