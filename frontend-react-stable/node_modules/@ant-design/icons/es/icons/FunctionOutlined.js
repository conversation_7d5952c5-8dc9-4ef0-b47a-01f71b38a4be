import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FunctionOutlinedSvg from "@ant-design/icons-svg/es/asn/FunctionOutlined";
import AntdIcon from '../components/AntdIcon';
var FunctionOutlined = function FunctionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FunctionOutlinedSvg
  }));
};
FunctionOutlined.displayName = 'FunctionOutlined';
export default /*#__PURE__*/React.forwardRef(FunctionOutlined);