import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SolutionOutlinedSvg from "@ant-design/icons-svg/es/asn/SolutionOutlined";
import AntdIcon from '../components/AntdIcon';
var SolutionOutlined = function SolutionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SolutionOutlinedSvg
  }));
};
SolutionOutlined.displayName = 'SolutionOutlined';
export default /*#__PURE__*/React.forwardRef(SolutionOutlined);