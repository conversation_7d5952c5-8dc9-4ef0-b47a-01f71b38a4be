import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DingdingOutlinedSvg from "@ant-design/icons-svg/es/asn/DingdingOutlined";
import AntdIcon from '../components/AntdIcon';
var DingdingOutlined = function DingdingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DingdingOutlinedSvg
  }));
};
DingdingOutlined.displayName = 'DingdingOutlined';
export default /*#__PURE__*/React.forwardRef(DingdingOutlined);