import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnlockFilledSvg from "@ant-design/icons-svg/es/asn/UnlockFilled";
import AntdIcon from '../components/AntdIcon';
var UnlockFilled = function UnlockFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnlockFilledSvg
  }));
};
UnlockFilled.displayName = 'UnlockFilled';
export default /*#__PURE__*/React.forwardRef(UnlockFilled);