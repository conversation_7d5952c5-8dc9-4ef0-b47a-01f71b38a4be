import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExclamationCircleFilledSvg from "@ant-design/icons-svg/es/asn/ExclamationCircleFilled";
import AntdIcon from '../components/AntdIcon';
var ExclamationCircleFilled = function ExclamationCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExclamationCircleFilledSvg
  }));
};
ExclamationCircleFilled.displayName = 'ExclamationCircleFilled';
export default /*#__PURE__*/React.forwardRef(ExclamationCircleFilled);