import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ToTopOutlinedSvg from "@ant-design/icons-svg/es/asn/ToTopOutlined";
import AntdIcon from '../components/AntdIcon';
var ToTopOutlined = function ToTopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ToTopOutlinedSvg
  }));
};
ToTopOutlined.displayName = 'ToTopOutlined';
export default /*#__PURE__*/React.forwardRef(ToTopOutlined);