import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlaySquareFilledSvg from "@ant-design/icons-svg/es/asn/PlaySquareFilled";
import AntdIcon from '../components/AntdIcon';
var PlaySquareFilled = function PlaySquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlaySquareFilledSvg
  }));
};
PlaySquareFilled.displayName = 'PlaySquareFilled';
export default /*#__PURE__*/React.forwardRef(PlaySquareFilled);