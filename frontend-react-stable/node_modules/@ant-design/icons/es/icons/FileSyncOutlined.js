import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileSyncOutlinedSvg from "@ant-design/icons-svg/es/asn/FileSyncOutlined";
import AntdIcon from '../components/AntdIcon';
var FileSyncOutlined = function FileSyncOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileSyncOutlinedSvg
  }));
};
FileSyncOutlined.displayName = 'FileSyncOutlined';
export default /*#__PURE__*/React.forwardRef(FileSyncOutlined);