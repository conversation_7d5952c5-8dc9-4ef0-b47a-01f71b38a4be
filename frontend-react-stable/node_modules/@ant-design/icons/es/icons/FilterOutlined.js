import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilterOutlinedSvg from "@ant-design/icons-svg/es/asn/FilterOutlined";
import AntdIcon from '../components/AntdIcon';
var FilterOutlined = function FilterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilterOutlinedSvg
  }));
};
FilterOutlined.displayName = 'FilterOutlined';
export default /*#__PURE__*/React.forwardRef(FilterOutlined);