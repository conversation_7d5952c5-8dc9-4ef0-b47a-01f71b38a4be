import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WalletOutlinedSvg from "@ant-design/icons-svg/es/asn/WalletOutlined";
import AntdIcon from '../components/AntdIcon';
var WalletOutlined = function WalletOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WalletOutlinedSvg
  }));
};
WalletOutlined.displayName = 'WalletOutlined';
export default /*#__PURE__*/React.forwardRef(WalletOutlined);