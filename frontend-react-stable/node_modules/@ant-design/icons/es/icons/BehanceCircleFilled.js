import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BehanceCircleFilledSvg from "@ant-design/icons-svg/es/asn/BehanceCircleFilled";
import AntdIcon from '../components/AntdIcon';
var BehanceCircleFilled = function BehanceCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BehanceCircleFilledSvg
  }));
};
BehanceCircleFilled.displayName = 'BehanceCircleFilled';
export default /*#__PURE__*/React.forwardRef(BehanceCircleFilled);