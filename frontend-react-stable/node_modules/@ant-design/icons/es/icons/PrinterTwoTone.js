import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PrinterTwoToneSvg from "@ant-design/icons-svg/es/asn/PrinterTwoTone";
import AntdIcon from '../components/AntdIcon';
var PrinterTwoTone = function PrinterTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PrinterTwoToneSvg
  }));
};
PrinterTwoTone.displayName = 'PrinterTwoTone';
export default /*#__PURE__*/React.forwardRef(PrinterTwoTone);