import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CustomerServiceFilledSvg from "@ant-design/icons-svg/es/asn/CustomerServiceFilled";
import AntdIcon from '../components/AntdIcon';
var CustomerServiceFilled = function CustomerServiceFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CustomerServiceFilledSvg
  }));
};
CustomerServiceFilled.displayName = 'CustomerServiceFilled';
export default /*#__PURE__*/React.forwardRef(CustomerServiceFilled);