import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BankOutlinedSvg from "@ant-design/icons-svg/es/asn/BankOutlined";
import AntdIcon from '../components/AntdIcon';
var BankOutlined = function BankOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BankOutlinedSvg
  }));
};
BankOutlined.displayName = 'BankOutlined';
export default /*#__PURE__*/React.forwardRef(BankOutlined);