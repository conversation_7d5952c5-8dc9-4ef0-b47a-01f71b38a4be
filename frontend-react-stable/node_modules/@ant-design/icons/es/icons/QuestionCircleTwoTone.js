import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QuestionCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/QuestionCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var QuestionCircleTwoTone = function QuestionCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QuestionCircleTwoToneSvg
  }));
};
QuestionCircleTwoTone.displayName = 'QuestionCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(QuestionCircleTwoTone);