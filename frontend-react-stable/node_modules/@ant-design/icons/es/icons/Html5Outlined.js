import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Html5OutlinedSvg from "@ant-design/icons-svg/es/asn/Html5Outlined";
import AntdIcon from '../components/AntdIcon';
var Html5Outlined = function Html5Outlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: Html5OutlinedSvg
  }));
};
Html5Outlined.displayName = 'Html5Outlined';
export default /*#__PURE__*/React.forwardRef(Html5Outlined);