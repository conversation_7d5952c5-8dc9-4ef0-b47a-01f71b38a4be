import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TwitterSquareFilledSvg from "@ant-design/icons-svg/es/asn/TwitterSquareFilled";
import AntdIcon from '../components/AntdIcon';
var TwitterSquareFilled = function TwitterSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TwitterSquareFilledSvg
  }));
};
TwitterSquareFilled.displayName = 'TwitterSquareFilled';
export default /*#__PURE__*/React.forwardRef(TwitterSquareFilled);