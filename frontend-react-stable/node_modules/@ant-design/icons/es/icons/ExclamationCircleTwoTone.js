import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExclamationCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/ExclamationCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var ExclamationCircleTwoTone = function ExclamationCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExclamationCircleTwoToneSvg
  }));
};
ExclamationCircleTwoTone.displayName = 'ExclamationCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(ExclamationCircleTwoTone);