import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FrownFilledSvg from "@ant-design/icons-svg/es/asn/FrownFilled";
import AntdIcon from '../components/AntdIcon';
var FrownFilled = function FrownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FrownFilledSvg
  }));
};
FrownFilled.displayName = 'FrownFilled';
export default /*#__PURE__*/React.forwardRef(FrownFilled);