import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SignalFilledSvg from "@ant-design/icons-svg/es/asn/SignalFilled";
import AntdIcon from '../components/AntdIcon';
var SignalFilled = function SignalFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SignalFilledSvg
  }));
};
SignalFilled.displayName = 'SignalFilled';
export default /*#__PURE__*/React.forwardRef(SignalFilled);