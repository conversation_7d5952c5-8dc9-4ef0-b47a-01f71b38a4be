import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExportOutlinedSvg from "@ant-design/icons-svg/es/asn/ExportOutlined";
import AntdIcon from '../components/AntdIcon';
var ExportOutlined = function ExportOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExportOutlinedSvg
  }));
};
ExportOutlined.displayName = 'ExportOutlined';
export default /*#__PURE__*/React.forwardRef(ExportOutlined);