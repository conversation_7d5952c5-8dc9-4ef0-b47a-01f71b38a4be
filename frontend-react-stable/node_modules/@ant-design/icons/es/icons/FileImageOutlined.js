import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileImageOutlinedSvg from "@ant-design/icons-svg/es/asn/FileImageOutlined";
import AntdIcon from '../components/AntdIcon';
var FileImageOutlined = function FileImageOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileImageOutlinedSvg
  }));
};
FileImageOutlined.displayName = 'FileImageOutlined';
export default /*#__PURE__*/React.forwardRef(FileImageOutlined);