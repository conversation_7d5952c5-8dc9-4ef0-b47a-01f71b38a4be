import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DropboxOutlinedSvg from "@ant-design/icons-svg/es/asn/DropboxOutlined";
import AntdIcon from '../components/AntdIcon';
var DropboxOutlined = function DropboxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DropboxOutlinedSvg
  }));
};
DropboxOutlined.displayName = 'DropboxOutlined';
export default /*#__PURE__*/React.forwardRef(DropboxOutlined);