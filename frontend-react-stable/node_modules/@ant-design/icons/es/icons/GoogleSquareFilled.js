import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GoogleSquareFilledSvg from "@ant-design/icons-svg/es/asn/GoogleSquareFilled";
import AntdIcon from '../components/AntdIcon';
var GoogleSquareFilled = function GoogleSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GoogleSquareFilledSvg
  }));
};
GoogleSquareFilled.displayName = 'GoogleSquareFilled';
export default /*#__PURE__*/React.forwardRef(GoogleSquareFilled);