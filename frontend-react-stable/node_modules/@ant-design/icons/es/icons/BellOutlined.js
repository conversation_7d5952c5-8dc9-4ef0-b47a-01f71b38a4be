import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BellOutlinedSvg from "@ant-design/icons-svg/es/asn/BellOutlined";
import AntdIcon from '../components/AntdIcon';
var BellOutlined = function BellOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BellOutlinedSvg
  }));
};
BellOutlined.displayName = 'BellOutlined';
export default /*#__PURE__*/React.forwardRef(BellOutlined);