import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileWordFilledSvg from "@ant-design/icons-svg/es/asn/FileWordFilled";
import AntdIcon from '../components/AntdIcon';
var FileWordFilled = function FileWordFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileWordFilledSvg
  }));
};
FileWordFilled.displayName = 'FileWordFilled';
export default /*#__PURE__*/React.forwardRef(FileWordFilled);