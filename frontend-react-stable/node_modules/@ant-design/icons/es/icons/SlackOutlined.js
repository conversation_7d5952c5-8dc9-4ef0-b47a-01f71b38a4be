import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackOutlinedSvg from "@ant-design/icons-svg/es/asn/SlackOutlined";
import AntdIcon from '../components/AntdIcon';
var SlackOutlined = function SlackOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackOutlinedSvg
  }));
};
SlackOutlined.displayName = 'SlackOutlined';
export default /*#__PURE__*/React.forwardRef(SlackOutlined);