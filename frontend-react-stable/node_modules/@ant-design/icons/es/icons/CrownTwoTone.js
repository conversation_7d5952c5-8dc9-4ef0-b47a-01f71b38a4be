import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CrownTwoToneSvg from "@ant-design/icons-svg/es/asn/CrownTwoTone";
import AntdIcon from '../components/AntdIcon';
var CrownTwoTone = function CrownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CrownTwoToneSvg
  }));
};
CrownTwoTone.displayName = 'CrownTwoTone';
export default /*#__PURE__*/React.forwardRef(CrownTwoTone);