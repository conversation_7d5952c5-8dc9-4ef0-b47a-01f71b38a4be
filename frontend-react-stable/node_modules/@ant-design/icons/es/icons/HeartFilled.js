import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HeartFilledSvg from "@ant-design/icons-svg/es/asn/HeartFilled";
import AntdIcon from '../components/AntdIcon';
var HeartFilled = function HeartFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HeartFilledSvg
  }));
};
HeartFilled.displayName = 'HeartFilled';
export default /*#__PURE__*/React.forwardRef(HeartFilled);