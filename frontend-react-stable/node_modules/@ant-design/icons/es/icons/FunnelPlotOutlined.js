import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FunnelPlotOutlinedSvg from "@ant-design/icons-svg/es/asn/FunnelPlotOutlined";
import AntdIcon from '../components/AntdIcon';
var FunnelPlotOutlined = function FunnelPlotOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FunnelPlotOutlinedSvg
  }));
};
FunnelPlotOutlined.displayName = 'FunnelPlotOutlined';
export default /*#__PURE__*/React.forwardRef(FunnelPlotOutlined);