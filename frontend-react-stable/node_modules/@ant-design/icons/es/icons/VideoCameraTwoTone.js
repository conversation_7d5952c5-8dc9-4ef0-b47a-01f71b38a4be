import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VideoCameraTwoToneSvg from "@ant-design/icons-svg/es/asn/VideoCameraTwoTone";
import AntdIcon from '../components/AntdIcon';
var VideoCameraTwoTone = function VideoCameraTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VideoCameraTwoToneSvg
  }));
};
VideoCameraTwoTone.displayName = 'VideoCameraTwoTone';
export default /*#__PURE__*/React.forwardRef(VideoCameraTwoTone);