import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FontColorsOutlinedSvg from "@ant-design/icons-svg/es/asn/FontColorsOutlined";
import AntdIcon from '../components/AntdIcon';
var FontColorsOutlined = function FontColorsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FontColorsOutlinedSvg
  }));
};
FontColorsOutlined.displayName = 'FontColorsOutlined';
export default /*#__PURE__*/React.forwardRef(FontColorsOutlined);