import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RocketTwoToneSvg from "@ant-design/icons-svg/es/asn/RocketTwoTone";
import AntdIcon from '../components/AntdIcon';
var RocketTwoTone = function RocketTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RocketTwoToneSvg
  }));
};
RocketTwoTone.displayName = 'RocketTwoTone';
export default /*#__PURE__*/React.forwardRef(RocketTwoTone);