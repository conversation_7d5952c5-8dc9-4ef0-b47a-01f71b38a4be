import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CrownFilledSvg from "@ant-design/icons-svg/es/asn/CrownFilled";
import AntdIcon from '../components/AntdIcon';
var CrownFilled = function CrownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CrownFilledSvg
  }));
};
CrownFilled.displayName = 'CrownFilled';
export default /*#__PURE__*/React.forwardRef(CrownFilled);