import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FullscreenExitOutlinedSvg from "@ant-design/icons-svg/es/asn/FullscreenExitOutlined";
import AntdIcon from '../components/AntdIcon';
var FullscreenExitOutlined = function FullscreenExitOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FullscreenExitOutlinedSvg
  }));
};
FullscreenExitOutlined.displayName = 'FullscreenExitOutlined';
export default /*#__PURE__*/React.forwardRef(FullscreenExitOutlined);