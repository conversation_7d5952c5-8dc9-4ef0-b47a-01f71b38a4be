import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BorderOuterOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderOuterOutlined";
import AntdIcon from '../components/AntdIcon';
var BorderOuterOutlined = function BorderOuterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BorderOuterOutlinedSvg
  }));
};
BorderOuterOutlined.displayName = 'BorderOuterOutlined';
export default /*#__PURE__*/React.forwardRef(BorderOuterOutlined);