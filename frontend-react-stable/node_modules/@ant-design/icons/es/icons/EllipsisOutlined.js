import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EllipsisOutlinedSvg from "@ant-design/icons-svg/es/asn/EllipsisOutlined";
import AntdIcon from '../components/AntdIcon';
var EllipsisOutlined = function EllipsisOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EllipsisOutlinedSvg
  }));
};
EllipsisOutlined.displayName = 'EllipsisOutlined';
export default /*#__PURE__*/React.forwardRef(EllipsisOutlined);