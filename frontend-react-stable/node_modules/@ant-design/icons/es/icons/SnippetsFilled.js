import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SnippetsFilledSvg from "@ant-design/icons-svg/es/asn/SnippetsFilled";
import AntdIcon from '../components/AntdIcon';
var SnippetsFilled = function SnippetsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SnippetsFilledSvg
  }));
};
SnippetsFilled.displayName = 'SnippetsFilled';
export default /*#__PURE__*/React.forwardRef(SnippetsFilled);