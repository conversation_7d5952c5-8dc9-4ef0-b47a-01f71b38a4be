import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BuildOutlinedSvg from "@ant-design/icons-svg/es/asn/BuildOutlined";
import AntdIcon from '../components/AntdIcon';
var BuildOutlined = function BuildOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BuildOutlinedSvg
  }));
};
BuildOutlined.displayName = 'BuildOutlined';
export default /*#__PURE__*/React.forwardRef(BuildOutlined);