import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeleteFilledSvg from "@ant-design/icons-svg/es/asn/DeleteFilled";
import AntdIcon from '../components/AntdIcon';
var DeleteFilled = function DeleteFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeleteFilledSvg
  }));
};
DeleteFilled.displayName = 'DeleteFilled';
export default /*#__PURE__*/React.forwardRef(DeleteFilled);