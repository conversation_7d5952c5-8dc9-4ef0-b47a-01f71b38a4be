import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BarsOutlinedSvg from "@ant-design/icons-svg/es/asn/BarsOutlined";
import AntdIcon from '../components/AntdIcon';
var BarsOutlined = function BarsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BarsOutlinedSvg
  }));
};
BarsOutlined.displayName = 'BarsOutlined';
export default /*#__PURE__*/React.forwardRef(BarsOutlined);