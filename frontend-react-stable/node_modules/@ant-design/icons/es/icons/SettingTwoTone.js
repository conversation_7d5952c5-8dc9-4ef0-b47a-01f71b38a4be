import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SettingTwoToneSvg from "@ant-design/icons-svg/es/asn/SettingTwoTone";
import AntdIcon from '../components/AntdIcon';
var SettingTwoTone = function SettingTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SettingTwoToneSvg
  }));
};
SettingTwoTone.displayName = 'SettingTwoTone';
export default /*#__PURE__*/React.forwardRef(SettingTwoTone);