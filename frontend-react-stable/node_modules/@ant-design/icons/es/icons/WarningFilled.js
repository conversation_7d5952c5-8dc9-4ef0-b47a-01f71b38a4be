import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WarningFilledSvg from "@ant-design/icons-svg/es/asn/WarningFilled";
import AntdIcon from '../components/AntdIcon';
var WarningFilled = function WarningFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WarningFilledSvg
  }));
};
WarningFilled.displayName = 'WarningFilled';
export default /*#__PURE__*/React.forwardRef(WarningFilled);