import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileTextTwoToneSvg from "@ant-design/icons-svg/es/asn/FileTextTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileTextTwoTone = function FileTextTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileTextTwoToneSvg
  }));
};
FileTextTwoTone.displayName = 'FileTextTwoTone';
export default /*#__PURE__*/React.forwardRef(FileTextTwoTone);