import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ClearOutlinedSvg from "@ant-design/icons-svg/es/asn/ClearOutlined";
import AntdIcon from '../components/AntdIcon';
var ClearOutlined = function ClearOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ClearOutlinedSvg
  }));
};
ClearOutlined.displayName = 'ClearOutlined';
export default /*#__PURE__*/React.forwardRef(ClearOutlined);