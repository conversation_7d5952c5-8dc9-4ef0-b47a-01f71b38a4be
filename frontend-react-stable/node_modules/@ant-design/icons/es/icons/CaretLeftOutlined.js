import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/CaretLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var CaretLeftOutlined = function CaretLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretLeftOutlinedSvg
  }));
};
CaretLeftOutlined.displayName = 'CaretLeftOutlined';
export default /*#__PURE__*/React.forwardRef(CaretLeftOutlined);