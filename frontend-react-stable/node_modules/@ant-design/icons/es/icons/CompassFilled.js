import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CompassFilledSvg from "@ant-design/icons-svg/es/asn/CompassFilled";
import AntdIcon from '../components/AntdIcon';
var CompassFilled = function CompassFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CompassFilledSvg
  }));
};
CompassFilled.displayName = 'CompassFilled';
export default /*#__PURE__*/React.forwardRef(CompassFilled);