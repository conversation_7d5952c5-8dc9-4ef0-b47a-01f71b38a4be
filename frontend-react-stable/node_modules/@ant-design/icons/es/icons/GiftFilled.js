import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GiftFilledSvg from "@ant-design/icons-svg/es/asn/GiftFilled";
import AntdIcon from '../components/AntdIcon';
var GiftFilled = function GiftFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GiftFilledSvg
  }));
};
GiftFilled.displayName = 'GiftFilled';
export default /*#__PURE__*/React.forwardRef(GiftFilled);