import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BulbTwoToneSvg from "@ant-design/icons-svg/es/asn/BulbTwoTone";
import AntdIcon from '../components/AntdIcon';
var BulbTwoTone = function BulbTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BulbTwoToneSvg
  }));
};
BulbTwoTone.displayName = 'BulbTwoTone';
export default /*#__PURE__*/React.forwardRef(BulbTwoTone);