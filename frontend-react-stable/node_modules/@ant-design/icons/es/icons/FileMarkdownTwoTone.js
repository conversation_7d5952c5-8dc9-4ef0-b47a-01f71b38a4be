import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileMarkdownTwoToneSvg from "@ant-design/icons-svg/es/asn/FileMarkdownTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileMarkdownTwoTone = function FileMarkdownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileMarkdownTwoToneSvg
  }));
};
FileMarkdownTwoTone.displayName = 'FileMarkdownTwoTone';
export default /*#__PURE__*/React.forwardRef(FileMarkdownTwoTone);