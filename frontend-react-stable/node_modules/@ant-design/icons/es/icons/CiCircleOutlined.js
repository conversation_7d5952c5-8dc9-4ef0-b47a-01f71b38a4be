import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CiCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CiCircleOutlined = function CiCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiCircleOutlinedSvg
  }));
};
CiCircleOutlined.displayName = 'CiCircleOutlined';
export default /*#__PURE__*/React.forwardRef(CiCircleOutlined);