import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CloseCircleFilledSvg from "@ant-design/icons-svg/es/asn/CloseCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CloseCircleFilled = function CloseCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CloseCircleFilledSvg
  }));
};
CloseCircleFilled.displayName = 'CloseCircleFilled';
export default /*#__PURE__*/React.forwardRef(CloseCircleFilled);