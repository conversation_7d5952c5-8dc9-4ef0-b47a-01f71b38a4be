import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SkinOutlinedSvg from "@ant-design/icons-svg/es/asn/SkinOutlined";
import AntdIcon from '../components/AntdIcon';
var SkinOutlined = function SkinOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SkinOutlinedSvg
  }));
};
SkinOutlined.displayName = 'SkinOutlined';
export default /*#__PURE__*/React.forwardRef(SkinOutlined);