import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import <PERSON>payOutlinedSvg from "@ant-design/icons-svg/es/asn/AlipayOutlined";
import AntdIcon from '../components/AntdIcon';
var AlipayOutlined = function AlipayOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlipayOutlinedSvg
  }));
};
AlipayOutlined.displayName = 'AlipayOutlined';
export default /*#__PURE__*/React.forwardRef(AlipayOutlined);