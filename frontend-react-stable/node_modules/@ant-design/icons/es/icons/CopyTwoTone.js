import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CopyTwoToneSvg from "@ant-design/icons-svg/es/asn/CopyTwoTone";
import AntdIcon from '../components/AntdIcon';
var CopyTwoTone = function CopyTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CopyTwoToneSvg
  }));
};
CopyTwoTone.displayName = 'CopyTwoTone';
export default /*#__PURE__*/React.forwardRef(CopyTwoTone);