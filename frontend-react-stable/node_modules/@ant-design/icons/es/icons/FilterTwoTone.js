import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FilterTwoToneSvg from "@ant-design/icons-svg/es/asn/FilterTwoTone";
import AntdIcon from '../components/AntdIcon';
var FilterTwoTone = function FilterTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FilterTwoToneSvg
  }));
};
FilterTwoTone.displayName = 'FilterTwoTone';
export default /*#__PURE__*/React.forwardRef(FilterTwoTone);