import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ForkOutlinedSvg from "@ant-design/icons-svg/es/asn/ForkOutlined";
import AntdIcon from '../components/AntdIcon';
var ForkOutlined = function ForkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ForkOutlinedSvg
  }));
};
ForkOutlined.displayName = 'ForkOutlined';
export default /*#__PURE__*/React.forwardRef(ForkOutlined);