import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SkypeFilledSvg from "@ant-design/icons-svg/es/asn/SkypeFilled";
import AntdIcon from '../components/AntdIcon';
var SkypeFilled = function SkypeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SkypeFilledSvg
  }));
};
SkypeFilled.displayName = 'SkypeFilled';
export default /*#__PURE__*/React.forwardRef(SkypeFilled);