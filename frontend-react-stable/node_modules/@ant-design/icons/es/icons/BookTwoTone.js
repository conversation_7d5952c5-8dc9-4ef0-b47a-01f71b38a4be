import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BookTwoToneSvg from "@ant-design/icons-svg/es/asn/BookTwoTone";
import AntdIcon from '../components/AntdIcon';
var BookTwoTone = function BookTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BookTwoToneSvg
  }));
};
BookTwoTone.displayName = 'BookTwoTone';
export default /*#__PURE__*/React.forwardRef(BookTwoTone);