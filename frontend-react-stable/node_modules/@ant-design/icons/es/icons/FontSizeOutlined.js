import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FontSizeOutlinedSvg from "@ant-design/icons-svg/es/asn/FontSizeOutlined";
import AntdIcon from '../components/AntdIcon';
var FontSizeOutlined = function FontSizeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FontSizeOutlinedSvg
  }));
};
FontSizeOutlined.displayName = 'FontSizeOutlined';
export default /*#__PURE__*/React.forwardRef(FontSizeOutlined);