import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import GooglePlusSquareFilledSvg from "@ant-design/icons-svg/es/asn/GooglePlusSquareFilled";
import AntdIcon from '../components/AntdIcon';
var GooglePlusSquareFilled = function GooglePlusSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: GooglePlusSquareFilledSvg
  }));
};
GooglePlusSquareFilled.displayName = 'GooglePlusSquareFilled';
export default /*#__PURE__*/React.forwardRef(GooglePlusSquareFilled);