import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrophyOutlinedSvg from "@ant-design/icons-svg/es/asn/TrophyOutlined";
import AntdIcon from '../components/AntdIcon';
var TrophyOutlined = function TrophyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrophyOutlinedSvg
  }));
};
TrophyOutlined.displayName = 'TrophyOutlined';
export default /*#__PURE__*/React.forwardRef(TrophyOutlined);