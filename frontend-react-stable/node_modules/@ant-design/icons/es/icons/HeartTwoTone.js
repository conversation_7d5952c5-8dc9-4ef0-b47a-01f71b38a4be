import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HeartTwoToneSvg from "@ant-design/icons-svg/es/asn/HeartTwoTone";
import AntdIcon from '../components/AntdIcon';
var HeartTwoTone = function HeartTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HeartTwoToneSvg
  }));
};
HeartTwoTone.displayName = 'HeartTwoTone';
export default /*#__PURE__*/React.forwardRef(HeartTwoTone);