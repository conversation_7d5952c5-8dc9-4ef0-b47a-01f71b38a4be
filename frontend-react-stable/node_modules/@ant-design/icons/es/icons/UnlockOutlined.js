import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UnlockOutlinedSvg from "@ant-design/icons-svg/es/asn/UnlockOutlined";
import AntdIcon from '../components/AntdIcon';
var UnlockOutlined = function UnlockOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UnlockOutlinedSvg
  }));
};
UnlockOutlined.displayName = 'UnlockOutlined';
export default /*#__PURE__*/React.forwardRef(UnlockOutlined);