import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FullscreenOutlinedSvg from "@ant-design/icons-svg/es/asn/FullscreenOutlined";
import AntdIcon from '../components/AntdIcon';
var FullscreenOutlined = function FullscreenOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FullscreenOutlinedSvg
  }));
};
FullscreenOutlined.displayName = 'FullscreenOutlined';
export default /*#__PURE__*/React.forwardRef(FullscreenOutlined);