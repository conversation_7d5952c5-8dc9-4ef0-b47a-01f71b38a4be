import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ForwardFilledSvg from "@ant-design/icons-svg/es/asn/ForwardFilled";
import AntdIcon from '../components/AntdIcon';
var ForwardFilled = function ForwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ForwardFilledSvg
  }));
};
ForwardFilled.displayName = 'ForwardFilled';
export default /*#__PURE__*/React.forwardRef(ForwardFilled);