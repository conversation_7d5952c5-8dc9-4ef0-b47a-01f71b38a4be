import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ZoomInOutlinedSvg from "@ant-design/icons-svg/es/asn/ZoomInOutlined";
import AntdIcon from '../components/AntdIcon';
var ZoomInOutlined = function ZoomInOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ZoomInOutlinedSvg
  }));
};
ZoomInOutlined.displayName = 'ZoomInOutlined';
export default /*#__PURE__*/React.forwardRef(ZoomInOutlined);