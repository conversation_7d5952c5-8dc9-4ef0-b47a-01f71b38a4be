import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BoxPlotOutlinedSvg from "@ant-design/icons-svg/es/asn/BoxPlotOutlined";
import AntdIcon from '../components/AntdIcon';
var BoxPlotOutlined = function BoxPlotOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BoxPlotOutlinedSvg
  }));
};
BoxPlotOutlined.displayName = 'BoxPlotOutlined';
export default /*#__PURE__*/React.forwardRef(BoxPlotOutlined);