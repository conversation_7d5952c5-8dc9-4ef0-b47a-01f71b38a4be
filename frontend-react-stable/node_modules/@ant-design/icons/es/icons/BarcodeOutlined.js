import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BarcodeOutlinedSvg from "@ant-design/icons-svg/es/asn/BarcodeOutlined";
import AntdIcon from '../components/AntdIcon';
var BarcodeOutlined = function BarcodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BarcodeOutlinedSvg
  }));
};
BarcodeOutlined.displayName = 'BarcodeOutlined';
export default /*#__PURE__*/React.forwardRef(BarcodeOutlined);