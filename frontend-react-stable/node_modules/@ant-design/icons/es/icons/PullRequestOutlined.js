import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PullRequestOutlinedSvg from "@ant-design/icons-svg/es/asn/PullRequestOutlined";
import AntdIcon from '../components/AntdIcon';
var PullRequestOutlined = function PullRequestOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PullRequestOutlinedSvg
  }));
};
PullRequestOutlined.displayName = 'PullRequestOutlined';
export default /*#__PURE__*/React.forwardRef(PullRequestOutlined);