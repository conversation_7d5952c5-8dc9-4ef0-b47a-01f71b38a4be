import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExperimentFilledSvg from "@ant-design/icons-svg/es/asn/ExperimentFilled";
import AntdIcon from '../components/AntdIcon';
var ExperimentFilled = function ExperimentFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExperimentFilledSvg
  }));
};
ExperimentFilled.displayName = 'ExperimentFilled';
export default /*#__PURE__*/React.forwardRef(ExperimentFilled);