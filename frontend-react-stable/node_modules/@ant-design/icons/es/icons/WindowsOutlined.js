import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WindowsOutlinedSvg from "@ant-design/icons-svg/es/asn/WindowsOutlined";
import AntdIcon from '../components/AntdIcon';
var WindowsOutlined = function WindowsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WindowsOutlinedSvg
  }));
};
WindowsOutlined.displayName = 'WindowsOutlined';
export default /*#__PURE__*/React.forwardRef(WindowsOutlined);