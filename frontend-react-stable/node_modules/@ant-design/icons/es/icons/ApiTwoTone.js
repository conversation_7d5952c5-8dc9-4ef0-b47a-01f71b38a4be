import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ApiTwoToneSvg from "@ant-design/icons-svg/es/asn/ApiTwoTone";
import AntdIcon from '../components/AntdIcon';
var ApiTwoTone = function ApiTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ApiTwoToneSvg
  }));
};
ApiTwoTone.displayName = 'ApiTwoTone';
export default /*#__PURE__*/React.forwardRef(ApiTwoTone);