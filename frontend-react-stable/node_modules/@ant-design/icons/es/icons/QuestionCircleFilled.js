import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QuestionCircleFilledSvg from "@ant-design/icons-svg/es/asn/QuestionCircleFilled";
import AntdIcon from '../components/AntdIcon';
var QuestionCircleFilled = function QuestionCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QuestionCircleFilledSvg
  }));
};
QuestionCircleFilled.displayName = 'QuestionCircleFilled';
export default /*#__PURE__*/React.forwardRef(QuestionCircleFilled);