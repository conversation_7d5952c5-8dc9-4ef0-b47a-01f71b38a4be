import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReadOutlinedSvg from "@ant-design/icons-svg/es/asn/ReadOutlined";
import AntdIcon from '../components/AntdIcon';
var ReadOutlined = function ReadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReadOutlinedSvg
  }));
};
ReadOutlined.displayName = 'ReadOutlined';
export default /*#__PURE__*/React.forwardRef(ReadOutlined);