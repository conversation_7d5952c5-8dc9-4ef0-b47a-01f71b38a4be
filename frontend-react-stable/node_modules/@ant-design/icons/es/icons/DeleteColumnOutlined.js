import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DeleteColumnOutlinedSvg from "@ant-design/icons-svg/es/asn/DeleteColumnOutlined";
import AntdIcon from '../components/AntdIcon';
var DeleteColumnOutlined = function DeleteColumnOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DeleteColumnOutlinedSvg
  }));
};
DeleteColumnOutlined.displayName = 'DeleteColumnOutlined';
export default /*#__PURE__*/React.forwardRef(DeleteColumnOutlined);