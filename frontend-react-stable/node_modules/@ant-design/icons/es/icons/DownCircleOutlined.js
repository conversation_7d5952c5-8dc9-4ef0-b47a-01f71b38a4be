import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/DownCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var DownCircleOutlined = function DownCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownCircleOutlinedSvg
  }));
};
DownCircleOutlined.displayName = 'DownCircleOutlined';
export default /*#__PURE__*/React.forwardRef(DownCircleOutlined);