import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PictureTwoToneSvg from "@ant-design/icons-svg/es/asn/PictureTwoTone";
import AntdIcon from '../components/AntdIcon';
var PictureTwoTone = function PictureTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PictureTwoToneSvg
  }));
};
PictureTwoTone.displayName = 'PictureTwoTone';
export default /*#__PURE__*/React.forwardRef(PictureTwoTone);