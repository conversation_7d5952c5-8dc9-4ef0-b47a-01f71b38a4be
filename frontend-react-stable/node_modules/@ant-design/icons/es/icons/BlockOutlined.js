import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BlockOutlinedSvg from "@ant-design/icons-svg/es/asn/BlockOutlined";
import AntdIcon from '../components/AntdIcon';
var BlockOutlined = function BlockOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BlockOutlinedSvg
  }));
};
BlockOutlined.displayName = 'BlockOutlined';
export default /*#__PURE__*/React.forwardRef(BlockOutlined);