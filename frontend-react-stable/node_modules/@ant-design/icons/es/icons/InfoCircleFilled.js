import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InfoCircleFilledSvg from "@ant-design/icons-svg/es/asn/InfoCircleFilled";
import AntdIcon from '../components/AntdIcon';
var InfoCircleFilled = function InfoCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InfoCircleFilledSvg
  }));
};
InfoCircleFilled.displayName = 'InfoCircleFilled';
export default /*#__PURE__*/React.forwardRef(InfoCircleFilled);