import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SmileTwoToneSvg from "@ant-design/icons-svg/es/asn/SmileTwoTone";
import AntdIcon from '../components/AntdIcon';
var SmileTwoTone = function SmileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SmileTwoToneSvg
  }));
};
SmileTwoTone.displayName = 'SmileTwoTone';
export default /*#__PURE__*/React.forwardRef(SmileTwoTone);