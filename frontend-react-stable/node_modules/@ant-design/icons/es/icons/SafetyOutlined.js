import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SafetyOutlinedSvg from "@ant-design/icons-svg/es/asn/SafetyOutlined";
import AntdIcon from '../components/AntdIcon';
var SafetyOutlined = function SafetyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SafetyOutlinedSvg
  }));
};
SafetyOutlined.displayName = 'SafetyOutlined';
export default /*#__PURE__*/React.forwardRef(SafetyOutlined);