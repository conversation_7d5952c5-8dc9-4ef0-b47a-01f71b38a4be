import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwapLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/SwapLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var SwapLeftOutlined = function SwapLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwapLeftOutlinedSvg
  }));
};
SwapLeftOutlined.displayName = 'SwapLeftOutlined';
export default /*#__PURE__*/React.forwardRef(SwapLeftOutlined);