import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DatabaseFilledSvg from "@ant-design/icons-svg/es/asn/DatabaseFilled";
import AntdIcon from '../components/AntdIcon';
var DatabaseFilled = function DatabaseFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DatabaseFilledSvg
  }));
};
DatabaseFilled.displayName = 'DatabaseFilled';
export default /*#__PURE__*/React.forwardRef(DatabaseFilled);