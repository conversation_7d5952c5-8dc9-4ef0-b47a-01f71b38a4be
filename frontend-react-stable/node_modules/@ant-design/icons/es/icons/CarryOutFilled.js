import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarryOutFilledSvg from "@ant-design/icons-svg/es/asn/CarryOutFilled";
import AntdIcon from '../components/AntdIcon';
var CarryOutFilled = function CarryOutFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarryOutFilledSvg
  }));
};
CarryOutFilled.displayName = 'CarryOutFilled';
export default /*#__PURE__*/React.forwardRef(CarryOutFilled);