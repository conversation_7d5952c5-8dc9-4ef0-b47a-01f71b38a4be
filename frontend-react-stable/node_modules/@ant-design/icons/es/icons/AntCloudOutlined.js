import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AntCloudOutlinedSvg from "@ant-design/icons-svg/es/asn/AntCloudOutlined";
import AntdIcon from '../components/AntdIcon';
var AntCloudOutlined = function AntCloudOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AntCloudOutlinedSvg
  }));
};
AntCloudOutlined.displayName = 'AntCloudOutlined';
export default /*#__PURE__*/React.forwardRef(AntCloudOutlined);