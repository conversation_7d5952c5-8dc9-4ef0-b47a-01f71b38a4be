import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AudioTwoToneSvg from "@ant-design/icons-svg/es/asn/AudioTwoTone";
import AntdIcon from '../components/AntdIcon';
var AudioTwoTone = function AudioTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AudioTwoToneSvg
  }));
};
AudioTwoTone.displayName = 'AudioTwoTone';
export default /*#__PURE__*/React.forwardRef(AudioTwoTone);