import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EuroCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/EuroCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var EuroCircleTwoTone = function EuroCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EuroCircleTwoToneSvg
  }));
};
EuroCircleTwoTone.displayName = 'EuroCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(EuroCircleTwoTone);