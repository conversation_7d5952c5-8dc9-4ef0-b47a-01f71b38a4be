import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LeftCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var LeftCircleOutlined = function LeftCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LeftCircleOutlinedSvg
  }));
};
LeftCircleOutlined.displayName = 'LeftCircleOutlined';
export default /*#__PURE__*/React.forwardRef(LeftCircleOutlined);