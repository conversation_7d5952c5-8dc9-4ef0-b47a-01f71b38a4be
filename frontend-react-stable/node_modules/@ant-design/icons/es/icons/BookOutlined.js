import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BookOutlinedSvg from "@ant-design/icons-svg/es/asn/BookOutlined";
import AntdIcon from '../components/AntdIcon';
var BookOutlined = function BookOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BookOutlinedSvg
  }));
};
BookOutlined.displayName = 'BookOutlined';
export default /*#__PURE__*/React.forwardRef(BookOutlined);