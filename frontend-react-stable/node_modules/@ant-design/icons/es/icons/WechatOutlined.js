import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WechatOutlinedSvg from "@ant-design/icons-svg/es/asn/WechatOutlined";
import AntdIcon from '../components/AntdIcon';
var WechatOutlined = function WechatOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WechatOutlinedSvg
  }));
};
WechatOutlined.displayName = 'WechatOutlined';
export default /*#__PURE__*/React.forwardRef(WechatOutlined);