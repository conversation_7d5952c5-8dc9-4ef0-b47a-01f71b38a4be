import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import IdcardOutlinedSvg from "@ant-design/icons-svg/es/asn/IdcardOutlined";
import AntdIcon from '../components/AntdIcon';
var IdcardOutlined = function IdcardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: IdcardOutlinedSvg
  }));
};
IdcardOutlined.displayName = 'IdcardOutlined';
export default /*#__PURE__*/React.forwardRef(IdcardOutlined);