import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/RightSquareOutlined";
import AntdIcon from '../components/AntdIcon';
var RightSquareOutlined = function RightSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightSquareOutlinedSvg
  }));
};
RightSquareOutlined.displayName = 'RightSquareOutlined';
export default /*#__PURE__*/React.forwardRef(RightSquareOutlined);