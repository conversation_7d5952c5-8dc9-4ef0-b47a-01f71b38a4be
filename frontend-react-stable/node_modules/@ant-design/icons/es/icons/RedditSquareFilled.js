import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedditSquareFilledSvg from "@ant-design/icons-svg/es/asn/RedditSquareFilled";
import AntdIcon from '../components/AntdIcon';
var RedditSquareFilled = function RedditSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedditSquareFilledSvg
  }));
};
RedditSquareFilled.displayName = 'RedditSquareFilled';
export default /*#__PURE__*/React.forwardRef(RedditSquareFilled);