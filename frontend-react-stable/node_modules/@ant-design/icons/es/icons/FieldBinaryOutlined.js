import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FieldBinaryOutlinedSvg from "@ant-design/icons-svg/es/asn/FieldBinaryOutlined";
import AntdIcon from '../components/AntdIcon';
var FieldBinaryOutlined = function FieldBinaryOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FieldBinaryOutlinedSvg
  }));
};
FieldBinaryOutlined.displayName = 'FieldBinaryOutlined';
export default /*#__PURE__*/React.forwardRef(FieldBinaryOutlined);