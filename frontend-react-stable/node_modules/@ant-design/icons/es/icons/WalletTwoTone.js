import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WalletTwoToneSvg from "@ant-design/icons-svg/es/asn/WalletTwoTone";
import AntdIcon from '../components/AntdIcon';
var WalletTwoTone = function WalletTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WalletTwoToneSvg
  }));
};
WalletTwoTone.displayName = 'WalletTwoTone';
export default /*#__PURE__*/React.forwardRef(WalletTwoTone);