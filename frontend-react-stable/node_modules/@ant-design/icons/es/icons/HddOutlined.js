import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import HddOutlinedSvg from "@ant-design/icons-svg/es/asn/HddOutlined";
import AntdIcon from '../components/AntdIcon';
var HddOutlined = function HddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: HddOutlinedSvg
  }));
};
HddOutlined.displayName = 'HddOutlined';
export default /*#__PURE__*/React.forwardRef(HddOutlined);