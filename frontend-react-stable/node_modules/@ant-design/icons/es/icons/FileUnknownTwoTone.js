import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileUnknownTwoToneSvg from "@ant-design/icons-svg/es/asn/FileUnknownTwoTone";
import AntdIcon from '../components/AntdIcon';
var FileUnknownTwoTone = function FileUnknownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileUnknownTwoToneSvg
  }));
};
FileUnknownTwoTone.displayName = 'FileUnknownTwoTone';
export default /*#__PURE__*/React.forwardRef(FileUnknownTwoTone);