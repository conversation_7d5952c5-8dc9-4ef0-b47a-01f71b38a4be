import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeTwoToneSvg from "@ant-design/icons-svg/es/asn/EyeTwoTone";
import AntdIcon from '../components/AntdIcon';
var EyeTwoTone = function EyeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeTwoToneSvg
  }));
};
EyeTwoTone.displayName = 'EyeTwoTone';
export default /*#__PURE__*/React.forwardRef(EyeTwoTone);