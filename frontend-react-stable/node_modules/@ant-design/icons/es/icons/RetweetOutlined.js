import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RetweetOutlinedSvg from "@ant-design/icons-svg/es/asn/RetweetOutlined";
import AntdIcon from '../components/AntdIcon';
var RetweetOutlined = function RetweetOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RetweetOutlinedSvg
  }));
};
RetweetOutlined.displayName = 'RetweetOutlined';
export default /*#__PURE__*/React.forwardRef(RetweetOutlined);