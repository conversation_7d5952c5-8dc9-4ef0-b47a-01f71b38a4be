import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RiseOutlinedSvg from "@ant-design/icons-svg/es/asn/RiseOutlined";
import AntdIcon from '../components/AntdIcon';
var RiseOutlined = function RiseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RiseOutlinedSvg
  }));
};
RiseOutlined.displayName = 'RiseOutlined';
export default /*#__PURE__*/React.forwardRef(RiseOutlined);