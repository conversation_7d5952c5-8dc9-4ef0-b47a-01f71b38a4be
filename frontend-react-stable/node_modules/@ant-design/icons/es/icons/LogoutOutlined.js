import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LogoutOutlinedSvg from "@ant-design/icons-svg/es/asn/LogoutOutlined";
import AntdIcon from '../components/AntdIcon';
var LogoutOutlined = function LogoutOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LogoutOutlinedSvg
  }));
};
LogoutOutlined.displayName = 'LogoutOutlined';
export default /*#__PURE__*/React.forwardRef(LogoutOutlined);