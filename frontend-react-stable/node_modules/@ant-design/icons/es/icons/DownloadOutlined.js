import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DownloadOutlinedSvg from "@ant-design/icons-svg/es/asn/DownloadOutlined";
import AntdIcon from '../components/AntdIcon';
var DownloadOutlined = function DownloadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DownloadOutlinedSvg
  }));
};
DownloadOutlined.displayName = 'DownloadOutlined';
export default /*#__PURE__*/React.forwardRef(DownloadOutlined);