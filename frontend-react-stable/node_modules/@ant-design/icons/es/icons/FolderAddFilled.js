import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FolderAddFilledSvg from "@ant-design/icons-svg/es/asn/FolderAddFilled";
import AntdIcon from '../components/AntdIcon';
var FolderAddFilled = function FolderAddFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FolderAddFilledSvg
  }));
};
FolderAddFilled.displayName = 'FolderAddFilled';
export default /*#__PURE__*/React.forwardRef(FolderAddFilled);