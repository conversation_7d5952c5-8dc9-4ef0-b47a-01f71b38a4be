import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlidersFilledSvg from "@ant-design/icons-svg/es/asn/SlidersFilled";
import AntdIcon from '../components/AntdIcon';
var SlidersFilled = function SlidersFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlidersFilledSvg
  }));
};
SlidersFilled.displayName = 'SlidersFilled';
export default /*#__PURE__*/React.forwardRef(SlidersFilled);