import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EuroCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/EuroCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var EuroCircleOutlined = function EuroCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EuroCircleOutlinedSvg
  }));
};
EuroCircleOutlined.displayName = 'EuroCircleOutlined';
export default /*#__PURE__*/React.forwardRef(EuroCircleOutlined);