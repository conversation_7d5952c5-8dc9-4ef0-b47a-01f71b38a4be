import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import DollarCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/DollarCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var DollarCircleTwoTone = function DollarCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: DollarCircleTwoToneSvg
  }));
};
DollarCircleTwoTone.displayName = 'DollarCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(DollarCircleTwoTone);