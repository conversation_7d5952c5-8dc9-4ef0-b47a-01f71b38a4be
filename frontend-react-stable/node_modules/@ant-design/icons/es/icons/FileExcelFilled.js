import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileExcelFilledSvg from "@ant-design/icons-svg/es/asn/FileExcelFilled";
import AntdIcon from '../components/AntdIcon';
var FileExcelFilled = function FileExcelFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileExcelFilledSvg
  }));
};
FileExcelFilled.displayName = 'FileExcelFilled';
export default /*#__PURE__*/React.forwardRef(FileExcelFilled);