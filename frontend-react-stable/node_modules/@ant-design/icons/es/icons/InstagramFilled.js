import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InstagramFilledSvg from "@ant-design/icons-svg/es/asn/InstagramFilled";
import AntdIcon from '../components/AntdIcon';
var InstagramFilled = function InstagramFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InstagramFilledSvg
  }));
};
InstagramFilled.displayName = 'InstagramFilled';
export default /*#__PURE__*/React.forwardRef(InstagramFilled);