import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileZipFilledSvg from "@ant-design/icons-svg/es/asn/FileZipFilled";
import AntdIcon from '../components/AntdIcon';
var FileZipFilled = function FileZipFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileZipFilledSvg
  }));
};
FileZipFilled.displayName = 'FileZipFilled';
export default /*#__PURE__*/React.forwardRef(FileZipFilled);