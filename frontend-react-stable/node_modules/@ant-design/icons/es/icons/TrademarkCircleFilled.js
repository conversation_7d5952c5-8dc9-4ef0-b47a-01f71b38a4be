import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrademarkCircleFilledSvg from "@ant-design/icons-svg/es/asn/TrademarkCircleFilled";
import AntdIcon from '../components/AntdIcon';
var TrademarkCircleFilled = function TrademarkCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrademarkCircleFilledSvg
  }));
};
TrademarkCircleFilled.displayName = 'TrademarkCircleFilled';
export default /*#__PURE__*/React.forwardRef(TrademarkCircleFilled);