import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import EyeInvisibleTwoToneSvg from "@ant-design/icons-svg/es/asn/EyeInvisibleTwoTone";
import AntdIcon from '../components/AntdIcon';
var EyeInvisibleTwoTone = function EyeInvisibleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: EyeInvisibleTwoToneSvg
  }));
};
EyeInvisibleTwoTone.displayName = 'EyeInvisibleTwoTone';
export default /*#__PURE__*/React.forwardRef(EyeInvisibleTwoTone);