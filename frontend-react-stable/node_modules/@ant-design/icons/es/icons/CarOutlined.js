import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CarOutlinedSvg from "@ant-design/icons-svg/es/asn/CarOutlined";
import AntdIcon from '../components/AntdIcon';
var CarOutlined = function CarOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CarOutlinedSvg
  }));
};
CarOutlined.displayName = 'CarOutlined';
export default /*#__PURE__*/React.forwardRef(CarOutlined);