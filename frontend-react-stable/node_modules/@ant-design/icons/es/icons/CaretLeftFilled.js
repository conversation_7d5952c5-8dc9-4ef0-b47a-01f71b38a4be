import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CaretLeftFilledSvg from "@ant-design/icons-svg/es/asn/CaretLeftFilled";
import AntdIcon from '../components/AntdIcon';
var CaretLeftFilled = function CaretLeftFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CaretLeftFilledSvg
  }));
};
CaretLeftFilled.displayName = 'CaretLeftFilled';
export default /*#__PURE__*/React.forwardRef(CaretLeftFilled);