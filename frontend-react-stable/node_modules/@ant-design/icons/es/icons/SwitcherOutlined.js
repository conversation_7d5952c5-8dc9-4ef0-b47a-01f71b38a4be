import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SwitcherOutlinedSvg from "@ant-design/icons-svg/es/asn/SwitcherOutlined";
import AntdIcon from '../components/AntdIcon';
var SwitcherOutlined = function SwitcherOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SwitcherOutlinedSvg
  }));
};
SwitcherOutlined.displayName = 'SwitcherOutlined';
export default /*#__PURE__*/React.forwardRef(SwitcherOutlined);