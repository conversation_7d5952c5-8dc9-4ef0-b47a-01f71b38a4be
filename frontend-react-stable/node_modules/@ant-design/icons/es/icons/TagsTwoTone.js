import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TagsTwoToneSvg from "@ant-design/icons-svg/es/asn/TagsTwoTone";
import AntdIcon from '../components/AntdIcon';
var TagsTwoTone = function TagsTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TagsTwoToneSvg
  }));
};
TagsTwoTone.displayName = 'TagsTwoTone';
export default /*#__PURE__*/React.forwardRef(TagsTwoTone);