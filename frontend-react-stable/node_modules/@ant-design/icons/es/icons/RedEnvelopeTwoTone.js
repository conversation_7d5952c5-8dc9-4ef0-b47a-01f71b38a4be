import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedEnvelopeTwoToneSvg from "@ant-design/icons-svg/es/asn/RedEnvelopeTwoTone";
import AntdIcon from '../components/AntdIcon';
var RedEnvelopeTwoTone = function RedEnvelopeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedEnvelopeTwoToneSvg
  }));
};
RedEnvelopeTwoTone.displayName = 'RedEnvelopeTwoTone';
export default /*#__PURE__*/React.forwardRef(RedEnvelopeTwoTone);