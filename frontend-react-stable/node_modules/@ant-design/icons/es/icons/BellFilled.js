import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BellFilledSvg from "@ant-design/icons-svg/es/asn/BellFilled";
import AntdIcon from '../components/AntdIcon';
var BellFilled = function BellFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BellFilledSvg
  }));
};
BellFilled.displayName = 'BellFilled';
export default /*#__PURE__*/React.forwardRef(BellFilled);