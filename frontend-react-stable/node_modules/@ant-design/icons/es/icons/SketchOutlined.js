import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SketchOutlinedSvg from "@ant-design/icons-svg/es/asn/SketchOutlined";
import AntdIcon from '../components/AntdIcon';
var SketchOutlined = function SketchOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SketchOutlinedSvg
  }));
};
SketchOutlined.displayName = 'SketchOutlined';
export default /*#__PURE__*/React.forwardRef(SketchOutlined);