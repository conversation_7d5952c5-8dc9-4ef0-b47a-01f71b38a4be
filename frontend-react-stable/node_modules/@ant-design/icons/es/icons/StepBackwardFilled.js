import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StepBackwardFilledSvg from "@ant-design/icons-svg/es/asn/StepBackwardFilled";
import AntdIcon from '../components/AntdIcon';
var StepBackwardFilled = function StepBackwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StepBackwardFilledSvg
  }));
};
StepBackwardFilled.displayName = 'StepBackwardFilled';
export default /*#__PURE__*/React.forwardRef(StepBackwardFilled);