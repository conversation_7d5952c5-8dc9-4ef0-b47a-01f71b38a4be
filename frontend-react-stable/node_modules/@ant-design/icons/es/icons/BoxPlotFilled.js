import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BoxPlotFilledSvg from "@ant-design/icons-svg/es/asn/BoxPlotFilled";
import AntdIcon from '../components/AntdIcon';
var BoxPlotFilled = function BoxPlotFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BoxPlotFilledSvg
  }));
};
BoxPlotFilled.displayName = 'BoxPlotFilled';
export default /*#__PURE__*/React.forwardRef(BoxPlotFilled);