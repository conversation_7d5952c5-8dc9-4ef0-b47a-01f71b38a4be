import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContactsOutlinedSvg from "@ant-design/icons-svg/es/asn/ContactsOutlined";
import AntdIcon from '../components/AntdIcon';
var ContactsOutlined = function ContactsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContactsOutlinedSvg
  }));
};
ContactsOutlined.displayName = 'ContactsOutlined';
export default /*#__PURE__*/React.forwardRef(ContactsOutlined);