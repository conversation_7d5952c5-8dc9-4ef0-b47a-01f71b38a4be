import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TaobaoCircleFilledSvg from "@ant-design/icons-svg/es/asn/TaobaoCircleFilled";
import AntdIcon from '../components/AntdIcon';
var TaobaoCircleFilled = function TaobaoCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TaobaoCircleFilledSvg
  }));
};
TaobaoCircleFilled.displayName = 'TaobaoCircleFilled';
export default /*#__PURE__*/React.forwardRef(TaobaoCircleFilled);