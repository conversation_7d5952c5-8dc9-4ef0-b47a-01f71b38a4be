import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShareAltOutlinedSvg from "@ant-design/icons-svg/es/asn/ShareAltOutlined";
import AntdIcon from '../components/AntdIcon';
var ShareAltOutlined = function ShareAltOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShareAltOutlinedSvg
  }));
};
ShareAltOutlined.displayName = 'ShareAltOutlined';
export default /*#__PURE__*/React.forwardRef(ShareAltOutlined);