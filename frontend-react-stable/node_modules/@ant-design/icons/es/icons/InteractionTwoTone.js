import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import InteractionTwoToneSvg from "@ant-design/icons-svg/es/asn/InteractionTwoTone";
import AntdIcon from '../components/AntdIcon';
var InteractionTwoTone = function InteractionTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: InteractionTwoToneSvg
  }));
};
InteractionTwoTone.displayName = 'InteractionTwoTone';
export default /*#__PURE__*/React.forwardRef(InteractionTwoTone);