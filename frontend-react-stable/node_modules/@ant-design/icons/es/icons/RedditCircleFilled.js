import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedditCircleFilledSvg from "@ant-design/icons-svg/es/asn/RedditCircleFilled";
import AntdIcon from '../components/AntdIcon';
var RedditCircleFilled = function RedditCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedditCircleFilledSvg
  }));
};
RedditCircleFilled.displayName = 'RedditCircleFilled';
export default /*#__PURE__*/React.forwardRef(RedditCircleFilled);