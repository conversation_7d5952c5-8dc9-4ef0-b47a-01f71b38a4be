import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ExperimentOutlinedSvg from "@ant-design/icons-svg/es/asn/ExperimentOutlined";
import AntdIcon from '../components/AntdIcon';
var ExperimentOutlined = function ExperimentOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ExperimentOutlinedSvg
  }));
};
ExperimentOutlined.displayName = 'ExperimentOutlined';
export default /*#__PURE__*/React.forwardRef(ExperimentOutlined);