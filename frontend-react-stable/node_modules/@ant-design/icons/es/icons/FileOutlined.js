import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FileOutlinedSvg from "@ant-design/icons-svg/es/asn/FileOutlined";
import AntdIcon from '../components/AntdIcon';
var FileOutlined = function FileOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FileOutlinedSvg
  }));
};
FileOutlined.displayName = 'FileOutlined';
export default /*#__PURE__*/React.forwardRef(FileOutlined);