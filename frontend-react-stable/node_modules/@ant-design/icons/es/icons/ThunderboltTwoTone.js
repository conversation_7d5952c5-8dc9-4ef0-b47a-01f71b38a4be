import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ThunderboltTwoToneSvg from "@ant-design/icons-svg/es/asn/ThunderboltTwoTone";
import AntdIcon from '../components/AntdIcon';
var ThunderboltTwoTone = function ThunderboltTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ThunderboltTwoToneSvg
  }));
};
ThunderboltTwoTone.displayName = 'ThunderboltTwoTone';
export default /*#__PURE__*/React.forwardRef(ThunderboltTwoTone);