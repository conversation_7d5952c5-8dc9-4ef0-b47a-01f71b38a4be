import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import FlagTwoToneSvg from "@ant-design/icons-svg/es/asn/FlagTwoTone";
import AntdIcon from '../components/AntdIcon';
var FlagTwoTone = function FlagTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: FlagTwoToneSvg
  }));
};
FlagTwoTone.displayName = 'FlagTwoTone';
export default /*#__PURE__*/React.forwardRef(FlagTwoTone);