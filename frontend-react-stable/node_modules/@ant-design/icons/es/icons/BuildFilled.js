import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BuildFilledSvg from "@ant-design/icons-svg/es/asn/BuildFilled";
import AntdIcon from '../components/AntdIcon';
var BuildFilled = function BuildFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BuildFilledSvg
  }));
};
BuildFilled.displayName = 'BuildFilled';
export default /*#__PURE__*/React.forwardRef(BuildFilled);