import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MessageFilledSvg from "@ant-design/icons-svg/es/asn/MessageFilled";
import AntdIcon from '../components/AntdIcon';
var MessageFilled = function MessageFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MessageFilledSvg
  }));
};
MessageFilled.displayName = 'MessageFilled';
export default /*#__PURE__*/React.forwardRef(MessageFilled);