import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SisternodeOutlinedSvg from "@ant-design/icons-svg/es/asn/SisternodeOutlined";
import AntdIcon from '../components/AntdIcon';
var SisternodeOutlined = function SisternodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SisternodeOutlinedSvg
  }));
};
SisternodeOutlined.displayName = 'SisternodeOutlined';
export default /*#__PURE__*/React.forwardRef(SisternodeOutlined);