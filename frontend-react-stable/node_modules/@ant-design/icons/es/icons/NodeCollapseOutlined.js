import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NodeCollapseOutlinedSvg from "@ant-design/icons-svg/es/asn/NodeCollapseOutlined";
import AntdIcon from '../components/AntdIcon';
var NodeCollapseOutlined = function NodeCollapseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NodeCollapseOutlinedSvg
  }));
};
NodeCollapseOutlined.displayName = 'NodeCollapseOutlined';
export default /*#__PURE__*/React.forwardRef(NodeCollapseOutlined);