import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SecurityScanTwoToneSvg from "@ant-design/icons-svg/es/asn/SecurityScanTwoTone";
import AntdIcon from '../components/AntdIcon';
var SecurityScanTwoTone = function SecurityScanTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SecurityScanTwoToneSvg
  }));
};
SecurityScanTwoTone.displayName = 'SecurityScanTwoTone';
export default /*#__PURE__*/React.forwardRef(SecurityScanTwoTone);