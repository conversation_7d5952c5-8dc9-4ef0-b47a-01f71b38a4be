import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CoffeeOutlinedSvg from "@ant-design/icons-svg/es/asn/CoffeeOutlined";
import AntdIcon from '../components/AntdIcon';
var CoffeeOutlined = function CoffeeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CoffeeOutlinedSvg
  }));
};
CoffeeOutlined.displayName = 'CoffeeOutlined';
export default /*#__PURE__*/React.forwardRef(CoffeeOutlined);