import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import LikeFilledSvg from "@ant-design/icons-svg/es/asn/LikeFilled";
import AntdIcon from '../components/AntdIcon';
var LikeFilled = function LikeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: LikeFilledSvg
  }));
};
LikeFilled.displayName = 'LikeFilled';
export default /*#__PURE__*/React.forwardRef(LikeFilled);