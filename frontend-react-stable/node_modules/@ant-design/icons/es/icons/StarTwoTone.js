import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StarTwoToneSvg from "@ant-design/icons-svg/es/asn/StarTwoTone";
import AntdIcon from '../components/AntdIcon';
var StarTwoTone = function StarTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StarTwoToneSvg
  }));
};
StarTwoTone.displayName = 'StarTwoTone';
export default /*#__PURE__*/React.forwardRef(StarTwoTone);