import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeSandboxOutlinedSvg from "@ant-design/icons-svg/es/asn/CodeSandboxOutlined";
import AntdIcon from '../components/AntdIcon';
var CodeSandboxOutlined = function CodeSandboxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeSandboxOutlinedSvg
  }));
};
CodeSandboxOutlined.displayName = 'CodeSandboxOutlined';
export default /*#__PURE__*/React.forwardRef(CodeSandboxOutlined);