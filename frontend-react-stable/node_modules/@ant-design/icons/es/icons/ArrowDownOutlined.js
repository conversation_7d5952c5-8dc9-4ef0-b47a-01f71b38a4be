import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ArrowDownOutlinedSvg from "@ant-design/icons-svg/es/asn/ArrowDownOutlined";
import AntdIcon from '../components/AntdIcon';
var ArrowDownOutlined = function ArrowDownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ArrowDownOutlinedSvg
  }));
};
ArrowDownOutlined.displayName = 'ArrowDownOutlined';
export default /*#__PURE__*/React.forwardRef(ArrowDownOutlined);