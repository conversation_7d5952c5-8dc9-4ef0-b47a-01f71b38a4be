"use strict";
// This icon file is generated automatically.
Object.defineProperty(exports, "__esModule", { value: true });
var TwitchFilled = { "icon": { "tag": "svg", "attrs": { "fill-rule": "evenodd", "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "defs", "attrs": {}, "children": [{ "tag": "filter", "attrs": { "filterUnits": "objectBoundingBox", "height": "102.3%", "id": "a", "width": "102.3%", "x": "-1.2%", "y": "-1.2%" }, "children": [{ "tag": "feOffset", "attrs": { "dy": "2", "in": "SourceAlpha", "result": "shadowOffsetOuter1" } }, { "tag": "feGaussianBlur", "attrs": { "in": "shadowOffsetOuter1", "result": "shadowBlurOuter1", "stdDeviation": "2" } }, { "tag": "feColorMatrix", "attrs": { "in": "shadowBlurOuter1", "result": "shadowMatrixOuter1", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" } }, { "tag": "feMerge", "attrs": {}, "children": [{ "tag": "feMergeNode", "attrs": { "in": "shadowMatrixOuter1" } }, { "tag": "feMergeNode", "attrs": { "in": "SourceGraphic" } }] }] }] }, { "tag": "g", "attrs": { "filter": "url(#a)", "transform": "translate(9 9)" }, "children": [{ "tag": "path", "attrs": { "d": "M185.14 112L128 254.86V797.7h171.43V912H413.7L528 797.71h142.86l200-200V112zm314.29 428.57H413.7V310.21h85.72zm200 0H613.7V310.21h85.72z" } }] }] }, "name": "twitch", "theme": "filled" };
exports.default = TwitchFilled;
