{"name": "@ant-design/icons-svg", "version": "4.4.2", "sideEffects": false, "description": "Abstract nodes for ant design icons.", "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "homepage": "https://github.com/ant-design/ant-design-icons/", "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "files": ["lib", "es", "inline-svg", "inline-namespaced-svg"], "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-icons.git"}, "scripts": {"g": "npm run generate", "generate": "cross-env NODE_ENV=production gulp --require ts-node/register/transpile-only", "type-check": "tsc --noEmit", "build:es": "tsc --project tsconfig.build.json --module esnext --outDir es", "build:lib": "tsc --project tsconfig.build.json --module commonjs --outDir lib", "build": "cross-env NODE_ENV=production && npm run build:es && npm run build:lib", "test": "cross-env NODE_ENV=test && npm run test:unit", "test:unit": "jest", "prepublishOnly": "npm run test && npm run g && npm run build && npm run type-check && npm run digest", "digest": "node scripts/digest.js"}, "bugs": {"url": "https://github.com/ant-design/ant-design-icons/issues"}, "devDependencies": {"@babel/core": "^7.8.6", "@babel/preset-env": "^7.8.6", "@babel/preset-typescript": "^7.8.3", "@rgrove/parse-xml": "^2.0.2", "@types/gulp": "^4.0.6", "@types/gulp-concat": "^0.0.32", "@types/gulp-rename": "^0.0.33", "@types/jest": "^25.1.3", "@types/lodash.camelcase": "^4.3.6", "@types/lodash.template": "^4.4.6", "@types/lodash.upperfirst": "^4.3.6", "@types/node": "^13.9.3", "@types/ramda": "^0.26.43", "@types/rgrove__parse-xml": "^1.1.0", "@types/svgo": "^1.3.1", "@types/through2": "^2.0.34", "@typescript-eslint/eslint-plugin": "^2.21.0", "@typescript-eslint/parser": "^2.21.0", "babel-jest": "^25.1.0", "cross-env": "^7.0.0", "del": "^5.1.0", "eslint": "^7.16.0", "globby": "^11.0.0", "gulp": "^4.0.2", "gulp-clone": "^2.0.1", "gulp-concat": "^2.6.1", "gulp-header": "^2.0.9", "gulp-rename": "^2.0.0", "jest": "^25.1.0", "lodash.camelcase": "^4.3.0", "lodash.template": "^4.5.0", "lodash.upperfirst": "^4.3.1", "ramda": "^0.27.0", "svgo": "^1.3.2", "through2": "^3.0.1", "ts-node": "^8.6.2", "typescript": "^4.0.0", "vinyl": "^2.2.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}}