{"name": "rc-rate", "version": "2.9.3", "description": "React Star Rate Component", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-rate", "rate"], "homepage": "https://github.com/react-component/rate", "repository": {"type": "git", "url": "https://github.com/react-component/rate.git"}, "bugs": {"url": "https://github.com/react-component/rate/issues"}, "files": ["lib", "es", "assets/*.css"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch 2.9.x", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.0.1"}, "devDependencies": {"@types/classnames": "^2.2.9", "@types/jest": "^26.0.0", "@types/react": "^16.9.2", "@types/react-dom": "^16.9.0", "@umijs/fabric": "^2.0.0", "cross-env": "^7.0.0", "dumi": "^1.1.0", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^2.22.0", "father-build": "^1.18.6", "gh-pages": "^3.1.0", "less": "^3.0.0", "np": "^7.0.0", "rc-tooltip": "^5.0.1", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^4.0.5"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}