{"name": "rc-upload", "version": "4.3.6", "description": "upload ui component for react", "keywords": ["react", "react-component", "react-upload", "upload"], "homepage": "http://github.com/react-component/upload", "repository": {"type": "git", "url": "**************:react-component/upload.git"}, "bugs": {"url": "http://github.com/react-component/upload/issues"}, "license": "MIT", "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "npm run docs:build && gh-pages -d .doc", "compile": "father build", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "postpublish": "npm run docs:deploy", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "father test", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "devDependencies": {"@types/enzyme": "^3.10.8", "@types/jest": "^26.0.20", "@types/react": "^17.0.15", "@types/react-dom": "^17.0.9", "@umijs/fabric": "^2.0.0", "axios": "^0.26.1", "co-busboy": "^1.3.0", "coveralls": "^3.0.3", "cross-env": "^7.0.0", "dumi": "^1.0.37", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^2.22.0", "fs-extra": "^9.0.0", "gh-pages": "^3.1.0", "np": "^7.0.0", "raf": "^3.4.0", "rc-tools": "8.x", "react": "^16.0.0", "react-dom": "^16.0.0", "regenerator-runtime": "^0.13.7", "sinon": "^9.0.2", "typescript": "^4.0.2", "vinyl-fs": "^3.0.3"}, "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.5", "rc-util": "^5.2.0"}, "jest": {"collectCoverageFrom": ["src/*"], "coveragePathIgnorePatterns": ["src/IframeUploader.jsx"], "transform": {"\\.tsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js", "\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}