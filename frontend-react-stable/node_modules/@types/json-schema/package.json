{"name": "@types/json-schema", "version": "7.0.15", "description": "TypeScript definitions for json-schema", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/json-schema", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "b<PERSON>ny", "url": "https://github.com/bcherny"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lucianbuzzo"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rolandjitsu"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/json-schema"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "79984fd70cd25c3f7d72b84368778c763c89728ea0073832d745d4691b705257", "typeScriptVersion": "4.5"}