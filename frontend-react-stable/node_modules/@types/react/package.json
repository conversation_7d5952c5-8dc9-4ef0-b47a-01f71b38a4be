{"name": "@types/react", "version": "17.0.50", "description": "TypeScript definitions for React", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bbenezech", "githubUsername": "bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pza<PERSON><PERSON>ky", "githubUsername": "pza<PERSON>linsky"}, {"name": "<PERSON>", "url": "https://github.com/ericanderson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d", "githubUsername": "theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber", "githubUsername": "ferd<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa", "githubUsername": "jrakotoharisoa"}, {"name": "<PERSON>", "url": "https://github.com/pascaloliv", "githubUsername": "pascal<PERSON>v"}, {"name": "<PERSON>", "url": "https://github.com/hotell", "githubUsername": "hotell"}, {"name": "<PERSON>", "url": "https://github.com/franklixuefei", "githubUsername": "frank<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia", "githubUsername": "Jessidhia"}, {"name": "Saransh Kataria", "url": "https://github.com/saranshkataria", "githubUsername": "saranshkataria"}, {"name": "Kanitkorn Sujautra", "url": "https://github.com/lukyth", "githubUsername": "luk<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/zieka", "githubUsername": "zieka"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/dancerphil", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dimitrop<PERSON>los", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/disjukr", "githubUsername": "disju<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vhfmag", "githubUsername": "vhfmag"}, {"name": "<PERSON>", "url": "https://github.com/hellatan", "githubUsername": "hellatan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}, "typesPublisherContentHash": "32211c72b90e0b36dd3c0ad41510b246986bab44678c764c84eafc84dc38100a", "typeScriptVersion": "4.1", "exports": {".": {"types": {"default": "./index.d.ts"}}, "./jsx-runtime": {"types": {"default": "./jsx-runtime.d.ts"}}, "./jsx-dev-runtime": {"types": {"default": "./jsx-dev-runtime.d.ts"}}, "./package.json": "./package.json"}}