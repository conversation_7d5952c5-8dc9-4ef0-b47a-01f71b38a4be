{"name": "@types/webpack", "version": "4.41.40", "description": "TypeScript definitions for webpack", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "tkqubo", "url": "https://github.com/tkqubo"}, {"name": "<PERSON>", "githubUsername": "bumbleblym", "url": "https://github.com/bumbleblym"}, {"name": "<PERSON>", "githubUsername": "b<PERSON>ny", "url": "https://github.com/bcherny"}, {"name": "<PERSON>", "githubUsername": "tommytroylin", "url": "https://github.com/tommy<PERSON>ylin"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mohsen1", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "githubUsername": "jcreamer898", "url": "https://github.com/jcreamer898"}, {"name": "<PERSON>", "githubUsername": "alan-agius4", "url": "https://github.com/alan-agius4"}, {"name": "<PERSON>", "githubUsername": "dennispg", "url": "https://github.com/dennispg"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/christo<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ZSkycat", "githubUsername": "ZSkycat", "url": "https://github.com/ZSkycat"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rwaskie<PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "grgur", "url": "https://github.com/grgur"}, {"name": "<PERSON><PERSON><PERSON> Gonçalves Cavalcante", "githubUsername": "rubenspgcavalcante", "url": "https://github.com/rubenspgcavalcante"}, {"name": "<PERSON>", "githubUsername": "andersk", "url": "https://github.com/andersk"}, {"name": "<PERSON>", "githubUsername": "ofhouse", "url": "https://github.com/ofhouse"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/danielthank"}, {"name": "<PERSON><PERSON>", "githubUsername": "sasurau4", "url": "https://github.com/sasurau4"}, {"name": "<PERSON>", "githubUsername": "dionshihk", "url": "https://github.com/dionshihk"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "spamshaker", "url": "https://github.com/spamshaker"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/tapable": "^1", "@types/uglify-js": "*", "@types/webpack-sources": "*", "anymatch": "^3.0.0", "source-map": "^0.6.0"}, "peerDependencies": {}, "typesPublisherContentHash": "42873596867e3e4e787f9c797420a559a6b285212eeb65ad8e2b61fc88392186", "typeScriptVersion": "4.8"}