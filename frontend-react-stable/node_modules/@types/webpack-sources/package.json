{"name": "@types/webpack-sources", "version": "3.2.3", "description": "TypeScript definitions for webpack-sources", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources", "license": "MIT", "contributors": [{"name": "e-cloud", "githubUsername": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/chrise<PERSON>tein"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "typesPublisherContentHash": "7bdf9048654ee82707e648ea73eb0dbef57288a82d4076860bac7b3e738fd185", "typeScriptVersion": "4.5"}