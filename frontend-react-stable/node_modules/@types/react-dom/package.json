{"name": "@types/react-dom", "version": "17.0.17", "description": "TypeScript definitions for React (react-dom)", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/MartynasZilinskas", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d", "githubUsername": "theruther4d"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia", "githubUsername": "Jessidhia"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-dom"}, "scripts": {}, "dependencies": {"@types/react": "^17"}, "typesPublisherContentHash": "dfec09de4b807f00f3f08fd54872b6267ff1cc2b14c68020d5d7c92b4815f070", "typeScriptVersion": "3.9"}