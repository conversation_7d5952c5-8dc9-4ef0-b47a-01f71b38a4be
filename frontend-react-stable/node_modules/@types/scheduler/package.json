{"name": "@types/scheduler", "version": "0.26.0", "description": "TypeScript definitions for scheduler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/scheduler"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "da4a66c8598d6abe642c123415ff742736404b4acb294242859c381283dc67b3", "typeScriptVersion": "5.1"}