{"name": "@types/resolve", "version": "0.0.8", "description": "TypeScript definitions for resolve", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marionebl", "githubUsername": "marion<PERSON>l"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "5f7b20658879058bc7b11142bb7fd133c0e1701cc1434145bfce9c9a8e86c339", "typeScriptVersion": "2.0"}